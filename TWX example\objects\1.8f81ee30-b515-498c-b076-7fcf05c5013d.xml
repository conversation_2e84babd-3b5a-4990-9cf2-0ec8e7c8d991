<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8f81ee30-b515-498c-b076-7fcf05c5013d" name="Branch Hub filter service">
        <lastModified>1692705527874</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>7cde1afb-3893-45ba-8982-c0e008626078</guid>
        <versionId>3314b751-9904-485b-ab67-4e3bf2f6d912</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-cf1" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.aec004eb-335b-40d9-a5c9-abbea44346c9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f06475b9-f5f4-407b-ba1f-915062a54008"},{"incoming":["12b9015a-6e57-4345-8a10-64892b251308","f0a4ce49-8c69-46aa-8d07-bd63a9926657"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ad0"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"73a9caa3-fdf0-480a-8f44-9e4f3d05fa04"},{"targetRef":"be1fda0a-2124-42a9-b08c-6f2778ba20a7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IsHub?","declaredType":"sequenceFlow","id":"2027.aec004eb-335b-40d9-a5c9-abbea44346c9","sourceRef":"f06475b9-f5f4-407b-ba1f-915062a54008"},{"startQuantity":1,"outgoing":["12b9015a-6e57-4345-8a10-64892b251308"],"incoming":["45d004ef-92f8-45ef-939a-2cc599b74b5e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":278,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get HUB team","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"df768d6d-ea25-4c7e-9ad3-191b2c5a5197","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.filteredTeam = new tw.object.Team();\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\n\r\nvar users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;\r\n\t\r\nlog.info(\"users::: \"+ users);\r\n\tfor (var i = 0; i &lt; users.listLength ; i++)\r\n\t{\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\n\t}"]}},{"targetRef":"73a9caa3-fdf0-480a-8f44-9e4f3d05fa04","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"12b9015a-6e57-4345-8a10-64892b251308","sourceRef":"df768d6d-ea25-4c7e-9ad3-191b2c5a5197"},{"outgoing":["45d004ef-92f8-45ef-939a-2cc599b74b5e","14d72347-5405-4406-8dea-937a299f9300"],"incoming":["2027.aec004eb-335b-40d9-a5c9-abbea44346c9"],"default":"14d72347-5405-4406-8dea-937a299f9300","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":125,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"IsHub?","declaredType":"exclusiveGateway","id":"be1fda0a-2124-42a9-b08c-6f2778ba20a7"},{"targetRef":"df768d6d-ea25-4c7e-9ad3-191b2c5a5197","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.hubCode != \"\" &amp;&amp; tw.local.hubCode != null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"45d004ef-92f8-45ef-939a-2cc599b74b5e","sourceRef":"be1fda0a-2124-42a9-b08c-6f2778ba20a7"},{"startQuantity":1,"outgoing":["f0a4ce49-8c69-46aa-8d07-bd63a9926657"],"incoming":["14d72347-5405-4406-8dea-937a299f9300"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":266,"y":210,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Branch team","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1ef0195c-59c4-43c1-8034-2aaecfd6310c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.filteredTeam = new tw.object.Team();\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\n\r\nlog.info(\"filtered team before branch call# \"+tw.local.filteredTeam.name);\r\n\r\nvar branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;\r\nvar group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;\r\n\t\r\nlog.info(\"users::: \"+ branch_users);\r\n\tfor (var i=0;i&lt;branch_users.listLength;i++)\r\n\t{\r\n\t\tfor(var j=0;j&lt;group_users.listLength;j++)\r\n\t\t{\r\n\t\t\tif(branch_users[i].name == group_users[j].name &amp;&amp; group_users[j].name != \"\") \r\n\t\t\t{\r\n\t\t\t\ttw.local.filteredTeam.members[j] = group_users[j].name;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t}\r\n\tlog.info(\"Filtered team :# \"+tw.local.filteredTeam);"]}},{"targetRef":"1ef0195c-59c4-43c1-8034-2aaecfd6310c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"14d72347-5405-4406-8dea-937a299f9300","sourceRef":"be1fda0a-2124-42a9-b08c-6f2778ba20a7"},{"targetRef":"73a9caa3-fdf0-480a-8f44-9e4f3d05fa04","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f0a4ce49-8c69-46aa-8d07-bd63a9926657","sourceRef":"1ef0195c-59c4-43c1-8034-2aaecfd6310c"}],"laneSet":[{"id":"93b10567-2d65-4b8f-b412-4a8dfadcdd2a","lane":[{"flowNodeRef":["f06475b9-f5f4-407b-ba1f-915062a54008","73a9caa3-fdf0-480a-8f44-9e4f3d05fa04","df768d6d-ea25-4c7e-9ad3-191b2c5a5197","be1fda0a-2124-42a9-b08c-6f2778ba20a7","1ef0195c-59c4-43c1-8034-2aaecfd6310c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"06a8a8fb-0210-461e-933e-44b52e029d8c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Branch Hub filter service","declaredType":"process","id":"1.8f81ee30-b515-498c-b076-7fcf05c5013d","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"filteredTeam","isCollection":false,"id":"2055.6330b2e0-827d-4902-8f44-e58e62025b66","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{"dataInputRefs":["2055.312f03e5-b96b-4b91-a7bf-de82b2b3c5e6","2055.eadef062-dcf6-4826-a900-663f318749b1","2055.b99f37cb-08f3-48eb-a87e-9c60a3f55a34","2055.dc7410ee-d8a4-47ee-80ca-dee898908e61","2055.cc56ddc5-6fd5-4f0a-844f-396fb976f185"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"originalTeam","isCollection":false,"id":"2055.312f03e5-b96b-4b91-a7bf-de82b2b3c5e6","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"BR001_SHR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.eadef062-dcf6-4826-a900-663f318749b1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"077\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubCode","isCollection":false,"id":"2055.b99f37cb-08f3-48eb-a87e-9c60a3f55a34"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"BPM_IDC_BR_COMP_REP_CHKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchGroupName","isCollection":false,"id":"2055.dc7410ee-d8a4-47ee-80ca-dee898908e61"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"BPM_IDC_HUB_077_COMP_CHKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubGroupName","isCollection":false,"id":"2055.cc56ddc5-6fd5-4f0a-844f-396fb976f185"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.312f03e5-b96b-4b91-a7bf-de82b2b3c5e6</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8a276f83-b251-41f7-af5d-f09afb8d48cb</guid>
            <versionId>eff3103c-c660-451f-890b-558885dc3991</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.eadef062-dcf6-4826-a900-663f318749b1</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>54dac0ca-d322-459c-a01b-02aa4e879ed6</guid>
            <versionId>424bcff6-878d-417d-8754-3d5c9d21f00b</versionId>
        </processParameter>
        <processParameter name="hubCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b99f37cb-08f3-48eb-a87e-9c60a3f55a34</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>01d4f94f-35a6-459e-ae24-3e40d8acb32f</guid>
            <versionId>a70e7eab-defc-4afe-8fa0-d8e57156fef7</versionId>
        </processParameter>
        <processParameter name="branchGroupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dc7410ee-d8a4-47ee-80ca-dee898908e61</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>12269df1-71b4-4935-baec-1f4f2fee6bfa</guid>
            <versionId>89515707-1ac5-4b01-a4f1-93bf92ebf37e</versionId>
        </processParameter>
        <processParameter name="hubGroupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cc56ddc5-6fd5-4f0a-844f-396fb976f185</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cd49bc00-567a-4224-9dbc-bd267440fb94</guid>
            <versionId>0ad5ab24-4fdc-4b8b-992f-b1802d66c05a</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6330b2e0-827d-4902-8f44-e58e62025b66</processParameterId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1517c4b1-ac0c-4469-b874-e22037e9d289</guid>
            <versionId>e23be360-c0fc-48df-b8d3-1a7147e06a02</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1ef0195c-59c4-43c1-8034-2aaecfd6310c</processItemId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <name>Get Branch team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7045d4df-f64c-46a1-a62e-5eb9e4461bf5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18939e75821:-16e7</guid>
            <versionId>0983be7b-3204-41eb-baef-4576b989b84c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="266" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7045d4df-f64c-46a1-a62e-5eb9e4461bf5</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
log.info("filtered team before branch call# "+tw.local.filteredTeam.name);&#xD;
&#xD;
var branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;&#xD;
var group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ branch_users);&#xD;
	for (var i=0;i&lt;branch_users.listLength;i++)&#xD;
	{&#xD;
		for(var j=0;j&lt;group_users.listLength;j++)&#xD;
		{&#xD;
			if(branch_users[i].name == group_users[j].name &amp;&amp; group_users[j].name != "") &#xD;
			{&#xD;
				tw.local.filteredTeam.members[j] = group_users[j].name;&#xD;
			}&#xD;
		}&#xD;
		&#xD;
	}&#xD;
	log.info("Filtered team :# "+tw.local.filteredTeam);</script>
                <isRule>false</isRule>
                <guid>e3a333ec-4008-4c06-b03f-b8bbd7dd45ef</guid>
                <versionId>3a02939b-86a1-42a8-b26c-a279cfb1da87</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df768d6d-ea25-4c7e-9ad3-191b2c5a5197</processItemId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <name>Get HUB team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c018a0d0-cd09-4d69-97f3-754c9ae7027b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ad1</guid>
            <versionId>3191437d-a89a-4359-aaa5-78745bb94a58</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="278" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c018a0d0-cd09-4d69-97f3-754c9ae7027b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</script>
                <isRule>false</isRule>
                <guid>e895e1ab-1df8-4547-bccd-1afeed6cf129</guid>
                <versionId>f3f088e3-5d03-4add-aa1a-db7b79dd2fa3</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</processItemId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.af90d637-d384-4284-8f6f-814955cc1a0c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ad0</guid>
            <versionId>940dc6e0-e71a-49af-8c0f-4799016a4bb4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.af90d637-d384-4284-8f6f-814955cc1a0c</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>27aa368b-9a7f-460d-bdaa-9e1199f70e90</guid>
                <versionId>7f660bc0-5c28-43e7-b799-d115e6c5fa0c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</processItemId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <name>IsHub?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.f4f2b13b-5ab4-4518-914d-d745e10e7993</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ad2</guid>
            <versionId>a30c5da4-0071-4264-a5f0-10dff5d4b16c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="125" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.f4f2b13b-5ab4-4518-914d-d745e10e7993</switchId>
                <guid>01f926ac-ac5e-4dfd-be96-fe88dd465156</guid>
                <versionId>104ce563-d2e9-41ac-a929-56499a1c439c</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.76a1f22e-0caf-4198-bdc8-32fcf121f16d</switchConditionId>
                    <switchId>3013.f4f2b13b-5ab4-4518-914d-d745e10e7993</switchId>
                    <seq>1</seq>
                    <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-cf2</endStateId>
                    <condition>tw.local.hubCode != "" &amp;&amp; tw.local.hubCode != null</condition>
                    <guid>b9c1bfe3-e87c-470e-92b4-ac9ffd8c50e5</guid>
                    <versionId>5076a10a-93ed-44c0-a864-3980787415b2</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Branch Hub filter service" id="1.8f81ee30-b515-498c-b076-7fcf05c5013d" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.312f03e5-b96b-4b91-a7bf-de82b2b3c5e6" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.eadef062-dcf6-4826-a900-663f318749b1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BR001_SHR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="hubCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b99f37cb-08f3-48eb-a87e-9c60a3f55a34">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"077"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="branchGroupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.dc7410ee-d8a4-47ee-80ca-dee898908e61">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BPM_IDC_BR_COMP_REP_CHKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="hubGroupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.cc56ddc5-6fd5-4f0a-844f-396fb976f185">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BPM_IDC_HUB_077_COMP_CHKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.6330b2e0-827d-4902-8f44-e58e62025b66" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.312f03e5-b96b-4b91-a7bf-de82b2b3c5e6</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.eadef062-dcf6-4826-a900-663f318749b1</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b99f37cb-08f3-48eb-a87e-9c60a3f55a34</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.dc7410ee-d8a4-47ee-80ca-dee898908e61</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.cc56ddc5-6fd5-4f0a-844f-396fb976f185</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="93b10567-2d65-4b8f-b412-4a8dfadcdd2a">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="06a8a8fb-0210-461e-933e-44b52e029d8c" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f06475b9-f5f4-407b-ba1f-915062a54008</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>df768d6d-ea25-4c7e-9ad3-191b2c5a5197</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>be1fda0a-2124-42a9-b08c-6f2778ba20a7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1ef0195c-59c4-43c1-8034-2aaecfd6310c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f06475b9-f5f4-407b-ba1f-915062a54008">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.aec004eb-335b-40d9-a5c9-abbea44346c9</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="73a9caa3-fdf0-480a-8f44-9e4f3d05fa04">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ad0</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>12b9015a-6e57-4345-8a10-64892b251308</ns16:incoming>
                        
                        
                        <ns16:incoming>f0a4ce49-8c69-46aa-8d07-bd63a9926657</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f06475b9-f5f4-407b-ba1f-915062a54008" targetRef="be1fda0a-2124-42a9-b08c-6f2778ba20a7" name="To IsHub?" id="2027.aec004eb-335b-40d9-a5c9-abbea44346c9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get HUB team" id="df768d6d-ea25-4c7e-9ad3-191b2c5a5197">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="278" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>45d004ef-92f8-45ef-939a-2cc599b74b5e</ns16:incoming>
                        
                        
                        <ns16:outgoing>12b9015a-6e57-4345-8a10-64892b251308</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="df768d6d-ea25-4c7e-9ad3-191b2c5a5197" targetRef="73a9caa3-fdf0-480a-8f44-9e4f3d05fa04" name="To End" id="12b9015a-6e57-4345-8a10-64892b251308">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="14d72347-5405-4406-8dea-937a299f9300" name="IsHub?" id="be1fda0a-2124-42a9-b08c-6f2778ba20a7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="125" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.aec004eb-335b-40d9-a5c9-abbea44346c9</ns16:incoming>
                        
                        
                        <ns16:outgoing>45d004ef-92f8-45ef-939a-2cc599b74b5e</ns16:outgoing>
                        
                        
                        <ns16:outgoing>14d72347-5405-4406-8dea-937a299f9300</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="be1fda0a-2124-42a9-b08c-6f2778ba20a7" targetRef="df768d6d-ea25-4c7e-9ad3-191b2c5a5197" name="Yes" id="45d004ef-92f8-45ef-939a-2cc599b74b5e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.hubCode != "" &amp;&amp; tw.local.hubCode != null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get Branch team" id="1ef0195c-59c4-43c1-8034-2aaecfd6310c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="266" y="210" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>14d72347-5405-4406-8dea-937a299f9300</ns16:incoming>
                        
                        
                        <ns16:outgoing>f0a4ce49-8c69-46aa-8d07-bd63a9926657</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
log.info("filtered team before branch call# "+tw.local.filteredTeam.name);&#xD;
&#xD;
var branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;&#xD;
var group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ branch_users);&#xD;
	for (var i=0;i&lt;branch_users.listLength;i++)&#xD;
	{&#xD;
		for(var j=0;j&lt;group_users.listLength;j++)&#xD;
		{&#xD;
			if(branch_users[i].name == group_users[j].name &amp;&amp; group_users[j].name != "") &#xD;
			{&#xD;
				tw.local.filteredTeam.members[j] = group_users[j].name;&#xD;
			}&#xD;
		}&#xD;
		&#xD;
	}&#xD;
	log.info("Filtered team :# "+tw.local.filteredTeam);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="be1fda0a-2124-42a9-b08c-6f2778ba20a7" targetRef="1ef0195c-59c4-43c1-8034-2aaecfd6310c" name="No" id="14d72347-5405-4406-8dea-937a299f9300">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1ef0195c-59c4-43c1-8034-2aaecfd6310c" targetRef="73a9caa3-fdf0-480a-8f44-9e4f3d05fa04" name="To End" id="f0a4ce49-8c69-46aa-8d07-bd63a9926657">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f0a4ce49-8c69-46aa-8d07-bd63a9926657</processLinkId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1ef0195c-59c4-43c1-8034-2aaecfd6310c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</toProcessItemId>
            <guid>707368d3-6f74-4f34-afa3-7cabfbb81e86</guid>
            <versionId>05db2b2c-021c-4a23-869e-bd48f6333fc4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.1ef0195c-59c4-43c1-8034-2aaecfd6310c</fromProcessItemId>
            <toProcessItemId>2025.73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.12b9015a-6e57-4345-8a10-64892b251308</processLinkId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.df768d6d-ea25-4c7e-9ad3-191b2c5a5197</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</toProcessItemId>
            <guid>4927ba18-513e-45e8-ad32-5c9af596bba1</guid>
            <versionId>5d69dc29-5e0a-4605-9dcf-1f1e4fa9dc49</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.df768d6d-ea25-4c7e-9ad3-191b2c5a5197</fromProcessItemId>
            <toProcessItemId>2025.73a9caa3-fdf0-480a-8f44-9e4f3d05fa04</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.14d72347-5405-4406-8dea-937a299f9300</processLinkId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.1ef0195c-59c4-43c1-8034-2aaecfd6310c</toProcessItemId>
            <guid>a7f45019-ff1c-4eea-83fe-a0617552fcd3</guid>
            <versionId>b98f095d-23d2-4692-9c8a-cc6295f4614d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</fromProcessItemId>
            <toProcessItemId>2025.1ef0195c-59c4-43c1-8034-2aaecfd6310c</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.45d004ef-92f8-45ef-939a-2cc599b74b5e</processLinkId>
            <processId>1.8f81ee30-b515-498c-b076-7fcf05c5013d</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</fromProcessItemId>
            <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-cf2</endStateId>
            <toProcessItemId>2025.df768d6d-ea25-4c7e-9ad3-191b2c5a5197</toProcessItemId>
            <guid>3aa6fe76-e34c-48e6-9dd7-7115fd7fdd94</guid>
            <versionId>c91dd215-6eaa-41c6-a84e-f36f97c92c61</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.be1fda0a-2124-42a9-b08c-6f2778ba20a7</fromProcessItemId>
            <toProcessItemId>2025.df768d6d-ea25-4c7e-9ad3-191b2c5a5197</toProcessItemId>
        </link>
    </process>
</teamworks>

