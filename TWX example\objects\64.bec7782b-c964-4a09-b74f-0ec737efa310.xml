<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.bec7782b-c964-4a09-b74f-0ec737efa310" name="Basic Details">
        <lastModified>1692017366785</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;8b08a310-3944-4f66-84ad-3c6d0278597f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Error_Message&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f9eb0efe-eaf1-4a22-809f-e427e4cc321d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Error Message&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4d728a1-da41-4514-8e3e-f361ae376b54&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db913d57-2ffd-40b4-8bd0-3f5f65ef1c23&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0cdd91fe-4d08-4f64-80fa-c3a2336ba3dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;alertMessage&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.alertMessage&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;80c24de4-b228-41c3-8a0a-f0d12853bb80&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"NONE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6bbc3342-3760-4499-810c-f99be2c39289&lt;/ns2:id&gt;&lt;ns2:optionName&gt;errorVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.errorVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;44599af5-6560-4612-8c3e-b27a60e26433&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2dbf99c-4b10-4132-8114-e03162608790&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Basic Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;64d8a1cf-f361-43eb-8649-0fa3d4a7f4c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aabd8070-1b16-4eca-8acb-b4d689b74a90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae2a6212-2d0b-44b3-8ecd-3f7b973aaf09&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bdd5a9c8-0f29-446e-82dd-6b00c3eac9fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorfulBody&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77eff10d-944d-4440-8760-d789e6ce70ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;lightColor&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;8308cb1d-2a10-4b73-8484-d61b225e9159&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;bb75c642-36b6-4b1d-80bf-3f3a03041c4a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;098d6611-d3fd-47ae-86b1-e319e5f07cc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;c568cdad-4d96-4f17-879d-e85e41286556&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f1cecc0-aeb1-4796-8c2e-3cec0d0bbbca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;09bebbde-ea1f-4f45-8979-84d50cacdedf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4fe1ea2-783c-4540-8d1a-5b23aa9747df&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2d857653-819e-43ba-8cf6-5441c6be1d97&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestNature&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;302024f4-c577-4a67-8d32-2f503368df56&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestNature&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b6e580a-710f-4852-879a-477b573d9703&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eb19bc37-cb0e-42ee-8bcf-2b5757add02e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d60e010-a7a1-44fd-819c-d656539a7c0b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.IDCRequestNature.englishdescription&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a9484845-e493-4ec4-8820-dc6bfb2e36cf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ContractStage&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c78afc89-8850-4f69-8d68-0ae5a4f69c23&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ContractStage&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a5ad5c78-f506-49e8-8cc9-57b861305e31&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;73c1b93c-feba-49d8-8787-4b1540497972&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e93ed295-29f2-4da2-83d7-10a67c4c2f8c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.IDCRequestStage&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f254b5df-d353-40cb-82a3-8547899d39b0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;parentIDCRequestNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;607438d6-**************-daa0974c414e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ParentIDCRequestNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c8075a5f-0211-4aba-8c6a-f74c2195fc9f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa3aef6f-0f36-41a9-8b58-e75c9619d984&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c968275-58e1-49ce-8050-d5080dd6258a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aaf200d7-5a09-46ab-8c04-b41994ca5465&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.haveParentIDCRequestNumber();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.ParentIDCRequestNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;b6a75642-872c-4536-8da6-36f81766c1bb&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b158a1d7-892a-4c5e-858d-407b2a83714c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;30335b33-31d6-43a0-8159-8f7d75fdbcb7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestType&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;239fd42e-b16e-4bce-8a2a-c9319c2bf64e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8fe92a33-21b1-4864-84a1-4915b039276a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;64131fe1-36da-4fc5-80f3-9764744e4144&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;739a0673-6af6-4fde-8829-cb505ed02d2a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.IDCRequestType.englishdescription&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7944ead9-370c-49e7-8151-54be22890409&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestState&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d0c927c6-e696-4c29-81e8-af6f7d383a34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestState&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef1a5227-e661-46e8-800e-2a9294fa4065&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6989677a-a26a-42be-8fa2-bde841599fb9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9c2edc5b-d5e0-4d3e-87a6-979caf990ffb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.IDCRequestState&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fbc5f1be-9e3b-4e8b-829a-f8eed9b620aa&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;FlexCubeContractNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6534655c-e45d-44b1-89fb-9c51a55a39d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.FlexCubeContractNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ac4206c-1ce8-4906-813e-636e20741f5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71c26b81-34ba-4aca-8800-f3fa04f62710&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cdcda2d5-e935-4ff0-8580-4802ebc22a4e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8261a45-04c0-4572-8d47-d7c308cecc02&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.haveFlexCubeContractNumber();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3f52a0a6-38c3-4ca9-832b-d10950bf77d6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 16)
{
	me.setValid(false , "max lenght is 16 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.FCContractNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;c533abd2-97e4-4686-856c-440f2f6fe7db&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0236a97b-8c81-4189-8352-773b01f74d3c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;b6505198-7ae9-47c7-8100-21dbcf6c3b29&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e367b0e9-2f28-44db-85ae-66734a7c0921&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;d225f678-070a-4634-8538-32e0037f3742&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e49c2a85-eef0-4612-85bc-8bdfee7287ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;05bc4e13-4f3f-4d09-8c80-e47445d8ec6b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;customerisSanctionedbyCBE&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4464c91a-8db4-4718-83a1-8b742f81bfa3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerisSanctionedbyNBE&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f6c7b355-74fa-495b-8adb-39d1a72b9372&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a738caf-ba60-490e-8128-75a23da372ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50784907-97ab-48d1-8df9-dff4a2011610&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b5c2e852-1d60-4e53-841e-6e19629291b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;W&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3e1143f1-41ec-443a-8231-14db964c5d8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3ea5a6f-a35f-4d0d-82e6-fda46eee041e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;onLabel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Yes&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f453e0b0-6b03-4de3-8a7b-ca2a6a268716&lt;/ns2:id&gt;&lt;ns2:optionName&gt;offLabel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;No&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a5b01116-74ae-451c-8067-c1220e811074&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;026c86be-5765-45da-88f1-429b211190dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData() == "YES"){
	document.getElementById("text-input-Basic_Details1:customerisSanctionedbyCBE").style.backgroundColor = "#eb8724";
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.customerInformation.isCustomeSanctionedbyCBE&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;55cc86bc-7c2b-438f-8a70-4207bbd2a5db&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;78426fd3-c13a-4e05-861a-111ea185bead&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;51eca001-1a30-4d1c-81bc-6ff4f95686a5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4768df9-f0a1-457b-8e87-d1747b7082ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Withdraw IDC Request&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49b83598-7d38-4c9f-8f6f-7eb7c2398da0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81779c58-f463-436e-8895-a97bf75a1997&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2e25e778-b3d1-4083-89f6-94a8ba0b0980&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb9bc878-746b-4877-8ccd-ab86a91199a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c3aef595-d1a6-4199-82f7-c801a38f7ec1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Switch1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09947771-9f5a-426a-8ffe-7f12810aa603&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;MarktheIDCasWithdrawn&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e0a8a21-e902-4dcc-889d-113fc699a5ae&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f1fa362-4254-4417-8c7c-dd59970b780a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f526bfa7-565f-48de-8f6a-9b58b24867d6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;onLabel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Yes&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1aa8874f-92ca-4274-83ad-8d377bb3d9f5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;offLabel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;No&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df1d7485-5360-404c-8366-a4fe573131ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;W&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5c15b9f0-f210-47f2-8159-de5e0f880cf9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;D&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1a922932-6f94-427d-894c-665d0ab886d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;labelType&lt;/ns2:optionName&gt;&lt;ns2:value&gt;T&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ecfc5a60-45d0-482b-873c-abdcf016e5e5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75f21087-9001-47c1-80db-064a88c83bb0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.hasWithdrawRequest();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87ce6584-caec-4b06-808a-1c9be8866445&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.bff9f5ff-fea4-4a70-87a5-************&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.isIDCWithdrawn&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;160020ef-0e78-48eb-8fe1-ceafe8ef2552&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b757422-bcc4-4a19-80bf-4d4e83bc187f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;6bf76599-c060-4486-8fab-0b484106c91d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;88ff7d29-3fa8-4f64-8115-53d3ab74c498&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;7006667d-17ff-464d-8aa0-29baaaee3a0f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5744bea-401e-481f-8df8-881164678513&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;166c4c53-eed8-4f30-8c99-9b48589ecdd5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ImportPurpose&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f875cbf8-04ff-4948-87fa-3eb66b6e9990&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ImportPurpose&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fcec3260-128e-490a-88c4-daad2474191c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51037f9b-6392-442d-8f3a-29bb670c03d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5ed4493-c221-4074-80a1-46ceee6b0ab9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad165610-6f84-4f28-8e74-1bdb5698178a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4daa053-c0d2-40f4-8b80-5fdf07a99c79&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b6d0264-bb1c-4aa0-8a64-b23491b12b21&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06c55e8f-2efe-4da4-86c4-f89d9595d1bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Import_Purpose&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f81fe6d7-51e6-45fe-81d9-20304594f94b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.importPurpose&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8d7f9c8b-2049-4ee1-8904-1a287e8c33cf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DocumentsSource&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c6eab602-15af-4b5b-8a3e-bc92c77f5491&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DocumentsSource&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72c0845f-7e2a-4f97-889e-b5a8e4bb6684&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dedcd7c5-5fd8-42cb-8000-8742b3c50d98&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4353b84f-bed5-4a9f-8763-a7d5948dea7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;909108d9-1d27-4748-8730-b2b51c0abbac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1bcc8ba1-68c2-44de-870f-18c82f9c54a0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8583097a-c9c8-4ac7-8949-950a20b0b040&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82e5bf5e-aa3f-4fb6-8261-3d75515f0cd2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Documents_Source&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8fc99243-e00a-417b-8b2b-a2a92c6999ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.documentsSource&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;70114c33-c97e-44b0-85d0-f059ec9fa360&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CommodityDescription&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe736f0e-251a-486f-888c-7127cac6934e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CommodityDescription&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a80c991-a558-4e4a-8d2f-5825cceef729&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f7f0fb8c-7408-4a9e-8863-d2b7280d0708&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c071b565-36fa-47f1-8905-a23f429e713a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 160)
{
	me.setValid(false , "max lenght is 160 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.commodityDescription&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;a18fc6d0-ad9a-42d0-8966-8ceaf564de43&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb1f9eee-cb16-4f8a-8941-4852055d43ae&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;17532eb6-c87e-48a5-8100-927c19ba7861&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;PaymentTerms&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;79512503-3ece-4ebc-84a3-92a613076872&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.PaymentTerms&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82d8ef0a-fcbe-48da-8657-f3b08735e9bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e52d8588-3e15-4cb8-82ba-2553e35c835f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f89bf7dd-c8e6-4423-8adf-faf5dff9fabb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;269d62db-b52f-4ce9-8875-137777c2e1d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a5c1c2b-df55-49c5-8269-4e8eb43a4b72&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06699ac0-424d-4be0-8f2e-7fb6b9bfbac9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0a0cde47-5ae0-4b14-8795-96eef4c24113&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Payment_Terms&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bff8f5d3-eb95-4c13-8347-7e6e3c5b2569&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.havePaymentTerm();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;844125b6-6ce3-4a86-88ca-d980ebf2675f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.havePaymentTerm();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;868a8879-65d8-4496-8a4a-9b3296e37312&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.paymentTerms&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;112f99d3-d1a2-45bd-8e3d-efbd7946656d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ProductCategory&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;618e120d-db0f-4d87-8a86-09a0817e5e61&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ProductCategory&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54c4e77e-4d8a-4d30-834a-0c5488b8b9ec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c59e0afc-c6cc-4d17-833c-650067256e45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1502e91c-5369-4462-8df2-37174f3d95a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb88eae5-306c-43c8-8273-b55392f6b101&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9ee1032-9b4b-4940-8b06-5f735e24829c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8fa2beb5-c7ff-442c-8865-4c80b34f0cf9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9be3884-f1b4-4dfc-85e3-a83b13826f4f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_NBE_Product_Category&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a01067b-0ce0-4e24-88a2-7ebc663322e4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.productCategory&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3b4f0404-d2df-4ba7-8711-259d7fd62b14&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CountryofOrigin&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8887bc0-19c4-4fbd-87f9-bf8d298ed2af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CountryofOrigin&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7053927c-a4a1-43fa-8fc8-0bb6432cadd0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;389694e8-4a76-4863-894e-1cf79e9951bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5091ab3b-6477-403c-8212-5f0d1cd6c595&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d743fa87-c32b-4f61-8d86-97f38d0d4147&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0cc65cd8-8105-4c01-843a-d0d31c6caef2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af4a9308-751b-4738-85df-5c5f5f091dbf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.b24f815d-f98b-4f2e-9dbe-f7601985749f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;44a500ea-92d0-476e-8eb7-15d1a3d7ca1f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.countryOfOrigin&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a6c48d3d-a640-4ec1-8d17-46fb23e81df1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Table1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95a95596-d208-48c8-80c4-676e99f26f76&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Invoice&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59515060-e0b3-4c40-8466-badbf904939b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb5cdb2c-53ec-4423-859f-9b6aad070197&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bbfe9812-6d47-4393-8785-a8829a98ecc0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addInvoice&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38ae400f-cc92-4771-83db-3c0f0c9a2867&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.deleteInvoice&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;88abcf4e-97be-4366-848f-7237c8f3ceb7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b745a135-37a5-4267-8136-c243742e8bc5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fce38caf-32df-44d0-828e-c7b927a2420f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5acfd87a-454d-4dcc-8835-b92f7429fb72&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getRecordIndex(item) ==0){
alert("You can not delete this row");
return false;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.invoices[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ec7954e5-e8a4-4be3-80af-a028b6c8109d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0f0c9497-fab2-45fe-8038-7debf9ba3806&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;InvoiceNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bad4b996-a92e-44a9-8960-5026ff88459e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;29e591a0-9f1e-469e-873a-7a033b097e26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InvoiceNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51ec01bb-ed4b-410f-8273-58e6de8cf934&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2454a3d1-1cba-4dcc-8bba-6aa6f5123f04&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e97f6fe-2bf9-49e3-8833-e37ada6d4dd3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
if(potential.length &amp;gt; 20)
{
	me.setValid(false , "max lenght is 20 characters");
	return false;
}
else
{
	me.setValid(true);
	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d39591ba-36ce-4a9f-8532-bcc00a37ff73&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@checkInvoice(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.invoices.currentItem.number&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;93997aca-dffe-424f-8984-edcb8be047fb&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;InvoiceDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4eef108-ad18-4ae3-8a65-abd323f5bf4b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d60e5be8-74da-4e52-8cd1-f83cca17ec04&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InvoiceDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6eee8a30-cbf4-4ade-8aa3-cabf5fd10add&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95538a60-efd1-484a-896c-2d673c690c35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb8f59e8-9d88-49c6-8925-263e5ac817fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4c4dcf0-4df5-4aef-83a9-70556093691d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68e1d324-4fa7-4fbc-8f91-1dd199e73a33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;yearSelectorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Default&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;18397b66-8470-432a-82e1-595dc795dae2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;highlightToday&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0ac3c2b8-8f20-46f3-8498-22a238048cc7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.invoices.currentItem.date&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;dbcebc29-9c8e-448b-8a83-2c1bf1129e4e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;checkInvoiceCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4d1fede-36f5-4026-81ea-a9278ac2cf9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;check Invoice&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;301f6396-1bcc-4b42-8f9e-ba283b5b4a3e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe426bf5-bd3d-40f0-83fd-d3e1d84793ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;48dddcdf-c9c0-47d9-871d-2a6c158cc8d6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setInvoiceValidation();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d196c50-2945-4b84-8cf9-8761c386b578&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.5b59000d-e60c-4dee-87e1-34bfc64da51b&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b50915f0-b1c2-49bd-8be8-e2be6395ab13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);
//console.log(error);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;608832bc-5294-4997-88b3-a31559f76967&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.invoiceExist&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1523509f-a6b9-4747-8631-174b7dec879a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BillTable&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4faeb7e-a3e0-4215-80b3-4b0f8859f9b6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Bill&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a4a8aa5-b38e-4a23-8827-1be23292dd5d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d37d2af3-f473-442b-82a2-a50bad610df7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ab721e0-b7ee-45ce-8df8-f5e187e6b0bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.deleteBill&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;369289b9-7934-4990-8f76-6e7df54c8a0f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addBill&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5dcdcb93-965e-4ee1-8095-0016b5e885c6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;862e42e1-b7f6-43e5-84a5-b76aa4de0d2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7449d6cb-7518-4714-8173-e8c0e53f45e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowWrap&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;685542f7-bce0-4d65-8184-2c8a81769b58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;headerFooterStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Default&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b87eb621-08ab-48cb-8f0b-edbf41f6da4c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b0d753f-1462-409b-8238-a25aa0a3394d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getRecordIndex(item) ==0){
alert("You can not delete this row");
return false;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.billOfLading[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c088ba9e-f524-4d09-86a0-d8e673f48780&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;09af8b5c-db85-47a1-8e72-3af82cbfd2b2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BillofLadingRef&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1950eec-e9b3-4ede-9084-2286e3fe542b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cf84909a-a5a0-4c8d-88da-e147c95705e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BillofLadingRef&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c6b20bb-eb09-42bc-8bb2-db59b49e48c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97bb5c31-34bf-4c2f-8eb0-1d3bb1704576&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;66d1de0f-ce06-4cbb-859f-72dad94893a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 20)
{
	me.setValid(false , "max lenght is 20 characters");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;66cd4dac-5b14-4ab5-8050-fe1b816af1d4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@checkBill(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.billOfLading.currentItem.number&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;aa0f1387-ef97-4487-81b7-9ddabead6529&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BillofLadingDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8359780-8d7d-4ef2-9891-7e03f485d6d8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06a87ab3-dffd-48cf-80fc-e73312ffa51b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BillofLadingDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;735da842-b29c-4ba6-84fe-f72a52b36969&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7e60bef0-6422-4891-86f3-50d9477a26ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dc9b35aa-2d0c-44fc-827e-746711d40dfd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c644fa19-d41e-498f-8d1e-b0deef55768c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a06802f7-02ef-4d14-88b8-e554abe1e143&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCrequest.billOfLading.currentItem.date&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2f8ee9b5-859e-456c-8c7b-ee8c61812a10&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1549db31-4c5b-4c71-8fa6-38ad5a614e33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dd06f711-51e5-4dad-8061-bb59537a6645&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;12cd26c3-35af-48e8-8995-099ded9a5663&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d71c7599-6cea-4268-8fc7-3aa8e5179ecc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;checkBillCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;508fa8b4-653f-4fa1-85fb-db806c523df9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Check Bill&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b948744-ae4c-49f3-809f-be2afc3e41a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3638aa95-985e-4ea4-8c6f-90245f3730f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9f173237-8484-412d-8628-36fb16e8c7e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;466c360c-ca76-4190-86b6-1a95dc63dba1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setBillValidation();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;abf7d5d3-5e6f-402c-85fc-f44084e23e1e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.billExist&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction></validateJsFunction>
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>3097f1ab-c8d0-49b7-b672-8bf14c3c47e7</guid>
        <versionId>2015adea-0de0-452d-b1ac-974b47bb9e44</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="IDCrequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.a33b1292-67af-4e9c-9f37-53a3b688569c</coachViewBindingTypeId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>9bdde9f5-bdf1-4835-a5e3-a508081a05a0</guid>
            <versionId>6178052a-2e2e-46da-b41e-d3bc973e6643</versionId>
        </bindingType>
        <configOption name="addBill">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.4ce7ad39-a4ff-44a2-9b12-f158cfd90a8d</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>dc6bf159-a9b4-42e2-8404-657b7340e6bb</guid>
            <versionId>e39727f2-ca7a-4871-9d61-dc54503c64ab</versionId>
        </configOption>
        <configOption name="deleteBill">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e797af91-8c22-4e81-9c44-f64d955441a5</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>5bbe0a43-cd98-4661-a449-a12fe1555ac3</guid>
            <versionId>028d26ff-9659-4b9b-ae5a-9841bc19d7f3</versionId>
        </configOption>
        <configOption name="addInvoice">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.1c09e389-b09f-4f07-a760-e72c5b352c08</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>2781bff6-f268-4cc1-bc5b-0723a6150079</guid>
            <versionId>1f509253-af66-425c-8c45-10829faa2a1e</versionId>
        </configOption>
        <configOption name="deleteInvoice">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.f80b66bf-9c4a-4d98-a09c-eb0c95b358e6</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>83b73879-4b0c-47d6-a913-59a43da30fab</guid>
            <versionId>f654beca-7acb-4926-82b0-91b3eed92738</versionId>
        </configOption>
        <configOption name="hasWithdraw">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c3254f82-18d5-4fb4-adf7-9b2995eeaf4c</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>42495c01-1858-48b8-88ea-88a91f4100c6</guid>
            <versionId>2fd7c9a0-7da6-4d09-be49-84f0af153519</versionId>
        </configOption>
        <configOption name="havePaymentTerm">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.1e9518f3-0325-4087-9616-cb054f8b2e70</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>1466d098-9c3d-4156-b139-5ff0c59de0ca</guid>
            <versionId>163afd44-52ca-4695-b772-da6eae369121</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.148e17e9-bde3-45e0-86db-959ee74e0ed8</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>39e266f9-cc09-4e0d-84a0-3938a6bf6ab0</guid>
            <versionId>25e900c9-90fb-4782-9cd7-11e145d67175</versionId>
        </configOption>
        <configOption name="billExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8baa5d78-f304-4648-a7ca-2445ce8abd37</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>a5d75016-2e2e-4021-9ba9-41ccdb2abc92</guid>
            <versionId>5be61b19-de49-4195-881c-ad883a67e170</versionId>
        </configOption>
        <configOption name="invoiceExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.16ee766f-b9d6-4769-a653-17f91f55d4e4</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>e2aefb6d-c532-447b-8b35-0efd0e585b01</guid>
            <versionId>a6fc1262-9c49-4389-8a7f-06ccaa3cae83</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b083e753-7ae6-48b0-bf72-852c0cdab76b</coachViewConfigOptionId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>be21e633-95ee-4bba-a6e4-c4f3be4623dd</guid>
            <versionId>7584e55a-6d41-4af0-a6cc-d28d79709b54</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.cf5cfd8f-2e3d-4d3d-9c65-619764e23544</coachViewInlineScriptId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>// this.haveParentIDCRequestNumber = function  () {&#xD;
//	if (this.context.binding.get("value").get("IDCRequestNature").get("englishdescription") == "Update Request") {&#xD;
//		this.ui.get("parentIDCRequestNumber").setVisible(true,true);&#xD;
//		&#xD;
//	}else{&#xD;
//		this.ui.get("parentIDCRequestNumber").setVisible(false,true);&#xD;
//		&#xD;
//	}&#xD;
// }&#xD;
var bill = {};&#xD;
 this.checkBill = function (value) {&#xD;
 	bill =value;&#xD;
 	var input = this.context.binding.get("value").get("appInfo").get("instanceID")+"-"+value.getData();&#xD;
 	this.ui.get("checkBillCall").execute(input);&#xD;
 }&#xD;
 this.setBillValidation = function () {&#xD;
 	if (this.context.options.billExist.get("value") == 1) {&#xD;
 		bill.setValid(false,"WARNING : This Bill Is Used Before In Another Request");&#xD;
 	}else{&#xD;
 		bill.setValid(true);&#xD;
 	}&#xD;
 }&#xD;
 &#xD;
 var invoice = {};&#xD;
 this.checkInvoice = function (value) {&#xD;
// 	alert("checkInvoice");&#xD;
 	invoice =value;&#xD;
 	var input = this.context.binding.get("value").get("appInfo").get("instanceID")+"-"+invoice.getData();&#xD;
// 	alert(input);&#xD;
 	this.ui.get("checkInvoiceCall").execute(input);&#xD;
 }&#xD;
 this.setInvoiceValidation = function () {&#xD;
// 	alert(this.context.options.invoiceExist.get("value"));&#xD;
 	if (this.context.options.invoiceExist.get("value") == 1) {&#xD;
 		invoice.setValid(false,"WARNING : This Invoice Is Used Before In Another Request");&#xD;
 	}else{&#xD;
 		invoice.setValid(true);&#xD;
 	}&#xD;
 }&#xD;
 &#xD;
 this.haveFlexCubeContractNumber = function  () {&#xD;
// 	alert("test");&#xD;
 	if (this.context.binding.get("value").get("IDCRequestNature").get("englishdescription") == "New Request" &amp;&amp; this.context.binding.get("value").get("appInfo").get("stepName") == "IDC Execution Hub - Initiation") {&#xD;
 		this.ui.get("FlexCubeContractNumber").setEnabled(true);&#xD;
 	}&#xD;
 	else{&#xD;
 	this.ui.get("FlexCubeContractNumber").setEnabled(false);&#xD;
 	}&#xD;
 	&#xD;
 }&#xD;
 &#xD;
 this.hasWithdrawRequest = function  () {&#xD;
 	if (this.context.binding.get("value").get("IDCRequestType").get("englishdescription")=="IDC Acknowledgement") {&#xD;
 		if (this.context.options.hasWithdraw.get("value")) {&#xD;
	 		this.ui.get("Output_Text2").setEnabled(true);&#xD;
	 		this.ui.get("Switch1").setEnabled(true);&#xD;
	 	}	&#xD;
 	}else{&#xD;
 		this.ui.get("Output_Text2").setVisible(false,true);&#xD;
	 		this.ui.get("Switch1").setVisible(false,true);&#xD;
 	}&#xD;
	 	&#xD;
 }&#xD;
 this.havePaymentTerm = function  () {&#xD;
 	if (this.context.binding.get("value").get("paymentTerms").get("englishdescription") == "Sight") {&#xD;
 		this.context.options.havePaymentTerm.set("value",  "NONE") ;&#xD;
 	}else{&#xD;
 		this.context.options.havePaymentTerm.set("value",  "DEFAULT") ;&#xD;
 	}&#xD;
 }&#xD;
 //------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.ui.get("Error_Message").setVisible(true);&#xD;
	this.ui.get("Error_Message/Alert_Modal").setVisible(true);&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>77a86d34-7873-4658-8bb6-22412e4a42db</guid>
            <versionId>ba15c64e-c7af-420b-92d9-e6b00792ccb3</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.b36d04ea-1537-4671-ac7a-5a879cac495b</coachViewLocalResId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>6a2dab37-f84f-4f1c-9f22-c9fb47a44e7f</guid>
            <versionId>019d80f0-1316-4290-a3d0-44206f5758fb</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.59bc1547-86c1-4984-9d02-74789dcb81d8</coachViewLocalResId>
            <coachViewId>64.bec7782b-c964-4a09-b74f-0ec737efa310</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>c0a9f57d-fd4e-4109-9991-a5a0552b8cd2</guid>
            <versionId>af10006c-f4a1-4a15-87b5-7c4fbd4a5b62</versionId>
        </localization>
    </coachView>
</teamworks>

