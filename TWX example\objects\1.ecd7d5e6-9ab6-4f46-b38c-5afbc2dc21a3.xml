<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3" name="Authorize FileNet">
        <lastModified>1691502702140</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.2e6b2ebf-6356-4cb9-88e7-2e21827140f3</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>8da050fd-878e-4bd8-840b-93d609645c4d</guid>
        <versionId>f3ffbae2-1002-46d8-aae2-2da8c742857c</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:42738960b3d81f85:8740bc4:189d575e46d:-6be6" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"bdab7c37-bc60-4a11-b4f9-d583783059c7"},{"incoming":["d30c0851-24f0-4ac6-8428-451bdf738024"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b0f"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"41848348-4f0f-4a9e-8ee9-bd899433c938"},{"targetRef":"2e6b2ebf-6356-4cb9-88e7-2e21827140f3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Authorize","declaredType":"sequenceFlow","id":"2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c","sourceRef":"bdab7c37-bc60-4a11-b4f9-d583783059c7"},{"startQuantity":1,"outgoing":["d30c0851-24f0-4ac6-8428-451bdf738024"],"incoming":["2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":277,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Authorize","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2e6b2ebf-6356-4cb9-88e7-2e21827140f3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.authorized = true;\r\n\r\n"]}},{"targetRef":"41848348-4f0f-4a9e-8ee9-bd899433c938","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"d30c0851-24f0-4ac6-8428-451bdf738024","sourceRef":"2e6b2ebf-6356-4cb9-88e7-2e21827140f3"}],"laneSet":[{"id":"2d721183-9bf2-4812-93e4-b4ebd673c8d1","lane":[{"flowNodeRef":["bdab7c37-bc60-4a11-b4f9-d583783059c7","41848348-4f0f-4a9e-8ee9-bd899433c938","2e6b2ebf-6356-4cb9-88e7-2e21827140f3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"8d0e9a3d-2323-48e7-afbf-acd4bbe9e0e1","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Authorize FileNet","declaredType":"process","id":"1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"authorized","isCollection":false,"id":"2055.1cd10ed7-3fb2-4f6e-b3be-eabdc66b50de","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"documentId","isCollection":false,"id":"2055.bff2f346-8822-4abf-a8ce-0dc4cd4476f8","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"objectTypeId","isCollection":false,"id":"2055.2881361f-1037-4ce8-9462-78a94861e1cc","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":false,"id":"2055.31004335-f931-45ad-8780-2a985fef1ebb","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"serverName","isCollection":false,"id":"2055.d35385da-0cff-4efa-8914-999bb45d50a8","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="documentId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bff2f346-8822-4abf-a8ce-0dc4cd4476f8</processParameterId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>089ce2f3-6826-457b-b63c-13e1e16723f9</guid>
            <versionId>22cc868d-a2bf-4e7e-9b42-14c9b52b5082</versionId>
        </processParameter>
        <processParameter name="objectTypeId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2881361f-1037-4ce8-9462-78a94861e1cc</processParameterId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6f71d68e-ddf1-4615-8633-4a096633e964</guid>
            <versionId>beceff64-de3b-455e-876a-6d908f8eb9ef</versionId>
        </processParameter>
        <processParameter name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.31004335-f931-45ad-8780-2a985fef1ebb</processParameterId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>56429aab-3c32-43e7-8c5a-90f34dbc7378</guid>
            <versionId>4a912841-d341-49e1-bb9e-1be7f6d96c34</versionId>
        </processParameter>
        <processParameter name="serverName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d35385da-0cff-4efa-8914-999bb45d50a8</processParameterId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4400b234-ce80-4df4-a522-4c203d3f283b</guid>
            <versionId>228e8d1b-a90c-4c15-96a8-d9921bb3b3b0</versionId>
        </processParameter>
        <processParameter name="authorized">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1cd10ed7-3fb2-4f6e-b3be-eabdc66b50de</processParameterId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>be5cceae-b1b5-4928-9cad-f6d66be23c5d</guid>
            <versionId>62c68432-c6e6-41ff-95e2-d3ba2442f4e2</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2e6b2ebf-6356-4cb9-88e7-2e21827140f3</processItemId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <name>Authorize</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5597b560-c37d-4560-a866-4c2f32282382</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b0e</guid>
            <versionId>b1a44197-2694-4668-ba79-d96768f8fa16</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="277" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5597b560-c37d-4560-a866-4c2f32282382</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.authorized = true;&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>48d577e0-871b-46b7-9c42-e6d07f6ee977</guid>
                <versionId>702a38c9-123a-47b7-ac53-e0a830d972c1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.41848348-4f0f-4a9e-8ee9-bd899433c938</processItemId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ec0e513d-ff6a-4fbc-b6a2-fbfae6507e4e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b0f</guid>
            <versionId>cd365093-be82-4d56-a4fd-2f713a93ccee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ec0e513d-ff6a-4fbc-b6a2-fbfae6507e4e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>aa1b8802-c001-4c46-9c48-7723a10bce3f</guid>
                <versionId>2ce0b83e-c8e5-41ef-ab62-438b86e847ee</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.2e6b2ebf-6356-4cb9-88e7-2e21827140f3</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Authorize FileNet" id="1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="documentId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.bff2f346-8822-4abf-a8ce-0dc4cd4476f8" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="objectTypeId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.2881361f-1037-4ce8-9462-78a94861e1cc" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="action" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.31004335-f931-45ad-8780-2a985fef1ebb" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="serverName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d35385da-0cff-4efa-8914-999bb45d50a8" ns3:readOnly="true" />
                        
                        
                        <ns16:dataOutput name="authorized" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.1cd10ed7-3fb2-4f6e-b3be-eabdc66b50de" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2d721183-9bf2-4812-93e4-b4ebd673c8d1">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="8d0e9a3d-2323-48e7-afbf-acd4bbe9e0e1" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>bdab7c37-bc60-4a11-b4f9-d583783059c7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>41848348-4f0f-4a9e-8ee9-bd899433c938</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2e6b2ebf-6356-4cb9-88e7-2e21827140f3</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="bdab7c37-bc60-4a11-b4f9-d583783059c7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="41848348-4f0f-4a9e-8ee9-bd899433c938">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b0f</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d30c0851-24f0-4ac6-8428-451bdf738024</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="bdab7c37-bc60-4a11-b4f9-d583783059c7" targetRef="2e6b2ebf-6356-4cb9-88e7-2e21827140f3" name="To Authorize" id="2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Authorize" id="2e6b2ebf-6356-4cb9-88e7-2e21827140f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="277" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.7b3df880-c588-43d3-948e-e11ebdd2cf7c</ns16:incoming>
                        
                        
                        <ns16:outgoing>d30c0851-24f0-4ac6-8428-451bdf738024</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.authorized = true;&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2e6b2ebf-6356-4cb9-88e7-2e21827140f3" targetRef="41848348-4f0f-4a9e-8ee9-bd899433c938" name="To End" id="d30c0851-24f0-4ac6-8428-451bdf738024">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d30c0851-24f0-4ac6-8428-451bdf738024</processLinkId>
            <processId>1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2e6b2ebf-6356-4cb9-88e7-2e21827140f3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.41848348-4f0f-4a9e-8ee9-bd899433c938</toProcessItemId>
            <guid>e2895f2a-2c49-4104-8ff4-fcc2f5a6fbe3</guid>
            <versionId>6372f588-da66-4ffc-97ee-01a2d6652638</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2e6b2ebf-6356-4cb9-88e7-2e21827140f3</fromProcessItemId>
            <toProcessItemId>2025.41848348-4f0f-4a9e-8ee9-bd899433c938</toProcessItemId>
        </link>
    </process>
</teamworks>

