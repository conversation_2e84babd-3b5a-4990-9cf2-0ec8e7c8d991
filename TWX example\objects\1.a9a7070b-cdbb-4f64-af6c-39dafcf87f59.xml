<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" name="Update History">
        <lastModified>1692015585211</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>e22f8ce7-0dbc-4722-b4e0-d9339f215895</guid>
        <versionId>cbf38ad1-7216-4342-a2f8-852a4004eda1</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:e6d52ec61513ed03:232a663a:189f41ec14b:-5090" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.48cf402a-8a65-42b6-a9bf-92c0c4761641"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"62571825-1a47-49e6-b729-3315225cbe92"},{"incoming":["a6092044-286d-49fb-8a70-f108ba17f330"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":744,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-618d"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"265ed903-be8b-4b62-a34d-a53b67237cd5"},{"targetRef":"f2e87e5e-bbd8-4b1e-8216-43b7101492c9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Update History","declaredType":"sequenceFlow","id":"2027.48cf402a-8a65-42b6-a9bf-92c0c4761641","sourceRef":"62571825-1a47-49e6-b729-3315225cbe92"},{"startQuantity":1,"outgoing":["6d793c8f-0889-4c07-8a6b-beff3e741c59"],"incoming":["2027.48cf402a-8a65-42b6-a9bf-92c0c4761641"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":108,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Update History","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f2e87e5e-bbd8-4b1e-8216-43b7101492c9","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog.endTime = new tw.object.Date();\r\ntw.local.idcRequest.stepLog.userName = tw.system.user_fullName;\r\ntw.local.idcRequest.stepLog.step = tw.system.currentTask.subject;\r\ntw.local.idcRequest.stepLog.role = tw.local.role;\r\nif (tw.local.idcRequest.stepLog.returnReason != null &amp;&amp; tw.local.idcRequest.stepLog.returnReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.returnReason != \"\") {\r\n\t\ttw.local.idcRequest.stepLog.comment = \"Comment is:\" + tw.local.idcRequest.stepLog.comment+ \"&lt;br&gt;Return Reason is: \"+ tw.local.idcRequest.stepLog.returnReason;\r\n}\r\nif (tw.local.idcRequest.stepLog.terminateReason != null &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != \"\") {\r\n\t\ttw.local.idcRequest.stepLog.comment = \"Comment is:\" + tw.local.idcRequest.stepLog.comment+ \"&lt;br&gt;terminate Reason is: \"+ tw.local.idcRequest.stepLog.returnReason;\r\n}\r\ntw.local.appLog.insertIntoList(tw.local.appLog.listLength, tw.local.idcRequest.stepLog);\r\n\r\n\r\n"]}},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.39432c77-90f7-466d-8493-19fa76afa565"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.362014eb-1476-4be8-86f5-35499911d017"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.211422fb-a582-40d8-8573-a6c3cd2cf31b"},{"startQuantity":1,"outgoing":["bad92269-63f6-4b27-8ea4-56ef050a9f90"],"incoming":["6d793c8f-0889-4c07-8a6b-beff3e741c59"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":303,"y":61,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Insert history &amp; Update Statues Q","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7d31f163-526c-4924-830b-c13884b5b2cb","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\n\/\/-------------------------Update Statues-------------\r\ntw.local.sqlStatements[0] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();\r\n\r\nfunction addProp(value){\r\n\tvar prop = new tw.object.SQLParameter();\r\n\tprop.value = value;\r\n\ttw.local.sqlStatements[0].parameters.insertIntoList(tw.local.sqlStatements[0].parameters.listLength, prop);\r\n}\r\n\r\naddProp(tw.local.idcRequest.IDCRequestState);\r\naddProp(tw.local.idcRequest.IDCRequestStage);\r\naddProp(tw.local.idcRequest.appInfo.status);\r\naddProp(tw.local.idcRequest.appInfo.subStatus);\r\naddProp(tw.local.idcRequest.DBID);\r\n\r\ntw.local.sqlStatements[0].sql = \"UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_STATE = ?, REQUEST_STAGE = ? , REQUEST_STATUS = ? , REQUEST_SUB_STATUS = ? WHERE ID = ?;\";\r\n\r\n\/\/------------------------Update History--------------\r\ntw.local.sqlStatements[1] = {};\r\ntw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[1].maxRows = -1;\r\n\r\nfunction addProp1(value){\r\n\tvar prop = new tw.object.SQLParameter();\r\n\tprop.value = value;\r\n\ttw.local.sqlStatements[1].parameters.insertIntoList(tw.local.sqlStatements[1].parameters.listLength, prop);\r\n}\r\n\r\naddProp1(tw.local.idcRequest.DBID);\r\naddProp1(tw.local.idcRequest.stepLog.startTime);\r\naddProp1(tw.local.idcRequest.stepLog.endTime);\r\naddProp1(tw.local.idcRequest.stepLog.userName);\r\naddProp1(tw.local.idcRequest.stepLog.role);\r\naddProp1(tw.local.idcRequest.stepLog.step);\r\naddProp1(tw.local.idcRequest.stepLog.action);\r\naddProp1(tw.local.idcRequest.stepLog.comment);\r\naddProp1(tw.local.idcRequest.stepLog.terminateReason);\r\naddProp1(tw.local.idcRequest.stepLog.returnReason);\r\n\r\ntw.local.sqlStatements[1].sql = \"INSERT INTO BPM.IDC_REQUEST_HISTORY (IDC_REQUEST_ID , START_TIME, END_TIME, USER_NAME, ROLE, STEP, ACTION, COMMENT, TERMINATE_REASON, RETURN_REASON) VALUES (?,?,?,?,?,?,?,?,?,?)\";\r\n\/\/---------------------------------------------------------------------------------------------------------------------------------------------\r\n"]}},{"startQuantity":1,"outgoing":["a6092044-286d-49fb-8a70-f108ba17f330"],"incoming":["bad92269-63f6-4b27-8ea4-56ef050a9f90"],"extensionElements":{"nodeVisualInfo":[{"color":"#FFC875","width":95,"x":531,"y":64,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Insert history &amp; Update Statues","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"63a5b89c-9bf1-4b00-8848-17c0a6b25a4a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"63a5b89c-9bf1-4b00-8848-17c0a6b25a4a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert history &amp; Update Statues","declaredType":"sequenceFlow","id":"bad92269-63f6-4b27-8ea4-56ef050a9f90","sourceRef":"7d31f163-526c-4924-830b-c13884b5b2cb"},{"targetRef":"265ed903-be8b-4b62-a34d-a53b67237cd5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a6092044-286d-49fb-8a70-f108ba17f330","sourceRef":"63a5b89c-9bf1-4b00-8848-17c0a6b25a4a"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.d975e8ab-485a-4e22-80ee-5c434dd8b56a"},{"targetRef":"7d31f163-526c-4924-830b-c13884b5b2cb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert history &amp; Update Statues Q","declaredType":"sequenceFlow","id":"6d793c8f-0889-4c07-8a6b-beff3e741c59","sourceRef":"f2e87e5e-bbd8-4b1e-8216-43b7101492c9"},{"parallelMultiple":false,"outgoing":["c3e19073-56f2-498f-8802-db5ad861fb6d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"74de4970-4916-4601-8ec8-5d66ae2380d3"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"01504e1b-f857-44db-834a-4df418c939c7","otherAttributes":{"eventImplId":"9db9950c-a800-485a-89b6-745898fa8852"}}],"attachedToRef":"f2e87e5e-bbd8-4b1e-8216-43b7101492c9","extensionElements":{"nodeVisualInfo":[{"width":24,"x":143,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"a775d04c-f465-4197-8180-21453d633e71","outputSet":{}},{"parallelMultiple":false,"outgoing":["22847585-80da-42f4-840d-374c727d55ed"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"74c07d10-b2db-47cd-81a7-31eff516f6ec"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"f20a4fb8-ffee-43f8-8ef9-e626437b3744","otherAttributes":{"eventImplId":"c6b5fb36-a6d6-4e46-8ba7-08d7ed2aa680"}}],"attachedToRef":"7d31f163-526c-4924-830b-c13884b5b2cb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":338,"y":119,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"c2e5f848-2f37-4201-8c16-b66320f56ba7","outputSet":{}},{"parallelMultiple":false,"outgoing":["c4b8a165-457e-47e7-80da-ea629f0bb0d5"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"295cb682-fd8a-460d-8b1e-28bb8523b9ad"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"85b0babf-a58c-4f0d-8f00-cd9ed3831932","otherAttributes":{"eventImplId":"8ce90960-040c-42b6-863a-d25d1c809d7d"}}],"attachedToRef":"63a5b89c-9bf1-4b00-8848-17c0a6b25a4a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":566,"y":122,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"a13b1e37-0c7f-4163-8a6a-8e2ed26354c8","outputSet":{}},{"incoming":["c3e19073-56f2-498f-8802-db5ad861fb6d","22847585-80da-42f4-840d-374c727d55ed","c4b8a165-457e-47e7-80da-ea629f0bb0d5"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"92ebfe6a-1d56-4303-8c8d-4f06fe03bddf","otherAttributes":{"eventImplId":"9c0958dd-c14f-4d46-8e33-9486ee011c62"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":412,"y":181,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Update History -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Update History -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"42da6765-faf2-42ac-8ead-ff0d4c841911"},{"targetRef":"42da6765-faf2-42ac-8ead-ff0d4c841911","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"c3e19073-56f2-498f-8802-db5ad861fb6d","sourceRef":"a775d04c-f465-4197-8180-21453d633e71"},{"targetRef":"42da6765-faf2-42ac-8ead-ff0d4c841911","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"22847585-80da-42f4-840d-374c727d55ed","sourceRef":"c2e5f848-2f37-4201-8c16-b66320f56ba7"},{"targetRef":"42da6765-faf2-42ac-8ead-ff0d4c841911","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"c4b8a165-457e-47e7-80da-ea629f0bb0d5","sourceRef":"a13b1e37-0c7f-4163-8a6a-8e2ed26354c8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.4741f2b7-b438-4db8-8e42-465a0ca376cd"}],"laneSet":[{"id":"82fe2300-2d7e-40e4-851d-adbbf5277a4e","lane":[{"flowNodeRef":["62571825-1a47-49e6-b729-3315225cbe92","265ed903-be8b-4b62-a34d-a53b67237cd5","f2e87e5e-bbd8-4b1e-8216-43b7101492c9","7d31f163-526c-4924-830b-c13884b5b2cb","63a5b89c-9bf1-4b00-8848-17c0a6b25a4a","a775d04c-f465-4197-8180-21453d633e71","c2e5f848-2f37-4201-8c16-b66320f56ba7","a13b1e37-0c7f-4163-8a6a-8e2ed26354c8","42da6765-faf2-42ac-8ead-ff0d4c841911"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"90b9931c-53cd-46f2-a30c-d2078a645bbb","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Update History","declaredType":"process","id":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.60a8424b-59f2-4328-8d4f-c388b30e202f"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"appLog","isCollection":true,"id":"2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"}],"inputSet":[{"dataInputRefs":["2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","2055.322bdb97-0698-43d7-8172-71cbc933103d","2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6"]}],"outputSet":[{"dataOutputRefs":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f","2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"role","isCollection":false,"id":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\nautoObject.customerInformation.facilityType.id = 0;\nautoObject.customerInformation.facilityType.code = \"\";\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\nautoObject.customerInformation.facilityType.englishdescription = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new TWDate();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.322bdb97-0698-43d7-8172-71cbc933103d"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"appLog","isCollection":true,"id":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8f836da8-db49-4c39-a818-9504f1570ebd</guid>
            <versionId>3d34295f-7415-4165-97c6-94d4d6fbe34d</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.322bdb97-0698-43d7-8172-71cbc933103d</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>89d72add-8677-44c7-9467-91721160b5d3</guid>
            <versionId>d1070bbe-5ba7-46da-84d6-6bdffbcd65fc</versionId>
        </processParameter>
        <processParameter name="appLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e9107022-3cf2-4ba7-8f32-b0e62b2a3fd6</guid>
            <versionId>0911ab6b-05c1-49ab-a7aa-7bd628bef961</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>676e3168-386e-4b2d-8546-63f433197876</guid>
            <versionId>8353201a-c5d2-4f42-bad5-dc8b51abe0fc</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.45d7b344-dffe-4935-87ec-36b1db04511c</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6c408fca-819a-4109-858a-ec5b85d981e4</guid>
            <versionId>f687bd24-9a63-43a3-9201-107e86fed116</versionId>
        </processParameter>
        <processParameter name="appLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</processParameterId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1a7f4697-ace1-4023-9f20-559c713446d1</guid>
            <versionId>b9c85201-f50c-4094-a40a-10b9da3241ab</versionId>
        </processParameter>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.39432c77-90f7-466d-8493-19fa76afa565</processVariableId>
            <description isNull="true" />
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d66fe9ac-2065-47f2-8d65-d45ccaab242c</guid>
            <versionId>2b0534c2-77f7-4111-ba35-b851d2e259f9</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.362014eb-1476-4be8-86f5-35499911d017</processVariableId>
            <description isNull="true" />
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>06231634-7540-4f55-b1ae-63a8c9402363</guid>
            <versionId>e2e7f8d9-b992-4bb2-b9da-d5a453d55bbb</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.211422fb-a582-40d8-8573-a6c3cd2cf31b</processVariableId>
            <description isNull="true" />
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c19b9df3-8514-451f-aadd-9a3e1c3f0fac</guid>
            <versionId>c4273835-a415-4eb8-b93f-ccf6e5244a27</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d975e8ab-485a-4e22-80ee-5c434dd8b56a</processVariableId>
            <description isNull="true" />
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>abd88ffa-a309-4533-b279-db853aeabe64</guid>
            <versionId>36520274-129d-4b9c-ba46-251e3228bf46</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4741f2b7-b438-4db8-8e42-465a0ca376cd</processVariableId>
            <description isNull="true" />
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ddd8ea5d-cff4-4028-8e87-0dcfc6808fa7</guid>
            <versionId>a7218b42-9c4b-4695-98b5-1826329ed646</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</processItemId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <name>Insert history &amp; Update Statues</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.1cad1496-6db3-44bb-9c51-0ef0fe1a88aa</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:2abc</guid>
            <versionId>35448e52-53e9-4bc7-ac34-3df2ac4686eb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FFC875</nodeColor>
            <layoutData x="531" y="64">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-692f</errorHandlerItem>
                <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.1cad1496-6db3-44bb-9c51-0ef0fe1a88aa</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>aec9dfc4-2c70-4dfd-a548-fed7a108bf3a</guid>
                <versionId>19dda912-5345-4955-b6db-5a94a48d0ad2</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d38d2b04-0965-4aac-adf5-4245d02acd77</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.1cad1496-6db3-44bb-9c51-0ef0fe1a88aa</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>83b39737-bbd1-4dde-ad3a-971cd401a07a</guid>
                    <versionId>3c7a0778-9202-46da-b673-9af270bf0410</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7a30a923-f325-411a-9bd0-305a8b9bf6c5</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.1cad1496-6db3-44bb-9c51-0ef0fe1a88aa</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a5b9f381-4dff-47fe-a5d5-4670de44f60e</guid>
                    <versionId>84603886-f6c7-4c60-912c-ccb73bd2ee93</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1c951997-722b-4d51-9688-18f88792626e</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.1cad1496-6db3-44bb-9c51-0ef0fe1a88aa</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>16785ea3-bbda-45c1-8010-1c501eefb4f2</guid>
                    <versionId>db768e44-b39f-45eb-90e7-31e83b98abff</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7d31f163-526c-4924-830b-c13884b5b2cb</processItemId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <name>Insert history &amp; Update Statues Q</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.452b2e07-8601-4986-b5d3-684316f440e0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:2a82</guid>
            <versionId>5b8b1c7c-9f43-4d98-b5e7-ce86a200d1eb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="303" y="61">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-692f</errorHandlerItem>
                <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.452b2e07-8601-4986-b5d3-684316f440e0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
//-------------------------Update Statues-------------&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
function addProp(value){&#xD;
	var prop = new tw.object.SQLParameter();&#xD;
	prop.value = value;&#xD;
	tw.local.sqlStatements[0].parameters.insertIntoList(tw.local.sqlStatements[0].parameters.listLength, prop);&#xD;
}&#xD;
&#xD;
addProp(tw.local.idcRequest.IDCRequestState);&#xD;
addProp(tw.local.idcRequest.IDCRequestStage);&#xD;
addProp(tw.local.idcRequest.appInfo.status);&#xD;
addProp(tw.local.idcRequest.appInfo.subStatus);&#xD;
addProp(tw.local.idcRequest.DBID);&#xD;
&#xD;
tw.local.sqlStatements[0].sql = "UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_STATE = ?, REQUEST_STAGE = ? , REQUEST_STATUS = ? , REQUEST_SUB_STATUS = ? WHERE ID = ?;";&#xD;
&#xD;
//------------------------Update History--------------&#xD;
tw.local.sqlStatements[1] = {};&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[1].maxRows = -1;&#xD;
&#xD;
function addProp1(value){&#xD;
	var prop = new tw.object.SQLParameter();&#xD;
	prop.value = value;&#xD;
	tw.local.sqlStatements[1].parameters.insertIntoList(tw.local.sqlStatements[1].parameters.listLength, prop);&#xD;
}&#xD;
&#xD;
addProp1(tw.local.idcRequest.DBID);&#xD;
addProp1(tw.local.idcRequest.stepLog.startTime);&#xD;
addProp1(tw.local.idcRequest.stepLog.endTime);&#xD;
addProp1(tw.local.idcRequest.stepLog.userName);&#xD;
addProp1(tw.local.idcRequest.stepLog.role);&#xD;
addProp1(tw.local.idcRequest.stepLog.step);&#xD;
addProp1(tw.local.idcRequest.stepLog.action);&#xD;
addProp1(tw.local.idcRequest.stepLog.comment);&#xD;
addProp1(tw.local.idcRequest.stepLog.terminateReason);&#xD;
addProp1(tw.local.idcRequest.stepLog.returnReason);&#xD;
&#xD;
tw.local.sqlStatements[1].sql = "INSERT INTO BPM.IDC_REQUEST_HISTORY (IDC_REQUEST_ID , START_TIME, END_TIME, USER_NAME, ROLE, STEP, ACTION, COMMENT, TERMINATE_REASON, RETURN_REASON) VALUES (?,?,?,?,?,?,?,?,?,?)";&#xD;
//---------------------------------------------------------------------------------------------------------------------------------------------&#xD;
</script>
                <isRule>false</isRule>
                <guid>a4c84197-003b-4f44-a38e-d6ef5dcb5735</guid>
                <versionId>8c3c5d08-391a-4b5c-9141-ce12e9826f9a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</processItemId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.f92e5758-49ff-41a3-b238-d002473ab9cd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-692f</guid>
            <versionId>a4c4910c-55bf-411b-9041-9c73697ad017</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.139a49fb-ed90-4438-a8d0-015254e8a891</processItemPrePostId>
                <processItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Update History -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Update History -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>065d2a43-7bbe-46c0-86c2-b1b3c541807f</guid>
                <versionId>2816032d-b063-4a74-a062-79e46fa729da</versionId>
            </processPrePosts>
            <layoutData x="412" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.f92e5758-49ff-41a3-b238-d002473ab9cd</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>576522cf-10b0-4cc1-8b63-9f41130f01cc</guid>
                <versionId>d2ec6058-4442-446d-8541-35a56d017db6</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.60aeb09e-3b37-47a1-addf-4a51f10ab2da</parameterMappingId>
                    <processParameterId>2055.45d7b344-dffe-4935-87ec-36b1db04511c</processParameterId>
                    <parameterMappingParentId>3007.f92e5758-49ff-41a3-b238-d002473ab9cd</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ddeb68ee-187b-46fe-a622-9e768826a467</guid>
                    <versionId>19539ac0-21d9-4d5d-b914-711e66c9c574</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</processItemId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <name>Update History</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7ba14e4d-af49-47f3-ae18-b8dbc8462549</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-618c</guid>
            <versionId>abdcf89d-35eb-43fc-86b6-f369c6229e62</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e0f957f4-06d2-417b-9eac-58577bc50cae</processItemPrePostId>
                <processItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>ff45fe74-fd63-4ec0-bffa-b705a4f13844</guid>
                <versionId>7f05986e-cc39-4967-85e0-8412098263d3</versionId>
            </processPrePosts>
            <layoutData x="108" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-692f</errorHandlerItem>
                <errorHandlerItemId>2025.42da6765-faf2-42ac-8ead-ff0d4c841911</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7ba14e4d-af49-47f3-ae18-b8dbc8462549</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.idcRequest.stepLog.endTime = new tw.object.Date();&#xD;
tw.local.idcRequest.stepLog.userName = tw.system.user_fullName;&#xD;
tw.local.idcRequest.stepLog.step = tw.system.currentTask.subject;&#xD;
tw.local.idcRequest.stepLog.role = tw.local.role;&#xD;
if (tw.local.idcRequest.stepLog.returnReason != null &amp;&amp; tw.local.idcRequest.stepLog.returnReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.returnReason != "") {&#xD;
		tw.local.idcRequest.stepLog.comment = "Comment is:" + tw.local.idcRequest.stepLog.comment+ "&lt;br&gt;Return Reason is: "+ tw.local.idcRequest.stepLog.returnReason;&#xD;
}&#xD;
if (tw.local.idcRequest.stepLog.terminateReason != null &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != "") {&#xD;
		tw.local.idcRequest.stepLog.comment = "Comment is:" + tw.local.idcRequest.stepLog.comment+ "&lt;br&gt;terminate Reason is: "+ tw.local.idcRequest.stepLog.returnReason;&#xD;
}&#xD;
tw.local.appLog.insertIntoList(tw.local.appLog.listLength, tw.local.idcRequest.stepLog);&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>2f091ec3-ca32-49dc-864b-428ed03d76d1</guid>
                <versionId>0a38ed42-5ffd-42e7-8a81-6697b373c2f5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.265ed903-be8b-4b62-a34d-a53b67237cd5</processItemId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.83bb9c8b-6030-44f9-a622-3b4a89b91f13</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-618d</guid>
            <versionId>d34ae43e-4458-4b98-8723-bf9be6b65d82</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="744" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.83bb9c8b-6030-44f9-a622-3b4a89b91f13</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>f25a9784-0f75-48e7-9f4e-00e6dc41e578</guid>
                <versionId>91128fae-7b7b-4a66-80ec-263731ac6b88</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Update History" id="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="role" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.322bdb97-0698-43d7-8172-71cbc933103d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new TWDate();
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="appLog" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.60a8424b-59f2-4328-8d4f-c388b30e202f" />
                        
                        
                        <ns16:dataOutput name="appLog" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="82fe2300-2d7e-40e4-851d-adbbf5277a4e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="90b9931c-53cd-46f2-a30c-d2078a645bbb" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>62571825-1a47-49e6-b729-3315225cbe92</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>265ed903-be8b-4b62-a34d-a53b67237cd5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f2e87e5e-bbd8-4b1e-8216-43b7101492c9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7d31f163-526c-4924-830b-c13884b5b2cb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a775d04c-f465-4197-8180-21453d633e71</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c2e5f848-2f37-4201-8c16-b66320f56ba7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a13b1e37-0c7f-4163-8a6a-8e2ed26354c8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>42da6765-faf2-42ac-8ead-ff0d4c841911</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="62571825-1a47-49e6-b729-3315225cbe92">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.48cf402a-8a65-42b6-a9bf-92c0c4761641</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="265ed903-be8b-4b62-a34d-a53b67237cd5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="744" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-618d</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a6092044-286d-49fb-8a70-f108ba17f330</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="62571825-1a47-49e6-b729-3315225cbe92" targetRef="f2e87e5e-bbd8-4b1e-8216-43b7101492c9" name="To Update History" id="2027.48cf402a-8a65-42b6-a9bf-92c0c4761641">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Update History" id="f2e87e5e-bbd8-4b1e-8216-43b7101492c9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="108" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.48cf402a-8a65-42b6-a9bf-92c0c4761641</ns16:incoming>
                        
                        
                        <ns16:outgoing>6d793c8f-0889-4c07-8a6b-beff3e741c59</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.idcRequest.stepLog.endTime = new tw.object.Date();&#xD;
tw.local.idcRequest.stepLog.userName = tw.system.user_fullName;&#xD;
tw.local.idcRequest.stepLog.step = tw.system.currentTask.subject;&#xD;
tw.local.idcRequest.stepLog.role = tw.local.role;&#xD;
if (tw.local.idcRequest.stepLog.returnReason != null &amp;&amp; tw.local.idcRequest.stepLog.returnReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.returnReason != "") {&#xD;
		tw.local.idcRequest.stepLog.comment = "Comment is:" + tw.local.idcRequest.stepLog.comment+ "&lt;br&gt;Return Reason is: "+ tw.local.idcRequest.stepLog.returnReason;&#xD;
}&#xD;
if (tw.local.idcRequest.stepLog.terminateReason != null &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != undefined &amp;&amp; tw.local.idcRequest.stepLog.terminateReason != "") {&#xD;
		tw.local.idcRequest.stepLog.comment = "Comment is:" + tw.local.idcRequest.stepLog.comment+ "&lt;br&gt;terminate Reason is: "+ tw.local.idcRequest.stepLog.returnReason;&#xD;
}&#xD;
tw.local.appLog.insertIntoList(tw.local.appLog.listLength, tw.local.idcRequest.stepLog);&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.39432c77-90f7-466d-8493-19fa76afa565" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.362014eb-1476-4be8-86f5-35499911d017" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.211422fb-a582-40d8-8573-a6c3cd2cf31b" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert history &amp; Update Statues Q" id="7d31f163-526c-4924-830b-c13884b5b2cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="303" y="61" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6d793c8f-0889-4c07-8a6b-beff3e741c59</ns16:incoming>
                        
                        
                        <ns16:outgoing>bad92269-63f6-4b27-8ea4-56ef050a9f90</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
//-------------------------Update Statues-------------&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
function addProp(value){&#xD;
	var prop = new tw.object.SQLParameter();&#xD;
	prop.value = value;&#xD;
	tw.local.sqlStatements[0].parameters.insertIntoList(tw.local.sqlStatements[0].parameters.listLength, prop);&#xD;
}&#xD;
&#xD;
addProp(tw.local.idcRequest.IDCRequestState);&#xD;
addProp(tw.local.idcRequest.IDCRequestStage);&#xD;
addProp(tw.local.idcRequest.appInfo.status);&#xD;
addProp(tw.local.idcRequest.appInfo.subStatus);&#xD;
addProp(tw.local.idcRequest.DBID);&#xD;
&#xD;
tw.local.sqlStatements[0].sql = "UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_STATE = ?, REQUEST_STAGE = ? , REQUEST_STATUS = ? , REQUEST_SUB_STATUS = ? WHERE ID = ?;";&#xD;
&#xD;
//------------------------Update History--------------&#xD;
tw.local.sqlStatements[1] = {};&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[1].maxRows = -1;&#xD;
&#xD;
function addProp1(value){&#xD;
	var prop = new tw.object.SQLParameter();&#xD;
	prop.value = value;&#xD;
	tw.local.sqlStatements[1].parameters.insertIntoList(tw.local.sqlStatements[1].parameters.listLength, prop);&#xD;
}&#xD;
&#xD;
addProp1(tw.local.idcRequest.DBID);&#xD;
addProp1(tw.local.idcRequest.stepLog.startTime);&#xD;
addProp1(tw.local.idcRequest.stepLog.endTime);&#xD;
addProp1(tw.local.idcRequest.stepLog.userName);&#xD;
addProp1(tw.local.idcRequest.stepLog.role);&#xD;
addProp1(tw.local.idcRequest.stepLog.step);&#xD;
addProp1(tw.local.idcRequest.stepLog.action);&#xD;
addProp1(tw.local.idcRequest.stepLog.comment);&#xD;
addProp1(tw.local.idcRequest.stepLog.terminateReason);&#xD;
addProp1(tw.local.idcRequest.stepLog.returnReason);&#xD;
&#xD;
tw.local.sqlStatements[1].sql = "INSERT INTO BPM.IDC_REQUEST_HISTORY (IDC_REQUEST_ID , START_TIME, END_TIME, USER_NAME, ROLE, STEP, ACTION, COMMENT, TERMINATE_REASON, RETURN_REASON) VALUES (?,?,?,?,?,?,?,?,?,?)";&#xD;
//---------------------------------------------------------------------------------------------------------------------------------------------&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="Insert history &amp; Update Statues" id="63a5b89c-9bf1-4b00-8848-17c0a6b25a4a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="531" y="64" width="95" height="70" color="#FFC875" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bad92269-63f6-4b27-8ea4-56ef050a9f90</ns16:incoming>
                        
                        
                        <ns16:outgoing>a6092044-286d-49fb-8a70-f108ba17f330</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="7d31f163-526c-4924-830b-c13884b5b2cb" targetRef="63a5b89c-9bf1-4b00-8848-17c0a6b25a4a" name="To Insert history &amp; Update Statues" id="bad92269-63f6-4b27-8ea4-56ef050a9f90">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="63a5b89c-9bf1-4b00-8848-17c0a6b25a4a" targetRef="265ed903-be8b-4b62-a34d-a53b67237cd5" name="To End" id="a6092044-286d-49fb-8a70-f108ba17f330">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.d975e8ab-485a-4e22-80ee-5c434dd8b56a" />
                    
                    
                    <ns16:sequenceFlow sourceRef="f2e87e5e-bbd8-4b1e-8216-43b7101492c9" targetRef="7d31f163-526c-4924-830b-c13884b5b2cb" name="To Insert history &amp; Update Statues Q" id="6d793c8f-0889-4c07-8a6b-beff3e741c59">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f2e87e5e-bbd8-4b1e-8216-43b7101492c9" parallelMultiple="false" name="Error" id="a775d04c-f465-4197-8180-21453d633e71">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="143" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c3e19073-56f2-498f-8802-db5ad861fb6d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="74de4970-4916-4601-8ec8-5d66ae2380d3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="01504e1b-f857-44db-834a-4df418c939c7" eventImplId="9db9950c-a800-485a-89b6-745898fa8852">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7d31f163-526c-4924-830b-c13884b5b2cb" parallelMultiple="false" name="Error1" id="c2e5f848-2f37-4201-8c16-b66320f56ba7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="338" y="119" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>22847585-80da-42f4-840d-374c727d55ed</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="74c07d10-b2db-47cd-81a7-31eff516f6ec" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="f20a4fb8-ffee-43f8-8ef9-e626437b3744" eventImplId="c6b5fb36-a6d6-4e46-8ba7-08d7ed2aa680">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="63a5b89c-9bf1-4b00-8848-17c0a6b25a4a" parallelMultiple="false" name="Error2" id="a13b1e37-0c7f-4163-8a6a-8e2ed26354c8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="566" y="122" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c4b8a165-457e-47e7-80da-ea629f0bb0d5</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="295cb682-fd8a-460d-8b1e-28bb8523b9ad" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="85b0babf-a58c-4f0d-8f00-cd9ed3831932" eventImplId="8ce90960-040c-42b6-863a-d25d1c809d7d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="42da6765-faf2-42ac-8ead-ff0d4c841911">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="412" y="181" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Update History -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Update History -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c3e19073-56f2-498f-8802-db5ad861fb6d</ns16:incoming>
                        
                        
                        <ns16:incoming>22847585-80da-42f4-840d-374c727d55ed</ns16:incoming>
                        
                        
                        <ns16:incoming>c4b8a165-457e-47e7-80da-ea629f0bb0d5</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="92ebfe6a-1d56-4303-8c8d-4f06fe03bddf" eventImplId="9c0958dd-c14f-4d46-8e33-9486ee011c62">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a775d04c-f465-4197-8180-21453d633e71" targetRef="42da6765-faf2-42ac-8ead-ff0d4c841911" name="To End Event" id="c3e19073-56f2-498f-8802-db5ad861fb6d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c2e5f848-2f37-4201-8c16-b66320f56ba7" targetRef="42da6765-faf2-42ac-8ead-ff0d4c841911" name="To End Event" id="22847585-80da-42f4-840d-374c727d55ed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a13b1e37-0c7f-4163-8a6a-8e2ed26354c8" targetRef="42da6765-faf2-42ac-8ead-ff0d4c841911" name="To End Event" id="c4b8a165-457e-47e7-80da-ea629f0bb0d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.4741f2b7-b438-4db8-8e42-465a0ca376cd" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Insert history &amp; Update Statues">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bad92269-63f6-4b27-8ea4-56ef050a9f90</processLinkId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7d31f163-526c-4924-830b-c13884b5b2cb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</toProcessItemId>
            <guid>1e4fcf99-62c4-4366-8596-ce11b230a96d</guid>
            <versionId>655a64ae-7360-47f6-ad3c-3ab258c8dde7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7d31f163-526c-4924-830b-c13884b5b2cb</fromProcessItemId>
            <toProcessItemId>2025.63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a6092044-286d-49fb-8a70-f108ba17f330</processLinkId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.265ed903-be8b-4b62-a34d-a53b67237cd5</toProcessItemId>
            <guid>2ac50c7f-a4b6-4e21-aa43-b5cacbcf89fb</guid>
            <versionId>686c8442-27c6-42cf-b91f-26f264938922</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.63a5b89c-9bf1-4b00-8848-17c0a6b25a4a</fromProcessItemId>
            <toProcessItemId>2025.265ed903-be8b-4b62-a34d-a53b67237cd5</toProcessItemId>
        </link>
        <link name="To Insert history &amp; Update Statues Q">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6d793c8f-0889-4c07-8a6b-beff3e741c59</processLinkId>
            <processId>1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7d31f163-526c-4924-830b-c13884b5b2cb</toProcessItemId>
            <guid>a3961a7f-b5bb-458b-ad3e-4e4009ee1802</guid>
            <versionId>8544ce62-01cc-4d1d-89e5-d12fdbfdf16c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f2e87e5e-bbd8-4b1e-8216-43b7101492c9</fromProcessItemId>
            <toProcessItemId>2025.7d31f163-526c-4924-830b-c13884b5b2cb</toProcessItemId>
        </link>
    </process>
</teamworks>

