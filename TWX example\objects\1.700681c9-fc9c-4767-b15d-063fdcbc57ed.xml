<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.700681c9-fc9c-4767-b15d-063fdcbc57ed" name="Withdrawal Approval Mail Service">
        <lastModified>1688661262075</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.1b950126-33f6-4560-ac75-1a890bd2ca5f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>c374d6fb-5aa8-4390-a8e3-5b2ea7d995df</guid>
        <versionId>c93560c8-10b6-4d12-a981-279cc878a423</versionId>
        <dependencySummary>&lt;dependencySummary id="8c9c9548-60ce-4f28-a25b-7d251fb98073" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="mailDebugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.598b5eb5-a531-4d19-a0de-3c2a347fc8c4</processParameterId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Y"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ccb41f35-ecf8-47ca-92a7-ffff78936aeb</guid>
            <versionId>d2f23a16-f078-4b9d-b4bc-547983b3c4c2</versionId>
        </processParameter>
        <processParameter name="taskid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bd5c0c09-775a-49e9-a0d7-0df9afe33b4b</processParameterId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"2078.70913"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>be8a2640-4745-42d8-a032-d5ccc41b08bb</guid>
            <versionId>e5856e5b-b914-431b-9fa2-43db276a9b92</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.02414283-1d95-4440-b1b6-482b9ee02bde</processParameterId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2c0f5a5e-77f7-4f80-8366-b9d53c70385a</guid>
            <versionId>cc65b0eb-0d64-48e5-b627-b767cb66cb1e</versionId>
        </processParameter>
        <processVariable name="mailTo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9987fe9b-f8de-4406-b42e-c33f2e319d46</processVariableId>
            <description isNull="true" />
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>87275021-ec15-4f5c-b136-ecbd2503cd9e</guid>
            <versionId>f2839efc-d040-4a1e-a431-ccb69113ecb1</versionId>
        </processVariable>
        <processVariable name="msgBody">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.57d42ce0-f67a-482c-a0ad-9778052e1801</processVariableId>
            <description isNull="true" />
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>6de9e296-4b8c-476b-91e9-ce6c794f0640</guid>
            <versionId>f51c6a31-4c14-41fe-b802-9ac8766c602e</versionId>
        </processVariable>
        <processVariable name="subject">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6d6be0c2-064c-4c82-bb2a-1c89495df6a2</processVariableId>
            <description isNull="true" />
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>ddbaf6bd-f207-434e-b093-dc9a85cdde95</guid>
            <versionId>c6c59363-8b0b-4265-a276-a02f669728f3</versionId>
        </processVariable>
        <processVariable name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3b482237-0f43-4e17-9512-f8e3953b9934</processVariableId>
            <description isNull="true" />
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0caa176-0a02-4259-8c38-63c6c0f8ae08</guid>
            <versionId>8ad334ab-4ae5-4fe3-9cd8-9a457d0822b9</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fbe30840-7616-4423-9c67-45c3c1be6605</processItemId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <name>Send Email</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6231</guid>
            <versionId>3fd29650-0bed-4f20-b00c-aaba6f78fd11</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.338e9f4d-8538-4ceb-a155-c288604435d4</attachedProcessRef>
                <guid>75c690ad-db75-4978-97d8-58adb6b78cde</guid>
                <versionId>36cf8efe-20ab-4553-946a-0a1c0f072b80</versionId>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e8b4fc46-3201-4465-b5f2-427d59967048</parameterMappingId>
                    <processParameterId>2055.20348cf5-023e-4d3a-826e-5b92143ec224</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8fec83c2-9927-4ff7-adcb-b0984afc2d22</guid>
                    <versionId>06abec8a-6a82-471e-b4f6-9980f3445e42</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailDebugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.78bb87e9-3307-47ad-8696-5e758b8391f5</parameterMappingId>
                    <processParameterId>2055.1da05789-2131-46bc-aacf-34d84ca37def</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailDebugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de02ae57-8c9a-4046-8981-d30c5d20a23a</guid>
                    <versionId>3d8053ab-493b-43ad-868c-547cb4ea0e99</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="msgBody">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c88e540a-8bc7-4145-976f-c197cbfb2bbe</parameterMappingId>
                    <processParameterId>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.msgBody</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>938ad8a5-4e19-46c3-b4fd-30a9e27972dc</guid>
                    <versionId>807b050e-c649-41ce-b225-abc2dec704ca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailTo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2eead1a4-24eb-4b25-921a-c5b543952b41</parameterMappingId>
                    <processParameterId>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailTo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>beb58268-abd0-48ed-aaba-3e1919c60235</guid>
                    <versionId>90126929-a763-4705-bcd3-fdb822f530fe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="subject">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dbd114c6-91c9-412d-86cc-61ed117fd7c0</parameterMappingId>
                    <processParameterId>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subject</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>24dbd349-8dc2-4a3f-8df7-a8f475aab893</guid>
                    <versionId>a1066c4a-ef07-41de-a995-78a7d4e9dd01</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b4c89e9a-d931-493d-a0d1-b8b9904a2a05</parameterMappingId>
                    <processParameterId>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a8ce4e26-6dd1-4c95-a57f-769c0f3e2a0d</guid>
                    <versionId>bc77e1d9-762c-42b6-b18b-551b6b325517</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="attachments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e486d64e-4acb-460d-bf89-cc802d47be25</parameterMappingId>
                    <processParameterId>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</processParameterId>
                    <parameterMappingParentId>3012.f88b34f1-45ed-48e4-bdd5-5fbc20684300</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.8a11240b-682f-4caf-9f03-5ce6a64d720b</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>f51a4ad6-0c61-4a0f-8e67-ac9019bd88c7</guid>
                    <versionId>e594244a-8a61-4710-8d27-ae2134a79e09</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1b950126-33f6-4560-ac75-1a890bd2ca5f</processItemId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bae2e51c-2479-4694-925f-bd48296c9e08</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6232</guid>
            <versionId>8764e1e6-6eb7-4353-8109-521dc0276d91</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="218" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bae2e51c-2479-4694-925f-bd48296c9e08</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	&#xD;
	tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
	tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
&#xD;
	tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.customerName = "";&#xD;
	&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
}&#xD;
&#xD;
var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
&#xD;
var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
&#xD;
&#xD;
var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
&#xD;
if (owner == null){owner = ""}&#xD;
&#xD;
tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" is approved for withdrawal "&#xD;
			+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
			" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" تمت الموافقة على رده/اعادة توجيهه";&#xD;
&#xD;
tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
			+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Kindly be informed that the Request "&#xD;
			+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved for withdrawal&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
			+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
			+"&lt;p&gt;برجاء العلم أن طلب "&#xD;
			+"رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&lt;p&gt;تمت الموافقة على رده/اعادة توجيهه&lt;/p&gt;"&#xD;
			+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
		+"&lt;/html&gt;"</script>
                <isRule>false</isRule>
                <guid>d63e3a76-ef23-44fb-b18a-2081e2a2f07c</guid>
                <versionId>ab77b5ee-f1ee-471b-a601-bebb73131c94</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.710e7d1d-3807-439d-9680-973495c924f5</processItemId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.1ff107c8-7a83-4f7c-b705-a506128df2c4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6230</guid>
            <versionId>8bf77709-3ce8-4669-b236-927b9e95c63d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="670" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.1ff107c8-7a83-4f7c-b705-a506128df2c4</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>999c93e1-c41b-4a05-924c-f34d9d113611</guid>
                <versionId>4f35862b-0992-49a2-93a8-8d553f3aa8ed</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.1b950126-33f6-4560-ac75-1a890bd2ca5f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Withdrawal Approval Mail Service" id="1.700681c9-fc9c-4767-b15d-063fdcbc57ed" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="mailDebugMode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.598b5eb5-a531-4d19-a0de-3c2a347fc8c4">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Y"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="taskid" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.bd5c0c09-775a-49e9-a0d7-0df9afe33b4b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"2078.70913"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.02414283-1d95-4440-b1b6-482b9ee02bde">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "12345678909876";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "test";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.598b5eb5-a531-4d19-a0de-3c2a347fc8c4</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.bd5c0c09-775a-49e9-a0d7-0df9afe33b4b</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.02414283-1d95-4440-b1b6-482b9ee02bde</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5515b9a4-2afe-49fd-9b77-b7e16982713c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="f3d8474d-fa39-4fbc-9a21-d907b0eac53a" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>6aabde3d-9fd1-4f67-aae2-c868ec62c962</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>710e7d1d-3807-439d-9680-973495c924f5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fbe30840-7616-4423-9c67-45c3c1be6605</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1b950126-33f6-4560-ac75-1a890bd2ca5f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="6aabde3d-9fd1-4f67-aae2-c868ec62c962">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.738ba04f-862f-4225-b536-83dd617b2722</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="710e7d1d-3807-439d-9680-973495c924f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="670" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6230</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>78351691-d508-4543-b783-8fb15c25a92a</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="6aabde3d-9fd1-4f67-aae2-c868ec62c962" targetRef="1b950126-33f6-4560-ac75-1a890bd2ca5f" name="To Exclusive Gateway" id="2027.738ba04f-862f-4225-b536-83dd617b2722">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.338e9f4d-8538-4ceb-a155-c288604435d4" name="Send Email" id="fbe30840-7616-4423-9c67-45c3c1be6605">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>94876413-023f-4a25-a5b5-2b22da191768</ns16:incoming>
                        
                        
                        <ns16:outgoing>78351691-d508-4543-b783-8fb15c25a92a</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailTo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.subject</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.msgBody</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.20348cf5-023e-4d3a-826e-5b92143ec224</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1da05789-2131-46bc-aacf-34d84ca37def</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailDebugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="fbe30840-7616-4423-9c67-45c3c1be6605" targetRef="710e7d1d-3807-439d-9680-973495c924f5" name="To End" id="78351691-d508-4543-b783-8fb15c25a92a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="2056.9987fe9b-f8de-4406-b42e-c33f2e319d46">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="msgBody" id="2056.57d42ce0-f67a-482c-a0ad-9778052e1801">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="subject" id="2056.6d6be0c2-064c-4c82-bb2a-1c89495df6a2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMsg" id="2056.3b482237-0f43-4e17-9512-f8e3953b9934" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="1b950126-33f6-4560-ac75-1a890bd2ca5f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="218" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.738ba04f-862f-4225-b536-83dd617b2722</ns16:incoming>
                        
                        
                        <ns16:outgoing>94876413-023f-4a25-a5b5-2b22da191768</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	&#xD;
	tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
	tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
&#xD;
	tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.customerName = "";&#xD;
	&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
}&#xD;
&#xD;
var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
&#xD;
var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
&#xD;
&#xD;
var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
&#xD;
if (owner == null){owner = ""}&#xD;
&#xD;
tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" is approved for withdrawal "&#xD;
			+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
			" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" تمت الموافقة على رده/اعادة توجيهه";&#xD;
&#xD;
tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
			+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Kindly be informed that the Request "&#xD;
			+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved for withdrawal&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
			+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
			+"&lt;p&gt;برجاء العلم أن طلب "&#xD;
			+"رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&lt;p&gt;تمت الموافقة على رده/اعادة توجيهه&lt;/p&gt;"&#xD;
			+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
		+"&lt;/html&gt;"</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1b950126-33f6-4560-ac75-1a890bd2ca5f" targetRef="fbe30840-7616-4423-9c67-45c3c1be6605" name="To Send Email" id="94876413-023f-4a25-a5b5-2b22da191768">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a1844661-2102-4cc8-9fb2-b5f65c5fe118</processLinkId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fbe30840-7616-4423-9c67-45c3c1be6605</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</endStateId>
            <toProcessItemId>2025.710e7d1d-3807-439d-9680-973495c924f5</toProcessItemId>
            <guid>09d0d0cd-08e8-42f8-9e94-8aba26089c3f</guid>
            <versionId>206f3334-53e5-4454-8dc0-cdcdb15e70d8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fbe30840-7616-4423-9c67-45c3c1be6605</fromProcessItemId>
            <toProcessItemId>2025.710e7d1d-3807-439d-9680-973495c924f5</toProcessItemId>
        </link>
        <link name="To Send Email">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a57e13bd-06b0-46a2-8cdb-8dc3b7e4cf59</processLinkId>
            <processId>1.700681c9-fc9c-4767-b15d-063fdcbc57ed</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1b950126-33f6-4560-ac75-1a890bd2ca5f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.fbe30840-7616-4423-9c67-45c3c1be6605</toProcessItemId>
            <guid>a1b70576-bd2a-4974-bda7-749806f1a3d5</guid>
            <versionId>b843b302-da7d-4305-af7d-2244c063c491</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1b950126-33f6-4560-ac75-1a890bd2ca5f</fromProcessItemId>
            <toProcessItemId>2025.fbe30840-7616-4423-9c67-45c3c1be6605</toProcessItemId>
        </link>
    </process>
</teamworks>

