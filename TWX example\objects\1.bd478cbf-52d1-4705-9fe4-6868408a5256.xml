<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.bd478cbf-52d1-4705-9fe4-6868408a5256" name="IDC Initialization Process Service">
        <lastModified>1692791680981</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.df7a70c4-b3e3-4f55-baab-0679b405a0d5</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>5e2bcc65-098c-43de-81d3-677d39867063</guid>
        <versionId>b85f4944-ce3a-4078-a025-170cba6c5726</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3323" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7"},{"incoming":["c455aaad-8f38-4379-9f6e-eac771f2671c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6191"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c8837dc6-c566-4f74-b2ba-43ebe1eed3d1"},{"targetRef":"df7a70c4-b3e3-4f55-baab-0679b405a0d5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init IDC Request","declaredType":"sequenceFlow","id":"2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76","sourceRef":"d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7"},{"startQuantity":1,"outgoing":["c455aaad-8f38-4379-9f6e-eac771f2671c"],"incoming":["b5bcf8ad-c63d-41b1-8ede-e47a44561399"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":405,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init IDC Request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ef5ff563-2eca-4c0c-96e1-f7aa8822f68a","scriptFormat":"text\/x-javascript","script":{"content":["\r\nif (tw.local.idcRequest == null) {\r\n\ttw.local.idcRequest = new tw.object.IDCRequest();\r\n\ttw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();\r\n\ttw.local.idcRequest.appInfo = new tw.object.AppInfo();\r\n\ttw.local.idcRequest.appLog = new tw.object.listOf.AppLog();\r\n\ttw.local.idcRequest.countryOfOrigin =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();\r\n\ttw.local.idcRequest.customerInformation.facilityType = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.documentsSource = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.financialDetails.beneficiaryDetails =new tw.object.BeneficiaryDetails();\r\n\ttw.local.idcRequest.financialDetails.beneficiaryDetails.country =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.financialDetails.documentCurrency =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.financialDetails.executionHub =new tw.object.DBLookup();\r\n\r\n\ttw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.IDCRequestType =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.importPurpose =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.paymentTerms =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.productCategory =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.productsDetails =new tw.object.ProductsDetails();\r\n\ttw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.productsDetails.CBECommodityClassification.id = null;\r\n\ttw.local.idcRequest.productsDetails.HSProduct =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.productsDetails.shipmentMethod =new tw.object.DBLookup();\r\n\ttw.local.idcRequest.appInfo.subStatus = \"\";\r\n\ttw.local.idcRequest.appInfo.status = \"\";\r\n\ttw.local.idcRequest.appInfo.initiator = tw.system.user.name;\r\n\ttw.local.idcRequest.appInfo.requestDate = new tw.object.Date().format(\"dd\/MM\/yyyy\"); \r\n\ttw.local.idcRequest.appInfo.branch = tw.local.ldapUserProfile.branch;\r\n\ttw.local.idcRequest.appInfo.requestName = \"\";\r\n\ttw.local.idcRequest.appInfo.requestType = \"\";\r\n\ttw.local.idcRequest.appInfo.appRef = \"\";\r\n\ttw.local.idcRequest.appInfo.instanceID = \"\";\r\n\t\r\n\/\/\ttw.local.idcRequest.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\n\r\n\t\r\n\ttw.local.idcRequest.approvals = new tw.object.Approvals();\r\n\ttw.local.idcRequest.approvals.CAD = false;\r\n\ttw.local.idcRequest.approvals.compliance = false;\r\n\ttw.local.idcRequest.approvals.treasury = false;\r\n\t\r\n\r\n\ttw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\n\ttw.local.idcRequest.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\n\ttw.local.idcRequest.invoices = new tw.object.listOf.Invoice();\r\n\ttw.local.idcRequest.invoices[0] = new tw.object.Invoice() ;\r\n\ttw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();\r\n\ttw.local.idcRequest.billOfLading[0] = new tw.object.Invoice() ;\r\n\ttw.local.idcRequest.requestDate = new tw.object.Date();\r\n\ttw.local.idcRequest.DBID = 0;\r\n}\r\n\r\n\/\/---------------------------------------------------First Time Init---------------------------------------------------------------------------\r\nif (tw.local.idcContract == null &amp;&amp; tw.local.idcContract == undefined) {\r\n\/\/IDCContract Init\r\ntw.local.idcContract = {};\r\n\r\ntw.local.idcContract.settlementAccounts = [];\r\ntw.local.idcContract.settlementAccounts[0] = {};\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount = {};\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = \"Customer Account\";\r\ntw.local.idcContract.settlementAccounts[0].accountNumberList = [];\r\n\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = {};\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.currency = {};\r\n\r\ntw.local.idcContract.settlementAccounts[0].debitedAmount = {};\r\n\r\ntw.local.idcContract.contractLimitsTracking = [];\r\ntw.local.idcContract.contractLimitsTracking[0] = {};\r\ntw.local.idcContract.contractLimitsTracking[0].partyType = {};\r\ntw.local.idcContract.contractLimitsTracking[0].type = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].jointVentureParent = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].customerNo = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].linkageRefNum = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].amountTag = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].contributionPercentage = 0.0;\r\n\r\ntw.local.idcContract.billCurrency = {};\r\n\r\ntw.local.idcContract.party = [];\r\ntw.local.idcContract.party[0] = {};\r\ntw.local.idcContract.party[0].partyType = {};\r\ntw.local.idcContract.party[0].partyType.name = \"Drawee\";\r\ntw.local.idcContract.party[0].partyType.value = \"Drawee\";\r\ntw.local.idcContract.party[0].partyId = tw.local.idcRequest.customerInformation.CIFNumber;\r\ntw.local.idcContract.party[0].name = tw.local.idcRequest.customerInformation.customerName;\r\ntw.local.idcContract.party[0].country = \"EG\";\r\ntw.local.idcContract.party[0].reference = \"NO REF\";\r\ntw.local.idcContract.party[0].address1 = tw.local.idcRequest.customerInformation.addressLine1;\r\ntw.local.idcContract.party[0].address2 =tw.local.idcRequest.customerInformation.addressLine2;\r\ntw.local.idcContract.party[0].address3 = \"\";\r\ntw.local.idcContract.party[0].address4 = \"\";\r\ntw.local.idcContract.party[0].media = \"\";\r\n\r\ntw.local.idcContract.party[0].phone = \"\";\r\ntw.local.idcContract.party[0].fax = \"\";\r\ntw.local.idcContract.party[0].email = \"\";\r\ntw.local.idcContract.party[0].contactPersonName = \"\";\r\ntw.local.idcContract.party[0].mobile = \"\";\r\ntw.local.idcContract.party[0].branch = {}\r\ntw.local.idcContract.party[0].branch.name = \"\";\r\ntw.local.idcContract.party[0].branch.value = \"\";\r\ntw.local.idcContract.party[0].language = \"\";\r\ntw.local.idcContract.party[0].partyCIF = \"\";\r\ntw.local.idcContract.party[0].isNbeCustomer = false;\r\n\r\ntw.local.idcContract.party[1] = {};\r\ntw.local.idcContract.party[1].partyType = {};\r\ntw.local.idcContract.party[1].partyType.name = \"Drawer\";\r\ntw.local.idcContract.party[1].partyType.value = \"Drawer\";\r\ntw.local.idcContract.party[1].partyId = \"\";\r\ntw.local.idcContract.party[1].name = \"\";\r\ntw.local.idcContract.party[1].country = \"\";\r\ntw.local.idcContract.party[1].reference = \"\";\r\ntw.local.idcContract.party[1].address1 = \"\";\r\ntw.local.idcContract.party[1].address2 = \"\";\r\ntw.local.idcContract.party[1].address3 = \"\";\r\ntw.local.idcContract.party[1].address4 = \"\";\r\ntw.local.idcContract.party[1].media = \"\";\r\n\r\ntw.local.idcContract.party[1].phone = \"\";\r\ntw.local.idcContract.party[1].fax = \"\";\r\ntw.local.idcContract.party[1].email = \"\";\r\ntw.local.idcContract.party[1].contactPersonName = \"\";\r\ntw.local.idcContract.party[1].mobile = \"\";\r\ntw.local.idcContract.party[1].branch = {}\r\ntw.local.idcContract.party[1].branch.name = \"\";\r\ntw.local.idcContract.party[1].branch.value = \"\";\r\ntw.local.idcContract.party[1].language = \"\";\r\ntw.local.idcContract.party[1].partyCIF = \"\";\r\ntw.local.idcContract.party[1].isNbeCustomer = false;\r\n\r\ntw.local.idcContract.liquidationSummary = {};\r\ntw.local.idcContract.IDCProduct = {};\r\ntw.local.idcContract.commissionsAndCharges = [];\r\n\r\ntw.local.idcContract.collateralCurrency = {};\r\ntw.local.idcContract.swiftMessageData = {};\r\ntw.local.idcContract.swiftMessageData.intermediary = {};\r\ntw.local.idcContract.swiftMessageData.accountWithInstitution = {};\r\ntw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};\r\ntw.local.idcContract.swiftMessageData.receiverCorrespondent = {};\r\ntw.local.idcContract.swiftMessageData.detailsOfPayment = {};\r\n\r\ntw.local.idcContract.swiftMessageData.orderingInstitution = {};\r\n\r\ntw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};\r\ntw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};\r\ntw.local.idcContract.swiftMessageData.orderingCustomer = {};\r\ntw.local.idcContract.swiftMessageData.senderToReciever = {};\r\n\r\ntw.local.idcContract.advices = [];\r\n\r\ntw.local.idcContract.cashCollateralAccounts = [];\r\ntw.local.idcContract.cashCollateralAccounts[0] = {};\r\ntw.local.idcContract.cashCollateralAccounts[0].accountNumber = {};\r\ntw.local.idcContract.facilities = new tw.object.listOf.creditFacilityInformation();\r\n\/\/-----------------------Defaults-------------------------\r\n\/\/Value Date\r\n\/\/tw.local.idcContract.transactionValueDate = new Date();\r\n\/\/tw.local.idcContract.transactionBaseDate = new Date();\r\n\/\/tw.local.idcContract.interestFromDate = new Date();\r\n\/\/tw.local.idcContract.interestToDate = tw.local.idcRequest.financialDetails.firstInstallementMaturityDate;\r\n\/\/\r\n\/\/tw.local.idcContract.tradeFinanceApprovalNumber = tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber;\r\n\/\/tw.local.draweeCIF = tw.local.idcRequest.customerInformation.CIFNumber;\r\n\/\/tw.local.contractAmount = tw.local.idcRequest.financialDetails.documentAmount;\r\n\/\/\/\/Contract Stage\r\n\/\/tw.local.idcContract.IDCRequestStage = tw.local.idcRequest.IDCRequestStage;\r\n\/\/\r\n\/\/\/\/Bill Details\r\n\/\/tw.local.idcContract.billCurrency.code = tw.local.idcRequest.financialDetails.documentCurrency.code;\r\n\/\/tw.local.idcContract.billCurrency.englishdescription = tw.local.idcRequest.financialDetails.documentCurrency.englishdescription;\r\n\/\/tw.local.idcContract.billAmount = tw.local.idcRequest.financialDetails.amtPayableByNBE;\r\n\/\/\r\n\/\/tw.local.idcContract.sourceReference = tw.local.idcRequest.appInfo.instanceID;\r\n\r\n}\r\n"]}},{"targetRef":"c8837dc6-c566-4f74-b2ba-43ebe1eed3d1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c455aaad-8f38-4379-9f6e-eac771f2671c","sourceRef":"ef5ff563-2eca-4c0c-96e1-f7aa8822f68a"},{"startQuantity":1,"outgoing":["c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0"],"incoming":["2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":131,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"LDAP_Get User Profile","dataInputAssociation":[{"targetRef":"2055.19bf907d-5bb3-4677-8403-cd5628d55c51","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["false"]}}]},{"targetRef":"2055.d3668934-f65c-433d-bc52-5dc16734b85a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"df7a70c4-b3e3-4f55-baab-0679b405a0d5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2","declaredType":"TFormalExpression","content":["tw.local.ldapUserProfile"]}}],"sourceRef":["2055.248c8a0a-74b7-4dfc-97cb-66303411feef"]}],"calledElement":"1.956a8aca-1727-4485-8c3f-261d2eafc464"},{"targetRef":"f5bbcf02-fed2-4857-881a-4a86ab60c63b","extensionElements":{"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2af8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0","sourceRef":"df7a70c4-b3e3-4f55-baab-0679b405a0d5"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.de01c43e-d3bb-4a84-9598-54a1044363a2"},{"itemSubjectRef":"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2","name":"ldapUserProfile","isCollection":false,"declaredType":"dataObject","id":"2056.3137f8dd-5fde-40d6-974c-806e4ef5af9c"},{"parallelMultiple":false,"outgoing":["39faa3a8-9d60-4a21-83bf-6eaebe0a89a1"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"20029c18-839b-498f-821e-498328580793"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a39fdd8f-02be-45d8-8b4b-fe54ac543e4b","otherAttributes":{"eventImplId":"b51a4d24-5fdc-408d-8cd8-284c53f36781"}}],"attachedToRef":"df7a70c4-b3e3-4f55-baab-0679b405a0d5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":166,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"64bca9f7-e995-4dbd-8832-5f8a6423718b","outputSet":{}},{"parallelMultiple":false,"outgoing":["e2c6615e-46be-4834-897f-039a9c5eaf67"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"75565f72-4570-42ee-836e-b6869be52f5b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ca2e4488-d77a-469d-8985-7f293c7424c5","otherAttributes":{"eventImplId":"d69202c6-3726-43bb-8349-45b5a99603ea"}}],"attachedToRef":"ef5ff563-2eca-4c0c-96e1-f7aa8822f68a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":440,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"198a77c6-014a-4b62-8b13-252747025f9a","outputSet":{}},{"incoming":["39faa3a8-9d60-4a21-83bf-6eaebe0a89a1","e2c6615e-46be-4834-897f-039a9c5eaf67","ae5949ab-4925-415c-8e1c-499886ad87aa"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8c813198-e29c-4a00-89ca-7d4ff2b691b3","otherAttributes":{"eventImplId":"0a63be62-c736-467f-85d9-713123d74162"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":264,"y":191,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[IDC Initialization Process Service -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[IDC Initialization Process Service -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"219c64af-723d-40e0-8f86-0a9845021651"},{"targetRef":"219c64af-723d-40e0-8f86-0a9845021651","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"39faa3a8-9d60-4a21-83bf-6eaebe0a89a1","sourceRef":"64bca9f7-e995-4dbd-8832-5f8a6423718b"},{"targetRef":"219c64af-723d-40e0-8f86-0a9845021651","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"e2c6615e-46be-4834-897f-039a9c5eaf67","sourceRef":"198a77c6-014a-4b62-8b13-252747025f9a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.0f23e3cd-b7a1-4be1-8aa5-45440df01941"},{"outgoing":["b5bcf8ad-c63d-41b1-8ede-e47a44561399","ae5949ab-4925-415c-8e1c-499886ad87aa"],"incoming":["c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0"],"default":"b5bcf8ad-c63d-41b1-8ede-e47a44561399","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":250,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"f5bbcf02-fed2-4857-881a-4a86ab60c63b"},{"targetRef":"ef5ff563-2eca-4c0c-96e1-f7aa8822f68a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Init IDC Request","declaredType":"sequenceFlow","id":"b5bcf8ad-c63d-41b1-8ede-e47a44561399","sourceRef":"f5bbcf02-fed2-4857-881a-4a86ab60c63b"},{"targetRef":"219c64af-723d-40e0-8f86-0a9845021651","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ae5949ab-4925-415c-8e1c-499886ad87aa","sourceRef":"f5bbcf02-fed2-4857-881a-4a86ab60c63b"}],"laneSet":[{"id":"9b5df769-a013-4c26-8b15-69d9f07d89d6","lane":[{"flowNodeRef":["d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7","c8837dc6-c566-4f74-b2ba-43ebe1eed3d1","ef5ff563-2eca-4c0c-96e1-f7aa8822f68a","df7a70c4-b3e3-4f55-baab-0679b405a0d5","64bca9f7-e995-4dbd-8832-5f8a6423718b","198a77c6-014a-4b62-8b13-252747025f9a","219c64af-723d-40e0-8f86-0a9845021651","f5bbcf02-fed2-4857-881a-4a86ab60c63b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c280878d-d884-4d75-abdb-4f41927427e3","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"IDC Initialization Process Service","declaredType":"process","id":"1.bd478cbf-52d1-4705-9fe4-6868408a5256","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.4d4ced80-1e82-4190-b046-e12e920ce53c"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.cdee38a2-a864-497e-8737-402567eae371"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.8e43443b-**************-41491f5d9a6b"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"bbf202fa-c170-4c76-89a8-b2868f47acef","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19","2055.23a2f68c-da9d-492b-8b22-d384b35cdca5"]}],"outputSet":[{"dataOutputRefs":["2055.4d4ced80-1e82-4190-b046-e12e920ce53c","2055.cdee38a2-a864-497e-8737-402567eae371","2055.8e43443b-**************-41491f5d9a6b"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.23a2f68c-da9d-492b-8b22-d384b35cdca5"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>eb23dce6-5a45-43aa-abd3-127ccee16c23</guid>
            <versionId>3b465f37-814a-4340-a1d2-7d05ae7416ea</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.23a2f68c-da9d-492b-8b22-d384b35cdca5</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7bb607c5-773e-4495-8dcc-9e2a7f50ac55</guid>
            <versionId>6922c932-197e-4fe3-a87b-d65f990e7de6</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a58b6649-b0e8-44c9-bfe0-fc67f134d5bf</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d4f1828a-1f32-4b2f-bdf4-c4d4ed4950aa</guid>
            <versionId>63c1efcf-dad3-4065-bfe1-d31309f2fedf</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4d4ced80-1e82-4190-b046-e12e920ce53c</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bbb87523-078b-49e7-af5a-8414e4579723</guid>
            <versionId>b8b310e8-5348-4920-a30e-597ca42109d1</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cdee38a2-a864-497e-8737-402567eae371</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>99e018ff-e43b-463a-8a59-8fc1e264c660</guid>
            <versionId>be01d8db-e1f8-4ef6-8769-38bd2519c554</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8e43443b-**************-41491f5d9a6b</processParameterId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>242f03d8-2a56-4131-a2df-02d09bb552ba</guid>
            <versionId>e2dda800-0971-41dc-803d-222a7da4c547</versionId>
        </processParameter>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.de01c43e-d3bb-4a84-9598-54a1044363a2</processVariableId>
            <description isNull="true" />
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ca30e8b-2389-46eb-a535-a948cf5aafc5</guid>
            <versionId>823faadb-1b1c-4fa5-8c98-c1f1ed160fa2</versionId>
        </processVariable>
        <processVariable name="ldapUserProfile">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3137f8dd-5fde-40d6-974c-806e4ef5af9c</processVariableId>
            <description isNull="true" />
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9644e23e-9bab-450f-9b44-f0d1a34a3e9d</guid>
            <versionId>499a1aba-d593-4e7e-b2aa-8d8a349f3c4c</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0f23e3cd-b7a1-4be1-8aa5-45440df01941</processVariableId>
            <description isNull="true" />
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>790b8901-8c71-4b90-ba2b-05454c451f32</guid>
            <versionId>3048dc71-f0cb-4e6c-a66b-d4bf598661be</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</processItemId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.3d808b72-f63d-4a34-adf0-d7c446c3b12c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed6</guid>
            <versionId>3fb65437-6353-47be-80cb-cbf370c1d897</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="250" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.3d808b72-f63d-4a34-adf0-d7c446c3b12c</switchId>
                <guid>853e187a-170c-469d-af03-88b500cc3593</guid>
                <versionId>85a3b0fa-5934-496d-8eb4-1f83603c13cf</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.d2c1a895-6b38-41b4-a472-142ec4993118</switchConditionId>
                    <switchId>3013.3d808b72-f63d-4a34-adf0-d7c446c3b12c</switchId>
                    <seq>1</seq>
                    <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3322</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>182cbf6d-a5a4-49c1-9154-a4c396de0845</guid>
                    <versionId>32276b3c-ef21-44a2-8836-02f8a2f7295f</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</processItemId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <name>Init IDC Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.073d67b8-12ef-4ed7-9fdb-3fa4a426a409</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6190</guid>
            <versionId>c52e0643-c0c1-49ca-9a02-ab946b56564a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="405" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-75db</errorHandlerItem>
                <errorHandlerItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.073d67b8-12ef-4ed7-9fdb-3fa4a426a409</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	tw.local.idcRequest.countryOfOrigin =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.facilityType = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails =new tw.object.BeneficiaryDetails();&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.executionHub =new tw.object.DBLookup();&#xD;
&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestType =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.importPurpose =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.paymentTerms =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productCategory =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails =new tw.object.ProductsDetails();&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification.id = null;&#xD;
	tw.local.idcRequest.productsDetails.HSProduct =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.appInfo.subStatus = "";&#xD;
	tw.local.idcRequest.appInfo.status = "";&#xD;
	tw.local.idcRequest.appInfo.initiator = tw.system.user.name;&#xD;
	tw.local.idcRequest.appInfo.requestDate = new tw.object.Date().format("dd/MM/yyyy"); &#xD;
	tw.local.idcRequest.appInfo.branch = tw.local.ldapUserProfile.branch;&#xD;
	tw.local.idcRequest.appInfo.requestName = "";&#xD;
	tw.local.idcRequest.appInfo.requestType = "";&#xD;
	tw.local.idcRequest.appInfo.appRef = "";&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
//	tw.local.idcRequest.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();&#xD;
&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	tw.local.idcRequest.approvals.CAD = false;&#xD;
	tw.local.idcRequest.approvals.compliance = false;&#xD;
	tw.local.idcRequest.approvals.treasury = false;&#xD;
	&#xD;
&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	tw.local.idcRequest.invoices[0] = new tw.object.Invoice() ;&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	tw.local.idcRequest.billOfLading[0] = new tw.object.Invoice() ;&#xD;
	tw.local.idcRequest.requestDate = new tw.object.Date();&#xD;
	tw.local.idcRequest.DBID = 0;&#xD;
}&#xD;
&#xD;
//---------------------------------------------------First Time Init---------------------------------------------------------------------------&#xD;
if (tw.local.idcContract == null &amp;&amp; tw.local.idcContract == undefined) {&#xD;
//IDCContract Init&#xD;
tw.local.idcContract = {};&#xD;
&#xD;
tw.local.idcContract.settlementAccounts = [];&#xD;
tw.local.idcContract.settlementAccounts[0] = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "Customer Account";&#xD;
tw.local.idcContract.settlementAccounts[0].accountNumberList = [];&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.currency = {};&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAmount = {};&#xD;
&#xD;
tw.local.idcContract.contractLimitsTracking = [];&#xD;
tw.local.idcContract.contractLimitsTracking[0] = {};&#xD;
tw.local.idcContract.contractLimitsTracking[0].partyType = {};&#xD;
tw.local.idcContract.contractLimitsTracking[0].type = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].jointVentureParent = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].customerNo = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].linkageRefNum = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].amountTag = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].contributionPercentage = 0.0;&#xD;
&#xD;
tw.local.idcContract.billCurrency = {};&#xD;
&#xD;
tw.local.idcContract.party = [];&#xD;
tw.local.idcContract.party[0] = {};&#xD;
tw.local.idcContract.party[0].partyType = {};&#xD;
tw.local.idcContract.party[0].partyType.name = "Drawee";&#xD;
tw.local.idcContract.party[0].partyType.value = "Drawee";&#xD;
tw.local.idcContract.party[0].partyId = tw.local.idcRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].name = tw.local.idcRequest.customerInformation.customerName;&#xD;
tw.local.idcContract.party[0].country = "EG";&#xD;
tw.local.idcContract.party[0].reference = "NO REF";&#xD;
tw.local.idcContract.party[0].address1 = tw.local.idcRequest.customerInformation.addressLine1;&#xD;
tw.local.idcContract.party[0].address2 =tw.local.idcRequest.customerInformation.addressLine2;&#xD;
tw.local.idcContract.party[0].address3 = "";&#xD;
tw.local.idcContract.party[0].address4 = "";&#xD;
tw.local.idcContract.party[0].media = "";&#xD;
&#xD;
tw.local.idcContract.party[0].phone = "";&#xD;
tw.local.idcContract.party[0].fax = "";&#xD;
tw.local.idcContract.party[0].email = "";&#xD;
tw.local.idcContract.party[0].contactPersonName = "";&#xD;
tw.local.idcContract.party[0].mobile = "";&#xD;
tw.local.idcContract.party[0].branch = {}&#xD;
tw.local.idcContract.party[0].branch.name = "";&#xD;
tw.local.idcContract.party[0].branch.value = "";&#xD;
tw.local.idcContract.party[0].language = "";&#xD;
tw.local.idcContract.party[0].partyCIF = "";&#xD;
tw.local.idcContract.party[0].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.party[1] = {};&#xD;
tw.local.idcContract.party[1].partyType = {};&#xD;
tw.local.idcContract.party[1].partyType.name = "Drawer";&#xD;
tw.local.idcContract.party[1].partyType.value = "Drawer";&#xD;
tw.local.idcContract.party[1].partyId = "";&#xD;
tw.local.idcContract.party[1].name = "";&#xD;
tw.local.idcContract.party[1].country = "";&#xD;
tw.local.idcContract.party[1].reference = "";&#xD;
tw.local.idcContract.party[1].address1 = "";&#xD;
tw.local.idcContract.party[1].address2 = "";&#xD;
tw.local.idcContract.party[1].address3 = "";&#xD;
tw.local.idcContract.party[1].address4 = "";&#xD;
tw.local.idcContract.party[1].media = "";&#xD;
&#xD;
tw.local.idcContract.party[1].phone = "";&#xD;
tw.local.idcContract.party[1].fax = "";&#xD;
tw.local.idcContract.party[1].email = "";&#xD;
tw.local.idcContract.party[1].contactPersonName = "";&#xD;
tw.local.idcContract.party[1].mobile = "";&#xD;
tw.local.idcContract.party[1].branch = {}&#xD;
tw.local.idcContract.party[1].branch.name = "";&#xD;
tw.local.idcContract.party[1].branch.value = "";&#xD;
tw.local.idcContract.party[1].language = "";&#xD;
tw.local.idcContract.party[1].partyCIF = "";&#xD;
tw.local.idcContract.party[1].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.liquidationSummary = {};&#xD;
tw.local.idcContract.IDCProduct = {};&#xD;
tw.local.idcContract.commissionsAndCharges = [];&#xD;
&#xD;
tw.local.idcContract.collateralCurrency = {};&#xD;
tw.local.idcContract.swiftMessageData = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediary = {};&#xD;
tw.local.idcContract.swiftMessageData.accountWithInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.receiverCorrespondent = {};&#xD;
tw.local.idcContract.swiftMessageData.detailsOfPayment = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.orderingInstitution = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};&#xD;
tw.local.idcContract.swiftMessageData.orderingCustomer = {};&#xD;
tw.local.idcContract.swiftMessageData.senderToReciever = {};&#xD;
&#xD;
tw.local.idcContract.advices = [];&#xD;
&#xD;
tw.local.idcContract.cashCollateralAccounts = [];&#xD;
tw.local.idcContract.cashCollateralAccounts[0] = {};&#xD;
tw.local.idcContract.cashCollateralAccounts[0].accountNumber = {};&#xD;
tw.local.idcContract.facilities = new tw.object.listOf.creditFacilityInformation();&#xD;
//-----------------------Defaults-------------------------&#xD;
//Value Date&#xD;
//tw.local.idcContract.transactionValueDate = new Date();&#xD;
//tw.local.idcContract.transactionBaseDate = new Date();&#xD;
//tw.local.idcContract.interestFromDate = new Date();&#xD;
//tw.local.idcContract.interestToDate = tw.local.idcRequest.financialDetails.firstInstallementMaturityDate;&#xD;
//&#xD;
//tw.local.idcContract.tradeFinanceApprovalNumber = tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber;&#xD;
//tw.local.draweeCIF = tw.local.idcRequest.customerInformation.CIFNumber;&#xD;
//tw.local.contractAmount = tw.local.idcRequest.financialDetails.documentAmount;&#xD;
////Contract Stage&#xD;
//tw.local.idcContract.IDCRequestStage = tw.local.idcRequest.IDCRequestStage;&#xD;
//&#xD;
////Bill Details&#xD;
//tw.local.idcContract.billCurrency.code = tw.local.idcRequest.financialDetails.documentCurrency.code;&#xD;
//tw.local.idcContract.billCurrency.englishdescription = tw.local.idcRequest.financialDetails.documentCurrency.englishdescription;&#xD;
//tw.local.idcContract.billAmount = tw.local.idcRequest.financialDetails.amtPayableByNBE;&#xD;
//&#xD;
//tw.local.idcContract.sourceReference = tw.local.idcRequest.appInfo.instanceID;&#xD;
&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>57ac6350-929e-44f0-af5b-bfb49a94b4f5</guid>
                <versionId>057f98f6-3c8e-4d5d-9c62-d8570ec8b3a6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df7a70c4-b3e3-4f55-baab-0679b405a0d5</processItemId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <name>LDAP_Get User Profile</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6192</guid>
            <versionId>d3bf01c9-445f-4c99-a7f7-a45c54a6b224</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="131" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-75db</errorHandlerItem>
                <errorHandlerItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.956a8aca-1727-4485-8c3f-261d2eafc464</attachedProcessRef>
                <guid>ba1c329d-463e-4c1b-ad39-b407af7feb04</guid>
                <versionId>5a7ba0e2-6a8a-494c-8bef-5eab5bb7ed44</versionId>
                <parameterMapping name="debugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.*************-4a3d-8a2c-d350d47c90b8</parameterMappingId>
                    <processParameterId>2055.19bf907d-5bb3-4677-8403-cd5628d55c51</processParameterId>
                    <parameterMappingParentId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>false</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4fd3e700-cc4f-4830-b27b-fa2302c7ba0d</guid>
                    <versionId>06ed888c-2d7d-4d2f-b52d-319c36690886</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="ldapUserProfile">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8a57b7ce-ed04-4cb2-888c-1c2707a66a2d</parameterMappingId>
                    <processParameterId>2055.248c8a0a-74b7-4dfc-97cb-66303411feef</processParameterId>
                    <parameterMappingParentId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ldapUserProfile</value>
                    <classRef>/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d34b62cd-8526-4388-b98d-71fddca10acc</guid>
                    <versionId>af4a00de-d175-44d8-92cc-481a683988a5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6321db70-ab51-40ff-a15d-c9e16c7d3489</parameterMappingId>
                    <processParameterId>2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643</processParameterId>
                    <parameterMappingParentId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>113c49c6-21d9-4e0a-8727-4b2d33152ff8</guid>
                    <versionId>be1bc913-43c5-4608-a904-ba495c7ef1ed</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6bba77ac-d7f0-4fb0-b805-4b9edaa472d0</parameterMappingId>
                    <processParameterId>2055.d3668934-f65c-433d-bc52-5dc16734b85a</processParameterId>
                    <parameterMappingParentId>3012.65fc6fe5-23e1-4511-bdf1-8a76e6843717</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6ba468b4-01d8-4554-874a-21f0a9a1bbec</guid>
                    <versionId>c9d2416a-5c94-4d65-81aa-58db5ca2123f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c8837dc6-c566-4f74-b2ba-43ebe1eed3d1</processItemId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.8f164246-6e87-4928-88d2-50063e37aa34</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6191</guid>
            <versionId>d41cfddc-5ef8-4115-b94e-0c57cee91538</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.8f164246-6e87-4928-88d2-50063e37aa34</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>1089d707-ff21-4654-a178-03cd98c29924</guid>
                <versionId>d6ef5c5f-177d-40bd-95c4-e4ef29436d93</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</processItemId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.a48aa0bf-f0ff-4a45-9116-e6ee8ed7f9e0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-75db</guid>
            <versionId>e216dfd0-5826-4ca7-b82e-a988fe17b7b2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.737558dd-65e1-415b-8444-0ae0c044f433</processItemPrePostId>
                <processItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[IDC Initialization Process Service -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[IDC Initialization Process Service -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>1a79bf92-c9b1-4f3a-9c73-0f6b12c21123</guid>
                <versionId>c9260503-bf56-41b0-a3f2-82266fe4d395</versionId>
            </processPrePosts>
            <layoutData x="264" y="191">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.a48aa0bf-f0ff-4a45-9116-e6ee8ed7f9e0</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>f47295e1-30fc-423d-9640-6c7e45aa767f</guid>
                <versionId>52ab4315-dccd-4635-8fe0-4b6cb78f7189</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.38145e99-64dc-40fd-8be2-3c42e15ada6c</parameterMappingId>
                    <processParameterId>2055.a58b6649-b0e8-44c9-bfe0-fc67f134d5bf</processParameterId>
                    <parameterMappingParentId>3007.a48aa0bf-f0ff-4a45-9116-e6ee8ed7f9e0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>036f4c6f-e541-4b2e-9051-4bbbab794091</guid>
                    <versionId>e850447f-fc9c-4310-98fc-47626740a1c8</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.e76e732a-f43f-492f-abf5-aaffb292459a</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <guid>4c574321-bc6a-4847-9bae-018238e8fd02</guid>
            <versionId>5acda830-7ac2-4c45-85d1-11942e2dc705</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.df7a70c4-b3e3-4f55-baab-0679b405a0d5</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="IDC Initialization Process Service" id="1.bd478cbf-52d1-4705-9fe4-6868408a5256" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="bbf202fa-c170-4c76-89a8-b2868f47acef" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.23a2f68c-da9d-492b-8b22-d384b35cdca5" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.4d4ced80-1e82-4190-b046-e12e920ce53c" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.cdee38a2-a864-497e-8737-402567eae371" />
                        
                        
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.8e43443b-**************-41491f5d9a6b" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.23a2f68c-da9d-492b-8b22-d384b35cdca5</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.4d4ced80-1e82-4190-b046-e12e920ce53c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.cdee38a2-a864-497e-8737-402567eae371</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.8e43443b-**************-41491f5d9a6b</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="9b5df769-a013-4c26-8b15-69d9f07d89d6">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c280878d-d884-4d75-abdb-4f41927427e3" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c8837dc6-c566-4f74-b2ba-43ebe1eed3d1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>df7a70c4-b3e3-4f55-baab-0679b405a0d5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>64bca9f7-e995-4dbd-8832-5f8a6423718b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>198a77c6-014a-4b62-8b13-252747025f9a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>219c64af-723d-40e0-8f86-0a9845021651</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f5bbcf02-fed2-4857-881a-4a86ab60c63b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c8837dc6-c566-4f74-b2ba-43ebe1eed3d1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6191</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c455aaad-8f38-4379-9f6e-eac771f2671c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="d2eb481b-74f8-4e42-aab8-b0ad8fcf53f7" targetRef="df7a70c4-b3e3-4f55-baab-0679b405a0d5" name="To Init IDC Request" id="2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init IDC Request" id="ef5ff563-2eca-4c0c-96e1-f7aa8822f68a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="405" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b5bcf8ad-c63d-41b1-8ede-e47a44561399</ns16:incoming>
                        
                        
                        <ns16:outgoing>c455aaad-8f38-4379-9f6e-eac771f2671c</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	tw.local.idcRequest.countryOfOrigin =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.facilityType = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails =new tw.object.BeneficiaryDetails();&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.financialDetails.executionHub =new tw.object.DBLookup();&#xD;
&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestType =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.importPurpose =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.paymentTerms =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productCategory =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails =new tw.object.ProductsDetails();&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification.id = null;&#xD;
	tw.local.idcRequest.productsDetails.HSProduct =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod =new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.appInfo.subStatus = "";&#xD;
	tw.local.idcRequest.appInfo.status = "";&#xD;
	tw.local.idcRequest.appInfo.initiator = tw.system.user.name;&#xD;
	tw.local.idcRequest.appInfo.requestDate = new tw.object.Date().format("dd/MM/yyyy"); &#xD;
	tw.local.idcRequest.appInfo.branch = tw.local.ldapUserProfile.branch;&#xD;
	tw.local.idcRequest.appInfo.requestName = "";&#xD;
	tw.local.idcRequest.appInfo.requestType = "";&#xD;
	tw.local.idcRequest.appInfo.appRef = "";&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
//	tw.local.idcRequest.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();&#xD;
&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	tw.local.idcRequest.approvals.CAD = false;&#xD;
	tw.local.idcRequest.approvals.compliance = false;&#xD;
	tw.local.idcRequest.approvals.treasury = false;&#xD;
	&#xD;
&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	tw.local.idcRequest.invoices[0] = new tw.object.Invoice() ;&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	tw.local.idcRequest.billOfLading[0] = new tw.object.Invoice() ;&#xD;
	tw.local.idcRequest.requestDate = new tw.object.Date();&#xD;
	tw.local.idcRequest.DBID = 0;&#xD;
}&#xD;
&#xD;
//---------------------------------------------------First Time Init---------------------------------------------------------------------------&#xD;
if (tw.local.idcContract == null &amp;&amp; tw.local.idcContract == undefined) {&#xD;
//IDCContract Init&#xD;
tw.local.idcContract = {};&#xD;
&#xD;
tw.local.idcContract.settlementAccounts = [];&#xD;
tw.local.idcContract.settlementAccounts[0] = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "Customer Account";&#xD;
tw.local.idcContract.settlementAccounts[0].accountNumberList = [];&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.currency = {};&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAmount = {};&#xD;
&#xD;
tw.local.idcContract.contractLimitsTracking = [];&#xD;
tw.local.idcContract.contractLimitsTracking[0] = {};&#xD;
tw.local.idcContract.contractLimitsTracking[0].partyType = {};&#xD;
tw.local.idcContract.contractLimitsTracking[0].type = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].jointVentureParent = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].customerNo = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].linkageRefNum = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].amountTag = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].contributionPercentage = 0.0;&#xD;
&#xD;
tw.local.idcContract.billCurrency = {};&#xD;
&#xD;
tw.local.idcContract.party = [];&#xD;
tw.local.idcContract.party[0] = {};&#xD;
tw.local.idcContract.party[0].partyType = {};&#xD;
tw.local.idcContract.party[0].partyType.name = "Drawee";&#xD;
tw.local.idcContract.party[0].partyType.value = "Drawee";&#xD;
tw.local.idcContract.party[0].partyId = tw.local.idcRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].name = tw.local.idcRequest.customerInformation.customerName;&#xD;
tw.local.idcContract.party[0].country = "EG";&#xD;
tw.local.idcContract.party[0].reference = "NO REF";&#xD;
tw.local.idcContract.party[0].address1 = tw.local.idcRequest.customerInformation.addressLine1;&#xD;
tw.local.idcContract.party[0].address2 =tw.local.idcRequest.customerInformation.addressLine2;&#xD;
tw.local.idcContract.party[0].address3 = "";&#xD;
tw.local.idcContract.party[0].address4 = "";&#xD;
tw.local.idcContract.party[0].media = "";&#xD;
&#xD;
tw.local.idcContract.party[0].phone = "";&#xD;
tw.local.idcContract.party[0].fax = "";&#xD;
tw.local.idcContract.party[0].email = "";&#xD;
tw.local.idcContract.party[0].contactPersonName = "";&#xD;
tw.local.idcContract.party[0].mobile = "";&#xD;
tw.local.idcContract.party[0].branch = {}&#xD;
tw.local.idcContract.party[0].branch.name = "";&#xD;
tw.local.idcContract.party[0].branch.value = "";&#xD;
tw.local.idcContract.party[0].language = "";&#xD;
tw.local.idcContract.party[0].partyCIF = "";&#xD;
tw.local.idcContract.party[0].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.party[1] = {};&#xD;
tw.local.idcContract.party[1].partyType = {};&#xD;
tw.local.idcContract.party[1].partyType.name = "Drawer";&#xD;
tw.local.idcContract.party[1].partyType.value = "Drawer";&#xD;
tw.local.idcContract.party[1].partyId = "";&#xD;
tw.local.idcContract.party[1].name = "";&#xD;
tw.local.idcContract.party[1].country = "";&#xD;
tw.local.idcContract.party[1].reference = "";&#xD;
tw.local.idcContract.party[1].address1 = "";&#xD;
tw.local.idcContract.party[1].address2 = "";&#xD;
tw.local.idcContract.party[1].address3 = "";&#xD;
tw.local.idcContract.party[1].address4 = "";&#xD;
tw.local.idcContract.party[1].media = "";&#xD;
&#xD;
tw.local.idcContract.party[1].phone = "";&#xD;
tw.local.idcContract.party[1].fax = "";&#xD;
tw.local.idcContract.party[1].email = "";&#xD;
tw.local.idcContract.party[1].contactPersonName = "";&#xD;
tw.local.idcContract.party[1].mobile = "";&#xD;
tw.local.idcContract.party[1].branch = {}&#xD;
tw.local.idcContract.party[1].branch.name = "";&#xD;
tw.local.idcContract.party[1].branch.value = "";&#xD;
tw.local.idcContract.party[1].language = "";&#xD;
tw.local.idcContract.party[1].partyCIF = "";&#xD;
tw.local.idcContract.party[1].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.liquidationSummary = {};&#xD;
tw.local.idcContract.IDCProduct = {};&#xD;
tw.local.idcContract.commissionsAndCharges = [];&#xD;
&#xD;
tw.local.idcContract.collateralCurrency = {};&#xD;
tw.local.idcContract.swiftMessageData = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediary = {};&#xD;
tw.local.idcContract.swiftMessageData.accountWithInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.receiverCorrespondent = {};&#xD;
tw.local.idcContract.swiftMessageData.detailsOfPayment = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.orderingInstitution = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};&#xD;
tw.local.idcContract.swiftMessageData.orderingCustomer = {};&#xD;
tw.local.idcContract.swiftMessageData.senderToReciever = {};&#xD;
&#xD;
tw.local.idcContract.advices = [];&#xD;
&#xD;
tw.local.idcContract.cashCollateralAccounts = [];&#xD;
tw.local.idcContract.cashCollateralAccounts[0] = {};&#xD;
tw.local.idcContract.cashCollateralAccounts[0].accountNumber = {};&#xD;
tw.local.idcContract.facilities = new tw.object.listOf.creditFacilityInformation();&#xD;
//-----------------------Defaults-------------------------&#xD;
//Value Date&#xD;
//tw.local.idcContract.transactionValueDate = new Date();&#xD;
//tw.local.idcContract.transactionBaseDate = new Date();&#xD;
//tw.local.idcContract.interestFromDate = new Date();&#xD;
//tw.local.idcContract.interestToDate = tw.local.idcRequest.financialDetails.firstInstallementMaturityDate;&#xD;
//&#xD;
//tw.local.idcContract.tradeFinanceApprovalNumber = tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber;&#xD;
//tw.local.draweeCIF = tw.local.idcRequest.customerInformation.CIFNumber;&#xD;
//tw.local.contractAmount = tw.local.idcRequest.financialDetails.documentAmount;&#xD;
////Contract Stage&#xD;
//tw.local.idcContract.IDCRequestStage = tw.local.idcRequest.IDCRequestStage;&#xD;
//&#xD;
////Bill Details&#xD;
//tw.local.idcContract.billCurrency.code = tw.local.idcRequest.financialDetails.documentCurrency.code;&#xD;
//tw.local.idcContract.billCurrency.englishdescription = tw.local.idcRequest.financialDetails.documentCurrency.englishdescription;&#xD;
//tw.local.idcContract.billAmount = tw.local.idcRequest.financialDetails.amtPayableByNBE;&#xD;
//&#xD;
//tw.local.idcContract.sourceReference = tw.local.idcRequest.appInfo.instanceID;&#xD;
&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ef5ff563-2eca-4c0c-96e1-f7aa8822f68a" targetRef="c8837dc6-c566-4f74-b2ba-43ebe1eed3d1" name="To End" id="c455aaad-8f38-4379-9f6e-eac771f2671c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.956a8aca-1727-4485-8c3f-261d2eafc464" name="LDAP_Get User Profile" id="df7a70c4-b3e3-4f55-baab-0679b405a0d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="131" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.5eb7f34a-a76f-4bb3-8f8b-cd287042ef76</ns16:incoming>
                        
                        
                        <ns16:outgoing>c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.19bf907d-5bb3-4677-8403-cd5628d55c51</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">false</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d3668934-f65c-433d-bc52-5dc16734b85a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.248c8a0a-74b7-4dfc-97cb-66303411feef</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2">tw.local.ldapUserProfile</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="df7a70c4-b3e3-4f55-baab-0679b405a0d5" targetRef="f5bbcf02-fed2-4857-881a-4a86ab60c63b" name="To Exclusive Gateway" id="c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2af8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.de01c43e-d3bb-4a84-9598-54a1044363a2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2" isCollection="false" name="ldapUserProfile" id="2056.3137f8dd-5fde-40d6-974c-806e4ef5af9c" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="df7a70c4-b3e3-4f55-baab-0679b405a0d5" parallelMultiple="false" name="Error" id="64bca9f7-e995-4dbd-8832-5f8a6423718b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="166" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>39faa3a8-9d60-4a21-83bf-6eaebe0a89a1</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="20029c18-839b-498f-821e-498328580793" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a39fdd8f-02be-45d8-8b4b-fe54ac543e4b" eventImplId="b51a4d24-5fdc-408d-8cd8-284c53f36781">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ef5ff563-2eca-4c0c-96e1-f7aa8822f68a" parallelMultiple="false" name="Error1" id="198a77c6-014a-4b62-8b13-252747025f9a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="440" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e2c6615e-46be-4834-897f-039a9c5eaf67</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="75565f72-4570-42ee-836e-b6869be52f5b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ca2e4488-d77a-469d-8985-7f293c7424c5" eventImplId="d69202c6-3726-43bb-8349-45b5a99603ea">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="219c64af-723d-40e0-8f86-0a9845021651">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="264" y="191" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[IDC Initialization Process Service -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[IDC Initialization Process Service -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>39faa3a8-9d60-4a21-83bf-6eaebe0a89a1</ns16:incoming>
                        
                        
                        <ns16:incoming>e2c6615e-46be-4834-897f-039a9c5eaf67</ns16:incoming>
                        
                        
                        <ns16:incoming>ae5949ab-4925-415c-8e1c-499886ad87aa</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="8c813198-e29c-4a00-89ca-7d4ff2b691b3" eventImplId="0a63be62-c736-467f-85d9-713123d74162">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="64bca9f7-e995-4dbd-8832-5f8a6423718b" targetRef="219c64af-723d-40e0-8f86-0a9845021651" name="To End Event" id="39faa3a8-9d60-4a21-83bf-6eaebe0a89a1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="198a77c6-014a-4b62-8b13-252747025f9a" targetRef="219c64af-723d-40e0-8f86-0a9845021651" name="To End Event" id="e2c6615e-46be-4834-897f-039a9c5eaf67">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.0f23e3cd-b7a1-4be1-8aa5-45440df01941" />
                    
                    
                    <ns16:exclusiveGateway default="b5bcf8ad-c63d-41b1-8ede-e47a44561399" name="Exclusive Gateway" id="f5bbcf02-fed2-4857-881a-4a86ab60c63b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="250" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0</ns16:incoming>
                        
                        
                        <ns16:outgoing>b5bcf8ad-c63d-41b1-8ede-e47a44561399</ns16:outgoing>
                        
                        
                        <ns16:outgoing>ae5949ab-4925-415c-8e1c-499886ad87aa</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f5bbcf02-fed2-4857-881a-4a86ab60c63b" targetRef="ef5ff563-2eca-4c0c-96e1-f7aa8822f68a" name="To Init IDC Request" id="b5bcf8ad-c63d-41b1-8ede-e47a44561399">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f5bbcf02-fed2-4857-881a-4a86ab60c63b" targetRef="219c64af-723d-40e0-8f86-0a9845021651" name="To End Event" id="ae5949ab-4925-415c-8e1c-499886ad87aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c0f4d6b7-eb99-457c-bfeb-a64e749a4ec0</processLinkId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.df7a70c4-b3e3-4f55-baab-0679b405a0d5</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2af8</endStateId>
            <toProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</toProcessItemId>
            <guid>6f8de3eb-d88f-4b0a-a3c2-5fd5f075d91c</guid>
            <versionId>0340ba67-be10-4f29-9251-36b5ef9837ee</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.df7a70c4-b3e3-4f55-baab-0679b405a0d5</fromProcessItemId>
            <toProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ae5949ab-4925-415c-8e1c-499886ad87aa</processLinkId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</fromProcessItemId>
            <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3322</endStateId>
            <toProcessItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</toProcessItemId>
            <guid>6b59acf1-d06f-414e-9303-7ed7a76017a7</guid>
            <versionId>73b35eea-47a3-4bca-b77b-bc546cd81c1c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</fromProcessItemId>
            <toProcessItemId>2025.219c64af-723d-40e0-8f86-0a9845021651</toProcessItemId>
        </link>
        <link name="To Init IDC Request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b5bcf8ad-c63d-41b1-8ede-e47a44561399</processLinkId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</toProcessItemId>
            <guid>9c3b3137-b154-4a06-b35f-39de1d39d472</guid>
            <versionId>a8d01014-b4d7-4936-a25c-19c3b35059ec</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f5bbcf02-fed2-4857-881a-4a86ab60c63b</fromProcessItemId>
            <toProcessItemId>2025.ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c455aaad-8f38-4379-9f6e-eac771f2671c</processLinkId>
            <processId>1.bd478cbf-52d1-4705-9fe4-6868408a5256</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c8837dc6-c566-4f74-b2ba-43ebe1eed3d1</toProcessItemId>
            <guid>d2ae5db8-d2f1-422a-9c07-166b161d1155</guid>
            <versionId>bc71f55f-c655-4b66-97c5-0a74acccc572</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ef5ff563-2eca-4c0c-96e1-f7aa8822f68a</fromProcessItemId>
            <toProcessItemId>2025.c8837dc6-c566-4f74-b2ba-43ebe1eed3d1</toProcessItemId>
        </link>
    </process>
</teamworks>

