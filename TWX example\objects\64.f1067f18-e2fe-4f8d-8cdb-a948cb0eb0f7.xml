<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7" name="Attachment">
        <lastModified>1692020654152</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;783eb8a0-73aa-4698-8a59-e50353bca554&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8726f0bd-a851-46f9-86ec-4513711356ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Attachment&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1fa72790-b486-43b4-81da-5db5e5c1b241&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32fb414d-13dc-4d89-811e-b6a041e58646&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b983a25-b893-4b09-8bdc-45f18726ac08&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36d1a8f6-3bbb-4d6d-87ce-580f9a9cb2d2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;211f5cdf-b0c2-4985-8b9c-e594125859f2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.visControl()&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;1eda16db-f2da-41e4-8c4b-b320317e537f&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:CustomHTML" version="8550"&gt;&lt;ns2:id&gt;4dca52db-ef32-4131-83a4-160218a19aa8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomHTML1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab8d7dd3-b405-4c4e-82a8-219a29cdae9e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@customHTML.contentType&lt;/ns2:optionName&gt;&lt;ns2:value&gt;TEXT&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34632d4e-2261-4b27-8f02-e9d2dfba4205&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@customHTML.textContent&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&amp;lt;style&amp;gt;
.spark-ui {
    background: rgb(255, 255, 255);
   // width: 99%;
}
.CoachView.Panel&amp;gt;.panel.SPARKPanel {
border-collapse: separate;
border-spacing: 0;
box-shadow: 0px 0px 12px #001B5929!important;
border-top: 5px solid #00643e!important;
border-radius: 10px!important;
}

.panel-primary.panel-dark&amp;gt;.panel-heading {
background: transparent !important;
border-color: transparent;
color: #fff;
}

.panel-primary.panel-dark {
border-color: transparent!important;
}
.panel-primary.panel-dark &amp;gt; .panel-heading .panel-title {
    color: rgb(0, 101, 71);
    font: normal normal 600 24px/45px Cairo;
}

.form-control {
    border-top-color: rgb(205, 205, 205);
    border-bottom-color: rgb(205,205,205);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 12px 12px;
    color: #181A1D !important;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.SPARKWell .stat-cell .bg-icon {
    line-height: normal;
    height: 100%;
    overflow: hidden;
 //   width: 97%;
    border-radius: inherit;
    box-shadow: 0px 0px 12px #001B5929!important;
    margin-bottom: 6px;
    border-radius: 10px!important;
    border-top: 5px solid !important;
   // margin-right: 49px;
}

//.form-control[disabled] {
//    background: rgb(242 242 242);
//}

.bg-success {
    background: #00654726 !important;
}
//.Single_Select select.placeHolder {
//    color: #0002037a;
//}

.panel-group .panel-heading+.panel-collapse .panel-body {
     border-top: 1px solid #fff;
}

.panel {
	     margin-bottom: 18px;
	     border-radius: 2px;
	     border-width: 0px;
}

.panel-group.panel-group-primary&amp;gt;.panel&amp;gt;.panel-heading&amp;gt;.accordion-toggle {
	     background: #fff;
	     color: #006547;
	     border-color: #fff;
	     padding: 15px;
	     border-radius: 10px!important;
}

.CoachView.Collapsible_Panel&amp;gt;.panel.SPARKCPanel {
	     border-collapse: collapse;
	     box-shadow: 0px 0px 22px #001B5929!important;
}
.SPARKCPanel &amp;gt; .panel-heading {
    padding: 0px;
    border-top-left-radius: 1px;
    border-top-right-radius: 1px;
    box-shadow: 0px 0px 0px #001B5929!important;
    border-top: 5px solid #00643e!important;
    border-radius: 10px!important;
    border-spacing: 0;
    border-collapse: separate;
}
.panel-body {
    background: #fff;
    margin: 0;
    padding-bottom: 15px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 15px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.panel-group .panel {
    border-radius: 10px;
}

.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{
    width: 50%;
}
.radio3 &amp;gt; input + span{
    display: inline-flex;
    flex-direction: row;
    align-items: center;
}
//.input-group.no-border&amp;gt;.input-group-addon {
//    border-radius: 10px;
//    min-width: 54px;
//    height: 54px;
//    top: -25px;
//}

//.form-group .input{
//    padding-left:73px!important;
//}
//.Input_Group .outer, .control-label{
//padding-left:73px;
//}
//.Single_Select, .control-label{
//padding-left:20!important;
//}
.Input_Group .ContentBox {
    width: 100%;
    border-collapse: collapse;
    padding-left: 20px;
}

.panel-group.panel-group-primary&amp;gt;.panel&amp;gt;.panel-heading&amp;gt;.accordion-toggle {
    background: #fff;
    color: #006547;
    border-color: #fff;
    border-radius: 10px!important;
    font-size: 18px;
    font-weight: 900;
    padding: 15px;
    font: normal normal 600 24px/45px Cairo;
}

.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role="img"]), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):active {
    padding: 12px 25px;
}
.btn-success, .btn-success:not([role="img"]):focus {
    color: #FFFFFF;
    fill: #006547;
    border-color: #006643;
    border-bottom-color: #006643;
    background: #006643;
    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;
    background-repeat: repeat-x;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
letter-spacing: 0px;
color: #181A1D !important;
opacity: 1;
font: normal normal normal 16px/20px Cairo;
background: transparent;
font-weight: bold;
border: 1px solid #000203;
}
//.Single_Select&amp;gt;.form-group&amp;gt;.input&amp;gt;select {
//
//    border-top-style: ridge;
//    border-bottom-style: outset;
//    border-right-style: outset;
//    border-left-style: ridge;
//    border-top-width: revert;
//    border-left-width: revert;
//    border-bottom-width: revert;
//    border-right-width: revert;
//}
select.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {
    height: 40px;
    line-height: 1.33;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    letter-spacing: 0px;
    color: #181A1D !important;
    opacity: 1;
    font: 14px Cairo;
    background: transparent;
    font-weight: lighter;
}


.btn-success:not([role="img"]):hover {
    color: #ffffff;
    fill: #3D8A70;
    border-color: #3D8A70;
    border-bottom-color: #429c42;
    background: #3D8A70;
    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;
    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;
    background-repeat: repeat-x;
}

.panel-group.panel-group-primary&amp;gt;.panel&amp;gt;.panel-heading&amp;gt;.accordion-toggle:hover {
    background: rgb(255 255 255 / 15%);
}
.panel-group .SPARKTable &amp;gt; .panel-heading {
    border-color: rgb(240, 240, 240);
    border-bottom-width: 2px
;
    border-bottom-style: solid;
    background: #006643 0% 0% no-repeat padding-box;
    color: white;
}
.Output_Text&amp;gt;.form-group&amp;gt;.input&amp;gt;p {
  
    padding-right: 1em;
 
}


.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
   
    font-weight: 600;
}




[class*="BPM_Resp_"] .bpm-label-default {
 height: 30px;
    font: normal normal normal 16px/20px Cairo !important;
    letter-spacing: 0px;
    opacity: 1;
    color: #8B8C8E;
}




//.ECMPropertiesContainer{
//display: none; 
//}


.Output_Text&amp;gt;.form-group&amp;gt;.input&amp;gt;p{
unicode-bidi: plaintext ;
text-align: inherit;
}

.CoachViewRTL {
 
    text-align: right !important;
}

.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&amp;gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&amp;gt;* {
    text-align: right;
}

.noIScroll.alignJustify {
    text-align: inherit;
}
.radio3 &amp;gt; input + span {

    unicode-bidi: plaintext;

}

.radio3 &amp;gt; input:checked + span::after {
 
  top: calc(50% - 3px) !important;
}
.switcher-primary .switcher-state-off {
    color: #000203;
}
.CoachViewRTL .Single_Select &amp;gt;{
 unicode-bidi: plaintext;

}

input[type="checkbox"]:checked[disabled] {
    right: 6%;
    opacity: 1;
    width: fit-content;
    left: 3px;
}

.Tab_Section&amp;gt;div&amp;gt;.nav-tabs-mnu {
    position: unset;
   }
   
a {
    color: #333;
    text-decoration: none;
}
.datepicker thead th {
   
    color: #006547;
&amp;lt;/style&amp;gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e96194ee-98df-475a-8e61-e19fcf7ccdd3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ECM_Document_List1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a349c3a4-415d-4e5a-8416-058dd0e2e526&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Attachment List&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa9a1d3c-7816-4ca4-84a5-192cb1b9edba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ae3111f-e825-4ae6-8888-f53445f0f9ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa29e0a9-42c0-4d07-8d31-9e4bd49fdc16&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowCreate&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.canCreate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba3c7de7-cee3-4140-8be9-40beb845b6ba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowUpdate&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.canUpdate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d848278d-788d-4115-8c91-b438224ee3cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ecmServerConfigurationName&lt;/ns2:optionName&gt;&lt;ns2:value&gt;FileNet&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87fd5632-a8df-461a-86d7-b013d28a70b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;documentObjectTypeId&lt;/ns2:optionName&gt;&lt;ns2:value&gt;IDCDocument&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab6ceebe-f94d-4d01-852d-7a8e3e242fa4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;folderPath&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.ECMproperties.fullPath&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c15c1358-741a-4ff4-89bf-f139bdbc53c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5785a387-f42e-4a3c-8c32-75dfaba9f027&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8aa4ea31-58d4-42b5-878b-5beb6abc9a2b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@className&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d5015c2-6e98-4ac1-8fed-4118d84d0ef2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@htmlOverrides&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67a64e37-e517-41ca-89a1-05b6400349f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_FILECLICKED&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${File_Viewer1}.setUrl(doc.url);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;437b516d-b6be-4fc7-8ecf-0e96f98c9fe3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowDelete&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.canDelete&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6565f717-0124-4259-8447-0c0020a7448b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;defaultProperties&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.ECMproperties.defaultProperties[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58c60e58-f244-4cc5-8b26-18038bcccfa5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;cmisQuery&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1aaf13e4-fd41-4c82-8c6c-f6f75836b072&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58cc1577-6d9d-4a17-8b76-3c523b406666&lt;/ns2:id&gt;&lt;ns2:optionName&gt;confirmDelete&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.c752c898-7a35-4c96-857a-feed3f59aeee&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;17704ab8-2ea1-4e01-8c68-171e412d3116&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;File_Viewer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c2eb382-272b-4cd4-86f4-a7dbaafe9e96&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;File Viewer&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;579f8c77-b64f-4a96-8ab2-7b96ba85064c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7a4da6f-f07b-49da-8079-ba0f61c5cd83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75bd4e64-0ecc-443a-81ce-87b5f11846bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;fitWidth&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3e0054dd-bb58-48e8-850d-6cc0c7dc5733&lt;/ns2:id&gt;&lt;ns2:optionName&gt;fitHeight&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;601e6485-2ce8-4d16-8cdb-d7c0acf0fc59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ctrlsPlacement&lt;/ns2:optionName&gt;&lt;ns2:value&gt;N&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38c7d603-7b35-4b90-8151-d44594c9e51d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4204483-1c6b-4079-8315-2c768950060c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1bd959ad-3c75-48c9-975b-8f234d664180&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;448429b2-6e10-4489-83ba-f7f2137bbf5d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Table&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89b1b092-9965-4c07-89e9-27f23a8dd894&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Document Validation&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7e30035a-0303-448f-83d9-607df6ee3f6c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01b5cd69-73fb-4909-8b24-44f2a77fd5bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;efa22bc5-cc6e-4bf8-8fd1-58f824bc556e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ADDREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b38f561-cae9-4012-8f51-b83dfd829d30&lt;/ns2:id&gt;&lt;ns2:optionName&gt;selectionMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;M&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b628fdd-d07f-41bb-8e55-9ecac2a86599&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0382e5c1-990e-465e-8b44-3323e36078e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ROWSLOADED&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c215e61f-e423-4d51-8bad-699bdac3b9aa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ceee38f3-c0f5-459b-8219-9e78a2721ea1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_NEWCELL&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a334b29-7be8-4b85-80d1-d342ac8626af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(item.name !="Others"){
alert("You can not delete this row");
return false;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21019cee-c120-42e9-8da8-b65bdf55e5a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d894129c-6b13-4117-861c-596bc46e3352&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1a39cfb0-b43f-40d4-83bb-ad5bee2276b3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;11c4ae8e-a2e9-4d47-8bb0-3a3141288a90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.attach[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;d63a21c3-1a8e-4318-831d-088bdd93dbd3&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;dfa2c52f-d43b-4367-873a-8879346441ae&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;docmentTypeName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;049334d6-f8eb-4219-a69a-155d62bda42c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e7ffadc9-f0a3-4a46-8f82-7e71a9ce12ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;English Name&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f6aa12e1-dd10-4904-87e1-0f184ed92e68&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;060e1bde-1fef-4eae-876e-0edd40ae4650&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;65f38cf1-01af-417a-8948-289a2004aa11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d55e0dfd-27f7-4f78-85e0-d5ce75a1dc8f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.attach.currentItem.name&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5ea5a02b-38c5-4e6b-8212-62c882808d70&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;arabicName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0fcbc91e-4e81-4387-866e-d5fba0d5209e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Arabic Name&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17f0a702-6e23-45b2-8ed5-a8ee6afe7e14&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9bd6c3ff-50db-4f43-88a6-a0671e14956c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8955721-5b36-4a50-89cf-2c94b35eae83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.attach.currentItem.arabicName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;508dca81-0fb4-4776-8974-f92c58cb253c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;documentTypeDescription&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d95d3dff-0b4d-4e2d-9d50-f9f62a93ca05&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3824e553-3be4-40b6-8673-71cb51ef8718&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Description&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;de0e92d3-9dd7-4424-833f-2361c152655f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;747fbc8c-b37f-440c-8d02-e56fa2303cec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d0615ca-302f-464b-87c1-8cb5f31fb129&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d97e532-96d1-476c-8e32-ee4aaca53508&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//if(newText != oldText){return confirm ("Are you sure you want to change the text field?");}
//if(oldText!=""){
//alert("test")
//	newText=oldText;
//}
//alert(oldText!="");

//${documentTypeDescription[0]}.setText("test");&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;358373cb-49ed-4cb7-81cc-e6fbd11c6932&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;409b1a10-e09c-40bd-818b-68a3e446a1ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
if(me.getText()!=""){me.setEnabled(false);}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.attach.currentItem.description&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4db533fd-2687-45da-8f0f-7308a936a0c1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Button1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea745958-30ce-48e7-8868-2105d6024d7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Add Other Document&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;724171f8-cd9f-44d3-859c-430d43ab238f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f19823af-39b8-4717-8ef1-3bed8135090c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e620287-f282-4a2c-8df4-1c62a7018d90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
${Table}.appendElement({"name":"Others", "arabicName":"اخرى", "description":""});
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f841f73-6539-4795-8c0f-294d2cf5d34b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05907b9b-7f10-4894-8c15-8d4db3df7021&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36b22ab2-1b85-433d-85a8-4047cef3028c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>8abad72b-9948-4515-ade7-a616d1fa245b</guid>
        <versionId>509ac3cb-bc5d-46f1-90b1-7c78b39d7a26</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="attach">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.f21e2088-3c97-40a4-943e-b6a4e400c04a</coachViewBindingTypeId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>true</isList>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>7f212c91-4d96-4aee-a184-3a74425393f3</guid>
            <versionId>de848857-44da-4680-9a3d-283ed841f2e1</versionId>
        </bindingType>
        <configOption name="canUpdate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.2b009876-caf6-4576-bc4d-d35c25a764ee</coachViewConfigOptionId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>2f337426-d8e0-4b2a-85b0-5f2063b77311</guid>
            <versionId>6c865873-c386-49ee-8eb6-594e54ab4030</versionId>
        </configOption>
        <configOption name="canCreate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.*************-490a-8229-f0ba928cacd9</coachViewConfigOptionId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>7190ec87-5b22-457d-a559-fb8fd8f1305f</guid>
            <versionId>491bbc6b-e78b-473f-aa9b-1cf461e8c6bd</versionId>
        </configOption>
        <configOption name="canDelete">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9b2c5054-9886-46fb-adcf-4208542a7019</coachViewConfigOptionId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>a3ae04f8-8300-481a-a9f1-3a648a072fca</guid>
            <versionId>b61aa08f-405c-4aec-a8ed-8682be99d971</versionId>
        </configOption>
        <configOption name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.0791d893-298b-4375-8c8e-eedae781e824</coachViewConfigOptionId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>24f4d8c6-1cd7-42f1-838c-6d96a701b7d6</guid>
            <versionId>bcc96fa9-d13a-4fd3-bcb9-f9dcb68ce724</versionId>
        </configOption>
        <configOption name="visiable">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d31623f1-cb5a-4e4c-a312-919e27b3a40b</coachViewConfigOptionId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>ffea6def-9244-41c6-b1cf-164b1c858a23</guid>
            <versionId>c7f8c70d-1a32-4610-afc5-13b93fc53257</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.2bf02a62-0eb2-45e6-ac96-4fe79259f3e3</coachViewInlineScriptId>
            <coachViewId>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>//this.add = function() {&#xD;
//	alert("adding");&#xD;
////	this.context.binding.get("value").set("name", "Others");&#xD;
////	this.ui.get("documentTypeDescription").setEnabled(true);&#xD;
////	&#xD;
//}&#xD;
var flag = false&#xD;
this.makeflagtrue = function () {&#xD;
	flag = true;&#xD;
	alert("makeing");&#xD;
}&#xD;
this.addAttacmentValidation=function () {&#xD;
	alert(flag);&#xD;
	var len = this.context.binding.get("value").length();&#xD;
	this.context.binding.get("value").get(len-1).set("name", "others")&#xD;
//	&#xD;
//	this.context.binding.get("value").get(len).set("name", "Others");&#xD;
//	this.ui.get("documentTypeDescription").setEnabled(true);&#xD;
&#xD;
	&#xD;
}&#xD;
this.visControl = function  () {&#xD;
&#xD;
	if (this.context.options.visiable.get("value")) {&#xD;
		this.ui.get("Table").setEnabled(true);&#xD;
		this.ui.get("Button1").setVisible(true,true);&#xD;
	}else{&#xD;
		this.ui.get("Table").setEnabled(false);&#xD;
		this.ui.get("Button1").setVisible(false,false);&#xD;
	}&#xD;
	&#xD;
	&#xD;
}&#xD;
this.setCMISquery = function  () {&#xD;
	var parent=this.context.options.parentPath;&#xD;
	var query ="SELECT * FROM cmis:Document WHERE IN_TREE('"+parent+"')";&#xD;
	this.context.options.cmisQuery.set("value", query);&#xD;
}&#xD;
&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>74501b6b-a3fe-4117-8126-c8d815672e37</guid>
            <versionId>bd0efe7d-a072-4917-84d9-d59465de3e96</versionId>
        </inlineScript>
    </coachView>
</teamworks>

