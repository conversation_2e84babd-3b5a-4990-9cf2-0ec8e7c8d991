{"metadata": {"project": {"id": "2066.45f2b4c6-0ecf-4efb-8f07-3f4e77132f0e", "name": "NEW NBE DC Processes", "shortName": "NEWNBED", "description": "", "isToolkit": false, "isSystem": false}, "branch": {"id": "2063.2cc41bc1-f5bb-4358-8e9c-15638e8a8202", "name": "Main", "description": ""}, "snapshot": {"id": "2064.2e3e75cb-79e1-4fc2-829a-127e163092b0", "name": "NEW_NBE_DC_Test3", "description": "<html><body>Test Nested process</body></html>", "creationDate": "2024-11-26T17:08:00.026+03:00"}, "buildInfo": {"buildId": "BPM8600-20211214-184954", "buildVersion": "8.6.3", "buildDescription": "IBM Business Process Manager V8.6.3.21030 - 20211214_2112 - BPM8600-20211214-184954"}}, "statistics": {"totalObjects": 203, "objectTypes": 9, "toolkits": 0, "extractedAt": "2025-06-09T10:57:41.561Z", "sourceFile": "TWX example.twx"}, "objectsByType": [{"typeName": "Process", "count": 111, "objects": [{"id": "1.********-6b73-40b1-957d-c2491f071bbb", "name": "getRequestType", "versionId": "efbec366-e797-45d2-9a92-b625a913f937", "hasDetails": false}, {"id": "1.027e3afb-1a94-4896-883d-daa4cdfee232", "name": "Get HUB name by code", "versionId": "cf9b85d7-e4d2-4dc2-9e41-5fd3490c6154", "hasDetails": false}, {"id": "1.02d07ad8-f212-4ea3-8b5e-e8457d984314", "name": "validate accounts and attachment", "versionId": "e7d97031-56df-4548-a0ab-0e5948847fd0", "hasDetails": false}, {"id": "1.041e4df2-7de8-4ce4-85a2-24f4482c8a7a", "name": "Execution Hub Processing Withdrawal Request Review", "versionId": "d6c4dd61-a0ce-410e-a5e1-aefc566f9b0e", "hasDetails": false}, {"id": "1.05e3bb1d-d52a-4ff9-9141-8307f650a0c9", "name": "Print Documents for Customer", "versionId": "503010f5-4243-4cbd-8d62-134c3d03e82e", "hasDetails": false}, {"id": "1.06eaabde-33db-480a-9d65-982fa27c2eac", "name": "Insert IDC Request", "versionId": "45d0fd4f-b287-4f32-a11d-c0369b35bc14", "hasDetails": false}, {"id": "1.07f8c418-7e5f-4b13-90a2-1e2660fc7597", "name": "Attachment", "versionId": "dc656848-3a27-4cf3-bc0f-be7095712a50", "hasDetails": false}, {"id": "1.0903f902-3a18-4b6d-9e59-1d4ad6437937", "name": "Review IDC Customs Release Request by Trade FO", "versionId": "3be3f9f3-da8c-48b4-96a7-f9f34867f5c7", "hasDetails": false}, {"id": "1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8", "name": "Check Customer Accounts", "versionId": "e7d1f10e-fd04-4367-ba26-aeab4c3edb93", "hasDetails": false}, {"id": "1.0d77a33d-fe75-4164-87ca-b9d0a0b91640", "name": "Get Interest Amount", "versionId": "e1b8f376-4c18-4c89-9511-78d482f982ae", "hasDetails": false}, {"id": "1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488", "name": "Check Bill", "versionId": "585872b8-a86e-40e7-8ebc-b9330273605d", "hasDetails": false}, {"id": "1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5", "name": "FC_Get Account Number", "versionId": "1396a188-ab01-4a12-8965-b83193544c74", "hasDetails": false}, {"id": "1.15b5fc3d-bb22-4981-b2bc-bafefbe8f721", "name": "Review Request by Credit Admin Execution Maker", "versionId": "f2021bfe-5e00-413e-a463-631eaeb79b7a", "hasDetails": false}, {"id": "1.16931d1e-3b6a-4725-9805-a04464e7991e", "name": "Review IDC Customs Release Request", "versionId": "8f5fff8e-eede-42ee-9e4c-eb63136b1769", "hasDetails": false}, {"id": "1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c", "name": "Generate BPM Request Number", "versionId": "78c0d0be-a20e-445e-95d5-f717564a75f6", "hasDetails": false}, {"id": "1.19d05118-4f9d-482a-afd5-662663bc3612", "name": "Create IDC Request", "versionId": "c6684745-aea7-48d3-b17a-570cd239aa61", "hasDetails": false}, {"id": "1.1d264c59-eafd-4f05-a029-411f466237ce", "name": "get advance payment", "versionId": "e79ea78c-c820-4f80-8789-75c76644ffc9", "hasDetails": false}, {"id": "1.1f68d702-993e-4d40-b851-571e35cda577", "name": "Reversal Approval Mail Service", "versionId": "60f2e782-0763-4672-822d-fcfe07eed11b", "hasDetails": false}, {"id": "1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51", "name": "Trade Compliance", "versionId": "5a364f9c-7622-48fb-8573-30f0a640056e", "hasDetails": false}, {"id": "1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f", "name": "Retrieve Customer Facilities", "versionId": "01e1a627-a82b-427d-a98b-b906a323ebd0", "hasDetails": false}, {"id": "1.2d69e423-2547-4495-845d-7bccbf743136", "name": "DB BPM Audit user tasks", "versionId": "4e69ec98-b083-4250-b444-8312c68bf7aa", "hasDetails": false}, {"id": "1.30211575-677e-4082-b83a-a9d7592dfc68", "name": "IDC Request Details UI", "versionId": "b415f71e-aee9-47ae-8bd8-e10e6b8b2f70", "hasDetails": false}, {"id": "1.31a44e81-e812-4dc3-b80d-e2bd710a4bad", "name": "Print Withdrawal Documents", "versionId": "07c9f141-bdc5-4c5a-80e4-4d509a68ca82", "hasDetails": false}, {"id": "1.33e9489c-6751-46e8-bc5c-74b573bdd856", "name": "update advance payment", "versionId": "8373c3f5-cf50-4f0d-9cb4-d5aed9bc8bc1", "hasDetails": false}, {"id": "1.37eab8a6-3b8b-4e52-8824-851827e9889b", "name": "Check Assign Large Corprate", "versionId": "8037e590-cde5-4c5f-bb6f-9d59bac51cdf", "hasDetails": false}, {"id": "1.3beb46eb-40aa-4939-bf56-225a33b731fd", "name": "Review IDC Withdrawal Request by Compliance Rep", "versionId": "0c9d8b08-496a-46a7-ac85-8e75fcece42a", "hasDetails": false}, {"id": "1.417ae6ff-837a-426f-b651-d8e3e8810d90", "name": "Check Existing GL Account", "versionId": "64cf0631-8b5b-4884-8a3a-ab390d205a43", "hasDetails": false}, {"id": "1.421a8257-34a5-45f9-b03f-a68e989d4ab8", "name": "Approvals Comments", "versionId": "8bbff875-a233-445f-9a27-b8bfa2be6cad", "hasDetails": false}, {"id": "1.43e432f4-0618-40df-9601-41aef33a2c23", "name": "Approve Request by Credit Admin Execution Checker", "versionId": "f7acd706-a1f7-47c0-9bd1-7c96fd6e4a14", "hasDetails": false}, {"id": "1.45e2a149-1d4a-4946-ad59-f81f1424f100", "name": "Create Folder FileNet", "versionId": "d8df3b6e-051c-495b-8085-957e0914d5e6", "hasDetails": false}, {"id": "1.********-457c-489f-9972-9349737b8a6e", "name": "Get Advices Code", "versionId": "d76e2b87-c084-41a4-aa81-1336fec3d7f3", "hasDetails": false}, {"id": "1.473ca24e-c03e-4a25-b37a-58cd047b0fff", "name": "Get Customer Information", "versionId": "106b6b3d-c66b-4e95-b1f1-a138f6a138c3", "hasDetails": false}, {"id": "1.480ae8a4-054d-4c43-9b3a-7c7c03194306", "name": "IDC Execution Hub Initiation Review", "versionId": "7e04ed62-6c92-45e9-8f7b-ba07ba12821a", "hasDetails": false}, {"id": "1.4c90d45f-cb2b-4afd-bc7a-b0f825c061a3", "name": "Execution Hub Processing Withdrawal Request", "versionId": "8a086af6-181f-4fe2-b687-29cf973a43d4", "hasDetails": false}, {"id": "1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13", "name": "Get Request Number And CBE", "versionId": "154abbc4-880d-4892-bb07-4e009700c72b", "hasDetails": false}, {"id": "1.4e624381-ed2d-4716-836c-494f4210ce96", "name": "convert currency", "versionId": "bef73ddf-aa6e-45da-b496-cb2893a566d9", "hasDetails": false}, {"id": "1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1", "name": "Client-Side Human Service", "versionId": "3b6bd824-0af0-4285-a55d-5235f1cbefd2", "hasDetails": false}, {"id": "1.53b740e1-0338-4573-9e9b-780485ae5319", "name": "Review IDC Withdrawal Request by Trade FO", "versionId": "84e6726d-00a9-4c2d-b9b1-b70a6adca153", "hasDetails": false}, {"id": "1.55393dcd-3352-41df-9690-75cb207d48b8", "name": "Get Exchange Rate", "versionId": "44eda8be-e2fb-4ce2-ad48-42ed1f19b2af", "hasDetails": false}, {"id": "1.5b59000d-e60c-4dee-87e1-34bfc64da51b", "name": "Check Invoice", "versionId": "5e845de4-135d-454c-bd98-e073e2c3c138", "hasDetails": false}, {"id": "1.5cb2beea-428b-42ba-b1c1-8b6c92bc89a0", "name": "IDC Execution Hub Initiation", "versionId": "5b8d1ff7-42b7-4543-b983-c04f8bab91c2", "hasDetails": false}, {"id": "1.604d7736-0fa5-4f03-af3c-19556056fdbf", "name": "update facility", "versionId": "0fb863f6-fe34-4648-9789-a8058a27dc3e", "hasDetails": false}, {"id": "1.6081d75e-a1c6-4d00-ade0-18cd3cfaaffa", "name": "Check Existing CIF", "versionId": "4875899d-0a9a-4397-8eca-291e30427256", "hasDetails": false}, {"id": "1.65481b15-1f15-43d3-b21f-fd25532bd3a4", "name": "Validate Collateral Amount", "versionId": "ce761e63-874e-4d57-8f2d-e48808e443de", "hasDetails": false}, {"id": "1.68698fcd-008a-4312-8428-1b0aee0e67c8", "name": "Delete Advance Payment", "versionId": "1513ec0e-fca2-4de3-8dcd-a3cb5b9a2f69", "hasDetails": false}, {"id": "1.69de9393-41a9-476a-9c6c-10054e92ebb8", "name": "Get Charge Details", "versionId": "c0102c8d-302a-4a3b-8d2c-d4e7365549f9", "hasDetails": false}, {"id": "1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df", "name": "Get Facility Codes", "versionId": "ec61280d-a56e-4d5b-ab3c-a1f96ccb1fed", "hasDetails": false}, {"id": "1.6d2930cb-0b25-4940-913e-e6643366bf6a", "name": "Cancel IDC", "versionId": "f3308354-323b-4503-8927-8816e96a8247", "hasDetails": false}, {"id": "1.6ed24840-941d-48c8-9769-8bbc53ced721", "name": "IDC Execution Hub Liquidation", "versionId": "1787bd0b-57eb-40ed-bcd5-f68fa6250b8b", "hasDetails": false}, {"id": "1.700681c9-fc9c-4767-b15d-063fdcbc57ed", "name": "Withdrawal Approval Mail Service", "versionId": "c93560c8-10b6-4d12-a981-279cc878a423", "hasDetails": false}, {"id": "1.7073c330-e8a8-4bd8-94fb-d764787dffeb", "name": "Execution HUB Filter Service", "versionId": "bb90ce49-9010-4cee-8d4d-ea8f86ab229d", "hasDetails": false}, {"id": "1.71c9670f-4700-465f-a303-3053d933a16e", "name": "Get Applied Charges", "versionId": "61c8ad15-3252-4361-8e7e-221b85be1614", "hasDetails": false}, {"id": "1.72840b39-03cc-42d8-a24b-01650b923101", "name": "Validate BIC", "versionId": "acfb07f7-5517-43f9-a493-0bdd78068752", "hasDetails": false}, {"id": "1.753ea3e8-d234-409e-9fb2-015400e4c9fb", "name": "Get Customer Accounts", "versionId": "b3bd252b-5841-4c98-beb8-464d9f17622b", "hasDetails": false}, {"id": "1.76e93686-1290-441b-87b6-9e3624b6ce34", "name": "Create Folder Structure", "versionId": "9891c5eb-aed4-470e-8b57-637f29eb7d1f", "hasDetails": false}, {"id": "1.7cd9c833-9de0-441c-987b-2cea94c2b257", "name": "test json", "versionId": "7065b81a-a1fb-4044-a3d4-9c906e371585", "hasDetails": false}, {"id": "1.7dcf1057-2d3e-4d5f-bea7-4f12cca3d3c4", "name": "Deployment Service Flow", "versionId": "3edca62b-a2ba-49cb-9a95-8581fdf9bd17", "hasDetails": false}, {"id": "1.7eee0980-1508-423c-95a0-c08797e92721", "name": "Create IDC Reversal Request", "versionId": "********-1d0c-49f7-b4a6-2361ce7b2758", "hasDetails": false}, {"id": "1.80b52f2e-cf28-4943-b3bd-447b9230d1db", "name": "IDC Request Details UI 3", "versionId": "0d14b68b-3083-4c3b-bd61-c787c3aecd10", "hasDetails": false}, {"id": "1.81cc6ee4-96cd-4a6a-97cf-31c4094876fd", "name": "Review IDC Request by Trade Front Office", "versionId": "c2c0b863-5b00-40de-8288-46bdf43cc88c", "hasDetails": false}, {"id": "1.8804be93-df98-44b0-bf01-1f35970dfee0", "name": "get booked facilities", "versionId": "14d6ae4e-b614-4869-aed1-77097c4f9d22", "hasDetails": false}, {"id": "1.89b422a8-bd14-41af-8dbc-4155dc2154f8", "name": "IDC Request Details UI_1", "versionId": "633735f9-ea5d-4aa6-8174-333f080f3db3", "hasDetails": false}, {"id": "1.8e17d2f7-29ca-4085-ba10-ed50e8088ff3", "name": "IDC Execution Hub Liquidation Review", "versionId": "21b131c1-4106-447a-80fa-8885da8b2e9b", "hasDetails": false}, {"id": "1.8ef1af07-5249-42da-b1a2-40f2e2ea490f", "name": "IDC Request Details UI 2", "versionId": "161d88a3-89d1-4f29-8cc4-84d5dfcca131", "hasDetails": false}, {"id": "1.8f81ee30-b515-498c-b076-7fcf05c5013d", "name": "Branch Hub filter service", "versionId": "3314b751-9904-485b-ab67-4e3bf2f6d912", "hasDetails": false}, {"id": "1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b", "name": "Create Liquidation", "versionId": "14b4332a-9733-4a90-b9e2-81cbbcbcddc9", "hasDetails": false}, {"id": "1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2", "name": "Get Required Documents", "versionId": "0cd7c585-f3c1-4732-8390-ee6b46efcaf3", "hasDetails": false}, {"id": "1.9545825f-4a3f-43f2-82b0-a6b75c82df6f", "name": "getLookups", "versionId": "393eb98b-f609-4418-a6e4-4b41c166b773", "hasDetails": false}, {"id": "1.96dc5449-b281-4b46-8b84-bf73531c54ff", "name": "cancel request", "versionId": "98330fbf-f4e8-4b51-a613-c860095f6cd3", "hasDetails": false}, {"id": "1.97607171-1a4e-436c-8b92-7a6920219722", "name": "Customs Release Approval Mail Service", "versionId": "8ebcbfcb-ab1c-4985-b703-1a40cd13a88e", "hasDetails": false}, {"id": "1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf", "name": "Get BIC Codes", "versionId": "1e6e6ff5-ed7e-4182-adc9-529fba296f72", "hasDetails": false}, {"id": "1.97d49092-c827-40be-bf11-7146f12c0134", "name": "Get IDC initiator", "versionId": "f530bd28-2c3c-41f6-b43c-502d7363fd16", "hasDetails": false}, {"id": "1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2", "name": "CAD filter service", "versionId": "8e12ecd0-e7fc-4851-bcd1-6e3046f5bb9b", "hasDetails": false}, {"id": "1.98cf1a97-9e24-44c6-9e06-44065464f45b", "name": "Review IDC Reversal Request", "versionId": "ad47be90-8369-4426-9c29-87af02e7a6f7", "hasDetails": false}, {"id": "1.9e0de1a3-7709-49c8-9b29-963ea4784c51", "name": "IDC Execution Hub Initiation 2", "versionId": "85b06b71-d037-473a-ad80-0b3621c3ab1c", "hasDetails": false}, {"id": "1.9f0a859b-5010-4ab6-947a-81ad99803cf1", "name": "Database Integration", "versionId": "7d0eadd2-805f-4406-bcc8-2ad6b25b4340", "hasDetails": false}, {"id": "1.a195e5d7-a7f4-4419-84bc-2bcff38a6561", "name": "Authorize ICAP", "versionId": "65a7057c-2e92-4adb-b5f0-39e53b996520", "hasDetails": false}, {"id": "1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058", "name": "Create CIF Folder", "versionId": "44205fb7-bc66-494c-aa1f-cfd44732b820", "hasDetails": false}, {"id": "1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258", "name": "Check Large corporate in DB", "versionId": "da92b884-e937-45c3-b77b-13a00d6fd441", "hasDetails": false}, {"id": "1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59", "name": "Update History", "versionId": "cbf38ad1-7216-4342-a2f8-852a4004eda1", "hasDetails": false}, {"id": "1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72", "name": "Client-Side Human Service_1", "versionId": "70293360-6bc8-4040-a332-b55749401b80", "hasDetails": false}, {"id": "1.b066b0aa-3b41-4663-a2f1-ca880f07a183", "name": "Filter CA Team", "versionId": "39ccb957-5a9e-4cc1-af21-148756d18c2e", "hasDetails": false}, {"id": "1.b0d126fc-c26e-4e48-b3c8-bf0af0485476", "name": "<PERSON><PERSON><PERSON>", "versionId": "005a140d-13e1-414c-9ef4-f919a087df20", "hasDetails": false}, {"id": "1.b16e9737-5d24-4229-83f7-2e9dc0637416", "name": "Get Product Codes", "versionId": "94cb3c17-ad1a-444c-9443-0a79e03f9a2e", "hasDetails": false}, {"id": "1.b24f815d-f98b-4f2e-9dbe-f7601985749f", "name": "Get Country of Origin", "versionId": "49fe4e23-b3ad-4893-9ab8-00a157ea3a7e", "hasDetails": false}, {"id": "1.b7869400-09ed-4f15-972f-39c44a324089", "name": "1.b7869400-09ed-4f15-972f-39c44a324089", "versionId": "3d49ff53-f14e-445c-826a-2e1e534e63f3", "hasDetails": false}, {"id": "1.b8deeb2e-af87-4d78-a0c3-a27d16bb22f8", "name": "Hub Liquidation Trade Compliance", "versionId": "c05f5cd6-2cbd-4661-9b0e-cfac593be59e", "hasDetails": false}, {"id": "1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300", "name": "Review Pending Queue by Credit Admin Execution Maker", "versionId": "ee5d4846-722c-40a1-a087-2e737861204c", "hasDetails": false}, {"id": "1.bd478cbf-52d1-4705-9fe4-6868408a5256", "name": "IDC Initialization Process Service", "versionId": "b85f4944-ce3a-4078-a025-170cba6c5726", "hasDetails": false}, {"id": "1.c042b0b3-**************-a4a851c1b331", "name": "Add Advance Payment", "versionId": "bdbad7cb-8c38-4cd3-9fa4-2f0110d5e0d2", "hasDetails": false}, {"id": "1.c04ab227-0184-4fcd-99e2-b165319d2807", "name": "Validate Required Documents", "versionId": "db717c91-795e-42b4-8aa4-ab91b87ea3f7", "hasDetails": false}, {"id": "1.c0682bdf-40ae-48f0-92cc-390e1358e90a", "name": "test", "versionId": "bf0d74be-3729-4165-b924-2c4e3faa3bce", "hasDetails": false}, {"id": "1.c3124e3b-0cdd-4710-a44b-6071a04e76dc", "name": "Get Party Details", "versionId": "6a1c2fa3-a60c-4e20-bb2d-08875bb8983e", "hasDetails": false}, {"id": "1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541", "name": "Get Booked Facilities", "versionId": "e65c6a95-f84a-4e05-8ebb-09058fcf8781", "hasDetails": false}, {"id": "1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165", "name": "Get Center by branch code", "versionId": "3d6f0975-26c1-481d-8b98-ef96abe9d20f", "hasDetails": false}, {"id": "1.cc39766e-740a-4aae-91d4-983d3f72c00c", "name": "Get CBE Sanctions", "versionId": "56dde49f-61bb-48fe-b400-384a4961055d", "hasDetails": false}, {"id": "1.d4263736-0fea-47c5-90ea-cc6b49adfcec", "name": "Test Swift", "versionId": "3af2140b-3fba-4022-a668-ec75b0cd31b4", "hasDetails": false}, {"id": "1.d5475add-79dd-4468-9671-f08aa8c7c181", "name": "Review Request by Treasury Maker", "versionId": "dd4b2646-7c46-40a3-85f2-19ad98279d06", "hasDetails": false}, {"id": "1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b", "name": "Escalation Mail Service", "versionId": "dde5b93d-5f58-413a-9c4e-335d330420ad", "hasDetails": false}, {"id": "1.e2bdefe5-4826-4c86-8897-1bfda517d27d", "name": "Create IDC Withdrawal Request", "versionId": "b18cbb03-cd9f-4625-b5ab-6154de5e0b4b", "hasDetails": false}, {"id": "1.e3028b61-b01a-4a56-a086-244083a8d445", "name": "Get Applicant and Accountee Facility Codes", "versionId": "a9cd3b56-b156-47f9-a16d-22b9ac5b4562", "hasDetails": false}, {"id": "1.e5318bc6-7232-4e45-a258-184ebb476098", "name": "IDC test", "versionId": "6532a569-a7f8-46fc-9b2b-4e37e36ae8f5", "hasDetails": false}, {"id": "1.e75c339e-472e-439b-878f-bfafd0c4b968", "name": "Update IDC Request", "versionId": "6719e525-cf7b-433e-bdeb-8fac927dd57f", "hasDetails": false}, {"id": "1.e93f9906-c4e8-472c-be9e-35e8202551c9", "name": "Hub Initiation Trade Compliance", "versionId": "a6922de6-9abf-4932-adeb-91bc37d9a2ba", "hasDetails": false}, {"id": "1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25", "name": "Create IDC Customs Release Request", "versionId": "203cbf5e-3e8a-44a9-bc4c-1d2fe5420e28", "hasDetails": false}, {"id": "1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1", "name": "get party type", "versionId": "011905f4-bb8b-46d2-bfb6-2eef2c<PERSON><PERSON><PERSON>", "hasDetails": false}, {"id": "1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3", "name": "Authorize FileNet", "versionId": "f3ffbae2-1002-46d8-aae2-2da8c742857c", "hasDetails": false}, {"id": "1.f19131b2-2481-4c62-a40d-8f829cdeb66a", "name": "Retrieve Request Data", "versionId": "5e659ced-4faf-498d-a457-4cbae46aa683", "hasDetails": false}, {"id": "1.f2c8f018-67e0-416a-acb3-8af30664cba5", "name": "<PERSON> <PERSON> App<PERSON>al", "versionId": "634e32ae-4dfc-4123-bd61-53bd0ef048b8", "hasDetails": false}, {"id": "1.f964f62b-c99c-41db-81e6-d7c13f5ef504", "name": "Review Request by Treasury Checker", "versionId": "e8837f42-8ac9-4aab-8dd8-8209314b2499", "hasDetails": false}, {"id": "1.fcc27b9d-793f-4d79-b8ed-d17e5877201f", "name": "Review IDC Request by Branch Compliance Rep", "versionId": "5ebd982d-c295-488a-bbb1-ee9c2b94f322", "hasDetails": false}]}, {"typeName": "Coach <PERSON>", "count": 34, "objects": [{"id": "64.0d7634e8-859f-4f60-b8ce-a0b32d5a1374", "name": "Withdrawal Request Trade FO", "versionId": "d40f4439-fb45-474b-acf2-8ed3d88c0a8f", "hasDetails": false}, {"id": "64.0fa21995-2169-498c-ba2e-ea66c3dc5616", "name": "Contract Creation", "versionId": "b7059166-d296-437a-9e30-5607ea8f75c7", "hasDetails": false}, {"id": "64.122789dd-9d59-4a0d-b507-23e1fe2141c4", "name": "Swift Message Data", "versionId": "9114d9e6-c538-4644-ba42-c22956c12cda", "hasDetails": false}, {"id": "64.1bff8164-e990-449a-85ee-473fa1c4f905", "name": "<PERSON><PERSON>", "versionId": "65a693d1-072c-46c6-8473-11c68cbe68fc", "hasDetails": false}, {"id": "64.23b35509-ef0d-4932-a3c5-3f3700e945b1", "name": "local facility", "versionId": "e35f281c-415b-4672-9861-fb9c8cadac1d", "hasDetails": false}, {"id": "64.349d9cfc-f3ed-4901-b89a-59594bac5b4f", "name": "Withdrawal Request Main", "versionId": "0a542800-ca84-4e8f-adaa-96391fe90321", "hasDetails": false}, {"id": "64.38c25c09-3e0f-4fe1-9480-9d69b975cd28", "name": "Payment Terms", "versionId": "220c10f0-a0a6-4465-862a-6c565bb42cdc", "hasDetails": false}, {"id": "64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705", "name": "Basic Details 2", "versionId": "c0891929-974a-4a9d-a8c0-bcde1ce66e4e", "hasDetails": false}, {"id": "64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f", "name": "Party", "versionId": "a3d608d5-1e66-480d-a9fe-673d01395e39", "hasDetails": false}, {"id": "64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97", "name": "Error Message", "versionId": "436ce08d-104e-4e18-bb0a-7ddeeab82936", "hasDetails": false}, {"id": "64.656cc232-8247-43c3-9481-3cc7a9aec2e6", "name": "Customer Information", "versionId": "d6db6ece-5692-4e79-b517-1fb798ac4e53", "hasDetails": false}, {"id": "64.66b344b9-e786-4183-bb38-58b60b772b7f", "name": "test", "versionId": "b2f379a3-f9b6-4dc7-be0f-7e0ffabc035f", "hasDetails": false}, {"id": "64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be", "name": "DC History", "versionId": "ce1826d0-8f84-40a3-9e7b-1b4ecc0d75df", "hasDetails": false}, {"id": "64.7020f6c3-7f8d-4052-81e6-d5b6b89f97e9", "name": "Financial Details  Branch Compliance Review", "versionId": "28375554-07f2-465b-8af1-b91ac2ac9540", "hasDetails": false}, {"id": "64.74d3cb97-ad59-4249-847b-a21122e44b22", "name": "Financial Details  Branch", "versionId": "a9997453-f712-494b-9341-469590d7acc2", "hasDetails": false}, {"id": "64.7a7ff012-3e34-4c17-949a-ed83917c49bf", "name": "new view", "versionId": "5c3c22fd-c39b-407c-b2ac-a7ba610ef812", "hasDetails": false}, {"id": "64.848ab487-8214-4d8b-88fd-a9cac5257791", "name": "Financial Details Trade FO", "versionId": "5059dce7-b1d2-4b7f-b88b-c9a36bf1972e", "hasDetails": false}, {"id": "64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5", "name": "Contract Liquidation", "versionId": "2fee23dd-76fc-485e-8d35-7a43b7a22e07", "hasDetails": false}, {"id": "64.9e7e0016-3899-48ff-9db4-bea34bea40f0", "name": "Limits Tracking", "versionId": "c173f944-afe2-4038-be7e-d5a90cd32beb", "hasDetails": false}, {"id": "64.ab2ea11b-7c5e-4835-9ed4-18708eee21be", "name": "Advance Payments Used", "versionId": "9bbe4e1d-2915-4f99-9ebc-c9ed04293d85", "hasDetails": false}, {"id": "64.ad8f3ff9-299c-4bd2-afe1-50281ea8b72a", "name": "Validation message", "versionId": "c3118473-49f4-4234-9b37-d25eb1c3120f", "hasDetails": false}, {"id": "64.b2d6d2ef-7c03-4419-bc73-52cbde1405b8", "name": "test View", "versionId": "bf849d2a-62f5-4f22-a118-410ad559c482", "hasDetails": false}, {"id": "64.b8ed6603-6938-4eb1-be9a-cf77018c844f", "name": "Customs Release Main", "versionId": "d1507c06-d2b2-4563-8b42-f1d021ab386d", "hasDetails": false}, {"id": "64.bd0ada34-acf3-449a-91df-9aa363c2b280", "name": "Products Details", "versionId": "c9d6f1fe-8209-4c83-94b9-0e48eb4187b4", "hasDetails": false}, {"id": "64.bec7782b-c964-4a09-b74f-0ec737efa310", "name": "Basic Details", "versionId": "2015adea-0de0-452d-b1ac-974b47bb9e44", "hasDetails": false}, {"id": "64.cfe950a2-9b16-4c82-9ca9-b8a5b2ffaa58", "name": "Reversal Request Main", "versionId": "b6f54eb0-4763-4805-ba70-d6e12612f41f", "hasDetails": false}, {"id": "64.d221b564-11f7-4ff9-84d6-ace71c94126c", "name": "IDC Booked Facility", "versionId": "fd93e00c-668f-4e2e-93d5-445e6ad8c5d1", "hasDetails": false}, {"id": "64.dd4f7118-11c6-4a2d-af1d-7252d135594d", "name": "Advice Lines", "versionId": "751cea61-0365-436f-8b20-ba8bc06176ae", "hasDetails": false}, {"id": "64.e45b18e8-a83a-4484-8089-b2ab3c33146a", "name": "Commissions And Charges", "versionId": "a0033234-5701-409e-ae02-871323b4035c", "hasDetails": false}, {"id": "64.e641d290-3254-4ce6-9377-d17c2feeeba0", "name": "Booked View", "versionId": "cc541601-bff4-461f-a756-cf5cda293c6a", "hasDetails": false}, {"id": "64.ed284446-1ac5-42cf-9523-6dd8086928a0", "name": "Validation Helper", "versionId": "8042f717-d79f-43f0-a7a6-247937b596c9", "hasDetails": false}, {"id": "64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7", "name": "Attachment", "versionId": "509ac3cb-bc5d-46f1-90b1-7c78b39d7a26", "hasDetails": false}, {"id": "64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4", "name": "Start New Request", "versionId": "e86d619b-ccd6-4aee-ba0a-271a3fca7bfb", "hasDetails": false}, {"id": "64.f9e6899d-e7d7-4296-ba71-268fcd57e296", "name": "DC Templete", "versionId": "a1e6cd50-2ba5-41c9-988d-5798953e1273", "hasDetails": false}]}, {"typeName": "Business Object", "count": 33, "objects": [{"id": "12.07546a6c-13be-4fe9-93f0-33c48a8492c7", "name": "DebitedAccount", "versionId": "4518ff06-f0b0-4fd1-95ce-a46f0ccc545f", "hasDetails": false}, {"id": "12.0b55eb1a-7871-4183-b9ee-f4146bfa5d07", "name": "ChargesObj", "versionId": "c975380a-eff0-4ded-a897-6da87fa50f6f", "hasDetails": false}, {"id": "12.********-ce0d-4b06-aafc-************", "name": "IDCWithdrawalRequest", "versionId": "0fa51b78-2b2f-42b5-b1bf-2e05405fdec4", "hasDetails": false}, {"id": "12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1", "name": "ExchangeCurrency", "versionId": "735d5b41-c5de-477e-992d-12cb012779dd", "hasDetails": false}, {"id": "12.36aae433-b480-4a1f-861b-6d30c09351a1", "name": "SettlementAccount", "versionId": "c904d9e8-f1cd-4e33-be30-df0bc0b41801", "hasDetails": false}, {"id": "12.36df9eba-480f-4caa-9609-6a99e6f2c474", "name": "IDCReversalRequest", "versionId": "317e6ac4-2627-4504-82ff-537a25747abb", "hasDetails": false}, {"id": "12.449bd82a-6888-4a7f-ab7d-01ca0e3d9b8e", "name": "Sequence", "versionId": "c11ff2ec-f930-4008-92a6-94da816492dd", "hasDetails": false}, {"id": "12.47b1b163-7266-46de-9171-529c0b65a377", "name": "FinancialDetails", "versionId": "e02397b0-4e8a-494e-8846-ca7de5eeb925", "hasDetails": false}, {"id": "12.5a746545-c6b4-49a5-85fe-aaccc003f1ef", "name": "CashCollateralAccount", "versionId": "01e6db89-c5ca-40aa-9099-2bfcd99d7370", "hasDetails": false}, {"id": "12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f", "name": "Attachment", "versionId": "7b9204f6-55b3-4b8a-9a28-ad1d7e0dbe49", "hasDetails": false}, {"id": "12.69b935ab-8de6-458e-aa2c-f8c8ff4862f3", "name": "ContractAdvice", "versionId": "d7a1872d-d258-41fc-aaba-37d02b71730f", "hasDetails": false}, {"id": "12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "name": "IDCContract", "versionId": "26ee22d0-fa5d-445e-8daa-cccca5d45a32", "hasDetails": false}, {"id": "12.770d3eea-9807-42ad-9ce2-c280a5145765", "name": "SwiftMessageData", "versionId": "671a4c32-56b6-4340-955e-901c20e68aec", "hasDetails": false}, {"id": "12.7a9e1924-91cb-42e8-a103-4e1d967340f1", "name": "IDCCustomsReleaseRequest", "versionId": "aa249f18-0202-4682-97c7-63f5b9232cbd", "hasDetails": false}, {"id": "12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "name": "IDCRequest", "versionId": "ddd44f72-c19b-4d04-93ae-331d609df38c", "hasDetails": false}, {"id": "12.89a53d06-50b8-41df-a5cc-5e4f61147b6d", "name": "CommissionsAndChargesDetails", "versionId": "acd6d01e-d2a6-4544-b543-fab8d2f8f23a", "hasDetails": false}, {"id": "12.8dacb128-2b07-4eaf-a2c1-3a0cb480a228", "name": "ProductsDetails", "versionId": "5db42eaf-ee63-4bbf-a6c2-2df63fc28a99", "hasDetails": false}, {"id": "12.9f3bdc5d-083e-4370-b8d4-f0a7541dcf11", "name": "IDCRoutingDetails", "versionId": "6ac32352-9901-46a8-8706-8f824cd3ff6c", "hasDetails": false}, {"id": "12.9ff3139c-f226-4eb9-b5b9-d827d8c74d45", "name": "PaymentTerm", "versionId": "7500088f-ea63-4b80-a51c-531e13334edc", "hasDetails": false}, {"id": "12.a025468c-38bc-4809-b337-57da9e95dacb", "name": "BeneficiaryDetails", "versionId": "ead285b1-af6c-4d2e-abef-275c70e7d9dd", "hasDetails": false}, {"id": "12.a39439e0-d96f-443a-9929-0d2b1f90de8e", "name": "ContractLimitTracking", "versionId": "27accf05-b38e-4895-a915-e183dc1a7ba2", "hasDetails": false}, {"id": "12.ae22157e-8e19-4a60-a294-712ff5dc96df", "name": "ECMproperties", "versionId": "9496e657-47bc-45ae-a0ca-577b0a711399", "hasDetails": false}, {"id": "12.af731d60-bee2-4d24-bfed-30192291dbd7", "name": "Parties", "versionId": "c0a588f3-7d27-47f9-9ec1-9bc9b19bc51f", "hasDetails": false}, {"id": "12.c15fd1af-f202-42aa-a4b5-3ebd0d5c99ad", "name": "DebitedAmount", "versionId": "ec3b484f-7df6-480e-aec8-dae6ed427f8a", "hasDetails": false}, {"id": "12.cc907e5e-4284-4dc5-8dea-8792e0a471c1", "name": "CustomerInformation", "versionId": "195418d2-6669-40e0-9bc5-d9ecd8a746d8", "hasDetails": false}, {"id": "12.ce3e5b4a-d024-4ff8-a49e-3a6a44d42027", "name": "SwiftMessagePart", "versionId": "696f7c3a-7502-4d3b-bde7-b021c1f82a93", "hasDetails": false}, {"id": "12.d249aea6-d076-4da7-887d-0f1dbba9713d", "name": "DBLookup", "versionId": "eed975dc-c6f5-496f-9d1e-7d7566847ff5", "hasDetails": false}, {"id": "12.dc2c5e47-1c75-43ac-86a7-93a939c19b0f", "name": "BasicDetails", "versionId": "f755037d-9b00-4989-aa29-1ce4ed282db5", "hasDetails": false}, {"id": "12.e9597b68-6c68-4101-ab1c-d6f8af32c79d", "name": "IDCBookedFacility", "versionId": "742ff11a-1e1e-4ef4-91a8-4ebf2985cb25", "hasDetails": false}, {"id": "12.e9e590bc-d1fd-48b5-9733-9a6240af3e5c", "name": "Approvals", "versionId": "278a2d99-8254-4442-a9dc-649bef3152d3", "hasDetails": false}, {"id": "12.ed2ab873-bea8-42bc-b684-6086fef5926a", "name": "LiquidationSummary", "versionId": "f8ea2889-f8cf-405e-a519-df68bddf2561", "hasDetails": false}, {"id": "12.eee84bca-f05c-48ad-9df2-43b9d111df36", "name": "Invoice", "versionId": "4afc7c87-3b6a-400c-8d8f-04fcba9fcebd", "hasDetails": false}, {"id": "12.fc23c5c1-8fd3-4584-89be-8352a606da67", "name": "UsedAdvancePayment", "versionId": "b2d3cafb-f9b5-4d6c-8855-6c1d930a5c5b", "hasDetails": false}]}, {"typeName": "Environment Property Variable", "count": 11, "objects": [{"id": "21.02818ba4-c183-4dfb-8924-18e2d9a515dd", "name": "IDCsubStatus", "versionId": "53a1eef5-10d4-4f0e-884c-456e873b9d49", "hasDetails": false}, {"id": "21.0bb89e09-258a-4bd1-b3a7-137a3219209f", "name": "IDCstage", "versionId": "349ef8f7-015c-4811-bb64-40ad15e2daee", "hasDetails": false}, {"id": "21.14b71d5c-752c-49c4-afba-cff5d026e9cd", "name": "IDCReversalSubStatus", "versionId": "2d17c6b5-15b1-458f-b8f0-b67ac4849a43", "hasDetails": false}, {"id": "21.2dce7af7-b766-40ab-acc0-0e77449191aa", "name": "IDCCustomsReleaseSuStatus", "versionId": "58240db9-3890-473c-91d1-463ae5514834", "hasDetails": false}, {"id": "21.3e88aca7-236e-4c3d-9b1c-b9c8ca160106", "name": "TeamsNameBPM", "versionId": "69a1c596-5799-47f3-b252-021e4c2da9d7", "hasDetails": false}, {"id": "21.8ce8b34e-54bb-4623-a4c9-ab892efacac6", "name": "Action", "versionId": "e0c7f213-6cb7-43fb-b599-f705d7b67a59", "hasDetails": false}, {"id": "21.a6e09805-0e21-4bb5-8e25-1aa9a5db67ad", "name": "IDCWithdrawalSubStatus", "versionId": "3953824e-5917-4b87-b6ed-efb06ac2cf10", "hasDetails": false}, {"id": "21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e", "name": "IDCStatus", "versionId": "7ea9f3d1-a287-4a97-a5ad-83b50748869d", "hasDetails": false}, {"id": "21.c7b9680f-d22f-48b4-8325-2716ee46bef1", "name": "ECMProperties", "versionId": "710d2c30-6310-4c36-b657-c8d10e814e46", "hasDetails": false}, {"id": "21.e5829eee-0ab1-4f47-9191-f0f8705bc33e", "name": "IDCState", "versionId": "45f71686-4dfb-46c1-9af4-51cb4c0b4d2c", "hasDetails": false}, {"id": "21.f83b19ad-8a7f-4026-bded-63fd31478fbd", "name": "BPMTeamsNamesReassign", "versionId": "53118022-8ec6-44e8-a17c-0bcb737bd430", "hasDetails": false}]}, {"typeName": "Business Process Definition", "count": 7, "objects": [{"id": "25.20ec3263-cd76-4c3d-8882-363ec29555fd", "name": "IDC Customs Release", "versionId": "05b2e5f1-ff1b-4750-b084-e72f5bfe325d", "hasDetails": false}, {"id": "25.410525f9-7abe-4470-ba1b-0afa24a6570b", "name": "IDC Withdrawal", "versionId": "7b1eb06e-54ef-4654-abfa-ffb77bee1b6f", "hasDetails": false}, {"id": "25.4de86ec1-53b9-4472-8ae0-17c04487a8d3", "name": "", "versionId": "9c0572c1-cb23-4399-9a0f-5e0a8c559bdc", "hasDetails": false}, {"id": "25.76082daa-5c1c-4f37-b09c-3a01e36f276c", "name": "IDC Request", "versionId": "dd161cd6-32b2-4b6d-ae58-da143ec15b27", "hasDetails": false}, {"id": "25.adb8fd93-1422-4df4-8c36-cbcc83243295", "name": "Test Nested Process", "versionId": "a6db8ada-5bdc-482c-9c72-525ca73c4881", "hasDetails": false}, {"id": "25.cdbb0d70-3d5a-4206-948e-194d0bed57ea", "name": "", "versionId": "897407a2-f6d8-42f4-90f7-6daf3aa7c9f5", "hasDetails": false}, {"id": "25.d582b1c1-db43-4886-b5bb-cfc5fc703349", "name": "IDC Reversal", "versionId": "789eb2c2-20f4-443c-ab44-4eeb9b50209f", "hasDetails": false}]}, {"typeName": "Managed Asset", "count": 3, "objects": [{"id": "61.45b9ebf8-56b3-4774-b7a1-ec385ab045f8", "name": "common.js", "versionId": "4bea6092-0194-4871-a30f-d9e260fe0946", "hasDetails": false}, {"id": "61.4e7caef0-428a-4547-bc22-4226413e0adf", "name": "header1.png", "versionId": "e39255ca-b683-4836-9b40-fc39e9a35b8b", "hasDetails": false}, {"id": "61.81d07735-1367-4a03-a508-9c646519dab0", "name": "0aacb363-83b1-4d0a-959d-7ab705b08e5c.zip", "versionId": "9fb6fc34-81c2-4016-87d4-39318c34ede4", "hasDetails": false}]}, {"typeName": "Resource Bundle", "count": 2, "objects": [{"id": "50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c", "name": "FiledsNames", "versionId": "7646c625-de65-4b75-84ee-66577834dae6", "hasDetails": false}, {"id": "50.d37ebe05-41d3-47ac-9237-53de467d6a4a", "name": "Lookups", "versionId": "8abe40c3-12aa-468d-a12f-99435df5e489", "hasDetails": false}]}, {"typeName": "Environment Variables", "count": 1, "objects": [{"id": "62.bb4641b5-49e7-44bc-941e-1b41651c0851", "name": "Environment Variables", "versionId": "909079cb-c2d4-4e12-b0cf-8c52306427bd", "hasDetails": false}]}, {"typeName": "Project Settings", "count": 1, "objects": [{"id": "63.e4004116-1272-4dd8-b1dc-f57199b8335f", "name": "Process App Settings", "versionId": "2acc7763-1f08-4258-8ba3-db78a6f141bc", "hasDetails": false}]}], "toolkits": []}