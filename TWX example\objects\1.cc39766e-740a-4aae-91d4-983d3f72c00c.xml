<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.cc39766e-740a-4aae-91d4-983d3f72c00c" name="Get CBE Sanctions">
        <lastModified>1692505980058</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d129d29f-5540-4407-a02b-5840aba76134</guid>
        <versionId>56dde49f-61bb-48fe-b400-384a4961055d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e36" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9accbfb8-1a69-4d22-9df4-796f93063559"},{"incoming":["283c7017-b0c8-4189-b71e-0fc7598fbd30","3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"a14a63ac-ec23-4058-bf5a-8e34a37edcbb"},{"targetRef":"8616db69-c420-4f27-ac49-e04a71b8811a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set SQL","declaredType":"sequenceFlow","id":"2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0","sourceRef":"9accbfb8-1a69-4d22-9df4-796f93063559"},{"startQuantity":1,"outgoing":["bfaf6366-9504-4882-923a-2633540ffa08"],"incoming":["521102e6-e36a-4e81-876e-4a16b8e40f51"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":325,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.parameters"]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Record\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"662c8c47-eb73-4324-9c84-144a9472f428","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["tw.local.tempresult"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"e677dc81-7aef-49cc-b444-ff06c47f8581","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Result","declaredType":"sequenceFlow","id":"bfaf6366-9504-4882-923a-2633540ffa08","sourceRef":"662c8c47-eb73-4324-9c84-144a9472f428"},{"startQuantity":1,"outgoing":["521102e6-e36a-4e81-876e-4a16b8e40f51"],"incoming":["2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":139,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Set SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8616db69-c420-4f27-ac49-e04a71b8811a","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"SELECT ID FROM BPM.IDC_NBE_CUSTOMER_SANCTIONS where CIF_NUMBER = ?\";\r\n\r\ntw.local.parameters= new tw.object.listOf.SQLParameter();\r\ntw.local.parameters[0]= new tw.object.SQLParameter();\r\ntw.local.parameters[0].type=\"VARCHAR\";\r\ntw.local.parameters[0].value = tw.local.cif;\n"]}},{"targetRef":"662c8c47-eb73-4324-9c84-144a9472f428","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"521102e6-e36a-4e81-876e-4a16b8e40f51","sourceRef":"8616db69-c420-4f27-ac49-e04a71b8811a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.414a0fd9-9abe-45f3-855f-bf6d91691c0b"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"tempresult","isCollection":true,"declaredType":"dataObject","id":"2056.95866585-7a5e-460f-a2cc-1a39f217d8da"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.332a3373-82e6-42c1-b3b1-5f1bee522b6a"},{"startQuantity":1,"outgoing":["283c7017-b0c8-4189-b71e-0fc7598fbd30"],"incoming":["bfaf6366-9504-4882-923a-2633540ffa08"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":471,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Result","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e677dc81-7aef-49cc-b444-ff06c47f8581","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.tempresult != null) {\r\n\tif (tw.local.tempresult.listLength&gt;0) {\r\n\ttw.local.result = \"YES\";\r\n\t}else{\r\n\t\ttw.local.result = \"NO\";\r\n\t}\r\n}\r\nelse{\r\n\ttw.local.result = \"NO\";\r\n}"]}},{"targetRef":"a14a63ac-ec23-4058-bf5a-8e34a37edcbb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"283c7017-b0c8-4189-b71e-0fc7598fbd30","sourceRef":"e677dc81-7aef-49cc-b444-ff06c47f8581"},{"parallelMultiple":false,"outgoing":["268d1a06-b93c-4b25-8c35-ec07e2c74b8f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f0da7b0f-a312-45b0-89f2-70f48170c817"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ddab7db8-8566-437f-85c6-a09a4a98e5b4","otherAttributes":{"eventImplId":"3c09eec6-0065-4aed-8fbe-e08792b65fda"}}],"attachedToRef":"8616db69-c420-4f27-ac49-e04a71b8811a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":174,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"34bdedb3-e67f-4a80-8ca6-645c8bc8bd22","outputSet":{}},{"parallelMultiple":false,"outgoing":["e1a7cf3e-b343-4b60-89f6-d3d6cc33778d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"66b30a96-452e-4260-8da6-d15c8a79ae85"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0561ec5d-6c2a-4b46-8e1d-f3ae5e6f5318","otherAttributes":{"eventImplId":"e7c53e0f-ebdd-44a4-8cdf-7a8190190c6e"}}],"attachedToRef":"662c8c47-eb73-4324-9c84-144a9472f428","extensionElements":{"nodeVisualInfo":[{"width":24,"x":360,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"baef34cc-ba39-4b21-8e0f-ea7d13b91f2f","outputSet":{}},{"parallelMultiple":false,"outgoing":["59ff4658-8b33-483d-8710-f7ab3e543564"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"690dd4d5-6ff8-4da6-8553-1e0cce83ab35"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"103d679b-dad2-40a4-8f26-e1eb2aef079e","otherAttributes":{"eventImplId":"845add23-89f7-424e-82d3-b3be452e8a3c"}}],"attachedToRef":"e677dc81-7aef-49cc-b444-ff06c47f8581","extensionElements":{"nodeVisualInfo":[{"width":24,"x":506,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"8750c421-6358-4742-8616-6864d59c24c1","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c654ab1b-7071-4ac2-8289-34691ae169d0"},{"startQuantity":1,"outgoing":["3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c"],"incoming":["268d1a06-b93c-4b25-8c35-ec07e2c74b8f","e1a7cf3e-b343-4b60-89f6-d3d6cc33778d","59ff4658-8b33-483d-8710-f7ab3e543564"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":341,"y":202,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"268d1a06-b93c-4b25-8c35-ec07e2c74b8f","sourceRef":"34bdedb3-e67f-4a80-8ca6-645c8bc8bd22"},{"targetRef":"e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"e1a7cf3e-b343-4b60-89f6-d3d6cc33778d","sourceRef":"baef34cc-ba39-4b21-8e0f-ea7d13b91f2f"},{"targetRef":"e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"59ff4658-8b33-483d-8710-f7ab3e543564","sourceRef":"8750c421-6358-4742-8616-6864d59c24c1"},{"targetRef":"a14a63ac-ec23-4058-bf5a-8e34a37edcbb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c","sourceRef":"e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.d81557d3-dc52-42e0-84a8-a961a487a798"}],"laneSet":[{"id":"bb7c2437-7540-4309-8420-f6a2ff8384ab","lane":[{"flowNodeRef":["9accbfb8-1a69-4d22-9df4-796f93063559","a14a63ac-ec23-4058-bf5a-8e34a37edcbb","662c8c47-eb73-4324-9c84-144a9472f428","8616db69-c420-4f27-ac49-e04a71b8811a","e677dc81-7aef-49cc-b444-ff06c47f8581","34bdedb3-e67f-4a80-8ca6-645c8bc8bd22","baef34cc-ba39-4b21-8e0f-ea7d13b91f2f","8750c421-6358-4742-8616-6864d59c24c1","e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"2471700b-46df-414e-b6f5-1c8b169844ef","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get CBE Sanctions","declaredType":"process","id":"1.cc39766e-740a-4aae-91d4-983d3f72c00c","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"result","isCollection":false,"id":"2055.e55e3035-c733-4b86-8251-e07e25aafcb2"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7"}],"inputSet":[{"dataInputRefs":["2055.b7ca2766-fba7-4fca-87a1-873a04b2291c"]}],"outputSet":[{"dataOutputRefs":["2055.e55e3035-c733-4b86-8251-e07e25aafcb2","2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"12345678\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cif","isCollection":false,"id":"2055.b7ca2766-fba7-4fca-87a1-873a04b2291c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="cif">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b7ca2766-fba7-4fca-87a1-873a04b2291c</processParameterId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"12345678"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4a35c03a-1387-4c87-91ea-63a355ae5770</guid>
            <versionId>d84fe461-18c6-4c23-825c-1a8e5481ce0c</versionId>
        </processParameter>
        <processParameter name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e55e3035-c733-4b86-8251-e07e25aafcb2</processParameterId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7940c07a-9880-4572-9bd4-dea672d54854</guid>
            <versionId>ec6032f1-e25f-4c6b-8427-251cc72bd204</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7</processParameterId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e2db4ed9-6cdc-4b7b-b533-55292e25cf76</guid>
            <versionId>e5e64fab-5c18-449d-8d0a-dbe15fbd0b63</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.414a0fd9-9abe-45f3-855f-bf6d91691c0b</processVariableId>
            <description isNull="true" />
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a5a91733-1392-4377-9e31-9a9c9271ef8a</guid>
            <versionId>e1ab5d91-83b6-4c06-8fdb-391caf3b510a</versionId>
        </processVariable>
        <processVariable name="tempresult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.95866585-7a5e-460f-a2cc-1a39f217d8da</processVariableId>
            <description isNull="true" />
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c55af1ea-99ea-430e-a451-31f49218cf9b</guid>
            <versionId>afbe90c2-5990-426e-b9d4-227a696ea50a</versionId>
        </processVariable>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.332a3373-82e6-42c1-b3b1-5f1bee522b6a</processVariableId>
            <description isNull="true" />
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>860beba2-6a3e-4742-9627-3939929e05fc</guid>
            <versionId>25d17fef-1315-4bf6-b440-f238e1ebaf70</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c654ab1b-7071-4ac2-8289-34691ae169d0</processVariableId>
            <description isNull="true" />
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8d6026c2-e4db-417b-b990-11782d5741d4</guid>
            <versionId>cc1cbc9b-46ad-403e-8951-6e03baf37644</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d81557d3-dc52-42e0-84a8-a961a487a798</processVariableId>
            <description isNull="true" />
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>294d3acf-4188-41c6-9cd5-50c3c473eafe</guid>
            <versionId>33e33bc9-6002-45da-994e-2a86ae43b14f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a14a63ac-ec23-4058-bf5a-8e34a37edcbb</processItemId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.44276523-dd36-46b2-ab69-67017c215f69</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd</guid>
            <versionId>06f17d95-bfb0-45f6-89ab-43b57579e18e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.44276523-dd36-46b2-ab69-67017c215f69</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2f186ac2-676b-4e38-ba14-7084f537ca23</guid>
                <versionId>fcf07848-6a28-400d-b254-613ab8c92c81</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.662c8c47-eb73-4324-9c84-144a9472f428</processItemId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bf</guid>
            <versionId>8b8f6ffc-134f-4cec-9ebc-af8109d3147f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="325" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e35</errorHandlerItem>
                <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3344e57d-7625-4d20-b894-d238839dcac3</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>f04598ae-3591-413f-bf5d-0b7f3aa197ad</guid>
                <versionId>4ae164e2-0a90-44ad-b806-fbdb83b57f63</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.441a6d9c-4a9e-46b5-bd95-006f8fa22e7d</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b1ea9d62-b1ec-4e97-8c83-9def01156504</guid>
                    <versionId>2fba5052-842f-4fcd-b715-e18432c01bf3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d6bf204d-ef29-4873-9d11-811d51a926a6</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.tempresult</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>612336c8-7aff-406f-8539-7b7e7e10c520</guid>
                    <versionId>6692aca5-d0e0-46c6-a160-09a68859ec8e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29a7e2b6-3e30-41df-b696-6418a57bbb55</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Record"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5af5a9e4-95da-48f4-a947-943cfaef3171</guid>
                    <versionId>88c513ac-7fe0-4302-8c23-78f9dcd79547</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.914cd755-**************-a4c4bf7bc0d4</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e35b18a5-2862-4dce-a8d3-e4c049c7b16f</guid>
                    <versionId>9090d3c4-91a1-416f-836a-94818fbcfb7e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1c4b1f61-a96f-46e5-85f4-f0ee559d4abe</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>5db44747-5a58-46e2-8c6f-1fd014a73c6c</guid>
                    <versionId>9fe502c1-8571-45bb-a231-946be594acd4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.34178fd1-de5c-4e71-8d7e-6cf691d8a794</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.3344e57d-7625-4d20-b894-d238839dcac3</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>52be2ac0-7ecc-4d63-bbce-f17c717b5674</guid>
                    <versionId>dd6d4899-2203-4944-b9e4-4d8bca68ebc7</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</processItemId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ae0c921b-2928-4215-9ebf-01a5904077d2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e35</guid>
            <versionId>be6470fe-19f5-429c-b458-d8817b25e3d4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="341" y="202">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ae0c921b-2928-4215-9ebf-01a5904077d2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>66fb1175-49f7-4433-8d98-217bffb7fb40</guid>
                <versionId>d1cb8cff-734f-45bb-8a46-baf6dac19242</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</processItemId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <name>Set SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6a4bddc8-43fd-4cbc-a8cf-38cad93de1dd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bc</guid>
            <versionId>cbd9669e-7665-4fc3-b954-27d238ee0ba7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2cac4770-2320-403f-ad68-112ac425f15b</processItemPrePostId>
                <processItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>9bc9ff79-a036-4935-ba7f-3f3a1074135f</guid>
                <versionId>d00c2b5d-3ab7-4a36-835d-ef635a1b4229</versionId>
            </processPrePosts>
            <layoutData x="139" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e35</errorHandlerItem>
                <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6a4bddc8-43fd-4cbc-a8cf-38cad93de1dd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "SELECT ID FROM BPM.IDC_NBE_CUSTOMER_SANCTIONS where CIF_NUMBER = ?";&#xD;
&#xD;
tw.local.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]= new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].type="VARCHAR";&#xD;
tw.local.parameters[0].value = tw.local.cif;
</script>
                <isRule>false</isRule>
                <guid>0c6dfe69-520e-4f7c-8fc3-eef13ce3d04d</guid>
                <versionId>2cc38091-9937-4479-b297-fc87dfd2da95</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e677dc81-7aef-49cc-b444-ff06c47f8581</processItemId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <name>Set Result</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.00b139bd-f4d3-4def-b400-da06526e54dd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61be</guid>
            <versionId>ff883241-66b3-4bd8-8d4e-e4c0a04fbae1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="471" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e35</errorHandlerItem>
                <errorHandlerItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.00b139bd-f4d3-4def-b400-da06526e54dd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.tempresult != null) {&#xD;
	if (tw.local.tempresult.listLength&gt;0) {&#xD;
	tw.local.result = "YES";&#xD;
	}else{&#xD;
		tw.local.result = "NO";&#xD;
	}&#xD;
}&#xD;
else{&#xD;
	tw.local.result = "NO";&#xD;
}</script>
                <isRule>false</isRule>
                <guid>4872080d-86e9-4c2c-bfb5-7f0d144d8035</guid>
                <versionId>7bd125a4-0ae1-4c2e-bfef-2ba6b046ac38</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get CBE Sanctions" id="1.cc39766e-740a-4aae-91d4-983d3f72c00c" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="cif" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b7ca2766-fba7-4fca-87a1-873a04b2291c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"12345678"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="result" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e55e3035-c733-4b86-8251-e07e25aafcb2" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.b7ca2766-fba7-4fca-87a1-873a04b2291c</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.e55e3035-c733-4b86-8251-e07e25aafcb2</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="bb7c2437-7540-4309-8420-f6a2ff8384ab">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2471700b-46df-414e-b6f5-1c8b169844ef" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9accbfb8-1a69-4d22-9df4-796f93063559</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a14a63ac-ec23-4058-bf5a-8e34a37edcbb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>662c8c47-eb73-4324-9c84-144a9472f428</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8616db69-c420-4f27-ac49-e04a71b8811a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e677dc81-7aef-49cc-b444-ff06c47f8581</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>34bdedb3-e67f-4a80-8ca6-645c8bc8bd22</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>baef34cc-ba39-4b21-8e0f-ea7d13b91f2f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8750c421-6358-4742-8616-6864d59c24c1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9accbfb8-1a69-4d22-9df4-796f93063559">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="a14a63ac-ec23-4058-bf5a-8e34a37edcbb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>283c7017-b0c8-4189-b71e-0fc7598fbd30</ns16:incoming>
                        
                        
                        <ns16:incoming>3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9accbfb8-1a69-4d22-9df4-796f93063559" targetRef="8616db69-c420-4f27-ac49-e04a71b8811a" name="To Set SQL" id="2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="662c8c47-eb73-4324-9c84-144a9472f428">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="325" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>521102e6-e36a-4e81-876e-4a16b8e40f51</ns16:incoming>
                        
                        
                        <ns16:outgoing>bfaf6366-9504-4882-923a-2633540ffa08</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Record"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">tw.local.tempresult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="662c8c47-eb73-4324-9c84-144a9472f428" targetRef="e677dc81-7aef-49cc-b444-ff06c47f8581" name="To Set Result" id="bfaf6366-9504-4882-923a-2633540ffa08">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set SQL" id="8616db69-c420-4f27-ac49-e04a71b8811a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="139" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.d37dc8f4-fb90-48f8-bdb3-3cacbafc68a0</ns16:incoming>
                        
                        
                        <ns16:outgoing>521102e6-e36a-4e81-876e-4a16b8e40f51</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "SELECT ID FROM BPM.IDC_NBE_CUSTOMER_SANCTIONS where CIF_NUMBER = ?";&#xD;
&#xD;
tw.local.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]= new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].type="VARCHAR";&#xD;
tw.local.parameters[0].value = tw.local.cif;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8616db69-c420-4f27-ac49-e04a71b8811a" targetRef="662c8c47-eb73-4324-9c84-144a9472f428" name="To SQL Execute Statement" id="521102e6-e36a-4e81-876e-4a16b8e40f51">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.414a0fd9-9abe-45f3-855f-bf6d91691c0b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" name="tempresult" id="2056.95866585-7a5e-460f-a2cc-1a39f217d8da" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.332a3373-82e6-42c1-b3b1-5f1bee522b6a" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Result" id="e677dc81-7aef-49cc-b444-ff06c47f8581">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="471" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bfaf6366-9504-4882-923a-2633540ffa08</ns16:incoming>
                        
                        
                        <ns16:outgoing>283c7017-b0c8-4189-b71e-0fc7598fbd30</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.tempresult != null) {&#xD;
	if (tw.local.tempresult.listLength&gt;0) {&#xD;
	tw.local.result = "YES";&#xD;
	}else{&#xD;
		tw.local.result = "NO";&#xD;
	}&#xD;
}&#xD;
else{&#xD;
	tw.local.result = "NO";&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e677dc81-7aef-49cc-b444-ff06c47f8581" targetRef="a14a63ac-ec23-4058-bf5a-8e34a37edcbb" name="To End" id="283c7017-b0c8-4189-b71e-0fc7598fbd30">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8616db69-c420-4f27-ac49-e04a71b8811a" parallelMultiple="false" name="Error" id="34bdedb3-e67f-4a80-8ca6-645c8bc8bd22">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="174" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>268d1a06-b93c-4b25-8c35-ec07e2c74b8f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f0da7b0f-a312-45b0-89f2-70f48170c817" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ddab7db8-8566-437f-85c6-a09a4a98e5b4" eventImplId="3c09eec6-0065-4aed-8fbe-e08792b65fda">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="662c8c47-eb73-4324-9c84-144a9472f428" parallelMultiple="false" name="Error1" id="baef34cc-ba39-4b21-8e0f-ea7d13b91f2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="360" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e1a7cf3e-b343-4b60-89f6-d3d6cc33778d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="66b30a96-452e-4260-8da6-d15c8a79ae85" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0561ec5d-6c2a-4b46-8e1d-f3ae5e6f5318" eventImplId="e7c53e0f-ebdd-44a4-8cdf-7a8190190c6e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e677dc81-7aef-49cc-b444-ff06c47f8581" parallelMultiple="false" name="Error2" id="8750c421-6358-4742-8616-6864d59c24c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="506" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>59ff4658-8b33-483d-8710-f7ab3e543564</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="690dd4d5-6ff8-4da6-8553-1e0cce83ab35" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="103d679b-dad2-40a4-8f26-e1eb2aef079e" eventImplId="845add23-89f7-424e-82d3-b3be452e8a3c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c654ab1b-7071-4ac2-8289-34691ae169d0" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="341" y="202" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>268d1a06-b93c-4b25-8c35-ec07e2c74b8f</ns16:incoming>
                        
                        
                        <ns16:incoming>e1a7cf3e-b343-4b60-89f6-d3d6cc33778d</ns16:incoming>
                        
                        
                        <ns16:incoming>59ff4658-8b33-483d-8710-f7ab3e543564</ns16:incoming>
                        
                        
                        <ns16:outgoing>3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="34bdedb3-e67f-4a80-8ca6-645c8bc8bd22" targetRef="e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c" name="To Catch Errors" id="268d1a06-b93c-4b25-8c35-ec07e2c74b8f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="baef34cc-ba39-4b21-8e0f-ea7d13b91f2f" targetRef="e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c" name="To Catch Errors" id="e1a7cf3e-b343-4b60-89f6-d3d6cc33778d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8750c421-6358-4742-8616-6864d59c24c1" targetRef="e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c" name="To Catch Errors" id="59ff4658-8b33-483d-8710-f7ab3e543564">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c" targetRef="a14a63ac-ec23-4058-bf5a-8e34a37edcbb" name="To End" id="3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.d81557d3-dc52-42e0-84a8-a961a487a798" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bfaf6366-9504-4882-923a-2633540ffa08</processLinkId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.662c8c47-eb73-4324-9c84-144a9472f428</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.e677dc81-7aef-49cc-b444-ff06c47f8581</toProcessItemId>
            <guid>5aa99273-2aec-4ff9-8c96-9a1b7d110b25</guid>
            <versionId>81c8d7b1-1827-4041-b897-e1b6cae86fa3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.662c8c47-eb73-4324-9c84-144a9472f428</fromProcessItemId>
            <toProcessItemId>2025.e677dc81-7aef-49cc-b444-ff06c47f8581</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.283c7017-b0c8-4189-b71e-0fc7598fbd30</processLinkId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e677dc81-7aef-49cc-b444-ff06c47f8581</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a14a63ac-ec23-4058-bf5a-8e34a37edcbb</toProcessItemId>
            <guid>2b3a8e98-de7f-4fcc-b630-be0bd67264b6</guid>
            <versionId>b8c8d972-a871-4d64-8f6c-051e5d808f5f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e677dc81-7aef-49cc-b444-ff06c47f8581</fromProcessItemId>
            <toProcessItemId>2025.a14a63ac-ec23-4058-bf5a-8e34a37edcbb</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3fe2dc0d-fcb3-43fa-8e4d-ae1a04ad906c</processLinkId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a14a63ac-ec23-4058-bf5a-8e34a37edcbb</toProcessItemId>
            <guid>56d563b3-765c-498f-b458-24af7bf4c146</guid>
            <versionId>f42a4163-619d-4f7f-9270-981d99d09fae</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.e9c6c01f-b6c0-4c2b-82cc-d0db8231e60c</fromProcessItemId>
            <toProcessItemId>2025.a14a63ac-ec23-4058-bf5a-8e34a37edcbb</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.521102e6-e36a-4e81-876e-4a16b8e40f51</processLinkId>
            <processId>1.cc39766e-740a-4aae-91d4-983d3f72c00c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.662c8c47-eb73-4324-9c84-144a9472f428</toProcessItemId>
            <guid>d05bca59-a4db-46d8-ab5f-a69260219b97</guid>
            <versionId>fdbbaf4a-7093-42e4-89c3-afa33008cb07</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8616db69-c420-4f27-ac49-e04a71b8811a</fromProcessItemId>
            <toProcessItemId>2025.662c8c47-eb73-4324-9c84-144a9472f428</toProcessItemId>
        </link>
    </process>
</teamworks>

