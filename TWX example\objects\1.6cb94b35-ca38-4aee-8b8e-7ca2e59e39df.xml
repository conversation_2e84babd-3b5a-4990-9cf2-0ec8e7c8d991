<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df" name="Get Facility Codes">
        <lastModified>1692506704929</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>39a116fa-cb45-4f2c-a482-62bd4558c794</guid>
        <versionId>ec61280d-a56e-4d5b-ab3c-a1f96ccb1fed</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e83" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f85f2606-1195-41e6-b3c0-212cd94f939f"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":77,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"64a02690-5e4c-4983-8648-ec10b589c270"},{"incoming":["20f11337-97a3-4d64-8a05-4adab1d5fdf8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":654,"y":33,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-613c"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c6ace50c-4429-4c75-80c8-7cb8dc814abd"},{"targetRef":"020191d9-9f66-4cc0-8ffc-5ec5e62d050d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Customer Facilities","declaredType":"sequenceFlow","id":"2027.f85f2606-1195-41e6-b3c0-212cd94f939f","sourceRef":"64a02690-5e4c-4983-8648-ec10b589c270"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.33b471cb-58c9-4d71-b603-5c3aa8c2cdd6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.0bfd8604-bee5-4564-9ce1-9f08b8fc0d3c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.9e5fe860-fb7a-4db7-8c5b-76b2b7bf3328"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.9a50fb67-c845-43b1-82a3-a80fafcaeb48"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.0b4a52ad-42d8-4fb7-95c4-f9edcefb37a1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.74c9854a-9a6f-4c4f-b696-4da325b6bca4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.04cb084e-965c-46bd-bf26-6cab7ae9038c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.db841e46-a77a-4fc9-abe8-1d15409ebd29"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.49e33b31-16bb-496b-b63b-f8e9f68ff214"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"declaredType":"dataObject","id":"2056.2d312ee3-018d-4601-a97d-464ce8de3eaf"},{"startQuantity":1,"outgoing":["20f11337-97a3-4d64-8a05-4adab1d5fdf8"],"incoming":["a307f6f2-dbb0-408a-8a63-f2a5540a3b4b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":484,"y":30,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Mapping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f2856051-ea0a-4c7f-8e1d-094d057eb0a2","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.listOf.String();\r\n\tif (tw.local.facilityLinesList == null || tw.local.facilityLinesList == undefined) {\r\n\t\ttw.local.results[0] = \"No Facilities Found\"\r\n\t}else{\r\n\t\tfor (var i=0; i&lt;tw.local.facilityLinesList.listLength; i++) {\r\n\t\ttw.local.results[i] = tw.local.facilityLinesList[i].LineCode;\r\n\t\t}\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"targetRef":"c6ace50c-4429-4c75-80c8-7cb8dc814abd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Facility Codes","declaredType":"sequenceFlow","id":"20f11337-97a3-4d64-8a05-4adab1d5fdf8","sourceRef":"f2856051-ea0a-4c7f-8e1d-094d057eb0a2"},{"startQuantity":1,"outgoing":["f3a11bd4-0f05-4435-8267-61fdac51c142"],"incoming":["2027.f85f2606-1195-41e6-b3c0-212cd94f939f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":54,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get Customer Facilities","dataInputAssociation":[{"targetRef":"2055.f763d984-addb-4571-955b-8e971e341929","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.153ad71c-109d-42cf-9547-007c43173e21","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.prefix"]}}]},{"targetRef":"2055.c6d042f1-4a04-40a0-891b-0870bd97b9dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.userID"]}}]},{"targetRef":"2055.026aed72-dc79-433b-82db-6bc4beb7e819","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceID"]}}]},{"targetRef":"2055.b51311d9-7817-4be5-b14b-70e0bd00afa3","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.processName"]}}]},{"targetRef":"2055.0d3815a3-03b1-4d4b-9e35-dd5f4579fe82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestAppID"]}}]},{"targetRef":"2055.6f2410fb-3f74-47cd-a2cd-9636ee2b5c65","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.snapshot"]}}]},{"targetRef":"2055.793b1189-327b-4e4b-80c5-077e024912cc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"020191d9-9f66-4cc0-8ffc-5ec5e62d050d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.6c68b590-b40f-4073-b08d-5ca9ce230347"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.status"]}}],"sourceRef":["2055.4dc04faf-1f1f-44da-b60b-0907bc5ed7b0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.a2321bda-7d31-4f43-b67e-d12a6fea5c8c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8","declaredType":"TFormalExpression","content":["tw.local.facilityLinesList"]}}],"sourceRef":["2055.cef85a89-c2b9-4551-8dfb-ab433145eabc"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.8e049310-6ad5-4592-b504-************"]}],"calledElement":"1.227fc1e1-f5da-4cda-8351-306a95eb9aa7"},{"targetRef":"1c75acfc-732f-4d14-87ec-1b113da65d52","extensionElements":{"endStateId":["guid:d06bc5264f3bac58:1a63dcc9:18920913403:-1cea"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"f3a11bd4-0f05-4435-8267-61fdac51c142","sourceRef":"020191d9-9f66-4cc0-8ffc-5ec5e62d050d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEINTT.FacilityLineCodes();\nautoObject[0] = new tw.object.toolkit.NBEINTT.FacilityLineCodes();\nautoObject[0].LiabNo = \"\";\nautoObject[0].LineCode = \"\";\nautoObject[0].LineSerial = \"\";\nautoObject[0].LimitAmount = 0.0;\nautoObject[0].AvailableAmount = 0.0;\nautoObject[0].Utilisation = 0.0;\nautoObject[0].PurposeUDF = \"\";\nautoObject[0].LGCommUDF = \"\";\nautoObject[0].LGBidBondUDF = \"\";\nautoObject[0].LGPerformaceUDF = \"\";\nautoObject[0].LGAdvacedPayment = \"\";\nautoObject[0].LCComm = \"\";\nautoObject[0].LCDefAccComm = \"\";\nautoObject[0].LCCashCover = \"\";\nautoObject[0].IdcComm = \"\";\nautoObject[0].IdcAvalComm = \"\";\nautoObject[0].IdcCashCover = \"\";\nautoObject[0].DebitAccNum = \"\";\nautoObject[0].FacilityID = \"\";\nautoObject[0].MainLineId = \"\";\nautoObject[0].EffectiveLineAmount = 0.0;\nautoObject[0].ExpiryDate = \"\";\nautoObject[0].FacilityBranch = \"\";\nautoObject[0].InternalRemarks = \"\";\nautoObject[0].Currency = \"\";\nautoObject[0].aviabilityFlag = \"\";\nautoObject[0].currencyRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCurrencyRestriction();\nautoObject[0].currencyRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCurrencyRestriction();\nautoObject[0].currencyRestricted[0].CCY = \"\";\nautoObject[0].currencyRestricted[0].ID = 0.0;\nautoObject[0].branchRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityBranchRestriction();\nautoObject[0].branchRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityBranchRestriction();\nautoObject[0].branchRestricted[0].BRANCHCODE = \"\";\nautoObject[0].branchRestricted[0].ID = 0.0;\nautoObject[0].customerRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCustomerRestriction();\nautoObject[0].customerRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCustomerRestriction();\nautoObject[0].customerRestricted[0].CUSTOMERNO = \"\";\nautoObject[0].customerRestricted[0].ID = 0.0;\nautoObject[0].productRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityProductRestriction();\nautoObject[0].productRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityProductRestriction();\nautoObject[0].productRestricted[0].PRODUCTCODE = \"\";\nautoObject[0].productRestricted[0].ID = 0.0;\nautoObject[0].mainLineFlag = false;\nautoObject"}]},"itemSubjectRef":"itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8","name":"facilityLinesList","isCollection":true,"declaredType":"dataObject","id":"2056.379b8a74-583b-468b-80be-759ea47d26b9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"declaredType":"dataObject","id":"2056.b6c1b141-b1cd-4ad0-84d9-bfc7f9751758"},{"parallelMultiple":false,"outgoing":["53c5e331-4d76-4450-8a59-c649881bfffe"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8c5f0700-60a4-43f7-8e7f-d2e53fc3c982"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c55f5b0d-86b6-42fe-8ba6-1550a57daf90","otherAttributes":{"eventImplId":"127b2ab9-5e68-49d2-89e3-bd6b35f54f24"}}],"attachedToRef":"020191d9-9f66-4cc0-8ffc-5ec5e62d050d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":112,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"03b1ff9f-fa64-4198-8b1f-d4d9174562bf","outputSet":{}},{"parallelMultiple":false,"outgoing":["889e1a75-6d28-40db-8e5f-b8370617492a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e39db912-ae6e-447b-830b-8d1ddabd056f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e2856df9-a14b-4c44-801b-fcc79f7d903c","otherAttributes":{"eventImplId":"b8e0060f-64bd-4486-8431-09b050d21460"}}],"attachedToRef":"f2856051-ea0a-4c7f-8e1d-094d057eb0a2","extensionElements":{"nodeVisualInfo":[{"width":24,"x":519,"y":88,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"7ee8d454-4774-4ad2-8ab8-960c70002005","outputSet":{}},{"incoming":["53c5e331-4d76-4450-8a59-c649881bfffe","889e1a75-6d28-40db-8e5f-b8370617492a","0d5fffec-3959-4f41-85ce-58833aef8eec"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"dbe8f8f2-5f4f-4a94-809f-a9088615a9b3","otherAttributes":{"eventImplId":"6f693dd3-0ff7-4dcd-81f5-6062c9c73d2f"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":347,"y":222,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Get Facility Codes -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\n\/\/var attribute = String(tw.system.error.getAttribute(\"type\"));\r\n\/\/var element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n\/\/tw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Get Facility Codes -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"ef86b0ef-32d3-4029-8de9-c3018164f7dc"},{"targetRef":"ef86b0ef-32d3-4029-8de9-c3018164f7dc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"53c5e331-4d76-4450-8a59-c649881bfffe","sourceRef":"03b1ff9f-fa64-4198-8b1f-d4d9174562bf"},{"targetRef":"ef86b0ef-32d3-4029-8de9-c3018164f7dc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"889e1a75-6d28-40db-8e5f-b8370617492a","sourceRef":"7ee8d454-4774-4ad2-8ab8-960c70002005"},{"outgoing":["a307f6f2-dbb0-408a-8a63-f2a5540a3b4b","0d5fffec-3959-4f41-85ce-58833aef8eec"],"incoming":["f3a11bd4-0f05-4435-8267-61fdac51c142"],"default":"a307f6f2-dbb0-408a-8a63-f2a5540a3b4b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":349,"y":94,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"1c75acfc-732f-4d14-87ec-1b113da65d52"},{"targetRef":"f2856051-ea0a-4c7f-8e1d-094d057eb0a2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Mapping","declaredType":"sequenceFlow","id":"a307f6f2-dbb0-408a-8a63-f2a5540a3b4b","sourceRef":"1c75acfc-732f-4d14-87ec-1b113da65d52"},{"targetRef":"ef86b0ef-32d3-4029-8de9-c3018164f7dc","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0d5fffec-3959-4f41-85ce-58833aef8eec","sourceRef":"1c75acfc-732f-4d14-87ec-1b113da65d52"}],"laneSet":[{"id":"5fdde0d9-5e70-44d3-9bef-6596dd6b1036","lane":[{"flowNodeRef":["64a02690-5e4c-4983-8648-ec10b589c270","c6ace50c-4429-4c75-80c8-7cb8dc814abd","f2856051-ea0a-4c7f-8e1d-094d057eb0a2","020191d9-9f66-4cc0-8ffc-5ec5e62d050d","03b1ff9f-fa64-4198-8b1f-d4d9174562bf","7ee8d454-4774-4ad2-8ab8-960c70002005","ef86b0ef-32d3-4029-8de9-c3018164f7dc","1c75acfc-732f-4d14-87ec-1b113da65d52"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":299}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"dfc6e8ef-1f74-4ea3-b6fd-b110d521d9f9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Facility Codes","declaredType":"process","id":"1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.797f4c60-d291-484f-915d-3e2f699861f9"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.822ba1d8-6cfc-4ebc-8a49-7e694afd79f8"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.797f4c60-d291-484f-915d-3e2f699861f9","2055.822ba1d8-6cfc-4ebc-8a49-7e694afd79f8"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"02366014\"\r\n\"02922318\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.992a4f7b-5943-4840-9771-628cca0cedc3"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.992a4f7b-5943-4840-9771-628cca0cedc3</processParameterId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"02366014"&#xD;
"02922318"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e4edd0cc-ac7a-4c1d-9d70-96ce018e80df</guid>
            <versionId>44f5b450-704a-4de8-a6f0-79f3e688a014</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.797f4c60-d291-484f-915d-3e2f699861f9</processParameterId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>473c1a0b-9d6b-4fba-a60a-a15179167705</guid>
            <versionId>29234c39-d5af-476c-947b-cebace0235da</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.822ba1d8-6cfc-4ebc-8a49-7e694afd79f8</processParameterId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d661ca12-3988-44b3-962e-acf1fbe52cff</guid>
            <versionId>0b72d635-fe70-4d9f-ab4e-a6fc0c7c111d</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fe70cde3-29fb-430b-8d9e-8c9a33ef9356</processParameterId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8e4d2b1b-0f31-40ef-ac67-c85e10123a2c</guid>
            <versionId>2b1e3fa8-08f0-4b3f-b335-e3d3c76820a6</versionId>
        </processParameter>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.33b471cb-58c9-4d71-b603-5c3aa8c2cdd6</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bdc80f4c-b1a6-4301-b1cf-89b718e8c003</guid>
            <versionId>82821d7c-d713-432e-89f7-d2d0baf972cc</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0bfd8604-bee5-4564-9ce1-9f08b8fc0d3c</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a1a3de12-3a41-47fa-922a-8f405ec8c65b</guid>
            <versionId>bc77621d-3a55-492c-a001-c31cc3176782</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9e5fe860-fb7a-4db7-8c5b-76b2b7bf3328</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a6049844-01c1-4257-acb8-7c964a4f18b6</guid>
            <versionId>3b5e512a-5e49-4a78-83f8-ce6b8ab0d7b2</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a50fb67-c845-43b1-82a3-a80fafcaeb48</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc32e6ba-fa59-4b2a-93a0-19c46af0bba9</guid>
            <versionId>27492bf5-b547-4b61-a5ae-30c2405eedcf</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0b4a52ad-42d8-4fb7-95c4-f9edcefb37a1</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>13331823-8ce0-4421-abeb-4ad6bdace73c</guid>
            <versionId>9f1a869a-a616-48d5-9c3c-79457d84e43e</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.74c9854a-9a6f-4c4f-b696-4da325b6bca4</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dd23466c-74f7-40b1-a23a-ca13a5c595f3</guid>
            <versionId>ccb7f5c5-5fee-4838-a061-18379f070bbb</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.04cb084e-965c-46bd-bf26-6cab7ae9038c</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>560e7a21-ee37-461b-ad12-e5430493e1d3</guid>
            <versionId>581a905e-5944-4615-9f15-7d9f860fe98a</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.db841e46-a77a-4fc9-abe8-1d15409ebd29</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c4c0920b-217d-4399-bf24-7a28d8de813f</guid>
            <versionId>6a1be49f-0154-4dad-99e0-f854d17514c4</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.49e33b31-16bb-496b-b63b-f8e9f68ff214</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9f7d71a5-90f2-4af2-945e-1cf1241ee17e</guid>
            <versionId>49f670c0-f11c-4778-acc1-15af15a35a12</versionId>
        </processVariable>
        <processVariable name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2d312ee3-018d-4601-a97d-464ce8de3eaf</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8f2686b1-d74b-450e-9411-a780680ae3ee</guid>
            <versionId>edea763e-e12d-4233-a58c-fd15988db6a4</versionId>
        </processVariable>
        <processVariable name="facilityLinesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.379b8a74-583b-468b-80be-759ea47d26b9</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.1c20960f-48f5-49c7-83aa-b503d27100c8</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEINTT.FacilityLineCodes();
autoObject[0] = new tw.object.toolkit.NBEINTT.FacilityLineCodes();
autoObject[0].LiabNo = "";
autoObject[0].LineCode = "";
autoObject[0].LineSerial = "";
autoObject[0].LimitAmount = 0.0;
autoObject[0].AvailableAmount = 0.0;
autoObject[0].Utilisation = 0.0;
autoObject[0].PurposeUDF = "";
autoObject[0].LGCommUDF = "";
autoObject[0].LGBidBondUDF = "";
autoObject[0].LGPerformaceUDF = "";
autoObject[0].LGAdvacedPayment = "";
autoObject[0].LCComm = "";
autoObject[0].LCDefAccComm = "";
autoObject[0].LCCashCover = "";
autoObject[0].IdcComm = "";
autoObject[0].IdcAvalComm = "";
autoObject[0].IdcCashCover = "";
autoObject[0].DebitAccNum = "";
autoObject[0].FacilityID = "";
autoObject[0].MainLineId = "";
autoObject[0].EffectiveLineAmount = 0.0;
autoObject[0].ExpiryDate = "";
autoObject[0].FacilityBranch = "";
autoObject[0].InternalRemarks = "";
autoObject[0].Currency = "";
autoObject[0].aviabilityFlag = "";
autoObject[0].currencyRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCurrencyRestriction();
autoObject[0].currencyRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCurrencyRestriction();
autoObject[0].currencyRestricted[0].CCY = "";
autoObject[0].currencyRestricted[0].ID = 0.0;
autoObject[0].branchRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityBranchRestriction();
autoObject[0].branchRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityBranchRestriction();
autoObject[0].branchRestricted[0].BRANCHCODE = "";
autoObject[0].branchRestricted[0].ID = 0.0;
autoObject[0].customerRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCustomerRestriction();
autoObject[0].customerRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCustomerRestriction();
autoObject[0].customerRestricted[0].CUSTOMERNO = "";
autoObject[0].customerRestricted[0].ID = 0.0;
autoObject[0].productRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityProductRestriction();
autoObject[0].productRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityProductRestriction();
autoObject[0].productRestricted[0].PRODUCTCODE = "";
autoObject[0].productRestricted[0].ID = 0.0;
autoObject[0].mainLineFlag = false;
autoObject</defaultValue>
            <guid>a01e9700-eb31-430f-a458-ebec8e832529</guid>
            <versionId>6168124b-0ed8-4740-9660-d40779e62386</versionId>
        </processVariable>
        <processVariable name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b6c1b141-b1cd-4ad0-84d9-bfc7f9751758</processVariableId>
            <description isNull="true" />
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8855a57-f5c2-4f7c-8406-d859918b94cf</guid>
            <versionId>c7c6b784-8152-4886-bca0-b93af5cbf4c2</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</processItemId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.0f733cf4-7d23-4da4-93d2-13d47f1b5750</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:42738960b3d81f85:8740bc4:189d9444efc:-30a0</guid>
            <versionId>0d9fa19e-b842-483c-9619-fab3dc34b550</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.88174ced-041c-43c6-8816-0ad0aeb29a49</processItemPrePostId>
                <processItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Get Facility Codes -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Facility Codes -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>112bb5a9-2715-4ca5-838a-1f97228880d0</guid>
                <versionId>b366e0ca-44e6-407b-845a-7603e7d89236</versionId>
            </processPrePosts>
            <layoutData x="347" y="222">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.0f733cf4-7d23-4da4-93d2-13d47f1b5750</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>02fc1750-51fb-4a9b-b81e-cfc4b2665987</guid>
                <versionId>c60578bf-5c12-4fb0-a9cb-87a813629c52</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.53f06f2e-e5b8-4325-a641-6bf7c5539a90</parameterMappingId>
                    <processParameterId>2055.fe70cde3-29fb-430b-8d9e-8c9a33ef9356</processParameterId>
                    <parameterMappingParentId>3007.0f733cf4-7d23-4da4-93d2-13d47f1b5750</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4e9e43c3-0c89-4f5d-8c10-fe6a630229eb</guid>
                    <versionId>3fa333e6-3c34-4892-9899-1163b784023d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c6ace50c-4429-4c75-80c8-7cb8dc814abd</processItemId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.28345dfc-325b-4683-81cd-c5fc25fdbe4d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-613c</guid>
            <versionId>95574ffe-e9db-4f39-843f-068a4de5ebeb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="654" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.28345dfc-325b-4683-81cd-c5fc25fdbe4d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>abf93fc3-8b21-4946-a40e-1bf627b4c0c5</guid>
                <versionId>b2c526b2-a25e-4121-babd-f32ab3408a0e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f2856051-ea0a-4c7f-8e1d-094d057eb0a2</processItemId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <name>Mapping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b1b29cfd-93ce-4459-a090-dac3f3f1cd47</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18949205b68:7d3e</guid>
            <versionId>b730e70f-c25c-4713-ae43-071688de123c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="484" y="30">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d9444efc:-30a0</errorHandlerItem>
                <errorHandlerItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b1b29cfd-93ce-4459-a090-dac3f3f1cd47</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
	if (tw.local.facilityLinesList == null || tw.local.facilityLinesList == undefined) {&#xD;
		tw.local.results[0] = "No Facilities Found"&#xD;
	}else{&#xD;
		for (var i=0; i&lt;tw.local.facilityLinesList.listLength; i++) {&#xD;
		tw.local.results[i] = tw.local.facilityLinesList[i].LineCode;&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>3957dd8a-9ed5-4855-9d73-bc6c3258a3e6</guid>
                <versionId>198b6850-aa77-4577-b021-67f1a3d9a61d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</processItemId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <name>Get Customer Facilities</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18949205b68:7d3f</guid>
            <versionId>dfcf3377-0e7a-4399-af64-bd56ef47d629</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.62add787-a812-4a38-be66-ddd229fcd120</processItemPrePostId>
                <processItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>da490019-afb5-48d6-ba96-9ecd798b0ab4</guid>
                <versionId>427d3285-ae7c-4b69-8ead-6a80e5050862</versionId>
            </processPrePosts>
            <layoutData x="177" y="54">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d9444efc:-30a0</errorHandlerItem>
                <errorHandlerItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.227fc1e1-f5da-4cda-8351-306a95eb9aa7</attachedProcessRef>
                <guid>0b7abdcd-debf-4a68-9eff-05f838deac1b</guid>
                <versionId>f39402ff-6c29-4b5e-949e-005162d2c8c4</versionId>
                <parameterMapping name="facilityLinesList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7392c31a-0d29-4e0e-8932-135bb4c42968</parameterMappingId>
                    <processParameterId>2055.cef85a89-c2b9-4551-8dfb-ab433145eabc</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.facilityLinesList</value>
                    <classRef>/12.1c20960f-48f5-49c7-83aa-b503d27100c8</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>f3778c08-ceaf-45e7-8952-cf131d3138e1</guid>
                    <versionId>12ad28b6-ff03-4c7e-b774-9d6758319008</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6837e2d2-bbce-47b9-bc1e-0d1098b32df1</parameterMappingId>
                    <processParameterId>2055.a2321bda-7d31-4f43-b67e-d12a6fea5c8c</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b27afbba-e7ac-4ec7-8543-10d206bc0d78</guid>
                    <versionId>172fa396-ae4b-4b90-b7cd-835f6adee9b6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef1db372-3b58-4175-8990-691373a301f0</parameterMappingId>
                    <processParameterId>2055.f763d984-addb-4571-955b-8e971e341929</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3fde4cc3-6e8f-4613-b2e2-487adec211ea</guid>
                    <versionId>174071a2-b628-453e-91a3-cefa14b26f76</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e03f68b4-b6d1-40c6-8aca-b30289aab4f7</parameterMappingId>
                    <processParameterId>2055.6f2410fb-3f74-47cd-a2cd-9636ee2b5c65</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.snapshot</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cfbb9f77-d7bc-4c83-9f2d-8cc21a423b9b</guid>
                    <versionId>2893e916-765f-4441-a300-e6b5a282f57d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.233f6d40-0ce0-4b6e-ae19-d12e502574a6</parameterMappingId>
                    <processParameterId>2055.026aed72-dc79-433b-82db-6bc4beb7e819</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>12db9bd0-a51b-4b43-8df2-33d008114ea2</guid>
                    <versionId>41bcd0e7-4375-4936-af56-09367938fa4b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d34a4852-bf79-4856-81b4-2b94d7dd0f43</parameterMappingId>
                    <processParameterId>2055.6c68b590-b40f-4073-b08d-5ca9ce230347</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e51f2c10-d8ff-4847-8edd-022b3044c8a0</guid>
                    <versionId>4f18c0aa-7e1e-4b10-baee-b347d092e4b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.eb38c390-42b9-4f97-a4d0-4d1cbbde013a</parameterMappingId>
                    <processParameterId>2055.0d3815a3-03b1-4d4b-9e35-dd5f4579fe82</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestAppID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>286900c4-df5d-4c2e-9cd4-40f6077cc85a</guid>
                    <versionId>7fc06c4d-9a7d-4d3c-a3a6-9e7a05c26979</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.64df97dc-7484-4e2f-8453-b26ad6d345c3</parameterMappingId>
                    <processParameterId>2055.4dc04faf-1f1f-44da-b60b-0907bc5ed7b0</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>fb23b5c7-255b-48af-96b6-7b9fc6f5cb6b</guid>
                    <versionId>8458bb03-e1fb-47f5-9e89-29066037b3b7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.50d3c6a8-4240-4053-97ae-e69be5870351</parameterMappingId>
                    <processParameterId>2055.793b1189-327b-4e4b-80c5-077e024912cc</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b52acde8-7b37-4785-b9d3-d4b9c2c9b3c8</guid>
                    <versionId>8f1fb33c-e9a3-43fa-8ba7-4021552a2e39</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a9db0d8e-2167-415a-b54b-d255eb5d3dec</parameterMappingId>
                    <processParameterId>2055.b51311d9-7817-4be5-b14b-70e0bd00afa3</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.processName</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7008a9b6-6738-40c5-a5b1-e6773791466e</guid>
                    <versionId>a5972f13-4eef-49c5-9906-758d200a3ed8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d711b05a-6067-4147-b7e1-f895b0a767c0</parameterMappingId>
                    <processParameterId>2055.8e049310-6ad5-4592-b504-************</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1a41b0f3-cb94-4a92-8a04-a85df082fd48</guid>
                    <versionId>ad429565-1474-4675-92c8-59e586edf12b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b11f1b27-67cb-4c09-b0a8-3b3f33de8e15</parameterMappingId>
                    <processParameterId>2055.153ad71c-109d-42cf-9547-007c43173e21</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bf9d1319-0063-4584-88ff-4a8325a20fc9</guid>
                    <versionId>bcdbe388-e72e-4cf0-b14b-578e0a36b202</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fbe74e17-a530-4206-89fb-8ff01aa18425</parameterMappingId>
                    <processParameterId>2055.c6d042f1-4a04-40a0-891b-0870bd97b9dc</processParameterId>
                    <parameterMappingParentId>3012.938886d2-9123-4faa-ae96-fb9fd3dec640</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.userID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>22cb6107-5a18-43d3-9425-95ca9ac74859</guid>
                    <versionId>ea66c77e-92a7-4327-be8e-cf2d07815bff</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</processItemId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.4cce86e1-68ca-4952-9a33-d0c1a0481c3b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-ef7</guid>
            <versionId>f1c07b18-db48-424e-8a50-91d6bfd993a4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="349" y="94">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.4cce86e1-68ca-4952-9a33-d0c1a0481c3b</switchId>
                <guid>ab7d0af0-5e10-4a02-8283-454787043ef3</guid>
                <versionId>b117ec27-bb3a-4a9d-b125-c786390d5940</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.79db97ae-2385-4f1c-a77f-cd35f6d36405</switchConditionId>
                    <switchId>3013.4cce86e1-68ca-4952-9a33-d0c1a0481c3b</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e82</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>c8353d28-9e03-45df-9075-09e2a370bd53</guid>
                    <versionId>40087c47-60b2-4ff2-a0f0-24671a4f833b</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Facility Codes" id="1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.992a4f7b-5943-4840-9771-628cca0cedc3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"02366014"&#xD;
"02922318"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.797f4c60-d291-484f-915d-3e2f699861f9" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.822ba1d8-6cfc-4ebc-8a49-7e694afd79f8" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.797f4c60-d291-484f-915d-3e2f699861f9</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.822ba1d8-6cfc-4ebc-8a49-7e694afd79f8</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5fdde0d9-5e70-44d3-9bef-6596dd6b1036">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="dfc6e8ef-1f74-4ea3-b6fd-b110d521d9f9" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="299" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>64a02690-5e4c-4983-8648-ec10b589c270</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c6ace50c-4429-4c75-80c8-7cb8dc814abd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f2856051-ea0a-4c7f-8e1d-094d057eb0a2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>020191d9-9f66-4cc0-8ffc-5ec5e62d050d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>03b1ff9f-fa64-4198-8b1f-d4d9174562bf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7ee8d454-4774-4ad2-8ab8-960c70002005</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ef86b0ef-32d3-4029-8de9-c3018164f7dc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1c75acfc-732f-4d14-87ec-1b113da65d52</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="64a02690-5e4c-4983-8648-ec10b589c270">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="77" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.f85f2606-1195-41e6-b3c0-212cd94f939f</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c6ace50c-4429-4c75-80c8-7cb8dc814abd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="654" y="33" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-613c</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>20f11337-97a3-4d64-8a05-4adab1d5fdf8</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="64a02690-5e4c-4983-8648-ec10b589c270" targetRef="020191d9-9f66-4cc0-8ffc-5ec5e62d050d" name="To Get Customer Facilities" id="2027.f85f2606-1195-41e6-b3c0-212cd94f939f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.33b471cb-58c9-4d71-b603-5c3aa8c2cdd6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.0bfd8604-bee5-4564-9ce1-9f08b8fc0d3c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.9e5fe860-fb7a-4db7-8c5b-76b2b7bf3328" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.9a50fb67-c845-43b1-82a3-a80fafcaeb48" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.0b4a52ad-42d8-4fb7-95c4-f9edcefb37a1" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.74c9854a-9a6f-4c4f-b696-4da325b6bca4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.04cb084e-965c-46bd-bf26-6cab7ae9038c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.db841e46-a77a-4fc9-abe8-1d15409ebd29" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.49e33b31-16bb-496b-b63b-f8e9f68ff214" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="status" id="2056.2d312ee3-018d-4601-a97d-464ce8de3eaf" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Mapping" id="f2856051-ea0a-4c7f-8e1d-094d057eb0a2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="484" y="30" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a307f6f2-dbb0-408a-8a63-f2a5540a3b4b</ns16:incoming>
                        
                        
                        <ns16:outgoing>20f11337-97a3-4d64-8a05-4adab1d5fdf8</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
	if (tw.local.facilityLinesList == null || tw.local.facilityLinesList == undefined) {&#xD;
		tw.local.results[0] = "No Facilities Found"&#xD;
	}else{&#xD;
		for (var i=0; i&lt;tw.local.facilityLinesList.listLength; i++) {&#xD;
		tw.local.results[i] = tw.local.facilityLinesList[i].LineCode;&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="f2856051-ea0a-4c7f-8e1d-094d057eb0a2" targetRef="c6ace50c-4429-4c75-80c8-7cb8dc814abd" name="To Get Facility Codes" id="20f11337-97a3-4d64-8a05-4adab1d5fdf8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.227fc1e1-f5da-4cda-8351-306a95eb9aa7" name="Get Customer Facilities" id="020191d9-9f66-4cc0-8ffc-5ec5e62d050d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="54" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.f85f2606-1195-41e6-b3c0-212cd94f939f</ns16:incoming>
                        
                        
                        <ns16:outgoing>f3a11bd4-0f05-4435-8267-61fdac51c142</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f763d984-addb-4571-955b-8e971e341929</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.153ad71c-109d-42cf-9547-007c43173e21</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c6d042f1-4a04-40a0-891b-0870bd97b9dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.userID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.026aed72-dc79-433b-82db-6bc4beb7e819</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b51311d9-7817-4be5-b14b-70e0bd00afa3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.processName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0d3815a3-03b1-4d4b-9e35-dd5f4579fe82</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestAppID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6f2410fb-3f74-47cd-a2cd-9636ee2b5c65</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.snapshot</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.793b1189-327b-4e4b-80c5-077e024912cc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.6c68b590-b40f-4073-b08d-5ca9ce230347</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4dc04faf-1f1f-44da-b60b-0907bc5ed7b0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a2321bda-7d31-4f43-b67e-d12a6fea5c8c</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.cef85a89-c2b9-4551-8dfb-ab433145eabc</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8">tw.local.facilityLinesList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.8e049310-6ad5-4592-b504-************</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="020191d9-9f66-4cc0-8ffc-5ec5e62d050d" targetRef="1c75acfc-732f-4d14-87ec-1b113da65d52" name="To Exclusive Gateway" id="f3a11bd4-0f05-4435-8267-61fdac51c142">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d06bc5264f3bac58:1a63dcc9:18920913403:-1cea</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8" isCollection="true" name="facilityLinesList" id="2056.379b8a74-583b-468b-80be-759ea47d26b9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEINTT.FacilityLineCodes();
autoObject[0] = new tw.object.toolkit.NBEINTT.FacilityLineCodes();
autoObject[0].LiabNo = "";
autoObject[0].LineCode = "";
autoObject[0].LineSerial = "";
autoObject[0].LimitAmount = 0.0;
autoObject[0].AvailableAmount = 0.0;
autoObject[0].Utilisation = 0.0;
autoObject[0].PurposeUDF = "";
autoObject[0].LGCommUDF = "";
autoObject[0].LGBidBondUDF = "";
autoObject[0].LGPerformaceUDF = "";
autoObject[0].LGAdvacedPayment = "";
autoObject[0].LCComm = "";
autoObject[0].LCDefAccComm = "";
autoObject[0].LCCashCover = "";
autoObject[0].IdcComm = "";
autoObject[0].IdcAvalComm = "";
autoObject[0].IdcCashCover = "";
autoObject[0].DebitAccNum = "";
autoObject[0].FacilityID = "";
autoObject[0].MainLineId = "";
autoObject[0].EffectiveLineAmount = 0.0;
autoObject[0].ExpiryDate = "";
autoObject[0].FacilityBranch = "";
autoObject[0].InternalRemarks = "";
autoObject[0].Currency = "";
autoObject[0].aviabilityFlag = "";
autoObject[0].currencyRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCurrencyRestriction();
autoObject[0].currencyRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCurrencyRestriction();
autoObject[0].currencyRestricted[0].CCY = "";
autoObject[0].currencyRestricted[0].ID = 0.0;
autoObject[0].branchRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityBranchRestriction();
autoObject[0].branchRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityBranchRestriction();
autoObject[0].branchRestricted[0].BRANCHCODE = "";
autoObject[0].branchRestricted[0].ID = 0.0;
autoObject[0].customerRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityCustomerRestriction();
autoObject[0].customerRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityCustomerRestriction();
autoObject[0].customerRestricted[0].CUSTOMERNO = "";
autoObject[0].customerRestricted[0].ID = 0.0;
autoObject[0].productRestricted = new tw.object.listOf.toolkit.NBEINTT.FacilityProductRestriction();
autoObject[0].productRestricted[0] = new tw.object.toolkit.NBEINTT.FacilityProductRestriction();
autoObject[0].productRestricted[0].PRODUCTCODE = "";
autoObject[0].productRestricted[0].ID = 0.0;
autoObject[0].mainLineFlag = false;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="productCode" id="2056.b6c1b141-b1cd-4ad0-84d9-bfc7f9751758" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="020191d9-9f66-4cc0-8ffc-5ec5e62d050d" parallelMultiple="false" name="Error" id="03b1ff9f-fa64-4198-8b1f-d4d9174562bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="112" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>53c5e331-4d76-4450-8a59-c649881bfffe</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8c5f0700-60a4-43f7-8e7f-d2e53fc3c982" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c55f5b0d-86b6-42fe-8ba6-1550a57daf90" eventImplId="127b2ab9-5e68-49d2-89e3-bd6b35f54f24">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f2856051-ea0a-4c7f-8e1d-094d057eb0a2" parallelMultiple="false" name="Error1" id="7ee8d454-4774-4ad2-8ab8-960c70002005">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="88" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>889e1a75-6d28-40db-8e5f-b8370617492a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e39db912-ae6e-447b-830b-8d1ddabd056f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e2856df9-a14b-4c44-801b-fcc79f7d903c" eventImplId="b8e0060f-64bd-4486-8431-09b050d21460">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="ef86b0ef-32d3-4029-8de9-c3018164f7dc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="222" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Get Facility Codes -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Facility Codes -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>53c5e331-4d76-4450-8a59-c649881bfffe</ns16:incoming>
                        
                        
                        <ns16:incoming>889e1a75-6d28-40db-8e5f-b8370617492a</ns16:incoming>
                        
                        
                        <ns16:incoming>0d5fffec-3959-4f41-85ce-58833aef8eec</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="dbe8f8f2-5f4f-4a94-809f-a9088615a9b3" eventImplId="6f693dd3-0ff7-4dcd-81f5-6062c9c73d2f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="03b1ff9f-fa64-4198-8b1f-d4d9174562bf" targetRef="ef86b0ef-32d3-4029-8de9-c3018164f7dc" name="To End Event" id="53c5e331-4d76-4450-8a59-c649881bfffe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ee8d454-4774-4ad2-8ab8-960c70002005" targetRef="ef86b0ef-32d3-4029-8de9-c3018164f7dc" name="To End Event" id="889e1a75-6d28-40db-8e5f-b8370617492a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="a307f6f2-dbb0-408a-8a63-f2a5540a3b4b" name="Exclusive Gateway" id="1c75acfc-732f-4d14-87ec-1b113da65d52">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="349" y="94" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f3a11bd4-0f05-4435-8267-61fdac51c142</ns16:incoming>
                        
                        
                        <ns16:outgoing>a307f6f2-dbb0-408a-8a63-f2a5540a3b4b</ns16:outgoing>
                        
                        
                        <ns16:outgoing>0d5fffec-3959-4f41-85ce-58833aef8eec</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c75acfc-732f-4d14-87ec-1b113da65d52" targetRef="f2856051-ea0a-4c7f-8e1d-094d057eb0a2" name="To Mapping" id="a307f6f2-dbb0-408a-8a63-f2a5540a3b4b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c75acfc-732f-4d14-87ec-1b113da65d52" targetRef="ef86b0ef-32d3-4029-8de9-c3018164f7dc" name="To End Event" id="0d5fffec-3959-4f41-85ce-58833aef8eec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Get Facility Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.20f11337-97a3-4d64-8a05-4adab1d5fdf8</processLinkId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f2856051-ea0a-4c7f-8e1d-094d057eb0a2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c6ace50c-4429-4c75-80c8-7cb8dc814abd</toProcessItemId>
            <guid>073aedd1-40b7-4cb7-860a-b7a0dd469a3c</guid>
            <versionId>1f0b22d1-7898-44ac-bb42-aa2f59df4c26</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f2856051-ea0a-4c7f-8e1d-094d057eb0a2</fromProcessItemId>
            <toProcessItemId>2025.c6ace50c-4429-4c75-80c8-7cb8dc814abd</toProcessItemId>
        </link>
        <link name="To Mapping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a307f6f2-dbb0-408a-8a63-f2a5540a3b4b</processLinkId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.f2856051-ea0a-4c7f-8e1d-094d057eb0a2</toProcessItemId>
            <guid>64c5fa01-cd0e-4d95-b9d2-8324b11c96db</guid>
            <versionId>68e366bb-ed31-4447-83d8-05deefcaeec1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</fromProcessItemId>
            <toProcessItemId>2025.f2856051-ea0a-4c7f-8e1d-094d057eb0a2</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0d5fffec-3959-4f41-85ce-58833aef8eec</processLinkId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e82</endStateId>
            <toProcessItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</toProcessItemId>
            <guid>04e766a8-0130-4ebd-b0a3-67d1cefa257f</guid>
            <versionId>7e3871ee-ae7a-45e1-9d68-d3b16cc483ad</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</fromProcessItemId>
            <toProcessItemId>2025.ef86b0ef-32d3-4029-8de9-c3018164f7dc</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f3a11bd4-0f05-4435-8267-61fdac51c142</processLinkId>
            <processId>1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</fromProcessItemId>
            <endStateId>guid:d06bc5264f3bac58:1a63dcc9:18920913403:-1cea</endStateId>
            <toProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</toProcessItemId>
            <guid>bc0aa278-5bb9-482c-b8b7-02ec161d1769</guid>
            <versionId>ad0ddfcc-69ba-4a59-a062-5fc2d97f9a5d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.020191d9-9f66-4cc0-8ffc-5ec5e62d050d</fromProcessItemId>
            <toProcessItemId>2025.1c75acfc-732f-4d14-87ec-1b113da65d52</toProcessItemId>
        </link>
    </process>
</teamworks>

