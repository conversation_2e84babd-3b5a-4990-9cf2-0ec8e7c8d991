<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.4e624381-ed2d-4716-836c-494f4210ce96" name="convert currency">
        <lastModified>1692728609216</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:debeaea024f39647:-512ef1a:189991da1a7:3e0c</guid>
        <versionId>bef73ddf-aa6e-45da-b496-cb2893a566d9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-2df6" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0eecdb18-0417-4fb6-890e-9c279383991d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"50aed778-3c05-445c-862c-3cd5fd29778e"},{"incoming":["b79557f1-95bf-46ff-8e1d-0db80c3bc0cf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":690,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:debeaea024f39647:-512ef1a:189991da1a7:3e0e"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"f7b2ea19-47eb-429a-832c-7bc6d1e4f78a"},{"targetRef":"fdbef8d0-3e0c-4811-8afa-d87ee715d832","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.0eecdb18-0417-4fb6-890e-9c279383991d","sourceRef":"50aed778-3c05-445c-862c-3cd5fd29778e"},{"startQuantity":1,"outgoing":["169db284-57ae-4a37-86c5-4e8f402a3622"],"incoming":["264e922b-b4a3-4098-87f3-358a84dfe4cf"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":287,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"MW_FC Convert Amount To Equivalent Currency","dataInputAssociation":[{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.amount"]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.currency1"]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.currency2"]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"TRANSFER\""]}}]},{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"S\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"11c476e2-3b90-4ebf-8e02-54485ac668de","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.amountResult"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"8b3341ad-e469-4dfc-83ab-aa39294dd17d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"169db284-57ae-4a37-86c5-4e8f402a3622","sourceRef":"11c476e2-3b90-4ebf-8e02-54485ac668de"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"amount","isCollection":false,"declaredType":"dataObject","id":"2056.84cbd06f-a2b8-43c7-8829-f29c916f8cb7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currency1","isCollection":false,"declaredType":"dataObject","id":"2056.abb072a5-0143-4077-812f-a05728600d50"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currency2","isCollection":false,"declaredType":"dataObject","id":"2056.acea380b-5728-41a7-8ea2-696a3775d45d"},{"startQuantity":1,"outgoing":["264e922b-b4a3-4098-87f3-358a84dfe4cf"],"incoming":["2027.0eecdb18-0417-4fb6-890e-9c279383991d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":110,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"fdbef8d0-3e0c-4811-8afa-d87ee715d832","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.Seperated = new tw.object.listOf.String();\r\n\ttw.local.Seperated = tw.local.data.split(\"-\");\r\n\ttw.local.amount = tw.local.Seperated[0];\r\n\ttw.local.currency1 = tw.local.Seperated[1];\r\n\ttw.local.currency2 = tw.local.Seperated[2];\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"11c476e2-3b90-4ebf-8e02-54485ac668de","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To MW_FC Convert Amount To Equivalent Currency","declaredType":"sequenceFlow","id":"264e922b-b4a3-4098-87f3-358a84dfe4cf","sourceRef":"fdbef8d0-3e0c-4811-8afa-d87ee715d832"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.799a8ef4-c701-4626-886b-88457a56b9a5"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"amountResult","isCollection":false,"declaredType":"dataObject","id":"2056.b6befd74-3314-4a3b-8344-21ad41c8c643"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.deae9198-3791-4f51-8fc4-e96dcc30bb48"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.6904ddf5-62d7-4f8f-821e-b513410b5343"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.c016a406-7ce6-47fb-86c4-d8148389c093"},{"startQuantity":1,"outgoing":["b79557f1-95bf-46ff-8e1d-0db80c3bc0cf"],"incoming":["bffb8fca-a8fc-407c-854a-878458b93a4a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":498,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1ba1bf7d-4015-4487-82a9-fe925debdc1e","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n      var rounded = 0.0\r\n\ttw.local.results = 0.0;\r\n\tif (tw.local.isSuccessful) {\r\n\t\trounded= tw.local.amountResult;\r\n\t\ttw.local.results = rounded.toFixed(2)\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"f7b2ea19-47eb-429a-832c-7bc6d1e4f78a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"b79557f1-95bf-46ff-8e1d-0db80c3bc0cf","sourceRef":"1ba1bf7d-4015-4487-82a9-fe925debdc1e"},{"parallelMultiple":false,"outgoing":["f83df90d-2802-4159-894b-10b2434753c4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"381fbf93-1b2f-4e66-8f28-165867ab90e7"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"876f3627-4d62-453d-8529-c79edaaa9725","otherAttributes":{"eventImplId":"85d29eb4-1ffa-4681-82cb-43117239aefe"}}],"attachedToRef":"fdbef8d0-3e0c-4811-8afa-d87ee715d832","extensionElements":{"nodeVisualInfo":[{"width":24,"x":145,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"ee59f11d-d028-4498-81d5-bdbf10084c43","outputSet":{}},{"parallelMultiple":false,"outgoing":["edcc864f-5606-4df1-8039-7b578024f78c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"822dd86e-b365-4b6c-8e65-9e9e5fe25525"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c9a6c965-9a91-454b-8e43-951a7e382275","otherAttributes":{"eventImplId":"f02890a0-9f43-4822-86aa-483360509913"}}],"attachedToRef":"11c476e2-3b90-4ebf-8e02-54485ac668de","extensionElements":{"nodeVisualInfo":[{"width":24,"x":322,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"704a52cd-869c-4f2d-8235-53898745fa67","outputSet":{}},{"parallelMultiple":false,"outgoing":["a154a862-91b0-4b61-806b-1fe17b976854"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"42fe1e24-56ca-4d4a-8b33-6d5ea9ecf288"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3b1a7a61-94c3-4f98-8e9a-212261cc3597","otherAttributes":{"eventImplId":"d3487118-6efc-49b8-817a-a9590e4358e3"}}],"attachedToRef":"1ba1bf7d-4015-4487-82a9-fe925debdc1e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":533,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"5212149a-3753-4cd6-8198-ad09b17b86cf","outputSet":{}},{"incoming":["f83df90d-2802-4159-894b-10b2434753c4","edcc864f-5606-4df1-8039-7b578024f78c","a154a862-91b0-4b61-806b-1fe17b976854","46177577-05f2-4c2b-841d-da8f0f19501d"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"17a610de-4d73-426e-8fd9-886a13502063","otherAttributes":{"eventImplId":"2506ccb3-b7e3-4303-8c00-f95d654093dd"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":319,"y":188,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Convert currency -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\n\/\/var attribute = String(tw.system.error.getAttribute(\"type\"));\r\n\/\/var element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n\/\/tw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Convert currency -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"a04609f2-47b0-4b05-809d-df4e58aaa2bf"},{"targetRef":"a04609f2-47b0-4b05-809d-df4e58aaa2bf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f83df90d-2802-4159-894b-10b2434753c4","sourceRef":"ee59f11d-d028-4498-81d5-bdbf10084c43"},{"targetRef":"a04609f2-47b0-4b05-809d-df4e58aaa2bf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"edcc864f-5606-4df1-8039-7b578024f78c","sourceRef":"704a52cd-869c-4f2d-8235-53898745fa67"},{"targetRef":"a04609f2-47b0-4b05-809d-df4e58aaa2bf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a154a862-91b0-4b61-806b-1fe17b976854","sourceRef":"5212149a-3753-4cd6-8198-ad09b17b86cf"},{"outgoing":["bffb8fca-a8fc-407c-854a-878458b93a4a","46177577-05f2-4c2b-841d-da8f0f19501d"],"incoming":["169db284-57ae-4a37-86c5-4e8f402a3622"],"default":"bffb8fca-a8fc-407c-854a-878458b93a4a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":400,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"8b3341ad-e469-4dfc-83ab-aa39294dd17d"},{"targetRef":"1ba1bf7d-4015-4487-82a9-fe925debdc1e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"bffb8fca-a8fc-407c-854a-878458b93a4a","sourceRef":"8b3341ad-e469-4dfc-83ab-aa39294dd17d"},{"targetRef":"a04609f2-47b0-4b05-809d-df4e58aaa2bf","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"46177577-05f2-4c2b-841d-da8f0f19501d","sourceRef":"8b3341ad-e469-4dfc-83ab-aa39294dd17d"}],"laneSet":[{"id":"6f345bbc-8a46-4d48-8956-9478ff235758","lane":[{"flowNodeRef":["50aed778-3c05-445c-862c-3cd5fd29778e","f7b2ea19-47eb-429a-832c-7bc6d1e4f78a","11c476e2-3b90-4ebf-8e02-54485ac668de","fdbef8d0-3e0c-4811-8afa-d87ee715d832","1ba1bf7d-4015-4487-82a9-fe925debdc1e","ee59f11d-d028-4498-81d5-bdbf10084c43","704a52cd-869c-4f2d-8235-53898745fa67","5212149a-3753-4cd6-8198-ad09b17b86cf","a04609f2-47b0-4b05-809d-df4e58aaa2bf","8b3341ad-e469-4dfc-83ab-aa39294dd17d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"006ea44c-6cf3-4727-8e67-c98542c35100","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"convert currency","declaredType":"process","id":"1.4e624381-ed2d-4716-836c-494f4210ce96","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.f639a9f6-cb52-4945-8ef6-e647886716bb"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.76d4f7c0-eef2-4020-8dc4-0fab0f60d558"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"5-USD-EGP\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.06ffe7ef-2698-4412-82c0-b9659fb28657"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.06ffe7ef-2698-4412-82c0-b9659fb28657</processParameterId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"5-USD-EGP"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bca9abad-2db7-4922-a18a-37a3b4c17417</guid>
            <versionId>9f72ef5f-2295-4c8e-9d11-fab61bbfb1a5</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f639a9f6-cb52-4945-8ef6-e647886716bb</processParameterId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0e80ecb8-ba09-43b4-aaa6-bb1cf45f2a5d</guid>
            <versionId>20563303-9d6d-4de0-9abf-109604733beb</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.76d4f7c0-eef2-4020-8dc4-0fab0f60d558</processParameterId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>335d3b2d-0a20-4d67-92c5-b267cf2377da</guid>
            <versionId>6f200a6b-ddf4-4936-be0c-deabe8e6c329</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.790e889f-59a7-4806-aad4-c24902b34f98</processParameterId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5fcf07b5-9877-47ab-af28-930b86a91f38</guid>
            <versionId>e4ec3157-86d7-41fa-acef-cc6c20d205b7</versionId>
        </processParameter>
        <processVariable name="amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.84cbd06f-a2b8-43c7-8829-f29c916f8cb7</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>83e63cb7-2906-4474-b5e7-41e055e0b186</guid>
            <versionId>9d49235f-e9dc-4e9d-954a-07789f52b9cf</versionId>
        </processVariable>
        <processVariable name="currency1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.abb072a5-0143-4077-812f-a05728600d50</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f530b0c3-2f05-4467-ae79-69badbcaea27</guid>
            <versionId>ab08eb72-2276-4a27-9f19-1f2abc640abc</versionId>
        </processVariable>
        <processVariable name="currency2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.acea380b-5728-41a7-8ea2-696a3775d45d</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e003cfed-4447-4a1d-9069-65ce412b3408</guid>
            <versionId>a6cbdeac-03f6-44f4-b865-a97f7ace00dc</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.799a8ef4-c701-4626-886b-88457a56b9a5</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4e9a51e3-9877-44d3-8403-33bda8069584</guid>
            <versionId>2086701f-8417-4b0a-81bf-5fd7b486d033</versionId>
        </processVariable>
        <processVariable name="amountResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b6befd74-3314-4a3b-8344-21ad41c8c643</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d2afd7e3-b7e1-4266-899c-44791d0e52d2</guid>
            <versionId>6257ee73-3ddf-44fe-9372-f7b7b80ed0f2</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.deae9198-3791-4f51-8fc4-e96dcc30bb48</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0b18b8e1-1f88-4a0a-b51f-a373cb901561</guid>
            <versionId>51fce374-bc65-4720-bb95-18034ddee474</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6904ddf5-62d7-4f8f-821e-b513410b5343</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a76b75f-4c98-4c2c-b324-56b764108759</guid>
            <versionId>eff9db24-7c5a-4531-a1a8-5c570a403aeb</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c016a406-7ce6-47fb-86c4-d8148389c093</processVariableId>
            <description isNull="true" />
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bdcd8660-489e-4d51-88ba-1e7f6f65305c</guid>
            <versionId>5dc42f71-f107-4fb8-b5de-dac5d5645c49</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f7b2ea19-47eb-429a-832c-7bc6d1e4f78a</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.132cfed5-ed6b-46da-9d4d-62fed9eaa941</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:debeaea024f39647:-512ef1a:189991da1a7:3e0e</guid>
            <versionId>0b8ee86b-a31d-4a8a-a6f0-0b557ae6f3e4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="690" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.132cfed5-ed6b-46da-9d4d-62fed9eaa941</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8e16dfa0-f086-4c1f-86c5-b309e6dca2f9</guid>
                <versionId>14b865d6-a5dc-4173-84d0-4eedd2bc58b4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.b51a2a5f-cf01-4885-b494-d4df24bf89ba</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:42738960b3d81f85:8740bc4:189d575e46d:-3809</guid>
            <versionId>66c0dcab-814d-41c4-b1a1-338a479b835c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b2e3b93f-ecf5-47f8-88f8-a0eec641ffe9</processItemPrePostId>
                <processItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Convert currency -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Convert currency -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>c656ae3f-03f6-4964-8d6f-419e0f3a4c83</guid>
                <versionId>8f16f93b-0cb7-49c8-b96d-8d4507e1fdc3</versionId>
            </processPrePosts>
            <layoutData x="319" y="188">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.b51a2a5f-cf01-4885-b494-d4df24bf89ba</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>ad1d1e00-01b0-46d2-9b17-90100119bf76</guid>
                <versionId>d8972873-55d3-47ee-9e1b-b8cb9e8f0850</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.21bc4a96-2b1d-4db0-86ca-dda19251eafe</parameterMappingId>
                    <processParameterId>2055.790e889f-59a7-4806-aad4-c24902b34f98</processParameterId>
                    <parameterMappingParentId>3007.b51a2a5f-cf01-4885-b494-d4df24bf89ba</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8dcb0aee-5d70-437f-b2a7-298c78da9214</guid>
                    <versionId>bb49b83f-28e0-48c0-b548-e76324a2fdea</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1ba1bf7d-4015-4487-82a9-fe925debdc1e</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.451484cf-e8aa-4394-91fd-edcde763ff2d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
            <guid>guid:ef0d56db9ddf1554:-58d13970:189a74c1378:-7c84</guid>
            <versionId>7de89232-fee8-4adb-abbe-617db8b49a8f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="498" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d575e46d:-3809</errorHandlerItem>
                <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.451484cf-e8aa-4394-91fd-edcde763ff2d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
      var rounded = 0.0&#xD;
	tw.local.results = 0.0;&#xD;
	if (tw.local.isSuccessful) {&#xD;
		rounded= tw.local.amountResult;&#xD;
		tw.local.results = rounded.toFixed(2)&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>07836ea6-ac35-4905-9a9b-852ae32dc1a2</guid>
                <versionId>c22a847c-6894-4c2f-926c-bff2bb9f22ef</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.0e004990-547c-4503-97e6-86fefeb558e8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f7c</guid>
            <versionId>9a47b876-264c-41b5-837a-ebe711afd175</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="400" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.0e004990-547c-4503-97e6-86fefeb558e8</switchId>
                <guid>c73aedc0-6e3a-4c1b-8e5c-c5f6848a39b2</guid>
                <versionId>e42f310f-b891-4dd5-8c9e-0a8caed50343</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.53e26e8b-f5e4-4709-b5e2-4e18dc22568d</switchConditionId>
                    <switchId>3013.0e004990-547c-4503-97e6-86fefeb558e8</switchId>
                    <seq>1</seq>
                    <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-2df7</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>a908ffdd-abb2-4140-a4d9-8ddcf7938fab</guid>
                    <versionId>73aaf0a2-f765-4d8c-973a-d49a1f1b0091</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.11c476e2-3b90-4ebf-8e02-54485ac668de</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>MW_FC Convert Amount To Equivalent Currency</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
            <guid>guid:debeaea024f39647:-512ef1a:189991da1a7:3e14</guid>
            <versionId>a4908ccf-7fb0-4462-815f-bb575ed9842d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="287" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d575e46d:-3809</errorHandlerItem>
                <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>aeda503b-b0f0-42ac-a26e-ef477e013429</guid>
                <versionId>30a6bc81-3e42-4eba-9f2b-1c89a8459304</versionId>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7effe49b-1876-48a9-8650-64028183672b</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0ad1615a-6c48-4660-8be2-56b66346eb31</guid>
                    <versionId>062dce3c-850e-4dde-be16-1de3f979ec44</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ce2384a7-1af9-4d86-875b-8444b13a90d6</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.amountResult</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b94fc1fb-3214-4e2f-b46e-7becebd50bd9</guid>
                    <versionId>07ad04b2-10f2-40e8-9795-454ab8f14c63</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c674be5a-17a7-4a47-a257-da5a4efd9651</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>534235c4-ca18-47c6-99db-206330c9a775</guid>
                    <versionId>09c99213-e461-4092-85a2-60ff2295ef6e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ce1353e3-b6b2-43ea-8a82-614202760e99</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"TRANSFER"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1a38d499-7943-411c-b223-73da16c8713b</guid>
                    <versionId>09f10e6d-9cd3-4019-b7ef-ce05a43dedfe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b79020c2-ebde-4e0b-b3da-b2ca98073e50</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0ba2b5c7-c88d-4652-9f07-1289a02a7063</guid>
                    <versionId>3fb20542-7a67-43cd-b89f-0695f3106d02</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ff470cd-b379-48d9-bfb2-5966beddb3c4</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"S"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a63b63cb-690b-4ce9-9cb5-7074870ef186</guid>
                    <versionId>4de5ba47-116a-40ec-8c1f-1c60cb60face</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.92f091db-7671-4c30-84ed-185cc7466ff2</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.amount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b7579e97-365c-418f-9096-04c45c97f7f3</guid>
                    <versionId>72196196-2eb0-4dd2-a352-78152e1dc331</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bf69624f-5ca2-48d4-9803-dcb8427bda53</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>51d8728f-3e8d-471d-96fb-0719a3d5695b</guid>
                    <versionId>77cb8846-c179-4e71-bd4e-2d4e0dc68a8f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ba209894-a606-4787-a91f-a3c0f684ea45</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>70714785-132e-4a71-bc2d-de85285bb23e</guid>
                    <versionId>7ce3b400-899b-4857-9175-359b1c5e1a46</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ded77cc-e1e3-4d71-88b3-df8824f0b8bd</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.currency1</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>47642f1c-6371-405b-8558-3a088624f020</guid>
                    <versionId>7e451d67-e308-4779-8afa-04dc85e8d7b7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d92383f4-8fcb-405e-94c3-09cba7af919b</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>94ae6cfb-3e5b-4d09-b774-4c9441b7a42e</guid>
                    <versionId>b5e36edb-7258-425d-b377-92d5713de778</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.44b2b77b-e61d-4617-8212-b39814800b43</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>159e63df-61bc-47b1-992c-7fa44fe82f08</guid>
                    <versionId>b83987b9-22aa-4450-8635-a800e1599fc0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b8469aa4-93b3-499b-81e3-93db26091b0b</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d21fa56e-1b17-471e-8ce4-14b3a3503ee2</guid>
                    <versionId>db971407-82a2-4104-9bbc-8e7ee1e25d91</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.71f83fd6-a908-40ff-903a-1a298efe7267</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8809c0ab-d784-47e7-9c02-d27ac71ea192</guid>
                    <versionId>dc65bb2d-ae9e-4904-be29-3a4a8d6066e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0c1a98e1-9b63-4e24-8ee6-28be22e5f14e</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.ae713f1d-9a6d-4204-8f2b-08c1c4b72d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.currency2</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f47f6bf0-afb4-4a8c-89ce-5a0cee31fa3b</guid>
                    <versionId>f1462add-1af3-4d74-bb2c-b5d80ba8f699</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</processItemId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4e3f8098-01ba-4744-9a05-8d96983f29de</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
            <guid>guid:debeaea024f39647:-512ef1a:189991da1a7:3e53</guid>
            <versionId>ff5a79a7-2283-4bd3-bb27-b3cb16377d90</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.962d80d0-e866-4482-8560-75243f4f3968</processItemPrePostId>
                <processItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>5ca8d05e-77fd-48ff-927a-a64c1a3925f7</guid>
                <versionId>38f2bc8d-f261-4fce-bd67-49594176723d</versionId>
            </processPrePosts>
            <layoutData x="110" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d575e46d:-3809</errorHandlerItem>
                <errorHandlerItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4e3f8098-01ba-4744-9a05-8d96983f29de</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.amount = tw.local.Seperated[0];&#xD;
	tw.local.currency1 = tw.local.Seperated[1];&#xD;
	tw.local.currency2 = tw.local.Seperated[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>31523e97-d1d8-4ed1-994b-4d4e9eab95c6</guid>
                <versionId>19a3cfc3-134b-480e-9041-24eadd04bf99</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="convert currency" id="1.4e624381-ed2d-4716-836c-494f4210ce96" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.06ffe7ef-2698-4412-82c0-b9659fb28657">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"5-USD-EGP"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.f639a9f6-cb52-4945-8ef6-e647886716bb" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.76d4f7c0-eef2-4020-8dc4-0fab0f60d558" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="6f345bbc-8a46-4d48-8956-9478ff235758">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="006ea44c-6cf3-4727-8e67-c98542c35100" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>50aed778-3c05-445c-862c-3cd5fd29778e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f7b2ea19-47eb-429a-832c-7bc6d1e4f78a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>11c476e2-3b90-4ebf-8e02-54485ac668de</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fdbef8d0-3e0c-4811-8afa-d87ee715d832</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1ba1bf7d-4015-4487-82a9-fe925debdc1e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ee59f11d-d028-4498-81d5-bdbf10084c43</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>704a52cd-869c-4f2d-8235-53898745fa67</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5212149a-3753-4cd6-8198-ad09b17b86cf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a04609f2-47b0-4b05-809d-df4e58aaa2bf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8b3341ad-e469-4dfc-83ab-aa39294dd17d</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="50aed778-3c05-445c-862c-3cd5fd29778e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.0eecdb18-0417-4fb6-890e-9c279383991d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="f7b2ea19-47eb-429a-832c-7bc6d1e4f78a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="690" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:debeaea024f39647:-512ef1a:189991da1a7:3e0e</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b79557f1-95bf-46ff-8e1d-0db80c3bc0cf</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="50aed778-3c05-445c-862c-3cd5fd29778e" targetRef="fdbef8d0-3e0c-4811-8afa-d87ee715d832" name="To Script Task" id="2027.0eecdb18-0417-4fb6-890e-9c279383991d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" name="MW_FC Convert Amount To Equivalent Currency" id="11c476e2-3b90-4ebf-8e02-54485ac668de">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="287" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>264e922b-b4a3-4098-87f3-358a84dfe4cf</ns16:incoming>
                        
                        
                        <ns16:outgoing>169db284-57ae-4a37-86c5-4e8f402a3622</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.amount</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.currency1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.currency2</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"TRANSFER"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"S"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.amountResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="11c476e2-3b90-4ebf-8e02-54485ac668de" targetRef="8b3341ad-e469-4dfc-83ab-aa39294dd17d" name="To is Successful" id="169db284-57ae-4a37-86c5-4e8f402a3622">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="amount" id="2056.84cbd06f-a2b8-43c7-8829-f29c916f8cb7" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currency1" id="2056.abb072a5-0143-4077-812f-a05728600d50" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currency2" id="2056.acea380b-5728-41a7-8ea2-696a3775d45d" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="fdbef8d0-3e0c-4811-8afa-d87ee715d832">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="110" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.0eecdb18-0417-4fb6-890e-9c279383991d</ns16:incoming>
                        
                        
                        <ns16:outgoing>264e922b-b4a3-4098-87f3-358a84dfe4cf</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.amount = tw.local.Seperated[0];&#xD;
	tw.local.currency1 = tw.local.Seperated[1];&#xD;
	tw.local.currency2 = tw.local.Seperated[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="fdbef8d0-3e0c-4811-8afa-d87ee715d832" targetRef="11c476e2-3b90-4ebf-8e02-54485ac668de" name="To MW_FC Convert Amount To Equivalent Currency" id="264e922b-b4a3-4098-87f3-358a84dfe4cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.799a8ef4-c701-4626-886b-88457a56b9a5" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="amountResult" id="2056.b6befd74-3314-4a3b-8344-21ad41c8c643" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.deae9198-3791-4f51-8fc4-e96dcc30bb48" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.6904ddf5-62d7-4f8f-821e-b513410b5343" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.c016a406-7ce6-47fb-86c4-d8148389c093" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="1ba1bf7d-4015-4487-82a9-fe925debdc1e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="498" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bffb8fca-a8fc-407c-854a-878458b93a4a</ns16:incoming>
                        
                        
                        <ns16:outgoing>b79557f1-95bf-46ff-8e1d-0db80c3bc0cf</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
      var rounded = 0.0&#xD;
	tw.local.results = 0.0;&#xD;
	if (tw.local.isSuccessful) {&#xD;
		rounded= tw.local.amountResult;&#xD;
		tw.local.results = rounded.toFixed(2)&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1ba1bf7d-4015-4487-82a9-fe925debdc1e" targetRef="f7b2ea19-47eb-429a-832c-7bc6d1e4f78a" name="To End" id="b79557f1-95bf-46ff-8e1d-0db80c3bc0cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="fdbef8d0-3e0c-4811-8afa-d87ee715d832" parallelMultiple="false" name="Error" id="ee59f11d-d028-4498-81d5-bdbf10084c43">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="145" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f83df90d-2802-4159-894b-10b2434753c4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="381fbf93-1b2f-4e66-8f28-165867ab90e7" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="876f3627-4d62-453d-8529-c79edaaa9725" eventImplId="85d29eb4-1ffa-4681-82cb-43117239aefe">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="11c476e2-3b90-4ebf-8e02-54485ac668de" parallelMultiple="false" name="Error1" id="704a52cd-869c-4f2d-8235-53898745fa67">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="322" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>edcc864f-5606-4df1-8039-7b578024f78c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="822dd86e-b365-4b6c-8e65-9e9e5fe25525" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c9a6c965-9a91-454b-8e43-951a7e382275" eventImplId="f02890a0-9f43-4822-86aa-483360509913">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1ba1bf7d-4015-4487-82a9-fe925debdc1e" parallelMultiple="false" name="Error2" id="5212149a-3753-4cd6-8198-ad09b17b86cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="533" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a154a862-91b0-4b61-806b-1fe17b976854</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="42fe1e24-56ca-4d4a-8b33-6d5ea9ecf288" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3b1a7a61-94c3-4f98-8e9a-212261cc3597" eventImplId="d3487118-6efc-49b8-817a-a9590e4358e3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="a04609f2-47b0-4b05-809d-df4e58aaa2bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="319" y="188" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Convert currency -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Convert currency -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f83df90d-2802-4159-894b-10b2434753c4</ns16:incoming>
                        
                        
                        <ns16:incoming>edcc864f-5606-4df1-8039-7b578024f78c</ns16:incoming>
                        
                        
                        <ns16:incoming>a154a862-91b0-4b61-806b-1fe17b976854</ns16:incoming>
                        
                        
                        <ns16:incoming>46177577-05f2-4c2b-841d-da8f0f19501d</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="17a610de-4d73-426e-8fd9-886a13502063" eventImplId="2506ccb3-b7e3-4303-8c00-f95d654093dd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ee59f11d-d028-4498-81d5-bdbf10084c43" targetRef="a04609f2-47b0-4b05-809d-df4e58aaa2bf" name="To End Event" id="f83df90d-2802-4159-894b-10b2434753c4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="704a52cd-869c-4f2d-8235-53898745fa67" targetRef="a04609f2-47b0-4b05-809d-df4e58aaa2bf" name="To End Event" id="edcc864f-5606-4df1-8039-7b578024f78c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="5212149a-3753-4cd6-8198-ad09b17b86cf" targetRef="a04609f2-47b0-4b05-809d-df4e58aaa2bf" name="To End Event" id="a154a862-91b0-4b61-806b-1fe17b976854">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="bffb8fca-a8fc-407c-854a-878458b93a4a" name="is Successful" id="8b3341ad-e469-4dfc-83ab-aa39294dd17d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="400" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>169db284-57ae-4a37-86c5-4e8f402a3622</ns16:incoming>
                        
                        
                        <ns16:outgoing>bffb8fca-a8fc-407c-854a-878458b93a4a</ns16:outgoing>
                        
                        
                        <ns16:outgoing>46177577-05f2-4c2b-841d-da8f0f19501d</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="8b3341ad-e469-4dfc-83ab-aa39294dd17d" targetRef="1ba1bf7d-4015-4487-82a9-fe925debdc1e" name="To Script Task" id="bffb8fca-a8fc-407c-854a-878458b93a4a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8b3341ad-e469-4dfc-83ab-aa39294dd17d" targetRef="a04609f2-47b0-4b05-809d-df4e58aaa2bf" name="To End Event" id="46177577-05f2-4c2b-841d-da8f0f19501d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b79557f1-95bf-46ff-8e1d-0db80c3bc0cf</processLinkId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1ba1bf7d-4015-4487-82a9-fe925debdc1e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f7b2ea19-47eb-429a-832c-7bc6d1e4f78a</toProcessItemId>
            <guid>042c0f95-cd9d-4ee7-8e88-820e51e0b875</guid>
            <versionId>1f4729eb-283d-4747-9a59-a431325255ea</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1ba1bf7d-4015-4487-82a9-fe925debdc1e</fromProcessItemId>
            <toProcessItemId>2025.f7b2ea19-47eb-429a-832c-7bc6d1e4f78a</toProcessItemId>
        </link>
        <link name="To MW_FC Convert Amount To Equivalent Currency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.264e922b-b4a3-4098-87f3-358a84dfe4cf</processLinkId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.11c476e2-3b90-4ebf-8e02-54485ac668de</toProcessItemId>
            <guid>09e4b913-0a7f-4a3a-a806-c0526f8c89af</guid>
            <versionId>238108c5-3668-40d5-aeec-d5e44db16cfa</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fdbef8d0-3e0c-4811-8afa-d87ee715d832</fromProcessItemId>
            <toProcessItemId>2025.11c476e2-3b90-4ebf-8e02-54485ac668de</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bffb8fca-a8fc-407c-854a-878458b93a4a</processLinkId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.1ba1bf7d-4015-4487-82a9-fe925debdc1e</toProcessItemId>
            <guid>e67c5a12-2d85-4456-adac-fdc93c7b9454</guid>
            <versionId>6c93bb81-4081-4acc-a1d1-53da509c13a5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</fromProcessItemId>
            <toProcessItemId>2025.1ba1bf7d-4015-4487-82a9-fe925debdc1e</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.46177577-05f2-4c2b-841d-da8f0f19501d</processLinkId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</fromProcessItemId>
            <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-2df7</endStateId>
            <toProcessItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</toProcessItemId>
            <guid>69e0549a-ac54-427f-be3d-a300ea3822ca</guid>
            <versionId>999bba7b-9aa5-4302-9735-c200403e1ebc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</fromProcessItemId>
            <toProcessItemId>2025.a04609f2-47b0-4b05-809d-df4e58aaa2bf</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.169db284-57ae-4a37-86c5-4e8f402a3622</processLinkId>
            <processId>1.4e624381-ed2d-4716-836c-494f4210ce96</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.11c476e2-3b90-4ebf-8e02-54485ac668de</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</toProcessItemId>
            <guid>a365cd9f-984a-4f94-9b79-49846bdc7d53</guid>
            <versionId>ff9d5536-9d21-47a5-8cc5-03c64fe8b2bb</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.11c476e2-3b90-4ebf-8e02-54485ac668de</fromProcessItemId>
            <toProcessItemId>2025.8b3341ad-e469-4dfc-83ab-aa39294dd17d</toProcessItemId>
        </link>
    </process>
</teamworks>

