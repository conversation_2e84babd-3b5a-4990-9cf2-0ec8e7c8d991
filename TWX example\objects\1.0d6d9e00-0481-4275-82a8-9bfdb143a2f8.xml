<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8" name="Check Customer Accounts">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d98f9d02-2da3-40ba-8b1e-1b66152d11bc</guid>
        <versionId>e7d1f10e-fd04-4367-ba26-aeab4c3edb93</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d61" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.27975ea8-5107-442a-803c-e16de82df45a"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f6b48925-97ef-46b5-8744-b375731f46ce"},{"incoming":["772d0975-bef1-4cb5-9d8d-dc90cb9db56d","a6dd595b-b976-4a1c-8f9f-1e0085174047"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":720,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ac8"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"641e8339-755c-4ec6-8c07-2b46def4309d"},{"targetRef":"ca3c58ee-e162-4e59-8dae-c2a009678041","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To MW_FC Retrieve Customer Accounts","declaredType":"sequenceFlow","id":"2027.27975ea8-5107-442a-803c-e16de82df45a","sourceRef":"f6b48925-97ef-46b5-8744-b375731f46ce"},{"startQuantity":1,"outgoing":["6308f4c0-5792-4a06-bff1-408ccc6b1382"],"incoming":["2027.27975ea8-5107-442a-803c-e16de82df45a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":270,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"MW_FC Retrieve Customer Accounts","dataInputAssociation":[{"targetRef":"2055.b7a97c7d-99cb-44db-827f-0333fa136b3e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.e8bee736-2a93-4760-aafb-38e55006f6b9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.f3935930-ab2f-433c-8bb0-566cbabffe45","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.customerNo"]}}]},{"targetRef":"2055.d9d57b1e-9356-4664-87b3-27347adfe41d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.ca98719d-77f8-4fde-9663-52914ee14551","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"ca3c58ee-e162-4e59-8dae-c2a009678041","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.customerNo"]}}],"sourceRef":["2055.c60b2a07-f8cb-402a-958b-7a1bd44b7358"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.accountsList"]}}],"sourceRef":["2055.cdb1e363-7e09-4015-8916-b750b411405d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.98ee7d56-00f0-4503-9aa9-a320f73a3361"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.status"]}}],"sourceRef":["2055.6a8c1cae-f653-4cbe-a332-230489206d39"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.2f75a761-4364-4c36-b175-6c50df867d38"]}],"calledElement":"1.75ff304b-c398-4669-9ed0-70ab03152e8d"},{"targetRef":"9f08397c-599f-4c8a-898a-dc9e1bd3cd97","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2a69"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful ?","declaredType":"sequenceFlow","id":"6308f4c0-5792-4a06-bff1-408ccc6b1382","sourceRef":"ca3c58ee-e162-4e59-8dae-c2a009678041"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();\nautoObject[0] = new tw.object.toolkit.NBEINTT.Account();\nautoObject[0].accountNO = \"\";\nautoObject[0].currencyCode = \"\";\nautoObject[0].branchCode = \"\";\nautoObject[0].balance = 0.0;\nautoObject[0].typeCode = \"\";\nautoObject[0].customerName = \"\";\nautoObject[0].customerNo = \"\";\nautoObject[0].frozen = false;\nautoObject[0].dormant = false;\nautoObject[0].noDebit = false;\nautoObject[0].noCredit = false;\nautoObject[0].postingAllowed = false;\nautoObject[0].ibanAccountNumber = \"\";\nautoObject[0].accountClassCode = \"\";\nautoObject[0].balanceType = \"\";\nautoObject[0].accountStatus = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.1be95a5e-5da2-494e-b27f-8bdff21cc640"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.0fa17601-3f45-4ba3-bb48-c9c75070cd3f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.8f4bf7a2-faab-4c9a-91c9-83cd30213f72"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.5a73e115-4b06-41e7-9167-df5d174f1e77"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"declaredType":"dataObject","id":"2056.93865fe6-0f94-4a87-a7dc-4dc12cf28eac"},{"startQuantity":1,"outgoing":["772d0975-bef1-4cb5-9d8d-dc90cb9db56d"],"incoming":["e86a56f4-c03a-4267-8da1-062b3a610f8f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":560,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"cce9b0ca-bd70-4873-8549-1fc716f29cca","scriptFormat":"text\/x-javascript","script":{"content":["try {\t\r\n\ttw.local.exist = new tw.object.listOf.Boolean();\r\n\tfor (var j=0; j&lt;tw.local.accountNumber.listLength; j++) {\r\n\t\ttw.local.exist[j] = false;\r\n\t\tfor (var i=0; i&lt;tw.local.accountsList.listLength; i++) {\r\n\t\t\tif (tw.local.accountsList[i].accountNO == tw.local.accountNumber[j]) {\r\n\t\t\t\ttw.local.exist[j] = true;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"641e8339-755c-4ec6-8c07-2b46def4309d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"772d0975-bef1-4cb5-9d8d-dc90cb9db56d","sourceRef":"cce9b0ca-bd70-4873-8549-1fc716f29cca"},{"parallelMultiple":false,"outgoing":["c2ae5b57-297c-4c2f-8663-87fb7d637284"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"049da4f0-4d57-423b-8621-4cdf6a734802"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5ee19dc4-1597-4292-803e-fbe0837bee24","otherAttributes":{"eventImplId":"17fbf929-41ea-49aa-8e2d-19f0a19f569c"}}],"attachedToRef":"ca3c58ee-e162-4e59-8dae-c2a009678041","extensionElements":{"nodeVisualInfo":[{"width":24,"x":305,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2b427649-fdb6-48ee-88cb-63fc54157f02","outputSet":{}},{"outgoing":["e86a56f4-c03a-4267-8da1-062b3a610f8f","fa27d522-0d84-4315-88d7-ec3e979778e5"],"incoming":["6308f4c0-5792-4a06-bff1-408ccc6b1382"],"default":"e86a56f4-c03a-4267-8da1-062b3a610f8f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":401,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful ?","declaredType":"exclusiveGateway","id":"9f08397c-599f-4c8a-898a-dc9e1bd3cd97"},{"targetRef":"cce9b0ca-bd70-4873-8549-1fc716f29cca","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"e86a56f4-c03a-4267-8da1-062b3a610f8f","sourceRef":"9f08397c-599f-4c8a-898a-dc9e1bd3cd97"},{"parallelMultiple":false,"outgoing":["c7127770-a266-49e5-8aa9-60ff11e33552"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"54414878-c122-4c4f-832a-274c80cf86e5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4e3e8341-a76b-4d53-8fd0-b58dfc5a22e9","otherAttributes":{"eventImplId":"d8d4cc16-10ca-4a68-86ac-a41d80eb4cad"}}],"attachedToRef":"cce9b0ca-bd70-4873-8549-1fc716f29cca","extensionElements":{"nodeVisualInfo":[{"width":24,"x":595,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"f842817c-4760-4c7f-8aab-42cbb2c06c5e","outputSet":{}},{"startQuantity":1,"outgoing":["a6dd595b-b976-4a1c-8f9f-1e0085174047"],"incoming":["c2ae5b57-297c-4c2f-8663-87fb7d637284","fa27d522-0d84-4315-88d7-ec3e979778e5","c7127770-a266-49e5-8aa9-60ff11e33552"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":407,"y":175,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c2ae5b57-297c-4c2f-8663-87fb7d637284","sourceRef":"2b427649-fdb6-48ee-88cb-63fc54157f02"},{"targetRef":"7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"fa27d522-0d84-4315-88d7-ec3e979778e5","sourceRef":"9f08397c-599f-4c8a-898a-dc9e1bd3cd97"},{"targetRef":"7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c7127770-a266-49e5-8aa9-60ff11e33552","sourceRef":"f842817c-4760-4c7f-8aab-42cbb2c06c5e"},{"targetRef":"641e8339-755c-4ec6-8c07-2b46def4309d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a6dd595b-b976-4a1c-8f9f-1e0085174047","sourceRef":"7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af"}],"laneSet":[{"id":"023203bc-5287-411b-a19e-13a824bf23bb","lane":[{"flowNodeRef":["f6b48925-97ef-46b5-8744-b375731f46ce","641e8339-755c-4ec6-8c07-2b46def4309d","ca3c58ee-e162-4e59-8dae-c2a009678041","cce9b0ca-bd70-4873-8549-1fc716f29cca","2b427649-fdb6-48ee-88cb-63fc54157f02","9f08397c-599f-4c8a-898a-dc9e1bd3cd97","f842817c-4760-4c7f-8aab-42cbb2c06c5e","7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"6bb4595f-6bfa-4a22-a068-299edd5d51a3","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Check Customer Accounts","declaredType":"process","id":"1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"exist","isCollection":true,"id":"2055.********-f45d-49b1-af20-ad99fd5a03f5"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.ef381658-b8ee-42f5-8da7-ec74e0e2946b"}],"inputSet":[{"dataInputRefs":["2055.71fbef00-98c0-4292-b408-1d388b207e97","2055.b0a2409e-2c4c-4fda-8b13-bcb762ed4910"]}],"outputSet":[{"dataOutputRefs":["2055.********-f45d-49b1-af20-ad99fd5a03f5","2055.ef381658-b8ee-42f5-8da7-ec74e0e2946b"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"********\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerNo","isCollection":false,"id":"2055.71fbef00-98c0-4292-b408-1d388b207e97"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"accountNumber","isCollection":true,"id":"2055.b0a2409e-2c4c-4fda-8b13-bcb762ed4910"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="customerNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.71fbef00-98c0-4292-b408-1d388b207e97</processParameterId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"********"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d29cc2c9-c228-49ab-af09-6c5ef2a6e72f</guid>
            <versionId>3c8858fd-9b33-4ded-9780-1996c59d98ff</versionId>
        </processParameter>
        <processParameter name="accountNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b0a2409e-2c4c-4fda-8b13-bcb762ed4910</processParameterId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fc9dac60-d3ee-48cc-a0ad-5a35eeabbee5</guid>
            <versionId>a879a9ac-9072-412d-b688-4c2f4fe7e671</versionId>
        </processParameter>
        <processParameter name="exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.********-f45d-49b1-af20-ad99fd5a03f5</processParameterId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>89074eae-137b-466a-8500-d62cd5e2a363</guid>
            <versionId>e2ad1096-8f55-49f7-8198-5aa26b63f225</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ef381658-b8ee-42f5-8da7-ec74e0e2946b</processParameterId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e444ba7f-7f27-42a1-872d-aca4684faefe</guid>
            <versionId>2c934e9c-cca9-4652-9449-352193f9241b</versionId>
        </processParameter>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1be95a5e-5da2-494e-b27f-8bdff21cc640</processVariableId>
            <description isNull="true" />
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();
autoObject[0] = new tw.object.toolkit.NBEINTT.Account();
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</defaultValue>
            <guid>5a8b2f69-f327-46b1-86aa-ab99ddb85756</guid>
            <versionId>e2b5a6d6-1859-44b6-b869-ca354d8eca99</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0fa17601-3f45-4ba3-bb48-c9c75070cd3f</processVariableId>
            <description isNull="true" />
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8eddd031-b46d-4982-b91d-a7150e730377</guid>
            <versionId>0be76340-b5c7-4daf-ab6b-1baf73b96310</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8f4bf7a2-faab-4c9a-91c9-83cd30213f72</processVariableId>
            <description isNull="true" />
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>842dc224-d020-4ab8-ae75-bd6b3551759e</guid>
            <versionId>db6710ac-1eb3-49aa-8931-d57b40f63795</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5a73e115-4b06-41e7-9167-df5d174f1e77</processVariableId>
            <description isNull="true" />
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5faf01b1-4d71-4e57-af52-00a4af8fe9dd</guid>
            <versionId>98254b55-0733-40d2-9bd7-07acaae879b1</versionId>
        </processVariable>
        <processVariable name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.93865fe6-0f94-4a87-a7dc-4dc12cf28eac</processVariableId>
            <description isNull="true" />
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dbfe190d-4435-49d2-915c-0e788f60dee0</guid>
            <versionId>3673a81e-bf23-436e-b5e0-7f07b758e608</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</processItemId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <name>is Successful ?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.cb595648-9208-4152-aaf0-7315f3370fe8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-fb2</guid>
            <versionId>2d691ba1-3387-4161-be73-935b70e95100</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="401" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.cb595648-9208-4152-aaf0-7315f3370fe8</switchId>
                <guid>dd877e6b-30c7-4c67-bda4-876f7e4c764b</guid>
                <versionId>9104f8bf-d34a-4c94-abf7-7eeaaf313383</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.766d19d2-a15f-4d43-9002-0100c10f9bd4</switchConditionId>
                    <switchId>3013.cb595648-9208-4152-aaf0-7315f3370fe8</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d60</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>51c786ed-1fe9-4b2c-9798-f7624b0bd8bf</guid>
                    <versionId>2c737ae7-9d0f-4fba-835c-fa7189b3233e</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.641e8339-755c-4ec6-8c07-2b46def4309d</processItemId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.a94e6e86-9fec-4e1f-88b4-4df07109d4ea</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ac8</guid>
            <versionId>713f4e06-4feb-4167-acb2-e0581f898712</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="720" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.a94e6e86-9fec-4e1f-88b4-4df07109d4ea</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>459ad8ca-9754-4c6c-81d3-3c542182877f</guid>
                <versionId>827fa621-43d1-4b1c-aaf1-d7ab3af15980</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</processItemId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <name>MW_FC Retrieve Customer Accounts</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ac9</guid>
            <versionId>7263826e-80a1-40af-962b-3f98ba20f3e9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.33de4cc0-b28c-4c0a-90e6-67e23f7f27ba</processItemPrePostId>
                <processItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>1674a9af-fdb4-49c9-a465-d969d2070618</guid>
                <versionId>b2e66833-e86b-42c6-97f4-d12dc683fcb0</versionId>
            </processPrePosts>
            <layoutData x="270" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d41</errorHandlerItem>
                <errorHandlerItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.75ff304b-c398-4669-9ed0-70ab03152e8d</attachedProcessRef>
                <guid>102ba8b8-06a0-46e9-b7de-3669876cf63b</guid>
                <versionId>42ce9db5-c1c3-4a9a-83b8-4d66eaa03449</versionId>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.97695896-334f-49db-9f6a-c6764e9bcda3</parameterMappingId>
                    <processParameterId>2055.c60b2a07-f8cb-402a-958b-7a1bd44b7358</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerNo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>90db14f9-c1e6-4444-8c3f-b9ef7342de47</guid>
                    <versionId>18d6305c-3fc4-4808-beed-1c417891af29</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8f7f9c72-3b1e-417b-bda2-9cd3da3f1f7d</parameterMappingId>
                    <processParameterId>2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>43e3f566-4cd2-44a8-af25-729ff6909372</guid>
                    <versionId>23041835-c199-4da9-a139-567f4a87eed4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5c6fe3e0-aa04-46c7-9774-bbb1b76d0e8f</parameterMappingId>
                    <processParameterId>2055.2f75a761-4364-4c36-b175-6c50df867d38</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b30ba164-484d-4384-980b-141a4e161c81</guid>
                    <versionId>2b713a00-4a31-480e-8061-14e1b83fe303</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a4b552c7-1e65-4094-a299-b60c0f3bf56a</parameterMappingId>
                    <processParameterId>2055.6a8c1cae-f653-4cbe-a332-230489206d39</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2e1dbc72-6306-490f-aee3-4100e979ee22</guid>
                    <versionId>38e49358-89ce-4d4d-8175-fc9d77641c7e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="accountsList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.932cdc0e-b076-4b22-b043-616bcdbd0154</parameterMappingId>
                    <processParameterId>2055.cdb1e363-7e09-4015-8916-b750b411405d</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.accountsList</value>
                    <classRef>/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>1dc5c2dc-0fc8-41fc-8ee7-4ff28daf3492</guid>
                    <versionId>63d6a537-1ec4-4915-bf8f-3a66e5b66c5b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.df1465cb-f917-427e-9f25-507592112f18</parameterMappingId>
                    <processParameterId>2055.f3935930-ab2f-433c-8bb0-566cbabffe45</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerNo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6bb073df-b58d-4421-941c-44f87f50a840</guid>
                    <versionId>647d04aa-64ed-4028-89dc-4caaa14e9b95</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c336df65-8da9-466f-aa5a-12837eb5d0b2</parameterMappingId>
                    <processParameterId>2055.98ee7d56-00f0-4503-9aa9-a320f73a3361</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6da77760-86e0-4b10-89fa-d4d799e92459</guid>
                    <versionId>74177d43-afb5-4f45-8222-ca38910473c2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b7042d69-5a15-4aac-bdd4-62e389078b86</parameterMappingId>
                    <processParameterId>2055.ca98719d-77f8-4fde-9663-52914ee14551</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>eca72629-2811-4c77-a0f9-54c132d59a6f</guid>
                    <versionId>7fdc48e4-2fc3-421d-a9bc-379626679b66</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7acf4c20-b161-48d1-b7b8-b54d277a37d4</parameterMappingId>
                    <processParameterId>2055.d9d57b1e-9356-4664-87b3-27347adfe41d</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c747a032-e70d-467d-97df-21329f04a6ee</guid>
                    <versionId>90f9ab28-d16b-4696-8ea7-3efbadaba3ea</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bd369658-d6ff-4a85-bb0c-e5c4f86175ce</parameterMappingId>
                    <processParameterId>2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0bdbb590-cf08-4498-a02b-1f0b451f796d</guid>
                    <versionId>b676dc82-5b06-48cf-ab1c-4635b8855a3d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b3b22116-137f-474b-846b-b205ff5aeb5a</parameterMappingId>
                    <processParameterId>2055.b7a97c7d-99cb-44db-827f-0333fa136b3e</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1f4758bb-2af9-48c4-b84e-9478deaf61b3</guid>
                    <versionId>bd89711e-3d25-45fd-89eb-35a3c47c1a55</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.be50d17e-3bb6-4ac6-a8f7-8e553bd95d3f</parameterMappingId>
                    <processParameterId>2055.e8bee736-2a93-4760-aafb-38e55006f6b9</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bd0ce914-dd45-456b-8ee3-ba843525d82f</guid>
                    <versionId>de91a2cf-79b9-4b15-9a25-b3602f74198c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fe9f6694-35cb-4368-b6d8-70c11d338f93</parameterMappingId>
                    <processParameterId>2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a</processParameterId>
                    <parameterMappingParentId>3012.c0f5ac96-79a6-4af0-8af2-25f3f196d66c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>71183f86-69f7-436e-ab08-18eaea2e246b</guid>
                    <versionId>fce42e4f-11c0-4ab1-996d-f6303732b434</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cce9b0ca-bd70-4873-8549-1fc716f29cca</processItemId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bf8eb401-ac83-40e6-a5fd-e0d4248da45d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5aca</guid>
            <versionId>74f970bd-22a2-40c4-8149-64c1a3c5d6e5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="560" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d41</errorHandlerItem>
                <errorHandlerItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bf8eb401-ac83-40e6-a5fd-e0d4248da45d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try {	&#xD;
	tw.local.exist = new tw.object.listOf.Boolean();&#xD;
	for (var j=0; j&lt;tw.local.accountNumber.listLength; j++) {&#xD;
		tw.local.exist[j] = false;&#xD;
		for (var i=0; i&lt;tw.local.accountsList.listLength; i++) {&#xD;
			if (tw.local.accountsList[i].accountNO == tw.local.accountNumber[j]) {&#xD;
				tw.local.exist[j] = true;&#xD;
				break;&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>eecf17d1-7325-464f-b9b4-7690617aba02</guid>
                <versionId>c73eacdd-c0b6-4304-9115-55981a8db33c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</processItemId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.67817a15-58e9-42ef-ba1e-e9146e3f717b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d41</guid>
            <versionId>b12ebb77-365e-4a8b-9bb7-58f9fb99f0f2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="407" y="175">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.67817a15-58e9-42ef-ba1e-e9146e3f717b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>d9f8fcd1-a8f4-49a4-9cf8-7c512eeff257</guid>
                <versionId>dd13b6a7-9e19-478e-bf79-d8484ec4196e</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Customer Accounts" id="1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="customerNo" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.71fbef00-98c0-4292-b408-1d388b207e97">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"********"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="accountNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" id="2055.b0a2409e-2c4c-4fda-8b13-bcb762ed4910">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="exist" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" id="2055.********-f45d-49b1-af20-ad99fd5a03f5" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.ef381658-b8ee-42f5-8da7-ec74e0e2946b" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.71fbef00-98c0-4292-b408-1d388b207e97</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b0a2409e-2c4c-4fda-8b13-bcb762ed4910</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.********-f45d-49b1-af20-ad99fd5a03f5</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.ef381658-b8ee-42f5-8da7-ec74e0e2946b</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="023203bc-5287-411b-a19e-13a824bf23bb">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="6bb4595f-6bfa-4a22-a068-299edd5d51a3" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f6b48925-97ef-46b5-8744-b375731f46ce</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>641e8339-755c-4ec6-8c07-2b46def4309d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ca3c58ee-e162-4e59-8dae-c2a009678041</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cce9b0ca-bd70-4873-8549-1fc716f29cca</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2b427649-fdb6-48ee-88cb-63fc54157f02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9f08397c-599f-4c8a-898a-dc9e1bd3cd97</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f842817c-4760-4c7f-8aab-42cbb2c06c5e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f6b48925-97ef-46b5-8744-b375731f46ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.27975ea8-5107-442a-803c-e16de82df45a</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="641e8339-755c-4ec6-8c07-2b46def4309d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="720" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ac8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>772d0975-bef1-4cb5-9d8d-dc90cb9db56d</ns16:incoming>
                        
                        
                        <ns16:incoming>a6dd595b-b976-4a1c-8f9f-1e0085174047</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f6b48925-97ef-46b5-8744-b375731f46ce" targetRef="ca3c58ee-e162-4e59-8dae-c2a009678041" name="To MW_FC Retrieve Customer Accounts" id="2027.27975ea8-5107-442a-803c-e16de82df45a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.75ff304b-c398-4669-9ed0-70ab03152e8d" name="MW_FC Retrieve Customer Accounts" id="ca3c58ee-e162-4e59-8dae-c2a009678041">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="270" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.27975ea8-5107-442a-803c-e16de82df45a</ns16:incoming>
                        
                        
                        <ns16:outgoing>6308f4c0-5792-4a06-bff1-408ccc6b1382</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b7a97c7d-99cb-44db-827f-0333fa136b3e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e8bee736-2a93-4760-aafb-38e55006f6b9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f3935930-ab2f-433c-8bb0-566cbabffe45</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.customerNo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d9d57b1e-9356-4664-87b3-27347adfe41d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ca98719d-77f8-4fde-9663-52914ee14551</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c60b2a07-f8cb-402a-958b-7a1bd44b7358</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.customerNo</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.cdb1e363-7e09-4015-8916-b750b411405d</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42">tw.local.accountsList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.98ee7d56-00f0-4503-9aa9-a320f73a3361</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.6a8c1cae-f653-4cbe-a332-230489206d39</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2f75a761-4364-4c36-b175-6c50df867d38</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="ca3c58ee-e162-4e59-8dae-c2a009678041" targetRef="9f08397c-599f-4c8a-898a-dc9e1bd3cd97" name="To is Successful ?" id="6308f4c0-5792-4a06-bff1-408ccc6b1382">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a69</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.1be95a5e-5da2-494e-b27f-8bdff21cc640">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();
autoObject[0] = new tw.object.toolkit.NBEINTT.Account();
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.0fa17601-3f45-4ba3-bb48-c9c75070cd3f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.8f4bf7a2-faab-4c9a-91c9-83cd30213f72" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.5a73e115-4b06-41e7-9167-df5d174f1e77" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="status" id="2056.93865fe6-0f94-4a87-a7dc-4dc12cf28eac" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="cce9b0ca-bd70-4873-8549-1fc716f29cca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="560" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e86a56f4-c03a-4267-8da1-062b3a610f8f</ns16:incoming>
                        
                        
                        <ns16:outgoing>772d0975-bef1-4cb5-9d8d-dc90cb9db56d</ns16:outgoing>
                        
                        
                        <ns16:script>try {	&#xD;
	tw.local.exist = new tw.object.listOf.Boolean();&#xD;
	for (var j=0; j&lt;tw.local.accountNumber.listLength; j++) {&#xD;
		tw.local.exist[j] = false;&#xD;
		for (var i=0; i&lt;tw.local.accountsList.listLength; i++) {&#xD;
			if (tw.local.accountsList[i].accountNO == tw.local.accountNumber[j]) {&#xD;
				tw.local.exist[j] = true;&#xD;
				break;&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="cce9b0ca-bd70-4873-8549-1fc716f29cca" targetRef="641e8339-755c-4ec6-8c07-2b46def4309d" name="To End" id="772d0975-bef1-4cb5-9d8d-dc90cb9db56d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ca3c58ee-e162-4e59-8dae-c2a009678041" parallelMultiple="false" name="Error" id="2b427649-fdb6-48ee-88cb-63fc54157f02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="305" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c2ae5b57-297c-4c2f-8663-87fb7d637284</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="049da4f0-4d57-423b-8621-4cdf6a734802" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5ee19dc4-1597-4292-803e-fbe0837bee24" eventImplId="17fbf929-41ea-49aa-8e2d-19f0a19f569c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:exclusiveGateway default="e86a56f4-c03a-4267-8da1-062b3a610f8f" name="is Successful ?" id="9f08397c-599f-4c8a-898a-dc9e1bd3cd97">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="401" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6308f4c0-5792-4a06-bff1-408ccc6b1382</ns16:incoming>
                        
                        
                        <ns16:outgoing>e86a56f4-c03a-4267-8da1-062b3a610f8f</ns16:outgoing>
                        
                        
                        <ns16:outgoing>fa27d522-0d84-4315-88d7-ec3e979778e5</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="9f08397c-599f-4c8a-898a-dc9e1bd3cd97" targetRef="cce9b0ca-bd70-4873-8549-1fc716f29cca" name="To Script Task" id="e86a56f4-c03a-4267-8da1-062b3a610f8f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="cce9b0ca-bd70-4873-8549-1fc716f29cca" parallelMultiple="false" name="Error1" id="f842817c-4760-4c7f-8aab-42cbb2c06c5e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="595" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c7127770-a266-49e5-8aa9-60ff11e33552</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="54414878-c122-4c4f-832a-274c80cf86e5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4e3e8341-a76b-4d53-8fd0-b58dfc5a22e9" eventImplId="d8d4cc16-10ca-4a68-86ac-a41d80eb4cad">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="407" y="175" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c2ae5b57-297c-4c2f-8663-87fb7d637284</ns16:incoming>
                        
                        
                        <ns16:incoming>fa27d522-0d84-4315-88d7-ec3e979778e5</ns16:incoming>
                        
                        
                        <ns16:incoming>c7127770-a266-49e5-8aa9-60ff11e33552</ns16:incoming>
                        
                        
                        <ns16:outgoing>a6dd595b-b976-4a1c-8f9f-1e0085174047</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2b427649-fdb6-48ee-88cb-63fc54157f02" targetRef="7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af" name="To Catch Errors" id="c2ae5b57-297c-4c2f-8663-87fb7d637284">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9f08397c-599f-4c8a-898a-dc9e1bd3cd97" targetRef="7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af" name="To Catch Errors" id="fa27d522-0d84-4315-88d7-ec3e979778e5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f842817c-4760-4c7f-8aab-42cbb2c06c5e" targetRef="7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af" name="To Catch Errors" id="c7127770-a266-49e5-8aa9-60ff11e33552">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af" targetRef="641e8339-755c-4ec6-8c07-2b46def4309d" name="To End" id="a6dd595b-b976-4a1c-8f9f-1e0085174047">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.772d0975-bef1-4cb5-9d8d-dc90cb9db56d</processLinkId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cce9b0ca-bd70-4873-8549-1fc716f29cca</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.641e8339-755c-4ec6-8c07-2b46def4309d</toProcessItemId>
            <guid>0594f201-3db7-4119-bc21-557d2f297ffd</guid>
            <versionId>7be77f63-b8f9-4918-a9ab-f7d4ebbdb9e8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.cce9b0ca-bd70-4873-8549-1fc716f29cca</fromProcessItemId>
            <toProcessItemId>2025.641e8339-755c-4ec6-8c07-2b46def4309d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a6dd595b-b976-4a1c-8f9f-1e0085174047</processLinkId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.641e8339-755c-4ec6-8c07-2b46def4309d</toProcessItemId>
            <guid>7eeb28c3-f2d8-433a-aa79-64c064601999</guid>
            <versionId>844355c5-6757-43ee-8ed4-79d6a4a789da</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</fromProcessItemId>
            <toProcessItemId>2025.641e8339-755c-4ec6-8c07-2b46def4309d</toProcessItemId>
        </link>
        <link name="To Catch Errors">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fa27d522-0d84-4315-88d7-ec3e979778e5</processLinkId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d60</endStateId>
            <toProcessItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</toProcessItemId>
            <guid>6c4a9c3c-cf3c-4bcb-a867-cdfc4fe5a4a0</guid>
            <versionId>ab1274d7-3139-4a5c-a432-e4a2b2a7d7d3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</fromProcessItemId>
            <toProcessItemId>2025.7a5a16a5-9cb2-4ff1-8b82-a35a00cc33af</toProcessItemId>
        </link>
        <link name="To is Successful ?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6308f4c0-5792-4a06-bff1-408ccc6b1382</processLinkId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a69</endStateId>
            <toProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</toProcessItemId>
            <guid>1678093f-875a-4be5-91f7-d49200cbceca</guid>
            <versionId>b31f4f53-7afd-4d2c-982e-0fc1f9a10be6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ca3c58ee-e162-4e59-8dae-c2a009678041</fromProcessItemId>
            <toProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e86a56f4-c03a-4267-8da1-062b3a610f8f</processLinkId>
            <processId>1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.cce9b0ca-bd70-4873-8549-1fc716f29cca</toProcessItemId>
            <guid>d83d7912-e2e3-4d94-bf35-e24a9ce13136</guid>
            <versionId>c011b314-e51c-412c-8fe8-97f495ea8480</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9f08397c-599f-4c8a-898a-dc9e1bd3cd97</fromProcessItemId>
            <toProcessItemId>2025.cce9b0ca-bd70-4873-8549-1fc716f29cca</toProcessItemId>
        </link>
    </process>
</teamworks>

