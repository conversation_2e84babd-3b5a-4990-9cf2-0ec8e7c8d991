<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541" name="Get Booked Facilities">
        <lastModified>1692703791316</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:-3036</guid>
        <versionId>e65c6a95-f84a-4e05-8ebb-09058fcf8781</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-1cb4" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.be1bc16b-01bc-4fec-87a1-b390228deca9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d"},{"incoming":["ab6830e6-d84d-4ab3-8fa0-315988d1991e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:-3034"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"475c242c-ee52-40af-8210-83d649f0f1cb"},{"targetRef":"7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To INIT SQL","declaredType":"sequenceFlow","id":"2027.be1bc16b-01bc-4fec-87a1-b390228deca9","sourceRef":"39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d"},{"startQuantity":1,"outgoing":["3bace9e7-2dcd-4deb-8d1c-c8b6e556910b"],"incoming":["2027.be1bc16b-01bc-4fec-87a1-b390228deca9"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":167,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"INIT SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.BookedFac = new tw.object.listOf.BookedFacility();\r\n\/\/tw.local.BookedFac[0] = {};\r\n\/\/tw.local.BookedFac[0].cashCover = 22;\r\n\/\/tw.local.BookedFac[0].customerCIF = tw.local.CIF;\r\n\/\/tw.local.BookedFac[0].customerName = \"Mohamed\";\r\n\/\/tw.local.BookedFac[0].facilityCode = \"A2222222222222222222222222222222222222\";\r\n\/\/tw.local.BookedFac[0].facilityNumber = \"22\";\r\n\/\/tw.local.BookedFac[0].instanceNumber = \"22611\";\r\n\/\/tw.local.BookedFac[0].LCAmount = 33; \r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"SELECT distinct f.FACILITY_ID,f.LINE_CODE,f.IDC_REQUEST_ID ,c.CIF,c.CUSTOMER_NAME, d.AMOUNT_PAYABLE_BY_NBE ,f.FACILITY_PERCENTAGE_TO_BOOK, d.CURRENCY FROM BPM.IDC_FACILITY_DETAILS f INNER JOIN BPM.IDC_CUSTOMER_INFORMATION c on f.IDC_REQUEST_ID = c.IDC_REQUEST_ID INNER JOIN BPM.IDC_REQUEST_DETAILS d on c.IDC_REQUEST_ID = d.ID WHERE c.CIF = ? AND (d.REQUEST_STATUS != 'Terminated' OR d.REQUEST_STATUS != 'Canceled' OR d.REQUEST_STATUS != 'Completed') ;\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.CIF);\r\n"]}},{"targetRef":"53bb17fc-ac3e-44f1-8b76-0609cb47eb38","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"3bace9e7-2dcd-4deb-8d1c-c8b6e556910b","sourceRef":"7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f"},{"startQuantity":1,"outgoing":["2f97a63f-1f70-43f7-8cec-337873d6e5c0"],"incoming":["3bace9e7-2dcd-4deb-8d1c-c8b6e556910b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":336,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlStatements[0].parameters"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sqlStatements[0].sql"]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]},{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"53bb17fc-ac3e-44f1-8b76-0609cb47eb38","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.SQLResult"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"3f4dab0d-2662-4de7-82f8-45d72a9885fc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Data Maping","declaredType":"sequenceFlow","id":"2f97a63f-1f70-43f7-8cec-337873d6e5c0","sourceRef":"53bb17fc-ac3e-44f1-8b76-0609cb47eb38"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.e95be18c-eead-45f0-86a6-4c42d7c8d83f"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"SQLResult","isCollection":true,"declaredType":"dataObject","id":"2056.88e476ae-6dce-4c44-8deb-8231c59d38ab"},{"startQuantity":1,"outgoing":["ab6830e6-d84d-4ab3-8fa0-315988d1991e"],"incoming":["2f97a63f-1f70-43f7-8cec-337873d6e5c0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":469,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Data Maping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3f4dab0d-2662-4de7-82f8-45d72a9885fc","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.BookedFac = new tw.object.listOf.IDCBookedFacility();\r\nif (tw.local.SQLResult[0].rows.listLength&gt;0) {\r\n\tfor (var i=0; i&lt;tw.local.SQLResult[0].rows.listLength; i++) {\r\n\t\ttw.local.BookedFac[i] = new tw.object.IDCBookedFacility();\r\n\t\ttw.local.BookedFac[i].facilityNumber = \"\"+tw.local.SQLResult[0].rows[i].data[0];\r\n\t\ttw.local.BookedFac[i].facilityCode = \"\"+tw.local.SQLResult[0].rows[i].data[1];\r\n\t\ttw.local.BookedFac[i].instanceNumber = \"\"+tw.local.SQLResult[0].rows[i].data[2];\r\n\t\ttw.local.BookedFac[i].customerCIF = \"\"+tw.local.SQLResult[0].rows[i].data[3];\r\n\t\ttw.local.BookedFac[i].customerName = \"\"+tw.local.SQLResult[0].rows[i].data[4];\r\n\t\ttw.local.BookedFac[i].LCAmount = tw.local.SQLResult[0].rows[i].data[5];\r\n\t\ttw.local.BookedFac[i].cashCover = tw.local.SQLResult[0].rows[i].data[6];\r\n\t\ttw.local.BookedFac[i].currency = tw.local.SQLResult[0].rows[i].data[7];\r\n\t}\r\n}"]}},{"targetRef":"475c242c-ee52-40af-8210-83d649f0f1cb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"ab6830e6-d84d-4ab3-8fa0-315988d1991e","sourceRef":"3f4dab0d-2662-4de7-82f8-45d72a9885fc"}],"laneSet":[{"id":"687447a6-91fc-42e6-82ab-7076b2129bc2","lane":[{"flowNodeRef":["39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d","475c242c-ee52-40af-8210-83d649f0f1cb","7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f","53bb17fc-ac3e-44f1-8b76-0609cb47eb38","3f4dab0d-2662-4de7-82f8-45d72a9885fc"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"3510388b-1393-4d29-83f9-b22a4584becb","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Booked Facilities","declaredType":"process","id":"1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.e9597b68-6c68-4101-ab1c-d6f8af32c79d","name":"BookedFac","isCollection":true,"id":"2055.d34b87d3-c15b-4b59-891d-80565e71154c"}],"inputSet":[{"dataInputRefs":["2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1"]}],"outputSet":[{"dataOutputRefs":["2055.d34b87d3-c15b-4b59-891d-80565e71154c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"06316421\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"CIF","isCollection":false,"id":"2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1</processParameterId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"06316421"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>db271288-c9e2-4b00-b6bd-bb033806312c</guid>
            <versionId>ebd21872-8dbb-47a4-9670-75f0fa98558b</versionId>
        </processParameter>
        <processParameter name="BookedFac">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d34b87d3-c15b-4b59-891d-80565e71154c</processParameterId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e9597b68-6c68-4101-ab1c-d6f8af32c79d</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>82e5d069-d45e-4a6a-95a9-67c014a1d158</guid>
            <versionId>67b02708-b39b-4a14-8187-d98b0f5e8679</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e95be18c-eead-45f0-86a6-4c42d7c8d83f</processVariableId>
            <description isNull="true" />
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2b36bb8c-38a8-4e2c-bf36-611097e84f0b</guid>
            <versionId>21056d38-cd85-4ae4-8595-cedd8fe1499d</versionId>
        </processVariable>
        <processVariable name="SQLResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.88e476ae-6dce-4c44-8deb-8231c59d38ab</processVariableId>
            <description isNull="true" />
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5aa529a-2eae-4119-8ac6-924698ef5dc4</guid>
            <versionId>648751c8-6628-4284-9d13-157c4370ae49</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</processItemId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <name>INIT SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0250f5fe-3576-4ad2-960f-1842d184f6db</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:-3032</guid>
            <versionId>066cfd6e-ca51-4bf0-b685-db8099a57b38</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="167" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0250f5fe-3576-4ad2-960f-1842d184f6db</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.BookedFac = new tw.object.listOf.BookedFacility();&#xD;
//tw.local.BookedFac[0] = {};&#xD;
//tw.local.BookedFac[0].cashCover = 22;&#xD;
//tw.local.BookedFac[0].customerCIF = tw.local.CIF;&#xD;
//tw.local.BookedFac[0].customerName = "Mohamed";&#xD;
//tw.local.BookedFac[0].facilityCode = "A2222222222222222222222222222222222222";&#xD;
//tw.local.BookedFac[0].facilityNumber = "22";&#xD;
//tw.local.BookedFac[0].instanceNumber = "22611";&#xD;
//tw.local.BookedFac[0].LCAmount = 33; &#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT distinct f.FACILITY_ID,f.LINE_CODE,f.IDC_REQUEST_ID ,c.CIF,c.CUSTOMER_NAME, d.AMOUNT_PAYABLE_BY_NBE ,f.FACILITY_PERCENTAGE_TO_BOOK, d.CURRENCY FROM BPM.IDC_FACILITY_DETAILS f INNER JOIN BPM.IDC_CUSTOMER_INFORMATION c on f.IDC_REQUEST_ID = c.IDC_REQUEST_ID INNER JOIN BPM.IDC_REQUEST_DETAILS d on c.IDC_REQUEST_ID = d.ID WHERE c.CIF = ? AND (d.REQUEST_STATUS != 'Terminated' OR d.REQUEST_STATUS != 'Canceled' OR d.REQUEST_STATUS != 'Completed') ;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.CIF);&#xD;
</script>
                <isRule>false</isRule>
                <guid>891a949d-4d5c-409f-b1f1-01022da1eea5</guid>
                <versionId>163fdcab-2021-47cc-a9f2-b6095062efd2</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.475c242c-ee52-40af-8210-83d649f0f1cb</processItemId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.7c987a03-5be2-4113-a048-6d77a6251ae7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:-3034</guid>
            <versionId>4e2ee234-dcbb-40e0-8ba4-a0d45354e86a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.7c987a03-5be2-4113-a048-6d77a6251ae7</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>e884d5c8-c05f-433e-8d79-28ace0fcf92e</guid>
                <versionId>5fbe1b91-3c55-4d39-ac15-411929a0f099</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3f4dab0d-2662-4de7-82f8-45d72a9885fc</processItemId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <name>Data Maping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.eafec4f2-33f5-44ae-9ff0-ca755ceb1abd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a18875745:-846</guid>
            <versionId>72a809ad-2ff1-4495-8d86-e45b32dc71e8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="469" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.eafec4f2-33f5-44ae-9ff0-ca755ceb1abd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.BookedFac = new tw.object.listOf.IDCBookedFacility();&#xD;
if (tw.local.SQLResult[0].rows.listLength&gt;0) {&#xD;
	for (var i=0; i&lt;tw.local.SQLResult[0].rows.listLength; i++) {&#xD;
		tw.local.BookedFac[i] = new tw.object.IDCBookedFacility();&#xD;
		tw.local.BookedFac[i].facilityNumber = ""+tw.local.SQLResult[0].rows[i].data[0];&#xD;
		tw.local.BookedFac[i].facilityCode = ""+tw.local.SQLResult[0].rows[i].data[1];&#xD;
		tw.local.BookedFac[i].instanceNumber = ""+tw.local.SQLResult[0].rows[i].data[2];&#xD;
		tw.local.BookedFac[i].customerCIF = ""+tw.local.SQLResult[0].rows[i].data[3];&#xD;
		tw.local.BookedFac[i].customerName = ""+tw.local.SQLResult[0].rows[i].data[4];&#xD;
		tw.local.BookedFac[i].LCAmount = tw.local.SQLResult[0].rows[i].data[5];&#xD;
		tw.local.BookedFac[i].cashCover = tw.local.SQLResult[0].rows[i].data[6];&#xD;
		tw.local.BookedFac[i].currency = tw.local.SQLResult[0].rows[i].data[7];&#xD;
	}&#xD;
}</script>
                <isRule>false</isRule>
                <guid>c03cc44c-c9af-44f5-b105-4cbf6962a5f2</guid>
                <versionId>8d6d9e42-86f5-4a54-ba62-05c35c608a86</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.53bb17fc-ac3e-44f1-8b76-0609cb47eb38</processItemId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a18875745:-c6e</guid>
            <versionId>abd19926-cbbe-40c2-8798-050859c29ef1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="336" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>2df443a6-d362-4830-85f8-287c984639cb</guid>
                <versionId>b44d844f-5851-4ab5-926c-ae4570ae3788</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.467d59d3-6fa8-43cd-820d-4551eb8452ec</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.SQLResult</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2db8d8c0-7ea1-4a49-a599-929c3797d4e9</guid>
                    <versionId>0e32302d-b2aa-4ff2-b01f-501fbce1d16d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fe2cefb1-4f02-40be-8795-6c3c28ddc6e4</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7c99b408-dda9-4a80-a1f5-35be6fa0590f</guid>
                    <versionId>3fb0be97-b87c-4e03-a9f6-b04c5129cd23</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.91cd961b-e1b4-4d02-ba2b-f1ac81ee8118</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements[0].parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a56674da-41c0-4148-a781-6d372db3368e</guid>
                    <versionId>73c25018-a8af-4817-b866-92de2b3cbfb8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6d7f8ef9-f3a8-402d-aaf0-d247ddfef4af</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f9567147-6450-416f-a6d1-c00ac9e49a2d</guid>
                    <versionId>96f201c9-929a-4ee7-80d1-0de45f2ace58</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f0ee1ac3-476f-4f87-b5dd-17f4bdca2531</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c49b323a-74e0-4e57-aa0d-525a0de9eac7</guid>
                    <versionId>a4578d18-a6a2-40ff-add6-c29c805ec062</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c4995bd2-cb5a-4970-a9ba-2a45a760f5a9</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.dfbd9df0-9743-467b-be1f-52baca510d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements[0].sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0490758e-cb68-4024-bdb0-1868fe8f32ee</guid>
                    <versionId>e1b64049-fd5b-4c9e-878b-08bdff4477d5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Booked Facilities" id="1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"06316421"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="BookedFac" itemSubjectRef="itm.12.e9597b68-6c68-4101-ab1c-d6f8af32c79d" isCollection="true" id="2055.d34b87d3-c15b-4b59-891d-80565e71154c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.d34b87d3-c15b-4b59-891d-80565e71154c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="687447a6-91fc-42e6-82ab-7076b2129bc2">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="3510388b-1393-4d29-83f9-b22a4584becb" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>475c242c-ee52-40af-8210-83d649f0f1cb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>53bb17fc-ac3e-44f1-8b76-0609cb47eb38</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3f4dab0d-2662-4de7-82f8-45d72a9885fc</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.be1bc16b-01bc-4fec-87a1-b390228deca9</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="475c242c-ee52-40af-8210-83d649f0f1cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:-3034</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ab6830e6-d84d-4ab3-8fa0-315988d1991e</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="39f9d69f-f7a5-4517-8fdc-9ac3fe9c7d4d" targetRef="7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f" name="To INIT SQL" id="2027.be1bc16b-01bc-4fec-87a1-b390228deca9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="INIT SQL" id="7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="167" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.be1bc16b-01bc-4fec-87a1-b390228deca9</ns16:incoming>
                        
                        
                        <ns16:outgoing>3bace9e7-2dcd-4deb-8d1c-c8b6e556910b</ns16:outgoing>
                        
                        
                        <ns16:script>//tw.local.BookedFac = new tw.object.listOf.BookedFacility();&#xD;
//tw.local.BookedFac[0] = {};&#xD;
//tw.local.BookedFac[0].cashCover = 22;&#xD;
//tw.local.BookedFac[0].customerCIF = tw.local.CIF;&#xD;
//tw.local.BookedFac[0].customerName = "Mohamed";&#xD;
//tw.local.BookedFac[0].facilityCode = "A2222222222222222222222222222222222222";&#xD;
//tw.local.BookedFac[0].facilityNumber = "22";&#xD;
//tw.local.BookedFac[0].instanceNumber = "22611";&#xD;
//tw.local.BookedFac[0].LCAmount = 33; &#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT distinct f.FACILITY_ID,f.LINE_CODE,f.IDC_REQUEST_ID ,c.CIF,c.CUSTOMER_NAME, d.AMOUNT_PAYABLE_BY_NBE ,f.FACILITY_PERCENTAGE_TO_BOOK, d.CURRENCY FROM BPM.IDC_FACILITY_DETAILS f INNER JOIN BPM.IDC_CUSTOMER_INFORMATION c on f.IDC_REQUEST_ID = c.IDC_REQUEST_ID INNER JOIN BPM.IDC_REQUEST_DETAILS d on c.IDC_REQUEST_ID = d.ID WHERE c.CIF = ? AND (d.REQUEST_STATUS != 'Terminated' OR d.REQUEST_STATUS != 'Canceled' OR d.REQUEST_STATUS != 'Completed') ;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.CIF);&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f" targetRef="53bb17fc-ac3e-44f1-8b76-0609cb47eb38" name="To SQL Execute Statement" id="3bace9e7-2dcd-4deb-8d1c-c8b6e556910b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="53bb17fc-ac3e-44f1-8b76-0609cb47eb38">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="336" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3bace9e7-2dcd-4deb-8d1c-c8b6e556910b</ns16:incoming>
                        
                        
                        <ns16:outgoing>2f97a63f-1f70-43f7-8cec-337873d6e5c0</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlStatements[0].parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sqlStatements[0].sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.SQLResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="53bb17fc-ac3e-44f1-8b76-0609cb47eb38" targetRef="3f4dab0d-2662-4de7-82f8-45d72a9885fc" name="To Data Maping" id="2f97a63f-1f70-43f7-8cec-337873d6e5c0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.e95be18c-eead-45f0-86a6-4c42d7c8d83f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="SQLResult" id="2056.88e476ae-6dce-4c44-8deb-8231c59d38ab" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Data Maping" id="3f4dab0d-2662-4de7-82f8-45d72a9885fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="469" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2f97a63f-1f70-43f7-8cec-337873d6e5c0</ns16:incoming>
                        
                        
                        <ns16:outgoing>ab6830e6-d84d-4ab3-8fa0-315988d1991e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.BookedFac = new tw.object.listOf.IDCBookedFacility();&#xD;
if (tw.local.SQLResult[0].rows.listLength&gt;0) {&#xD;
	for (var i=0; i&lt;tw.local.SQLResult[0].rows.listLength; i++) {&#xD;
		tw.local.BookedFac[i] = new tw.object.IDCBookedFacility();&#xD;
		tw.local.BookedFac[i].facilityNumber = ""+tw.local.SQLResult[0].rows[i].data[0];&#xD;
		tw.local.BookedFac[i].facilityCode = ""+tw.local.SQLResult[0].rows[i].data[1];&#xD;
		tw.local.BookedFac[i].instanceNumber = ""+tw.local.SQLResult[0].rows[i].data[2];&#xD;
		tw.local.BookedFac[i].customerCIF = ""+tw.local.SQLResult[0].rows[i].data[3];&#xD;
		tw.local.BookedFac[i].customerName = ""+tw.local.SQLResult[0].rows[i].data[4];&#xD;
		tw.local.BookedFac[i].LCAmount = tw.local.SQLResult[0].rows[i].data[5];&#xD;
		tw.local.BookedFac[i].cashCover = tw.local.SQLResult[0].rows[i].data[6];&#xD;
		tw.local.BookedFac[i].currency = tw.local.SQLResult[0].rows[i].data[7];&#xD;
	}&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3f4dab0d-2662-4de7-82f8-45d72a9885fc" targetRef="475c242c-ee52-40af-8210-83d649f0f1cb" name="To End" id="ab6830e6-d84d-4ab3-8fa0-315988d1991e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3bace9e7-2dcd-4deb-8d1c-c8b6e556910b</processLinkId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.53bb17fc-ac3e-44f1-8b76-0609cb47eb38</toProcessItemId>
            <guid>aa15227f-3323-42aa-9daf-47a825509397</guid>
            <versionId>0298aef9-2091-4999-ac30-6de98208d439</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7c37ce0c-4786-426d-8bd8-5e20e3fd9b1f</fromProcessItemId>
            <toProcessItemId>2025.53bb17fc-ac3e-44f1-8b76-0609cb47eb38</toProcessItemId>
        </link>
        <link name="To Data Maping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2f97a63f-1f70-43f7-8cec-337873d6e5c0</processLinkId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.53bb17fc-ac3e-44f1-8b76-0609cb47eb38</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.3f4dab0d-2662-4de7-82f8-45d72a9885fc</toProcessItemId>
            <guid>a0d0bdf7-aaa4-4bac-83f4-00feada28270</guid>
            <versionId>4696c731-fdc9-48f1-9838-3f8c3b92ffa4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.53bb17fc-ac3e-44f1-8b76-0609cb47eb38</fromProcessItemId>
            <toProcessItemId>2025.3f4dab0d-2662-4de7-82f8-45d72a9885fc</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ab6830e6-d84d-4ab3-8fa0-315988d1991e</processLinkId>
            <processId>1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3f4dab0d-2662-4de7-82f8-45d72a9885fc</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.475c242c-ee52-40af-8210-83d649f0f1cb</toProcessItemId>
            <guid>d3894471-bbed-4725-8cbe-227b751074c0</guid>
            <versionId>61b7a85e-bdbf-42f3-a786-a3c6241fa70f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3f4dab0d-2662-4de7-82f8-45d72a9885fc</fromProcessItemId>
            <toProcessItemId>2025.475c242c-ee52-40af-8210-83d649f0f1cb</toProcessItemId>
        </link>
    </process>
</teamworks>

