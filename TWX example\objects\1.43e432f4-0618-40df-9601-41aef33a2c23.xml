<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.43e432f4-0618-40df-9601-41aef33a2c23" name="Approve Request by Credit Admin Execution Checker">
        <lastModified>1692723517700</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.06b81167-2c1f-4b90-b961-4335fec4830c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>d090cde9-a480-45a6-8e7d-e74be804b733</guid>
        <versionId>f7acd706-a1f7-47c0-9bd1-7c96fd6e4a14</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.87c5a589-7542-410f-b3b8-b98da4ee4beb"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":49,"y":86,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"75280f93-4ee2-4fc0-91ad-b640114d2348"},{"incoming":["2027.8641c8e3-110f-4c01-81fc-4b57128c2454"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":976,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"94758e37-d036-4fab-928c-b0cb84945b85"},{"targetRef":"2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Approve Request by Credit Admin Execution Checker","declaredType":"sequenceFlow","id":"2027.87c5a589-7542-410f-b3b8-b98da4ee4beb","sourceRef":"75280f93-4ee2-4fc0-91ad-b640114d2348"},{"startQuantity":1,"outgoing":["2027.793a03d1-6ff5-4d83-a376-a478632062ec"],"incoming":["2027.0092cc35-5d59-4e0a-ab02-053309cdae9f"],"default":"2027.793a03d1-6ff5-4d83-a376-a478632062ec","extensionElements":{"nodeVisualInfo":[{"width":95,"x":573,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.ba269e33-924c-422e-9a8f-8364a6f8647f","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.selectedAction == tw.epv.Action.returnToMaker) {\r\n\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCADExecutionMakerUpdate;\r\n\t\r\n} else if (tw.local.selectedAction == tw.epv.Action.SendtoInvestigation) {\r\n\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingQueueCADExecution;\r\n\t\r\n}  else {\r\n\tif(tw.local.intiator == \"fo\"){\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;\r\n\t}else if (tw.local.intiator == \"Initiation\") {\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;\r\n\t}else{\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;\r\n\t}\r\n}\r\n\r\n\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To update facility","declaredType":"sequenceFlow","id":"2027.793a03d1-6ff5-4d83-a376-a478632062ec","sourceRef":"2025.ba269e33-924c-422e-9a8f-8364a6f8647f"},{"outgoing":["2027.c65ce168-3373-4392-aa04-ee87ab3ee751","2027.b49431e6-c476-4978-939b-63f24800b0c8","2027.8fb55bf7-97d8-4232-8093-d6d101733dcc"],"incoming":["2027.69544ee6-bbac-4983-a935-70cd38a88ee9","2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d","2027.833f44e7-3dca-4059-b2ba-f361891e918d","2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":318,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"IDC_Booked_Facility1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02cbbad3-0dcb-4a66-8ef0-ab83aff5d6d9","optionName":"@label","value":"IDC Booked Facility"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"824781c6-4403-42d3-83f0-94cb8a4862db","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"532626f5-c6c6-4c3f-8be8-81dcfc58cc82","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"be05f5e5-088c-4e20-8b9c-a41a6faee7b9","optionName":"BookedFacilityVis","value":"tw.local.BookedFacilityVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cb5c88f7-c119-43db-8c3e-f5cc5cc61ee3","optionName":"noPendingCasesVis","value":"tw.local.noPendingCasesVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f0a704c2-96d9-47fc-8e81-b6b121db0113","optionName":"noPendingCasesMsg","value":"tw.local.noPendingCasesMsg"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c8be75ac-**************-f319dcf9ad31","optionName":"@visibility","value":"tw.local.BookedFacilityVis"}],"viewUUID":"64.d221b564-11f7-4ff9-84d6-ace71c94126c","binding":"tw.local.bookedFacilities[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"65429d14-5f0b-4d02-875c-50da3c58a268","version":"8550"},{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"15679f2b-e0ec-43ea-8390-a1cc1973b1d4","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"83d01512-e5bd-4cfe-8908-88be505ac919","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9a8eebe7-0930-4b87-870b-83495b1606ca","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"289888b7-aae8-46e2-8095-4f8526f5fd99","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9af889d7-cbee-48f8-8336-1a04b8318a41","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"80f88b63-d39e-43e2-80dd-ba87e0c62847","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a442874e-cfb1-475b-8017-c3d70092589e","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"30aeff95-b997-481d-8413-cd7a0d2ae501","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f49b899b-4277-4577-87b5-a3c296c1d906","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9a3c76b6-5f44-44a7-8359-310c02d64c3d","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"afd0c34d-b74d-44ef-804a-9bd14f4fdc10","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b8043240-68b7-49b2-870b-82efa9a33acc","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"722c49e9-b430-4cde-8b8e-bbbbce0844c3","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"81006b62-558f-44ea-8dad-2d528795b75f","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"72ce6b76-57ca-4afa-84ec-df232525658c","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4d6d23b9-1f1b-46ee-8929-4f76c31da26a","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3aa49221-da37-4247-8efc-36dcee6cd775","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"42d3df52-c37e-4c59-8e2a-ef9f932a323c","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1405fd92-c2b1-4520-8a6e-8eb9a7f41fde","optionName":"havePaymentTerm","value":"no"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"90c15ec2-b021-4806-8753-414f6ce084b7","optionName":"deleteBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"312dffb5-b901-49c9-883b-0de0d7ee2c2d","optionName":"addInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ad59c005-9f7b-41cb-84e9-e44db7d00278","optionName":"deleteInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"*************-42d8-8046-71b77e802c4f","optionName":"hasWithdraw","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bbdcd2e4-b636-4717-8b06-942d0dcdf141","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"34723083-06cf-4d71-8618-a06946b78c8c","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bde6ceb5-5db4-4076-818f-54e358103b28","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9e4b8023-86a2-470f-8452-61d9c4f7c36c","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"919a4142-32c7-48cf-8073-7b062e29e079","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f4c0cff5-53ef-4312-8b00-3278733f0177","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1991edf0-1dfa-45a8-8b4c-8f7eeff94892","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"99bb91b6-f57c-4d97-8dde-e3a23297ccf4","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c1aa055c-81a1-4541-8998-d613e10e8452","optionName":"accountsList","value":"tw.local.accountsList[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"076a2e90-db99-4846-8f43-0f679ca4b450","optionName":"advancePaymentsUsedOption","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5460e900-14ca-44ab-8cb2-93586f3f492e","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8fcee418-0196-47c2-85eb-a6972c2d69d0","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"675805b0-f574-4304-8ed6-daf94f1398a9","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"024c7bfb-f664-4c6d-83e3-d72f63dd13ec","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b8c3cf71-d6c4-4e1b-8f7a-da027672c79f","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"856eff70-4490-4663-87c1-b64cff8faab9","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpAdvancePayment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"66e0e526-ba0a-4105-85e6-5fb2310f171d","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"50060d5d-7471-48e9-8962-369abcbb7343","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bce0a072-57bf-49c7-88b8-8847f8dfb6cf","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"181fff82-4a00-4c65-8335-6fc2686ff722","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3901fdec-1247-4c4d-80cd-62779f17693b","version":"8550"},{"layoutItemId":"Limits_Tracking1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"62ee0968-c779-40ba-84ee-726218d1f696","optionName":"@label","value":"Limits Tracking"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"53bb83bf-45aa-4e1d-87d0-a2639aa141f1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9df92e41-7849-48fe-8ded-c0b0e9b32e58","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bc58820a-6466-4640-88e6-3fc1cd6d4123","optionName":"SectionVis","value":"tw.local.SectionVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cb16e87d-d5ad-49a7-827e-6e998547ea05","optionName":"facilityPercentageToBookVis","value":"tw.local.facilityPercentageToBookVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"edf1b2f2-98c1-41a9-809b-25ab48e586e1","optionName":"facilitiesCodesList","value":"tw.local.facilityCodes[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8eee6ccd-6dfa-4357-8069-242341b4e460","optionName":"facility","value":"tw.local.facility"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"600c8eef-d19c-422a-8aff-d7b501640ad2","optionName":"LimitsVis","value":"tw.local.LimitsVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"de6d3ddf-dd1d-4dab-8bb8-ed71f2cc3528","optionName":"limitMessage","value":"tw.local.limitMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d98828c-0b75-4f97-891d-16fffbf95773","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}],"viewUUID":"64.9e7e0016-3899-48ff-9db4-bea34bea40f0","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d747b740-3e74-4015-8e6f-fd8ef8ccc3d1","version":"8550"},{"layoutItemId":"Contract_Liquidation1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73cca50b-8b3e-475f-8858-f4e1710b20e4","optionName":"@label","value":"Contract Liquidation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"beda188f-713d-457d-8a92-ef0ab0c58916","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4f29476a-1826-4dcf-8d2a-d394e42526c2","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2c4cd984-81de-4c22-834d-f9ddee3cc659","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"360ff116-db33-4235-8aa9-5caff64c772f","optionName":"accountList","value":"tw.local.accountsList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5b74c91-9783-4b61-896b-8cec2d2859f2","optionName":"customerCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c4629c4-2e35-4d46-8b30-c392684e13fa","optionName":"isGLFound","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"71f84145-ad85-4768-830d-b37e38b0d5ec","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9b4eb420-**************-7f42659f74bf","optionName":"isChecker","value":"tw.local.isChecker"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c103564e-be07-4f42-8fc3-ada190a40d02","optionName":"liquidationVis","value":"tw.local.liquidationVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5ab32efc-2d26-4dba-8866-aaffe62e2786","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2d6006cd-7590-4af1-8f7a-3fef14c6c79c","optionName":"alertMessage","value":"tw.local.alertMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ee7cf603-79a7-49eb-89a9-2b1c9ade89f5","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0637f87a-4b18-480e-8162-9dd857400d7a","version":"8550"},{"layoutItemId":"attach1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e5bd47f3-32f8-4e23-894f-db9cce4b3fcb","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a2e17da-e453-4c20-8f92-622392207b73","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b30545dd-023d-4046-8a4c-28d600bbbdb1","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a9b351aa-3590-4832-89f1-cdab30889c99","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d4fadd16-1acb-4d17-856e-ec168d93e6a4","optionName":"canUpdate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b92b7b37-4b81-40c5-80dd-f184c8d4eb67","optionName":"canCreate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"61545401-dbd0-4082-8c81-83ec3c7b8f56","optionName":"canDelete","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ba7af076-d323-4f9b-8367-b4b4abe7b297","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"faf352e6-a7a3-4d98-87be-a415afbd68bf","optionName":"visiable","value":"false"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"36a8ce30-f227-4aa1-8ae3-ee3cc6c37ddc","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1dca1af8-bc55-4e97-8e01-eeff3457ccac","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ee5e8408-673e-4d7a-8dcf-d509bb6685a3","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d919f16-9357-462b-871b-bb33b6413e24","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a030794f-1572-44c3-82b7-5c6fb5f93644","optionName":"historyVisFlag","value":"None"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.CADcomments[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5fa1efb7-221d-4e61-8b95-2becea1d0887","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"bf837ffc-baff-460f-861a-9c060200757d"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f823fd95-ce96-4af3-8629-cb81212f9433","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f7786640-1409-4784-8ac4-32733e1181e1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5191fd6a-1535-4cf5-80cf-e348fca58012","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f8f6c33e-f2b4-41b4-8820-66e4681dbdc5","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"4390c72f-bf6d-4713-8dd0-2f024f346f1b","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"4ebf2ca5-db45-4ed2-888b-47c7803b592a"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c3f350a4-1d81-4b2e-834f-397c8a4c30be","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"29675220-d64c-40ea-86f4-0d535ecc13bc","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e64cdfdc-c8e3-46e0-88f9-bf2c5bd00a57","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"30e76548-300b-45f9-8864-09911437fbd8","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"58123592-ec04-40fe-89ae-64db4e5a1af7","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a2f2286a-fb9e-402e-8362-3294f55ae23b","optionName":"hasApprovals","value":"tw.local.hasApprovals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"99c34eff-d1b4-417a-8190-3193baa9d9d5","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4733e8f8-b745-4295-888d-e091eba835c8","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2550a4ec-85be-4e96-8522-822bd6e5a3cc","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e65ce70b-5aad-48d4-89c7-49aadfb699d9","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24087e6e-0258-4423-8f1f-0f52fbed5aed","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ff1272db-1b8f-47a2-8e81-7514817dede8","optionName":"isCAD","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d260dc5-ba79-406c-8d27-b094810c3f8c","optionName":"BookedFacilityVis","value":"tw.local.BookedFacilityVis"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"e3c89981-c62d-44e5-88a1-cdfa4969caaf","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Approve Request by Credit Admin Execution Checker","isForCompensation":false,"completionQuantity":1,"id":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8"},{"targetRef":"2025.15e2172a-b877-4026-be9b-09d318c6a6d3","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"36a09cf3-81e5-4367-9329-de5dcfb10d42","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.c65ce168-3373-4392-aa04-ee87ab3ee751","sourceRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8"},{"outgoing":["2027.69544ee6-bbac-4983-a935-70cd38a88ee9"],"incoming":["2027.b49431e6-c476-4978-939b-63f24800b0c8"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.69544ee6-bbac-4983-a935-70cd38a88ee9"],"nodeVisualInfo":[{"width":24,"x":337,"y":47,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378"},{"targetRef":"2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"7f6a58bb-6bfe-499a-b0c4-9c29dc9c9288","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.b49431e6-c476-4978-939b-63f24800b0c8","sourceRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8"},{"targetRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Approve Request by Credit Admin Execution Checker","declaredType":"sequenceFlow","id":"2027.69544ee6-bbac-4983-a935-70cd38a88ee9","sourceRef":"2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378"},{"outgoing":["2027.8641c8e3-110f-4c01-81fc-4b57128c2454"],"incoming":["2027.0530926c-93fd-413d-8f42-cd33a1d9e07a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":849,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.8641c8e3-110f-4c01-81fc-4b57128c2454","name":"Update History","dataInputAssociation":[{"targetRef":"2055.648598d0-2039-40d4-b60b-3753a273a378","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}]},{"targetRef":"2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}]},{"targetRef":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Credit Admin Execution Checker\""]}}]},{"targetRef":"2055.322bdb97-0698-43d7-8172-71cbc933103d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.102be411-8f80-4dfd-9c48-a921009ea00a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}],"sourceRef":["2055.65675974-9215-43be-8dce-3b75511a591d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}],"sourceRef":["2055.8fcdef92-a110-407f-aff8-5693f497f953"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}],"sourceRef":["2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}],"sourceRef":["2055.fce152d9-1c42-43bc-8bff-44f6b45aba67"]}],"calledElement":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59"},{"targetRef":"94758e37-d036-4fab-928c-b0cb84945b85","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.8641c8e3-110f-4c01-81fc-4b57128c2454","sourceRef":"2025.102be411-8f80-4dfd-9c48-a921009ea00a"},{"startQuantity":1,"outgoing":["2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d"],"incoming":["2027.e42a6505-c5eb-4589-89a2-e028a6619745"],"default":"2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d","extensionElements":{"nodeVisualInfo":[{"width":95,"x":176,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.26fe791e-3cd2-487d-85f3-70b5904ff577","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\n"]}},{"targetRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Approve Request by Credit Admin Execution Checker","declaredType":"sequenceFlow","id":"2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d","sourceRef":"2025.26fe791e-3cd2-487d-85f3-70b5904ff577"},{"startQuantity":1,"outgoing":["2027.e42a6505-c5eb-4589-89a2-e028a6619745"],"incoming":["2027.87c5a589-7542-410f-b3b8-b98da4ee4beb"],"default":"2027.e42a6505-c5eb-4589-89a2-e028a6619745","extensionElements":{"nodeVisualInfo":[{"width":95,"x":176,"y":63,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f","scriptFormat":"text\/x-javascript","script":{"content":["\/\/Dummy\r\n\/\/tw.local.idcRequest.IDCRequestNature.englishdescription = \"New Request\";\r\ntw.local.idcRequest.appInfo.subStatus=\"test\";\r\n\r\ntw.local.facilityVis = \"NONE\";\r\ntw.local.LimitsVis = \"READONLY\";\r\ntw.local.noPendingCasesMsg = \"No Facilities Found\";\r\ntw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\ntw.local.errorVIS = \"NONE\";\r\n\r\ntw.local.isChecker = true;\r\n\r\ntw.local.liquidationVis = true;\r\nif (tw.local.idcRequest.IDCRequestType == \"IDC Execution\" &amp;&amp; tw.local.idcRequest.paymentTerms.englishdescription == \"Sight\") {\r\n\ttw.local.liquidationVis = false;\r\n}else{\r\n\ttw.local.liquidationVis = true;\r\n}\r\n\r\ntw.local.action = []; \r\ntw.local.action[0] = tw.epv.Action.returnToMaker;\r\ntw.local.action[1] = tw.epv.Action.SendtoInvestigation;\r\ntw.local.action[2] = tw.epv.Action.returnToInitiator;\r\ntw.local.action[3] = tw.epv.Action.approveRequest;\r\n"]}},{"targetRef":"2025.26fe791e-3cd2-487d-85f3-70b5904ff577","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Step Name","declaredType":"sequenceFlow","id":"2027.e42a6505-c5eb-4589-89a2-e028a6619745","sourceRef":"2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.9ae97c5d-2db3-4813-a58e-302952bc4965"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.15f659ec-f13d-42ee-b809-93ad93bc084c"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasApprovals","isCollection":false,"declaredType":"dataObject","id":"2056.692b9b3f-75f1-43fe-9f10-cfe8f714028f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasReturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.23612c21-2297-402b-9e76-cfafd59eba52"},{"outgoing":["2027.833f44e7-3dca-4059-b2ba-f361891e918d","2027.0092cc35-5d59-4e0a-ab02-053309cdae9f"],"incoming":["2027.fc29d705-dd90-410c-918a-d5985d230bbc"],"default":"2027.0092cc35-5d59-4e0a-ab02-053309cdae9f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":442,"y":331,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.d647a012-f8d0-4440-b330-e77b1eef1ad4"},{"startQuantity":1,"outgoing":["2027.fc29d705-dd90-410c-918a-d5985d230bbc"],"incoming":["2027.c65ce168-3373-4392-aa04-ee87ab3ee751"],"default":"2027.fc29d705-dd90-410c-918a-d5985d230bbc","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":318,"y":312,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.15e2172a-b877-4026-be9b-09d318c6a6d3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/tw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\/\/\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\n\/\/mandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\/\/\/\/------------------------------------financial Details fo -------------------------------------------------------------------\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,\"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.name\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank\" );\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.account\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,\"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber\" );\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.submitRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.executionHub.code,\"tw.local.idcRequest.financialDetails.executionHub.code\");\r\n\/\/}\r\n\/\/var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;\r\n\/\/if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.cashAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\r\n\/\/}\r\n\/\/\r\n\/\/var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;\r\n\/\/if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtSight\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/}\r\n\/\/for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {\r\n\/\/\t\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentDate\");\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentAmount\");\r\n\/\/}\r\n\/\/-------------------------------action-----------------------------------------------------------------------\r\nif (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {\r\n\tmandatory(tw.local.idcRequest.stepLog.returnReason,\"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n\/\/}\r\n\r\n\/\/if ((tw.local.idcRequest.approvals.CAD==true|| \r\n\/\/    tw.local.idcRequest.approvals.compliance==true ||\r\n\/\/    tw.local.idcRequest.approvals.treasury==true))\r\n\/\/{   \r\n\/\/    if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n\/\/       addError(\"tw.local.selectedAction\", \"Please uncheck Approvals\");\r\n\/\/    }\r\n\/\/}\r\n\/\/else if (tw.local.selectedAction == \"Obtain Approvals\")\r\n\/\/{\r\n\/\/    addError(\"tw.local.selectedAction\", \"Please check Approvals\");\r\n\/\/}\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n"]}},{"targetRef":"2025.d647a012-f8d0-4440-b330-e77b1eef1ad4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.fc29d705-dd90-410c-918a-d5985d230bbc","sourceRef":"2025.15e2172a-b877-4026-be9b-09d318c6a6d3"},{"targetRef":"2025.ba269e33-924c-422e-9a8f-8364a6f8647f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.0092cc35-5d59-4e0a-ab02-053309cdae9f","sourceRef":"2025.d647a012-f8d0-4440-b330-e77b1eef1ad4"},{"targetRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.833f44e7-3dca-4059-b2ba-f361891e918d","sourceRef":"2025.d647a012-f8d0-4440-b330-e77b1eef1ad4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.abb96986-2df1-48d4-b0d8-4cc09ce108ea"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.3297685b-8560-425d-80f6-bd4e32eb014e"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"liquidationVis","isCollection":false,"declaredType":"dataObject","id":"2056.504d945f-03be-48a4-8d99-194052dc9839"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isChecker","isCollection":false,"declaredType":"dataObject","id":"2056.0403311c-c8df-4779-8b56-648f4e59301b"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"exRate","isCollection":false,"declaredType":"dataObject","id":"2056.047cf3e9-ca16-4f2b-893a-d2ced6955f6b"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.611aa5da-c7d0-4fe4-8614-6fa1df94a0dc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.7bbc21c9-a935-4eb3-8b5a-cbf3b98e2e77"},{"parallelMultiple":false,"outgoing":["2027.7a3fe9b6-e218-4090-86b7-905d9155d98b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.fdbe398c-2af4-4708-880b-e5de17ee740a"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.102be411-8f80-4dfd-9c48-a921009ea00a","extensionElements":{"default":["2027.7a3fe9b6-e218-4090-86b7-905d9155d98b"],"nodeVisualInfo":[{"width":24,"x":884,"y":153,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.1711864c-cd2d-4e1b-8891-e565532f43ed","outputSet":{}},{"startQuantity":1,"outgoing":["2027.ccd48532-ed7d-469c-8924-9971c0a1f769"],"incoming":["2027.7a3fe9b6-e218-4090-86b7-905d9155d98b","2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16"],"default":"2027.ccd48532-ed7d-469c-8924-9971c0a1f769","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":847,"y":51,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.fdc04161-0e54-458c-8c67-753fb720c3aa","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.101f1b94-dd49-4f8d-8713-2eb4ef49e14a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.c62dcec8-a619-4f89-8eba-5c3d42fc5134"},{"targetRef":"2025.fdc04161-0e54-458c-8c67-753fb720c3aa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.7a3fe9b6-e218-4090-86b7-905d9155d98b","sourceRef":"2025.1711864c-cd2d-4e1b-8891-e565532f43ed"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.3ac7f380-df72-4f8a-819f-e9e8e01ca45d"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.74f19764-735b-4a96-8a25-c1bb9f82b46d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"NONE\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityVis","isCollection":false,"declaredType":"dataObject","id":"2056.6c7e463e-09b0-4810-859a-01c0ba95166d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"READONLY\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityPercentageToBookVis","isCollection":false,"declaredType":"dataObject","id":"2056.ab0c88ae-63e1-4301-8dac-afca42912619"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"LimitsVis","isCollection":false,"declaredType":"dataObject","id":"2056.110d89e1-e9d9-4172-8560-43db604a2b39"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"limitMessage","isCollection":false,"declaredType":"dataObject","id":"2056.0f6bc11c-0e9e-425b-8095-0e229c280104"},{"itemSubjectRef":"itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08","name":"facility","isCollection":false,"declaredType":"dataObject","id":"2056.1b4976d7-4607-4803-8ccb-86031c77040d"},{"itemSubjectRef":"itm.12.e9597b68-6c68-4101-ab1c-d6f8af32c79d","name":"bookedFacilities","isCollection":true,"declaredType":"dataObject","id":"2056.a05f1956-3b76-4f10-82ac-4cf31dac9388"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"READONLY\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"BookedFacilityVis","isCollection":false,"declaredType":"dataObject","id":"2056.790d103a-218a-4749-827d-5c755d88a83c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"noPendingCasesVis","isCollection":false,"declaredType":"dataObject","id":"2056.516b0153-0e6a-40fa-882d-fbaef316b8ea"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.20135fb1-6e8f-46d1-89f2-156ec532a4a4"},{"outgoing":["2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db"],"incoming":["2027.8fb55bf7-97d8-4232-8093-d6d101733dcc"],"extensionElements":{"postAssignmentScript":["if(!!tw.local.bookedFacilities &amp;&amp; tw.local.bookedFacilities.length&gt;0){\r\n\r\n\ttw.local.noPendingCasesVis = \"NONE\";\r\n\ttw.local.BookedFacilityVis = \"EDITABLE\";\r\n}\r\nelse {\r\n\ttw.local.noPendingCasesVis = \"EDITABLE\";\r\n\ttw.local.BookedFacilityVis = \"EDITABLE\";\r\n}"],"nodeVisualInfo":[{"width":95,"x":567,"y":55,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.drawee= tw.local.idcContract.party[0].partyId;"]},"declaredType":"callActivity","startQuantity":1,"default":"2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db","name":"Retrieve Booked Facilities","dataInputAssociation":[{"targetRef":"2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.drawee"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.37e6d8cb-11b8-49fa-8d94-9699c6aa6239","declaredType":"TFormalExpression","content":["tw.local.bookedFacilities"]}}],"sourceRef":["2055.d34b87d3-c15b-4b59-891d-80565e71154c"]}],"calledElement":"1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541"},{"parallelMultiple":false,"outgoing":["2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.f296d3d7-8ae2-4646-8e5f-3f4ce6408d65"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666","extensionElements":{"default":["2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16"],"nodeVisualInfo":[{"width":24,"x":650,"y":78,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 3","declaredType":"boundaryEvent","id":"2025.6b5add0c-6f32-41ce-888f-599966244783","outputSet":{}},{"targetRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false,"customBendPoint":[{"x":503,"y":149}]}]},"name":"To Approve Request by Credit Admin Execution Checker","declaredType":"sequenceFlow","id":"2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db","sourceRef":"2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666"},{"targetRef":"2025.fdc04161-0e54-458c-8c67-753fb720c3aa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16","sourceRef":"2025.6b5add0c-6f32-41ce-888f-599966244783"},{"incoming":["2027.ccd48532-ed7d-469c-8924-9971c0a1f769"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":705,"y":-11,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.cf36f7a9-796f-4322-887b-7cfcebe9bcb5"},{"targetRef":"2025.cf36f7a9-796f-4322-887b-7cfcebe9bcb5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.ccd48532-ed7d-469c-8924-9971c0a1f769","sourceRef":"2025.fdc04161-0e54-458c-8c67-753fb720c3aa"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"SectionVis","isCollection":false,"declaredType":"dataObject","id":"2056.01bbadf8-77bc-4506-810c-434e0f4e0953"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"noPendingCasesMsg","isCollection":false,"declaredType":"dataObject","id":"2056.71f121b4-3d91-42f2-8cd4-f4e1bc5fed7a"},{"targetRef":"2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"2429d6ab-34bc-4c23-8d68-401b78c042a2","coachEventPath":"DC_Templete1\/checkPendingCasesBtn"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Retrieve Booked Facilities","declaredType":"sequenceFlow","id":"2027.8fb55bf7-97d8-4232-8093-d6d101733dcc","sourceRef":"2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"drawee","isCollection":false,"declaredType":"dataObject","id":"2056.869b5e0b-6c16-4d52-82d3-beeb9dbfd8f4"},{"startQuantity":1,"outgoing":["2027.0530926c-93fd-413d-8f42-cd33a1d9e07a"],"incoming":["2027.793a03d1-6ff5-4d83-a376-a478632062ec"],"default":"2027.0530926c-93fd-413d-8f42-cd33a1d9e07a","extensionElements":{"nodeVisualInfo":[{"width":95,"x":702,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"update facility","dataInputAssociation":[{"targetRef":"2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["tw.local.idcRequest.DBID"]}}]},{"targetRef":"2055.264def96-e086-437f-8ebe-4af040a4863d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7","calledElement":"1.604d7736-0fa5-4f03-af3c-19556056fdbf"},{"targetRef":"2025.102be411-8f80-4dfd-9c48-a921009ea00a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.0530926c-93fd-413d-8f42-cd33a1d9e07a","sourceRef":"2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"45bad243-8903-4598-9dc5-ecc4f0b51101","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"cff0a511-d867-4b2d-aa4e-7d31471cf9b6","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Approve Request by Credit Admin Execution Checker","declaredType":"globalUserTask","id":"1.43e432f4-0618-40df-9601-41aef33a2c23","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.8217a069-5d69-4d2f-93ed-a27c107bd818"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.5ad9bcc7-3472-4d10-8d2b-d5379353b44e"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.8e6e8fc4-602a-40ae-b32b-163f75bacca5"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.f2f04934-0930-47d1-8e38-2e9de1438bae"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"3c0ee160-1ce5-4b07-8530-042befdc1d8c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"0ba720ae-5e05-40a9-8fb2-a73403805324","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"c3c75852-90e5-4180-8f96-13390ba008a9","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.aeb84df6-2359-4422-ac4d-4c3c5f12c7c8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = [];\nautoObject.settlementAccounts[0] = {};\nautoObject.settlementAccounts[0].debitedAccount = {};\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = {};\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAmount = {};\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = [];\nautoObject.settlementAccounts[0].accountNumberList[0] = {};\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = {};\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = [];\nautoObject.party[0] = {};\nautoObject.party[0].partyType = {};\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = {};\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = {};\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new Date();\nautoObject.liquidationSummary.creditValueDate = new Date();\nautoObject.IDCProduct = {};\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new Date();\nautoObject.transactionMaturityDate = new Date();\nautoObject.commissionsAndCharges = [];\nautoObject.commissionsAndCharges[0] = {};\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = {};\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = {};\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = {};\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = [];\nautoObject.commissionsAndCharges[0].commAccountList[0] = {};\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new Date();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = {};\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = {};\nautoObject.swiftMessageData.intermediary = {};\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = {};\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = {};\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = {};\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = {};\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = {};\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = {};\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = {};\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = {};\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = {};\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = [];\nautoObject.advices[0] = {};\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = {};\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = [];\nautoObject.cashCollateralAccounts[0] = {};\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = {};\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new Date();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = [];\nautoObject.contractLimitsTracking[0] = {};\nautoObject.contractLimitsTracking[0].partyType = {};\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new Date();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = [];\nautoObject.accountNumberList[0] = {};\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.1c9d1832-dc76-4ed4-8c93-5fca0b0a6111"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.06a66c19-90dd-4fa2-8bcc-475d9b135bf4"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.0943eea8-70e7-44e0-9a2a-************"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"intiator","isCollection":false,"id":"2055.70a52f62-5d8c-4b98-8a4e-fa510949e49b"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.d9f3b4d9-1695-4390-9d5d-79bdbae9f13c"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"facilityCodes","isCollection":true,"id":"2055.e559a6a0-703f-4cb2-86c1-0347fdcab976"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.16a870b7-a1ef-48a1-9816-5ccfd5126fcb"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.aeb84df6-2359-4422-ac4d-4c3c5f12c7c8</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d0d82489-6901-48b0-bd42-0d7e0aa90d65</guid>
            <versionId>f1b53e17-4afb-4b38-8849-e1c885af09d4</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1c9d1832-dc76-4ed4-8c93-5fca0b0a6111</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a0531ead-0bb5-4386-aa75-05b45641fb07</guid>
            <versionId>e6c0d49f-8bdc-4a12-885c-3537d7142d5a</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.06a66c19-90dd-4fa2-8bcc-475d9b135bf4</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>44fb0eae-9e82-412f-a55b-db759ede27dd</guid>
            <versionId>db653f9a-6821-4bf9-9ff7-5ca376e1d92d</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0943eea8-70e7-44e0-9a2a-************</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2cc641a7-ae70-41c4-abf9-a061b40f74de</guid>
            <versionId>472b5d7e-5087-441f-b7ad-9a01e66f8e9b</versionId>
        </processParameter>
        <processParameter name="intiator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.70a52f62-5d8c-4b98-8a4e-fa510949e49b</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>286bc614-4a02-46fa-9b40-f1c2138013cc</guid>
            <versionId>06eb4c7f-1c1d-481a-b4e8-351fbc1db43d</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d9f3b4d9-1695-4390-9d5d-79bdbae9f13c</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>53f5c7fb-6532-41ff-ac52-cacc44616600</guid>
            <versionId>ce5cf6c2-9cb0-490b-983a-2a4894cfcc7f</versionId>
        </processParameter>
        <processParameter name="facilityCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e559a6a0-703f-4cb2-86c1-0347fdcab976</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dae70f49-5f21-40a3-ba61-7f4ade338f27</guid>
            <versionId>6883cb8f-e9a3-4436-a2c5-20c4e76f004f</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8217a069-5d69-4d2f-93ed-a27c107bd818</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9571c15f-689d-47e5-9b2f-e01c540cb01f</guid>
            <versionId>81c7f131-7b01-4060-ab98-9d8afeccd70b</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5ad9bcc7-3472-4d10-8d2b-d5379353b44e</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>abe2cdf5-36f1-42b5-b8e0-81c53bc9ff61</guid>
            <versionId>28e61365-2f78-47d2-b978-52f27f9e4a79</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8e6e8fc4-602a-40ae-b32b-163f75bacca5</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>efc0a9be-4ae0-4df9-9bdf-9109585543cc</guid>
            <versionId>e6243c01-d831-420b-9fc7-3e97104cf24f</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f2f04934-0930-47d1-8e38-2e9de1438bae</processParameterId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f5061fe8-69c7-4dc5-b62c-45be2b03bdc1</guid>
            <versionId>c3f7be33-9d08-4e35-95a2-913a521a21a1</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9ae97c5d-2db3-4813-a58e-302952bc4965</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7379ce8f-48d5-4233-be61-5932247dfb9a</guid>
            <versionId>6f670974-8d53-47b8-94e3-61d73486e1d6</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.15f659ec-f13d-42ee-b809-93ad93bc084c</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>07e2a627-a894-48c1-ab19-f1e912f48c5c</guid>
            <versionId>62bfcba6-983a-4bc6-981d-6d52f938ad9c</versionId>
        </processVariable>
        <processVariable name="hasApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.692b9b3f-75f1-43fe-9f10-cfe8f714028f</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a795c6e8-56a0-48ae-b8be-b0f05a94b632</guid>
            <versionId>692f6903-cc1f-47a2-8689-8390140acaa0</versionId>
        </processVariable>
        <processVariable name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.23612c21-2297-402b-9e76-cfafd59eba52</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>def41b42-6aff-4a68-9c56-68155c7d1a8b</guid>
            <versionId>125a5a63-7d83-4db0-a51f-35c17b2cab2e</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.abb96986-2df1-48d4-b0d8-4cc09ce108ea</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>497de979-42bb-4b88-aad6-00b54ed15f89</guid>
            <versionId>1bdc198b-f005-47bf-80f4-7ed681037e98</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3297685b-8560-425d-80f6-bd4e32eb014e</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1fdea76d-ee9e-4d6d-aee5-2c15dedeebf6</guid>
            <versionId>0fa88efc-6b91-4da7-97f9-c5a142bd3ce6</versionId>
        </processVariable>
        <processVariable name="liquidationVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.504d945f-03be-48a4-8d99-194052dc9839</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a0d80d12-229b-419d-90dd-27b1fb720643</guid>
            <versionId>b03a5453-fc19-4668-a095-26fb03814922</versionId>
        </processVariable>
        <processVariable name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0403311c-c8df-4779-8b56-648f4e59301b</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3fe2aa18-e425-4fd6-8d8a-a1a418a9943b</guid>
            <versionId>cf26d3d7-bcda-4a00-8935-d5a04116a117</versionId>
        </processVariable>
        <processVariable name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.047cf3e9-ca16-4f2b-893a-d2ced6955f6b</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>380defa2-a6f1-4fad-a6dd-f9394ceba3ea</guid>
            <versionId>4f93b5d0-7e24-4469-bbe4-f722fc2d5ab1</versionId>
        </processVariable>
        <processVariable name="tmpAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.611aa5da-c7d0-4fe4-8614-6fa1df94a0dc</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6b77da46-bd25-435e-bc42-812862731baa</guid>
            <versionId>7757e91e-5f16-423d-9849-4fe7c83839bc</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7bbc21c9-a935-4eb3-8b5a-cbf3b98e2e77</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>33dfaf65-2c20-43ed-a75d-ac19689ba9a1</guid>
            <versionId>14245bf0-50fb-4462-9417-8c9a578f37f3</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.101f1b94-dd49-4f8d-8713-2eb4ef49e14a</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>03b81400-9cb0-4e3f-8a35-38c2150c66a9</guid>
            <versionId>bf4663d8-29bc-4d88-9d79-9fa03922d887</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c62dcec8-a619-4f89-8eba-5c3d42fc5134</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>42b2e675-d891-4373-84fd-46803e5e4c75</guid>
            <versionId>b5510a14-ff4c-4510-bbb7-ed07436665d6</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3ac7f380-df72-4f8a-819f-e9e8e01ca45d</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc2a671b-9cf5-4955-a18e-ac5cbff5cb87</guid>
            <versionId>33c9c4cb-cff6-44e1-8c7c-7e1ca3389a50</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.74f19764-735b-4a96-8a25-c1bb9f82b46d</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c9582915-1ddd-4a9c-80f0-e6ca491b166d</guid>
            <versionId>f1c1bf52-6fe0-4dad-9aca-1869bd8a5d8d</versionId>
        </processVariable>
        <processVariable name="facilityVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6c7e463e-09b0-4810-859a-01c0ba95166d</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f577e8c8-e3a3-4104-82bd-dc3e9536e35b</guid>
            <versionId>74a24c50-8cb5-4456-a0d4-f9fbd771678c</versionId>
        </processVariable>
        <processVariable name="facilityPercentageToBookVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab0c88ae-63e1-4301-8dac-afca42912619</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4665fddd-99b6-4f94-bb99-154306571312</guid>
            <versionId>4a372aaa-de7e-450f-9474-5b76497739b5</versionId>
        </processVariable>
        <processVariable name="LimitsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.110d89e1-e9d9-4172-8560-43db604a2b39</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2c48716a-e2ee-474c-b013-a099d8e0d02e</guid>
            <versionId>490e5927-c4d6-48f4-8b50-8f8b0b48227b</versionId>
        </processVariable>
        <processVariable name="limitMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0f6bc11c-0e9e-425b-8095-0e229c280104</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dd8ae9e9-65ed-4fbc-91df-5728e0587ee9</guid>
            <versionId>62aee89e-93aa-42ed-a46d-e096148b38b6</versionId>
        </processVariable>
        <processVariable name="facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1b4976d7-4607-4803-8ccb-86031c77040d</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.b7087c1b-7f18-4032-b88b-ffe584eafd08</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f15efcdb-cbe3-4b8a-87f8-4fb26034530e</guid>
            <versionId>1134a978-8320-41dc-be32-77d70759ac17</versionId>
        </processVariable>
        <processVariable name="bookedFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a05f1956-3b76-4f10-82ac-4cf31dac9388</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.e9597b68-6c68-4101-ab1c-d6f8af32c79d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>73893888-fddb-4a16-b813-9d6b2411396b</guid>
            <versionId>092281ff-91a6-4910-99af-a2a4ee20f5c5</versionId>
        </processVariable>
        <processVariable name="BookedFacilityVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.790d103a-218a-4749-827d-5c755d88a83c</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9da2a68d-be48-4bfe-a94a-5a80220f1698</guid>
            <versionId>2e279123-bb97-4f28-80fb-f3aa0d4c2bfc</versionId>
        </processVariable>
        <processVariable name="noPendingCasesVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.516b0153-0e6a-40fa-882d-fbaef316b8ea</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bf31131b-2ce0-4fd7-a4d0-9f57855055bf</guid>
            <versionId>1e672868-9e1c-4c04-8795-eb195ec368ec</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.20135fb1-6e8f-46d1-89f2-156ec532a4a4</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0f49f325-53aa-4d91-9a7e-b9f66d98b3ae</guid>
            <versionId>35d091d9-a528-4b21-893a-67757952dc00</versionId>
        </processVariable>
        <processVariable name="SectionVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.01bbadf8-77bc-4506-810c-434e0f4e0953</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a1b8198-95ed-456c-8385-6d60d2d1032d</guid>
            <versionId>1be4d718-8e05-47cf-9216-fdeff71adb2d</versionId>
        </processVariable>
        <processVariable name="noPendingCasesMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.71f121b4-3d91-42f2-8cd4-f4e1bc5fed7a</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ec8b006b-54a7-4ea5-892b-3946e323672b</guid>
            <versionId>27a73740-e71c-40a2-a5c5-3000c04f8615</versionId>
        </processVariable>
        <processVariable name="drawee">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.869b5e0b-6c16-4d52-82d3-beeb9dbfd8f4</processVariableId>
            <description isNull="true" />
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>19edd232-677f-424e-8d14-bcea3b3346b4</guid>
            <versionId>4fb5272e-2a64-4beb-90b4-224e5507739a</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666</processItemId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <name>Retrieve Booked Facilities</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.94e19584-d2e8-4778-b379-efc15d7ba206</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:565d</guid>
            <versionId>089652dd-4ea7-4458-9292-47d85aa9c5b4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.94e19584-d2e8-4778-b379-efc15d7ba206</subProcessId>
                <attachedProcessRef>/1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541</attachedProcessRef>
                <guid>60cfc008-5e3f-4be3-95ed-2b876d75a13b</guid>
                <versionId>80c8c86a-8c25-4057-806e-678ce2abb280</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7</processItemId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <name>update facility</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a38fc1de-c4a6-4012-8c28-0d1305bd16fb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-62ad</guid>
            <versionId>29e85d49-b1f8-4f54-9e5c-2d643f953b3f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a38fc1de-c4a6-4012-8c28-0d1305bd16fb</subProcessId>
                <attachedProcessRef>/1.604d7736-0fa5-4f03-af3c-19556056fdbf</attachedProcessRef>
                <guid>f3e746d8-5b4a-4996-855f-26e69b39d5dd</guid>
                <versionId>60e61fbf-6eb4-4ddf-8adc-43034af08172</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.102be411-8f80-4dfd-9c48-a921009ea00a</processItemId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <name>Update History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.efd98dcc-4c13-4685-bdd2-651db30b6615</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6188</guid>
            <versionId>46d5584f-30fc-4c13-a250-25957664c0bf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.efd98dcc-4c13-4685-bdd2-651db30b6615</subProcessId>
                <attachedProcessRef>/1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</attachedProcessRef>
                <guid>11ebd96b-d0c0-4ae9-a07f-114a0ce5f728</guid>
                <versionId>a248da90-6951-4d3a-86e5-8596c0388972</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.65b99074-95e3-4442-802c-3a7ee1f439a4</processItemId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6b2a1da5-0267-44f9-bc19-5a61e41f6b0e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6186</guid>
            <versionId>eaf890e4-ddd6-431a-85fd-6e00ecba4471</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6b2a1da5-0267-44f9-bc19-5a61e41f6b0e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>74d184c1-0525-4ac7-917a-7c3a51d8ee83</guid>
                <versionId>4c57a1a9-0ebf-4dd3-bc48-94b85b6a7bf8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.06b81167-2c1f-4b90-b961-4335fec4830c</processItemId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.f80eaf9c-9ca8-430d-a280-3b4f94c1adfb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6187</guid>
            <versionId>f28d676f-edb9-41e6-aedf-df271440c8cb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.5248c552-ed99-43c6-a378-8af5fd577afa</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <guid>cf896f5e-0d35-455c-8050-3473366ccc39</guid>
            <versionId>31fa591e-5cad-45ae-96d4-853c4889be12</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.fb057701-ea47-46e2-8c5b-9c54d15b1efd</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <guid>2a3c487e-1cfe-4498-8f45-f61718a744e9</guid>
            <versionId>5d433844-3577-4e2e-acc6-03e37cecebec</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.41ef7cb5-e257-4be6-99cf-320cf4d25bee</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <guid>f0bddd5f-4766-4894-ae27-29601255cb41</guid>
            <versionId>eae138d8-9507-4eeb-a8a9-82b2cc973c02</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.06b81167-2c1f-4b90-b961-4335fec4830c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.16a870b7-a1ef-48a1-9816-5ccfd5126fcb" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Approve Request by Credit Admin Execution Checker" id="1.43e432f4-0618-40df-9601-41aef33a2c23">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="cff0a511-d867-4b2d-aa4e-7d31471cf9b6">
                            
                            
                            <ns16:startEvent name="Start" id="75280f93-4ee2-4fc0-91ad-b640114d2348">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="49" y="86" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.87c5a589-7542-410f-b3b8-b98da4ee4beb</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="94758e37-d036-4fab-928c-b0cb84945b85">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="976" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8641c8e3-110f-4c01-81fc-4b57128c2454</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="75280f93-4ee2-4fc0-91ad-b640114d2348" targetRef="2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f" name="To Approve Request by Credit Admin Execution Checker" id="2027.87c5a589-7542-410f-b3b8-b98da4ee4beb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.793a03d1-6ff5-4d83-a376-a478632062ec" name="Set Status" id="2025.ba269e33-924c-422e-9a8f-8364a6f8647f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="573" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0092cc35-5d59-4e0a-ab02-053309cdae9f</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.793a03d1-6ff5-4d83-a376-a478632062ec</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.selectedAction == tw.epv.Action.returnToMaker) {&#xD;
&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCADExecutionMakerUpdate;&#xD;
	&#xD;
} else if (tw.local.selectedAction == tw.epv.Action.SendtoInvestigation) {&#xD;
&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingQueueCADExecution;&#xD;
	&#xD;
}  else {&#xD;
	if(tw.local.intiator == "fo"){&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;&#xD;
	}else if (tw.local.intiator == "Initiation") {&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;&#xD;
	}else{&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ba269e33-924c-422e-9a8f-8364a6f8647f" targetRef="2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7" name="To update facility" id="2027.793a03d1-6ff5-4d83-a376-a478632062ec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Approve Request by Credit Admin Execution Checker" id="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="318" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.69544ee6-bbac-4983-a935-70cd38a88ee9</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.833f44e7-3dca-4059-b2ba-f361891e918d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c65ce168-3373-4392-aa04-ee87ab3ee751</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.b49431e6-c476-4978-939b-63f24800b0c8</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.8fb55bf7-97d8-4232-8093-d6d101733dcc</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>65429d14-5f0b-4d02-875c-50da3c58a268</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>IDC_Booked_Facility1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>02cbbad3-0dcb-4a66-8ef0-ab83aff5d6d9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>IDC Booked Facility</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>824781c6-4403-42d3-83f0-94cb8a4862db</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>532626f5-c6c6-4c3f-8be8-81dcfc58cc82</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>be05f5e5-088c-4e20-8b9c-a41a6faee7b9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>BookedFacilityVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.BookedFacilityVis</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>cb5c88f7-c119-43db-8c3e-f5cc5cc61ee3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>noPendingCasesVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.noPendingCasesVis</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f0a704c2-96d9-47fc-8e81-b6b121db0113</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>noPendingCasesMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.noPendingCasesMsg</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c8be75ac-**************-f319dcf9ad31</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.BookedFacilityVis</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.d221b564-11f7-4ff9-84d6-ace71c94126c</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.bookedFacilities[]</ns19:binding>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>a442874e-cfb1-475b-8017-c3d70092589e</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>15679f2b-e0ec-43ea-8390-a1cc1973b1d4</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>83d01512-e5bd-4cfe-8908-88be505ac919</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9a8eebe7-0930-4b87-870b-83495b1606ca</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>289888b7-aae8-46e2-8095-4f8526f5fd99</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9af889d7-cbee-48f8-8336-1a04b8318a41</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>80f88b63-d39e-43e2-80dd-ba87e0c62847</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>e3c89981-c62d-44e5-88a1-cdfa4969caaf</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c3f350a4-1d81-4b2e-834f-397c8a4c30be</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>29675220-d64c-40ea-86f4-0d535ecc13bc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e64cdfdc-c8e3-46e0-88f9-bf2c5bd00a57</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>30e76548-300b-45f9-8864-09911437fbd8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>58123592-ec04-40fe-89ae-64db4e5a1af7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a2f2286a-fb9e-402e-8362-3294f55ae23b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasApprovals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>99c34eff-d1b4-417a-8190-3193baa9d9d5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>4733e8f8-b745-4295-888d-e091eba835c8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2550a4ec-85be-4e96-8522-822bd6e5a3cc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e65ce70b-5aad-48d4-89c7-49aadfb699d9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>24087e6e-0258-4423-8f1f-0f52fbed5aed</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ff1272db-1b8f-47a2-8e81-7514817dede8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>true</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>0d260dc5-ba79-406c-8d27-b094810c3f8c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>BookedFacilityVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.BookedFacilityVis</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>4ebf2ca5-db45-4ed2-888b-47c7803b592a</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>4390c72f-bf6d-4713-8dd0-2f024f346f1b</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f823fd95-ce96-4af3-8629-cb81212f9433</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f7786640-1409-4784-8ac4-32733e1181e1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>5191fd6a-1535-4cf5-80cf-e348fca58012</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f8f6c33e-f2b4-41b4-8820-66e4681dbdc5</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>bf837ffc-baff-460f-861a-9c060200757d</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>81006b62-558f-44ea-8dad-2d528795b75f</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>30aeff95-b997-481d-8413-cd7a0d2ae501</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f49b899b-4277-4577-87b5-a3c296c1d906</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9a3c76b6-5f44-44a7-8359-310c02d64c3d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>afd0c34d-b74d-44ef-804a-9bd14f4fdc10</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b8043240-68b7-49b2-870b-82efa9a33acc</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>722c49e9-b430-4cde-8b8e-bbbbce0844c3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>bde6ceb5-5db4-4076-818f-54e358103b28</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>72ce6b76-57ca-4afa-84ec-df232525658c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4d6d23b9-1f1b-46ee-8929-4f76c31da26a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3aa49221-da37-4247-8efc-36dcee6cd775</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>42d3df52-c37e-4c59-8e2a-ef9f932a323c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1405fd92-c2b1-4520-8a6e-8eb9a7f41fde</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>no</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>90c15ec2-b021-4806-8753-414f6ce084b7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>312dffb5-b901-49c9-883b-0de0d7ee2c2d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ad59c005-9f7b-41cb-84e9-e44db7d00278</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>*************-42d8-8046-71b77e802c4f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bbdcd2e4-b636-4717-8b06-942d0dcdf141</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>34723083-06cf-4d71-8618-a06946b78c8c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>3901fdec-1247-4c4d-80cd-62779f17693b</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9e4b8023-86a2-470f-8452-61d9c4f7c36c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>919a4142-32c7-48cf-8073-7b062e29e079</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f4c0cff5-53ef-4312-8b00-3278733f0177</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1991edf0-1dfa-45a8-8b4c-8f7eeff94892</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>99bb91b6-f57c-4d97-8dde-e3a23297ccf4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c1aa055c-81a1-4541-8998-d613e10e8452</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>076a2e90-db99-4846-8f43-0f679ca4b450</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5460e900-14ca-44ab-8cb2-93586f3f492e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8fcee418-0196-47c2-85eb-a6972c2d69d0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>675805b0-f574-4304-8ed6-daf94f1398a9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>024c7bfb-f664-4c6d-83e3-d72f63dd13ec</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b8c3cf71-d6c4-4e1b-8f7a-da027672c79f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>856eff70-4490-4663-87c1-b64cff8faab9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.tmpAdvancePayment</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>66e0e526-ba0a-4105-85e6-5fb2310f171d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>50060d5d-7471-48e9-8962-369abcbb7343</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bce0a072-57bf-49c7-88b8-8847f8dfb6cf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>181fff82-4a00-4c65-8335-6fc2686ff722</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>d747b740-3e74-4015-8e6f-fd8ef8ccc3d1</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Limits_Tracking1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>62ee0968-c779-40ba-84ee-726218d1f696</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Limits Tracking</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>53bb83bf-45aa-4e1d-87d0-a2639aa141f1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9df92e41-7849-48fe-8ded-c0b0e9b32e58</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bc58820a-6466-4640-88e6-3fc1cd6d4123</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>SectionVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.SectionVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cb16e87d-d5ad-49a7-827e-6e998547ea05</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facilityPercentageToBookVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facilityPercentageToBookVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>edf1b2f2-98c1-41a9-809b-25ab48e586e1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facilitiesCodesList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facilityCodes[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8eee6ccd-6dfa-4357-8069-242341b4e460</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facility</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>600c8eef-d19c-422a-8aff-d7b501640ad2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>LimitsVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.LimitsVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>de6d3ddf-dd1d-4dab-8bb8-ed71f2cc3528</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>limitMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.limitMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3d98828c-0b75-4f97-891d-16fffbf95773</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>0637f87a-4b18-480e-8162-9dd857400d7a</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Contract_Liquidation1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>73cca50b-8b3e-475f-8858-f4e1710b20e4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Contract Liquidation</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>beda188f-713d-457d-8a92-ef0ab0c58916</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4f29476a-1826-4dcf-8d2a-d394e42526c2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2c4cd984-81de-4c22-834d-f9ddee3cc659</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>360ff116-db33-4235-8aa9-5caff64c772f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f5b74c91-9783-4b61-896b-8cec2d2859f2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5c4629c4-2e35-4d46-8b30-c392684e13fa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isGLFound</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>71f84145-ad85-4768-830d-b37e38b0d5ec</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9b4eb420-**************-7f42659f74bf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.isChecker</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c103564e-be07-4f42-8fc3-ada190a40d02</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>liquidationVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.liquidationVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5ab32efc-2d26-4dba-8866-aaffe62e2786</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2d6006cd-7590-4af1-8f7a-3fef14c6c79c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ee7cf603-79a7-49eb-89a9-2b1c9ade89f5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>36a8ce30-f227-4aa1-8ae3-ee3cc6c37ddc</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>attach1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e5bd47f3-32f8-4e23-894f-db9cce4b3fcb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3a2e17da-e453-4c20-8f92-622392207b73</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b30545dd-023d-4046-8a4c-28d600bbbdb1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a9b351aa-3590-4832-89f1-cdab30889c99</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d4fadd16-1acb-4d17-856e-ec168d93e6a4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b92b7b37-4b81-40c5-80dd-f184c8d4eb67</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>61545401-dbd0-4082-8c81-83ec3c7b8f56</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ba7af076-d323-4f9b-8367-b4b4abe7b297</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>faf352e6-a7a3-4d98-87be-a415afbd68bf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>5fa1efb7-221d-4e61-8b95-2becea1d0887</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1dca1af8-bc55-4e97-8e01-eeff3457ccac</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ee5e8408-673e-4d7a-8dcf-d509bb6685a3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3d919f16-9357-462b-871b-bb33b6413e24</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a030794f-1572-44c3-82b7-5c6fb5f93644</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.CADcomments[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" targetRef="2025.15e2172a-b877-4026-be9b-09d318c6a6d3" name="To End" id="2027.c65ce168-3373-4392-aa04-ee87ab3ee751">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="36a09cf3-81e5-4367-9329-de5dcfb10d42">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="337" y="47" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.69544ee6-bbac-4983-a935-70cd38a88ee9</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b49431e6-c476-4978-939b-63f24800b0c8</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.69544ee6-bbac-4983-a935-70cd38a88ee9</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" targetRef="2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378" name="To Postpone" id="2027.b49431e6-c476-4978-939b-63f24800b0c8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="7f6a58bb-6bfe-499a-b0c4-9c29dc9c9288">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0b8e7fb8-33bb-4f7c-9c6f-d39c845c9378" targetRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" name="To Approve Request by Credit Admin Execution Checker" id="2027.69544ee6-bbac-4983-a935-70cd38a88ee9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.8641c8e3-110f-4c01-81fc-4b57128c2454" name="Update History" id="2025.102be411-8f80-4dfd-9c48-a921009ea00a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="849" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0530926c-93fd-413d-8f42-cd33a1d9e07a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8641c8e3-110f-4c01-81fc-4b57128c2454</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.648598d0-2039-40d4-b60b-3753a273a378</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Credit Admin Execution Checker"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.65675974-9215-43be-8dce-3b75511a591d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8fcdef92-a110-407f-aff8-5693f497f953</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fce152d9-1c42-43bc-8bff-44f6b45aba67</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.102be411-8f80-4dfd-9c48-a921009ea00a" targetRef="94758e37-d036-4fab-928c-b0cb84945b85" name="To End" id="2027.8641c8e3-110f-4c01-81fc-4b57128c2454">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d" name="Set Step Name" id="2025.26fe791e-3cd2-487d-85f3-70b5904ff577">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="176" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.e42a6505-c5eb-4589-89a2-e028a6619745</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.26fe791e-3cd2-487d-85f3-70b5904ff577" targetRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" name="To Approve Request by Credit Admin Execution Checker" id="2027.7d464fc1-a984-42b5-b0c8-ea417cd4826d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.e42a6505-c5eb-4589-89a2-e028a6619745" name="Initialization Script" id="2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="176" y="63" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.87c5a589-7542-410f-b3b8-b98da4ee4beb</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e42a6505-c5eb-4589-89a2-e028a6619745</ns16:outgoing>
                                
                                
                                <ns16:script>//Dummy&#xD;
//tw.local.idcRequest.IDCRequestNature.englishdescription = "New Request";&#xD;
tw.local.idcRequest.appInfo.subStatus="test";&#xD;
&#xD;
tw.local.facilityVis = "NONE";&#xD;
tw.local.LimitsVis = "READONLY";&#xD;
tw.local.noPendingCasesMsg = "No Facilities Found";&#xD;
tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
tw.local.errorVIS = "NONE";&#xD;
&#xD;
tw.local.isChecker = true;&#xD;
&#xD;
tw.local.liquidationVis = true;&#xD;
if (tw.local.idcRequest.IDCRequestType == "IDC Execution" &amp;&amp; tw.local.idcRequest.paymentTerms.englishdescription == "Sight") {&#xD;
	tw.local.liquidationVis = false;&#xD;
}else{&#xD;
	tw.local.liquidationVis = true;&#xD;
}&#xD;
&#xD;
tw.local.action = []; &#xD;
tw.local.action[0] = tw.epv.Action.returnToMaker;&#xD;
tw.local.action[1] = tw.epv.Action.SendtoInvestigation;&#xD;
tw.local.action[2] = tw.epv.Action.returnToInitiator;&#xD;
tw.local.action[3] = tw.epv.Action.approveRequest;&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.648b7d16-ff1c-4493-911f-4ea584a0ee5f" targetRef="2025.26fe791e-3cd2-487d-85f3-70b5904ff577" name="To Set Step Name" id="2027.e42a6505-c5eb-4589-89a2-e028a6619745">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.9ae97c5d-2db3-4813-a58e-302952bc4965" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.15f659ec-f13d-42ee-b809-93ad93bc084c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasApprovals" id="2056.692b9b3f-75f1-43fe-9f10-cfe8f714028f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasReturnReason" id="2056.23612c21-2297-402b-9e76-cfafd59eba52">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">true</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:exclusiveGateway default="2027.0092cc35-5d59-4e0a-ab02-053309cdae9f" gatewayDirection="Unspecified" name="Have Errors" id="2025.d647a012-f8d0-4440-b330-e77b1eef1ad4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="442" y="331" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fc29d705-dd90-410c-918a-d5985d230bbc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.833f44e7-3dca-4059-b2ba-f361891e918d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.0092cc35-5d59-4e0a-ab02-053309cdae9f</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.fc29d705-dd90-410c-918a-d5985d230bbc" name="validation" id="2025.15e2172a-b877-4026-be9b-09d318c6a6d3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="318" y="312" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c65ce168-3373-4392-aa04-ee87ab3ee751</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fc29d705-dd90-410c-918a-d5985d230bbc</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
//		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
////validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
////----------------------------------basic details------------------------------------------------------------------------------&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
////----------------------------------------app info------------------------------------------------------------------------------------&#xD;
//mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
////------------------------------------financial Details fo -------------------------------------------------------------------	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,"tw.local.idcRequest.financialDetails.beneficiaryDetails.name");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank" );	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,"tw.local.idcRequest.financialDetails.beneficiaryDetails.account" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber" );&#xD;
//if (tw.local.selectedAction == tw.epv.Action.submitRequest) {&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.executionHub.code,"tw.local.idcRequest.financialDetails.executionHub.code");&#xD;
//}&#xD;
//var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;&#xD;
//if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
//	addError("tw.local.idcRequest.financialDetails.cashAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//&#xD;
//}&#xD;
//&#xD;
//var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;&#xD;
//if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtSight" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//}&#xD;
//for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {&#xD;
//	&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentDate");&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentAmount");&#xD;
//}&#xD;
//-------------------------------action-----------------------------------------------------------------------&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.returnReason,"tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
//if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
//	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
//}&#xD;
&#xD;
//if ((tw.local.idcRequest.approvals.CAD==true|| &#xD;
//    tw.local.idcRequest.approvals.compliance==true ||&#xD;
//    tw.local.idcRequest.approvals.treasury==true))&#xD;
//{   &#xD;
//    if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
//       addError("tw.local.selectedAction", "Please uncheck Approvals");&#xD;
//    }&#xD;
//}&#xD;
//else if (tw.local.selectedAction == "Obtain Approvals")&#xD;
//{&#xD;
//    addError("tw.local.selectedAction", "Please check Approvals");&#xD;
//}&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.15e2172a-b877-4026-be9b-09d318c6a6d3" targetRef="2025.d647a012-f8d0-4440-b330-e77b1eef1ad4" name="To Have Errors" id="2027.fc29d705-dd90-410c-918a-d5985d230bbc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d647a012-f8d0-4440-b330-e77b1eef1ad4" targetRef="2025.ba269e33-924c-422e-9a8f-8364a6f8647f" name="no" id="2027.0092cc35-5d59-4e0a-ab02-053309cdae9f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d647a012-f8d0-4440-b330-e77b1eef1ad4" targetRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" name="yes" id="2027.833f44e7-3dca-4059-b2ba-f361891e918d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.abb96986-2df1-48d4-b0d8-4cc09ce108ea" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.3297685b-8560-425d-80f6-bd4e32eb014e" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="liquidationVis" id="2056.504d945f-03be-48a4-8d99-194052dc9839" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isChecker" id="2056.0403311c-c8df-4779-8b56-648f4e59301b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="exRate" id="2056.047cf3e9-ca16-4f2b-893a-d2ced6955f6b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpAdvancePayment" id="2056.611aa5da-c7d0-4fe4-8614-6fa1df94a0dc" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.7bbc21c9-a935-4eb3-8b5a-cbf3b98e2e77">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.102be411-8f80-4dfd-9c48-a921009ea00a" parallelMultiple="false" name="Error" id="2025.1711864c-cd2d-4e1b-8891-e565532f43ed">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="884" y="153" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.7a3fe9b6-e218-4090-86b7-905d9155d98b</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.7a3fe9b6-e218-4090-86b7-905d9155d98b</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.fdbe398c-2af4-4708-880b-e5de17ee740a" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.ccd48532-ed7d-469c-8924-9971c0a1f769" name="Handling Error" id="2025.fdc04161-0e54-458c-8c67-753fb720c3aa">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="847" y="51" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.7a3fe9b6-e218-4090-86b7-905d9155d98b</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.ccd48532-ed7d-469c-8924-9971c0a1f769</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.101f1b94-dd49-4f8d-8713-2eb4ef49e14a" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.c62dcec8-a619-4f89-8eba-5c3d42fc5134" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.1711864c-cd2d-4e1b-8891-e565532f43ed" targetRef="2025.fdc04161-0e54-458c-8c67-753fb720c3aa" name="To Handling Error" id="2027.7a3fe9b6-e218-4090-86b7-905d9155d98b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.3ac7f380-df72-4f8a-819f-e9e8e01ca45d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.74f19764-735b-4a96-8a25-c1bb9f82b46d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityVis" id="2056.6c7e463e-09b0-4810-859a-01c0ba95166d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"NONE"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityPercentageToBookVis" id="2056.ab0c88ae-63e1-4301-8dac-afca42912619">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"READONLY"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="LimitsVis" id="2056.110d89e1-e9d9-4172-8560-43db604a2b39">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="limitMessage" id="2056.0f6bc11c-0e9e-425b-8095-0e229c280104" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08" isCollection="false" name="facility" id="2056.1b4976d7-4607-4803-8ccb-86031c77040d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.e9597b68-6c68-4101-ab1c-d6f8af32c79d" isCollection="true" name="bookedFacilities" id="2056.a05f1956-3b76-4f10-82ac-4cf31dac9388" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="BookedFacilityVis" id="2056.790d103a-218a-4749-827d-5c755d88a83c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"READONLY"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="noPendingCasesVis" id="2056.516b0153-0e6a-40fa-882d-fbaef316b8ea" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.20135fb1-6e8f-46d1-89f2-156ec532a4a4" />
                            
                            
                            <ns16:callActivity calledElement="1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db" name="Retrieve Booked Facilities" id="2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="567" y="55" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript>tw.local.drawee= tw.local.idcContract.party[0].partyId;</ns3:preAssignmentScript>
                                    
                                    
                                    <ns3:postAssignmentScript>if(!!tw.local.bookedFacilities &amp;&amp; tw.local.bookedFacilities.length&gt;0){&#xD;
&#xD;
	tw.local.noPendingCasesVis = "NONE";&#xD;
	tw.local.BookedFacilityVis = "EDITABLE";&#xD;
}&#xD;
else {&#xD;
	tw.local.noPendingCasesVis = "EDITABLE";&#xD;
	tw.local.BookedFacilityVis = "EDITABLE";&#xD;
}</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8fb55bf7-97d8-4232-8093-d6d101733dcc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.9e792cbd-4e0a-4206-8204-69a43cea1ce1</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.drawee</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.d34b87d3-c15b-4b59-891d-80565e71154c</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.37e6d8cb-11b8-49fa-8d94-9699c6aa6239">tw.local.bookedFacilities</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666" parallelMultiple="false" name="Error 3" id="2025.6b5add0c-6f32-41ce-888f-599966244783">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="650" y="78" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.f296d3d7-8ae2-4646-8e5f-3f4ce6408d65" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666" targetRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" name="To Approve Request by Credit Admin Execution Checker" id="2027.cd85b7c3-88d5-412b-8b14-0d3c3e57d8db">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:customBendPoint x="503" y="149" />
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6b5add0c-6f32-41ce-888f-599966244783" targetRef="2025.fdc04161-0e54-458c-8c67-753fb720c3aa" name="To Handling Error" id="2027.f66f338f-10c0-4c0a-8cdf-e5b4272eee16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.cf36f7a9-796f-4322-887b-7cfcebe9bcb5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="705" y="-11" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ccd48532-ed7d-469c-8924-9971c0a1f769</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fdc04161-0e54-458c-8c67-753fb720c3aa" targetRef="2025.cf36f7a9-796f-4322-887b-7cfcebe9bcb5" name="To Stay on page" id="2027.ccd48532-ed7d-469c-8924-9971c0a1f769">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="SectionVis" id="2056.01bbadf8-77bc-4506-810c-434e0f4e0953" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="noPendingCasesMsg" id="2056.71f121b4-3d91-42f2-8cd4-f4e1bc5fed7a" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.94fe262a-ce11-47d2-968e-6c47a87e4bd8" targetRef="2025.6fef4f94-de2f-41be-8e0b-4d7fe3038666" name="To Retrieve Booked Facilities" id="2027.8fb55bf7-97d8-4232-8093-d6d101733dcc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="2429d6ab-34bc-4c23-8d68-401b78c042a2">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/checkPendingCasesBtn</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="drawee" id="2056.869b5e0b-6c16-4d52-82d3-beeb9dbfd8f4" />
                            
                            
                            <ns16:callActivity calledElement="1.604d7736-0fa5-4f03-af3c-19556056fdbf" default="2027.0530926c-93fd-413d-8f42-cd33a1d9e07a" name="update facility" id="2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="702" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.793a03d1-6ff5-4d83-a376-a478632062ec</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0530926c-93fd-413d-8f42-cd33a1d9e07a</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">tw.local.idcRequest.DBID</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.264def96-e086-437f-8ebe-4af040a4863d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.040c88ab-d0b7-4522-8b3c-2bdf087ecfc7" targetRef="2025.102be411-8f80-4dfd-9c48-a921009ea00a" name="OK To End" id="2027.0530926c-93fd-413d-8f42-cd33a1d9e07a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="45bad243-8903-4598-9dc5-ecc4f0b51101">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="3c0ee160-1ce5-4b07-8530-042befdc1d8c" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="0ba720ae-5e05-40a9-8fb2-a73403805324" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="c3c75852-90e5-4180-8f96-13390ba008a9" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.aeb84df6-2359-4422-ac4d-4c3c5f12c7c8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.1c9d1832-dc76-4ed4-8c93-5fca0b0a6111">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = [];
autoObject.settlementAccounts[0] = {};
autoObject.settlementAccounts[0].debitedAccount = {};
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = {};
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAmount = {};
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = [];
autoObject.settlementAccounts[0].accountNumberList[0] = {};
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = {};
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = [];
autoObject.party[0] = {};
autoObject.party[0].partyType = {};
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = {};
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = {};
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new Date();
autoObject.liquidationSummary.creditValueDate = new Date();
autoObject.IDCProduct = {};
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new Date();
autoObject.transactionMaturityDate = new Date();
autoObject.commissionsAndCharges = [];
autoObject.commissionsAndCharges[0] = {};
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = {};
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = {};
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = {};
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = {};
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = [];
autoObject.commissionsAndCharges[0].commAccountList[0] = {};
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new Date();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = {};
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = {};
autoObject.swiftMessageData.intermediary = {};
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = {};
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = {};
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = {};
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = {};
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = {};
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = {};
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = {};
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = {};
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = {};
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = [];
autoObject.advices[0] = {};
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = {};
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = [];
autoObject.cashCollateralAccounts[0] = {};
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = {};
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new Date();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = [];
autoObject.contractLimitsTracking[0] = {};
autoObject.contractLimitsTracking[0].partyType = {};
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new Date();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = [];
autoObject.accountNumberList[0] = {};
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.06a66c19-90dd-4fa2-8bcc-475d9b135bf4" />
                        
                        
                        <ns16:dataInput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.0943eea8-70e7-44e0-9a2a-************" />
                        
                        
                        <ns16:dataInput name="intiator" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.70a52f62-5d8c-4b98-8a4e-fa510949e49b" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.d9f3b4d9-1695-4390-9d5d-79bdbae9f13c" />
                        
                        
                        <ns16:dataInput name="facilityCodes" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.e559a6a0-703f-4cb2-86c1-0347fdcab976" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.8217a069-5d69-4d2f-93ed-a27c107bd818" />
                        
                        
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.5ad9bcc7-3472-4d10-8d2b-d5379353b44e" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.8e6e8fc4-602a-40ae-b32b-163f75bacca5" />
                        
                        
                        <ns16:dataOutput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.f2f04934-0930-47d1-8e38-2e9de1438bae" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.38fabe12-9b0d-4db2-97c2-766298d84adb</processLinkId>
            <processId>1.43e432f4-0618-40df-9601-41aef33a2c23</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.06b81167-2c1f-4b90-b961-4335fec4830c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.65b99074-95e3-4442-802c-3a7ee1f439a4</toProcessItemId>
            <guid>7a659868-7bab-4bb3-98e6-02bc62035bca</guid>
            <versionId>e19895c3-c038-4244-b60e-21aad761a928</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.06b81167-2c1f-4b90-b961-4335fec4830c</fromProcessItemId>
            <toProcessItemId>2025.65b99074-95e3-4442-802c-3a7ee1f439a4</toProcessItemId>
        </link>
    </process>
</teamworks>

