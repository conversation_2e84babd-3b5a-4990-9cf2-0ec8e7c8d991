<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.68698fcd-008a-4312-8428-1b0aee0e67c8" name="Delete Advance Payment">
        <lastModified>1692505428687</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-484</guid>
        <versionId>1513ec0e-fca2-4de3-8dcd-a3cb5b9a2f69</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d81" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.6f5c63c3-9780-4ce0-8c2b-43891969a765"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":90,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b30b5ebb-e03a-453b-8342-90c0bafec0d3"},{"incoming":["e5247109-718b-448e-888d-dad888d5cc03","d43d8f43-8b49-4a05-818d-d49d4556a1bf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":90,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-482"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"a2982a56-36e8-465d-8612-672488f7d570"},{"targetRef":"ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.6f5c63c3-9780-4ce0-8c2b-43891969a765","sourceRef":"b30b5ebb-e03a-453b-8342-90c0bafec0d3"},{"startQuantity":1,"outgoing":["3ee1149f-3a5a-426f-8261-112df97644a2"],"incoming":["2027.6f5c63c3-9780-4ce0-8c2b-43891969a765"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":110,"y":67,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.Seperated = new tw.object.listOf.String();\r\ntw.local.Seperated = tw.local.data.split(\"-\");\r\ntw.local.requestID = tw.local.Seperated[0];\r\ntw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();\r\ntw.local.tmpAdvancePayment.DBID = tw.local.Seperated[1];\r\n\r\n"]}},{"startQuantity":1,"outgoing":["e5247109-718b-448e-888d-dad888d5cc03"],"incoming":["4fee6049-28d0-4d3c-827d-5daad38ffc92"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":415,"y":67,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"398f5f1a-d329-42e6-844b-bb7d454a1184","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.queryResult"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"startQuantity":1,"outgoing":["4fee6049-28d0-4d3c-827d-5daad38ffc92"],"incoming":["3ee1149f-3a5a-426f-8261-112df97644a2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":235,"y":67,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"129e0207-11e5-488c-8c94-2c1f2268e27e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\n\/\/---------------------------------------------DELETE---------------------------------\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\n\r\nparamInit (\"INTEGER\",tw.local.requestID);\r\n\/\/----------------------------------------------UPDATE----------------------------------\r\ni=0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);"]}},{"targetRef":"129e0207-11e5-488c-8c94-2c1f2268e27e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"3ee1149f-3a5a-426f-8261-112df97644a2","sourceRef":"ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37"},{"targetRef":"398f5f1a-d329-42e6-844b-bb7d454a1184","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"4fee6049-28d0-4d3c-827d-5daad38ffc92","sourceRef":"129e0207-11e5-488c-8c94-2c1f2268e27e"},{"targetRef":"a2982a56-36e8-465d-8612-672488f7d570","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e5247109-718b-448e-888d-dad888d5cc03","sourceRef":"398f5f1a-d329-42e6-844b-bb7d454a1184"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.28334262-90a3-407c-812e-0e530afb1816"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.efe9ad1f-9c59-42ec-8261-1c0347b93f53"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.37ead502-a8c1-486a-8936-cf4f449311f3"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.37b58d6c-c351-4920-81e9-6d0ae3805ca2"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"queryResult","isCollection":true,"declaredType":"dataObject","id":"2056.365195a0-90ff-4a2a-838e-ea6a3aff1473"},{"parallelMultiple":false,"outgoing":["cb7599b4-f29f-4f24-8213-41d71f29567f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"4477d8d8-83af-48e1-8082-9c90334d6489"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c8780586-49b3-4f5c-8fd2-04dc4ba1bbdb","otherAttributes":{"eventImplId":"cad77cdd-3c71-497a-8e67-18118df4ac9f"}}],"attachedToRef":"ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37","extensionElements":{"nodeVisualInfo":[{"width":24,"x":145,"y":125,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"828bcbe2-bfcb-4b61-8f22-8144b708e32f","outputSet":{}},{"parallelMultiple":false,"outgoing":["c292541f-9208-489f-85cc-82aaa3b99668"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"988fb43f-9e48-4441-8940-201f4daaebb5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fc785543-17c2-4f4b-8360-a731eb29fd1f","otherAttributes":{"eventImplId":"a902ca62-3171-44e6-8650-48bcae1398b2"}}],"attachedToRef":"129e0207-11e5-488c-8c94-2c1f2268e27e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":270,"y":125,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"c9fa3343-bbdf-4bb8-8154-11a6265baa79","outputSet":{}},{"parallelMultiple":false,"outgoing":["d52ea0e6-d4fd-4544-8566-6be9045ed24b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3fd5b36e-7724-4553-89f6-a4e786fd6248"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a6f59698-9e47-4799-833e-e3176b1b328c","otherAttributes":{"eventImplId":"3ce111c9-9af9-4b9a-811e-cf2e3a71af23"}}],"attachedToRef":"398f5f1a-d329-42e6-844b-bb7d454a1184","extensionElements":{"nodeVisualInfo":[{"width":24,"x":450,"y":125,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"bd7a856c-c6c4-4e83-89e5-ac24e112569c","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.0eaa9c37-ce92-4f16-833d-8466d51819d1"},{"startQuantity":1,"outgoing":["d43d8f43-8b49-4a05-818d-d49d4556a1bf"],"incoming":["cb7599b4-f29f-4f24-8213-41d71f29567f","c292541f-9208-489f-85cc-82aaa3b99668","d52ea0e6-d4fd-4544-8566-6be9045ed24b"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":310,"y":185,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0825fe75-49b8-4736-8e57-12ff46551063","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"0825fe75-49b8-4736-8e57-12ff46551063","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"cb7599b4-f29f-4f24-8213-41d71f29567f","sourceRef":"828bcbe2-bfcb-4b61-8f22-8144b708e32f"},{"targetRef":"0825fe75-49b8-4736-8e57-12ff46551063","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c292541f-9208-489f-85cc-82aaa3b99668","sourceRef":"c9fa3343-bbdf-4bb8-8154-11a6265baa79"},{"targetRef":"0825fe75-49b8-4736-8e57-12ff46551063","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"d52ea0e6-d4fd-4544-8566-6be9045ed24b","sourceRef":"bd7a856c-c6c4-4e83-89e5-ac24e112569c"},{"targetRef":"a2982a56-36e8-465d-8612-672488f7d570","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"d43d8f43-8b49-4a05-818d-d49d4556a1bf","sourceRef":"0825fe75-49b8-4736-8e57-12ff46551063"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.5d4b4f79-3c0a-4290-8843-5f125bf6711f"}],"laneSet":[{"id":"d55feea8-2ed4-4bc4-8e1c-b490d2b2d795","lane":[{"flowNodeRef":["b30b5ebb-e03a-453b-8342-90c0bafec0d3","a2982a56-36e8-465d-8612-672488f7d570","ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37","398f5f1a-d329-42e6-844b-bb7d454a1184","129e0207-11e5-488c-8c94-2c1f2268e27e","828bcbe2-bfcb-4b61-8f22-8144b708e32f","c9fa3343-bbdf-4bb8-8154-11a6265baa79","bd7a856c-c6c4-4e83-89e5-ac24e112569c","0825fe75-49b8-4736-8e57-12ff46551063"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"44087d71-0f1a-4d33-8067-66d6ce92bbc3","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Delete Advance Payment","declaredType":"process","id":"1.68698fcd-008a-4312-8428-1b0aee0e67c8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.6f9386a9-5c75-4707-8c94-915ecba11016"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.ab465ff8-25f7-4a8a-8d3c-278790e0a3f0"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"55-56\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.d28a164f-4237-4dd7-8b4d-9a37b07330f2"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d28a164f-4237-4dd7-8b4d-9a37b07330f2</processParameterId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"55-56"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6d2653cd-2529-456d-b4eb-deb2792c7380</guid>
            <versionId>108133f8-f3b3-472d-840c-a80448a9b38b</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6f9386a9-5c75-4707-8c94-915ecba11016</processParameterId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7bbe1079-4694-4619-83d2-4e86e55d3909</guid>
            <versionId>62213151-e649-464f-b454-9b7e8395d148</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ab465ff8-25f7-4a8a-8d3c-278790e0a3f0</processParameterId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>78f3cde9-78e2-43d0-939f-04ff6af7d823</guid>
            <versionId>de528bc7-096b-413e-8b70-daddc6a757f9</versionId>
        </processParameter>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.28334262-90a3-407c-812e-0e530afb1816</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1c26c8d7-0b9a-44a6-987b-281383bef992</guid>
            <versionId>f63fb009-70d9-4c4f-aa28-b502d334a31d</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.efe9ad1f-9c59-42ec-8261-1c0347b93f53</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>43b0eff3-5ef1-4084-8851-ab6cdf7400a9</guid>
            <versionId>fd94c1ec-4b12-4dd4-84fa-2ce70575c919</versionId>
        </processVariable>
        <processVariable name="tmpAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.37ead502-a8c1-486a-8936-cf4f449311f3</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc151238-5c00-45d7-8a92-7165d2c0c7e1</guid>
            <versionId>55f13067-**************-84a288636b62</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.37b58d6c-c351-4920-81e9-6d0ae3805ca2</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ff288e8c-e2aa-4090-831b-4bacca6d89a7</guid>
            <versionId>b7f580e7-b673-4f05-8d79-e0512f6353a7</versionId>
        </processVariable>
        <processVariable name="queryResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.365195a0-90ff-4a2a-838e-ea6a3aff1473</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>79ec6ce1-e8ac-4a37-adce-f46eb7262480</guid>
            <versionId>3cee37df-99da-4927-8989-135cacfb516e</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0eaa9c37-ce92-4f16-833d-8466d51819d1</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b6b2e81e-a9a6-48e2-ba39-cd785fed156f</guid>
            <versionId>4defe68e-d6a6-41f2-a9cd-dd8aa898540a</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d4b4f79-3c0a-4290-8843-5f125bf6711f</processVariableId>
            <description isNull="true" />
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6dae360e-9e48-4f75-af85-175e00dadb0a</guid>
            <versionId>11c77196-ef55-4143-b82a-14cac6d4ca8d</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.398f5f1a-d329-42e6-844b-bb7d454a1184</processItemId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.1b1dd8a0-0be3-499a-8389-562b415d3f2c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-470</guid>
            <versionId>0d199509-b15e-4381-a7c7-7f11413fac75</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="415" y="67">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d7a</errorHandlerItem>
                <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.1b1dd8a0-0be3-499a-8389-562b415d3f2c</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>27c6bcae-5080-4d5d-9840-6453b81114b9</guid>
                <versionId>9f917107-bc1d-4eea-a524-d7421dd5d3bf</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.63eb7e36-bdce-4ebc-ba97-d3f2231ae1ff</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.1b1dd8a0-0be3-499a-8389-562b415d3f2c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3e260a30-0736-4139-b740-a1b668416555</guid>
                    <versionId>4c8fb435-5ab6-43c1-ae97-ee0aef1651a8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b0b55d01-4ace-4ca5-a36b-04b4a2654941</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.1b1dd8a0-0be3-499a-8389-562b415d3f2c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryResult</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>927461d4-f1b4-4a40-9260-5a81370a5783</guid>
                    <versionId>6da4ff5d-c630-4d29-a2f3-740cda7659a3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c3a184e6-975f-4f5d-8f1a-37117b8f48ac</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.1b1dd8a0-0be3-499a-8389-562b415d3f2c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>c39457f8-4c91-4231-890c-f219609d24aa</guid>
                    <versionId>cf345893-f04c-42a0-9a6d-883426b3c067</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.129e0207-11e5-488c-8c94-2c1f2268e27e</processItemId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <name>Init SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.dabc8910-36b4-4818-84b3-05468f6e7623</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-471</guid>
            <versionId>0e0d91d0-6a8c-4ecb-b48e-f4c7adbef483</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="235" y="67">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d7a</errorHandlerItem>
                <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.dabc8910-36b4-4818-84b3-05468f6e7623</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------DELETE---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
paramInit ("INTEGER",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);</script>
                <isRule>false</isRule>
                <guid>7ff9c954-7715-453b-b39d-e4973795bf03</guid>
                <versionId>90b03c86-db83-4725-804a-8a28eea8473a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</processItemId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ebd37295-b4ee-4ee2-a977-28e2f6e589b7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d7a</guid>
            <versionId>3e4e2c61-9746-4077-a8fe-f981f7e4627a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="310" y="185">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ebd37295-b4ee-4ee2-a977-28e2f6e589b7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>407b2027-dfe7-4d6a-919b-9feb32e7011d</guid>
                <versionId>b96e3799-3af9-4cf8-85b8-5466377939a6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</processItemId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6ba32e23-17e6-418b-9b50-50ad66221656</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-472</guid>
            <versionId>c059402d-fa27-43b9-b286-4bea683797ff</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.7c053874-729b-4e9c-ad98-6fcff85a27d3</processItemPrePostId>
                <processItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>2521a9fe-36f5-4a5f-9a08-9d0c3c440052</guid>
                <versionId>e4523b3f-bd08-417a-b20e-918f5d571fcb</versionId>
            </processPrePosts>
            <layoutData x="110" y="67">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d7a</errorHandlerItem>
                <errorHandlerItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6ba32e23-17e6-418b-9b50-50ad66221656</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[1];&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>636015b8-54e4-4887-bf9a-d5be61b7150b</guid>
                <versionId>f6bd4fbb-5bec-4daa-a1b9-2e4d6c452443</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a2982a56-36e8-465d-8612-672488f7d570</processItemId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2e0b7d7c-537b-4332-92a2-512f7441dd7a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-482</guid>
            <versionId>f59231a2-952b-4b9d-a99d-322fad18a1fd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="90">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2e0b7d7c-537b-4332-92a2-512f7441dd7a</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>642b478e-fd82-4949-96bc-c49b8a41b3b8</guid>
                <versionId>86725410-6b36-4907-b5fe-a96fc9a99d43</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="90">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Delete Advance Payment" id="1.68698fcd-008a-4312-8428-1b0aee0e67c8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d28a164f-4237-4dd7-8b4d-9a37b07330f2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"55-56"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.6f9386a9-5c75-4707-8c94-915ecba11016" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.ab465ff8-25f7-4a8a-8d3c-278790e0a3f0" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="d55feea8-2ed4-4bc4-8e1c-b490d2b2d795">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="44087d71-0f1a-4d33-8067-66d6ce92bbc3" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b30b5ebb-e03a-453b-8342-90c0bafec0d3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a2982a56-36e8-465d-8612-672488f7d570</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>398f5f1a-d329-42e6-844b-bb7d454a1184</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>129e0207-11e5-488c-8c94-2c1f2268e27e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>828bcbe2-bfcb-4b61-8f22-8144b708e32f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c9fa3343-bbdf-4bb8-8154-11a6265baa79</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bd7a856c-c6c4-4e83-89e5-ac24e112569c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0825fe75-49b8-4736-8e57-12ff46551063</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b30b5ebb-e03a-453b-8342-90c0bafec0d3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="90" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.6f5c63c3-9780-4ce0-8c2b-43891969a765</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="a2982a56-36e8-465d-8612-672488f7d570">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="90" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-482</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e5247109-718b-448e-888d-dad888d5cc03</ns16:incoming>
                        
                        
                        <ns16:incoming>d43d8f43-8b49-4a05-818d-d49d4556a1bf</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b30b5ebb-e03a-453b-8342-90c0bafec0d3" targetRef="ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37" name="To End" id="2027.6f5c63c3-9780-4ce0-8c2b-43891969a765">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="110" y="67" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.6f5c63c3-9780-4ce0-8c2b-43891969a765</ns16:incoming>
                        
                        
                        <ns16:outgoing>3ee1149f-3a5a-426f-8261-112df97644a2</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[1];&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Multiple Statements (SQLResult)" id="398f5f1a-d329-42e6-844b-bb7d454a1184">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="415" y="67" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4fee6049-28d0-4d3c-827d-5daad38ffc92</ns16:incoming>
                        
                        
                        <ns16:outgoing>e5247109-718b-448e-888d-dad888d5cc03</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.queryResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Init SQL Query" id="129e0207-11e5-488c-8c94-2c1f2268e27e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="235" y="67" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3ee1149f-3a5a-426f-8261-112df97644a2</ns16:incoming>
                        
                        
                        <ns16:outgoing>4fee6049-28d0-4d3c-827d-5daad38ffc92</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------DELETE---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
paramInit ("INTEGER",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37" targetRef="129e0207-11e5-488c-8c94-2c1f2268e27e" name="To Init SQL Query" id="3ee1149f-3a5a-426f-8261-112df97644a2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="129e0207-11e5-488c-8c94-2c1f2268e27e" targetRef="398f5f1a-d329-42e6-844b-bb7d454a1184" name="To SQL Execute Multiple Statements (SQLResult)" id="4fee6049-28d0-4d3c-827d-5daad38ffc92">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="398f5f1a-d329-42e6-844b-bb7d454a1184" targetRef="a2982a56-36e8-465d-8612-672488f7d570" name="To End" id="e5247109-718b-448e-888d-dad888d5cc03">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.28334262-90a3-407c-812e-0e530afb1816" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestID" id="2056.efe9ad1f-9c59-42ec-8261-1c0347b93f53" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpAdvancePayment" id="2056.37ead502-a8c1-486a-8936-cf4f449311f3" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.37b58d6c-c351-4920-81e9-6d0ae3805ca2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="queryResult" id="2056.365195a0-90ff-4a2a-838e-ea6a3aff1473" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37" parallelMultiple="false" name="Error" id="828bcbe2-bfcb-4b61-8f22-8144b708e32f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="145" y="125" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>cb7599b4-f29f-4f24-8213-41d71f29567f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="4477d8d8-83af-48e1-8082-9c90334d6489" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c8780586-49b3-4f5c-8fd2-04dc4ba1bbdb" eventImplId="cad77cdd-3c71-497a-8e67-18118df4ac9f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="129e0207-11e5-488c-8c94-2c1f2268e27e" parallelMultiple="false" name="Error1" id="c9fa3343-bbdf-4bb8-8154-11a6265baa79">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="270" y="125" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c292541f-9208-489f-85cc-82aaa3b99668</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="988fb43f-9e48-4441-8940-201f4daaebb5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fc785543-17c2-4f4b-8360-a731eb29fd1f" eventImplId="a902ca62-3171-44e6-8650-48bcae1398b2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="398f5f1a-d329-42e6-844b-bb7d454a1184" parallelMultiple="false" name="Error2" id="bd7a856c-c6c4-4e83-89e5-ac24e112569c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="450" y="125" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>d52ea0e6-d4fd-4544-8566-6be9045ed24b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3fd5b36e-7724-4553-89f6-a4e786fd6248" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a6f59698-9e47-4799-833e-e3176b1b328c" eventImplId="3ce111c9-9af9-4b9a-811e-cf2e3a71af23">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.0eaa9c37-ce92-4f16-833d-8466d51819d1" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="0825fe75-49b8-4736-8e57-12ff46551063">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="310" y="185" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cb7599b4-f29f-4f24-8213-41d71f29567f</ns16:incoming>
                        
                        
                        <ns16:incoming>c292541f-9208-489f-85cc-82aaa3b99668</ns16:incoming>
                        
                        
                        <ns16:incoming>d52ea0e6-d4fd-4544-8566-6be9045ed24b</ns16:incoming>
                        
                        
                        <ns16:outgoing>d43d8f43-8b49-4a05-818d-d49d4556a1bf</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="828bcbe2-bfcb-4b61-8f22-8144b708e32f" targetRef="0825fe75-49b8-4736-8e57-12ff46551063" name="To Catch Errors" id="cb7599b4-f29f-4f24-8213-41d71f29567f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c9fa3343-bbdf-4bb8-8154-11a6265baa79" targetRef="0825fe75-49b8-4736-8e57-12ff46551063" name="To Catch Errors" id="c292541f-9208-489f-85cc-82aaa3b99668">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="bd7a856c-c6c4-4e83-89e5-ac24e112569c" targetRef="0825fe75-49b8-4736-8e57-12ff46551063" name="To Catch Errors" id="d52ea0e6-d4fd-4544-8566-6be9045ed24b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0825fe75-49b8-4736-8e57-12ff46551063" targetRef="a2982a56-36e8-465d-8612-672488f7d570" name="To End" id="d43d8f43-8b49-4a05-818d-d49d4556a1bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.5d4b4f79-3c0a-4290-8843-5f125bf6711f" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Init SQL Query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3ee1149f-3a5a-426f-8261-112df97644a2</processLinkId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.129e0207-11e5-488c-8c94-2c1f2268e27e</toProcessItemId>
            <guid>f1d1533d-d648-47e7-9fd7-54df1552de6b</guid>
            <versionId>8ac44500-46b4-45c5-b0d9-136ce954e411</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ed54b3fe-2f2a-4e0f-8d4a-53de99c33d37</fromProcessItemId>
            <toProcessItemId>2025.129e0207-11e5-488c-8c94-2c1f2268e27e</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4fee6049-28d0-4d3c-827d-5daad38ffc92</processLinkId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.129e0207-11e5-488c-8c94-2c1f2268e27e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.398f5f1a-d329-42e6-844b-bb7d454a1184</toProcessItemId>
            <guid>62ebdb13-b997-4a81-a9e7-c96154c102b1</guid>
            <versionId>b2d5f7e7-9d31-4491-9b16-49f15612c736</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.129e0207-11e5-488c-8c94-2c1f2268e27e</fromProcessItemId>
            <toProcessItemId>2025.398f5f1a-d329-42e6-844b-bb7d454a1184</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d43d8f43-8b49-4a05-818d-d49d4556a1bf</processLinkId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a2982a56-36e8-465d-8612-672488f7d570</toProcessItemId>
            <guid>49aba9d3-b57b-457c-8f28-3b723453bee4</guid>
            <versionId>f56b4ac7-18d0-4f18-b3d5-455b5fe6300e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.0825fe75-49b8-4736-8e57-12ff46551063</fromProcessItemId>
            <toProcessItemId>2025.a2982a56-36e8-465d-8612-672488f7d570</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e5247109-718b-448e-888d-dad888d5cc03</processLinkId>
            <processId>1.68698fcd-008a-4312-8428-1b0aee0e67c8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.398f5f1a-d329-42e6-844b-bb7d454a1184</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.a2982a56-36e8-465d-8612-672488f7d570</toProcessItemId>
            <guid>a853d618-27ce-4788-b547-e1106a1bf844</guid>
            <versionId>fa6664b8-37a6-4729-ac42-bce8b52fd8ad</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.398f5f1a-d329-42e6-844b-bb7d454a1184</fromProcessItemId>
            <toProcessItemId>2025.a2982a56-36e8-465d-8612-672488f7d570</toProcessItemId>
        </link>
    </process>
</teamworks>

