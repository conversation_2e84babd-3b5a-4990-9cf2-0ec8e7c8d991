<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2d69e423-2547-4495-845d-7bccbf743136" name="DB BPM Audit user tasks">
        <lastModified>1692530536893</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>2a69b196-eb8d-4e60-bfd6-7c6fd69bd78a</guid>
        <versionId>4e69ec98-b083-4250-b444-8312c68bf7aa</versionId>
        <dependencySummary>&lt;dependencySummary id="caca3524-b196-4a7a-b5b0-5e352966acc5" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="INSTANCE_ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0dbed104-185b-44f0-9303-0765cbdb781e</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"14712"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7923e25d-3d3e-4bf4-b237-4cf468987011</guid>
            <versionId>e6bb6a67-a6ac-426b-a291-48d641287b2e</versionId>
        </processParameter>
        <processParameter name="TASK_ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8129799a-130c-4004-beb4-d73c68f78c65</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"12345"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8353185d-c86d-417e-8113-078f839e0739</guid>
            <versionId>397d7329-f45c-494e-987e-06655f798db9</versionId>
        </processParameter>
        <processParameter name="OWNER_ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>634cb95b-de21-4ca5-9290-2e0dac4f6a74</guid>
            <versionId>2ad7afd1-e3f9-4494-a822-d917f2db2dda</versionId>
        </processParameter>
        <processParameter name="TEAM_NAME">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e91e9ebe-6e70-4b06-996b-54441169b3ca</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ac7095c1-7df9-4afc-bc44-aee5d6990fff</guid>
            <versionId>089f046d-9fd3-43b8-b222-c1767a956236</versionId>
        </processParameter>
        <processParameter name="FILTER_BRANCH">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f7296d68-138b-4e58-b910-ce7df924d7f8</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>908e835f-4d8a-4600-84a2-27ded92fb589</guid>
            <versionId>365020b0-4d77-4390-8bdd-9a3c10177492</versionId>
        </processParameter>
        <processParameter name="TEAM_MANAGER">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.df186765-bf04-4089-99d9-3f84a4866be2</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>676ec80a-619e-4266-bd8b-e9c19cc3cd05</guid>
            <versionId>01f077cc-18fb-4fb9-8397-5c29a5ab1642</versionId>
        </processParameter>
        <processParameter name="STATUS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.11effbb1-5ad3-4f99-a23c-01eda7584e00</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7fdc761d-e0ad-480e-8fb6-a80d55878b7a</guid>
            <versionId>99163c85-15a3-49bf-a2ef-245e5c7dfb1b</versionId>
        </processParameter>
        <processParameter name="Operation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.72a822b9-58cf-4b48-9c2d-db5353162497</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <seq>8</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>1</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8f3e1018-2b12-4791-9a88-368572530ac8</guid>
            <versionId>5209ec1b-f3f0-4627-9dd7-32abce48dafc</versionId>
        </processParameter>
        <processParameter name="stepName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d289e7f8-9575-476a-9dd1-1c3074861a84</guid>
            <versionId>4e28d0ac-**************-0791616dacf2</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8f9c9257-f926-4f6d-a2e6-7cc87e5c9775</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>69d5122b-9504-4f97-a8c9-917afa95402f</guid>
            <versionId>0b6aa691-915b-4116-964a-ef05451c84c3</versionId>
        </processParameter>
        <processParameter name="Statuss">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b08b5b31-85f5-4e92-b629-d45112e9085e</processParameterId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ad6822b3-dad8-408b-bbc5-a69c858871e6</guid>
            <versionId>434003d2-f00e-4c82-848f-a6784c069784</versionId>
        </processParameter>
        <processVariable name="sqlQuerys">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ad436f93-a279-4fa9-a0ac-1b758e499f57</processVariableId>
            <description isNull="true" />
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>449c6e4b-e6bc-400c-86dd-3da002c121ca</guid>
            <versionId>f791ef46-3caf-4683-9c7b-89c5eb808155</versionId>
        </processVariable>
        <processVariable name="SQLResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.17d4a411-1988-461b-9531-41a09b03d966</processVariableId>
            <description isNull="true" />
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2d70956d-6f9d-447f-b12a-ecca9ea1dc75</guid>
            <versionId>df36405b-c3d0-4b25-9d9d-77915590bdbe</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.69b3afb4-d4d6-45c1-9c67-840d46afdde0</processVariableId>
            <description isNull="true" />
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c9b72048-8389-43fc-af18-491c5476d97e</guid>
            <versionId>8e95a275-bbc9-4402-bcfd-1bf02de9309a</versionId>
        </processVariable>
        <processVariable name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d97ae3bc-46eb-4279-b5c9-d0cd6743acc7</processVariableId>
            <description isNull="true" />
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ff082a2-a257-4c6d-a987-e4567c5a3516</guid>
            <versionId>2e9ac0b9-0061-47c2-bbd7-dbba8cf63704</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.66263ccd-7969-473b-93dd-f5101fc94872</processVariableId>
            <description isNull="true" />
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c948f95a-563b-4dff-92f2-b16df8bbbc67</guid>
            <versionId>78109a89-a813-4652-b9d5-b8cbdce8fc63</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.21678c28-871d-47af-87a3-c651d64e41e0</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.edf19766-5b6d-435d-a0aa-13dd26796355</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fb</guid>
            <versionId>081f9b39-f039-4ac1-8714-703bc762dac8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.1ccd5067-cabb-42c7-bb4e-8ba90afcaee0</processItemPrePostId>
                <processItemId>2025.21678c28-871d-47af-87a3-c651d64e41e0</processItemId>
                <location>2</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : DB BPM Audit user Tasks : END");&#xD;
</script>
                <guid>982c62db-a37c-4081-88ef-e0e7db286517</guid>
                <versionId>2958a3de-6a2c-4838-a071-a6af5a4b29df</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.29a1fa44-bfc7-4125-b54e-728c5c9c7967</processItemPrePostId>
                <processItemId>2025.21678c28-871d-47af-87a3-c651d64e41e0</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>d428b65a-719a-4a08-b0bf-2cef50b4f8f3</guid>
                <versionId>85f9592e-c753-4e71-a229-2bbedfd13d54</versionId>
            </processPrePosts>
            <layoutData x="803" y="94">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.edf19766-5b6d-435d-a0aa-13dd26796355</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>17e8931b-e6bd-47fb-96a2-63a5288f3198</guid>
                <versionId>7c2c0296-82b9-43ca-a72a-dfd5839e2b8b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.84b727f0-0eca-4832-a782-5721c217488e</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.dc8938b2-1bc0-4a76-9eca-5fcc6eca1b07</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f5</guid>
            <versionId>1f14b044-9bbd-4da6-82e2-149d1d146da5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="833" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.dc8938b2-1bc0-4a76-9eca-5fcc6eca1b07</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>3b3ba389-dd05-47aa-94ab-dd13116f072c</guid>
                <versionId>ac8ad044-3548-4e5d-a3e1-b59cbc1f9dd9</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4226e0b2-1d30-4102-9f7f-7e5aef61cc04</parameterMappingId>
                    <processParameterId>2055.8f9c9257-f926-4f6d-a2e6-7cc87e5c9775</processParameterId>
                    <parameterMappingParentId>3007.dc8938b2-1bc0-4a76-9eca-5fcc6eca1b07</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>80d10822-d77e-4fef-92ca-35abf2ce8049</guid>
                    <versionId>ba6729ac-e4d4-4ab2-818d-e0dd15abbeb5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>Catch Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.cd6f6719-cb2c-4163-b7f1-51988ba2f4ed</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f8</guid>
            <versionId>568129a4-732c-43f0-b54b-825f617cae96</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="661" y="158">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.cd6f6719-cb2c-4163-b7f1-51988ba2f4ed</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[DB BPM Audit user tasks -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[DB BPM Audit user tasks -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</script>
                <isRule>false</isRule>
                <guid>a728fa9c-4f68-4f07-bd98-4f358176009e</guid>
                <versionId>c907bc19-f668-4273-b8b5-4ccc28b5c3f4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cc9125cb-d940-4a38-a208-72e4090e0eb9</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>Update</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.fe746583-6ade-44c5-b156-9d9ce1a0d958</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f9</guid>
            <versionId>66e3dbcc-d632-435a-99f9-95cb41ab8c02</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#95D087</nodeColor>
            <layoutData x="349" y="306">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>rightBottom</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f8</errorHandlerItem>
                <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.fe746583-6ade-44c5-b156-9d9ce1a0d958</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>writeLog(" start update in DB BPM Audit user tasks");&#xD;
writeLog("OWNER_ID "+tw.local.OWNER_ID)&#xD;
writeLog("TASK_ID "+tw.local.TASK_ID)&#xD;
writeLog("INSTANCE_ID "+tw.local.INSTANCE_ID)&#xD;
&#xD;
tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "UPDATE BPM.AUDIT_USER_TASKS SET (TASK_ID) = (?) WHERE INSTANCE_ID = ? AND OTHER_GROUP_FILTER = ?;"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value=tw.local.TASK_ID.split(".")[1];&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value= tw.local.INSTANCE_ID ;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.stepName ;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>fb2ab031-8581-409d-96c7-5783c4f29c2c</guid>
                <versionId>21a5c3a5-34a6-4838-95bd-a5ca236e8f7f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>SQL Execute statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.18ca032b-2767-4885-8a25-727dfefb9875</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f4</guid>
            <versionId>a8b3be1b-4342-4265-a3a4-877c579375d5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="568" y="71">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f8</errorHandlerItem>
                <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.18ca032b-2767-4885-8a25-727dfefb9875</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>ea88f41e-c424-4f04-b16d-e724a85fd9f5</guid>
                <versionId>cd709fe8-944f-451e-bec1-9d93e23f99b2</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e44147f5-0e07-4220-ad80-9b6eadf7bc71</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.18ca032b-2767-4885-8a25-727dfefb9875</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>77c1b0e0-d996-44e6-8a24-0137f6a1fc6c</guid>
                    <versionId>7a0e6d81-a840-40bf-acf2-0aeb77d5f5f6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b7cdd22d-d7f3-4567-90f6-3fed05a69374</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.18ca032b-2767-4885-8a25-727dfefb9875</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.result</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>bbba3f6b-9ba8-4de4-8740-b97ad62dac63</guid>
                    <versionId>af2ce2a4-6536-49ed-b941-7ac985ad11b3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1ae4114d-5b02-4eb9-8094-304dd9f67968</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.18ca032b-2767-4885-8a25-727dfefb9875</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlQuerys</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>*************-4c86-9295-e9757839eb63</guid>
                    <versionId>fdec2940-7582-4f04-a6e8-3adfa53c08ee</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.2c034990-6544-4257-aed1-fb57d7566eb1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fa</guid>
            <versionId>c26e0913-fd51-4112-8069-0e3d4e6127c0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.65cb9fcf-80ca-40c0-be4d-c4b219fdb52d</processItemPrePostId>
                <processItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</processItemId>
                <location>1</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : DB BPM Audit user Tasks : START");&#xD;
</script>
                <guid>8d25d74c-288b-416b-9be7-cf8bde86daab</guid>
                <versionId>1b992d42-d65d-473e-ad0b-9320409b9c9b</versionId>
            </processPrePosts>
            <layoutData x="162" y="249">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.2c034990-6544-4257-aed1-fb57d7566eb1</switchId>
                <guid>63826fe8-542f-4a16-a847-cbabb0dcf2cc</guid>
                <versionId>8c144561-d476-468e-95a4-7c79127f2df4</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.b658b30c-3dc4-4b62-b0a0-50ef9dc07e68</switchConditionId>
                    <switchId>3013.2c034990-6544-4257-aed1-fb57d7566eb1</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4a5a0677488e996f:-34e0d142:1899236511b:38e1</endStateId>
                    <condition>tw.local.Operation	  ==	  1</condition>
                    <guid>b7b02f13-f4c3-45cf-88c3-ee447e7592e3</guid>
                    <versionId>32532366-d076-4267-b084-66d18fc44ead</versionId>
                </SwitchCondition>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.36005d16-4d3c-466f-b7ff-0e88d83c4d96</switchConditionId>
                    <switchId>3013.2c034990-6544-4257-aed1-fb57d7566eb1</switchId>
                    <seq>2</seq>
                    <endStateId>guid:4a5a0677488e996f:-34e0d142:1899236511b:38e2</endStateId>
                    <condition>tw.local.Operation	  ==	  2</condition>
                    <guid>aae52a46-77c3-486d-8019-8e4b705ab693</guid>
                    <versionId>6a2a35d5-73f2-4484-bf0b-55a5f342e151</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b93f437e-533c-4f1a-bfce-a8a194051099</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>Create</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.50ac2786-6bcd-4f8c-b89e-34f5f1219d57</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f6</guid>
            <versionId>cc563df0-0e10-46aa-b5e8-4a59946ab6f5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="230">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>rightCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f8</errorHandlerItem>
                <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="bottomLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.50ac2786-6bcd-4f8c-b89e-34f5f1219d57</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "INSERT INTO  BPM.AUDIT_USER_TASKS \&#xD;
(INSTANCE_ID ,TASK_ID, OWNER_ID, TEAM_NAME , FILTER_BRANCH  , STATUS  , TEAM_MANAGER , OTHER_GROUP_FILTER)\&#xD;
 VALUES  (?,?,?,?,?,?,?,?)"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value= tw.local.INSTANCE_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
	&#xD;
	&#xD;
	//tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value= tw.local.TASK_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
	&#xD;
	//tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.OWNER_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value=tw.local.TEAM_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN"; &#xD;
&#xD;
/*	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value=tw.local.GROUP_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN"; &#xD;
*/&#xD;
	tw.local.sqlQuerys[0].parameters[4]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[4].value=tw.local.FILTER_BRANCH;&#xD;
	tw.local.sqlQuerys[0].parameters[4].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value=tw.local.STATUS;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value=tw.local.TEAM_MANAGER;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN"; &#xD;
&#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[7]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[7].value=tw.local.stepName;&#xD;
	tw.local.sqlQuerys[0].parameters[7].mode="IN"; &#xD;
	&#xD;
/*	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value=tw.local.parent_team_name;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN"; */</script>
                <isRule>false</isRule>
                <guid>cee1bed1-b96b-4056-8f8a-3effe5eae453</guid>
                <versionId>1f72a4c1-750c-44f7-92ad-84a3701735b3</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.98c0e747-7072-4751-bfeb-7d37b89162b9</processItemId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <name>Update new team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.cfeb9f55-9570-4d76-8fc3-a2b074b3b0b9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f7</guid>
            <versionId>fe6830fb-ef8c-4a50-a27d-c5701a1461c5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#95D087</nodeColor>
            <layoutData x="351" y="391">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>rightBottom</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27f8</errorHandlerItem>
                <errorHandlerItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.cfeb9f55-9570-4d76-8fc3-a2b074b3b0b9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>writeLog("start update in DB BPM Audit user tasks");&#xD;
writeLog("OWNER_ID "+tw.local.OWNER_ID)&#xD;
writeLog("TASK_ID "+tw.local.TASK_ID)&#xD;
writeLog("INSTANCE_ID "+tw.local.INSTANCE_ID)&#xD;
&#xD;
tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "UPDATE BPM.AUDIT_USER_TASKS SET \&#xD;
(TEAM_NAME , OWNER_ID ,TEAM_MANAGER , FILTER_BRANCH , STATUS ) = (?,?,?,?,?) \&#xD;
WHERE INSTANCE_ID = ? AND OTHER_GROUP_FILTER = ? ;"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value=tw.local.TEAM_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value=tw.local.OWNER_ID;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.TEAM_MANAGER ;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value= tw.local.FILTER_BRANCH;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN";&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[4]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[4].value= tw.local.STATUS;&#xD;
	tw.local.sqlQuerys[0].parameters[4].mode="IN";&#xD;
	&#xD;
/*	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.GROUP_NAME ;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";*/&#xD;
&#xD;
/*	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.parent_team_name ;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";*/&#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.INSTANCE_ID ;//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";&#xD;
&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value= tw.local.stepName ;//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN";&#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>f64383e0-02e5-4c63-9c33-b7846221845b</guid>
                <versionId>145adb8a-67fe-4f78-ab76-5f4ccb5bb601</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="15" y="253">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="DB BPM Audit user tasks" id="1.2d69e423-2547-4495-845d-7bccbf743136" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="INSTANCE_ID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0dbed104-185b-44f0-9303-0765cbdb781e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"14712"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="TASK_ID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.8129799a-130c-4004-beb4-d73c68f78c65">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"12345"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="OWNER_ID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7" />
                        
                        
                        <ns16:dataInput name="TEAM_NAME" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e91e9ebe-6e70-4b06-996b-54441169b3ca">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"test"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="FILTER_BRANCH" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f7296d68-138b-4e58-b910-ce7df924d7f8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"test"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="TEAM_MANAGER" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.df186765-bf04-4089-99d9-3f84a4866be2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"test"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="STATUS" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.11effbb1-5ad3-4f99-a23c-01eda7584e00">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"test"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="Operation" itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" id="2055.72a822b9-58cf-4b48-9c2d-db5353162497">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">1</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="stepName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b" />
                        
                        
                        <ns16:dataOutput name="Statuss" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.b08b5b31-85f5-4e92-b629-d45112e9085e" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.0dbed104-185b-44f0-9303-0765cbdb781e</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8129799a-130c-4004-beb4-d73c68f78c65</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.e91e9ebe-6e70-4b06-996b-54441169b3ca</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.f7296d68-138b-4e58-b910-ce7df924d7f8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.df186765-bf04-4089-99d9-3f84a4866be2</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.11effbb1-5ad3-4f99-a23c-01eda7584e00</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.72a822b9-58cf-4b48-9c2d-db5353162497</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.b08b5b31-85f5-4e92-b629-d45112e9085e</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="3a7cf414-8e38-4d3e-9fae-e0c82d7f22d9">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="298496fa-5ff4-4177-9a75-09a333209894" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="579" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>330fd421-002c-450a-b2d3-07b38a4b8b4d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21678c28-871d-47af-87a3-c651d64e41e0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b93f437e-533c-4f1a-bfce-a8a194051099</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7dd37b89-8d3e-4333-b5c1-4408f02704bc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b405d038-ddc3-45f7-8bb3-96a5ee9d363a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cc9125cb-d940-4a38-a208-72e4090e0eb9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>98c0e747-7072-4751-bfeb-7d37b89162b9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>edf2c3d7-f82e-4960-9a05-36c6df7a202b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8c6cf979-689e-4aa5-a51a-c5e9be7d4ac6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>67f4b842-5f2f-42f6-9322-5f5efe3011c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2ae7a7ed-1625-4670-a476-b44c56c75b72</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>14b83cf5-e14c-4478-9443-81ab3d2d3764</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>84b727f0-0eca-4832-a782-5721c217488e</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="330fd421-002c-450a-b2d3-07b38a4b8b4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="15" y="253" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>30ab7c37-4740-4e67-94e2-b792264b460c</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="21678c28-871d-47af-87a3-c651d64e41e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="803" y="94" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fb</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : DB BPM Audit user Tasks : END");&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>050d97ab-39ae-494d-b88d-4dcc3b808edb</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlQuerys" id="2056.ad436f93-a279-4fa9-a0ac-1b758e499f57" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="SQLResults" id="2056.17d4a411-1988-461b-9531-41a09b03d966" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Create" id="b93f437e-533c-4f1a-bfce-a8a194051099">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="230" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>927dd770-a475-48ac-94d0-2423a1e09bf8</ns16:incoming>
                        
                        
                        <ns16:outgoing>de04d96b-6ae3-4234-b7f4-6edb6d7a327a</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "INSERT INTO  BPM.AUDIT_USER_TASKS \&#xD;
(INSTANCE_ID ,TASK_ID, OWNER_ID, TEAM_NAME , FILTER_BRANCH  , STATUS  , TEAM_MANAGER , OTHER_GROUP_FILTER)\&#xD;
 VALUES  (?,?,?,?,?,?,?,?)"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value= tw.local.INSTANCE_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
	&#xD;
	&#xD;
	//tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value= tw.local.TASK_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
	&#xD;
	//tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.OWNER_ID//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value=tw.local.TEAM_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN"; &#xD;
&#xD;
/*	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value=tw.local.GROUP_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN"; &#xD;
*/&#xD;
	tw.local.sqlQuerys[0].parameters[4]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[4].value=tw.local.FILTER_BRANCH;&#xD;
	tw.local.sqlQuerys[0].parameters[4].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value=tw.local.STATUS;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value=tw.local.TEAM_MANAGER;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN"; &#xD;
&#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[7]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[7].value=tw.local.stepName;&#xD;
	tw.local.sqlQuerys[0].parameters[7].mode="IN"; &#xD;
	&#xD;
/*	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value=tw.local.parent_team_name;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN"; */</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.69b3afb4-d4d6-45c1-9c67-840d46afdde0" />
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute statement" id="7dd37b89-8d3e-4333-b5c1-4408f02704bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="568" y="71" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>de04d96b-6ae3-4234-b7f4-6edb6d7a327a</ns16:incoming>
                        
                        
                        <ns16:incoming>e7d2c1d6-d11d-4117-bad4-ac1c053ce940</ns16:incoming>
                        
                        
                        <ns16:incoming>ca949086-db22-4c1d-9cfc-a510d2e27b6a</ns16:incoming>
                        
                        
                        <ns16:outgoing>050d97ab-39ae-494d-b88d-4dcc3b808edb</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlQuerys</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.result</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="b93f437e-533c-4f1a-bfce-a8a194051099" targetRef="7dd37b89-8d3e-4333-b5c1-4408f02704bc" name="To SQL Execute statement" id="de04d96b-6ae3-4234-b7f4-6edb6d7a327a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7dd37b89-8d3e-4333-b5c1-4408f02704bc" targetRef="21678c28-871d-47af-87a3-c651d64e41e0" name="To End" id="050d97ab-39ae-494d-b88d-4dcc3b808edb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" name="result" id="2056.d97ae3bc-46eb-4279-b5c9-d0cd6743acc7" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Error" id="b405d038-ddc3-45f7-8bb3-96a5ee9d363a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="661" y="158" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>87f72a87-0400-4ff9-a10f-a006dcf7088a</ns16:incoming>
                        
                        
                        <ns16:incoming>bed43fd3-da1a-4754-bcc2-37f63abe2d0b</ns16:incoming>
                        
                        
                        <ns16:incoming>ab7d259e-7293-43de-b002-8d5d61e255aa</ns16:incoming>
                        
                        
                        <ns16:incoming>0e48d23c-d4bf-4312-959a-561f23070fdd</ns16:incoming>
                        
                        
                        <ns16:outgoing>58fb1d2f-9666-4ac6-b45f-efb2d79f0392</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[DB BPM Audit user tasks -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[DB BPM Audit user tasks -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b405d038-ddc3-45f7-8bb3-96a5ee9d363a" targetRef="84b727f0-0eca-4832-a782-5721c217488e" name="To End" id="58fb1d2f-9666-4ac6-b45f-efb2d79f0392">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Update" id="cc9125cb-d940-4a38-a208-72e4090e0eb9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="349" y="306" width="95" height="70" color="#95D087" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e95356d7-ce8d-4d04-b847-fa41453b217e</ns16:incoming>
                        
                        
                        <ns16:outgoing>e7d2c1d6-d11d-4117-bad4-ac1c053ce940</ns16:outgoing>
                        
                        
                        <ns16:script>writeLog(" start update in DB BPM Audit user tasks");&#xD;
writeLog("OWNER_ID "+tw.local.OWNER_ID)&#xD;
writeLog("TASK_ID "+tw.local.TASK_ID)&#xD;
writeLog("INSTANCE_ID "+tw.local.INSTANCE_ID)&#xD;
&#xD;
tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "UPDATE BPM.AUDIT_USER_TASKS SET (TASK_ID) = (?) WHERE INSTANCE_ID = ? AND OTHER_GROUP_FILTER = ?;"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value=tw.local.TASK_ID.split(".")[1];&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value= tw.local.INSTANCE_ID ;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.stepName ;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="cc9125cb-d940-4a38-a208-72e4090e0eb9" targetRef="7dd37b89-8d3e-4333-b5c1-4408f02704bc" name="To SQL Execute statement" id="e7d2c1d6-d11d-4117-bad4-ac1c053ce940">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Update new team" id="98c0e747-7072-4751-bfeb-7d37b89162b9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="351" y="391" width="95" height="70" color="#95D087" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3b0fe994-3bfd-47de-b11b-1d1e2203d14e</ns16:incoming>
                        
                        
                        <ns16:outgoing>ca949086-db22-4c1d-9cfc-a510d2e27b6a</ns16:outgoing>
                        
                        
                        <ns16:script>writeLog("start update in DB BPM Audit user tasks");&#xD;
writeLog("OWNER_ID "+tw.local.OWNER_ID)&#xD;
writeLog("TASK_ID "+tw.local.TASK_ID)&#xD;
writeLog("INSTANCE_ID "+tw.local.INSTANCE_ID)&#xD;
&#xD;
tw.local.Statuss = true;&#xD;
tw.local.sqlQuerys = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
tw.local.sql= "UPDATE BPM.AUDIT_USER_TASKS SET \&#xD;
(TEAM_NAME , OWNER_ID ,TEAM_MANAGER , FILTER_BRANCH , STATUS ) = (?,?,?,?,?) \&#xD;
WHERE INSTANCE_ID = ? AND OTHER_GROUP_FILTER = ? ;"&#xD;
	tw.local.sqlQuerys[0]= new tw.object.SQLStatement();&#xD;
	tw.local.sqlQuerys[0].sql=tw.local.sql;   &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters= new tw.object.listOf.SQLParameter();   &#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[0]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[0].value=tw.local.TEAM_NAME;&#xD;
	tw.local.sqlQuerys[0].parameters[0].mode="IN"; &#xD;
&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[1]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[1].value=tw.local.OWNER_ID;&#xD;
	tw.local.sqlQuerys[0].parameters[1].mode="IN"; &#xD;
	&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[2]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[2].value= tw.local.TEAM_MANAGER ;&#xD;
	tw.local.sqlQuerys[0].parameters[2].mode="IN"; &#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[3]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[3].value= tw.local.FILTER_BRANCH;&#xD;
	tw.local.sqlQuerys[0].parameters[3].mode="IN";&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[4]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[4].value= tw.local.STATUS;&#xD;
	tw.local.sqlQuerys[0].parameters[4].mode="IN";&#xD;
	&#xD;
/*	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.GROUP_NAME ;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";*/&#xD;
&#xD;
/*	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.parent_team_name ;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";*/&#xD;
	&#xD;
	tw.local.sqlQuerys[0].parameters[5]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[5].value= tw.local.INSTANCE_ID ;//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[5].mode="IN";&#xD;
&#xD;
&#xD;
	tw.local.sqlQuerys[0].parameters[6]= new tw.object.SQLParameter();&#xD;
	tw.local.sqlQuerys[0].parameters[6].value= tw.local.stepName ;//tw.system.currentProcessInstanceID;&#xD;
	tw.local.sqlQuerys[0].parameters[6].mode="IN";&#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="98c0e747-7072-4751-bfeb-7d37b89162b9" targetRef="7dd37b89-8d3e-4333-b5c1-4408f02704bc" name="To SQL Execute statement" id="ca949086-db22-4c1d-9cfc-a510d2e27b6a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="e95356d7-ce8d-4d04-b847-fa41453b217e" name="Exclusive Gateway" id="edf2c3d7-f82e-4960-9a05-36c6df7a202b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="162" y="249" width="32" height="32" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : DB BPM Audit user Tasks : START");&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>30ab7c37-4740-4e67-94e2-b792264b460c</ns16:incoming>
                        
                        
                        <ns16:outgoing>e95356d7-ce8d-4d04-b847-fa41453b217e</ns16:outgoing>
                        
                        
                        <ns16:outgoing>927dd770-a475-48ac-94d0-2423a1e09bf8</ns16:outgoing>
                        
                        
                        <ns16:outgoing>3b0fe994-3bfd-47de-b11b-1d1e2203d14e</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="edf2c3d7-f82e-4960-9a05-36c6df7a202b" targetRef="cc9125cb-d940-4a38-a208-72e4090e0eb9" name="To Update" id="e95356d7-ce8d-4d04-b847-fa41453b217e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="edf2c3d7-f82e-4960-9a05-36c6df7a202b" targetRef="b93f437e-533c-4f1a-bfce-a8a194051099" name="To Create" id="927dd770-a475-48ac-94d0-2423a1e09bf8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.Operation	  ==	  1</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="edf2c3d7-f82e-4960-9a05-36c6df7a202b" targetRef="98c0e747-7072-4751-bfeb-7d37b89162b9" name="To Update new team" id="3b0fe994-3bfd-47de-b11b-1d1e2203d14e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.Operation	  ==	  2</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="330fd421-002c-450a-b2d3-07b38a4b8b4d" targetRef="edf2c3d7-f82e-4960-9a05-36c6df7a202b" name="To Exclusive Gateway" id="30ab7c37-4740-4e67-94e2-b792264b460c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="cc9125cb-d940-4a38-a208-72e4090e0eb9" parallelMultiple="false" name="Error2" id="8c6cf979-689e-4aa5-a51a-c5e9be7d4ac6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="432" y="347" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>bed43fd3-da1a-4754-bcc2-37f63abe2d0b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="33716b6d-ec0a-4c9f-bb82-f554d838cf89" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="7223b070-4160-4bcb-8571-1070c0c40f44" eventImplId="bcc0338a-2641-458a-8973-7367d38781b9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="98c0e747-7072-4751-bfeb-7d37b89162b9" parallelMultiple="false" name="Error3" id="67f4b842-5f2f-42f6-9322-5f5efe3011c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="434" y="432" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>87f72a87-0400-4ff9-a10f-a006dcf7088a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9b901b12-3ddf-4e73-8b04-7679361f1dd0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a1a15699-ea32-48e5-bdfd-23d02e40d800" eventImplId="d877722e-28a7-4ebf-8f8c-effea828bb1e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="67f4b842-5f2f-42f6-9322-5f5efe3011c5" targetRef="b405d038-ddc3-45f7-8bb3-96a5ee9d363a" name="To Catch Error" id="87f72a87-0400-4ff9-a10f-a006dcf7088a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8c6cf979-689e-4aa5-a51a-c5e9be7d4ac6" targetRef="b405d038-ddc3-45f7-8bb3-96a5ee9d363a" name="To Catch Error" id="bed43fd3-da1a-4754-bcc2-37f63abe2d0b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.66263ccd-7969-473b-93dd-f5101fc94872" />
                    
                    
                    <ns16:endEvent name="End Event" id="84b727f0-0eca-4832-a782-5721c217488e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="833" y="181" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>58fb1d2f-9666-4ac6-b45f-efb2d79f0392</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="89e74a3e-8f71-49b1-ac0b-3d3e5b7a9e36" eventImplId="8f0debda-6faf-49c7-8314-2907710e5cfb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b93f437e-533c-4f1a-bfce-a8a194051099" parallelMultiple="false" name="Error" id="2ae7a7ed-1625-4670-a476-b44c56c75b72">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="430" y="253" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ab7d259e-7293-43de-b002-8d5d61e255aa</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a5c67af8-4a9e-4029-b446-a197e96fc1ce" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="966237ba-33fb-4120-9bca-94661ce04c90" eventImplId="4a3c2926-4b1a-4feb-80a7-8863cd5e2b6d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="2ae7a7ed-1625-4670-a476-b44c56c75b72" targetRef="b405d038-ddc3-45f7-8bb3-96a5ee9d363a" name="To Catch Error" id="ab7d259e-7293-43de-b002-8d5d61e255aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7dd37b89-8d3e-4333-b5c1-4408f02704bc" parallelMultiple="false" name="Error1" id="14b83cf5-e14c-4478-9443-81ab3d2d3764">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="603" y="129" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0e48d23c-d4bf-4312-959a-561f23070fdd</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c1f2401f-a06f-4afb-b701-4d5c9176ab42" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8c2b687d-4e3b-4356-840a-5756a34e715a" eventImplId="8bca31bc-630d-4518-858b-43ba42f2a228">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="14b83cf5-e14c-4478-9443-81ab3d2d3764" targetRef="b405d038-ddc3-45f7-8bb3-96a5ee9d363a" name="To Catch Error" id="0e48d23c-d4bf-4312-959a-561f23070fdd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Create">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cd99b8b9-a997-42bf-9e56-26cf4d130073</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <endStateId>guid:4a5a0677488e996f:-34e0d142:1899236511b:38e1</endStateId>
            <toProcessItemId>2025.b93f437e-533c-4f1a-bfce-a8a194051099</toProcessItemId>
            <guid>f540477d-f9c2-476a-b6f0-e61b4197cdf3</guid>
            <versionId>27ab94b0-821d-4f8f-b617-bb7c65b6c1dd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <toProcessItemId>2025.b93f437e-533c-4f1a-bfce-a8a194051099</toProcessItemId>
        </link>
        <link name="To Update new team">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.733b2e85-c81a-4ce8-b618-ee47b99d80cf</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <endStateId>guid:4a5a0677488e996f:-34e0d142:1899236511b:38e2</endStateId>
            <toProcessItemId>2025.98c0e747-7072-4751-bfeb-7d37b89162b9</toProcessItemId>
            <guid>2cffa0df-b70e-4b3c-aa55-7ac545f72ebb</guid>
            <versionId>93f0ba04-a286-4c9a-9f59-9bfb1b9034fb</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <toProcessItemId>2025.98c0e747-7072-4751-bfeb-7d37b89162b9</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b3a205df-80dd-4132-a5bf-26174a5d4bad</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.21678c28-871d-47af-87a3-c651d64e41e0</toProcessItemId>
            <guid>36e2680d-3521-4110-ac63-ce7f93ea84b5</guid>
            <versionId>aa0f1f79-a830-47e1-b29c-f9afb6ac8110</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</fromProcessItemId>
            <toProcessItemId>2025.21678c28-871d-47af-87a3-c651d64e41e0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5e96445a-f921-4a26-a503-daa6c5494ab5</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.84b727f0-0eca-4832-a782-5721c217488e</toProcessItemId>
            <guid>0fae680c-4bb5-4d90-af6b-64f943dea1e0</guid>
            <versionId>af2a8beb-01a6-4a8b-b050-0feb8a9b6b27</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b405d038-ddc3-45f7-8bb3-96a5ee9d363a</fromProcessItemId>
            <toProcessItemId>2025.84b727f0-0eca-4832-a782-5721c217488e</toProcessItemId>
        </link>
        <link name="To SQL Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.591ca25b-25c5-4930-9e27-ce6eca2c9d7a</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.98c0e747-7072-4751-bfeb-7d37b89162b9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
            <guid>32295245-f987-4290-8f22-6da5da9606e3</guid>
            <versionId>caa36740-58f5-45e8-b791-64b298a26b6d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomLeft" portType="2" />
            <fromProcessItemId>2025.98c0e747-7072-4751-bfeb-7d37b89162b9</fromProcessItemId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
        </link>
        <link name="To Update">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.507483cf-db4d-486b-a7b0-8b210cf1f17c</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.cc9125cb-d940-4a38-a208-72e4090e0eb9</toProcessItemId>
            <guid>e4186c36-2c2b-479c-8a10-2ddabe127673</guid>
            <versionId>d85e7a0f-c559-46ba-a00c-d4082064e5d6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.edf2c3d7-f82e-4960-9a05-36c6df7a202b</fromProcessItemId>
            <toProcessItemId>2025.cc9125cb-d940-4a38-a208-72e4090e0eb9</toProcessItemId>
        </link>
        <link name="To SQL Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8d870c20-09b3-4adb-a4a7-dd7df176014d</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cc9125cb-d940-4a38-a208-72e4090e0eb9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
            <guid>fe38892d-edde-44f1-a8c0-d45a5f30e6e7</guid>
            <versionId>dcddcb46-1d88-4b5e-8b9b-9b671ef63e49</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomLeft" portType="2" />
            <fromProcessItemId>2025.cc9125cb-d940-4a38-a208-72e4090e0eb9</fromProcessItemId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
        </link>
        <link name="To SQL Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.db4f4de5-daac-44dd-8ea1-51c81323094e</processLinkId>
            <processId>1.2d69e423-2547-4495-845d-7bccbf743136</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b93f437e-533c-4f1a-bfce-a8a194051099</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
            <guid>39fb0516-2199-479c-b24e-be70d8070492</guid>
            <versionId>ef926ad8-7c2b-4857-aa1d-9ce62f377fc9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b93f437e-533c-4f1a-bfce-a8a194051099</fromProcessItemId>
            <toProcessItemId>2025.7dd37b89-8d3e-4333-b5c1-4408f02704bc</toProcessItemId>
        </link>
    </process>
</teamworks>

