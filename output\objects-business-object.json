{"typeName": "Business Object", "count": 33, "objects": [{"id": "12.e9e590bc-d1fd-48b5-9733-9a6240af3e5c", "versionId": "278a2d99-8254-4442-a9dc-649bef3152d3", "name": "Approvals", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f", "versionId": "7b9204f6-55b3-4b8a-9a28-ad1d7e0dbe49", "name": "Attachment", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.dc2c5e47-1c75-43ac-86a7-93a939c19b0f", "versionId": "f755037d-9b00-4989-aa29-1ce4ed282db5", "name": "BasicDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.a025468c-38bc-4809-b337-57da9e95dacb", "versionId": "ead285b1-af6c-4d2e-abef-275c70e7d9dd", "name": "BeneficiaryDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.5a746545-c6b4-49a5-85fe-aaccc003f1ef", "versionId": "01e6db89-c5ca-40aa-9099-2bfcd99d7370", "name": "CashCollateralAccount", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.0b55eb1a-7871-4183-b9ee-f4146bfa5d07", "versionId": "c975380a-eff0-4ded-a897-6da87fa50f6f", "name": "ChargesObj", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.89a53d06-50b8-41df-a5cc-5e4f61147b6d", "versionId": "acd6d01e-d2a6-4544-b543-fab8d2f8f23a", "name": "CommissionsAndChargesDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.69b935ab-8de6-458e-aa2c-f8c8ff4862f3", "versionId": "d7a1872d-d258-41fc-aaba-37d02b71730f", "name": "ContractAdvice", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.a39439e0-d96f-443a-9929-0d2b1f90de8e", "versionId": "27accf05-b38e-4895-a915-e183dc1a7ba2", "name": "ContractLimitTracking", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.cc907e5e-4284-4dc5-8dea-8792e0a471c1", "versionId": "195418d2-6669-40e0-9bc5-d9ecd8a746d8", "name": "CustomerInformation", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.d249aea6-d076-4da7-887d-0f1dbba9713d", "versionId": "eed975dc-c6f5-496f-9d1e-7d7566847ff5", "name": "DBLookup", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.07546a6c-13be-4fe9-93f0-33c48a8492c7", "versionId": "4518ff06-f0b0-4fd1-95ce-a46f0ccc545f", "name": "DebitedAccount", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.c15fd1af-f202-42aa-a4b5-3ebd0d5c99ad", "versionId": "ec3b484f-7df6-480e-aec8-dae6ed427f8a", "name": "DebitedAmount", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.ae22157e-8e19-4a60-a294-712ff5dc96df", "versionId": "9496e657-47bc-45ae-a0ca-577b0a711399", "name": "ECMproperties", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1", "versionId": "735d5b41-c5de-477e-992d-12cb012779dd", "name": "ExchangeCurrency", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.47b1b163-7266-46de-9171-529c0b65a377", "versionId": "e02397b0-4e8a-494e-8846-ca7de5eeb925", "name": "FinancialDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.e9597b68-6c68-4101-ab1c-d6f8af32c79d", "versionId": "742ff11a-1e1e-4ef4-91a8-4ebf2985cb25", "name": "IDCBookedFacility", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "versionId": "26ee22d0-fa5d-445e-8daa-cccca5d45a32", "name": "IDCContract", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.7a9e1924-91cb-42e8-a103-4e1d967340f1", "versionId": "aa249f18-0202-4682-97c7-63f5b9232cbd", "name": "IDCCustomsReleaseRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "versionId": "ddd44f72-c19b-4d04-93ae-331d609df38c", "name": "IDCRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.36df9eba-480f-4caa-9609-6a99e6f2c474", "versionId": "317e6ac4-2627-4504-82ff-537a25747abb", "name": "IDCReversalRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.9f3bdc5d-083e-4370-b8d4-f0a7541dcf11", "versionId": "6ac32352-9901-46a8-8706-8f824cd3ff6c", "name": "IDCRoutingDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.********-ce0d-4b06-aafc-************", "versionId": "0fa51b78-2b2f-42b5-b1bf-2e05405fdec4", "name": "IDCWithdrawalRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.eee84bca-f05c-48ad-9df2-43b9d111df36", "versionId": "4afc7c87-3b6a-400c-8d8f-04fcba9fcebd", "name": "Invoice", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.ed2ab873-bea8-42bc-b684-6086fef5926a", "versionId": "f8ea2889-f8cf-405e-a519-df68bddf2561", "name": "LiquidationSummary", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.af731d60-bee2-4d24-bfed-30192291dbd7", "versionId": "c0a588f3-7d27-47f9-9ec1-9bc9b19bc51f", "name": "Parties", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.9ff3139c-f226-4eb9-b5b9-d827d8c74d45", "versionId": "7500088f-ea63-4b80-a51c-531e13334edc", "name": "PaymentTerm", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.8dacb128-2b07-4eaf-a2c1-3a0cb480a228", "versionId": "5db42eaf-ee63-4bbf-a6c2-2df63fc28a99", "name": "ProductsDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.449bd82a-6888-4a7f-ab7d-01ca0e3d9b8e", "versionId": "c11ff2ec-f930-4008-92a6-94da816492dd", "name": "Sequence", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.36aae433-b480-4a1f-861b-6d30c09351a1", "versionId": "c904d9e8-f1cd-4e33-be30-df0bc0b41801", "name": "SettlementAccount", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.770d3eea-9807-42ad-9ce2-c280a5145765", "versionId": "671a4c32-56b6-4340-955e-901c20e68aec", "name": "SwiftMessageData", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.ce3e5b4a-d024-4ff8-a49e-3a6a44d42027", "versionId": "696f7c3a-7502-4d3b-bde7-b021c1f82a93", "name": "SwiftMessagePart", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.fc23c5c1-8fd3-4584-89be-8352a606da67", "versionId": "b2d3cafb-f9b5-4d6c-8855-6c1d930a5c5b", "name": "UsedAdvancePayment", "type": "twClass", "typeName": "Business Object", "details": {}}]}