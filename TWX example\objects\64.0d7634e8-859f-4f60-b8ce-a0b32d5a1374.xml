<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.0d7634e8-859f-4f60-b8ce-a0b32d5a1374" name="Withdrawal Request Trade FO">
        <lastModified>1688661260902</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <coachViewId>64.0d7634e8-859f-4f60-b8ce-a0b32d5a1374</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;40718afe-e1ca-43b9-8541-2b6d34a2ebc4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22e63786-2e26-4670-8a61-44cdfa21db7c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Withdrawal Request Trade FO&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49820fb3-a171-4c25-8878-7881f27f03f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05d958bf-d2df-404a-8b86-a1d9702d9f32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5fa2a7f9-22b4-4efb-84e7-a4d7d11532d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ed83ed28-88b7-4a71-82f2-4aef2180c577&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;bc24bba7-cb47-41a6-8bcc-3df991751fa3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;WithdrawalExecutionHubCode&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a0c0eb4-c914-48c1-a3af-d8952e462716&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cceafbac-64be-4e72-8756-5f5edd84077c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.WithdrawalExecutionHubCode&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0cef33ac-a483-42eb-810a-8f72893a0479&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f2db32d5-94da-43de-8471-fa845daadfda&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;57d1a1af-056c-404f-8dc9-73fe736623b7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;WithdrawalExecutionHubName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be8804c0-61f2-4558-8145-4efc27e713ce&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.WithdrawalExecutionHubName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a6747e41-2bba-4154-858a-2d482492aa95&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7c881ed-c592-448d-8122-a726dc6603d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>4a36a7fc-28c9-47e5-99f2-eae70bb976cc</guid>
        <versionId>d40f4439-fb45-474b-acf2-8ed3d88c0a8f</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.32ea2b11-6928-458c-97d3-52b612559c97</coachViewLocalResId>
            <coachViewId>64.0d7634e8-859f-4f60-b8ce-a0b32d5a1374</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>100df566-efbf-484d-b14a-06de4efec726</guid>
            <versionId>b9208335-ea0c-4a6f-a799-3d6d466ee065</versionId>
        </localization>
    </coachView>
</teamworks>

