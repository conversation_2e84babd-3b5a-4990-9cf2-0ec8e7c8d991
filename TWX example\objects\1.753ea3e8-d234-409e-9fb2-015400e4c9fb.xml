<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.753ea3e8-d234-409e-9fb2-015400e4c9fb" name="Get Customer Accounts">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>3600</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>48a33bf5-4b9a-45d3-838f-975311b7bf93</guid>
        <versionId>b3bd252b-5841-4c98-beb8-464d9f17622b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-1d03" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":33,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"cad49e9f-b4f0-4422-8de1-3e2a2d094a05"},{"incoming":["77c93e40-f709-4312-bf97-661c379663d5","ff693a6c-e025-40c1-8dcd-e96533eb0cfc"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":654,"y":33,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6172"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ee486dca-3327-4687-bc88-0af1e3d018f5"},{"targetRef":"46b7435c-31c7-4e42-82ca-8d89831d28a3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Accounts Info","declaredType":"sequenceFlow","id":"2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d","sourceRef":"cad49e9f-b4f0-4422-8de1-3e2a2d094a05"},{"startQuantity":1,"outgoing":["b4a9ac8f-7415-459f-b538-4b3876995158"],"incoming":["2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":10,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get Accounts Info","dataInputAssociation":[{"targetRef":"2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.ef963c07-a0e0-47de-a99a-995cd5146f82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.246bbadd-892b-4d12-ad68-7d03cbc463dd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.ab1eb755-2de3-44ae-893e-3b078b4b594d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.ee2392cb-9715-4e86-afa1-8376111abb77","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.72073a72-e4d4-4c67-af56-6dcd28abac8d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"46b7435c-31c7-4e42-82ca-8d89831d28a3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.accountsList"]}}],"sourceRef":["2055.081712aa-eb0f-473f-9a8c-8b128642a67b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.c40dc139-a328-4752-90f6-254db0bdb266"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.status"]}}],"sourceRef":["2055.c62c3bc0-46ea-4519-9703-3a27676d6d87"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.144a896d-105b-40b0-bb38-944b8f8b858b"]}],"calledElement":"1.bd127e8f-b948-40ef-a529-898ff4290d2a"},{"targetRef":"9851d6aa-239a-4722-8855-2f274b239081","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"b4a9ac8f-7415-459f-b538-4b3876995158","sourceRef":"46b7435c-31c7-4e42-82ca-8d89831d28a3"},{"startQuantity":1,"outgoing":["77c93e40-f709-4312-bf97-661c379663d5"],"incoming":["941e21ee-738c-4d59-8ded-f434d1a450bf"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":484,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"5b250ba9-d38e-440f-bccb-fa113ea436ac","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.listOf.Account();\r\n\tif (tw.local.isSuccessful) {\r\n\t\ttw.local.results = tw.local.accountsList;\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n\r\n"]}},{"targetRef":"ee486dca-3327-4687-bc88-0af1e3d018f5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"77c93e40-f709-4312-bf97-661c379663d5","sourceRef":"5b250ba9-d38e-440f-bccb-fa113ea436ac"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();\nautoObject[0] = new tw.object.toolkit.NBEINTT.Account();\nautoObject[0].accountNO = \"\";\nautoObject[0].currencyCode = \"\";\nautoObject[0].branchCode = \"\";\nautoObject[0].balance = 0.0;\nautoObject[0].typeCode = \"\";\nautoObject[0].customerName = \"\";\nautoObject[0].customerNo = \"\";\nautoObject[0].frozen = false;\nautoObject[0].dormant = false;\nautoObject[0].noDebit = false;\nautoObject[0].noCredit = false;\nautoObject[0].postingAllowed = false;\nautoObject[0].ibanAccountNumber = \"\";\nautoObject[0].accountClassCode = \"\";\nautoObject[0].balanceType = \"\";\nautoObject[0].accountStatus = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.62f5f519-45a5-4257-91d6-1048eeb4f843"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.b8fe173c-f5ac-4893-8077-5b0cf9eee3db"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.252d2d1e-e54c-4960-ab67-0caf893030b0"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.306789fb-c896-429a-81a2-aaf29156fbb0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"declaredType":"dataObject","id":"2056.fb662bd7-8a26-45c0-88d3-7713b3ad7182"},{"parallelMultiple":false,"outgoing":["c3ccd836-5413-4224-86c9-c0e24ba843f2"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"643eb8ed-7389-43d2-82a0-8b8af854d2b2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"836ba6ea-6821-465a-854a-35fc4b9c526d","otherAttributes":{"eventImplId":"cdba0726-f161-4cee-81e4-133a6764666f"}}],"attachedToRef":"46b7435c-31c7-4e42-82ca-8d89831d28a3","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"9288cea8-0c33-47f3-8c66-6ee5fed4083d","outputSet":{}},{"parallelMultiple":false,"outgoing":["a7493814-ad42-4083-8ffc-fd4bed4331df"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5bc64b1e-2e58-4c6f-8668-18272ec052cd"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d0b8daac-f607-41c9-867a-b9a1043f8008","otherAttributes":{"eventImplId":"016f5be6-4311-408c-8eb5-09b9ac685ce3"}}],"attachedToRef":"5b250ba9-d38e-440f-bccb-fa113ea436ac","extensionElements":{"nodeVisualInfo":[{"width":24,"x":519,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"6ab948b0-33fc-49ea-86df-2f9f4263fdce","outputSet":{}},{"targetRef":"0baa551e-55bd-4c25-8fbf-77c3b21f98b0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"c3ccd836-5413-4224-86c9-c0e24ba843f2","sourceRef":"9288cea8-0c33-47f3-8c66-6ee5fed4083d"},{"targetRef":"0baa551e-55bd-4c25-8fbf-77c3b21f98b0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a7493814-ad42-4083-8ffc-fd4bed4331df","sourceRef":"6ab948b0-33fc-49ea-86df-2f9f4263fdce"},{"outgoing":["941e21ee-738c-4d59-8ded-f434d1a450bf","9a14cfde-1bd8-4246-8a0c-db179477830b"],"incoming":["b4a9ac8f-7415-459f-b538-4b3876995158"],"default":"941e21ee-738c-4d59-8ded-f434d1a450bf","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":346,"y":29,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"9851d6aa-239a-4722-8855-2f274b239081"},{"targetRef":"5b250ba9-d38e-440f-bccb-fa113ea436ac","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map data","declaredType":"sequenceFlow","id":"941e21ee-738c-4d59-8ded-f434d1a450bf","sourceRef":"9851d6aa-239a-4722-8855-2f274b239081"},{"targetRef":"0baa551e-55bd-4c25-8fbf-77c3b21f98b0","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"9a14cfde-1bd8-4246-8a0c-db179477830b","sourceRef":"9851d6aa-239a-4722-8855-2f274b239081"},{"startQuantity":1,"outgoing":["ff693a6c-e025-40c1-8dcd-e96533eb0cfc"],"incoming":["9a14cfde-1bd8-4246-8a0c-db179477830b","c3ccd836-5413-4224-86c9-c0e24ba843f2","a7493814-ad42-4083-8ffc-fd4bed4331df"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":325,"y":140,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0baa551e-55bd-4c25-8fbf-77c3b21f98b0","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"ee486dca-3327-4687-bc88-0af1e3d018f5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"ff693a6c-e025-40c1-8dcd-e96533eb0cfc","sourceRef":"0baa551e-55bd-4c25-8fbf-77c3b21f98b0"}],"laneSet":[{"id":"b45ac9c9-f703-4ff2-85ca-54f004008c93","lane":[{"flowNodeRef":["cad49e9f-b4f0-4422-8de1-3e2a2d094a05","ee486dca-3327-4687-bc88-0af1e3d018f5","46b7435c-31c7-4e42-82ca-8d89831d28a3","5b250ba9-d38e-440f-bccb-fa113ea436ac","9288cea8-0c33-47f3-8c66-6ee5fed4083d","6ab948b0-33fc-49ea-86df-2f9f4263fdce","9851d6aa-239a-4722-8855-2f274b239081","0baa551e-55bd-4c25-8fbf-77c3b21f98b0"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":272}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"daafd483-0a61-48f8-890a-beaf9a28b2b9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"cachingType":[false],"cacheLength":[3600],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Customer Accounts","declaredType":"process","id":"1.753ea3e8-d234-409e-9fb2-015400e4c9fb","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.c041d819-6c20-4e12-bfe7-6fabc08502bf"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.********-da9b-4292-855d-60adc94a3051"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\/\/\"********\"\r\n\/\/\"********\"\r\n\"********\"\r\n\/\/\"********\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.98d8998c-bb83-4e1b-a0f4-398ba1fdc383"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.98d8998c-bb83-4e1b-a0f4-398ba1fdc383</processParameterId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>229b82db-0d5d-44e1-adaf-869131f54e5d</guid>
            <versionId>2cc755e7-30b6-4e3d-819f-56ce5df8df56</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c041d819-6c20-4e12-bfe7-6fabc08502bf</processParameterId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>23cd8248-25aa-4a62-bd07-8b3a16052073</guid>
            <versionId>5983a13e-e94b-4351-b506-2acb458ba273</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.********-da9b-4292-855d-60adc94a3051</processParameterId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>********-a02a-4e02-8d09-0ed9c816a5b1</guid>
            <versionId>aba68810-b929-4a6f-b31a-bd639c5b797f</versionId>
        </processParameter>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.62f5f519-45a5-4257-91d6-1048eeb4f843</processVariableId>
            <description isNull="true" />
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();
autoObject[0] = new tw.object.toolkit.NBEINTT.Account();
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</defaultValue>
            <guid>8cf736fd-5070-4c9f-85dc-a88616d874e6</guid>
            <versionId>********-a638-4bca-b319-80d5219c5ee5</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b8fe173c-f5ac-4893-8077-5b0cf9eee3db</processVariableId>
            <description isNull="true" />
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9490ec0e-6081-4e49-b892-2e6195e6ab53</guid>
            <versionId>d55c5e90-9434-48d8-9295-121a74cd20e0</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.252d2d1e-e54c-4960-ab67-0caf893030b0</processVariableId>
            <description isNull="true" />
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1da5c7e8-0f9f-4df7-addd-00eda1f9d566</guid>
            <versionId>105f2e36-09c1-4206-ad5c-31c01f50d473</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.306789fb-c896-429a-81a2-aaf29156fbb0</processVariableId>
            <description isNull="true" />
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>739ad64e-a833-4473-aa9b-c7bea318102c</guid>
            <versionId>b193fcc0-fa18-4874-a3e7-6680a1dbfd58</versionId>
        </processVariable>
        <processVariable name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fb662bd7-8a26-45c0-88d3-7713b3ad7182</processVariableId>
            <description isNull="true" />
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb65d487-e1dd-4369-8d9b-d5d28776c8a4</guid>
            <versionId>9c487331-c12b-424c-a967-d60aef461ea8</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</processItemId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <name>Get Accounts Info</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6171</guid>
            <versionId>10dad92c-2d2e-41a5-92b3-e0b9501d93ed</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.6ad6fab7-1ea8-461d-a931-a8c383321c8a</processItemPrePostId>
                <processItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>52dbc698-5f7b-4b45-8d69-97c9ac9c4d51</guid>
                <versionId>765b9179-03ce-4444-a48e-80b0369872cf</versionId>
            </processPrePosts>
            <layoutData x="177" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e4b</errorHandlerItem>
                <errorHandlerItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.99d7964a-c84f-4a21-961e-b6895520067f</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.bd127e8f-b948-40ef-a529-898ff4290d2a</attachedProcessRef>
                <guid>1c141da2-184c-4b80-be8d-fbc34bce8aad</guid>
                <versionId>0ae2ddca-f6a6-4072-924f-55526fa0cbd3</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d2131f98-1c88-4257-85a6-0395ae444161</parameterMappingId>
                    <processParameterId>2055.ee2392cb-9715-4e86-afa1-8376111abb77</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9d89e625-1b37-427f-b078-3f6d1c056dc4</guid>
                    <versionId>009f20c2-1b25-48f9-b1b8-316fa9c155a7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.904cda01-64d8-4c32-84d1-3cd5a19630af</parameterMappingId>
                    <processParameterId>2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f7f64373-5bff-4c4c-ad7c-761128baf5cb</guid>
                    <versionId>1d8c4ef7-74fa-4a56-99aa-f91026c088b5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="accountsList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.844aa3ea-32a5-40f5-b915-c8af608f3a8c</parameterMappingId>
                    <processParameterId>2055.081712aa-eb0f-473f-9a8c-8b128642a67b</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.accountsList</value>
                    <classRef>/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>29fd9f9b-9112-4c07-90c7-6643c74c24c0</guid>
                    <versionId>4d800c28-aacd-4934-9e89-d0e9ce561969</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a5f6df41-e3bc-4bd4-8ce6-50382759ce95</parameterMappingId>
                    <processParameterId>2055.72073a72-e4d4-4c67-af56-6dcd28abac8d</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1cede36d-b7bf-446e-b298-7241e397ccce</guid>
                    <versionId>4df4e04d-37f2-454d-bceb-6c7d165c3887</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e1967ee5-ee3c-4a76-b07a-20e942521263</parameterMappingId>
                    <processParameterId>2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>04be87f4-127b-45d2-a78e-81f1614c87ae</guid>
                    <versionId>*************-4d61-b974-08fe94c1e0fc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e6b90ad5-961f-4ff1-9081-746fcedf9592</parameterMappingId>
                    <processParameterId>2055.ab1eb755-2de3-44ae-893e-3b078b4b594d</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>53681077-2e46-4174-b941-83174ce1655d</guid>
                    <versionId>8dd20b22-39e9-4f1b-a072-866f3f934945</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.69b57fc0-547f-4a18-86b6-c4b4f29a1002</parameterMappingId>
                    <processParameterId>2055.c62c3bc0-46ea-4519-9703-3a27676d6d87</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>80bd4c49-d9b4-4321-b436-78e289f75729</guid>
                    <versionId>911bd8fb-a9e4-4423-b731-98b4abc71940</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.809437cd-4148-4578-9d2a-53b0b1f9df0b</parameterMappingId>
                    <processParameterId>2055.40cbdc9b-1d23-40e9-a38b-c6801fc3982e</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>959171bf-7fe6-411e-a9fd-a110c558160c</guid>
                    <versionId>9cfe3ca7-ede0-4b49-ba8f-150034fa92d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e6858c12-da88-4fc7-b3ab-b868414659ab</parameterMappingId>
                    <processParameterId>2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9f9831a2-7cd4-4ddb-89f2-af4cf9f83055</guid>
                    <versionId>a3564dc8-a182-430a-ac02-a14ea41fac3e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9eb96f7c-5277-4e1f-aa98-b73030657a5e</parameterMappingId>
                    <processParameterId>2055.c40dc139-a328-4752-90f6-254db0bdb266</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ebfc97ec-39fc-4a4b-8fa1-156fd055edbf</guid>
                    <versionId>c4f27ba8-057b-4030-b7a8-66934841dcb1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7e9343bc-3923-48a7-97ec-fd0abddd8e66</parameterMappingId>
                    <processParameterId>2055.246bbadd-892b-4d12-ad68-7d03cbc463dd</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8004983f-20ea-43d3-a980-7a45e748a651</guid>
                    <versionId>d02a85a8-0720-4572-b33b-9667652ce59f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c0bce95e-cf82-4c88-b4da-e147644a6ba0</parameterMappingId>
                    <processParameterId>2055.ef963c07-a0e0-47de-a99a-995cd5146f82</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9423ebef-5c0f-4cfb-b705-88410f7fab1d</guid>
                    <versionId>daed264b-aa95-4aa2-bc68-7767fc476c53</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d1a83be9-a517-4bef-b378-a72a62b77357</parameterMappingId>
                    <processParameterId>2055.144a896d-105b-40b0-bb38-944b8f8b858b</processParameterId>
                    <parameterMappingParentId>3012.99d7964a-c84f-4a21-961e-b6895520067f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c1b2be1b-6c34-4925-bff9-9abc39e61b67</guid>
                    <versionId>f7f7d26f-ea33-4294-9302-e9e5cdbfae95</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</processItemId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8c4aed01-82ef-4af5-991d-6b82b5212f63</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e4b</guid>
            <versionId>15f4151d-1ac4-4984-a471-5b10c2c28cb0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="325" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8c4aed01-82ef-4af5-991d-6b82b5212f63</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>b0cbe814-2988-40fe-9a5c-7601d1b0559a</guid>
                <versionId>22c4b563-4e13-4c69-9709-094c416eb74a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5b250ba9-d38e-440f-bccb-fa113ea436ac</processItemId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <name>Map data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b15453aa-4d5f-4020-bfe1-cb4f9f2cc2f9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6170</guid>
            <versionId>5250d342-96e7-44e0-8def-7e925b64f9d4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="484" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e4b</errorHandlerItem>
                <errorHandlerItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b15453aa-4d5f-4020-bfe1-cb4f9f2cc2f9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.listOf.Account();&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.results = tw.local.accountsList;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>3d656edc-ccd3-400c-be07-1545c004ab55</guid>
                <versionId>42e94580-117e-4686-812b-ad7d5a07d57e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ee486dca-3327-4687-bc88-0af1e3d018f5</processItemId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ca78124a-962f-4687-ad4e-08e13ac29527</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6172</guid>
            <versionId>8ec2bfd6-9deb-4c34-ad7a-0e34097b9081</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="654" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ca78124a-962f-4687-ad4e-08e13ac29527</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2ca16e10-93f7-4ed8-a0f8-d37945c7d028</guid>
                <versionId>ceb94596-bfe4-47da-8d25-2b02ffff1809</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</processItemId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.3f6334d1-5b4e-41e6-ac14-aafd1f4a006b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f29</guid>
            <versionId>f02793c4-b153-4764-915e-e597c6a0c85a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="346" y="29">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.3f6334d1-5b4e-41e6-ac14-aafd1f4a006b</switchId>
                <guid>5b1cd44e-d61f-4530-85d7-2aea4d2f172e</guid>
                <versionId>7e2ba777-9bd8-449a-aad8-3ea968ea7073</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.dfe28d0c-0e2d-410f-90c8-ed1c4b85b142</switchConditionId>
                    <switchId>3013.3f6334d1-5b4e-41e6-ac14-aafd1f4a006b</switchId>
                    <seq>1</seq>
                    <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-1d04</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>24977f68-aba6-40ba-a2ab-a3c1bec57424</guid>
                    <versionId>52a10946-25a8-4ef5-8fb5-cf12df24e994</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Customer Accounts" id="1.753ea3e8-d234-409e-9fb2-015400e4c9fb" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:cacheLength>3600</ns3:cacheLength>
                        
                        
                        <ns3:cachingType>false</ns3:cachingType>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.98d8998c-bb83-4e1b-a0f4-398ba1fdc383">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">//"********"&#xD;
//"********"&#xD;
"********"&#xD;
//"********"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.c041d819-6c20-4e12-bfe7-6fabc08502bf" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.********-da9b-4292-855d-60adc94a3051" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b45ac9c9-f703-4ff2-85ca-54f004008c93">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="daafd483-0a61-48f8-890a-beaf9a28b2b9" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="272" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>cad49e9f-b4f0-4422-8de1-3e2a2d094a05</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ee486dca-3327-4687-bc88-0af1e3d018f5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>46b7435c-31c7-4e42-82ca-8d89831d28a3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5b250ba9-d38e-440f-bccb-fa113ea436ac</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9288cea8-0c33-47f3-8c66-6ee5fed4083d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6ab948b0-33fc-49ea-86df-2f9f4263fdce</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9851d6aa-239a-4722-8855-2f274b239081</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0baa551e-55bd-4c25-8fbf-77c3b21f98b0</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="cad49e9f-b4f0-4422-8de1-3e2a2d094a05">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="33" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ee486dca-3327-4687-bc88-0af1e3d018f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="654" y="33" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6172</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>77c93e40-f709-4312-bf97-661c379663d5</ns16:incoming>
                        
                        
                        <ns16:incoming>ff693a6c-e025-40c1-8dcd-e96533eb0cfc</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="cad49e9f-b4f0-4422-8de1-3e2a2d094a05" targetRef="46b7435c-31c7-4e42-82ca-8d89831d28a3" name="To Get Accounts Info" id="2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.bd127e8f-b948-40ef-a529-898ff4290d2a" name="Get Accounts Info" id="46b7435c-31c7-4e42-82ca-8d89831d28a3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="10" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.5f98c471-5d9c-4982-b7a3-8890e3a0203d</ns16:incoming>
                        
                        
                        <ns16:outgoing>b4a9ac8f-7415-459f-b538-4b3876995158</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ef963c07-a0e0-47de-a99a-995cd5146f82</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.246bbadd-892b-4d12-ad68-7d03cbc463dd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ab1eb755-2de3-44ae-893e-3b078b4b594d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ee2392cb-9715-4e86-afa1-8376111abb77</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.72073a72-e4d4-4c67-af56-6dcd28abac8d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.081712aa-eb0f-473f-9a8c-8b128642a67b</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42">tw.local.accountsList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c40dc139-a328-4752-90f6-254db0bdb266</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c62c3bc0-46ea-4519-9703-3a27676d6d87</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.144a896d-105b-40b0-bb38-944b8f8b858b</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="46b7435c-31c7-4e42-82ca-8d89831d28a3" targetRef="9851d6aa-239a-4722-8855-2f274b239081" name="To is Successful" id="b4a9ac8f-7415-459f-b538-4b3876995158">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map data" id="5b250ba9-d38e-440f-bccb-fa113ea436ac">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="484" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>941e21ee-738c-4d59-8ded-f434d1a450bf</ns16:incoming>
                        
                        
                        <ns16:outgoing>77c93e40-f709-4312-bf97-661c379663d5</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.listOf.Account();&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.results = tw.local.accountsList;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="5b250ba9-d38e-440f-bccb-fa113ea436ac" targetRef="ee486dca-3327-4687-bc88-0af1e3d018f5" name="To End" id="77c93e40-f709-4312-bf97-661c379663d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.62f5f519-45a5-4257-91d6-1048eeb4f843">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();
autoObject[0] = new tw.object.toolkit.NBEINTT.Account();
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.b8fe173c-f5ac-4893-8077-5b0cf9eee3db" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.252d2d1e-e54c-4960-ab67-0caf893030b0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.306789fb-c896-429a-81a2-aaf29156fbb0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="status" id="2056.fb662bd7-8a26-45c0-88d3-7713b3ad7182" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="46b7435c-31c7-4e42-82ca-8d89831d28a3" parallelMultiple="false" name="Error" id="9288cea8-0c33-47f3-8c66-6ee5fed4083d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c3ccd836-5413-4224-86c9-c0e24ba843f2</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="643eb8ed-7389-43d2-82a0-8b8af854d2b2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="836ba6ea-6821-465a-854a-35fc4b9c526d" eventImplId="cdba0726-f161-4cee-81e4-133a6764666f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="5b250ba9-d38e-440f-bccb-fa113ea436ac" parallelMultiple="false" name="Error1" id="6ab948b0-33fc-49ea-86df-2f9f4263fdce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a7493814-ad42-4083-8ffc-fd4bed4331df</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5bc64b1e-2e58-4c6f-8668-18272ec052cd" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d0b8daac-f607-41c9-867a-b9a1043f8008" eventImplId="016f5be6-4311-408c-8eb5-09b9ac685ce3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9288cea8-0c33-47f3-8c66-6ee5fed4083d" targetRef="0baa551e-55bd-4c25-8fbf-77c3b21f98b0" name="To End Event" id="c3ccd836-5413-4224-86c9-c0e24ba843f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6ab948b0-33fc-49ea-86df-2f9f4263fdce" targetRef="0baa551e-55bd-4c25-8fbf-77c3b21f98b0" name="To End Event" id="a7493814-ad42-4083-8ffc-fd4bed4331df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="941e21ee-738c-4d59-8ded-f434d1a450bf" name="is Successful" id="9851d6aa-239a-4722-8855-2f274b239081">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="346" y="29" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b4a9ac8f-7415-459f-b538-4b3876995158</ns16:incoming>
                        
                        
                        <ns16:outgoing>941e21ee-738c-4d59-8ded-f434d1a450bf</ns16:outgoing>
                        
                        
                        <ns16:outgoing>9a14cfde-1bd8-4246-8a0c-db179477830b</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="9851d6aa-239a-4722-8855-2f274b239081" targetRef="5b250ba9-d38e-440f-bccb-fa113ea436ac" name="To Map data" id="941e21ee-738c-4d59-8ded-f434d1a450bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9851d6aa-239a-4722-8855-2f274b239081" targetRef="0baa551e-55bd-4c25-8fbf-77c3b21f98b0" name="To End Event" id="9a14cfde-1bd8-4246-8a0c-db179477830b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="0baa551e-55bd-4c25-8fbf-77c3b21f98b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="325" y="140" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9a14cfde-1bd8-4246-8a0c-db179477830b</ns16:incoming>
                        
                        
                        <ns16:incoming>c3ccd836-5413-4224-86c9-c0e24ba843f2</ns16:incoming>
                        
                        
                        <ns16:incoming>a7493814-ad42-4083-8ffc-fd4bed4331df</ns16:incoming>
                        
                        
                        <ns16:outgoing>ff693a6c-e025-40c1-8dcd-e96533eb0cfc</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0baa551e-55bd-4c25-8fbf-77c3b21f98b0" targetRef="ee486dca-3327-4687-bc88-0af1e3d018f5" name="To End" id="ff693a6c-e025-40c1-8dcd-e96533eb0cfc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9a14cfde-1bd8-4246-8a0c-db179477830b</processLinkId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</fromProcessItemId>
            <endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:-1d04</endStateId>
            <toProcessItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</toProcessItemId>
            <guid>a134692f-a396-4b50-a14d-b303fcab5e7c</guid>
            <versionId>6795a122-2e83-43be-aaed-76510b0cd4c2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</fromProcessItemId>
            <toProcessItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.77c93e40-f709-4312-bf97-661c379663d5</processLinkId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5b250ba9-d38e-440f-bccb-fa113ea436ac</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ee486dca-3327-4687-bc88-0af1e3d018f5</toProcessItemId>
            <guid>ff426f0f-7d72-4694-9463-64df81700029</guid>
            <versionId>907a2b53-a39d-4aa5-8e8d-647b3c01233d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5b250ba9-d38e-440f-bccb-fa113ea436ac</fromProcessItemId>
            <toProcessItemId>2025.ee486dca-3327-4687-bc88-0af1e3d018f5</toProcessItemId>
        </link>
        <link name="To Map data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.941e21ee-738c-4d59-8ded-f434d1a450bf</processLinkId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.5b250ba9-d38e-440f-bccb-fa113ea436ac</toProcessItemId>
            <guid>7af8e18b-d305-4b30-bda1-00687a6c01cd</guid>
            <versionId>bf200593-d97f-4163-9574-63242dce4ed2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</fromProcessItemId>
            <toProcessItemId>2025.5b250ba9-d38e-440f-bccb-fa113ea436ac</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ff693a6c-e025-40c1-8dcd-e96533eb0cfc</processLinkId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ee486dca-3327-4687-bc88-0af1e3d018f5</toProcessItemId>
            <guid>b4f92b27-763e-426c-bf34-2723ddd7b1f0</guid>
            <versionId>c520a912-a4bb-46cb-bc33-d911302fe414</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.0baa551e-55bd-4c25-8fbf-77c3b21f98b0</fromProcessItemId>
            <toProcessItemId>2025.ee486dca-3327-4687-bc88-0af1e3d018f5</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b4a9ac8f-7415-459f-b538-4b3876995158</processLinkId>
            <processId>1.753ea3e8-d234-409e-9fb2-015400e4c9fb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</fromProcessItemId>
            <endStateId>guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd</endStateId>
            <toProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</toProcessItemId>
            <guid>606affbc-9196-444e-953c-d5f8379c6423</guid>
            <versionId>cf621624-edfb-4d45-ad2a-7b500f7ff654</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.46b7435c-31c7-4e42-82ca-8d89831d28a3</fromProcessItemId>
            <toProcessItemId>2025.9851d6aa-239a-4722-8855-2f274b239081</toProcessItemId>
        </link>
    </process>
</teamworks>

