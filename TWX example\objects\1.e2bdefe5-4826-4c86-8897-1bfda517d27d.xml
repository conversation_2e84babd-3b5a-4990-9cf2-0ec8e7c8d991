<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e2bdefe5-4826-4c86-8897-1bfda517d27d" name="Create IDC Withdrawal Request">
        <lastModified>1688661261186</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.495a61ca-025c-479b-b8b2-351793bd5e53</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>1510803c-d7f0-4d4b-83a3-31ea44720c27</guid>
        <versionId>b18cbb03-cd9f-4625-b5ab-6154de5e0b4b</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcWithdrawalRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.edfc8822-8c07-4744-a64e-845e8febe86f</processParameterId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b41bdad4-911e-4640-82b4-2308386f6343</guid>
            <versionId>91afda78-2714-4cf9-8827-648739bb1fa6</versionId>
        </processParameter>
        <processParameter name="idcWithdrawalRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c409b8b1-bd8a-438f-a7e9-a6ca266bfa84</processParameterId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>790bea31-dbf9-4a08-a3e5-ef7ed64b88d7</guid>
            <versionId>d583a294-6c35-4a3c-a29d-5070a3590c87</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4cde0ff9-d2e9-46d0-b241-3db39198cf0a</processItemId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f883ecd3-77f5-4f53-bfeb-9c1bb8b3a969</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6209</guid>
            <versionId>848f7bd4-abb7-48d1-a304-9f049c4fdebc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f883ecd3-77f5-4f53-bfeb-9c1bb8b3a969</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5d2f1100-70d7-49c7-99eb-9d6d88b46196</guid>
                <versionId>2ba58c86-b286-4ef8-84f0-7c0628232637</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.495a61ca-025c-479b-b8b2-351793bd5e53</processItemId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.63a6cb20-82ee-4805-96bd-1404cefc2735</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6208</guid>
            <versionId>e08ede3e-7350-42b8-b648-d7206cf3c9e3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.20d3dcda-1e3d-435e-8747-9d8a752d295e</epvProcessLinkId>
            <epvId>/21.a6e09805-0e21-4bb5-8e25-1aa9a5db67ad</epvId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <guid>12c44674-d450-4a9e-a240-1ac832b91810</guid>
            <versionId>22647cf2-1d6f-48c3-a326-a69e27a57427</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.495a61ca-025c-479b-b8b2-351793bd5e53</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.c65ce1bd-8191-4c81-9191-f847761d7445" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Create IDC Withdrawal Request" id="1.e2bdefe5-4826-4c86-8897-1bfda517d27d">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="87a93a90-0202-4122-a3c1-78e353fde0f9">
                            
                            
                            <ns16:startEvent name="Start" id="9c4f1d54-2b3d-4238-b633-4fdbc089778d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.85f3b761-5b4e-4994-aa88-e535a2495897</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="92b71ab0-5c41-4ca3-8eed-50abac575f16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="700" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.efa64c0f-5537-4fc4-907d-e5ca62067218</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.fcd6b0d1-0eb8-43ee-99cf-b4434b42af1c</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="9c4f1d54-2b3d-4238-b633-4fdbc089778d" targetRef="2025.e3743bfd-f615-4fdd-831c-9ea419c6a020" name="Start To Coach" id="2027.85f3b761-5b4e-4994-aa88-e535a2495897">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.efa64c0f-5537-4fc4-907d-e5ca62067218" name="Set Status" id="2025.4112fdbd-9fd3-4db5-ae17-c105f5e85d99">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="553" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.efa64c0f-5537-4fc4-907d-e5ca62067218</ns16:outgoing>
                                
                                
                                <ns16:script />
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4112fdbd-9fd3-4db5-ae17-c105f5e85d99" targetRef="92b71ab0-5c41-4ca3-8eed-50abac575f16" name="OK To End" id="2027.efa64c0f-5537-4fc4-907d-e5ca62067218">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.dc7a3b30-73dc-44c2-84e9-221121e3dc9d" name="Initialization Script" id="2025.e3743bfd-f615-4fdd-831c-9ea419c6a020">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="162" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.85f3b761-5b4e-4994-aa88-e535a2495897</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.dc7a3b30-73dc-44c2-84e9-221121e3dc9d</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.idcWithdrawalRequest == null) {&#xD;
	tw.local.idcWithdrawalRequest = {};&#xD;
	tw.local.idcWithdrawalRequest.subStatus = "";&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e3743bfd-f615-4fdd-831c-9ea419c6a020" targetRef="2025.2a0ff7e4-5cd6-44a3-bf52-c93c1dc91cc7" name="To Create IDC Withdrawal Request" id="2027.dc7a3b30-73dc-44c2-84e9-221121e3dc9d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Create IDC Withdrawal Request" id="2025.2a0ff7e4-5cd6-44a3-bf52-c93c1dc91cc7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="359" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.dc7a3b30-73dc-44c2-84e9-221121e3dc9d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.979bd03c-cb95-4def-95be-ded0a88422bc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fcd6b0d1-0eb8-43ee-99cf-b4434b42af1c</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.f2c97990-5926-4fb7-842c-05211ae0afb2</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>19356d8f-d397-4b8d-8dd6-512ef7333ef2</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f06388b5-b0bf-416f-8942-3f13c655bb7f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>369e6e73-16b7-4dcc-8d8c-06b0dc5e0c29</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>293773d1-fdac-4c11-8079-f84945138a91</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2a0ff7e4-5cd6-44a3-bf52-c93c1dc91cc7" targetRef="92b71ab0-5c41-4ca3-8eed-50abac575f16" name="To End" id="2027.fcd6b0d1-0eb8-43ee-99cf-b4434b42af1c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="9936fc15-f509-4985-86a9-9bfe6df4cc4c">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.542c4525-c269-4a9f-a519-101455f38fbc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="383" y="78" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.979bd03c-cb95-4def-95be-ded0a88422bc</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f2c97990-5926-4fb7-842c-05211ae0afb2</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.979bd03c-cb95-4def-95be-ded0a88422bc</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2a0ff7e4-5cd6-44a3-bf52-c93c1dc91cc7" targetRef="2025.542c4525-c269-4a9f-a519-101455f38fbc" name="To Stay on page" id="2027.f2c97990-5926-4fb7-842c-05211ae0afb2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="979a3508-71a2-4b68-86c9-aa8ebbac35e5">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.542c4525-c269-4a9f-a519-101455f38fbc" targetRef="2025.2a0ff7e4-5cd6-44a3-bf52-c93c1dc91cc7" name="To Create IDC Withdrawal Request" id="2027.979bd03c-cb95-4def-95be-ded0a88422bc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="259a61b2-135e-4e02-b601-bd8982d08115">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a6e09805-0e21-4bb5-8e25-1aa9a5db67ad" epvProcessLinkId="7962390d-a699-4a37-8942-97edd6df80b2" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcWithdrawalRequest" itemSubjectRef="itm.12.11032488-ce0d-4b06-aafc-179676224382" isCollection="false" id="2055.edfc8822-8c07-4744-a64e-845e8febe86f" />
                        
                        
                        <ns16:dataOutput name="idcWithdrawalRequest" itemSubjectRef="itm.12.11032488-ce0d-4b06-aafc-179676224382" isCollection="false" id="2055.c409b8b1-bd8a-438f-a7e9-a6ca266bfa84" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ba36ec5f-1b37-4a0f-8f83-0bdca19e291b</processLinkId>
            <processId>1.e2bdefe5-4826-4c86-8897-1bfda517d27d</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.495a61ca-025c-479b-b8b2-351793bd5e53</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4cde0ff9-d2e9-46d0-b241-3db39198cf0a</toProcessItemId>
            <guid>70fb46e8-29cb-4f54-8a0f-ef31ca4f78ca</guid>
            <versionId>ba696f5e-4508-43a5-9320-a42d95da1efa</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.495a61ca-025c-479b-b8b2-351793bd5e53</fromProcessItemId>
            <toProcessItemId>2025.4cde0ff9-d2e9-46d0-b241-3db39198cf0a</toProcessItemId>
        </link>
    </process>
</teamworks>

