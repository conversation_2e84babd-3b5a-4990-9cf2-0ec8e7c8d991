<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4" name="Start New Request">
        <lastModified>1692103536812</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;7a521660-b968-4e9a-8098-d6878e2d936f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7865aced-8b88-46fb-8afb-95b24d99dbd9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.StartNewRequest&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23baaa89-76b5-4e31-8481-ba632892b275&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f388646-dd29-4f35-80ec-da05b0b455ba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;196c87b4-0527-4300-8d1c-648207d5f4bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;41410fac-5ffa-4423-84d0-ef54844344de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"REQUIRED"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;2879d34a-db6a-4c19-83ec-8d416d572bcb&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;5364dc2e-d1b9-4301-82f9-a709ae4ce6c7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21eb9ff6-5d95-4b38-8003-2b7832512b5f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;d0ff8d75-fbdb-4d72-8b5e-fb6071eaba22&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer10&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8c56f0cd-f45c-4613-8cd5-9d6912dd9254&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;66a0c03a-6343-4061-8b6b-823d3e14348c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell13&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6c726892-a032-43e4-8a9b-a8ed60781cf5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92204a1e-97c8-447c-8a49-15b2ff4ac024&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestNature&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c2c2d12-0304-47a0-83d8-d359b24e3e65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestNature&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09638071-ccee-4cef-8102-7dc9a9ced288&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f461f4f9-7dc3-4a40-8c2a-0bebbce3237a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;836f716d-58eb-461f-8efe-5be3eb4b2f95&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b4c1c8d-29f8-4880-8fd7-0d5a7ce9d3b5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da915fe2-8e81-4972-8b69-e79e3c3e7453&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ccf2be3-16c8-4840-80ab-0e354e70f410&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46f4a357-9193-4296-8cc5-9017e4231f52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Request_Nature&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;767a0a25-0fc6-4284-8fa9-fd2c9c3fb6f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showParentIDC();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab338278-8a26-48bc-8bb3-c7358f459d7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showParentIDC();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5d70e83-2781-411c-8b39-a83228f6d3a8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.IDCRequestNature&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;3cc19fc5-ca08-47c8-8cb5-d3f4943d18a5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell14&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;29b5b762-a9b6-4a28-8633-f12c1dffad44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c5be64d4-c3ee-4a99-85d2-f45977d134e4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestType&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43183922-6e9e-4cb4-81e8-34d82df8a405&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4c019b1-68e2-47e3-8795-f177b09ec57f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c57a5a24-fa52-491f-88b4-10efab06d633&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d7e4631-c07a-4fdd-8b5a-81ace54ca07c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3c60fe76-1012-4184-8fec-2a67a1c7a75c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.01554586-6b73-40b1-957d-c2491f071bbb&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;633add29-5d4a-4d06-8717-3f1249a95547&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f3d1be3-f910-497f-8ac9-bbd91fccb1f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a109d463-807b-4ee8-8a78-ca1cb4973f8a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCRequest.IDCRequestNature.id&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;001b10e1-b668-4fee-829b-a1a9c1c2002a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.IDCRequestType&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;cca3d07c-c0ed-4513-8429-b5d07d34beb3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92e99617-1d09-4be5-838f-6b584421aaf5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;ae123993-aca9-4a8d-8902-fe371371cb4d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell10&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d0fdf1b5-618b-4344-8eae-971f9d3b3d84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fd98122f-aae7-44c2-8b33-c4819c726022&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;cif&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;627f3eaa-84f1-42f5-830b-c5eb8fd45430&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff2ca2c7-47aa-426e-8a7b-ffc51844f60b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;939385a3-16fb-473a-80f5-6ccb450e0366&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97d791cd-3af4-4901-8a14-9a8e7eb4d15c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 8)
{
	me.setValid(false , "max lenght is 8 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba2e888c-bbe0-437f-8052-a3377762f17a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${retrieveCustomer}.setData(false);
${customerName}.setData("");
view.resetParentIDC();
if(isNaN(Number(me.getData()))){
	me.setValid(false , "must be digits");
	me.setData("");
	return false;
}else
{
	me.setValid(true);
	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.customerInformation.CIFNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;595b8111-e36c-4bce-86bc-91192373d9ed&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7e6cd0d7-4db7-4c21-8f18-8bf94a1bbd91&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3e8a6431-0f37-47a8-87ee-fa4ca764577f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@verticalAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"TOP"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21d4d2af-6f83-49fa-820e-bbb8bead3886&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"LEFT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c1ea0f51-9204-4b99-8193-3004bd7052fd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc9362a0-4434-428b-85d4-5c5aca015ab7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;    &lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c24ca3bb-fbce-49e7-8f74-470dd2bb372c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;371b36b5-5fd3-4f74-83a0-de969af5bfcd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3769aba-ecd5-44f9-8ec4-7ea290f0dae5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"M"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7f9c8791-d151-4808-893a-230f7b349706&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e3afd333-4502-4a07-8541-f6c78a9ed615&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveCustomer&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71294c7d-c5f2-447c-8260-670d149cffc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RetrieveCustomer&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;12e9ea68-bd18-4976-87c2-eb90bd4c26cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46d11f38-25b3-4bbc-86ce-67eb01fce765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b9d07847-6425-4167-855f-29111db1fe59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.getCustomer(${cif}.getText());

	if (${cif}.getText() != null &amp;amp;&amp;amp; ${cif}.getText() != "") {
		if(Number(${cif}.getText())){
			if (${cif}.getText().length == 8) {
				view.getCustomer();
				${cif}.setValid(true);
			}else{
				${cif}.setValid(false ,"CIF must be 8 digits");
		//		alert("CIF must be 8 digits");
			}
		
		}else{
			${cif}.setValid(false ,"CIF must be digit");
		}
	}
	else{
	${cif}.setValid(false ,"CIF is can not be empty");
	}
	&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ed2426e-f578-4162-8b7e-2ea1ac2439a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;510d7676-e12b-430d-89dd-29b34697c39c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e1d623cd-3b04-44d1-8c31-cdcd3d56f074&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae326d2e-153f-47c3-8d0a-4b8c6fdb400f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;956ab4c4-8f9c-4490-85bd-bf1e824dcd30&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;732dc6c9-4f2d-44b8-80d7-1a59e5bd2d74&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8aa379dd-b9a3-4af1-8ff4-66e01e3e1b42&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ec87df2-9f8f-42ac-8c0e-e93a9588466c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72ea5daf-a4f7-4690-8454-ed777c76e86c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.retrieveCustomer&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;b23dcb5d-4cc0-4a32-8407-d7f55aed3424&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3171a2df-013c-4ad5-81cb-71c351096303&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3b72914a-71cb-4a54-814d-84d70f0efabf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;customerName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e134240-ffea-4bfd-8f00-651d326eb765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fff71f65-02a4-439e-892c-c6681e4e21ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b28f0b8-bf70-4854-87c2-6b2751829892&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d89c0ae-7fa1-4b94-8691-58e9fd74a988&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.customerInformation.customerName&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;73c29f82-fd1b-4ce8-8222-6bc286876ead&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7067ee40-4023-4f1f-85b1-ad6b9ce9c4b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;beb1dcd4-cabd-47e5-88f8-42f95fd9693b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer11&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93cda58c-4360-4ba6-8fc3-267ebfb54426&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;2f0412f0-93de-45c6-8d7c-1ce3a7b287f2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4318d7d-3b25-4e91-813d-9f7c3f000caa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3554b829-d315-482f-81c1-7d6f9b2f3e33&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;parentIDCRequestNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2dd36f0-f0cc-45b2-84ea-da2d0578c142&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ParentIDCRequestNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37ac46cc-de3e-45d9-88c5-65c22ec887f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6837f09c-e6d1-4540-853f-75778a8ec2b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fbfac0e-22f7-47b6-830c-5d1ce1c80c00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;REQUIRED&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31c5198e-8f21-460e-87f7-06b22484f698&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 14)
{
	me.setValid(false , "max lenght is 14 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1328081b-9c99-4c96-8c84-b9266dcccbdb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetParentIDC2();
if(isNaN(Number(me.getData()))){
	me.setValid(false , "must be digits");
	me.setData("");
	return false;
}else
{
	me.setValid(true);
	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.ParentIDCRequestNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e9447e1e-b28b-42b5-82b3-2cb2dfa83adf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;61e9bb79-39d6-410f-87df-885f32babd85&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RequestDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1bfc731d-63d5-4a95-82d8-5b15ff0c42dc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;902e553b-a8ca-4f8f-82d1-c7f405a58adf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd6aadb9-3683-4f39-894d-bcc50da90344&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.parentIDCRequest.appInfo.requestDate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;8caa57c5-0b79-4571-8da6-0af4ad775cb7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7463a649-b00f-48d2-8603-0b2403b5f1cb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cad13f87-77dc-4b87-8943-03e8bf9c3302&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e93a6a3f-9aba-4f98-8d33-dbbd2af22c3a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;    &lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ca500e9b-a675-4a8d-8585-eeb384bdc62e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67f3b7ad-0d56-45dc-8400-cbfd0b3cc924&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aa73026a-570d-4fec-86d6-1cf7fbe40654&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"M"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6194267-2aad-4c46-8a09-7c4d4bc4ad16&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e0d5c089-42a2-48c5-816f-c346a19bd1c4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveRequest&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f81ad5e-3073-4f3c-8fdb-fa80e466d357&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.RetrieveRequest&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1877474-242a-44c3-8dd8-39906da0aa08&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;970d116e-fb22-4003-8d44-a8a2c03fe00c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;095bafa3-5ff9-4090-8d01-f3b72bd7eff2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;

if (${parentIDCRequestNumber}.getText() != null &amp;amp;&amp;amp; ${parentIDCRequestNumber}.getText() != "" &amp;amp;&amp;amp; ${parentIDCRequestNumber}.getText() != undefined) {
	if(Number(${parentIDCRequestNumber}.getText())){
		if (${parentIDCRequestNumber}.getText().length == 14) {
			view.getRequest();
			${parentIDCRequestNumber}.setValid(true);
		}else{
			${parentIDCRequestNumber}.setValid(false ,"Parent IDC Request Number must be 14 digits");

		}
	}else{
			${parentIDCRequestNumber}.setValid(false ,"parentIDCRequestNumber must be digit");
		}
	
}
else{
${parentIDCRequestNumber}.setValid(false ,"Parent IDC Request Number can not be empty");

}
me.setData(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;101f7bdc-e343-4cd0-8a83-d773bb0fd425&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;619c8494-50e6-4aa2-8bad-1a956a87ea83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a4680b6-7583-4caa-885e-024f4b2eac4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ac598e5-47db-4ba2-8c07-65e2edcdb24c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b7489fc5-93b5-4647-82c2-734383fad2e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;744483a1-9ec6-447e-8e18-de29fc8cb138&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.retrieveRequest&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0097d21b-1eba-48f8-8d53-ec867b6b8e71&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;beneficiaryName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4860fe5-722f-47bb-8084-35a41870e264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0ea9350-3d56-4fab-88fa-076acd2a7f11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23e76d2c-baf4-46a8-85a6-9759b9ad626d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;add821fe-e135-49d4-823e-60fdd2f6d003&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.parentIDCRequest.financialDetails.beneficiaryDetails.name&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c5c70b75-c831-42bd-8d88-f5e02feda034&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveCustomerServiceCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b47144cc-f64f-42db-8b4d-99cc6b3372ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve Customer&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c883af9-0c6a-48d7-8032-eaa1e7ea4977&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab3aec39-0d5d-4c83-8d92-a737bb5eeb17&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2e4704a5-e147-4a33-82f0-a05b62196cc3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCRequest.customerInformation.CIFNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb24694c-b5ee-4337-82da-73b83b2f3238&lt;/ns2:id&gt;&lt;ns2:optionName&gt;autoRun&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d86fd02f-9c9e-4af2-8230-862d929604b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21f5e490-0c58-4bbd-8c55-28a3d4b7ad49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.473ca24e-c03e-4a25-b37a-58cd047b0fff&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a48f7f4-a361-40c3-831c-afb4e233a084&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;alert("This CIF is not valid");
view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e63fab2f-be83-479a-8099-c9d01eee6221&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${retrieveCustomer}.setData(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;793ee1ae-78af-443e-82d2-734635d85ddf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${retrieveCustomer}.setData(true);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCRequest.customerInformation&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9d0f8fef-24dd-4492-87b9-812ade5bd35a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveRequestServiceCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a1dc9a7-a468-4c60-84e3-45f57feba79e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve Request&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;504ede76-e47a-4197-8a7c-db5ce71cfc32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;950878d0-c340-4962-80cf-f9ad9fa443b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d36b658d-171a-42f9-8976-28fac1cdfedf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.f19131b2-2481-4c62-a40d-8f829cdeb66a&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af44026e-8a11-436c-83e6-0f2d550b90d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bcb19623-4213-4e15-8354-fb7bd68e5faf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4db57704-335f-4409-8957-e3ef3c94eb0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89f5016b-cdfd-468c-8df3-0613f3420ce6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.parentIDCRequest&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>e01ebc55-3e2e-4d45-bfa5-3df718125747</guid>
        <versionId>e86d619b-ccd6-4aee-ba0a-271a3fca7bfb</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.7593ad95-c8b3-413e-ac87-cb6a7d309e95</coachViewBindingTypeId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>78232d8b-18ef-4f91-b7d7-60bceb83d598</guid>
            <versionId>ac726d00-7212-494a-ab06-f7df01888f77</versionId>
        </bindingType>
        <configOption name="parentIDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bafc46f4-a5e9-4bb3-8646-76dd4f68c6e8</coachViewConfigOptionId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>1e51e633-89d8-439a-8e97-ad6489fe1970</guid>
            <versionId>1f30434e-0ea4-4f09-8fb8-0d32e63c9620</versionId>
        </configOption>
        <configOption name="retrieveRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.687d822d-e147-4006-ab6b-07119547cb76</coachViewConfigOptionId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>75198df6-882e-483a-b997-e5edd61aca0b</guid>
            <versionId>0b26185c-8f27-4df1-b2a7-3d726bd10011</versionId>
        </configOption>
        <configOption name="retrieveCustomer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bf9ab42e-6fbc-48b0-a38f-7440d364ffac</coachViewConfigOptionId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>7887bf99-9f53-4f5a-b834-84f3104c778c</guid>
            <versionId>0a953788-2bb0-4811-9785-8869c24888c1</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.89ee3517-41d3-4ad0-a8eb-9a50ec877b33</coachViewConfigOptionId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>348cb0ad-82a0-4eed-9201-f08652575b87</guid>
            <versionId>1a72acfc-025c-40d5-9708-3d2d7772ffe2</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bda6046e-14d4-4d1f-9958-c546948ba781</coachViewConfigOptionId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>1337120d-5e58-4b51-9fe0-2c01a0f5ad2d</guid>
            <versionId>b632f416-ad16-498a-9c0a-cf13ff7f021d</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.ba9e3aa2-7f8b-4b27-88e0-7b41c76defb8</coachViewInlineScriptId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.getCustomer = function () {&#xD;
	this.ui.get("retrieveCustomerServiceCall").execute(this.ui.get("cif").getData());&#xD;
	}&#xD;
&#xD;
this.getRequest = function (callback) {&#xD;
	this.ui.get("retrieveRequestServiceCall").execute(this.ui.get("parentIDCRequestNumber").getData());&#xD;
	}&#xD;
&#xD;
this.showParentIDC = function  () {&#xD;
	if (this.context.binding.get("value").get("IDCRequestNature").get("englishdescription") == "Update Request") {&#xD;
		this.ui.get("parentIDCRequestNumber").setVisible(true,true);&#xD;
		this.ui.get("requestDate").setVisible(true,true);&#xD;
		this.ui.get("retrieveRequest").setVisible(true,true);&#xD;
		this.ui.get("beneficiaryName").setVisible(true,true);&#xD;
	}else{&#xD;
		this.ui.get("parentIDCRequestNumber").setVisible(false,true);&#xD;
		this.ui.get("requestDate").setVisible(false,true);&#xD;
		this.ui.get("retrieveRequest").setVisible(false,true);&#xD;
		this.ui.get("beneficiaryName").setVisible(false,true);&#xD;
		this.resetParentIDC();&#xD;
	}&#xD;
}&#xD;
this.resetParentIDC = function  () {&#xD;
	this.ui.get("parentIDCRequestNumber").setData("");&#xD;
	this.ui.get("requestDate").setData("");&#xD;
	this.ui.get("retrieveRequest").setData(false);&#xD;
	this.ui.get("beneficiaryName").setData("");&#xD;
}&#xD;
this.resetParentIDC2 = function  () {&#xD;
	&#xD;
	this.ui.get("requestDate").setData("");&#xD;
	this.ui.get("retrieveRequest").setData(false);&#xD;
	this.ui.get("beneficiaryName").setData("");&#xD;
}&#xD;
&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>fccd7e6f-0278-4de1-b622-7a775db9cfe3</guid>
            <versionId>2e033d39-0222-4e06-a4f8-d1041f865759</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.5c98404a-b309-4bf1-ad3e-77f72be58251</coachViewLocalResId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>90d4aa2b-1818-43d9-97e8-081a03552f41</guid>
            <versionId>d5aace92-903a-4d6d-ba8a-39251ce80159</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.7a9fc7ca-9c17-46a1-883b-41bf6902b301</coachViewLocalResId>
            <coachViewId>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>ae89bf53-b198-461b-a2a6-cbfd4b6c7867</guid>
            <versionId>1bf13993-8d28-4705-bd8b-5bde1099b69d</versionId>
        </localization>
    </coachView>
</teamworks>

