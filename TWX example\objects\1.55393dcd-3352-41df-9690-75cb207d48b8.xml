<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.55393dcd-3352-41df-9690-75cb207d48b8" name="Get Exchange Rate">
        <lastModified>1692506683846</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>1de891e9-b344-4efb-bb90-4498ce0994d4</guid>
        <versionId>44eda8be-e2fb-4ce2-ad48-42ed1f19b2af</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e7d" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.295feb05-be55-4b19-b56f-fc927ced0ecd"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":112,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"fc7fc946-65c5-4b3e-b179-6fa9d9c05525"},{"incoming":["dbdef4fe-565e-460d-91ee-c95019098fbf","f3d5d531-6ec6-4073-8369-17bc29f2d408","af679eba-e186-4e16-8dc3-2cc68bc1688f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":687,"y":108,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"63fef019-5b36-4c2e-90a7-e381f8020c84"},{"targetRef":"9c63ae9f-967b-4505-89f7-503241361ad8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Input","declaredType":"sequenceFlow","id":"2027.295feb05-be55-4b19-b56f-fc927ced0ecd","sourceRef":"fc7fc946-65c5-4b3e-b179-6fa9d9c05525"},{"startQuantity":1,"outgoing":["39b98aca-ae2d-41ed-9852-ad8be2acf804"],"incoming":["9bce7988-90df-4206-a4ac-1e714655868f"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":347,"y":89,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Exchange Rate","dataInputAssociation":[{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"1\""]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.exCurrency.fromCurrency"]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.exCurrency.toCurrency"]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateType"]}}]},{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateSubType"]}}]},{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1471b575-67db-46fd-8cf6-11ae2ac38d16","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"1b344bb2-d08b-490c-8f98-7c08a4885777","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To isSuccessful","declaredType":"sequenceFlow","id":"39b98aca-ae2d-41ed-9852-ad8be2acf804","sourceRef":"1471b575-67db-46fd-8cf6-11ae2ac38d16"},{"startQuantity":1,"outgoing":["dbdef4fe-565e-460d-91ee-c95019098fbf"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":517,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ae195407-3b55-4f0f-9c03-f5aa439f8584","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.convertedAmount != null || tw.local.convertedAmount != undefined) {\r\n\t\r\n\ttw.local.results = parseFloat(tw.local.convertedAmount.Amount);\r\n}else{\r\n\ttw.local.results = 0;\r\n}\r\n"]}},{"targetRef":"63fef019-5b36-4c2e-90a7-e381f8020c84","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"dbdef4fe-565e-460d-91ee-c95019098fbf","sourceRef":"ae195407-3b55-4f0f-9c03-f5aa439f8584"},{"startQuantity":1,"outgoing":["9bce7988-90df-4206-a4ac-1e714655868f"],"incoming":["2027.295feb05-be55-4b19-b56f-fc927ced0ecd"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":89,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Set Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"9c63ae9f-967b-4505-89f7-503241361ad8","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tvar input = JSON.parse(tw.local.data);\r\n\ttw.local.exCurrency = {};\r\n\ttw.local.exCurrency.fromCurrency = input.ccFrom;\r\n\ttw.local.exCurrency.toCurrency = input.ccTo;\r\n\ttw.local.rateSubType = input.sType;\r\n\ttw.local.rateType = input.type;\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"targetRef":"1471b575-67db-46fd-8cf6-11ae2ac38d16","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Exchange Rate","declaredType":"sequenceFlow","id":"9bce7988-90df-4206-a4ac-1e714655868f","sourceRef":"9c63ae9f-967b-4505-89f7-503241361ad8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.ExchangeCurrency();\nautoObject.toCurrency = \"\";\nautoObject.fromCurrency = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1","name":"exCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.8b72ffbb-f09a-4fb9-bdd8-0e70dfc96396"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.4edd1538-bcf8-40d1-857a-f62ee98e8560"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.926b730c-ab77-45f9-8aa0-e1e77aff5836"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.c4deea0b-b9cb-4c21-8549-5c90d5ffe0b2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateSubType","isCollection":false,"declaredType":"dataObject","id":"2056.3ae8322d-731c-47ab-8572-6b197d0a6196"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateType","isCollection":false,"declaredType":"dataObject","id":"2056.01ef5a6d-8f6c-4c51-80da-9c3c11fd1c1c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.a83431ed-cef2-497e-8d88-11520a9eda6d"},{"parallelMultiple":false,"outgoing":["0dc10609-e6d8-49fd-8b88-5541f62daab6"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"41ee1676-777f-4dea-81bd-480ebb8001fa"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c1d66ade-b8a4-403c-8ff1-cead0d02cd34","otherAttributes":{"eventImplId":"8226e341-7ca6-4de7-89e0-61ae1af4369f"}}],"attachedToRef":"9c63ae9f-967b-4505-89f7-503241361ad8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":147,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"b7eda709-5222-47fe-897b-7cb9c3fa8e51","outputSet":{}},{"parallelMultiple":false,"outgoing":["52da5dde-2d9b-4312-8768-d8301149fab7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"beb4c792-c964-4b26-87a9-26408a2ae829"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"592a0386-053b-496d-8963-c84a985a43c8","otherAttributes":{"eventImplId":"7a146d36-4584-494f-8157-c43d5958da7b"}}],"attachedToRef":"1471b575-67db-46fd-8cf6-11ae2ac38d16","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":147,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"6509363a-f179-499a-8335-420ee4e03b0d","outputSet":{}},{"targetRef":"6ef9cfdf-8492-48e0-8fa2-98ebb37e6350","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0dc10609-e6d8-49fd-8b88-5541f62daab6","sourceRef":"b7eda709-5222-47fe-897b-7cb9c3fa8e51"},{"targetRef":"6ef9cfdf-8492-48e0-8fa2-98ebb37e6350","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"52da5dde-2d9b-4312-8768-d8301149fab7","sourceRef":"6509363a-f179-499a-8335-420ee4e03b0d"},{"outgoing":["f3d5d531-6ec6-4073-8369-17bc29f2d408","125a13f9-741d-4e93-8ad3-aa99b64891d1"],"incoming":["39b98aca-ae2d-41ed-9852-ad8be2acf804"],"default":"f3d5d531-6ec6-4073-8369-17bc29f2d408","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":536,"y":104,"declaredType":"TNodeVisualInfo","height":32}]},"name":"isSuccessful","declaredType":"exclusiveGateway","id":"1b344bb2-d08b-490c-8f98-7c08a4885777"},{"targetRef":"63fef019-5b36-4c2e-90a7-e381f8020c84","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"f3d5d531-6ec6-4073-8369-17bc29f2d408","sourceRef":"1b344bb2-d08b-490c-8f98-7c08a4885777"},{"targetRef":"6ef9cfdf-8492-48e0-8fa2-98ebb37e6350","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"125a13f9-741d-4e93-8ad3-aa99b64891d1","sourceRef":"1b344bb2-d08b-490c-8f98-7c08a4885777"},{"startQuantity":1,"outgoing":["af679eba-e186-4e16-8dc3-2cc68bc1688f"],"incoming":["52da5dde-2d9b-4312-8768-d8301149fab7","0dc10609-e6d8-49fd-8b88-5541f62daab6","125a13f9-741d-4e93-8ad3-aa99b64891d1"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":364,"y":210,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6ef9cfdf-8492-48e0-8fa2-98ebb37e6350","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"63fef019-5b36-4c2e-90a7-e381f8020c84","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"af679eba-e186-4e16-8dc3-2cc68bc1688f","sourceRef":"6ef9cfdf-8492-48e0-8fa2-98ebb37e6350"}],"laneSet":[{"id":"ad0f84fd-ae7d-49ec-a804-9ece248f2124","lane":[{"flowNodeRef":["fc7fc946-65c5-4b3e-b179-6fa9d9c05525","63fef019-5b36-4c2e-90a7-e381f8020c84","1471b575-67db-46fd-8cf6-11ae2ac38d16","ae195407-3b55-4f0f-9c03-f5aa439f8584","9c63ae9f-967b-4505-89f7-503241361ad8","b7eda709-5222-47fe-897b-7cb9c3fa8e51","6509363a-f179-499a-8335-420ee4e03b0d","1b344bb2-d08b-490c-8f98-7c08a4885777","6ef9cfdf-8492-48e0-8fa2-98ebb37e6350"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":404}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5bd9c25e-992c-4235-941f-ac29b23c9fa3","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Exchange Rate","declaredType":"process","id":"1.55393dcd-3352-41df-9690-75cb207d48b8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.a0f02886-5e9f-473a-8ace-7a356e991464"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"'{\"ccFrom\" : \"EUR\", \"ccTo\" : \"USD\" , \"type\":\"SELL\" , \"sType\":\"S\"}'\r\n"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.4f02925b-11da-44b4-9ffd-ff1068446fe0"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4f02925b-11da-44b4-9ffd-ff1068446fe0</processParameterId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>08bd566e-168b-4f78-893e-8ac615041ef5</guid>
            <versionId>13799dcf-5ed0-4778-9f97-eea3013fb2c1</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce</processParameterId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>386e8a45-4196-44e3-b20b-9d7613f297d3</guid>
            <versionId>c7758107-169f-448a-b16e-93f4fc65ec38</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a0f02886-5e9f-473a-8ace-7a356e991464</processParameterId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c8deea9f-9c64-43f6-ab64-efb6bffd43ce</guid>
            <versionId>daf6d44e-6957-4ad3-90ee-91db0caa2405</versionId>
        </processParameter>
        <processVariable name="exCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8b72ffbb-f09a-4fb9-bdd8-0e70dfc96396</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.ExchangeCurrency();
autoObject.toCurrency = "";
autoObject.fromCurrency = "";
autoObject</defaultValue>
            <guid>db540b2e-c091-4119-96b9-8d9e2c40a363</guid>
            <versionId>4e33dc1e-3785-4439-88f2-2e129a9fab6a</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4edd1538-bcf8-40d1-857a-f62ee98e8560</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>55782e02-f89f-4191-9476-867d5aac4fee</guid>
            <versionId>dee37625-f801-4899-b855-d4a4eb5d9ed8</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.926b730c-ab77-45f9-8aa0-e1e77aff5836</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>df69af7b-e308-4b6f-898b-b2476a5a19e0</guid>
            <versionId>bfd5eb79-aa9f-4cb9-bad5-9eece0378aad</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c4deea0b-b9cb-4c21-8549-5c90d5ffe0b2</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ec2307dc-e7e3-40f9-8054-a3cdeb7aace6</guid>
            <versionId>c1a8f4e9-da81-4e69-ac41-61347d18d562</versionId>
        </processVariable>
        <processVariable name="rateSubType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3ae8322d-731c-47ab-8572-6b197d0a6196</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9a7919d7-73d1-472e-af7e-30816861a67d</guid>
            <versionId>c92c3168-a10b-4313-a62f-090eb323fdb1</versionId>
        </processVariable>
        <processVariable name="rateType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.01ef5a6d-8f6c-4c51-80da-9c3c11fd1c1c</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9bff2764-b990-4b85-a0f9-b1063ea8c6c4</guid>
            <versionId>765572ba-6051-4f22-9182-7163a0076e5f</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a83431ed-cef2-497e-8d88-11520a9eda6d</processVariableId>
            <description isNull="true" />
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6b85909c-2426-46fb-a3a4-2a9283526ccc</guid>
            <versionId>01f302e7-921d-459e-bf99-79c26213ad7e</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ae195407-3b55-4f0f-9c03-f5aa439f8584</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>Map Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8a7e2d57-1459-4bd7-958c-fa38d1579079</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6156</guid>
            <versionId>304b9bd5-53bf-42f7-b31f-c15b3b4d07e0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="517" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8a7e2d57-1459-4bd7-958c-fa38d1579079</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.convertedAmount != null || tw.local.convertedAmount != undefined) {&#xD;
	&#xD;
	tw.local.results = parseFloat(tw.local.convertedAmount.Amount);&#xD;
}else{&#xD;
	tw.local.results = 0;&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>f04f9546-16a6-4b3d-9ae0-aba8b8ad161f</guid>
                <versionId>03937081-c127-4c9d-8c29-e081245c0968</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>Get Exchange Rate</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.363a59ae-db14-4280-984c-83e85cedd551</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6157</guid>
            <versionId>496f8ae2-b5c8-4587-82ce-a8314a43f221</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.d63aa5de-5475-4ebb-878e-515dee27cc10</processItemPrePostId>
                <processItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>937dcee6-20f5-4c7a-90bf-517b20973ad6</guid>
                <versionId>e29ee068-8864-4ffe-be07-b9757d39c97f</versionId>
            </processPrePosts>
            <layoutData x="347" y="89">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e79</errorHandlerItem>
                <errorHandlerItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.363a59ae-db14-4280-984c-83e85cedd551</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>499bb235-c313-402a-a17d-e25e022ad126</guid>
                <versionId>91355a42-8f44-40d6-8ad3-e7dc3cea8fd5</versionId>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4348d472-8863-4cf6-813d-cc2d8016c5c3</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateSubType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1455b609-a510-427a-836f-689eba02c4a2</guid>
                    <versionId>0a84e043-cda0-490b-a5e9-7c769ce31ee1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7d06c5af-b5b5-46ba-a6ab-6cd9451071f0</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>*************-478b-954b-f3e063615b62</guid>
                    <versionId>39bfa432-2f71-4e8a-b57c-8a598f528cb6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.11dcb88b-c897-482f-9a35-d24cba369b34</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ce9c030c-2ec7-4b2a-af37-4dbd41c2cbb0</guid>
                    <versionId>3d0599d0-ba43-45ea-87f3-2a2296827909</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3eaf14c0-efc8-4c28-a89c-aa485b0429b1</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>db0b8790-26d3-43cb-9c84-8a72ed936f27</guid>
                    <versionId>52a97019-527a-40b3-a9ce-c3205c27aebf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dc867d3a-4721-4ff1-bab3-79733a507f22</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"1"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b71892d6-ee84-4696-8d96-07dee89b18b9</guid>
                    <versionId>563f4af7-b00d-4dc1-9d2e-6177744b67e1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b6c99426-cf58-4c7d-92f7-99cf8d5f32ca</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a56be8ae-c8bf-44ad-a6bb-ac44b4c0713b</guid>
                    <versionId>581dd8f8-de31-4a27-a807-721af736f5ae</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.78671173-5e06-44ca-b419-c20460541a12</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b1759915-1313-447a-9451-422052453f1d</guid>
                    <versionId>781e6832-652e-4854-a7fa-71ff4c318566</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8414e950-4250-4191-b82d-e61e400fcd89</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f6f2897a-ae38-432a-ac0e-c08cc2c71d67</guid>
                    <versionId>b7863252-791b-4447-ac28-fce7634e2be9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c2723aa2-62b8-43a9-b564-759c0aef780b</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>01ffdf4b-93fe-456b-bc6a-2a42fce50128</guid>
                    <versionId>b9829f70-c1a7-4c6c-b631-1a7895ed88d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.498cb3b4-4dc6-46b0-905e-e1d6fae5a26a</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6e64e357-0818-4ce9-8a0c-20c71f662ddc</guid>
                    <versionId>cbb0e0c5-a449-4fd0-86c5-bd3495dfc322</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.47d07aa2-0439-43f9-8e5a-f43ff7eff024</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.exCurrency.toCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>59037c32-2083-41e3-896b-35fb6a3be7bb</guid>
                    <versionId>d807cd0b-8aeb-459b-af87-e3ba1d51e427</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bd425db9-e606-4ad3-a652-5b80e88be2da</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>967d43d0-b0d6-4840-a73a-5e6094c81011</guid>
                    <versionId>dbe689fb-3741-49a6-9a85-e17b07a2f3da</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e83e0b22-db45-4930-9a95-16c20701eb92</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.exCurrency.fromCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9acd1442-630c-4670-9ced-51e969cf33c5</guid>
                    <versionId>edb46a35-2127-4f3f-a125-07c2c2105402</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.65521e41-4c0c-439d-a9ca-0a1fa8333f81</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>920ec894-2ef7-4869-884e-a6dec4669c4b</guid>
                    <versionId>f184dc0c-04bf-4ef4-9621-c3b4206c2e46</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ed088ef9-94e8-4b11-b46a-238daeb5165c</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.363a59ae-db14-4280-984c-83e85cedd551</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c71901a9-8f80-4f64-aedf-6dc799e5784c</guid>
                    <versionId>f3ca208e-6dc4-40ee-968f-a4ff127e3c6e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>isSuccessful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.516b8264-1111-41ca-b503-b7cdc2d34a2a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f08</guid>
            <versionId>5ff004c4-d856-493c-ad53-5d63d37496de</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="536" y="104">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.516b8264-1111-41ca-b503-b7cdc2d34a2a</switchId>
                <guid>ec00b37b-e4ef-41ff-8899-27a83358ce33</guid>
                <versionId>776e00a4-07d8-4fe9-846d-fc578c561ed8</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.e6a42371-7b3f-4425-80d3-efce9c7e4292</switchConditionId>
                    <switchId>3013.516b8264-1111-41ca-b503-b7cdc2d34a2a</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e7c</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>8950643b-e070-4551-8583-fe5fd11392c4</guid>
                    <versionId>9967fe97-b839-46a4-85ad-07f6cd27eebc</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>Set Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c59ec292-0a83-4206-8bf9-c40f5883f4ec</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6159</guid>
            <versionId>8b0bf10e-0e22-4070-939c-9a092c7f82a3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.8e20e1a4-7bca-4e7b-93a6-d5d60033aaa3</processItemPrePostId>
                <processItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>648644c4-0229-4bb7-bb88-909458316eb7</guid>
                <versionId>465aee1f-6637-44de-8c12-5f43b3aabe01</versionId>
            </processPrePosts>
            <layoutData x="177" y="89">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e79</errorHandlerItem>
                <errorHandlerItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c59ec292-0a83-4206-8bf9-c40f5883f4ec</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
	tw.local.exCurrency = {};&#xD;
	tw.local.exCurrency.fromCurrency = input.ccFrom;&#xD;
	tw.local.exCurrency.toCurrency = input.ccTo;&#xD;
	tw.local.rateSubType = input.sType;&#xD;
	tw.local.rateType = input.type;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>10797e15-ab26-485a-b19b-535d558f92c2</guid>
                <versionId>4213c95e-e911-40f9-862c-ab828e4d4886</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.921afd29-d4d8-471e-8827-d72301b2a007</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e79</guid>
            <versionId>8cf6b955-48c3-496a-b024-4b9fbc3cf174</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="364" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.921afd29-d4d8-471e-8827-d72301b2a007</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>68e354da-9319-4bb4-950e-60aafed4acd1</guid>
                <versionId>95bbc1fa-5dfd-439c-9f90-cc75ad035c2a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</processItemId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2cce3472-b43c-446b-8c1d-2949bbe07ae6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158</guid>
            <versionId>9dd8ade3-4975-4f03-8f68-366f475901cf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="687" y="108">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2cce3472-b43c-446b-8c1d-2949bbe07ae6</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5becc184-cfb2-423c-812f-ca1f95324e1c</guid>
                <versionId>27a4cb93-a739-430d-b994-abe6546472d1</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="112">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Exchange Rate" id="1.55393dcd-3352-41df-9690-75cb207d48b8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.4f02925b-11da-44b4-9ffd-ff1068446fe0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">'{"ccFrom" : "EUR", "ccTo" : "USD" , "type":"SELL" , "sType":"S"}'&#xD;
</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.a0f02886-5e9f-473a-8ace-7a356e991464" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="ad0f84fd-ae7d-49ec-a804-9ece248f2124">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5bd9c25e-992c-4235-941f-ac29b23c9fa3" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="404" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>fc7fc946-65c5-4b3e-b179-6fa9d9c05525</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>63fef019-5b36-4c2e-90a7-e381f8020c84</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1471b575-67db-46fd-8cf6-11ae2ac38d16</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ae195407-3b55-4f0f-9c03-f5aa439f8584</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9c63ae9f-967b-4505-89f7-503241361ad8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b7eda709-5222-47fe-897b-7cb9c3fa8e51</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6509363a-f179-499a-8335-420ee4e03b0d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1b344bb2-d08b-490c-8f98-7c08a4885777</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="fc7fc946-65c5-4b3e-b179-6fa9d9c05525">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="112" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.295feb05-be55-4b19-b56f-fc927ced0ecd</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="63fef019-5b36-4c2e-90a7-e381f8020c84">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="687" y="108" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>dbdef4fe-565e-460d-91ee-c95019098fbf</ns16:incoming>
                        
                        
                        <ns16:incoming>f3d5d531-6ec6-4073-8369-17bc29f2d408</ns16:incoming>
                        
                        
                        <ns16:incoming>af679eba-e186-4e16-8dc3-2cc68bc1688f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="fc7fc946-65c5-4b3e-b179-6fa9d9c05525" targetRef="9c63ae9f-967b-4505-89f7-503241361ad8" name="To Set Input" id="2027.295feb05-be55-4b19-b56f-fc927ced0ecd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" name="Get Exchange Rate" id="1471b575-67db-46fd-8cf6-11ae2ac38d16">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="89" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9bce7988-90df-4206-a4ac-1e714655868f</ns16:incoming>
                        
                        
                        <ns16:outgoing>39b98aca-ae2d-41ed-9852-ad8be2acf804</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"1"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.exCurrency.fromCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.exCurrency.toCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateSubType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1471b575-67db-46fd-8cf6-11ae2ac38d16" targetRef="1b344bb2-d08b-490c-8f98-7c08a4885777" name="To isSuccessful" id="39b98aca-ae2d-41ed-9852-ad8be2acf804">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Data" id="ae195407-3b55-4f0f-9c03-f5aa439f8584">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="517" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>dbdef4fe-565e-460d-91ee-c95019098fbf</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.convertedAmount != null || tw.local.convertedAmount != undefined) {&#xD;
	&#xD;
	tw.local.results = parseFloat(tw.local.convertedAmount.Amount);&#xD;
}else{&#xD;
	tw.local.results = 0;&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ae195407-3b55-4f0f-9c03-f5aa439f8584" targetRef="63fef019-5b36-4c2e-90a7-e381f8020c84" name="To End" id="dbdef4fe-565e-460d-91ee-c95019098fbf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Input" id="9c63ae9f-967b-4505-89f7-503241361ad8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="89" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.295feb05-be55-4b19-b56f-fc927ced0ecd</ns16:incoming>
                        
                        
                        <ns16:outgoing>9bce7988-90df-4206-a4ac-1e714655868f</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
	tw.local.exCurrency = {};&#xD;
	tw.local.exCurrency.fromCurrency = input.ccFrom;&#xD;
	tw.local.exCurrency.toCurrency = input.ccTo;&#xD;
	tw.local.rateSubType = input.sType;&#xD;
	tw.local.rateType = input.type;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="9c63ae9f-967b-4505-89f7-503241361ad8" targetRef="1471b575-67db-46fd-8cf6-11ae2ac38d16" name="To Get Exchange Rate" id="9bce7988-90df-4206-a4ac-1e714655868f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1" isCollection="false" name="exCurrency" id="2056.8b72ffbb-f09a-4fb9-bdd8-0e70dfc96396">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.ExchangeCurrency();
autoObject.toCurrency = "";
autoObject.fromCurrency = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.4edd1538-bcf8-40d1-857a-f62ee98e8560" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.926b730c-ab77-45f9-8aa0-e1e77aff5836" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.c4deea0b-b9cb-4c21-8549-5c90d5ffe0b2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateSubType" id="2056.3ae8322d-731c-47ab-8572-6b197d0a6196" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateType" id="2056.01ef5a6d-8f6c-4c51-80da-9c3c11fd1c1c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.a83431ed-cef2-497e-8d88-11520a9eda6d" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9c63ae9f-967b-4505-89f7-503241361ad8" parallelMultiple="false" name="Error" id="b7eda709-5222-47fe-897b-7cb9c3fa8e51">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="147" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0dc10609-e6d8-49fd-8b88-5541f62daab6</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="41ee1676-777f-4dea-81bd-480ebb8001fa" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c1d66ade-b8a4-403c-8ff1-cead0d02cd34" eventImplId="8226e341-7ca6-4de7-89e0-61ae1af4369f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1471b575-67db-46fd-8cf6-11ae2ac38d16" parallelMultiple="false" name="Error1" id="6509363a-f179-499a-8335-420ee4e03b0d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="147" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>52da5dde-2d9b-4312-8768-d8301149fab7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="beb4c792-c964-4b26-87a9-26408a2ae829" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="592a0386-053b-496d-8963-c84a985a43c8" eventImplId="7a146d36-4584-494f-8157-c43d5958da7b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b7eda709-5222-47fe-897b-7cb9c3fa8e51" targetRef="6ef9cfdf-8492-48e0-8fa2-98ebb37e6350" name="To End Event" id="0dc10609-e6d8-49fd-8b88-5541f62daab6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6509363a-f179-499a-8335-420ee4e03b0d" targetRef="6ef9cfdf-8492-48e0-8fa2-98ebb37e6350" name="To End Event" id="52da5dde-2d9b-4312-8768-d8301149fab7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="f3d5d531-6ec6-4073-8369-17bc29f2d408" name="isSuccessful" id="1b344bb2-d08b-490c-8f98-7c08a4885777">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="536" y="104" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>39b98aca-ae2d-41ed-9852-ad8be2acf804</ns16:incoming>
                        
                        
                        <ns16:outgoing>f3d5d531-6ec6-4073-8369-17bc29f2d408</ns16:outgoing>
                        
                        
                        <ns16:outgoing>125a13f9-741d-4e93-8ad3-aa99b64891d1</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="1b344bb2-d08b-490c-8f98-7c08a4885777" targetRef="63fef019-5b36-4c2e-90a7-e381f8020c84" name="To End" id="f3d5d531-6ec6-4073-8369-17bc29f2d408">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1b344bb2-d08b-490c-8f98-7c08a4885777" targetRef="6ef9cfdf-8492-48e0-8fa2-98ebb37e6350" name="To End Event" id="125a13f9-741d-4e93-8ad3-aa99b64891d1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="6ef9cfdf-8492-48e0-8fa2-98ebb37e6350">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="210" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>52da5dde-2d9b-4312-8768-d8301149fab7</ns16:incoming>
                        
                        
                        <ns16:incoming>0dc10609-e6d8-49fd-8b88-5541f62daab6</ns16:incoming>
                        
                        
                        <ns16:incoming>125a13f9-741d-4e93-8ad3-aa99b64891d1</ns16:incoming>
                        
                        
                        <ns16:outgoing>af679eba-e186-4e16-8dc3-2cc68bc1688f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6ef9cfdf-8492-48e0-8fa2-98ebb37e6350" targetRef="63fef019-5b36-4c2e-90a7-e381f8020c84" name="To End" id="af679eba-e186-4e16-8dc3-2cc68bc1688f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.125a13f9-741d-4e93-8ad3-aa99b64891d1</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e7c</endStateId>
            <toProcessItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</toProcessItemId>
            <guid>859d4060-06cf-4aff-9c82-9292c4c080a6</guid>
            <versionId>229ec050-84ff-4111-966c-8eeafc8b40b6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topRight" portType="2" />
            <fromProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</fromProcessItemId>
            <toProcessItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</toProcessItemId>
        </link>
        <link name="To isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.39b98aca-ae2d-41ed-9852-ad8be2acf804</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</toProcessItemId>
            <guid>5f59f457-a4a0-4fe2-9b86-57d3fca9c27d</guid>
            <versionId>39dd9c28-d5c6-4e4a-aa47-78c3415dbfce</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</fromProcessItemId>
            <toProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.af679eba-e186-4e16-8dc3-2cc68bc1688f</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
            <guid>d892edff-6b30-4ea5-a4c8-49c77fc9d41e</guid>
            <versionId>*************-40b9-b12e-d8fe39a204b9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.6ef9cfdf-8492-48e0-8fa2-98ebb37e6350</fromProcessItemId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.dbdef4fe-565e-460d-91ee-c95019098fbf</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ae195407-3b55-4f0f-9c03-f5aa439f8584</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
            <guid>0d29cdf4-5e4b-4e18-9ba3-65ac4ba0060a</guid>
            <versionId>d7ceaf3f-583d-496d-8307-1428b4740b72</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ae195407-3b55-4f0f-9c03-f5aa439f8584</fromProcessItemId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
        </link>
        <link name="To Get Exchange Rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9bce7988-90df-4206-a4ac-1e714655868f</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</toProcessItemId>
            <guid>5e784378-6119-4305-a3b2-cf31df4ab887</guid>
            <versionId>d854fcfd-8afb-4515-8c1a-7e5cb43ef723</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9c63ae9f-967b-4505-89f7-503241361ad8</fromProcessItemId>
            <toProcessItemId>2025.1471b575-67db-46fd-8cf6-11ae2ac38d16</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f3d5d531-6ec6-4073-8369-17bc29f2d408</processLinkId>
            <processId>1.55393dcd-3352-41df-9690-75cb207d48b8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
            <guid>b79ec779-97ff-4e26-aaf9-ad784c85b2f6</guid>
            <versionId>ed54f2c5-8e1a-4238-8754-10970ff97e8c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1b344bb2-d08b-490c-8f98-7c08a4885777</fromProcessItemId>
            <toProcessItemId>2025.63fef019-5b36-4c2e-90a7-e381f8020c84</toProcessItemId>
        </link>
    </process>
</teamworks>

