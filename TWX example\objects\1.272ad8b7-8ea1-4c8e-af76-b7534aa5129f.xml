<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f" name="Retrieve Customer Facilities">
        <lastModified>1691671543024</lastModified>
        <lastModifiedBy>Hossam</lastModifiedBy>
        <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>39a87ef2-3153-46ee-b141-734b30778696</guid>
        <versionId>01e1a627-a82b-427d-a98b-b906a323ebd0</versionId>
        <dependencySummary>&lt;dependencySummary id="8506b90c-f681-44ac-846c-c9584a4feff8" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>78993c6b-0594-48b7-bcad-1247d9495b9b</guid>
            <versionId>f4fc2dd1-5612-4dae-b136-e8fac12db1ad</versionId>
        </processParameter>
        <processParameter name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2c7aebf4-ff3e-454a-a999-c39c7ba5d91c</guid>
            <versionId>a4fa3ef3-4eb4-42af-b466-57928f73529a</versionId>
        </processParameter>
        <processParameter name="customerNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"03024932"&#xD;
//"02922318"&#xD;
"08810824"&#xD;
//"14135051"&#xD;
</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8e7ae77c-4562-4e23-9283-1f26db063cbf</guid>
            <versionId>b38af6e9-c05c-4d58-b9b1-e6217dce05d7</versionId>
        </processParameter>
        <processParameter name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>49ff31f3-d116-4dfb-8bfb-0cc98a68e619</guid>
            <versionId>b05fdaf6-4862-4dad-9676-5f994ec55575</versionId>
        </processParameter>
        <processParameter name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1b074b68-bcee-4c98-8d30-4c268a160c7d</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ca5d3a24-b5fd-43af-8c6f-d3d9861dd575</guid>
            <versionId>a7fe2055-1f74-4a21-a7ed-0739fe14881d</versionId>
        </processParameter>
        <processParameter name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8524f680-2f8c-44ac-8819-19d77f9e07a2</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>360f59bf-8899-4d86-a5cb-a349d1822fba</guid>
            <versionId>70c469c7-4275-42be-950d-49d342e9d89b</versionId>
        </processParameter>
        <processParameter name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>18cd7e2c-da74-4ff2-a211-7de205f503d5</guid>
            <versionId>fb793078-c3a6-47c0-9afb-35071912ba27</versionId>
        </processParameter>
        <processParameter name="partyType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>8</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.name = "Applicant";
autoObject.value = "APP";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>06027d36-6d99-4407-a7eb-092f9e3cd081</guid>
            <versionId>b2a36f64-c24b-4bf2-b28e-4da73809ccc9</versionId>
        </processParameter>
        <processParameter name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.38bc233e-db52-4fae-a389-f979f7efca57</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>9</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"ILSN"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>de4dde18-5927-4cb9-b414-ebd3674ee77c</guid>
            <versionId>b51f89d5-ac1c-4010-986e-af02159f20f0</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c27cf6f4-5524-4ac0-a2f7-66e195dd522f</guid>
            <versionId>52a5930c-1e79-4702-82c0-e2878aea21de</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.72812cef-1b03-4818-8d0e-f55b3e396b46</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>91c43a9c-0e28-41b7-8686-f049164b015c</guid>
            <versionId>80ec33f9-ddd8-49e1-a9e4-d3aad93600f0</versionId>
        </processParameter>
        <processParameter name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.399f4efd-9154-4595-a15c-435ca48599ce</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>12</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>674818f3-6533-4d9d-8e7c-8cb467875ba8</guid>
            <versionId>8a720e06-4a76-45b5-8658-3de3e916eed5</versionId>
        </processParameter>
        <processParameter name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>13</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>23a98f62-3972-41c3-9eee-423cf58f0cde</guid>
            <versionId>eff2e21f-65e4-48ed-846c-7af437d5cfb3</versionId>
        </processParameter>
        <processParameter name="customerFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.40d442e7-0366-4e9d-83ec-deb29b0fc118</processParameterId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <seq>14</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>86144ffb-a3cc-472c-9156-ead02b6526dd</guid>
            <versionId>d08d537c-030c-4932-8716-889da73447c3</versionId>
        </processParameter>
        <processVariable name="appID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c993d84a-d7ce-4ba8-a325-215cfc21caac</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>74b85143-feb3-4173-b978-85bde808b136</guid>
            <versionId>1dd47a28-4f86-4e60-9bde-bf800df4aca8</versionId>
        </processVariable>
        <processVariable name="appRef">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e8a63f6a-fd91-4f1e-85a3-fec6d1979008</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>729186ae-b349-4fc5-9373-f8240e57ad47</guid>
            <versionId>d69887c4-a7d6-4945-867f-293dd9b35d42</versionId>
        </processVariable>
        <processVariable name="serailNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2074a3b2-c0c2-4625-ade4-8d1e36e6592f</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d6154dec-5331-4227-b45b-56ff28d610db</guid>
            <versionId>08adc19d-cf5a-496f-bff5-29f057f5791a</versionId>
        </processVariable>
        <processVariable name="token">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.033c32c2-ff33-4f0b-8d76-5a56d1002fa8</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e35f9529-cee6-4001-80ba-229169de9b62</guid>
            <versionId>124642f3-85b0-49cc-b002-7eb0dd2938ac</versionId>
        </processVariable>
        <processVariable name="requestTime">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.612dbb2c-1437-4300-af1c-3e8b06fcda5c</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>38c90702-03c0-42fa-a397-787efdc1ad76</guid>
            <versionId>31e68dca-6a4e-44cd-8c42-502928b98095</versionId>
        </processVariable>
        <processVariable name="responseTime">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d431bcba-3b5e-4dc8-8d9e-4304f14a1a72</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3c99a2f0-2234-4c9e-8b62-2a622f19d418</guid>
            <versionId>d9c1dd57-4443-4aad-85b3-3e36f780a1aa</versionId>
        </processVariable>
        <processVariable name="ServiceName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0fee056e-0a09-45ef-9e90-2c60be5aad44</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"Retrieve Customer Facilities"</defaultValue>
            <guid>17cfcd1e-c1d8-41f1-87bc-9282ee8937d9</guid>
            <versionId>5a7f5e91-a25f-4be2-bc3a-e588e3afdb33</versionId>
        </processVariable>
        <processVariable name="messageID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9d52c897-2f20-43ed-8870-2b351ca47cc8</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0f81ccbe-f483-4ac0-a15c-bead535fca22</guid>
            <versionId>5eab4fec-029a-4caa-a46c-81edadd27a4b</versionId>
        </processVariable>
        <processVariable name="returnCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7742d3c6-3c6e-49db-8985-c91b0d49cc9b</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>997c1633-3961-4b0c-bd26-d4968e82d773</guid>
            <versionId>dfe3e742-77e2-4e21-aab5-cc137ae1cd41</versionId>
        </processVariable>
        <processVariable name="xmlRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.497aa55f-7bd2-4797-b9f6-09a02dc2c124</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a7ae636-4d07-42ae-9649-4e7614438648</guid>
            <versionId>2dca702b-116d-4365-bf9c-0ef536cde651</versionId>
        </processVariable>
        <processVariable name="xmlResponse">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7fe5abd9-1966-461f-bd77-68f8f35e4415</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>96630583-b3c7-432a-bf55-d67b9b677c57</guid>
            <versionId>bc13c5c9-5294-4dbc-a236-723a42339023</versionId>
        </processVariable>
        <processVariable name="Facilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a7d1c8df-5b0f-4f80-a876-e7b17a0f80d6</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.1c20960f-48f5-49c7-83aa-b503d27100c8</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f2f51442-ce43-4983-a3fe-26a68da9fde7</guid>
            <versionId>d7200606-2326-4406-bf56-76ca574d9433</versionId>
        </processVariable>
        <processVariable name="facilityLines">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d527b2b-cf9d-43f8-9945-da97941efee9</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.1de54e16-43ff-4cda-9017-a615df8bf6f6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>42080503-632d-4887-92ad-9953a46fda79</guid>
            <versionId>cb19bec7-e368-489b-81de-238ddbeb8ff1</versionId>
        </processVariable>
        <processVariable name="creditFacilityInformation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c11cee64-a4d8-4711-8e85-9014ac2ecae7</processVariableId>
            <description isNull="true" />
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>31011c17-75b9-4a93-a7aa-90f669f14d78</guid>
            <versionId>ed7418a6-ea23-4a44-b664-c9ab13ecf9c9</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</processItemId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2895007c-fb8e-45b4-9c70-8e73aed88f16</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-599f</guid>
            <versionId>00251453-74d9-4778-8dbf-9928e9a4a116</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.a9a4ccba-c60d-4332-8a98-4a1334d1a7d4</processItemPrePostId>
                <processItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</processItemId>
                <location>1</location>
                <script>writeLog("End Service");&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}</script>
                <guid>0e674430-fed4-40c1-8af4-d66865161801</guid>
                <versionId>3ecc2825-c127-4cc1-afa6-dd9c13d6f9da</versionId>
            </processPrePosts>
            <layoutData x="867" y="160">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2895007c-fb8e-45b4-9c70-8e73aed88f16</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>08bb0ee7-90ab-4992-b9ea-5bb7a22bbc6d</guid>
                <versionId>d79db2a1-a661-408a-a479-18728da6dd38</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</processItemId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9f1024bb-4c8b-40d3-aea5-b14bb44885b0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-59a1</guid>
            <versionId>513cce8d-b712-40f6-a504-1a79dc870a43</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.045905c0-eaa8-4bbc-bae6-e938f25187dd</processItemPrePostId>
                <processItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>75e9135d-5895-44cf-82f7-96229a395c7d</guid>
                <versionId>b6066d5a-689d-4998-a40f-4137585a16bc</versionId>
            </processPrePosts>
            <layoutData x="409" y="137">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9f1024bb-4c8b-40d3-aea5-b14bb44885b0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.isSuccessful==true)&#xD;
{&#xD;
var mainFacilityIndex = 0;&#xD;
var subFacilityIndex = 0;&#xD;
var mainLinesIDs = [];&#xD;
var parentLinesIDs = [];&#xD;
tw.local.customerFacilities = new tw.object.listOf.creditFacilityInformation();&#xD;
tw.local.creditFacilityInformation= new tw.object.listOf.creditFacilityInformation();&#xD;
tw.local.facilityLines = new tw.object.listOf.FacilityLinesDetails();&#xD;
&#xD;
    if(tw.local.Facilities!=null &amp;&amp; tw.local.Facilities.listLength&gt;0){&#xD;
    &#xD;
    	for (var index=0;index&lt;tw.local.Facilities.listLength;index++){&#xD;
	    				&#xD;
					if(tw.local.Facilities[index].MainLineId == "" || tw.local.Facilities[index].MainLineId == null){&#xD;
	    		&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex]= new tw.object.creditFacilityInformation();&#xD;
			    			&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].facilityID = tw.local.Facilities[index].FacilityID;&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].facilityCode = tw.local.Facilities[index].LineCode;&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].overallLimit = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].limitAmount = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].effectiveLimitAmount = parseFloat(tw.local.Facilities[index].EffectiveLineAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].availableAmount = parseFloat(tw.local.Facilities[index].AvailableAmount);&#xD;
						if (tw.local.Facilities[index].ExpiryDate != null &amp;&amp; tw.local.Facilities[index].ExpiryDate != ""){&#xD;
							tw.local.creditFacilityInformation[mainFacilityIndex].expiryDate =  stringToDate(tw.local.Facilities[index].ExpiryDate, "-");&#xD;
						}&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].availableFlag = true;&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].authorizedFlag =  true;&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].Utilization =  parseFloat(tw.local.Facilities[index].Utilisation);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].facilityCurrency = new tw.object.NameValuePair();&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].facilityCurrency.value = tw.local.Facilities[index].Currency;&#xD;
						&#xD;
						mainLinesIDs[mainFacilityIndex++] =  tw.local.Facilities[index].FacilityID;&#xD;
			    		}&#xD;
			    		else{&#xD;
			    		&#xD;
			    		&#xD;
			    		if(tw.local.Facilities[index].productRestricted != null &amp;&amp; tw.local.Facilities[index].productRestricted.listLength&gt;0){&#xD;
	    		for (var i=0; i&lt;tw.local.Facilities[index].productRestricted.listLength; i++) {&#xD;
	    			if(tw.local.Facilities[index].productRestricted[i].PRODUCTCODE.trim() == tw.local.productCode){&#xD;
			    		&#xD;
			    			tw.local.facilityLines[subFacilityIndex]= new tw.object.FacilityLinesDetails();&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityID = tw.local.Facilities[index].FacilityID;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineCode = tw.local.Facilities[index].LineCode;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineAmount = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].availableAmount = parseFloat(tw.local.Facilities[index].AvailableAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].effectiveLineAmount = parseFloat(tw.local.Facilities[index].EffectiveLineAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].expiryDate = stringToDate(tw.local.Facilities[index].ExpiryDate,"-");&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityBranch =  new tw.object.NameValuePair();&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityBranch.value = tw.local.Facilities[index].FacilityBranch;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].availableFlag = true;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].authorizedFlag = true;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].internalRemarks = tw.local.Facilities[index].InternalRemarks;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].purpose = tw.local.Facilities[index].PurposeUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCCommissionPercentage = tw.local.Facilities[index].LCComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCDef = tw.local.Facilities[index].LCDefAccComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCCashCover = tw.local.Facilities[index].LCCashCover;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCCommission = tw.local.Facilities[index].IdcComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCAvalCommPercentage = tw.local.Facilities[index].IdcAvalComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCCashCoverPercentage = tw.local.Facilities[index].IdcCashCover;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].debitAccountNumber = tw.local.Facilities[index].DebitAccNum;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineCurrency = tw.local.Facilities[index].Currency;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineSerialNumber = tw.local.Facilities[index].LineSerial;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCommission = tw.local.Facilities[index].LGCommUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_BidBond = tw.local.Facilities[index].LGBidBondUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_Performance = tw.local.Facilities[index].LGPerformaceUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_AdvancedPayment = tw.local.Facilities[index].LGAdvacedPayment;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].CIF = tw.local.customerNo;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].partyType = tw.local.partyType;&#xD;
			    			&#xD;
			    			tw.local.facilityLines[subFacilityIndex].restrictedCustomers = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].customerRestricted != null &amp;&amp; tw.local.Facilities[index].customerRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].customerRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedCustomers[i] = new tw.object.restrictedItem();&#xD;
					&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedCustomers[i].code = tw.local.Facilities[index].customerRestricted[i].CUSTOMERNO;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedBranches = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].branchRestricted != null &amp;&amp; tw.local.Facilities[index].branchRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].branchRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedBranches[i] = new tw.object.restrictedItem();&#xD;
					&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedBranches[i].code = tw.local.Facilities[index].branchRestricted[i].BRANCHCODE;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedProducts = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].productRestricted != null &amp;&amp; tw.local.Facilities[index].productRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].productRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedProducts[i] = new tw.object.restrictedItem();&#xD;
								&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedProducts[i].code = tw.local.Facilities[index].productRestricted[i].PRODUCTCODE;&#xD;
							}&#xD;
						}	&#xD;
						&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedCurrencies = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].currencyRestricted != null &amp;&amp; tw.local.Facilities[index].currencyRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].currencyRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[index].restrictedCurrencies[i] = new tw.object.restrictedItem();&#xD;
								&#xD;
								tw.local.facilityLines[index].restrictedCurrencies[i].code = tw.local.Facilities[index].currencyRestricted[i].CCY;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						&#xD;
						&#xD;
						&#xD;
						parentLinesIDs[subFacilityIndex++] = tw.local.Facilities[index].MainLineId;&#xD;
							    				break;&#xD;
	    			}&#xD;
	    			&#xD;
	    		}&#xD;
	    	&#xD;
	    	}&#xD;
			    		}&#xD;
    	}&#xD;
&#xD;
    	////////////////////////////////////////////////////////////////////////////////////////////////////&#xD;
    	var mainLineIndex = 0;&#xD;
    	for (var i=0; i&lt;parentLinesIDs.length; i++) {&#xD;
    		mainLineIndex = mainLinesIDs.indexOf(parentLinesIDs[i]);&#xD;
    		if(mainLineIndex == -1){&#xD;
    			for (var x=0; x&lt;tw.local.customerFacilities.listLength; x++) {&#xD;
    				if(tw.local.customerFacilities[x].facilityID == parentLinesIDs[i]){&#xD;
    					var subLinesLen = tw.local.customerFacilities[x].facilityLines.listLength;&#xD;
    					tw.local.customerFacilities[x].facilityLines[subLinesLen]= tw.local.facilityLines[i];&#xD;
    					break;&#xD;
    				}&#xD;
    			}&#xD;
    		}&#xD;
    		else{&#xD;
    			var customerFacilitiesLen = tw.local.customerFacilities.listLength;&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen] = new tw.object.creditFacilityInformation();&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen] = tw.local.creditFacilityInformation[mainLineIndex];&#xD;
	    		mainLinesIDs.splice(mainLineIndex, 1);&#xD;
	    		tw.local.creditFacilityInformation.removeIndex(mainLineIndex);&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen].facilityLines = new tw.object.listOf.FacilityLinesDetails();&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen].facilityLines[0]= tw.local.facilityLines[i];&#xD;
	    	}&#xD;
    	}&#xD;
  &#xD;
    &#xD;
    }&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
function stringToDate (date,spliter) {&#xD;
    const str = date;&#xD;
&#xD;
    const [ day,month, year] = str.split(spliter);&#xD;
    return new Date(+year, +month - 1, +day);&#xD;
}</script>
                <isRule>false</isRule>
                <guid>0475a706-2a7e-4674-8108-ab06dcfbd927</guid>
                <versionId>e70fd08c-58ad-4605-9d0e-ea98b931f0b9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</processItemId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <name>Retrieve Customer Facility</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a85ac836-78d6-4243-8ae4-183e64d8dbc2</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-59a0</guid>
            <versionId>73061388-8a6b-4f71-8d42-5386d1e7fb00</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.839d1b18-89c1-4f08-bd32-d8f67b0b3e71</processItemPrePostId>
                <processItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</processItemId>
                <location>2</location>
                <script>writeLog("tw.local.xmlResponse",tw.local.xmlResponse);&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
&#xD;
}</script>
                <guid>91f10942-45b7-4c03-8b3c-1e63a7196929</guid>
                <versionId>11470cc7-5dec-4af4-b356-660c7c869d28</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.9f03851c-6fba-4d7b-a65e-d8b2f35068d1</processItemPrePostId>
                <processItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</processItemId>
                <location>1</location>
                <script>&#xD;
writeLog(" tw.local.customerNo "+tw.local.customerNo);&#xD;
writeLog(" tw.local.productCode "+tw.local.productCode);&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}&#xD;
</script>
                <guid>c46693ce-92e5-44ce-82dc-98ed92dbd2c9</guid>
                <versionId>632ec368-1aa2-456f-8aa7-6e2fbbc95237</versionId>
            </processPrePosts>
            <layoutData x="197" y="137">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>topLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:fef170a08f25d496:5466e087:189df5f8551:-599e</errorHandlerItem>
                <errorHandlerItemId>2025.a85ac836-78d6-4243-8ae4-183e64d8dbc2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4fcf3caf-443a-4206-8881-18d39870653c</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.227fc1e1-f5da-4cda-8351-306a95eb9aa7</attachedProcessRef>
                <guid>a855c71e-79df-46d6-b1b5-c98c3e45a216</guid>
                <versionId>aae93054-4dd2-4ccb-bc4e-e80784b8d8c7</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ddfc6f7e-626b-49ab-88ae-6e9d5d875a40</parameterMappingId>
                    <processParameterId>2055.6c68b590-b40f-4073-b08d-5ca9ce230347</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c37f8ed6-0db3-4007-b8ec-0d88fd775ab1</guid>
                    <versionId>1a8f1887-58a1-4b42-bf99-32d0db32dc1a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7e37e6b6-e1ba-4b1f-bfc1-7650746fcb5f</parameterMappingId>
                    <processParameterId>2055.153ad71c-109d-42cf-9547-007c43173e21</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>752fc13c-4805-4f49-96e6-a87a98b086ef</guid>
                    <versionId>328328de-f317-406f-839b-086a409ce43f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2eedc57b-66d6-4f61-b5e6-02363b770d54</parameterMappingId>
                    <processParameterId>2055.0d3815a3-03b1-4d4b-9e35-dd5f4579fe82</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestAppID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>32951e69-63ea-4396-bc7f-412a1131ff42</guid>
                    <versionId>39f5dfa8-1c78-4175-a877-4482e5ff52d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.98adc421-1569-49db-b2c9-88eb4a9bd5a2</parameterMappingId>
                    <processParameterId>2055.b51311d9-7817-4be5-b14b-70e0bd00afa3</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.processName</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>eea7bb5f-bd0c-45c7-ad78-d0b4460ed679</guid>
                    <versionId>40a1d561-1364-4905-9cb1-688434e4841a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dffe5b13-9e5b-4d7b-a9a9-fdd7fa8b6fba</parameterMappingId>
                    <processParameterId>2055.793b1189-327b-4e4b-80c5-077e024912cc</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9143fc62-fb77-4cd6-bd58-73ee1dc54a81</guid>
                    <versionId>84a4c772-5fa1-4a0c-9104-35b5b9ce6c0f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e7e917b4-5cf2-4b85-8cce-014728e80e60</parameterMappingId>
                    <processParameterId>2055.c6d042f1-4a04-40a0-891b-0870bd97b9dc</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.userID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>670ed2a0-0dcc-45a1-bca4-837b10928150</guid>
                    <versionId>8f1531a9-4322-44a1-afd3-af9e11ad304f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="facilityLinesList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.da8515cf-03b0-4981-92c1-d6558415ea40</parameterMappingId>
                    <processParameterId>2055.cef85a89-c2b9-4551-8dfb-ab433145eabc</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.Facilities</value>
                    <classRef>/12.1c20960f-48f5-49c7-83aa-b503d27100c8</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>8fccb67f-bb40-4874-9828-12ae9ac9efa0</guid>
                    <versionId>abc6a995-1c63-4091-9cf7-496e8c1dc172</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c6886ab6-d837-4ec9-9309-09348bcb2a58</parameterMappingId>
                    <processParameterId>2055.f763d984-addb-4571-955b-8e971e341929</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerNo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b2493501-dde0-4761-b3a9-af2ed5a42a08</guid>
                    <versionId>af445251-35cc-493c-931d-60a71f5c5e0b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.272cd6c2-2537-42a3-9b1d-e9b26f5d607e</parameterMappingId>
                    <processParameterId>2055.4dc04faf-1f1f-44da-b60b-0907bc5ed7b0</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>aba850ea-7669-403e-84dc-038f4317283e</guid>
                    <versionId>b96dde86-403b-44d1-9e34-1ee4d7c101d2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e524638b-5ede-4eeb-9192-8862b5f8c761</parameterMappingId>
                    <processParameterId>2055.a2321bda-7d31-4f43-b67e-d12a6fea5c8c</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>63da7e5d-b88d-4ba6-b2d7-5a4d94a3c9b9</guid>
                    <versionId>bc9728a2-23d9-4081-9b6c-dba8144f126f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f76455fa-f971-4418-b09c-4e1629e091ce</parameterMappingId>
                    <processParameterId>2055.026aed72-dc79-433b-82db-6bc4beb7e819</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>55562f20-c448-42b9-9daa-f483132d5df6</guid>
                    <versionId>c33a9f80-42e1-423a-b900-084aafdd1df8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.950da398-94b5-4a99-9c94-c3ac0ea0d96e</parameterMappingId>
                    <processParameterId>2055.8e049310-6ad5-4592-b504-************</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d61a6aed-5408-4b28-81ec-3c892edc9b68</guid>
                    <versionId>e887acdd-02a9-4200-9654-be9f3baaeeb9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.738f3923-ae08-46c0-af57-5c67794f0727</parameterMappingId>
                    <processParameterId>2055.6f2410fb-3f74-47cd-a2cd-9636ee2b5c65</processParameterId>
                    <parameterMappingParentId>3012.4fcf3caf-443a-4206-8881-18d39870653c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.snapshot</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ec15151c-e448-4251-b3dc-6158d098b76d</guid>
                    <versionId>eb42d65f-9e97-402c-a6d4-a15f67718a96</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a85ac836-78d6-4243-8ae4-183e64d8dbc2</processItemId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <name>catch Exception</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.89fc42ad-f4ef-4e33-9eff-3a6ce0affea5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-599e</guid>
            <versionId>a4c80971-211c-42be-bf6f-e7235e9add5d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="465" y="16">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.89fc42ad-f4ef-4e33-9eff-3a6ce0affea5</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.isSuccessful=false;&#xD;
tw.local.status="0";&#xD;
tw.local.errorMsg="Error Occurred please contact Administrator";&#xD;
&#xD;
writeLog("Error In Service,Message : "+tw.system.error);&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}</script>
                <isRule>false</isRule>
                <guid>449d913b-ecef-43a1-bba2-2c073fe8e802</guid>
                <versionId>21b2b205-2c93-4e5a-8ecc-222ab907c23a</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="160">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Retrieve Customer Facilities" id="1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="prefix" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3" />
                        
                        
                        <ns16:dataInput name="userID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33" />
                        
                        
                        <ns16:dataInput name="customerNo" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"03024932"&#xD;
//"02922318"&#xD;
"08810824"&#xD;
//"14135051"&#xD;
</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="instanceID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc" />
                        
                        
                        <ns16:dataInput name="processName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1b074b68-bcee-4c98-8d30-4c268a160c7d" />
                        
                        
                        <ns16:dataInput name="requestAppID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.8524f680-2f8c-44ac-8819-19d77f9e07a2" />
                        
                        
                        <ns16:dataInput name="snapshot" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3" />
                        
                        
                        <ns16:dataInput name="partyType" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" id="2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.name = "Applicant";
autoObject.value = "APP";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="productCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.38bc233e-db52-4fae-a389-f979f7efca57">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"ILSN"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab" />
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.72812cef-1b03-4818-8d0e-f55b3e396b46" />
                        
                        
                        <ns16:dataOutput name="status" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.399f4efd-9154-4595-a15c-435ca48599ce" />
                        
                        
                        <ns16:dataOutput name="errorCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1" />
                        
                        
                        <ns16:dataOutput name="customerFacilities" itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" id="2055.40d442e7-0366-4e9d-83ec-deb29b0fc118" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.1b074b68-bcee-4c98-8d30-4c268a160c7d</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8524f680-2f8c-44ac-8819-19d77f9e07a2</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.38bc233e-db52-4fae-a389-f979f7efca57</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.72812cef-1b03-4818-8d0e-f55b3e396b46</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.399f4efd-9154-4595-a15c-435ca48599ce</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.40d442e7-0366-4e9d-83ec-deb29b0fc118</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="a548b140-bd7f-4b24-ac19-728a85fe9300">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c0647dd1-aaf6-4469-9352-f26d193ef06a" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>489d6ebe-9521-4859-b92f-05b7e5de6c55</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6e14603d-6454-4a04-a0dc-43a7780cbf86</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2dddc380-6ad4-446c-af75-597f63048c36</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a85ac836-78d6-4243-8ae4-183e64d8dbc2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4dabe701-0c9b-4b85-9552-641d122f2e1c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="489d6ebe-9521-4859-b92f-05b7e5de6c55">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="160" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.b6ddf15e-226f-4c0b-b9b6-fb193300c951</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6e14603d-6454-4a04-a0dc-43a7780cbf86">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="867" y="160" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-599f</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript>writeLog("End Service");&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>27dc78d9-d118-4594-900c-a3b39e1224f5</ns16:incoming>
                        
                        
                        <ns16:incoming>546bab44-7cbe-4d58-830d-5f9e6b642ef8</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="489d6ebe-9521-4859-b92f-05b7e5de6c55" targetRef="8e61011d-45b4-464b-9dcb-6f9fe84b3bbf" name="To End" id="2027.b6ddf15e-226f-4c0b-b9b6-fb193300c951">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="appID" id="2056.c993d84a-d7ce-4ba8-a325-215cfc21caac" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="appRef" id="2056.e8a63f6a-fd91-4f1e-85a3-fec6d1979008" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="serailNo" id="2056.2074a3b2-c0c2-4625-ade4-8d1e36e6592f" />
                    
                    
                    <ns16:callActivity calledElement="1.227fc1e1-f5da-4cda-8351-306a95eb9aa7" name="Retrieve Customer Facility" id="8e61011d-45b4-464b-9dcb-6f9fe84b3bbf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="197" y="137" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>writeLog("tw.local.xmlResponse",tw.local.xmlResponse);&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.debug( message);&#xD;
&#xD;
}</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>&#xD;
writeLog(" tw.local.customerNo "+tw.local.customerNo);&#xD;
writeLog(" tw.local.productCode "+tw.local.productCode);&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.b6ddf15e-226f-4c0b-b9b6-fb193300c951</ns16:incoming>
                        
                        
                        <ns16:outgoing>76bcfb22-c609-429c-bcf2-646ee2384d35</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.153ad71c-109d-42cf-9547-007c43173e21</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c6d042f1-4a04-40a0-891b-0870bd97b9dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.userID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f763d984-addb-4571-955b-8e971e341929</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.customerNo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.026aed72-dc79-433b-82db-6bc4beb7e819</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b51311d9-7817-4be5-b14b-70e0bd00afa3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.processName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0d3815a3-03b1-4d4b-9e35-dd5f4579fe82</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestAppID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6f2410fb-3f74-47cd-a2cd-9636ee2b5c65</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.snapshot</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.793b1189-327b-4e4b-80c5-077e024912cc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.8e049310-6ad5-4592-b504-************</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.6c68b590-b40f-4073-b08d-5ca9ce230347</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4dc04faf-1f1f-44da-b60b-0907bc5ed7b0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a2321bda-7d31-4f43-b67e-d12a6fea5c8c</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.cef85a89-c2b9-4551-8dfb-ab433145eabc</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8">tw.local.Facilities</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8e61011d-45b4-464b-9dcb-6f9fe84b3bbf" targetRef="2dddc380-6ad4-446c-af75-597f63048c36" name="To Audit Integration" id="76bcfb22-c609-429c-bcf2-646ee2384d35">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d06bc5264f3bac58:1a63dcc9:18920913403:-1cea</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="token" id="2056.033c32c2-ff33-4f0b-8d76-5a56d1002fa8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="requestTime" id="2056.612dbb2c-1437-4300-af1c-3e8b06fcda5c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="responseTime" id="2056.d431bcba-3b5e-4dc8-8d9e-4304f14a1a72" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="ServiceName" id="2056.0fee056e-0a09-45ef-9e90-2c60be5aad44">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"Retrieve Customer Facilities"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="2dddc380-6ad4-446c-af75-597f63048c36">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="409" y="137" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>76bcfb22-c609-429c-bcf2-646ee2384d35</ns16:incoming>
                        
                        
                        <ns16:outgoing>27dc78d9-d118-4594-900c-a3b39e1224f5</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.isSuccessful==true)&#xD;
{&#xD;
var mainFacilityIndex = 0;&#xD;
var subFacilityIndex = 0;&#xD;
var mainLinesIDs = [];&#xD;
var parentLinesIDs = [];&#xD;
tw.local.customerFacilities = new tw.object.listOf.creditFacilityInformation();&#xD;
tw.local.creditFacilityInformation= new tw.object.listOf.creditFacilityInformation();&#xD;
tw.local.facilityLines = new tw.object.listOf.FacilityLinesDetails();&#xD;
&#xD;
    if(tw.local.Facilities!=null &amp;&amp; tw.local.Facilities.listLength&gt;0){&#xD;
    &#xD;
    	for (var index=0;index&lt;tw.local.Facilities.listLength;index++){&#xD;
	    				&#xD;
					if(tw.local.Facilities[index].MainLineId == "" || tw.local.Facilities[index].MainLineId == null){&#xD;
	    		&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex]= new tw.object.creditFacilityInformation();&#xD;
			    			&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].facilityID = tw.local.Facilities[index].FacilityID;&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].facilityCode = tw.local.Facilities[index].LineCode;&#xD;
			    			tw.local.creditFacilityInformation[mainFacilityIndex].overallLimit = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].limitAmount = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].effectiveLimitAmount = parseFloat(tw.local.Facilities[index].EffectiveLineAmount);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].availableAmount = parseFloat(tw.local.Facilities[index].AvailableAmount);&#xD;
						if (tw.local.Facilities[index].ExpiryDate != null &amp;&amp; tw.local.Facilities[index].ExpiryDate != ""){&#xD;
							tw.local.creditFacilityInformation[mainFacilityIndex].expiryDate =  stringToDate(tw.local.Facilities[index].ExpiryDate, "-");&#xD;
						}&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].availableFlag = true;&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].authorizedFlag =  true;&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].Utilization =  parseFloat(tw.local.Facilities[index].Utilisation);&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].facilityCurrency = new tw.object.NameValuePair();&#xD;
						tw.local.creditFacilityInformation[mainFacilityIndex].facilityCurrency.value = tw.local.Facilities[index].Currency;&#xD;
						&#xD;
						mainLinesIDs[mainFacilityIndex++] =  tw.local.Facilities[index].FacilityID;&#xD;
			    		}&#xD;
			    		else{&#xD;
			    		&#xD;
			    		&#xD;
			    		if(tw.local.Facilities[index].productRestricted != null &amp;&amp; tw.local.Facilities[index].productRestricted.listLength&gt;0){&#xD;
	    		for (var i=0; i&lt;tw.local.Facilities[index].productRestricted.listLength; i++) {&#xD;
	    			if(tw.local.Facilities[index].productRestricted[i].PRODUCTCODE.trim() == tw.local.productCode){&#xD;
			    		&#xD;
			    			tw.local.facilityLines[subFacilityIndex]= new tw.object.FacilityLinesDetails();&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityID = tw.local.Facilities[index].FacilityID;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineCode = tw.local.Facilities[index].LineCode;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineAmount = parseFloat(tw.local.Facilities[index].LimitAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].availableAmount = parseFloat(tw.local.Facilities[index].AvailableAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].effectiveLineAmount = parseFloat(tw.local.Facilities[index].EffectiveLineAmount);&#xD;
			    			tw.local.facilityLines[subFacilityIndex].expiryDate = stringToDate(tw.local.Facilities[index].ExpiryDate,"-");&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityBranch =  new tw.object.NameValuePair();&#xD;
			    			tw.local.facilityLines[subFacilityIndex].facilityBranch.value = tw.local.Facilities[index].FacilityBranch;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].availableFlag = true;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].authorizedFlag = true;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].internalRemarks = tw.local.Facilities[index].InternalRemarks;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].purpose = tw.local.Facilities[index].PurposeUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCCommissionPercentage = tw.local.Facilities[index].LCComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCDef = tw.local.Facilities[index].LCDefAccComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LCCashCover = tw.local.Facilities[index].LCCashCover;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCCommission = tw.local.Facilities[index].IdcComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCAvalCommPercentage = tw.local.Facilities[index].IdcAvalComm;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].IDCCashCoverPercentage = tw.local.Facilities[index].IdcCashCover;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].debitAccountNumber = tw.local.Facilities[index].DebitAccNum;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineCurrency = tw.local.Facilities[index].Currency;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].lineSerialNumber = tw.local.Facilities[index].LineSerial;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCommission = tw.local.Facilities[index].LGCommUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_BidBond = tw.local.Facilities[index].LGBidBondUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_Performance = tw.local.Facilities[index].LGPerformaceUDF;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].LGCashCover_AdvancedPayment = tw.local.Facilities[index].LGAdvacedPayment;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].CIF = tw.local.customerNo;&#xD;
			    			tw.local.facilityLines[subFacilityIndex].partyType = tw.local.partyType;&#xD;
			    			&#xD;
			    			tw.local.facilityLines[subFacilityIndex].restrictedCustomers = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].customerRestricted != null &amp;&amp; tw.local.Facilities[index].customerRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].customerRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedCustomers[i] = new tw.object.restrictedItem();&#xD;
					&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedCustomers[i].code = tw.local.Facilities[index].customerRestricted[i].CUSTOMERNO;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedBranches = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].branchRestricted != null &amp;&amp; tw.local.Facilities[index].branchRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].branchRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedBranches[i] = new tw.object.restrictedItem();&#xD;
					&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedBranches[i].code = tw.local.Facilities[index].branchRestricted[i].BRANCHCODE;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedProducts = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].productRestricted != null &amp;&amp; tw.local.Facilities[index].productRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].productRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedProducts[i] = new tw.object.restrictedItem();&#xD;
								&#xD;
								tw.local.facilityLines[subFacilityIndex].restrictedProducts[i].code = tw.local.Facilities[index].productRestricted[i].PRODUCTCODE;&#xD;
							}&#xD;
						}	&#xD;
						&#xD;
						&#xD;
						&#xD;
						tw.local.facilityLines[subFacilityIndex].restrictedCurrencies = new tw.object.listOf.restrictedItem();&#xD;
						if(tw.local.Facilities[index].currencyRestricted != null &amp;&amp; tw.local.Facilities[index].currencyRestricted.listLength &gt; 0)&#xD;
						{&#xD;
							for (var i=0; i&lt;tw.local.Facilities[index].currencyRestricted.listLength; i++) &#xD;
							{&#xD;
								tw.local.facilityLines[index].restrictedCurrencies[i] = new tw.object.restrictedItem();&#xD;
								&#xD;
								tw.local.facilityLines[index].restrictedCurrencies[i].code = tw.local.Facilities[index].currencyRestricted[i].CCY;&#xD;
							}&#xD;
						}&#xD;
						&#xD;
						&#xD;
						&#xD;
						&#xD;
						&#xD;
						parentLinesIDs[subFacilityIndex++] = tw.local.Facilities[index].MainLineId;&#xD;
							    				break;&#xD;
	    			}&#xD;
	    			&#xD;
	    		}&#xD;
	    	&#xD;
	    	}&#xD;
			    		}&#xD;
    	}&#xD;
&#xD;
    	////////////////////////////////////////////////////////////////////////////////////////////////////&#xD;
    	var mainLineIndex = 0;&#xD;
    	for (var i=0; i&lt;parentLinesIDs.length; i++) {&#xD;
    		mainLineIndex = mainLinesIDs.indexOf(parentLinesIDs[i]);&#xD;
    		if(mainLineIndex == -1){&#xD;
    			for (var x=0; x&lt;tw.local.customerFacilities.listLength; x++) {&#xD;
    				if(tw.local.customerFacilities[x].facilityID == parentLinesIDs[i]){&#xD;
    					var subLinesLen = tw.local.customerFacilities[x].facilityLines.listLength;&#xD;
    					tw.local.customerFacilities[x].facilityLines[subLinesLen]= tw.local.facilityLines[i];&#xD;
    					break;&#xD;
    				}&#xD;
    			}&#xD;
    		}&#xD;
    		else{&#xD;
    			var customerFacilitiesLen = tw.local.customerFacilities.listLength;&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen] = new tw.object.creditFacilityInformation();&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen] = tw.local.creditFacilityInformation[mainLineIndex];&#xD;
	    		mainLinesIDs.splice(mainLineIndex, 1);&#xD;
	    		tw.local.creditFacilityInformation.removeIndex(mainLineIndex);&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen].facilityLines = new tw.object.listOf.FacilityLinesDetails();&#xD;
	    		tw.local.customerFacilities[customerFacilitiesLen].facilityLines[0]= tw.local.facilityLines[i];&#xD;
	    	}&#xD;
    	}&#xD;
  &#xD;
    &#xD;
    }&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
function stringToDate (date,spliter) {&#xD;
    const str = date;&#xD;
&#xD;
    const [ day,month, year] = str.split(spliter);&#xD;
    return new Date(+year, +month - 1, +day);&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2dddc380-6ad4-446c-af75-597f63048c36" targetRef="6e14603d-6454-4a04-a0dc-43a7780cbf86" name="To Audit Integration" id="27dc78d9-d118-4594-900c-a3b39e1224f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="messageID" id="2056.9d52c897-2f20-43ed-8870-2b351ca47cc8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="returnCode" id="2056.7742d3c6-3c6e-49db-8985-c91b0d49cc9b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="xmlRequest" id="2056.497aa55f-7bd2-4797-b9f6-09a02dc2c124" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="xmlResponse" id="2056.7fe5abd9-1966-461f-bd77-68f8f35e4415" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="catch Exception" id="a85ac836-78d6-4243-8ae4-183e64d8dbc2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="465" y="16" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f3589202-c0b4-41b9-a389-6500f64bd864</ns16:incoming>
                        
                        
                        <ns16:outgoing>546bab44-7cbe-4d58-830d-5f9e6b642ef8</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.isSuccessful=false;&#xD;
tw.local.status="0";&#xD;
tw.local.errorMsg="Error Occurred please contact Administrator";&#xD;
&#xD;
writeLog("Error In Service,Message : "+tw.system.error);&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="a85ac836-78d6-4243-8ae4-183e64d8dbc2" targetRef="6e14603d-6454-4a04-a0dc-43a7780cbf86" name="To Audit Integration" id="546bab44-7cbe-4d58-830d-5f9e6b642ef8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.1c20960f-48f5-49c7-83aa-b503d27100c8" isCollection="true" name="Facilities" id="2056.a7d1c8df-5b0f-4f80-a876-e7b17a0f80d6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.1de54e16-43ff-4cda-9017-a615df8bf6f6" isCollection="true" name="facilityLines" id="2056.0d527b2b-cf9d-43f8-9945-da97941efee9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" name="creditFacilityInformation" id="2056.c11cee64-a4d8-4711-8e85-9014ac2ecae7" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8e61011d-45b4-464b-9dcb-6f9fe84b3bbf" parallelMultiple="false" name="Error" id="4dabe701-0c9b-4b85-9552-641d122f2e1c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="206" y="125" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f3589202-c0b4-41b9-a389-6500f64bd864</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="32f66ac8-0978-4bfa-a5bd-03093d393d73" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="b70e2301-e8b9-4036-90f6-4f94b58a951b" eventImplId="1c19cdcc-fe84-4208-881a-47de64d07d47">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="4dabe701-0c9b-4b85-9552-641d122f2e1c" targetRef="a85ac836-78d6-4243-8ae4-183e64d8dbc2" name="To catch Exception" id="f3589202-c0b4-41b9-a389-6500f64bd864">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Audit Integration">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3b1fc359-8409-434d-9adb-842b5069fb6f</processLinkId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</toProcessItemId>
            <guid>367d5b4f-5ee3-4399-9c33-37327538398e</guid>
            <versionId>3b1c6cbf-bc8d-448c-adcc-e353edbd1769</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</fromProcessItemId>
            <toProcessItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</toProcessItemId>
        </link>
        <link name="To Audit Integration">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6dea3834-951c-4a68-b6be-e77f881e55ab</processLinkId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a85ac836-78d6-4243-8ae4-183e64d8dbc2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</toProcessItemId>
            <guid>af2ac046-4893-4298-a657-2a48167e8d35</guid>
            <versionId>452fc80f-8a40-4bb0-b77f-a6d49afae652</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.a85ac836-78d6-4243-8ae4-183e64d8dbc2</fromProcessItemId>
            <toProcessItemId>2025.6e14603d-6454-4a04-a0dc-43a7780cbf86</toProcessItemId>
        </link>
        <link name="To Audit Integration">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ff91eb44-cec6-4290-afd9-2ec002e944de</processLinkId>
            <processId>1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</fromProcessItemId>
            <endStateId>guid:d06bc5264f3bac58:1a63dcc9:18920913403:-1cea</endStateId>
            <toProcessItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</toProcessItemId>
            <guid>04df4a9e-000d-4318-b183-7d1b2b0185e4</guid>
            <versionId>bacebc24-c3b4-4211-9e35-c684fed3858b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8e61011d-45b4-464b-9dcb-6f9fe84b3bbf</fromProcessItemId>
            <toProcessItemId>2025.2dddc380-6ad4-446c-af75-597f63048c36</toProcessItemId>
        </link>
    </process>
</teamworks>

