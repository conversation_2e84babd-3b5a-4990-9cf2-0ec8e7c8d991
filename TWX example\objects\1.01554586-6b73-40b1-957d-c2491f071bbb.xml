<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.01554586-6b73-40b1-957d-c2491f071bbb" name="getRequestType">
        <lastModified>1691935261705</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f81a850c-8894-4c7e-aaf9-79dd61644be0</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>61f0deec-45c6-4d8e-9ec6-3800faed84b3</guid>
        <versionId>efbec366-e797-45d2-9a92-b625a913f937</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:6ad7eb4224455a46:11f23e39:189eef37169:-4c05" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.a80a7275-40a4-450b-9804-2acbf5a47d7f"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":255,"y":30,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"1266c195-6c6c-457d-a9b2-c7c2544f43e3"},{"incoming":["496347d8-d042-43fb-809c-0fae644a8b78"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":838,"y":28,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6239"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ebcdeb85-c43d-4f1f-b744-0a863dc81bef"},{"targetRef":"f81a850c-8894-4c7e-aaf9-79dd61644be0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.a80a7275-40a4-450b-9804-2acbf5a47d7f","sourceRef":"1266c195-6c6c-457d-a9b2-c7c2544f43e3"},{"startQuantity":1,"outgoing":["1141ba6c-74b7-42d2-a480-00457f53cc6b"],"incoming":["2027.a80a7275-40a4-450b-9804-2acbf5a47d7f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":387,"y":12,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f81a850c-8894-4c7e-aaf9-79dd61644be0","scriptFormat":"text\/plain","script":{"content":["tw.local.sql\nselect * from BPM.IDC_Request_Type where Request_Nature_ID = &lt;#= tw.local.data #&gt;"]}},{"startQuantity":1,"outgoing":["097e5a31-1114-4dc8-8d55-c13968cdedc5"],"incoming":["1141ba6c-74b7-42d2-a480-00457f53cc6b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":526,"y":12,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"DBLookup\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a0cce9a0-09d2-4a3f-b7ea-09a7593a898b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d","declaredType":"TFormalExpression","content":["tw.local.lookup"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"startQuantity":1,"outgoing":["496347d8-d042-43fb-809c-0fae644a8b78"],"incoming":["097e5a31-1114-4dc8-8d55-c13968cdedc5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":677,"y":12,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Results","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"634bcd94-0840-4683-9c62-4cd4099bf60a","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = tw.local.lookup;"]}},{"targetRef":"a0cce9a0-09d2-4a3f-b7ea-09a7593a898b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"1141ba6c-74b7-42d2-a480-00457f53cc6b","sourceRef":"f81a850c-8894-4c7e-aaf9-79dd61644be0"},{"targetRef":"634bcd94-0840-4683-9c62-4cd4099bf60a","extensionElements":{"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Results","declaredType":"sequenceFlow","id":"097e5a31-1114-4dc8-8d55-c13968cdedc5","sourceRef":"a0cce9a0-09d2-4a3f-b7ea-09a7593a898b"},{"targetRef":"ebcdeb85-c43d-4f1f-b744-0a863dc81bef","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"496347d8-d042-43fb-809c-0fae644a8b78","sourceRef":"634bcd94-0840-4683-9c62-4cd4099bf60a"},{"itemSubjectRef":"itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d","name":"lookup","isCollection":true,"declaredType":"dataObject","id":"2056.2f4decd5-5cd3-43cf-8a29-915e262c0c0c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.c8e5c223-44f5-4176-98e2-e3e2bda34b8b"},{"parallelMultiple":false,"outgoing":["7fff2183-ba82-4393-8e88-ab37c86c2458"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8bf865a7-ab60-4803-8dea-c2ba37bc61c2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"7939b2cc-601b-4f47-8234-4c964a2076f0","otherAttributes":{"eventImplId":"4757f6be-f40a-4df2-8065-05465c810771"}}],"attachedToRef":"f81a850c-8894-4c7e-aaf9-79dd61644be0","extensionElements":{"nodeVisualInfo":[{"width":24,"x":422,"y":70,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"76db0a74-dc97-47ed-896b-fda6211ef709","outputSet":{}},{"parallelMultiple":false,"outgoing":["83d25e2a-b52f-43fd-8a2f-9fb938bf8acf"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"97257cff-aead-42f5-8ec1-b2ce00b9bfe1"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"51a5ee98-2026-4b63-8678-7a0ff88e8d5d","otherAttributes":{"eventImplId":"aa0d5449-62b3-4660-8b5a-5367ee469bef"}}],"attachedToRef":"a0cce9a0-09d2-4a3f-b7ea-09a7593a898b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":561,"y":70,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"1806f91a-706b-4b0c-8ba4-05874344c331","outputSet":{}},{"parallelMultiple":false,"outgoing":["7951e157-f6f0-4f6e-86bf-4272f0cd9f61"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"9f0fb0a5-7226-421d-8292-20df92767cc5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ffb781f6-8006-4b78-8303-cf39633f2a00","otherAttributes":{"eventImplId":"9437ea28-c167-446c-8997-2599e1cded72"}}],"attachedToRef":"634bcd94-0840-4683-9c62-4cd4099bf60a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":712,"y":70,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"c1b9f773-824d-4544-8e1d-2230e398246a","outputSet":{}},{"incoming":["7fff2183-ba82-4393-8e88-ab37c86c2458","83d25e2a-b52f-43fd-8a2f-9fb938bf8acf","7951e157-f6f0-4f6e-86bf-4272f0cd9f61"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"edeee933-86c2-4780-86e4-26bb108b5541","otherAttributes":{"eventImplId":"d013e9ec-2d71-41db-812b-4bd8f801e788"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":649,"y":138,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[getRequestType -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[getRequestType -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"fe82742f-a22f-4749-81ef-44ba8f781059"},{"targetRef":"fe82742f-a22f-4749-81ef-44ba8f781059","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"7fff2183-ba82-4393-8e88-ab37c86c2458","sourceRef":"76db0a74-dc97-47ed-896b-fda6211ef709"},{"targetRef":"fe82742f-a22f-4749-81ef-44ba8f781059","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"83d25e2a-b52f-43fd-8a2f-9fb938bf8acf","sourceRef":"1806f91a-706b-4b0c-8ba4-05874344c331"},{"targetRef":"fe82742f-a22f-4749-81ef-44ba8f781059","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"7951e157-f6f0-4f6e-86bf-4272f0cd9f61","sourceRef":"c1b9f773-824d-4544-8e1d-2230e398246a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.526e1723-5830-42ab-890e-96260d9fbe99"}],"laneSet":[{"id":"f4a4a095-4f10-40d6-b4ed-6f262cd622c4","lane":[{"flowNodeRef":["1266c195-6c6c-457d-a9b2-c7c2544f43e3","ebcdeb85-c43d-4f1f-b744-0a863dc81bef","f81a850c-8894-4c7e-aaf9-79dd61644be0","a0cce9a0-09d2-4a3f-b7ea-09a7593a898b","634bcd94-0840-4683-9c62-4cd4099bf60a","76db0a74-dc97-47ed-896b-fda6211ef709","1806f91a-706b-4b0c-8ba4-05874344c331","c1b9f773-824d-4544-8e1d-2230e398246a","fe82742f-a22f-4749-81ef-44ba8f781059"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"403f417a-3adb-4d6d-ad77-51d820c4b98d","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"getRequestType","declaredType":"process","id":"1.01554586-6b73-40b1-957d-c2491f071bbb","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.c16b5c04-cb4c-4228-a3e1-953599a2be74"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.5d6a09fc-d752-4a9c-8c85-4c6d4f6ef049"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.c16b5c04-cb4c-4228-a3e1-953599a2be74","2055.5d6a09fc-d752-4a9c-8c85-4c6d4f6ef049"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"1"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.b1e0f69b-f559-4399-98b0-6e6130a24138"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b1e0f69b-f559-4399-98b0-6e6130a24138</processParameterId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>1</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>965d9593-2d90-4218-98ac-fb6fbf8316ae</guid>
            <versionId>0bb2d654-b65d-46bf-82d1-9b9a856e9486</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c16b5c04-cb4c-4228-a3e1-953599a2be74</processParameterId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>94847a22-ea15-4483-a917-cd45e6def691</guid>
            <versionId>4d0d40e4-95f1-481d-8c0b-71375904357e</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f860d475-07f3-435c-b199-92e9c6b00aef</processParameterId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6f58d0be-8c76-4229-938f-1a75375bb160</guid>
            <versionId>4224a396-deb4-4974-b939-12937e4129b4</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5d6a09fc-d752-4a9c-8c85-4c6d4f6ef049</processParameterId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a1f01b4b-6b08-490c-a269-26d44874a811</guid>
            <versionId>afdfdf75-4441-486e-b83b-7ed294a6a4ec</versionId>
        </processParameter>
        <processVariable name="lookup">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2f4decd5-5cd3-43cf-8a29-915e262c0c0c</processVariableId>
            <description isNull="true" />
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.d249aea6-d076-4da7-887d-0f1dbba9713d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4f473bc1-143d-442c-ae6f-cfba0ebcf6bb</guid>
            <versionId>b5958101-f895-4523-b523-4d5eb44efa3d</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c8e5c223-44f5-4176-98e2-e3e2bda34b8b</processVariableId>
            <description isNull="true" />
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6593ef9f-bfe7-453e-8173-6959bdabaca6</guid>
            <versionId>7494a867-46ed-457e-a6fa-373d73f480d0</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.526e1723-5830-42ab-890e-96260d9fbe99</processVariableId>
            <description isNull="true" />
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3f0f1da1-581d-40ad-b6f9-f3f1927dce39</guid>
            <versionId>da85e4d8-0d52-4c50-b159-a596b200a5a7</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.634bcd94-0840-4683-9c62-4cd4099bf60a</processItemId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <name>Set Results</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2b67002e-e5c4-4765-86ac-70b18822e148</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6237</guid>
            <versionId>0a6e268a-4078-4a29-9baf-5ce4a1db3aba</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="677" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-785a</errorHandlerItem>
                <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2b67002e-e5c4-4765-86ac-70b18822e148</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = tw.local.lookup;</script>
                <isRule>false</isRule>
                <guid>d5cdfb99-b8a6-48f7-95e9-eeb21e8e6d00</guid>
                <versionId>1ae98cc6-0c87-4ee1-82a1-9d725bd861d5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f81a850c-8894-4c7e-aaf9-79dd61644be0</processItemId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <name>Set Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5314da24-2a8e-4cc1-b44a-205ec79f5464</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6238</guid>
            <versionId>11e550a2-ffb3-4b74-bc98-1bc20b9e3324</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="387" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-785a</errorHandlerItem>
                <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5314da24-2a8e-4cc1-b44a-205ec79f5464</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql
select * from BPM.IDC_Request_Type where Request_Nature_ID = &lt;#= tw.local.data #&gt;</script>
                <isRule>false</isRule>
                <guid>551912a4-6261-48cd-ac99-bd7ac905ebfe</guid>
                <versionId>0b588d30-4187-4f92-a534-def4b5af31dc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</processItemId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.4eba22bc-3673-4419-a5c2-08a95d2b356a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-785a</guid>
            <versionId>684586a3-9a97-42cd-aff5-c554c96c5284</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.d66ca1d5-7604-4300-bc98-a419b251d7f8</processItemPrePostId>
                <processItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[getRequestType -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[getRequestType -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>33a81332-5bb8-4912-90c0-9e0424e72523</guid>
                <versionId>eb76ec26-01b1-459f-a716-c62351c95952</versionId>
            </processPrePosts>
            <layoutData x="649" y="138">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.4eba22bc-3673-4419-a5c2-08a95d2b356a</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>1edf6fb0-5527-4bef-8a99-313e044b6eb9</guid>
                <versionId>8f8237bd-762c-4d05-87f9-f5703743be5c</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4f163973-a6ea-4694-8cca-712bcf6d36e3</parameterMappingId>
                    <processParameterId>2055.f860d475-07f3-435c-b199-92e9c6b00aef</processParameterId>
                    <parameterMappingParentId>3007.4eba22bc-3673-4419-a5c2-08a95d2b356a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1cdbe261-ca2e-4d82-bb18-9bcb06123b8f</guid>
                    <versionId>7af3cc31-ac4e-419f-ac65-fc4a14822507</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ebcdeb85-c43d-4f1f-b744-0a863dc81bef</processItemId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.496ad6cf-fb54-4e04-b6ef-e33cfcf63dc0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6239</guid>
            <versionId>c8157f85-da7b-4d16-8c39-bd8f67165b17</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="838" y="28">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.496ad6cf-fb54-4e04-b6ef-e33cfcf63dc0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>825bfabe-01b9-4e85-b17a-09231a236bdf</guid>
                <versionId>d02a4ef9-1a00-4815-a1e7-08afa6520db7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</processItemId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6236</guid>
            <versionId>cbd10dae-4a41-4740-b781-5704c165fbc7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="526" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-785a</errorHandlerItem>
                <errorHandlerItemId>2025.fe82742f-a22f-4749-81ef-44ba8f781059</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>5c6c3ffb-e35a-4d70-b96b-b17ecf965f2c</guid>
                <versionId>b28cad11-115d-47d4-841d-38ffdfd54f0e</versionId>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cd3c4d73-6d16-496d-b296-7dcbb5875a56</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"DBLookup"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>92e57798-0df9-40de-a118-261de9145210</guid>
                    <versionId>0116f688-6104-499f-b043-62353be00596</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ded9d023-9733-49c6-8c43-2e8e2deef162</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>125bb6df-dbb1-49a3-8ef4-aed7801daef4</guid>
                    <versionId>3a905517-9cd0-4ca3-aad2-abc8aeb47ae9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b74af101-b1bd-4a8b-a92f-6a9fed14062f</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.lookup</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a191c9e9-e71a-445c-ab69-4ed1fee8cfa6</guid>
                    <versionId>ab71aaea-9aca-4282-8fb6-56c4107ffdfd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.570566ae-2ed9-4bea-a0c1-e496755b6152</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cbb360d6-5316-470b-b341-3f4d8c91bf7e</guid>
                    <versionId>ac138163-32a2-44a2-a5b8-93be7be6bb9f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c6581129-5ef2-4ed0-a088-2a28a47a009e</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bb4a3249-edbc-46f4-a52c-3df0cc116426</guid>
                    <versionId>d42c0d7d-1dc7-4135-996b-3694e7615add</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d22c3a2a-383f-4522-888c-b0c8f37508df</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.2ee11a6e-1bef-45bb-b34a-5fa25b5424f8</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>5f24c6f8-ce7f-4196-b0bd-5ec43a4a9e96</guid>
                    <versionId>e14530fa-5c40-49ea-bb81-a3aefe85bbc0</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.f81a850c-8894-4c7e-aaf9-79dd61644be0</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="255" y="30">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="getRequestType" id="1.01554586-6b73-40b1-957d-c2491f071bbb" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.b1e0f69b-f559-4399-98b0-6e6130a24138">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">1</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.c16b5c04-cb4c-4228-a3e1-953599a2be74" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5d6a09fc-d752-4a9c-8c85-4c6d4f6ef049" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.c16b5c04-cb4c-4228-a3e1-953599a2be74</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.5d6a09fc-d752-4a9c-8c85-4c6d4f6ef049</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="f4a4a095-4f10-40d6-b4ed-6f262cd622c4">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="403f417a-3adb-4d6d-ad77-51d820c4b98d" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1266c195-6c6c-457d-a9b2-c7c2544f43e3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ebcdeb85-c43d-4f1f-b744-0a863dc81bef</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f81a850c-8894-4c7e-aaf9-79dd61644be0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>634bcd94-0840-4683-9c62-4cd4099bf60a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>76db0a74-dc97-47ed-896b-fda6211ef709</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1806f91a-706b-4b0c-8ba4-05874344c331</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c1b9f773-824d-4544-8e1d-2230e398246a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fe82742f-a22f-4749-81ef-44ba8f781059</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1266c195-6c6c-457d-a9b2-c7c2544f43e3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="255" y="30" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.a80a7275-40a4-450b-9804-2acbf5a47d7f</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ebcdeb85-c43d-4f1f-b744-0a863dc81bef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="838" y="28" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6239</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>496347d8-d042-43fb-809c-0fae644a8b78</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1266c195-6c6c-457d-a9b2-c7c2544f43e3" targetRef="f81a850c-8894-4c7e-aaf9-79dd61644be0" name="To End" id="2027.a80a7275-40a4-450b-9804-2acbf5a47d7f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Query" id="f81a850c-8894-4c7e-aaf9-79dd61644be0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="387" y="12" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.a80a7275-40a4-450b-9804-2acbf5a47d7f</ns16:incoming>
                        
                        
                        <ns16:outgoing>1141ba6c-74b7-42d2-a480-00457f53cc6b</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql
select * from BPM.IDC_Request_Type where Request_Nature_ID = &lt;#= tw.local.data #&gt;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Statement" id="a0cce9a0-09d2-4a3f-b7ea-09a7593a898b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="526" y="12" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1141ba6c-74b7-42d2-a480-00457f53cc6b</ns16:incoming>
                        
                        
                        <ns16:outgoing>097e5a31-1114-4dc8-8d55-c13968cdedc5</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"DBLookup"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d">tw.local.lookup</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Results" id="634bcd94-0840-4683-9c62-4cd4099bf60a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="677" y="12" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>097e5a31-1114-4dc8-8d55-c13968cdedc5</ns16:incoming>
                        
                        
                        <ns16:outgoing>496347d8-d042-43fb-809c-0fae644a8b78</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = tw.local.lookup;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="f81a850c-8894-4c7e-aaf9-79dd61644be0" targetRef="a0cce9a0-09d2-4a3f-b7ea-09a7593a898b" name="To SQL Execute Statement" id="1141ba6c-74b7-42d2-a480-00457f53cc6b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a0cce9a0-09d2-4a3f-b7ea-09a7593a898b" targetRef="634bcd94-0840-4683-9c62-4cd4099bf60a" name="To Set Results" id="097e5a31-1114-4dc8-8d55-c13968cdedc5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="634bcd94-0840-4683-9c62-4cd4099bf60a" targetRef="ebcdeb85-c43d-4f1f-b744-0a863dc81bef" name="To End" id="496347d8-d042-43fb-809c-0fae644a8b78">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d" isCollection="true" name="lookup" id="2056.2f4decd5-5cd3-43cf-8a29-915e262c0c0c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.c8e5c223-44f5-4176-98e2-e3e2bda34b8b" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f81a850c-8894-4c7e-aaf9-79dd61644be0" parallelMultiple="false" name="Error" id="76db0a74-dc97-47ed-896b-fda6211ef709">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="422" y="70" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>7fff2183-ba82-4393-8e88-ab37c86c2458</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8bf865a7-ab60-4803-8dea-c2ba37bc61c2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="7939b2cc-601b-4f47-8234-4c964a2076f0" eventImplId="4757f6be-f40a-4df2-8065-05465c810771">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a0cce9a0-09d2-4a3f-b7ea-09a7593a898b" parallelMultiple="false" name="Error1" id="1806f91a-706b-4b0c-8ba4-05874344c331">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="561" y="70" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>83d25e2a-b52f-43fd-8a2f-9fb938bf8acf</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="97257cff-aead-42f5-8ec1-b2ce00b9bfe1" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="51a5ee98-2026-4b63-8678-7a0ff88e8d5d" eventImplId="aa0d5449-62b3-4660-8b5a-5367ee469bef">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="634bcd94-0840-4683-9c62-4cd4099bf60a" parallelMultiple="false" name="Error2" id="c1b9f773-824d-4544-8e1d-2230e398246a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="712" y="70" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>7951e157-f6f0-4f6e-86bf-4272f0cd9f61</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9f0fb0a5-7226-421d-8292-20df92767cc5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ffb781f6-8006-4b78-8303-cf39633f2a00" eventImplId="9437ea28-c167-446c-8997-2599e1cded72">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="fe82742f-a22f-4749-81ef-44ba8f781059">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="649" y="138" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[getRequestType -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[getRequestType -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7fff2183-ba82-4393-8e88-ab37c86c2458</ns16:incoming>
                        
                        
                        <ns16:incoming>83d25e2a-b52f-43fd-8a2f-9fb938bf8acf</ns16:incoming>
                        
                        
                        <ns16:incoming>7951e157-f6f0-4f6e-86bf-4272f0cd9f61</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="edeee933-86c2-4780-86e4-26bb108b5541" eventImplId="d013e9ec-2d71-41db-812b-4bd8f801e788">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="76db0a74-dc97-47ed-896b-fda6211ef709" targetRef="fe82742f-a22f-4749-81ef-44ba8f781059" name="To End Event" id="7fff2183-ba82-4393-8e88-ab37c86c2458">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1806f91a-706b-4b0c-8ba4-05874344c331" targetRef="fe82742f-a22f-4749-81ef-44ba8f781059" name="To End Event" id="83d25e2a-b52f-43fd-8a2f-9fb938bf8acf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c1b9f773-824d-4544-8e1d-2230e398246a" targetRef="fe82742f-a22f-4749-81ef-44ba8f781059" name="To End Event" id="7951e157-f6f0-4f6e-86bf-4272f0cd9f61">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.526e1723-5830-42ab-890e-96260d9fbe99" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1141ba6c-74b7-42d2-a480-00457f53cc6b</processLinkId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f81a850c-8894-4c7e-aaf9-79dd61644be0</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</toProcessItemId>
            <guid>ff438d77-d758-45e4-af7a-42f4fcb80b6d</guid>
            <versionId>7c456b01-e1bf-438a-826a-10c5d3f801a1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f81a850c-8894-4c7e-aaf9-79dd61644be0</fromProcessItemId>
            <toProcessItemId>2025.a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.496347d8-d042-43fb-809c-0fae644a8b78</processLinkId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.634bcd94-0840-4683-9c62-4cd4099bf60a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ebcdeb85-c43d-4f1f-b744-0a863dc81bef</toProcessItemId>
            <guid>69c2a4f5-8a07-4f4d-a4da-6420a3ff7120</guid>
            <versionId>9affbd05-53dd-4fbe-9959-9a30e6f0c63a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.634bcd94-0840-4683-9c62-4cd4099bf60a</fromProcessItemId>
            <toProcessItemId>2025.ebcdeb85-c43d-4f1f-b744-0a863dc81bef</toProcessItemId>
        </link>
        <link name="To Set Results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.097e5a31-1114-4dc8-8d55-c13968cdedc5</processLinkId>
            <processId>1.01554586-6b73-40b1-957d-c2491f071bbb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.634bcd94-0840-4683-9c62-4cd4099bf60a</toProcessItemId>
            <guid>b2981257-c686-41db-981f-2588c1ba50a6</guid>
            <versionId>e1e8d42d-afa1-4d28-bf48-969f5ad116e5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a0cce9a0-09d2-4a3f-b7ea-09a7593a898b</fromProcessItemId>
            <toProcessItemId>2025.634bcd94-0840-4683-9c62-4cd4099bf60a</toProcessItemId>
        </link>
    </process>
</teamworks>

