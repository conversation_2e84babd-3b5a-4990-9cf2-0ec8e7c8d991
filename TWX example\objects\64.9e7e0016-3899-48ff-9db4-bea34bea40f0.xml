<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.9e7e0016-3899-48ff-9db4-bea34bea40f0" name="Limits Tracking">
        <lastModified>1692281889907</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;0ea86520-2eba-4f8f-8de3-6dbb99b6d495&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fcdbd131-dd87-4371-8934-609031995b0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Limits Tracking&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75f6cc5a-ed46-4b52-8612-e75d66c1757b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e8e599c-6d90-452b-8728-cf48ccc15bec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;96bc54a9-7a87-4219-8c16-1560bcc45534&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;820301fd-d078-4934-88aa-13354d240aab&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;61e0abc6-6e41-4e13-8211-115b4f2a4906&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e840100a-cf29-46d0-80b8-4c4ff5a65474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dcb0fa53-5549-42f8-8e1f-bc2fd86f4226&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;969a6da1-05c4-4a96-8989-3955fbd5f58f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa6cec9e-fb09-447e-8df9-bcf9d4abc2a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;cb815236-2190-4391-8d20-e76e0344e0ff&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ff5ff740-80c2-4ec3-8acf-e08aa01ebe34&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;LimitsTrackingRequired&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e7c9029-059b-4657-81ec-b8d42c2c4f90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Limits Tracking Required&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b07dbed-354b-4444-8252-477808a99b1f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad68302f-7cdb-4bb2-8ccb-2dba840d67e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b283e7c-71a6-47c4-8a98-92c063ef6531&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showValidationMarker&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f7a49d45-71d6-47c2-8e9b-de511d61db84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.vis();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;245cdfe5-fca0-445b-879e-809eceffcf4b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.LimitsVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6e6d488a-bbc9-43fb-870b-6b05cd8d9c42&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.haveFicility();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.idcContract.isLimitsTrackingRequired&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3155eb0b-d841-48ac-8589-1e795a6c30af&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3434a79-2234-4f96-8628-b50e95e94b43&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c9ae75e-e857-44a2-8b75-ffff3c6a47de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f14b050-3a56-4852-816c-b14863e455d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c0cad8ad-3af3-4097-8126-b416df8c8628&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d60a4c7-20b9-4b64-845b-52b3180c6819&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;24b13011-73ed-409e-84ef-f6078197f34d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b47dafa-91e9-4017-8691-b338e9ddd47d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;D&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.limitMessage&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;650e6d1f-c694-4611-8174-8b652018e1ef&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetFacility&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb8095f5-88cb-4ac6-8cf7-8a3fc39eb82f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Facility&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;710669dc-46c6-4be8-8bbf-c6d8d341a19c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72c289f4-ba6e-4c37-8b99-4086c5a833e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d95c6f8c-f6e8-4cc8-8c25-92875ad38180&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05419667-9270-4d9b-8c83-60776538a4b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dbd3f6e3-dcf5-4cc6-8949-735cdc7eb553&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"NONE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3cb9badd-9875-45a0-879c-6cc6b39a55d8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Credit_Facility_Information1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c5115762-b1ed-478e-8a74-45fd93d0b150&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Credit Facility Information&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8738f93d-4621-4ca5-8f4e-b61c90686a32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;839cafb9-e818-45f7-825e-a33322e65c71&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b015be34-6f0c-4563-8f9e-66a18f7aeedf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;CashCoverPercentageAndAmountVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;DEFAULT&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;99485133-2901-4e7e-820b-dbe426828e9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;liabAndCashCoverVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;NONE&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;105df204-7c58-4c1a-8d9f-580394260d0b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;SectionVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.SectionVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2fb13eaa-a86e-4774-8ffa-fc70d50db585&lt;/ns2:id&gt;&lt;ns2:optionName&gt;facilityPercentageToBookVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.facilityPercentageToBookVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35d22c00-d0a3-4bb8-8f07-9014e48017b6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;creditFacilityInfoVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;READONLY&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe58a2e4-c581-41d3-8842-ea477b0cb34d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;facilityCodeVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;DEFAULT&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f2ad93d5-0cb2-4fe5-8654-06902fec672a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;facilitiesCodesList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.facilitiesCodesList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;74630b62-cf86-4c9c-87e2-c7b8832fb656&lt;/ns2:id&gt;&lt;ns2:optionName&gt;facility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.facility&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93497ba4-1eba-4ae8-86d8-977616f7fc7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;deleteFacility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e304d330-f55e-4c05-b792-da2278c3fa02&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.idcContract.facilities[]&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a70</guid>
        <versionId>c173f944-afe2-4038-be7e-d5a90cd32beb</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.6f97c745-f41e-470b-8b65-906219f91594</coachViewBindingTypeId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>be7c0bb0-b968-421a-8dca-416e2dff38b8</guid>
            <versionId>88a57bf7-ae99-4436-8e2f-f3174fccc043</versionId>
        </bindingType>
        <configOption name="SectionVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.48d5dc13-5298-4f44-ad55-ff56b5c503ae</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>c25484a1-0a9f-4c56-997b-91dfeda7c05e</guid>
            <versionId>a4a92536-2c47-433f-a922-28e5603931b6</versionId>
        </configOption>
        <configOption name="facilityPercentageToBookVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.236df1ab-22dc-4b8d-ae7a-1c2f30f6829d</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>64eb5107-71cf-4cd6-8110-77a8dbbeded6</guid>
            <versionId>a65c2686-bcb4-4e83-92cb-9155194dc8cd</versionId>
        </configOption>
        <configOption name="facilitiesCodesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.f69b8a61-6bb6-4543-a0e3-63d67928cc9b</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>7d3057eb-2054-400f-9838-9525ee121067</guid>
            <versionId>da20a96e-ab7d-48e4-a80a-a547eecf0fda</versionId>
        </configOption>
        <configOption name="facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7c26b4de-1233-4c6e-a3fb-c8c90fb0faa9</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.b7087c1b-7f18-4032-b88b-ffe584eafd08</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>cd34d202-4a70-43b5-bae2-49af26342b86</guid>
            <versionId>e1db51b4-1d16-4a77-ab21-f209cfb0498f</versionId>
        </configOption>
        <configOption name="LimitsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e1e968be-498f-4dc2-a59a-9d687e99c351</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>6f166f32-6277-4e65-8eef-c7f5af6c7d88</guid>
            <versionId>c93ca373-4014-4e8e-8ad8-8d48c50d9886</versionId>
        </configOption>
        <configOption name="limitMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.2f303035-6637-4924-881b-7576d4c5b9bd</coachViewConfigOptionId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>7f79e379-aa1d-4226-b126-c9eb9ebffb64</guid>
            <versionId>ed7bed7d-fd27-41db-a52c-35a98948a78f</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.f025f12d-c308-492a-bf16-09be74708691</coachViewInlineScriptId>
            <coachViewId>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.vis = function  () {&#xD;
	if (this.ui.get("LimitsTrackingRequired").getData() == true) {&#xD;
		this.ui.get("GetFacility").click();&#xD;
	}&#xD;
	else{&#xD;
		this.context.options.SectionVis.set("value", "NONE");&#xD;
		this.context.binding.get("value").set("facilities", []);&#xD;
		this.context.options.facility.get("value").set("facilityCode", {});&#xD;
&#xD;
	}&#xD;
}&#xD;
this.haveFicility = function name (parameter) {&#xD;
	if (this.ui.get("LimitsTrackingRequired").getData() == true) {&#xD;
		this.context.options.SectionVis.set("value", "DEFAULT");&#xD;
	}&#xD;
	else{&#xD;
		this.context.options.SectionVis.set("value", "NONE");&#xD;
&#xD;
	}&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>fb0dc3ec-fbed-4d53-8f0f-2884316da639</guid>
            <versionId>de9e8025-0476-441d-9e4f-70c458dad0a1</versionId>
        </inlineScript>
    </coachView>
</teamworks>

