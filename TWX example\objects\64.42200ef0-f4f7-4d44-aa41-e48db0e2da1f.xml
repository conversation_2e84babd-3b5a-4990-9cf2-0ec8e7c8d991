<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f" name="Party">
        <lastModified>1717330271156</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;9273866e-439d-45be-8af3-28969225d096&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_panel2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8a624705-c6db-4dbd-8a0b-8b4d4f5408c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Collapsible panel&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86fee247-cbbb-4404-848c-3793168874b3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc9f9abd-3d6d-4dce-8a2a-6337e58f8b65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;69a10798-e5c7-4316-8750-08363fd87c9a&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3fbac431-4890-42e3-8fc1-6291d6c19b43&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a780789d-00c7-44e3-86fc-94630f607332&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.party&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;770e91dd-c524-4f7a-8d4f-877e5ddc912b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dc6a1319-0626-470f-8c69-9b4cd23c5b50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fbcd04d-9fa5-4aa8-8d68-471aafc966da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dbe35c55-cdb4-40de-8566-e1e672110cb5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81d79244-faed-4734-8b9a-5961415f4ee0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//this.ui.get("address").setEnabled(true);
//this.ui.get("media").setEnabled(true);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;325b69de-5c95-4a70-85cf-28b68b8929c5&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8f093819-ab55-4274-8238-f2cce5ad9c19&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;73e705a8-d2ce-4089-8f6b-ea56c6d53cbd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cd4dc4a-**************-c0eff665e17a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;11f185e9-7621-4523-868a-88e2cbe7a156&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49b22179-8ef2-4139-868f-4263c178d972&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"H"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4938cafd-4133-4d92-8914-47f6cb6993d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;679fb1ce-cc86-4c0e-816a-31540c937582&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8802c138-7dab-4524-8d17-33511e03767d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;PartyTable&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c6557e8-9b95-415b-84b2-b67849fc3baa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;512b9878-2c23-4b2c-8548-2e4cc82a07b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1e7bfc5-b51d-4215-8720-6cc7559d592f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Parties Table&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d302aff-0b0d-4e07-8cf1-7cf5dc63a5f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c9adea63-4097-47a9-8ecb-d56fab9063ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f153700-10c8-4bbe-8d86-8aa48df9b9e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expandableRows&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34718a41-c834-4610-8caf-c281a60ec9a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowColumnResize&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;693a3a6f-ea4d-40ec-809d-15e74826b7a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff39b7e1-4394-4276-8a0f-ec2dfd175029&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e95a2f9-b93f-46e8-8649-5ca3679bf2d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db3660a3-c2ae-42fb-8ae4-5ae6608d0991&lt;/ns2:id&gt;&lt;ns2:optionName&gt;columnSpecs&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2861dbfb-0584-44da-8853-728b33a02abc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"400%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb6c0a70-d4c3-4786-8846-adcfbdacdf22&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ADDREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//alert(me.getData());&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd1baf9e-893f-4c48-8cf2-cd51015cf429&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.concatOnDelete(me.getRecordIndex(item));
if(item.partyType.name =="Drawer" || item.partyType.name =="Drawee"){
alert("You can not delete this row");
return false;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;7cc7fd18-3e3a-473d-8e0a-5179501d8120&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e0168522-1676-4bdb-8133-3a6eccfafa37&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partytype&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92c77f30-1a44-48b4-b85a-a88cde39c380&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f00315fb-9596-4352-8dc4-944abab5f7f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.partytype&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eafe285b-b6a7-41cd-8796-10577640b56e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8f16404-7953-416b-8498-532ae7a48b34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae34a733-0283-4daa-81d8-f5ba30198e86&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;19de7c7a-7643-4cc0-8c21-692795ac203f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.ListNames.PartyType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd8f69a8-0e16-4e4c-8d66-669d4ab32373&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0946557d-8346-4981-8ad5-6cc1b85ecba3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7eb25bb5-6c08-4a6b-8256-00a10ef89a41&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.partyTypeList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce31854d-b4d2-4003-8629-051997986baa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@validateParty(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c5e4b595-4a54-450a-8b4e-88a96e3335fe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.ui.getIndex() &amp;lt;2){
	me.setEnabled(false);
}

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6748914-94c2-4dd3-875e-508a3778acbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5179e91-c386-4ccf-8b13-96fbfe64f2ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Accountee","value":"Accountee"},{"name":"GL Account","value":"gl"},{"name":"new2","value":"new2"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d91e1e0-6997-4742-8f8e-4d97faeb7e99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_BLUR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyType&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;153e2e06-cd03-410f-807b-2af91921fbd8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;nbeCustomer&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;63ff4e55-cb86-4f62-8c4a-b8e8ec7227f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.nbeCustomer&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e54afbaa-4407-4e9a-807d-1b6268e5e479&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97ce49de-0dc7-4fe5-88f6-7685afd76ef5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;28519375-2a40-4ad6-89e8-50578a437f13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showValidationMarker&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":false}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92a47bbd-a249-45d8-8a12-be35e383d8d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bca7de24-3fba-46ee-847b-1faec1753555&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@disableRetrieveBtn(me);


&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a338ee0a-1085-4bc8-838a-6680d95c3674&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@disableRetrieveBtn(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.isNbeCustomer&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c90046c1-a32c-4a68-8bc5-2c43c26eee55&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partyCIF&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4c91c68-dff7-4ee7-ad69-971806360b45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83abce14-f255-4d67-8a0c-2937cea873ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Party CIF&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a49eb100-44d6-445d-8a7f-83f4c4c0a334&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56567d11-3dc9-4f5e-8e08-386732de766e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ec00c580-05d3-4544-8949-e14b86319e34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setPartyId(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9999b0e9-852c-44b2-81f5-629aad3290f3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 8){
	me.setValid(false , "must be 8 digits");
	me.setData("");
	return false;
}else{
	me.setValid(true);
	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba367efe-c593-4acf-825d-35db1c551c97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@validateParty(me);
//@setPartyId(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyCIF&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c390859a-d31d-4138-82d4-b46426ae09ae&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;RetrieveBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5a6fdc3-ec08-485e-897b-eba1955a9f40&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22d8c8dd-8ebe-4c72-8d85-60c301dc6485&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b9f2faf-6902-44e6-8682-0cc83247609b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;210954a5-5ddf-47e8-8d53-b803e3051987&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7c64ee4-d36e-4c56-8ada-361ca688901e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@clickEx(me);
//${GetPartyDetails1}.execute();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;99ad1cd8-17ce-417c-8fb3-5dc32dcfd2ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b25f612-155c-44e7-8ac2-f3296d150765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0efd7cb0-5cee-4dc4-8bd5-ea13632890c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//me.setData(false);
//if(me.ui.getIndex() == 1){
//	me.setEnabled(false);
//}else{
//	me.setEnabled(true);
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9eaafb57-e3a1-4703-87de-f0d5905e30c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.isRetrived&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;88d4ccfa-4fde-4543-82a5-c80b63a2b9dd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CIF&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a87191e-ecc6-4b16-b924-4861fdd7d7e6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36a8f52f-4931-4038-84b4-b2965e0b9c47&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Party ID&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77d5663a-262d-4c1f-86f2-804e357e9ccb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a9f5260-b84b-428f-8d6f-46ffd983ff19&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23711435-d7ab-4188-8246-690d3976e339&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(isNaN(Number(me.getData())) || me.getData().length &amp;lt; 8){
	me.setValid(false , "must be 8 digits");
//	me.setData("");
	return false;
}else
{
	me.setValid(true);
	return true;	
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4eeeea28-8607-43c1-8945-2603cc215106&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 8)
{
	me.setValid(false , "max lenght is 8 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyId&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d1c48724-c648-4a45-8de7-b1d46ca1caae&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partyname&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4af6b9bb-01b6-4da9-953b-60cbc58bbbf3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f45a1bc-6b9e-4c90-86b2-8e24dc05e102&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.partyname&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b74b48bb-7189-4635-8cdd-af1460290cba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56f9cab0-42ba-485c-8184-e12b306ac9e6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;835077c4-88b7-4b1b-8695-2a3bbabc7bfe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.name&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9c0e8436-0a67-4a2a-85ea-b3c78e45c9e3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;country&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ca6165c9-6862-4412-9ac6-2f02d2895e0c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0112f3f5-34ab-4777-863c-5687af43c1db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.country&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5fe3879-6ea1-4848-8a1c-bae2fe0e46d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49f667c2-92e7-4f5c-89e3-9945ba68b0a2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5bff1cbc-e735-43dd-88fc-19684913c14d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;60361e13-bba2-4fbf-8da9-c68cf55e23a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;974197d1-9604-4725-8c93-023b722395f1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.ListNames.Country&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40cdb65c-dd45-4ddf-894e-2d29ffd0fc57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;981c98dd-2620-4283-85d4-0fa9e2714520&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.country&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;16ae3cb7-413a-47dc-8c99-a3f684fcf4d2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;language&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;102b9238-3e1e-424e-85c0-ee8c8683a38b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb368526-879f-4170-83ab-1f9e9824ffd8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.language&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebae7d00-1100-4ec5-837c-ab93524188e0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebf10515-b989-4e00-8c05-8ddc9eb73d36&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.language&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;aa4fbb8e-1eff-409f-8faf-3136edfe37b9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;reference&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;873af441-0129-47c5-a3cd-cd4d7b28a7a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;878b088b-15eb-40ff-87fb-d4eb243241ac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.reference&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d48ef1d-e32d-4714-88f5-87ade3c67af0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cc70c73-e82d-4612-8b9d-109eee7fdb3b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.reference&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;460d0fba-**************-984bb38823a0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91aa628e-5e98-4678-95e3-40a18aa5ece7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7df0e730-8beb-468b-8303-486acdba7d0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address1&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d5efb27-903f-4181-80a6-7c7b37ec332e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ff5445c-58da-4807-8b83-2379237d7220&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address1&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92c4eb2b-4c1e-43d2-8704-5a93172ec198&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fcff24f4-5187-447c-aa34-8954b3b7ad5c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;48adad91-cbda-4328-831d-17009e5f68ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address2&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8455f632-335c-4c52-8698-12a3f1925048&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7555b621-d0f7-40b8-863d-07c795f8ba06&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address2&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7789c0c1-8a7c-4018-86f9-a0760911988b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee92456e-3142-41f8-95c3-1e05cdc026af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d9026e2d-8800-4ab0-8328-f1c43a835203&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address3&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3711837d-e1dc-49d8-84bf-7804c5ce75e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff0076e4-3b99-4918-8d0a-c999b894747a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.PartyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address3&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a2a5e47f-5f7d-434d-87f3-cdb4d40e7359&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;media&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e66e1fd-7d8e-4e30-aedd-fe0bfdeaf191&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a5c1573-a457-4c74-8bc7-f66b597b1e4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.media&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef8f92da-bb0d-401b-8e95-dd54d4d74922&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f11d80af-d487-4de1-819a-7728f03ff32a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.media&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7a69b036-e2a2-4711-85bd-cf1aef36de30&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43edfb72-3468-4931-8c43-4697be11f96c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f82835b-d033-4083-8ee7-8ce25d4b0a07&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b9a1eb0-3fdb-4bd2-8e50-e38c4f7ec672&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ea99a68-f819-4072-8b1c-32bb081a8ed6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aef02cd9-6d3a-411f-87d4-17681c97480c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b85e7dbe-31bb-4932-822c-f1d75a19e613&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setBIC(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;904f9e06-db7d-4804-8bba-f521375da86f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;373481b7-1eaf-4e3d-8c08-23cbcde91a3e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cf95d0f-f366-40d1-8815-71ef0e87138b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.Party.currentItem.partyCIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f212838d-9b0a-457a-8350-a0b4c6e264dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9799d472-9fb7-4123-8927-4ce26bfb7f1a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addressBICList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f9440858-1b77-4e4c-8070-81248f283fc8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetPartyDetails1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;547ced56-317e-43d4-8120-c453aef0edbc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Party Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df46e941-8d67-41f9-877b-17ea9816f764&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15e0c455-4b66-418d-8ac5-1bc8cb240a75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc69f1ac-bff8-43b4-88c6-e6f68d3de474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.c3124e3b-0cdd-4710-a44b-6071a04e76dc&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b991536-7c8b-482e-84ef-9187fda0440b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.mapPartyData();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5779ce9e-afde-4738-8148-567315e86af6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6442679f-5dc6-449c-87c7-ef1c6912b906&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.customerFullDetails&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0ddfe2fe-e6a5-4145-8e6e-260ad2bafdb4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetAddressBIC&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5f29ccc7-fb4a-42ed-8908-b95ab2986081&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Address BIC&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e99180a4-bd54-4dcc-8470-f5016fef2a0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e68a5eb-8ba3-4754-8e89-1ff6a6b81827&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58a11cfe-5597-4790-80ec-449b467b68a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d6b394c-c514-4c5c-8219-36944bd336b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.addressBICList[]&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>dc0bd8b0-f2e6-4d30-9b9e-dfea13401bf1</guid>
        <versionId>a3d608d5-1e66-480d-a9fe-673d01395e39</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="Party">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.d3fffbeb-01e1-4e6b-95db-8d8dd51db2f5</coachViewBindingTypeId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>true</isList>
            <classId>/12.af731d60-bee2-4d24-bfed-30192291dbd7</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>700546f1-2f3e-4e2c-b0d7-0c1a7bf8cca6</guid>
            <versionId>eb210f5e-b26c-4edd-9ed1-49aaa8e48fa0</versionId>
        </bindingType>
        <configOption name="partyTypeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.23a1f3f1-aa0c-4b85-8160-218542467bb9</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>769dc1f1-39e7-4916-bf94-585e060cb4bd</guid>
            <versionId>488fc681-a634-41a8-81b5-854a3bf60662</versionId>
        </configOption>
        <configOption name="concatString">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e6579506-a47c-458d-8d46-7e93686b3c21</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>ddf40e8b-235f-429e-9e98-e6d6805978bf</guid>
            <versionId>2527f255-7875-4532-b03e-951e7ce402cb</versionId>
        </configOption>
        <configOption name="BeneficiaryDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b6380736-a8f4-4126-be99-74456d9513e5</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.a025468c-38bc-4809-b337-57da9e95dacb</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>e06a93da-ab73-45a9-bef6-d5d37c491bfa</guid>
            <versionId>960b633e-ae78-415f-b148-f2bc5edc8c50</versionId>
        </configOption>
        <configOption name="partyIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b2b3673e-ebb3-4817-ac1d-a40729948df2</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>1f435676-c4aa-40d7-9bb2-fbd345863572</guid>
            <versionId>969fcdd7-7ff6-43a3-814e-34fe00f152b4</versionId>
        </configOption>
        <configOption name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9ec8c5e2-52d3-408f-b11d-4b8b11b003a4</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>f3814b98-73fe-4a1a-908e-ff56a13086ed</guid>
            <versionId>53d198c3-4229-4866-98de-b3a668111f9b</versionId>
        </configOption>
        <configOption name="selectedBIC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8f7aebb6-e5dd-45b2-929b-43b196d52eba</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>37f5b826-0245-4dbf-a0c0-8752450c9951</guid>
            <versionId>a70b948b-14f0-46be-befa-c3f856e6097d</versionId>
        </configOption>
        <configOption name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.36d67f88-e34e-451e-aca7-0fa0b5399509</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>7f1dcb43-0551-49f3-822b-f54c7ac5c904</guid>
            <versionId>95f3efe0-d73e-4c53-b668-79dce0bd261e</versionId>
        </configOption>
        <configOption name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d23da83d-ab5f-40fb-81c2-f2e3b729caf9</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>1757cedd-c73d-476e-abfb-425d63a336aa</guid>
            <versionId>dffc31e2-4a3b-4291-9784-662120328e20</versionId>
        </configOption>
        <configOption name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.*************-4cf1-9b09-0962ad8e7c63</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>301ff95c-825f-4973-9484-3577d0124509</guid>
            <versionId>ef89c90c-80cb-4d90-a73d-c835280fd7ec</versionId>
        </configOption>
        <configOption name="addressBICList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bac6e286-3c4d-49fd-9849-9e37eac87452</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>49efb760-010a-4c8a-823d-e5f79169cea0</guid>
            <versionId>39c4c81a-54e6-45c1-839c-d48505413702</versionId>
        </configOption>
        <configOption name="selectedCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.3d150725-d109-4628-877a-75f3c86699ae</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>8387909a-e563-4ab1-b015-c88d80070802</guid>
            <versionId>69e5c9f9-f478-482a-b3fc-4ff47285343e</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b48c5d0c-8862-4195-907c-eff4753bb496</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>123eb7f8-43aa-4060-b4ab-b7d0700a483e</guid>
            <versionId>9fb84829-315b-47e7-ae1e-19bcd136b926</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8ea19e52-81cf-4583-9cf7-0ad450cb4dfb</coachViewConfigOptionId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>12</seq>
            <description></description>
            <groupName></groupName>
            <guid>f5b09a78-ebe2-483e-96e2-f45e3e38bbb6</guid>
            <versionId>e012f509-9313-4481-b3ec-b298ccf9dab0</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.140bf5c6-6db0-4da6-b7e9-e662158fa9b0</coachViewInlineScriptId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.validateParty = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	this.ui.get("PartyTable/address["+record+"]").setEnabled(false);&#xD;
	this.ui.get("PartyTable/media["+record+"]").setEnabled(false);&#xD;
	var newString = "";&#xD;
	//Concat string&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").length(); i++) {&#xD;
		newString +=this.context.binding.get("value").get(i).get("partyType").name +",";&#xD;
	}&#xD;
	this.context.options.concatString.set("value", newString);&#xD;
    //Default values&#xD;
	if (value.getData().name == "Accountee"|| value.getData().name == "Case in Need") {&#xD;
		this.context.binding.get("value").get(record).set("country", "EG");&#xD;
	}&#xD;
&#xD;
	if (value.getData().name == "Remitting Bank"){&#xD;
		this.context.binding.get("value").get(record).set("media", "SWIFT");&#xD;
		this.context.binding.get("value").get(record).set("reference", this.context.options.BeneficiaryDetails.get("value").get("correspondentRefNum"));&#xD;
		this.ui.get("PartyTable/address["+record+"]").setEnabled(true);&#xD;
		this.ui.get("PartyTable/media["+record+"]").setEnabled(true);&#xD;
		&#xD;
	}else{&#xD;
		this.context.binding.get("value").get(record).set("media", "");&#xD;
		this.ui.get("PartyTable/address["+record+"]").setData("");&#xD;
		this.context.binding.get("value").get(record).set("language", "ENG");&#xD;
		this.context.binding.get("value").get(record).set("reference", "NO REF")&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setPartyId = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	this.context.options.partyIndex.set("value", record);&#xD;
	this.context.binding.get("value").get(record).set("isRetrived", false);&#xD;
	&#xD;
	if (this.context.binding.get("value").get(record).get("partyType").name == "Accountee") {&#xD;
        	this.context.options.accounteeCIF.set("value", value.getData());&#xD;
	}else if (this.context.binding.get("value").get(record).get("partyType").name == "Case in Need") {&#xD;
        	this.context.options.caseCIF.set("value", value.getData());&#xD;
	}&#xD;
	&#xD;
	if(isNaN(Number(value.getData())) || value.getData().length &lt; 8){&#xD;
        value.setValid(false , "must be 8 digits");&#xD;
		return false;&#xD;
	}else{&#xD;
        value.setValid(true);&#xD;
		return true;	&#xD;
	}&#xD;
}&#xD;
&#xD;
this.concatOnDelete = function (value){&#xD;
	var newString = "";&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").length(); i++) {&#xD;
		if (i == value &amp;&amp; i &gt; 1) {&#xD;
			continue;&#xD;
		}&#xD;
		newString +=this.context.binding.get("value").get(i).get("partyType").name +",";&#xD;
	}&#xD;
	this.context.options.concatString.set("value", newString);&#xD;
}&#xD;
&#xD;
this.drawerNotNBE = function (){&#xD;
    if (this.context.binding.get("value").get(1).get("isNbeCustomer") == false){&#xD;
        this.context.binding.get("value").get(1).set("country", this.context.options.BeneficiaryDetails.get("value").get("country").get("englishdescription"));&#xD;
		this.context.binding.get("value").get(1).set("name", this.context.options.BeneficiaryDetails.get("value").get("name"));&#xD;
	}&#xD;
}&#xD;
&#xD;
this.mapPartyData = function (){&#xD;
    record = this.context.options.partyIndex.get("value");&#xD;
	this.context.binding.get("value").get(record).set("address1", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine1"));&#xD;
	this.context.binding.get("value").get(record).set("address2", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine2"));&#xD;
	this.context.binding.get("value").get(record).set("address3", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine3"));&#xD;
	this.context.binding.get("value").get(record).set("name", this.context.options.customerFullDetails.get("value").get("EnglishName"));&#xD;
	this.context.binding.get("value").get(record).set("country","EG");&#xD;
	this.context.binding.get("value").get(record).set("language", "ENG");&#xD;
}&#xD;
&#xD;
this.clickEx = function (value){&#xD;
	record = value.ui.getIndex();&#xD;
	value.setData(true);&#xD;
	if (this.context.binding.get("value").get(record).get("partyCIF") != "" &amp;&amp; this.context.binding.get("value").get(record).get("partyCIF") != null &amp;&amp; this.context.binding.get("value").get(record).get("partyCIF") != undefined) {&#xD;
		if (this.context.binding.get("value").get(record).get("partyType").name != "Remitting Bank"){&#xD;
	    		this.context.binding.get("value").get(record).set("partyId",  this.context.binding.get("value").get(record).get("partyCIF"));&#xD;
		}else{&#xD;
	        	this.context.binding.get("value").get(record).set("partyId", this.context.binding.get("value").get(1).get("partyCIF"));&#xD;
	        	this.ui.get("GetAddressBIC").execute(this.context.binding.get("value").get(record).get("partyCIF"));&#xD;
		}&#xD;
	    	this.ui.get("GetPartyDetails1").execute(this.context.binding.get("value").get(record).get("partyCIF"));&#xD;
	}	&#xD;
}&#xD;
&#xD;
this.setBIC = function (value){&#xD;
    this.context.options.selectedBIC.set("value", value.getData());&#xD;
}&#xD;
//---------------------------------------------------------------------------Drop_2--------------------------------------------------&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}&#xD;
&#xD;
this.disableRetrieveBtn = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	if (record != 1){&#xD;
//		value.setEnabled(false);&#xD;
		value.hide();&#xD;
	}else{&#xD;
		value.setEnabled(true);&#xD;
	}&#xD;
	if(record == 1 &amp;&amp; this.context.binding.get("value").get(record).get("isNbeCustomer") == true){&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(true);&#xD;
	}else if (record != 1){&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(true);&#xD;
	}else {&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(false);&#xD;
	}&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>47c709df-7160-4b8f-9af4-d7b1950c8c1d</guid>
            <versionId>5e9e5aff-8aef-4da3-b4f9-b357f39de2f1</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.8dc33568-c090-4486-8485-8741bb7bdefb</coachViewLocalResId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <resourceBundleGroupId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/50.babe04b6-56bb-4ff0-9723-638bba7612f2</resourceBundleGroupId>
            <seq>0</seq>
            <guid>37a94857-d992-49a2-84d8-64a447aca8ad</guid>
            <versionId>dbe172f0-0ef1-4268-ba24-7a6e671e6c25</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.d1cb64b6-7ca8-47e6-98f2-da2d20305e74</coachViewLocalResId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <resourceBundleGroupId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/50.49a69c8c-61e1-426f-907a-e8ed80310ea5</resourceBundleGroupId>
            <seq>1</seq>
            <guid>04ebcce0-3ef8-4c28-8381-99ec3e3ec3c8</guid>
            <versionId>95a2622f-1088-4f64-951b-232d4f12d198</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.a69f2315-2ae0-46ce-b673-6238c33ef8d4</coachViewLocalResId>
            <coachViewId>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>2</seq>
            <guid>4fbff84f-4c89-4484-85fc-10fe304faa3c</guid>
            <versionId>43defaba-94ce-413d-bfe1-cabe0c08683f</versionId>
        </localization>
    </coachView>
</teamworks>

