<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.80b52f2e-cf28-4943-b3bd-447b9230d1db" name="IDC Request Details UI 3">
        <lastModified>1692800138124</lastModified>
        <lastModifiedBy>eslam</lastModifiedBy>
        <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.5b0cb366-e3e1-4b8b-9ee9-8a0b800d2845</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>5</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</description>
        <guid>5f10a3db-6cd5-43c5-b2ce-0f1638b1b1db</guid>
        <versionId>0d14b68b-3083-4c3b-bd61-c787c3aecd10</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.690dcf77-98e6-47a3-a4d4-f4baf4c88a0c</processParameterId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9cf144cd-4136-47f3-a13a-c2f7bd76b421</guid>
            <versionId>fe11847a-a49d-4e24-81b0-b22c8c015ff2</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d129b7b6-2145-48d6-8bc5-327ecfc2d798</processParameterId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8f2740b6-7786-4ab6-bd3f-8e3e0540546b</guid>
            <versionId>1e2e9f46-7b05-4ea4-a4e3-e695bcee0ee1</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e644912b-b285-47fb-b611-1d4616436c0d</processParameterId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4de1d6e0-ae8a-41c9-9f2f-13e92fce11aa</guid>
            <versionId>*************-4389-b0c1-e3ffb0d38dc5</versionId>
        </processParameter>
        <processVariable name="activitiesTextFilter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.467dcf83-74ed-4a30-821d-955c280f2d72</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2882fd82-1f56-41aa-9a16-6feec8944af9</guid>
            <versionId>9d859182-9517-494f-b378-1510a2a31ffe</versionId>
        </processVariable>
        <processVariable name="breadcrumbs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2fc273b8-665f-4c8f-9689-fce7c838b282</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a4dadf6-b645-421e-adb2-bc0a0af93e0a</guid>
            <versionId>bbae0101-0a52-45fe-abd1-b3505d6ad0a2</versionId>
        </processVariable>
        <processVariable name="buttonClicked_GanttView">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63d01b7f-abed-4b0b-a8ff-c7ab1e940caf</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7950b92a-c102-4a90-815d-0705da9b5bc7</guid>
            <versionId>dea96d11-2443-4f56-a14b-2bae397ce70b</versionId>
        </processVariable>
        <processVariable name="currentDate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0427652c-9faa-487f-8c1b-f815663dd28e</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9d704324-b7ce-4ec7-9794-ce0a0e722b33</guid>
            <versionId>1a6a002f-68a2-4665-83d8-863528ed7893</versionId>
        </processVariable>
        <processVariable name="follow">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.806a9f27-88c8-4731-98c6-e1575c2224c8</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3b7e1e11-c982-4aeb-ba73-0600776e6359</guid>
            <versionId>77cb69b0-3f83-4be8-898b-56ff6a73ef8f</versionId>
        </processVariable>
        <processVariable name="instanceName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab5ae478-7275-4a5f-8333-f5a3457b9ac1</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1a4bb402-8c18-4b1b-9211-6cc4025ca2c3</guid>
            <versionId>d4530eb4-af45-4b4e-a071-ebaf28461fb9</versionId>
        </processVariable>
        <processVariable name="hideDocumentSection">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c92886ac-b9f9-4e96-9511-5c15ab1d9242</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>80d41925-c2b5-4e62-bd99-dbb8b7ade8d0</guid>
            <versionId>bd953e4d-3eaf-4dcf-9989-d0a765653e98</versionId>
        </processVariable>
        <processVariable name="instanceSummary">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d97ca4f6-3279-46fc-8bd2-d48170d63949</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>0ba27466-a399-4ffe-9736-824ab864919c/12.9a9d479f-686b-484b-80a6-30b52aa4e935</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2afdc648-2c5f-4051-a17a-74a4a4e333f4</guid>
            <versionId>3dc09c12-8f6a-463c-8237-d367dbd5dd23</versionId>
        </processVariable>
        <processVariable name="selectedActivitiesCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1c171268-53f9-4804-86b2-8eb0f1e13e6d</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>58057cbf-c63d-43b9-9ee2-7b5edbb19007</guid>
            <versionId>aa4224a0-d734-472a-ade0-12859752fee8</versionId>
        </processVariable>
        <processVariable name="activitiesCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7a82015f-7699-4251-adde-9adb1f41cbb3</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>046e1eb6-53d7-4256-b34d-b1a2a543b531</guid>
            <versionId>541fef87-7c97-4b3a-a07d-c4d96cd97d3b</versionId>
        </processVariable>
        <processVariable name="selectedTasksCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.85045268-75cb-41c8-8354-1d372770d8bb</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b93d7859-d31b-43d0-ba62-2f16e8a47536</guid>
            <versionId>01f35789-3694-4af9-86c4-163525d15274</versionId>
        </processVariable>
        <processVariable name="tasksCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1f2074e3-9805-4d09-b436-9deef39355a2</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>18d7566d-875f-4395-86f2-ab1172dde463</guid>
            <versionId>cec7931f-7789-48bd-9b47-cb086dd45c3d</versionId>
        </processVariable>
        <processVariable name="selectedInstanceId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e52c2f44-08d3-4b25-9456-989f9bd4779b</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c06aef07-75f3-4990-93f4-fd8b04e0ee37</guid>
            <versionId>90adafba-7d12-4b5f-a4e0-3e9a9edc72ed</versionId>
        </processVariable>
        <processVariable name="automaticRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0dded75d-7ed7-4bac-b841-a5c151fbfa6c</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a94f8cf1-41e8-4f66-950b-49e433481896</guid>
            <versionId>55a670ef-3a1c-4827-a7e0-22df8f8b908b</versionId>
        </processVariable>
        <processVariable name="manualRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0faba3e1-e4a8-4270-b1f4-ba4a9d4d3a1d</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>09b509bb-46e3-4f00-8f13-288614b934b2</guid>
            <versionId>7a649415-be28-4841-bf9a-aec0f63d6e94</versionId>
        </processVariable>
        <processVariable name="failedSaveMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4e7cad33-2e7b-4599-a37b-a84ec4ff9a5e</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>df8bc875-89a6-466a-a5b9-19895e0fd6b9</guid>
            <versionId>dd6e5e6d-e6a9-488a-b9c8-caf065bab62e</versionId>
        </processVariable>
        <processVariable name="boSaveFailedError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eb38130d-9dbb-4b5b-ad94-15906dd5488f</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d8fa7561-8636-40a9-bd70-f45128bb7e54</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f6e79dcc-63e1-4ff9-8839-32122da10385</guid>
            <versionId>4b5d5102-58f7-4bea-b006-182d92d7bf8f</versionId>
        </processVariable>
        <processVariable name="canViewDiagram">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fb22b29a-6b4b-4b0e-ba6b-e1654c540adb</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>30c80dea-4030-401a-9d15-c8f7ddf6c551</guid>
            <versionId>31f2d52f-e5e8-435d-9627-777c3ba576b2</versionId>
        </processVariable>
        <processVariable name="navigationURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.043c9a71-07d7-4654-a187-ca7031239db5</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>843c7d5e-d2fd-4112-aa14-3294f404e02d</guid>
            <versionId>c63752f6-db9e-4b41-99ed-a79a96e8a2d7</versionId>
        </processVariable>
        <processVariable name="unsavedLocalChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.65f95c6e-f71c-4505-86d4-17c9a5f43b46</processVariableId>
            <description>This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b12ffd77-6768-4b15-b1c1-a73d4c65166c</guid>
            <versionId>6d4e807a-ac0b-46f7-ac64-66ce5b4615dd</versionId>
        </processVariable>
        <processVariable name="originalInput">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab4caa8f-ec72-4d82-95a8-d59bc0256a42</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>586388f0-1576-4a54-9fa2-57e35bc975aa</guid>
            <versionId>f803c3aa-cfff-4612-a272-b306567ebe38</versionId>
        </processVariable>
        <processVariable name="incomingChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d03b3e91-c0a0-48ea-9824-bbe35bbc03b5</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>46f3a149-aa21-44a5-94ee-0c1dc52ee740</guid>
            <versionId>e4c50178-7762-4b40-aad5-938fd2fccae4</versionId>
        </processVariable>
        <processVariable name="localChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.24e384df-1b1a-47ec-ae31-d00b268c09b4</processVariableId>
            <description>This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>65997501-edeb-4ff1-8ca5-46c7389a0c80</guid>
            <versionId>4aba7b0f-2df8-4fcc-b28a-313cb0fb8f23</versionId>
        </processVariable>
        <processVariable name="dataSectionBoundaryEventType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6168be58-2b43-434f-96fd-8db0f49bd325</processVariableId>
            <description>This variable indicates the type of boundary event triggered by the Data Section coach view.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc1c6acc-1743-4ac7-9be2-4d256264a2ec</guid>
            <versionId>95899b65-753b-49e5-8cc3-c4bc937fc42c</versionId>
        </processVariable>
        <processVariable name="helperScriptURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.72a3e454-66ef-4d50-819b-2136b6607abe</processVariableId>
            <description>This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d6fff0b6-abe4-4f43-b66e-8ae6d33b52c8</guid>
            <versionId>6c373c05-0268-4a9b-bae1-fd909aac052b</versionId>
        </processVariable>
        <processVariable name="incomingChangesMerged">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8ef9ee0f-42ba-431d-a0c1-71cda01b4d27</processVariableId>
            <description>This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</description>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3422b220-a941-49c8-81d6-84c542667040</guid>
            <versionId>47ab6934-9762-4608-b647-d269d537a28e</versionId>
        </processVariable>
        <processVariable name="documentSectionVisibility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.81c80af6-6149-4be6-8a85-214c90858bd3</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>57d5533a-b86e-49dc-80f7-4b8384d83bdd</guid>
            <versionId>cb6555ee-950d-46c2-8669-bb5de8c8569d</versionId>
        </processVariable>
        <processVariable name="havePaymentTerm">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b43ea862-862a-4ded-9c71-c4bb9f57c15f</processVariableId>
            <description isNull="true" />
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ee4683ac-2d5d-4752-b8be-094ce23f448e</guid>
            <versionId>51da9e9c-a28a-4739-89cb-935c93cb66a3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b52bbaee-d1ab-4e38-bbcd-dc4cd3e91385</processItemId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <name>Get Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.28bdedfc-4329-4b99-85a8-9c6e71973a98</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee21eb31e0163954:-121b3b42:18a036b92b9:1a7b</guid>
            <versionId>13c729ff-2371-48d8-ab9b-3b767a2925bd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.28bdedfc-4329-4b99-85a8-9c6e71973a98</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>d1d1cfc5-6f79-4d41-be98-4fb19d6646e9</guid>
                <versionId>2e71a9d9-e889-48a7-813d-411c0a44b35c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5b0cb366-e3e1-4b8b-9ee9-8a0b800d2845</processItemId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.9a984013-7983-4486-82fb-254e721f288e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee21eb31e0163954:-121b3b42:18a036b92b9:1a7e</guid>
            <versionId>25ce362e-913f-4a60-8cd3-513bc698c745</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4f8208a0-2642-47b8-bcab-b11153c577f1</processItemId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d764afd2-4bfd-4a12-ab3e-8355e5f5ee6c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee21eb31e0163954:-121b3b42:18a036b92b9:1a7c</guid>
            <versionId>4c6167d8-157c-433e-885e-e704b1a0ffea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d764afd2-4bfd-4a12-ab3e-8355e5f5ee6c</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9fe82f26-ed8c-4bd8-bf0f-817a19e79680</guid>
                <versionId>15c6cf03-aedf-44cc-9e47-686c05402875</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8</processItemId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <name>Update Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.729283f2-dbba-4f0a-bd99-eece21cf3669</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee21eb31e0163954:-121b3b42:18a036b92b9:1a7f</guid>
            <versionId>656a5502-2a2c-4944-acb6-d0f5ac445417</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.729283f2-dbba-4f0a-bd99-eece21cf3669</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>4893df56-f305-4a2d-9e73-b1bacb404e28</guid>
                <versionId>2dd2e9ad-5b7d-4f5d-9a34-59315903133a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf</processItemId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <name>Server Side Init Data</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4cc0fe06-131d-4ed8-92d9-1a8f0b8e6ce8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee21eb31e0163954:-121b3b42:18a036b92b9:1a7d</guid>
            <versionId>ed5594a4-8891-4af6-ba76-b8eb70b8c44d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4cc0fe06-131d-4ed8-92d9-1a8f0b8e6ce8</subProcessId>
                <attachedProcessRef>0ba27466-a399-4ffe-9736-824ab864919c/1.554dd7d5-ceb8-4548-91e5-788940b70e0d</attachedProcessRef>
                <guid>0fa7a155-d86d-42f7-bfc9-9c69f7fe86fb</guid>
                <versionId>64ea5257-eda3-45d3-a953-e1a86dc60601</versionId>
            </TWComponent>
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.0af848ae-8625-455f-a46f-f540f6f1baa2</resourceProcessLinkId>
            <resourceBundleGroupId>0ba27466-a399-4ffe-9736-824ab864919c/50.4b698a84-427b-4801-9c1d-18ddcc561bc6</resourceBundleGroupId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <guid>885e37b8-3d01-4ad4-baef-42d1b5d0cf52</guid>
            <versionId>580fe5f0-284a-44ce-9ec7-fc104d26f327</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.5b0cb366-e3e1-4b8b-9ee9-8a0b800d2845</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="311104fa-0b9d-4344-a84f-427bc9edd703" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="IDC Request Details UI 3" id="1.80b52f2e-cf28-4943-b3bd-447b9230d1db">
                    
                    
                    <ns16:documentation>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</ns16:documentation>
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="6533cc30-40a0-4917-8153-6e0264340291">
                            
                            
                            <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="23feea56-a828-426c-ab06-8df2fab80089">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.fa981844-8a6f-499c-af64-9ee6c070db9a</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="activitiesTextFilter" id="2056.467dcf83-74ed-4a30-821d-955c280f2d72" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="breadcrumbs" id="2056.2fc273b8-665f-4c8f-9689-fce7c838b282" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="buttonClicked_GanttView" id="2056.63d01b7f-abed-4b0b-a8ff-c7ab1e940caf" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="currentDate" id="2056.0427652c-9faa-487f-8c1b-f815663dd28e" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="follow" id="2056.806a9f27-88c8-4731-98c6-e1575c2224c8" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceName" id="2056.ab5ae478-7275-4a5f-8333-f5a3457b9ac1" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hideDocumentSection" id="2056.c92886ac-b9f9-4e96-9511-5c15ab1d9242" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935" isCollection="false" name="instanceSummary" id="2056.d97ca4f6-3279-46fc-8bd2-d48170d63949" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedActivitiesCategory" id="2056.1c171268-53f9-4804-86b2-8eb0f1e13e6d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="activitiesCategoriesSelectionList" id="2056.7a82015f-7699-4251-adde-9adb1f41cbb3" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedTasksCategory" id="2056.85045268-75cb-41c8-8354-1d372770d8bb" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="tasksCategoriesSelectionList" id="2056.1f2074e3-9805-4d09-b436-9deef39355a2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedInstanceId" id="2056.e52c2f44-08d3-4b25-9456-989f9bd4779b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="automaticRefreshTrigger" id="2056.0dded75d-7ed7-4bac-b841-a5c151fbfa6c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="manualRefreshTrigger" id="2056.0faba3e1-e4a8-4270-b1f4-ba4a9d4d3a1d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="failedSaveMessage" id="2056.4e7cad33-2e7b-4599-a37b-a84ec4ff9a5e" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54" isCollection="false" name="boSaveFailedError" id="2056.eb38130d-9dbb-4b5b-ad94-15906dd5488f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="canViewDiagram" id="2056.fb22b29a-6b4b-4b0e-ba6b-e1654c540adb" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="navigationURL" id="2056.043c9a71-07d7-4654-a187-ca7031239db5" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="unsavedLocalChanges" id="2056.65f95c6e-f71c-4505-86d4-17c9a5f43b46">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="originalInput" id="2056.ab4caa8f-ec72-4d82-95a8-d59bc0256a42">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="incomingChanges" id="2056.d03b3e91-c0a0-48ea-9824-bbe35bbc03b5">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="localChanges" id="2056.24e384df-1b1a-47ec-ae31-d00b268c09b4">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="dataSectionBoundaryEventType" id="2056.6168be58-2b43-434f-96fd-8db0f49bd325">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable indicates the type of boundary event triggered by the Data Section coach view.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="helperScriptURL" id="2056.72a3e454-66ef-4d50-819b-2136b6607abe">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="incomingChangesMerged" id="2056.8ef9ee0f-42ba-431d-a0c1-71cda01b4d27">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="documentSectionVisibility" id="2056.81c80af6-6149-4be6-8a85-214c90858bd3" />
                            
                            
                            <ns16:exclusiveGateway default="2027.8566257c-6b76-4a11-bf48-27ebe76f4a47" gatewayDirection="Unspecified" name="Data Section Action?" id="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="919" y="235" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.68117914-012e-40c6-b02b-13a5f78ed831</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.7269ac2e-918a-4fbe-ae75-b304e2c53b13</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fb249304-24b2-4229-bc05-36f2be1c4efd</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.17da7434-bf9f-4fd6-bb26-c6111b1c3cc9</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.8566257c-6b76-4a11-bf48-27ebe76f4a47</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.7cd350d7-983a-478e-9dc9-39baeb071564" name="Populate BO Save Error" id="2025.527b8c17-c535-467e-89f7-a01de4d2ab42">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1372" y="424" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.36165a1a-f483-4650-a6b7-47ea1d20c120</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7cd350d7-983a-478e-9dc9-39baeb071564</ns16:outgoing>
                                
                                
                                <ns16:script>tw.system.coachValidation.populateFromBOSaveFailedError( tw.local.boSaveFailedError );</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.444c5d1c-2de8-450f-a1ae-9c0be94ccd35" name="Init Data Change Support" id="2025.257db290-344d-40a0-98d9-34a1529985be">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="733" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4476fbd0-8871-43a9-b76a-481dea601c02</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.444c5d1c-2de8-450f-a1ae-9c0be94ccd35</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// Initialize variables that support data change syncrhonization.  &#xD;
	&#xD;
	// This can only be done once the URL for the helper functions is returned by the &#xD;
	// Server Sice Init Data.  Since this is can also be invoked as part of auto refresh the initialization&#xD;
	// should only happen if the variables are undefined.&#xD;
	&#xD;
	if(tw.local.originalInput == undefined &amp;&amp; tw.local.localChanges == undefined &amp;&amp; tw.local.incomingChanges == undefined){&#xD;
		initializeDataSyncronizationVariables(tw.local); // Intialize variables&#xD;
	}&#xD;
	&#xD;
}); &#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:subProcess triggeredByEvent="true" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Data Change" id="2025.827cdfde-7f69-42ee-a35a-13867ad49022">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="261" y="463" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="2025.d25bab6e-c252-414e-a24b-fd4756bc3848">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:outgoing>2027.48b67e8b-d304-415c-a3f6-a60f38607f0b</ns16:outgoing>
                                    
                                    
                                    <ns3:dataNotificationEventDefinition>
                                        
                                        
                                        <ns16:extensionElements>
                                            
                                            
                                            <ns3:coachEventTriggers enable="true">
                                                
                                                
                                                <ns3:coachEventTriggerBinding id="99ed94e1-ff64-4b2a-a21b-f79109d5acac">
                                                    
                                                    
                                                    <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller2</ns3:coachEventPath>
                                                    
                                                    
                                                    <ns3:coachId>2025.323ea910-2504-4da8-a9da-9a2c076942da</ns3:coachId>
                                                    
                                                
                                                </ns3:coachEventTriggerBinding>
                                                
                                            
                                            </ns3:coachEventTriggers>
                                            
                                        
                                        </ns16:extensionElements>
                                        
                                    
                                    </ns3:dataNotificationEventDefinition>
                                    
                                
                                </ns16:startEvent>
                                
                                
                                <ns16:intermediateThrowEvent name="Stay on page" id="2025.ac428002-13c2-4d80-a168-45a2cd720092">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" />
                                        
                                        
                                        <ns3:navigationInstructions>
                                            
                                            
                                            <ns3:targetType>Default</ns3:targetType>
                                            
                                        
                                        </ns3:navigationInstructions>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:incoming>2027.a641c1b9-fbda-4c9e-96dc-03e477a16614</ns16:incoming>
                                    
                                    
                                    <ns3:stayOnPageEventDefinition />
                                    
                                
                                </ns16:intermediateThrowEvent>
                                
                                
                                <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.a641c1b9-fbda-4c9e-96dc-03e477a16614" name="Collect change information" id="2025.1c49235f-3757-41b4-8dfd-5bc4d9108a00">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="345" y="176" width="95" height="70" />
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:incoming>2027.48b67e8b-d304-415c-a3f6-a60f38607f0b</ns16:incoming>
                                    
                                    
                                    <ns16:outgoing>2027.a641c1b9-fbda-4c9e-96dc-03e477a16614</ns16:outgoing>
                                    
                                    
                                    <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
	&#xD;
	// Calculate if there are any relevant incoming changes&#xD;
&#xD;
	// There are unsaved changes .  This can be detected by calling tw.system.coachUtils.hasLocallyChangedInputVars().  Here, however, this variable is&#xD;
	// set by the Data Section coach view when it detects that a child view's data binding has changed.&#xD;
	if(tw.local.unsavedLocalChanges ===  true || tw.system.coachUtils.hasLocallyChangedInputVars()){&#xD;
		&#xD;
		if(!tw.local.unsavedLocalChanges)&#xD;
			tw.local.unsavedLocalChanges = true;&#xD;
			&#xD;
		// Call the helper function to update the incoming and local changes.&#xD;
		checkIncomingChanges(tw.local, tw.system.coachUtils.getLocallyChangedVars(),  tw.system.dataChangeUtils.getIncomingVars())		          &#xD;
		&#xD;
	} else if(tw.system.coachUtils.getLocallyChangedVars().length &gt; 0) { // No pending changes to be merged, or local changes&#xD;
	&#xD;
		// Apply incoming changes to the local variables.&#xD;
		tw.system.dataChangeUtils.applyAllIncomingVars();&#xD;
		tw.local.incomingChangesMerged = true;  // Signal that changes were applied automatically.  The Data Section can listen and pop up a notification.&#xD;
	 &#xD;
	}&#xD;
&#xD;
}); </ns16:script>
                                    
                                
                                </ns16:scriptTask>
                                
                                
                                <ns16:sequenceFlow sourceRef="2025.d25bab6e-c252-414e-a24b-fd4756bc3848" targetRef="2025.1c49235f-3757-41b4-8dfd-5bc4d9108a00" name="To Collect change information" id="2027.48b67e8b-d304-415c-a3f6-a60f38607f0b">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:linkVisualInfo>
                                            
                                            
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            
                                            
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            
                                            
                                            <ns13:showLabel>false</ns13:showLabel>
                                            
                                            
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            
                                            
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            
                                            
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                            
                                        
                                        </ns13:linkVisualInfo>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:sequenceFlow>
                                
                                
                                <ns16:sequenceFlow sourceRef="2025.1c49235f-3757-41b4-8dfd-5bc4d9108a00" targetRef="2025.ac428002-13c2-4d80-a168-45a2cd720092" name="To Stay on page" id="2027.a641c1b9-fbda-4c9e-96dc-03e477a16614">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:linkVisualInfo>
                                            
                                            
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            
                                            
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            
                                            
                                            <ns13:showLabel>false</ns13:showLabel>
                                            
                                            
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            
                                            
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            
                                            
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                            
                                        
                                        </ns13:linkVisualInfo>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:sequenceFlow>
                                
                            
                            </ns16:subProcess>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b335a598-4a04-4228-ab25-c2d39c8d6ba3" name="Reset Pending Change Data" id="2025.ab19441a-98cf-437b-b393-7d7ff201c2b8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="765" y="406" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.81738a2b-8683-4133-973e-948969d87888</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.ccf5d8df-def1-4369-90ac-2341680e24fb</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b335a598-4a04-4228-ab25-c2d39c8d6ba3</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The local variables have been refreshed with the latest changes from the server,&#xD;
	// or the instance UI has been saved.  There are no pending changes.  Reset merge data.&#xD;
		&#xD;
	resetDataSyncronizationVariables(tw.local); // Reset variables&#xD;
	          &#xD;
}); &#xD;
&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.9d4956a1-a4e0-4ca8-8e3d-60c62c424f53" name="Merge Changes" id="2025.b68c998f-62b2-4358-8ae0-1f00d32dd28d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1275" y="213" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8566257c-6b76-4a11-bf48-27ebe76f4a47</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.9d4956a1-a4e0-4ca8-8e3d-60c62c424f53</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The merge the server changes into the local variables based on the merge policy picked by the user.&#xD;
&#xD;
	// Determine what type of merge the user requested&#xD;
	var keepConflictingLocalChanges = false;&#xD;
	if(tw.local.dataSectionBoundaryEventType === "KEEP_LOCAL")&#xD;
		keepConflictingLocalChanges = true;&#xD;
		&#xD;
	merge(tw.local, keepConflictingLocalChanges); // Reset variables&#xD;
	          &#xD;
}); </ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.c84a35e7-96cc-4711-a32c-981658fdc905">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="771" y="519" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b335a598-4a04-4228-ab25-c2d39c8d6ba3</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:exclusiveGateway default="2027.6318fb2f-d234-41ee-91c7-88277995bb7e" gatewayDirection="Unspecified" name="Validation Error?" id="2025.52e736d4-52c1-435e-8348-ad47bff888c3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1162" y="317" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c789113e-5683-43bb-9695-aa607b066334</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.afe9263d-17f6-4b34-8501-9546633ed078</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.6318fb2f-d234-41ee-91c7-88277995bb7e</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.c789113e-5683-43bb-9695-aa607b066334" name="Validation" id="2025.3f13390a-a0f9-46a2-975c-346df06d1fe5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1025" y="298" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.17da7434-bf9f-4fd6-bb26-c6111b1c3cc9</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c789113e-5683-43bb-9695-aa607b066334</ns16:outgoing>
                                
                                
                                <ns16:script>//  Check for validation errors in the instance data. For examle:&#xD;
// if (tw.local.name == "" ) {&#xD;
//    tw.system.coachValidation.addValidationError("tw.local.name", "Name must be specified");&#xD;
// }</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:endEvent name="End" id="2025.97841d9d-a80b-4a4c-8cca-d580a80a8234">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1278" y="127" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>ToOtherDashboard</ns3:targetType>
                                        
                                        
                                        <ns3:targetURL>tw.local.navigationURL</ns3:targetURL>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f7cd8072-6d68-4a6b-9518-f0f14808efb7</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.29f3cfd6-ed46-4d21-8511-4c3a8df78b60</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.d384368f-8e9d-4adc-a152-c2a7b727b6f4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1284" y="321" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.afe9263d-17f6-4b34-8501-9546633ed078</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.9d4956a1-a4e0-4ca8-8e3d-60c62c424f53</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.4cbdee9c-b0d4-4feb-be13-6be997632c58</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.7cd350d7-983a-478e-9dc9-39baeb071564</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.ccf5d8df-def1-4369-90ac-2341680e24fb" name="Update Process Variables" id="2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:mode>SaveBPDVariables</ns3:mode>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1166" y="406" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6318fb2f-d234-41ee-91c7-88277995bb7e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.ccf5d8df-def1-4369-90ac-2341680e24fb</ns16:outgoing>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.81738a2b-8683-4133-973e-948969d87888" name="Get Process Variables" id="2025.b52bbaee-d1ab-4e38-bbcd-dc4cd3e91385">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:mode>RefreshVariables</ns3:mode>
                                    
                                    
                                    <ns13:nodeVisualInfo x="765" y="293" width="95" height="70" />
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fb249304-24b2-4229-bc05-36f2be1c4efd</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.81738a2b-8683-4133-973e-948969d87888</ns16:outgoing>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:endEvent name="End" id="2025.ff57bca8-0d2c-4425-b320-aac418a37970">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="611" y="316" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4aef0d98-6a61-4c40-8a5f-22c56578c96a</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Show Error" id="2025.ae26fe07-461b-40b4-8121-a3176be0c59e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="434" y="292" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9ac4ea8b-fa46-4259-b2c5-a2e4dab11598</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4aef0d98-6a61-4c40-8a5f-22c56578c96a</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>78b2ab0b-7b94-46db-8737-8ce857c45be3</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Output_Text1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1fa476df-8872-4212-8eb6-78a4f4ee0c5f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3b1ca332-b7d0-4ac8-81e7-589d693e342f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ebb4f8b6-cc75-4aaa-830c-7cf84c9e566e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6c1c2e85-08d7-47d3-89a6-94fe6b5d5c3d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@htmlOverrides</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.fc2d6e5b-da91-4e0a-b874-3ec8ace34c82</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.resource.Dashboards.defaultInstanceDetails.NoInstance</ns19:binding>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>272ece89-7eb6-4a83-8e48-e6e22e132ae9</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>okButton</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f6970509-b656-457c-86e2-91e5101ed142</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>OK</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e311d86e-e717-4ad1-8042-9806d00d5420</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>649a0446-7ab5-4bef-850a-d509921c432e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.36f46ec6-616b-4e38-86aa-fba20ec6f9b4</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.0367a2e3-970f-495a-a043-1db0f3751284" gatewayDirection="Unspecified" name="Instance present?" id="2025.1eee6e1d-783b-45a8-9018-9eec7e5baf74">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="148" y="196" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fa981844-8a6f-499c-af64-9ee6c070db9a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0367a2e3-970f-495a-a043-1db0f3751284</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.9ac4ea8b-fa46-4259-b2c5-a2e4dab11598</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.9a647f0d-50bc-4223-bbcc-29a786e34a21" name="Client Side Init Data" id="2025.90f66223-13ff-4928-b3da-cfd703c93e40">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="437" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8372111e-9ea9-46a8-abf2-d3e765c610a6</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.9a647f0d-50bc-4223-bbcc-29a786e34a21</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
&#xD;
tw.local.selectedInstanceId = tw.system.processInstance.id;&#xD;
if (tw.system.processInstance.parentActivityId &amp;&amp; (tw.system.processInstance.parentActivityId != null)) {&#xD;
	tw.local.documentSectionVisibility = "NONE";&#xD;
}&#xD;
&#xD;
var breadcrumbText = null;&#xD;
if (window.location.href.indexOf('breadcrumbText=') != -1)  {&#xD;
	breadcrumbText = window.location.href.substring(window.location.href.indexOf('breadcrumbText=')+15);&#xD;
	if (breadcrumbText.indexOf('&amp;') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('&amp;'));&#xD;
	}&#xD;
	if (breadcrumbText.indexOf('#') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('#'));&#xD;
	}&#xD;
}
                                &#xD;
var _debug = function(){};&#xD;
// enable following line to log into browser console&#xD;
//var _debug = console.log;&#xD;
&#xD;
try {&#xD;
	// check for breadcrumb&#xD;
	tw.local.breadcrumbs = [];&#xD;
	if (breadcrumbText) {&#xD;
		tw.local.breadcrumbs.push({&#xD;
			name: decodeURI(breadcrumbText),&#xD;
			value: '{"value":"back","navigationDestination":"BACK"}'&#xD;
		});&#xD;
	}&#xD;
    &#xD;
} catch (err) {&#xD;
	console.log("Error within Instance Details initialization: "+err);&#xD;
}&#xD;
&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:callActivity calledElement="1.554dd7d5-ceb8-4548-91e5-788940b70e0d" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.4476fbd0-8871-43a9-b76a-481dea601c02" name="Server Side Init Data" id="2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf">
                                
                                
                                <ns16:documentation textFormat="text/plain">&lt;br _moz_editor_bogus_node="TRUE" /&gt;</ns16:documentation>
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="582" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9a647f0d-50bc-4223-bbcc-29a786e34a21</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.8d61d21e-cdbc-43a5-ab98-435b21554bea</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.7a36570b-7852-4c7f-afdf-5f8c5de896d3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.4476fbd0-8871-43a9-b76a-481dea601c02</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.79a0c5f3-08ff-45b1-a99d-3b828a60339d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0344478b-933e-4e7e-8122-349aaeb03f2d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.9918e74b-8220-44bd-a6df-213ae1f96c8f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceName</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.99c78e1f-41aa-4f5b-89e4-9f0449dbb478</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935">tw.local.instanceSummary</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8e52cfcb-db25-4bc4-a016-c8103a1cae4b</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.currentDate</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.83bae48e-f188-476a-bd4f-eee8360c042d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.canViewDiagram</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.b2e6d33b-136e-4c8e-b88f-abe718a89062</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.helperScriptURL</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="View Instance Details" id="2025.323ea910-2504-4da8-a9da-9a2c076942da">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1055" y="103" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.74f5145f-7359-4997-8f6c-7e7fab515db1</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8d61d21e-cdbc-43a5-ab98-435b21554bea</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.f7cd8072-6d68-4a6b-9518-f0f14808efb7</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.68117914-012e-40c6-b02b-13a5f78ed831</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>312c9e8b-f749-4fc7-89d7-fdf42381a821</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Default_Instance_Details_Template1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f63e14ca-c966-45b2-882e-81bc2b9c8187</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Default Instance Details Template</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2bcc1159-507c-4aac-8738-9e6fba11a6e1</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d179dba8-0882-4a25-8d61-a0fb9c613201</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b690cd77-16bd-4efd-8563-59acf3690f2e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesTextFilter</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesTextFilter</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>039d54a8-4561-4592-86b1-80d78fb8c9af</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>addButtonDropdownSelectedItem</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>18d42900-50f6-42ca-8402-4e73c7058a9a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.breadcrumbs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e753769b-2683-4abf-8949-c0ee9983d269</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>dd8e737f-97d3-4649-8cce-85cd250b15f7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonClicked_GanttView</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.buttonClicked_GanttView</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>90082a09-f8d0-4d81-8f4f-d0978b834eeb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentPage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>75848139-b40b-4f67-83a2-5357fa191e10</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentAddButtonDropdownList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d78f38bf-38b3-4b96-81af-5cd2965170b3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>follow</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.follow</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>adf58ddf-5fba-4503-8024-a73d35ab180c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceName</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3d03a7a7-7ae9-4769-8ca9-19f74bf3af1f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceSummary</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceSummary</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>75345881-e617-4621-8d2a-23fa52df5af3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2ffcd419-851b-484b-82e6-21633b183a06</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedActivitiesCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedActivitiesCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8c5da619-d4d9-4239-8986-3a9ad08ba623</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedInstanceId</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a8a63797-2fbe-4edb-8a03-d1bfd0d586a5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedTasksCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedTasksCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fad4e682-3e04-4ca1-8f63-816879e3b34d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tasksCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.tasksCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>efb4cd08-f86e-40d7-80e9-8cc94e746452</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentDate</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.currentDate</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>db8724e7-9529-45a3-89a0-056aa3f5311e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hideDocumentSection</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hideDocumentSection</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f8422e0b-3194-4e87-854e-97532e1f6f24</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>refreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d509199f-5840-416e-8ba6-e492ec8116d7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>manualRefreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.manualRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c8dc1403-728a-42dc-8a46-e61f16c73af3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>canViewDiagram</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.canViewDiagram</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2b0fcd30-bde7-4bb8-8188-e237917bd138</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>navigationURL</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.navigationURL</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3fc5cd8c-d88c-443f-82d3-51cac3534953</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7e161c1f-1c84-4d83-8d2c-da98b99f38fd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>preventBoundaryEvent</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>12555f37-bbe3-4f0e-8f64-f2cda0392da9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentSectionVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>38852e9d-acc3-4f4b-8709-dd8666a7f53a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unSavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>241e065f-05b5-433a-8062-e5631cd79712</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedProcessInstanceChangesMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.resource.Dashboards.control.datasection.msg.unsaveddatanavigation</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.5f0e6839-f7f9-41a3-b633-541ba91f9b31</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>7b9be36f-a072-4a9f-8e28-5b82b638e3a5</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>bf0bed38-b0b3-4061-86b8-ed9a128a2f74</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>CustomDataSection</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4e2a3c9c-c1b4-46c1-8702-a759801b9a98</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Data Section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>dfa8c6bf-96af-4357-884c-feccd236438e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b1b4c0df-7ee7-4d78-80b3-b304b125bc71</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Show</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>fe1d0946-027e-4431-8e4e-cf61959e7246</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceId</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>812694fc-ec5b-4b28-8cc3-30b5f79a6df8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>refreshTrigger</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4e9831a9-2a2c-42ad-8496-64f8a2986996</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedUpdateMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ba75e726-3020-4deb-801d-97c773689bc1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedSaveMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>5ffdf52f-a81e-4102-8021-e42182614f3b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsible</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>71c3f7d5-5923-43a1-8afb-08d66ef58935</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceStatus</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>completed</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c56113bb-d385-4851-8f6d-845a7e4735cf</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>boundaryEventType</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>"DISCARD"</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>2671d70f-2db5-481a-8f4d-4bfab3eb4c78</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>72d5852e-9d8a-4e7d-874e-4c65a27b99e8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>localChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.localChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ea375ed0-449e-4fc0-832c-1b3b8aceeb78</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChangesMerged</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChangesMerged</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>64dfbd54-2f9b-47e8-82db-68f5ff41050d</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>977f2648-ba2a-456b-8a27-8e7b2daff4bc</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>originalInput</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.originalInput</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b7418614-9401-468e-87e3-62e9470ba109</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>editMode</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>7685bcfd-df0a-4bbf-8bcc-1724f74dd227</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsed</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.0678c7bb-7028-4bca-8111-e0e9977f294d</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding />
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>af5aa920-caaf-4618-895d-b379640f82ef</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:CustomHTML" version="8550">
                                                                
                                                                
                                                                <ns19:id>a41e3024-237d-42b7-88d4-1b9fd158ab75</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>CustomHTML1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a1080271-b07b-4980-827d-3245b005bf78</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@customHTML.contentType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>TEXT</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>df0edb7a-8b29-47c8-8be6-c9bf7d4750fb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@customHTML.textContent</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>&lt;style&gt;
.spark-ui {
    background: rgb(255, 255, 255);
   // width: 99%;
}
.CoachView.Panel&gt;.panel.SPARKPanel {
border-collapse: separate;
border-spacing: 0;
box-shadow: 0px 0px 12px #001B5929!important;
border-top: 5px solid #00643e!important;
border-radius: 10px!important;
}

.panel-primary.panel-dark&gt;.panel-heading {
background: transparent !important;
border-color: transparent;
color: #fff;
}

.panel-primary.panel-dark {
border-color: transparent!important;
}
.panel-primary.panel-dark &gt; .panel-heading .panel-title {
    color: rgb(0, 101, 71);
    font: normal normal 600 24px/45px Cairo;
}

.form-control {
    border-top-color: rgb(205, 205, 205);
    border-bottom-color: rgb(205,205,205);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 12px 12px;
    color: #181A1D !important;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.SPARKWell .stat-cell .bg-icon {
    line-height: normal;
    height: 100%;
    overflow: hidden;
 //   width: 97%;
    border-radius: inherit;
    box-shadow: 0px 0px 12px #001B5929!important;
    margin-bottom: 6px;
    border-radius: 10px!important;
    border-top: 5px solid !important;
   // margin-right: 49px;
}

//.form-control[disabled] {
//    background: rgb(242 242 242);
//}

.bg-success {
    background: #00654726 !important;
}
//.Single_Select select.placeHolder {
//    color: #0002037a;
//}

.panel-group .panel-heading+.panel-collapse .panel-body {
     border-top: 1px solid #fff;
}

.panel {
	     margin-bottom: 18px;
	     border-radius: 2px;
	     border-width: 0px;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {
	     background: #fff;
	     color: #006547;
	     border-color: #fff;
	     padding: 15px;
	     border-radius: 10px!important;
}

.CoachView.Collapsible_Panel&gt;.panel.SPARKCPanel {
	     border-collapse: collapse;
	     box-shadow: 0px 0px 22px #001B5929!important;
}
.SPARKCPanel &gt; .panel-heading {
    padding: 0px;
    border-top-left-radius: 1px;
    border-top-right-radius: 1px;
    box-shadow: 0px 0px 0px #001B5929!important;
    border-top: 5px solid #00643e!important;
    border-radius: 10px!important;
    border-spacing: 0;
    border-collapse: separate;
}
.panel-body {
    background: #fff;
    margin: 0;
    padding-bottom: 15px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 15px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.panel-group .panel {
    border-radius: 10px;
}

.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{
    width: 50%;
}
.radio3 &gt; input + span{
    display: inline-flex;
    flex-direction: row;
    align-items: center;
}
//.input-group.no-border&gt;.input-group-addon {
//    border-radius: 10px;
//    min-width: 54px;
//    height: 54px;
//    top: -25px;
//}

//.form-group .input{
//    padding-left:73px!important;
//}
//.Input_Group .outer, .control-label{
//padding-left:73px;
//}
//.Single_Select, .control-label{
//padding-left:20!important;
//}
.Input_Group .ContentBox {
    width: 100%;
    border-collapse: collapse;
    padding-left: 20px;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {
    background: #fff;
    color: #006547;
    border-color: #fff;
    border-radius: 10px!important;
    font-size: 18px;
    font-weight: 900;
    padding: 15px;
    font: normal normal 600 24px/45px Cairo;
}

.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role="img"]), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):active {
    padding: 12px 25px;
}
.btn-success, .btn-success:not([role="img"]):focus {
    color: #FFFFFF;
    fill: #006547;
    border-color: #006643;
    border-bottom-color: #006643;
    background: #006643;
    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;
    background-repeat: repeat-x;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
letter-spacing: 0px;
color: #181A1D !important;
opacity: 1;
font: normal normal normal 16px/20px Cairo;
background: transparent;
font-weight: bold;
border: 1px solid #000203;
}
//.Single_Select&gt;.form-group&gt;.input&gt;select {
//
//    border-top-style: ridge;
//    border-bottom-style: outset;
//    border-right-style: outset;
//    border-left-style: ridge;
//    border-top-width: revert;
//    border-left-width: revert;
//    border-bottom-width: revert;
//    border-right-width: revert;
//}
select.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {
    height: 40px;
    line-height: 1.33;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    letter-spacing: 0px;
    color: #181A1D !important;
    opacity: 1;
    font: 14px Cairo;
    background: transparent;
    font-weight: lighter;
}


.btn-success:not([role="img"]):hover {
    color: #ffffff;
    fill: #3D8A70;
    border-color: #3D8A70;
    border-bottom-color: #429c42;
    background: #3D8A70;
    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;
    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;
    background-repeat: repeat-x;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle:hover {
    background: rgb(255 255 255 / 15%);
}
.panel-group .SPARKTable &gt; .panel-heading {
    border-color: rgb(240, 240, 240);
    border-bottom-width: 2px
;
    border-bottom-style: solid;
    background: #006643 0% 0% no-repeat padding-box;
    color: white;
}
.Output_Text&gt;.form-group&gt;.input&gt;p {
  
    padding-right: 1em;
 
}


.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
   
    font-weight: 600;
}




[class*="BPM_Resp_"] .bpm-label-default {
 height: 30px;
    font: normal normal normal 16px/20px Cairo !important;
    letter-spacing: 0px;
    opacity: 1;
    color: #8B8C8E;
}




//.ECMPropertiesContainer{
//display: none; 
//}


.Output_Text&gt;.form-group&gt;.input&gt;p{
unicode-bidi: plaintext ;
text-align: inherit;
}

.CoachViewRTL {
 
    text-align: right !important;
}

.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&gt;* {
    text-align: right;
}

.noIScroll.alignJustify {
    text-align: inherit;
}
.radio3 &gt; input + span {

    unicode-bidi: plaintext;

}

.radio3 &gt; input:checked + span::after {
 
  top: calc(50% - 3px) !important;
}
.switcher-primary .switcher-state-off {
    color: #000203;
}
.CoachViewRTL .Single_Select &gt;{
 unicode-bidi: plaintext;

}

input[type="checkbox"]:checked[disabled] {
    right: 6%;
    opacity: 1;
    width: fit-content;
    left: 3px;
}

.Tab_Section&gt;div&gt;.nav-tabs-mnu {
    position: unset;
   }
   
a {
    color: #333;
    text-decoration: none;
}
.datepicker thead th {
   
    color: #006547;
&lt;/style&gt;</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>baee4c8d-1dce-4d5c-8a36-6c5a2921017d</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f27d60a2-98eb-4487-83a8-3932bce355af</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Tab section</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>94bd11b7-fca5-41a9-85b3-145bbb541004</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a5b5d0b6-f90b-4dcf-8e24-3b25f274c38a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:contentBoxContrib>
                                                                    
                                                                    
                                                                    <ns19:id>dfcf72bd-ccf5-4f18-89d3-f65509dbc3f9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>45786581-27b2-468f-88a6-d0543bb5c8a9</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f606ed0a-9aa6-4aae-84fe-ffb0eb3fad8a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Customer Information</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ca2d1a6-4409-4647-8bfc-19e697eca5d0</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7df5d7a3-3e51-4da8-8e2a-1cbb9f6cc57e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>be6d5372-bac2-4338-8fe8-36d6000659aa</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>3ee6cdf0-b785-4267-8fd3-736d7b0231e6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>441cabe2-3a46-451f-8e4f-0c00b7312fe6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>instanceview</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c388a619-e0d7-4081-83ae-358102374002</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ac08089b-fe45-4ca0-88de-14d5a1f46ece</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Basic Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f2f27ed7-7010-4246-8fe1-ef1ab6a25f92</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>345d391a-b83c-4675-831e-037eed5a2ecc</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ffba317c-8b55-4e92-8dee-44d2d34d588b</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>cce9d3d4-898b-4440-8b2f-cf1cce5c5319</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0dafba76-c784-41e0-858e-cd1fdb9582b3</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ccd42209-8ea9-4787-8016-5d44c58bc756</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>addBill</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>17da7864-9918-4496-8287-ba78026afc53</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>deleteBill</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>bc4b0019-24e5-4735-81c4-193202fdbc4f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>addInvoice</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>db56c9b0-8f8f-4aac-8c3e-b41115d1238c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bec7782b-c964-4a09-b74f-0ec737efa310</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>ad060494-5dd3-4154-825a-6465eddd50e4</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details__Branch_Compliance_Review1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>4bf834c8-32f2-4729-8a92-76071d2972aa</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Branch</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7a6c5a3b-8407-41c6-8d4a-17061cae6fcb</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>beee3dbe-93b3-4a63-8ffe-ab655cd9e0a1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>86c23f9e-1f49-4dae-85e8-5eb854e6b91c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.7020f6c3-7f8d-4052-81e6-d5b6b89f97e9</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>324aaf51-4804-4817-8b1c-ebc2af8f02f9</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details_Trade_FO1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>1e117c55-140f-474b-8354-7fd261d07f2d</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Trade FO</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ebc55ad2-1138-4f36-85a2-f27e7942b15b</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>2128253b-8c50-45a8-891b-3608c56601c0</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>1741ec52-a475-4a1c-8bac-d0b1609ace5e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>abb07088-5b25-44e5-82e6-4d64ee865bee</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerms</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f4e59b23-f574-4c16-8c40-3d7b7705207c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>requestType</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>5c2a9a05-60d4-46ff-84dc-b8f01e004674</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>haveTradeFOReferenceNumber</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>e69eff61-321c-4c9a-8c88-d2d0a9b7c64e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>beneficiaryDetails</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.financialDetails.beneficiaryDetails</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.848ab487-8214-4d8b-88fd-a9cac5257791</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>ae4b12f1-9714-42bc-84ab-7c3b2e5ccd3b</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Products_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>a611ab59-0902-4902-845f-2e96eb05140c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Products Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>530982b8-4bea-49e7-87db-ebfed30698fe</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>5911acdb-7949-43e6-8210-a9e4a959f804</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7a9449c5-cfa3-4ba0-8c1c-8132fdfcce3c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bd0ada34-acf3-449a-91df-9aa363c2b280</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.productsDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>cb813196-c737-44b6-8cb4-d5209cdced8e</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>fe287dfc-85e0-47ce-85ff-4f5cc2aebd25</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>History</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8dcf0335-6d2d-4e44-8aee-f7342f639271</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>daf1b51e-5d34-4551-8d3a-edd5efd01dbf</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>4fef4a0f-16f3-41cd-871d-1bdc83e4ec44</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                
                                                                </ns19:contentBoxContrib>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8" parallelMultiple="false" name="Error" id="2025.a2e0f46b-1f75-48e6-8cca-ca648ae43c63">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1249" y="447" width="24" height="24" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.36165a1a-f483-4650-a6b7-47ea1d20c120</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.78c70fbd-f421-441c-845a-33a83cd5f4aa" />
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54">tw.local.boSaveFailedError</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>false</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8" parallelMultiple="false" name="Error" id="2025.2d93145b-039c-450b-bb28-76610444859f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1249" y="411" width="24" height="24" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.4cbdee9c-b0d4-4feb-be13-6be997632c58</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.d96b1896-734b-4ba2-9708-6e929ded6b94" />
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.failedSaveMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>false</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.1eee6e1d-783b-45a8-9018-9eec7e5baf74" targetRef="2025.f8fe627b-9e0c-4ad0-ba0c-12c92d7fdf9b" name="To Client-Side Script" id="2027.0367a2e3-970f-495a-a043-1db0f3751284">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2" targetRef="2025.b68c998f-62b2-4358-8ae0-1f00d32dd28d" name="Merge Changes" id="2027.8566257c-6b76-4a11-bf48-27ebe76f4a47">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.527b8c17-c535-467e-89f7-a01de4d2ab42" targetRef="2025.d384368f-8e9d-4adc-a152-c2a7b727b6f4" name="To Stay on page" id="2027.7cd350d7-983a-478e-9dc9-39baeb071564">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.257db290-344d-40a0-98d9-34a1529985be" targetRef="2025.e6b23f0e-c326-4be4-9dc0-b4f8abc5180c" name="To Exclusive Gateway" id="2027.444c5d1c-2de8-450f-a1ae-9c0be94ccd35">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf" targetRef="2025.257db290-344d-40a0-98d9-34a1529985be" name="To Init Data Change Support" id="2027.4476fbd0-8871-43a9-b76a-481dea601c02">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b68c998f-62b2-4358-8ae0-1f00d32dd28d" targetRef="2025.d384368f-8e9d-4adc-a152-c2a7b727b6f4" name="To Stay on page" id="2027.9d4956a1-a4e0-4ca8-8e3d-60c62c424f53">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.52e736d4-52c1-435e-8348-ad47bff888c3" targetRef="2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8" name="No" id="2027.6318fb2f-d234-41ee-91c7-88277995bb7e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">  </ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ab19441a-98cf-437b-b393-7d7ff201c2b8" targetRef="2025.c84a35e7-96cc-4711-a32c-981658fdc905" name="To Stay on page" id="2027.b335a598-4a04-4228-ab25-c2d39c8d6ba3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.002bfd2c-87dc-4562-8338-34d9eb5dd4f8" targetRef="2025.ab19441a-98cf-437b-b393-7d7ff201c2b8" name="To Reset Pending Change Data" id="2027.ccf5d8df-def1-4369-90ac-2341680e24fb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b52bbaee-d1ab-4e38-bbcd-dc4cd3e91385" targetRef="2025.ab19441a-98cf-437b-b393-7d7ff201c2b8" name="To Stay on page" id="2027.81738a2b-8683-4133-973e-948969d87888">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.3f13390a-a0f9-46a2-975c-346df06d1fe5" targetRef="2025.52e736d4-52c1-435e-8348-ad47bff888c3" name="To Validation Error" id="2027.c789113e-5683-43bb-9695-aa607b066334">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.90f66223-13ff-4928-b3da-cfd703c93e40" targetRef="2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf" name="To Server Side Init Data" id="2027.9a647f0d-50bc-4223-bbcc-29a786e34a21">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="23feea56-a828-426c-ab06-8df2fab80089" targetRef="2025.1eee6e1d-783b-45a8-9018-9eec7e5baf74" name="To Instance present?" id="2027.fa981844-8a6f-499c-af64-9ee6c070db9a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ae26fe07-461b-40b4-8121-a3176be0c59e" targetRef="2025.ff57bca8-0d2c-4425-b320-aac418a37970" name="To End" id="2027.4aef0d98-6a61-4c40-8a5f-22c56578c96a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="3052a792-58f6-49dd-81b3-075d9f01952e">
                                        
                                        
                                        <ns3:coachEventPath>okButton</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.1eee6e1d-783b-45a8-9018-9eec7e5baf74" targetRef="2025.ae26fe07-461b-40b4-8121-a3176be0c59e" name="No" id="2027.9ac4ea8b-fa46-4259-b2c5-a2e4dab11598">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.processInstance == null</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.52e736d4-52c1-435e-8348-ad47bff888c3" targetRef="2025.d384368f-8e9d-4adc-a152-c2a7b727b6f4" name="Yes" id="2027.afe9263d-17f6-4b34-8501-9546633ed078">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length != 0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2d93145b-039c-450b-bb28-76610444859f" targetRef="2025.d384368f-8e9d-4adc-a152-c2a7b727b6f4" name="To Stay on page" id="2027.4cbdee9c-b0d4-4feb-be13-6be997632c58">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a2e0f46b-1f75-48e6-8cca-ca648ae43c63" targetRef="2025.527b8c17-c535-467e-89f7-a01de4d2ab42" name="To Client-Side Script" id="2027.36165a1a-f483-4650-a6b7-47ea1d20c120">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.323ea910-2504-4da8-a9da-9a2c076942da" targetRef="2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf" name="Automatic Refresh" id="2027.8d61d21e-cdbc-43a5-ab98-435b21554bea">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="c2c9833d-de6d-422d-a8b2-a692025d33df">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2" targetRef="2025.b52bbaee-d1ab-4e38-bbcd-dc4cd3e91385" name="Discard Local Changes" id="2027.fb249304-24b2-4229-bc05-36f2be1c4efd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.dataSectionBoundaryEventType == "DISCARD"</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2" targetRef="2025.3f13390a-a0f9-46a2-975c-346df06d1fe5" name="Save Data" id="2027.17da7434-bf9f-4fd6-bb26-c6111b1c3cc9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.dataSectionBoundaryEventType == "SAVE"</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.323ea910-2504-4da8-a9da-9a2c076942da" targetRef="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2" name="To Exclusive Gateway" id="2027.68117914-012e-40c6-b02b-13a5f78ed831">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="4a845224-59a4-4f55-ba8a-5a4596b5b238">
                                        
                                        
                                        <ns3:coachEventPath>CustomDataSection</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.323ea910-2504-4da8-a9da-9a2c076942da" targetRef="2025.97841d9d-a80b-4a4c-8cca-d580a80a8234" name="To End" id="2027.f7cd8072-6d68-4a6b-9518-f0f14808efb7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="4f45b5f8-9928-495e-9b17-0e0070e1d091">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Navigation_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.8372111e-9ea9-46a8-abf2-d3e765c610a6" name="Client-Side Script" id="2025.f8fe627b-9e0c-4ad0-ba0c-12c92d7fdf9b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="315" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0367a2e3-970f-495a-a043-1db0f3751284</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8372111e-9ea9-46a8-abf2-d3e765c610a6</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.attachment == null) {&#xD;
	tw.local.attachment = [];&#xD;
	tw.local.attachment[0] = {};&#xD;
	tw.local.attachment[0].name = "Customer Request";&#xD;
	tw.local.attachment[0].description = "Customer Request";&#xD;
	tw.local.attachment[0].arabicName = "طلب العميل" ;&#xD;
	&#xD;
	tw.local.attachment[1] = {};&#xD;
	tw.local.attachment[1].name = "Correspondent cover letter";&#xD;
	tw.local.attachment[1].description = "Correspondent cover letter";&#xD;
	tw.local.attachment[1].arabicName = "خطاب المراسل" ;&#xD;
	&#xD;
	tw.local.attachment[2] = {};&#xD;
	tw.local.attachment[2].name = "Invoice";&#xD;
	tw.local.attachment[2].description = "Invoice";&#xD;
	tw.local.attachment[2].arabicName = "فاتورة" ;&#xD;
	&#xD;
	tw.local.attachment[3] = {};&#xD;
	tw.local.attachment[3].name = "Transport document";&#xD;
	tw.local.attachment[3].description = "Transport document";&#xD;
	tw.local.attachment[3].arabicName = "مستند النقل" ;&#xD;
	&#xD;
	tw.local.attachment[4] = {};&#xD;
	tw.local.attachment[4].name = "Packing list";&#xD;
	tw.local.attachment[4].description = "Packing list";&#xD;
	tw.local.attachment[4].arabicName = "قائمة التعبئة" ;&#xD;
	&#xD;
	tw.local.attachment[5] = {};&#xD;
	tw.local.attachment[5].name = "Weight list";&#xD;
	tw.local.attachment[5].description = "Weight list"&#xD;
	tw.local.attachment[5].arabicName = "قائمة الاوزان" ;&#xD;
	&#xD;
	tw.local.attachment[6] = {};&#xD;
	tw.local.attachment[6].name = "Certificate of origin";&#xD;
	tw.local.attachment[6].description = "Certificate of origin";&#xD;
	tw.local.attachment[6].arabicName = "شهادة المنشأ" ;&#xD;
	&#xD;
	tw.local.attachment[7] = {};&#xD;
	tw.local.attachment[7].name = "Certificate of analysis";&#xD;
	tw.local.attachment[7].description = "Certificate of analysis";&#xD;
	tw.local.attachment[7].arabicName = "شهادة التحليل" ;&#xD;
	&#xD;
	tw.local.attachment[8] = {};&#xD;
	tw.local.attachment[8].name = "Inspection certificate";&#xD;
	tw.local.attachment[8].description = "Inspection certificate";&#xD;
	tw.local.attachment[8].arabicName = "شهادة التفتيش" ;&#xD;
	&#xD;
	tw.local.attachment[9] = {};&#xD;
	tw.local.attachment[9].name = "Insurance policy / certificate";&#xD;
	tw.local.attachment[9].description = "Insurance policy / certificate";&#xD;
	tw.local.attachment[9].arabicName = "شهادة / بوليصة التأمين" ;&#xD;
	&#xD;
	tw.local.attachment[10] = {};&#xD;
	tw.local.attachment[10].name = "Bill of exchange/draft";&#xD;
	tw.local.attachment[10].description = "Bill of exchange/draft";&#xD;
	tw.local.attachment[10].arabicName = "الكمبيالة" ;&#xD;
	&#xD;
	tw.local.attachment[11] = {};&#xD;
	tw.local.attachment[11].name = "Compliance ticket";&#xD;
	tw.local.attachment[11].description = "Compliance ticket";&#xD;
	tw.local.attachment[11].arabicName = "موافقة الإلتزام" ;&#xD;
	&#xD;
	tw.local.attachment[12] = {};&#xD;
	tw.local.attachment[12].name = "Form 4";&#xD;
	tw.local.attachment[12].description = "Form 4";&#xD;
	tw.local.attachment[12].arabicName = "نموذج 4 للمستوردين" ;&#xD;
	&#xD;
	tw.local.attachment[13] = {};&#xD;
	tw.local.attachment[13].name = "Customs Letter";&#xD;
	tw.local.attachment[13].description = "Customs Letter";&#xD;
	tw.local.attachment[13].arabicName = "خطاب الجمارك" ;&#xD;
tw.local.idcRequest = {};&#xD;
&#xD;
tw.local.idcRequest.countryOfOrigin = {};&#xD;
&#xD;
tw.local.idcRequest.appInfo = {};&#xD;
&#xD;
tw.local.idcRequest.appInfo.branch = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.HSProduct = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.incoterms = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.CBECommodityClassification = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.shipmentMethod = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.paymentTerms = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.financialDetails.usedAdvancePayment = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.beneficiaryDetails = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.beneficiaryDetails.country = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.executionHub = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.documentCurrency = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.sourceOfFunds = {};&#xD;
&#xD;
tw.local.idcRequest.IDCRequestType = {};&#xD;
&#xD;
tw.local.idcRequest.billOfLading = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.importPurpose = {};&#xD;
tw.local.idcRequest.IDCRequestNature = {};&#xD;
&#xD;
&#xD;
tw.local.idcRequest.customerInformation = {};&#xD;
&#xD;
tw.local.idcRequest.invoices = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.productCategory = {};&#xD;
tw.local.idcRequest.documentsSource = {};&#xD;
&#xD;
tw.local.idcRequest.paymentTerms = {};&#xD;
&#xD;
tw.local.idcRequest.approvals = {};&#xD;
&#xD;
tw.local.idcRequest.appLog = {};&#xD;
&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f8fe627b-9e0c-4ad0-ba0c-12c92d7fdf9b" targetRef="2025.90f66223-13ff-4928-b3da-cfd703c93e40" name="To Client-Side Script" id="2027.8372111e-9ea9-46a8-abf2-d3e765c610a6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="With Attachment View Instance Details" id="2025.5ac444a8-f966-439c-8761-b54731f0b4f9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="974" y="-87" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.330b0f0e-33f8-463e-b7a4-1174eb0953b5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7269ac2e-918a-4fbe-ae75-b304e2c53b13</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.7a36570b-7852-4c7f-afdf-5f8c5de896d3</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.29f3cfd6-ed46-4d21-8511-4c3a8df78b60</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>6d40d66b-ee05-4044-8ade-b213a310d38f</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Default_Instance_Details_Template1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6bb52122-bbbe-4e2b-88c0-10d1ce69519a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Default Instance Details Template</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6bc6023a-f319-4cad-848e-6db8befff612</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b2c4d491-3789-4c77-8b52-ac7c15e10f32</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a8458e0d-f0eb-4c9a-86fe-8f3c671b2ca2</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesTextFilter</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesTextFilter</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9eb91e7e-d4d6-4e5a-8b85-42fc910fd987</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>addButtonDropdownSelectedItem</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>442fe2dd-3d20-4fd0-88c4-c9c3842caa43</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.breadcrumbs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>943deb11-cca3-43b9-80da-cd9071dd365b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>37d667ad-de89-4b31-8d18-3368e6fefcca</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonClicked_GanttView</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.buttonClicked_GanttView</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>47b6b9f8-605c-4d82-8063-7e4fd46ee968</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentPage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f368d64e-cbc8-41d9-8cb7-7ca8aea28e13</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentAddButtonDropdownList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7d52f37e-fd26-485d-85b0-d78bb6340a1d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>follow</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.follow</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d2fede42-9d24-4df3-810c-e2dde4079cb0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceName</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>db4ee03c-d2c9-49db-8b89-0a240ccb3d8b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceSummary</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceSummary</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9e926513-9fca-4a1b-872c-c68c268c27fc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f773d12d-6063-4763-8665-456217c27aa3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedActivitiesCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedActivitiesCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7a65ee97-c4f2-4352-8c8e-7d04e0a7ba31</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedInstanceId</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2a452fec-e656-4259-8673-0eb44c91d479</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedTasksCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedTasksCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7901c347-cd46-4bf1-8fc8-0a97482b90f5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tasksCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.tasksCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>22dd432a-b008-4fb5-836c-c7c3dddc1c3d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentDate</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.currentDate</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fc0b1ca5-b4c9-452a-895e-66e3d98314a8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hideDocumentSection</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hideDocumentSection</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9fdf0efe-5a33-4238-8b53-2d8411fa6b83</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>refreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d732ac5b-0e12-4591-8c47-69aab9dd2977</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>manualRefreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.manualRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a23a582f-8eac-412a-8cc3-2da088e37ff5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>canViewDiagram</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.canViewDiagram</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>124656cc-5105-4197-8f3c-7c1804dd6235</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>navigationURL</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.navigationURL</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>34a7fafe-4c08-40f8-8fb9-c7ce3a1fa5d3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ce13fed7-3342-478e-8e84-cac7b3ad1a3c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>preventBoundaryEvent</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8440f952-9342-4749-86f0-86235a5bdb9f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentSectionVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>21da54fa-4530-447d-83c9-034ed9be2cbe</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unSavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bff8d14b-9495-41e5-8e38-c8eac3229413</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedProcessInstanceChangesMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.resource.Dashboards.control.datasection.msg.unsaveddatanavigation</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.5f0e6839-f7f9-41a3-b633-541ba91f9b31</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>ca158dc7-4aac-4e47-8cb2-73984f03a492</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>09103815-dfd6-4f33-8607-0561014ce771</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>CustomDataSection</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>bd65e501-38f8-4fde-8cab-e192dcd78151</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Data Section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>fd80b6db-98ec-4d73-845c-26c2c323bebf</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>cd094414-6f91-42c3-873b-516b5aa8f244</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Show</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>3f577ce5-e734-4305-8e85-3dcd516b7311</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceId</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b70997b1-1d58-45f6-8812-f87bb46f822c</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>refreshTrigger</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>58304228-fc82-4621-8465-7b012051b5f1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedUpdateMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>9eed4b35-cbb1-44ea-8882-2f9bc6bf1b2b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedSaveMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4d70cc26-39f6-4b64-817e-a7ab51d1af92</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsible</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>af8e8ebe-ecb6-4425-8eee-905babd83453</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceStatus</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>completed</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c5733400-8d25-4fc7-851c-579a1712be3e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>boundaryEventType</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>"DISCARD"</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ed8c74ff-c13c-4277-8f94-2526690d862f</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>8eccb4b0-e9aa-421c-851f-2b8481757d9b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>localChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.localChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d1a9b5eb-9032-4f81-8317-3fc5992b2b54</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChangesMerged</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChangesMerged</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>84b7217d-928a-4b1e-8e7c-55704c085ee1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>234b3582-ff33-4920-82aa-971d2fd8c304</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>originalInput</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.originalInput</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4afc566f-0a85-40df-8f6f-24fec59809e0</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>editMode</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>7980e29e-4124-4118-8206-c0957b383690</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsed</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.0678c7bb-7028-4bca-8111-e0e9977f294d</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding />
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>0a5b08da-8634-4b1a-8b55-d76e254a8930</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>9b77cf0a-410c-45fe-8e2d-db887240125a</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>331aca27-c535-454e-8b5b-865a19fef40f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Tab section</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>228ac7c0-ad3b-452c-8977-09edc25ebe2c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c184f0f5-2b50-4251-875f-277b6ad22c2d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:contentBoxContrib>
                                                                    
                                                                    
                                                                    <ns19:id>40a1dc59-38cf-4414-83c1-bf1774bebf7f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>91c51c2d-a3cd-4e8d-89cf-d9d7ebd38723</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>3587380f-6811-476c-8ff2-9b781f77da6f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Customer Information</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>81260423-9338-481a-865c-d4a8d6512006</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>bcbeb859-fb9b-4922-8c2a-eaf60abf219a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>69d464be-86f6-4fcb-8708-f7b0a1342c21</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>11ee9b0f-c41b-4ae9-8d79-2f11d8262a82</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f797224b-7a6a-4e12-8b34-d80d55f4af49</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>instanceview</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>1f505734-0989-459d-898e-cb1e0fb27f3f</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>d116d48e-0b0e-49ab-820d-5b30871f499c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Basic Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>85cf0c81-f35b-43e3-8bcd-f25a0f25234f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>eff90776-6c76-4519-875e-0889ba571e22</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>22fe86bf-efd7-4cee-8a67-9c6e652958e1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b61d9e52-ced5-4a3b-8ef3-fe8ce6156a2c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7e7d08db-9d4a-4813-8aa9-bc13256140a5</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bec7782b-c964-4a09-b74f-0ec737efa310</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>48b01f20-52c6-43f8-8032-fc14afd2d9da</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details__Branch_Compliance_Review1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>dcd28ec6-6235-4622-8650-12d1830fcc6e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Branch</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>79edc659-54b9-495c-8604-54566be1e872</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>eda95500-b3f3-4c6b-86b6-76b1b500b200</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ad14872-c2f9-45fa-81fc-c573551b10cc</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.7020f6c3-7f8d-4052-81e6-d5b6b89f97e9</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>f4e5f6a0-1699-4b6e-8bf7-7303c5289fe7</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details_Trade_FO1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>877ce0d3-34e5-44ed-8e79-513b20eab074</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Trade FO</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>c3e4779a-3b93-47b1-80a4-4331ee65df6a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>55ea59b6-04b5-477e-8a66-05f688c2284c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>876fc54b-3356-4a79-813b-d5eaf73c510a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>333d1b41-e335-46cb-8fb4-8d1f23417b3f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerms</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>129cdc90-0391-47b6-8e81-89dfc27df67e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>requestType</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7709013e-148a-4cb1-8517-8e2d71b76620</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>beneficiaryDetails</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.financialDetails.beneficiaryDetails</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>15616d3e-d8b0-4e38-808b-6adaf55a411a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>haveTradeFOReferenceNumber</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.848ab487-8214-4d8b-88fd-a9cac5257791</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>22c75529-8c9c-4cb2-8f87-cae369e70a75</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Products_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b5e9f407-b9c4-42cb-8453-e690928932de</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Products Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>828e66a8-1b67-4416-8c0c-9c2bee5900f2</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0bfc1ed3-7ec4-4336-8629-f446eec5d58c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>03b54376-838a-40dd-860f-3cecff4ced15</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bd0ada34-acf3-449a-91df-9aa363c2b280</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.productsDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c31ac594-e3dd-43bf-8e25-4dfddd9fe5c0</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>31630e65-71c8-4b3a-888d-80b0838577d5</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Attachment</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0663c93a-2edd-4956-8d58-6bab8317bca1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>10f054dd-d5c6-428c-880d-c2ba0e708687</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>669f09e1-2230-4e95-82a8-8d4c9a9a04b4</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>9154cbd2-dd1e-43ad-8b33-1e4e591f07de</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>visiable</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>94c4354d-a744-40d5-8a8a-5821637167da</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canDelete</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>dfff13cc-419e-47fa-84d0-091584bd19a3</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canCreate</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ca8bc39-dba9-43a7-8077-f2ee37fb4d98</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canUpdate</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>c8ac15be-97ca-4ad1-8563-ddb4cfecd1ba</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>ECMproperties</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c252cf11-ab75-494d-8770-8dfc6da1c0a3</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f248a611-0847-4f2f-81a4-0f57c20b5737</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>History</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b1cdfa44-38aa-4496-854c-4cf81ef319a6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>307b63b7-93fa-437a-81bc-c4047526108e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0fa431b6-dc79-454d-81d8-b9445146a1f9</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                
                                                                </ns19:contentBoxContrib>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5ac444a8-f966-439c-8761-b54731f0b4f9" targetRef="2025.f7f44c64-64f7-4310-9c05-18e1087e99b2" name="Copy of To Exclusive Gateway" id="2027.7269ac2e-918a-4fbe-ae75-b304e2c53b13">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns3:coachEventBinding id="6e3659d2-4ada-48ef-bdae-5fed72d646c8">
                                        
                                        
                                        <ns3:coachEventPath>CustomDataSection</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5ac444a8-f966-439c-8761-b54731f0b4f9" targetRef="2025.46da1cd7-37ac-4780-9fd8-35ba658ffbaf" name="Copy of Automatic Refresh" id="2027.7a36570b-7852-4c7f-afdf-5f8c5de896d3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns3:coachEventBinding id="4a645fc7-a60f-414c-b1f2-efa9c655eabf">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5ac444a8-f966-439c-8761-b54731f0b4f9" targetRef="2025.97841d9d-a80b-4a4c-8cca-d580a80a8234" name="Copy of To End" id="2027.29f3cfd6-ed46-4d21-8511-4c3a8df78b60">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns3:coachEventBinding id="733d6b7c-795b-4633-ace7-5e713f38ce07">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Navigation_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.330b0f0e-33f8-463e-b7a4-1174eb0953b5" name="Exclusive Gateway" id="2025.e6b23f0e-c326-4be4-9dc0-b4f8abc5180c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="843" y="103" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.444c5d1c-2de8-450f-a1ae-9c0be94ccd35</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.74f5145f-7359-4997-8f6c-7e7fab515db1</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.330b0f0e-33f8-463e-b7a4-1174eb0953b5</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e6b23f0e-c326-4be4-9dc0-b4f8abc5180c" targetRef="2025.323ea910-2504-4da8-a9da-9a2c076942da" name="To View Instance Details" id="2027.74f5145f-7359-4997-8f6c-7e7fab515db1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.idcRequest.appInfo.instanceID=="" || tw.local.idcRequest.appInfo.instanceID==null</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e6b23f0e-c326-4be4-9dc0-b4f8abc5180c" targetRef="2025.5ac444a8-f966-439c-8761-b54731f0b4f9" name="To With Attachment View Instance Details" id="2027.330b0f0e-33f8-463e-b7a4-1174eb0953b5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="havePaymentTerm" id="2056.b43ea862-862a-4ded-9c71-c4bb9f57c15f" />
                            
                            
                            <ns3:htmlHeaderTag id="505ad95a-4710-4433-b4f8-2f621b19bd84">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>InstanceDetailsUI</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyInputs="false" ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.4b698a84-427b-4801-9c1d-18ddcc561bc6</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.46923c36-435d-480a-8627-cca9772b6a02</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.690dcf77-98e6-47a3-a4d4-f4baf4c88a0c" />
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.d129b7b6-2145-48d6-8bc5-327ecfc2d798" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.e644912b-b285-47fb-b611-1d4616436c0d" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9148cae9-4c1b-403e-8473-79a5097ba1e2</processLinkId>
            <processId>1.80b52f2e-cf28-4943-b3bd-447b9230d1db</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5b0cb366-e3e1-4b8b-9ee9-8a0b800d2845</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4f8208a0-2642-47b8-bcab-b11153c577f1</toProcessItemId>
            <guid>2cfbbb10-78bc-4108-b30b-c2352400adad</guid>
            <versionId>098eb7f9-36f2-4505-905b-f5961c982829</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.5b0cb366-e3e1-4b8b-9ee9-8a0b800d2845</fromProcessItemId>
            <toProcessItemId>2025.4f8208a0-2642-47b8-bcab-b11153c577f1</toProcessItemId>
        </link>
    </process>
</teamworks>

