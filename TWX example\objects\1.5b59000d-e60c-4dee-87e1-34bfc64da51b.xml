<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.5b59000d-e60c-4dee-87e1-34bfc64da51b" name="Check Invoice">
        <lastModified>1692505153926</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>960368b9-7e35-4249-bfa4-535f3ef5971e</guid>
        <versionId>5e845de4-135d-454c-bd98-e073e2c3c138</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d67" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f8805b70-6005-413e-9559-d52e82a0667d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"23d06f63-15bc-4684-8032-385bcceca8f0"},{"incoming":["710bae78-7e76-4ae6-9392-89f02ee48401","35f34226-9d0f-45ce-8197-4d72feda5ee4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:7796"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"31bae867-392e-4426-a6e4-8dc68b0ae965"},{"targetRef":"bc5e2625-fbc6-4d23-8480-02027b6ddb2f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"2027.f8805b70-6005-413e-9559-d52e82a0667d","sourceRef":"23d06f63-15bc-4684-8032-385bcceca8f0"},{"startQuantity":1,"outgoing":["03babc29-4e0e-40db-967f-8a70f66c966e"],"incoming":["3a92ac51-8633-4e85-a269-8fbff2578454"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":207,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8a48b78c-ff3c-4e5a-98bc-b9c1a390788b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_INVOICES WHERE INVOICE_NUMBER = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');\";\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.parameters[0]=new tw.object.SQLParameter();\r\ntw.local.parameters[1]=new tw.object.SQLParameter();\r\ntw.local.parameters[0].value=tw.local.billNumber;\r\ntw.local.parameters[0].type = \"VARCHAR\";\r\ntw.local.parameters[1].value=tw.local.requestID;\r\ntw.local.parameters[1].type = \"INTEGER\";"]}},{"targetRef":"8761667c-13e4-409f-a9fc-30d463a5f396","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"03babc29-4e0e-40db-967f-8a70f66c966e","sourceRef":"8a48b78c-ff3c-4e5a-98bc-b9c1a390788b"},{"startQuantity":1,"outgoing":["98f980ae-b2dd-4428-86d5-f8648c01ce4d"],"incoming":["03babc29-4e0e-40db-967f-8a70f66c966e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.parameters"]}}]},{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8761667c-13e4-409f-a9fc-30d463a5f396","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.bills"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"0284bb5b-2792-4b1d-a848-53243a9d81bb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Output Mapping","declaredType":"sequenceFlow","id":"98f980ae-b2dd-4428-86d5-f8648c01ce4d","sourceRef":"8761667c-13e4-409f-a9fc-30d463a5f396"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.a61d8394-3a5a-4508-9c18-3dcdb650a19d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.e8486019-5986-494a-936b-ea119fd7a60f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"bills","isCollection":true,"declaredType":"dataObject","id":"2056.ca8ca606-5cb3-4c5a-a81f-2eecab2845ff"},{"startQuantity":1,"outgoing":["3a92ac51-8633-4e85-a269-8fbff2578454"],"incoming":["2027.f8805b70-6005-413e-9559-d52e82a0667d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":82,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"bc5e2625-fbc6-4d23-8480-02027b6ddb2f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.Seperated = new tw.object.listOf.String();\r\ntw.local.Seperated = tw.local.data.split(\"-\");\r\ntw.local.requestID = tw.local.Seperated[0];\r\ntw.local.billNumber = tw.local.Seperated[1];\r\n"]}},{"targetRef":"8a48b78c-ff3c-4e5a-98bc-b9c1a390788b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"3a92ac51-8633-4e85-a269-8fbff2578454","sourceRef":"bc5e2625-fbc6-4d23-8480-02027b6ddb2f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.e70b1021-366f-49dd-b918-ddddb25d7477"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.e1a995fb-5d2b-4e5e-a146-3da549c50774"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"billNumber","isCollection":false,"declaredType":"dataObject","id":"2056.a24c91a7-d7b9-44d4-a639-8a85075a1692"},{"startQuantity":1,"outgoing":["710bae78-7e76-4ae6-9392-89f02ee48401"],"incoming":["98f980ae-b2dd-4428-86d5-f8648c01ce4d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":509,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Output Mapping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0284bb5b-2792-4b1d-a848-53243a9d81bb","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.bills[0].rows.listLength&gt;0) {\r\n\ttw.local.results = 1;\r\n}\r\nelse{\r\n\ttw.local.results = 0;\r\n}"]}},{"targetRef":"31bae867-392e-4426-a6e4-8dc68b0ae965","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"710bae78-7e76-4ae6-9392-89f02ee48401","sourceRef":"0284bb5b-2792-4b1d-a848-53243a9d81bb"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.13d3f706-502b-499a-82dd-95e246e8ab48"},{"parallelMultiple":false,"outgoing":["1ce883a9-dbf9-44a3-8e12-fdb259f2a538"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a77f4c27-78a3-4a95-80e7-86fefac4c245"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3a11f1ad-5b35-4c53-890c-5c90a93f91b6","otherAttributes":{"eventImplId":"6fabd3df-2b59-43db-88a2-eff8346a9b4f"}}],"attachedToRef":"bc5e2625-fbc6-4d23-8480-02027b6ddb2f","extensionElements":{"nodeVisualInfo":[{"width":24,"x":117,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1f683527-f1a4-4fb5-81d9-9f9e951dba8a","outputSet":{}},{"parallelMultiple":false,"outgoing":["c20a130b-e984-40e4-8327-01f1743e1c9d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"aa3c277a-cad7-4044-85c1-58924e23a825"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"2c792ffd-7bd3-4d80-89d5-9b7c407bb1a4","otherAttributes":{"eventImplId":"0d4fb349-573c-4c34-8827-32a44f4db820"}}],"attachedToRef":"8a48b78c-ff3c-4e5a-98bc-b9c1a390788b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":242,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"e1285f4c-166a-4d70-8580-0b4542c6fc02","outputSet":{}},{"parallelMultiple":false,"outgoing":["2f517510-a94d-4de4-8a3a-d678663c7eea"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"597ee65e-d913-4918-894e-1d32de4e383c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6fd13d92-8abd-466f-8024-ef6ac4f5383c","otherAttributes":{"eventImplId":"56d0a384-703a-4bc4-85c2-4be6d8a3a8fa"}}],"attachedToRef":"8761667c-13e4-409f-a9fc-30d463a5f396","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"670dbcd4-3c2e-4707-8a78-50420c002894","outputSet":{}},{"parallelMultiple":false,"outgoing":["d5f8aa13-73ac-4380-8956-219e4f9ca0aa"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"aa4886ed-bccd-45ef-88f5-d6dc6e17d954"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c6311bca-a6c5-4668-85e8-fe2a88cb18b7","otherAttributes":{"eventImplId":"f2832a1f-a15a-4db6-8b5d-3491de4f32c8"}}],"attachedToRef":"0284bb5b-2792-4b1d-a848-53243a9d81bb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":544,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"76191766-a0c8-4011-8dc7-b1d6e103b6f8","outputSet":{}},{"startQuantity":1,"outgoing":["35f34226-9d0f-45ce-8197-4d72feda5ee4"],"incoming":["1ce883a9-dbf9-44a3-8e12-fdb259f2a538","c20a130b-e984-40e4-8327-01f1743e1c9d","2f517510-a94d-4de4-8a3a-d678663c7eea","d5f8aa13-73ac-4380-8956-219e4f9ca0aa"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":281,"y":188,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"64d91e47-8aed-42e3-8e9f-6d41194f03ba","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"64d91e47-8aed-42e3-8e9f-6d41194f03ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"1ce883a9-dbf9-44a3-8e12-fdb259f2a538","sourceRef":"1f683527-f1a4-4fb5-81d9-9f9e951dba8a"},{"targetRef":"64d91e47-8aed-42e3-8e9f-6d41194f03ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c20a130b-e984-40e4-8327-01f1743e1c9d","sourceRef":"e1285f4c-166a-4d70-8580-0b4542c6fc02"},{"targetRef":"64d91e47-8aed-42e3-8e9f-6d41194f03ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"2f517510-a94d-4de4-8a3a-d678663c7eea","sourceRef":"670dbcd4-3c2e-4707-8a78-50420c002894"},{"targetRef":"64d91e47-8aed-42e3-8e9f-6d41194f03ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"d5f8aa13-73ac-4380-8956-219e4f9ca0aa","sourceRef":"76191766-a0c8-4011-8dc7-b1d6e103b6f8"},{"targetRef":"31bae867-392e-4426-a6e4-8dc68b0ae965","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"35f34226-9d0f-45ce-8197-4d72feda5ee4","sourceRef":"64d91e47-8aed-42e3-8e9f-6d41194f03ba"}],"laneSet":[{"id":"aedfd189-7a3e-4ea0-ba03-df40a828db3e","lane":[{"flowNodeRef":["23d06f63-15bc-4684-8032-385bcceca8f0","31bae867-392e-4426-a6e4-8dc68b0ae965","8a48b78c-ff3c-4e5a-98bc-b9c1a390788b","8761667c-13e4-409f-a9fc-30d463a5f396","bc5e2625-fbc6-4d23-8480-02027b6ddb2f","0284bb5b-2792-4b1d-a848-53243a9d81bb","1f683527-f1a4-4fb5-81d9-9f9e951dba8a","e1285f4c-166a-4d70-8580-0b4542c6fc02","670dbcd4-3c2e-4707-8a78-50420c002894","76191766-a0c8-4011-8dc7-b1d6e103b6f8","64d91e47-8aed-42e3-8e9f-6d41194f03ba"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"88eeb72e-36e5-4b00-912e-84f467f1a4a1","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Check Invoice","declaredType":"process","id":"1.5b59000d-e60c-4dee-87e1-34bfc64da51b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.c8576c7b-e89f-4709-9555-f9c3d1707da8"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.2dbf9d00-9ab7-4c98-ab4f-a484ad6375aa"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"22-22\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.dce5c8f5-da8d-47b7-8934-c547db19ddf4"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dce5c8f5-da8d-47b7-8934-c547db19ddf4</processParameterId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"22-22"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>974828ec-f6ed-4276-84da-5aa1f898766d</guid>
            <versionId>a0359ca5-8ac2-4902-b36a-************</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c8576c7b-e89f-4709-9555-f9c3d1707da8</processParameterId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a87a0baf-9daa-4716-8a07-ea3c0a778b06</guid>
            <versionId>808d8148-f7b5-445c-8a2c-65ce43568930</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2dbf9d00-9ab7-4c98-ab4f-a484ad6375aa</processParameterId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f04e058c-3004-4921-91b1-c66fc9aad882</guid>
            <versionId>46211533-343c-444a-92a2-6a8398a727eb</versionId>
        </processParameter>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a61d8394-3a5a-4508-9c18-3dcdb650a19d</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3795edf8-68e5-4871-bc31-666d7634c094</guid>
            <versionId>6d806d41-8161-489a-bfa2-bfe94d12d746</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e8486019-5986-494a-936b-ea119fd7a60f</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5fa3c378-266c-4b4e-a79d-2c2f73341166</guid>
            <versionId>7b782b6d-5a5f-4908-93f5-4f0587a44769</versionId>
        </processVariable>
        <processVariable name="bills">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ca8ca606-5cb3-4c5a-a81f-2eecab2845ff</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ec2bd14-9034-4069-963e-ffbeab9fd27e</guid>
            <versionId>a8d6eef4-eb3d-40d4-b815-9e357b9a5fdf</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e70b1021-366f-49dd-b918-ddddb25d7477</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>266afd48-e6d2-44ca-a342-961ee3065684</guid>
            <versionId>ac8ce699-4beb-4f25-9469-cc1e6cf952d2</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e1a995fb-5d2b-4e5e-a146-3da549c50774</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1adc916a-a5dd-4fe6-b13b-82a311c3de86</guid>
            <versionId>b2551b8c-6fd7-4130-b093-610bfdc822ac</versionId>
        </processVariable>
        <processVariable name="billNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a24c91a7-d7b9-44d4-a639-8a85075a1692</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5c7d301e-2540-44e0-b6fc-fda2026d96bc</guid>
            <versionId>6fdbdf45-1052-4f84-820d-dd7f1d57a8bd</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.13d3f706-502b-499a-82dd-95e246e8ab48</processVariableId>
            <description isNull="true" />
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>48dc4c5f-ea7c-4f69-b046-25ddd22fd1d6</guid>
            <versionId>531d52d1-6890-4b82-a4a7-d0a5dd69cc91</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bf530dce-8457-4145-b5fb-0f013a070871</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7794</guid>
            <versionId>2a7de0d6-018f-4248-ae26-75befea86c12</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e14b200c-6135-4373-8baa-7588e937edfa</processItemPrePostId>
                <processItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>9b2c15d1-2a8a-4ac1-9db5-30e60d86b987</guid>
                <versionId>fba6a664-0ccf-4763-b750-fa8a530fd4ff</versionId>
            </processPrePosts>
            <layoutData x="82" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d66</errorHandlerItem>
                <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bf530dce-8457-4145-b5fb-0f013a070871</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.billNumber = tw.local.Seperated[1];&#xD;
</script>
                <isRule>false</isRule>
                <guid>dfc9e6ab-6277-46d1-b535-cc7ae472e230</guid>
                <versionId>f1511f00-c91e-4ded-a1b0-53749d3bbe8c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.31bae867-392e-4426-a6e4-8dc68b0ae965</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c312827d-e366-4e06-a741-5a54314ffb7d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7796</guid>
            <versionId>40df1a06-8dba-42c3-83a2-862b6eb2fa56</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c312827d-e366-4e06-a741-5a54314ffb7d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>d462c95f-235e-48d9-89ad-11a8fc74866a</guid>
                <versionId>6171be38-f6c0-46a8-90d7-88fbd0b5637d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0284bb5b-2792-4b1d-a848-53243a9d81bb</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>Output Mapping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4ec1ee9f-cf3c-4369-be92-1b454507cbea</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7798</guid>
            <versionId>becf151c-ec57-4507-a720-d0166f755b2f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="509" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d66</errorHandlerItem>
                <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4ec1ee9f-cf3c-4369-be92-1b454507cbea</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.bills[0].rows.listLength&gt;0) {&#xD;
	tw.local.results = 1;&#xD;
}&#xD;
else{&#xD;
	tw.local.results = 0;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>73e5df18-9590-4aa9-9591-ca0f094706da</guid>
                <versionId>e360372a-870d-4692-b6b2-b7a4932c8ce9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8761667c-13e4-409f-a9fc-30d463a5f396</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7795</guid>
            <versionId>ccd3d9ce-0093-4a3b-b680-5f6d49b38387</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d66</errorHandlerItem>
                <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>051372e5-529a-4e6c-bd00-8dc437844dcb</guid>
                <versionId>3e1fe7a1-70de-472f-9248-9032d8324bd5</versionId>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2d698872-8db0-4b7e-82af-ce8617d40e28</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>67345aef-c212-4de6-9d85-ce7f482e64e5</guid>
                    <versionId>2598702d-5d68-4d56-8527-c4deaa491f43</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8937dfb3-a968-4fef-9e36-82aa61d6abbc</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6a416389-176f-4401-aa68-4c3652186ec6</guid>
                    <versionId>31d10d52-fcc8-4e6a-b9f1-0c875c15d01e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3540d388-6585-4134-80f6-ec9f86f9bc69</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>2885e66f-25ee-44b4-80c0-dd733528aa4f</guid>
                    <versionId>84f33edf-aefd-445e-a987-f15c44d91045</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2d7b2b56-17cf-4649-85b0-efb09a4104bd</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>976add64-624a-4395-b025-b54777f70880</guid>
                    <versionId>bea868c8-bccf-455c-970d-3849c92fd062</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e69061d1-671c-45f7-a512-8f57dbe676f5</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.bills</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7406955e-685f-455f-92b0-7618235e5e96</guid>
                    <versionId>c7c8295c-6ca2-4bc8-ba34-2e5f1a47c81b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dcd41924-105b-4233-af7e-31a164052892</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.8eccd872-2a1d-4632-b91b-f931eb7072cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8a024551-4ed3-4d56-ad61-79dce41891ec</guid>
                    <versionId>e86a65af-d834-4aa1-a9b9-4258bc6625d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>Init SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.81a57fdd-905a-4e3a-b79a-3cfc0f776f3c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7797</guid>
            <versionId>deb08a3b-e737-4c3b-abc1-ed0ff8b68f00</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="207" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d66</errorHandlerItem>
                <errorHandlerItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.81a57fdd-905a-4e3a-b79a-3cfc0f776f3c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_INVOICES WHERE INVOICE_NUMBER = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value=tw.local.billNumber;&#xD;
tw.local.parameters[0].type = "VARCHAR";&#xD;
tw.local.parameters[1].value=tw.local.requestID;&#xD;
tw.local.parameters[1].type = "INTEGER";</script>
                <isRule>false</isRule>
                <guid>5dac10f2-2384-46fa-aa12-281965028fb7</guid>
                <versionId>4526a1e9-35fe-49a1-87ff-774a62bf2150</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</processItemId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.686962bb-05c3-4c92-8e5d-79ae8e67cac4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d66</guid>
            <versionId>e76c733e-243c-4d10-a4e1-4f566f82c756</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="281" y="188">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.686962bb-05c3-4c92-8e5d-79ae8e67cac4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>23ae9eb3-f9ac-4e12-8695-8754d5f228a3</guid>
                <versionId>9ba9e27d-ee80-4e92-b17f-4fbcbf00f96b</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Invoice" id="1.5b59000d-e60c-4dee-87e1-34bfc64da51b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.dce5c8f5-da8d-47b7-8934-c547db19ddf4">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"22-22"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.c8576c7b-e89f-4709-9555-f9c3d1707da8" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.2dbf9d00-9ab7-4c98-ab4f-a484ad6375aa" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="aedfd189-7a3e-4ea0-ba03-df40a828db3e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="88eeb72e-36e5-4b00-912e-84f467f1a4a1" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>23d06f63-15bc-4684-8032-385bcceca8f0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>31bae867-392e-4426-a6e4-8dc68b0ae965</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8761667c-13e4-409f-a9fc-30d463a5f396</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bc5e2625-fbc6-4d23-8480-02027b6ddb2f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0284bb5b-2792-4b1d-a848-53243a9d81bb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1f683527-f1a4-4fb5-81d9-9f9e951dba8a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e1285f4c-166a-4d70-8580-0b4542c6fc02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>670dbcd4-3c2e-4707-8a78-50420c002894</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>76191766-a0c8-4011-8dc7-b1d6e103b6f8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>64d91e47-8aed-42e3-8e9f-6d41194f03ba</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="23d06f63-15bc-4684-8032-385bcceca8f0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.f8805b70-6005-413e-9559-d52e82a0667d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="31bae867-392e-4426-a6e4-8dc68b0ae965">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:7796</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>710bae78-7e76-4ae6-9392-89f02ee48401</ns16:incoming>
                        
                        
                        <ns16:incoming>35f34226-9d0f-45ce-8197-4d72feda5ee4</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="23d06f63-15bc-4684-8032-385bcceca8f0" targetRef="bc5e2625-fbc6-4d23-8480-02027b6ddb2f" name="To Init SQL Query" id="2027.f8805b70-6005-413e-9559-d52e82a0667d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL Query" id="8a48b78c-ff3c-4e5a-98bc-b9c1a390788b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="207" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3a92ac51-8633-4e85-a269-8fbff2578454</ns16:incoming>
                        
                        
                        <ns16:outgoing>03babc29-4e0e-40db-967f-8a70f66c966e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_INVOICES WHERE INVOICE_NUMBER = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value=tw.local.billNumber;&#xD;
tw.local.parameters[0].type = "VARCHAR";&#xD;
tw.local.parameters[1].value=tw.local.requestID;&#xD;
tw.local.parameters[1].type = "INTEGER";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8a48b78c-ff3c-4e5a-98bc-b9c1a390788b" targetRef="8761667c-13e4-409f-a9fc-30d463a5f396" name="To SQL Execute Statement" id="03babc29-4e0e-40db-967f-8a70f66c966e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="8761667c-13e4-409f-a9fc-30d463a5f396">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>03babc29-4e0e-40db-967f-8a70f66c966e</ns16:incoming>
                        
                        
                        <ns16:outgoing>98f980ae-b2dd-4428-86d5-f8648c01ce4d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.bills</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8761667c-13e4-409f-a9fc-30d463a5f396" targetRef="0284bb5b-2792-4b1d-a848-53243a9d81bb" name="To Output Mapping" id="98f980ae-b2dd-4428-86d5-f8648c01ce4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.a61d8394-3a5a-4508-9c18-3dcdb650a19d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.e8486019-5986-494a-936b-ea119fd7a60f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="bills" id="2056.ca8ca606-5cb3-4c5a-a81f-2eecab2845ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false" />
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="bc5e2625-fbc6-4d23-8480-02027b6ddb2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="82" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.f8805b70-6005-413e-9559-d52e82a0667d</ns16:incoming>
                        
                        
                        <ns16:outgoing>3a92ac51-8633-4e85-a269-8fbff2578454</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.billNumber = tw.local.Seperated[1];&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="bc5e2625-fbc6-4d23-8480-02027b6ddb2f" targetRef="8a48b78c-ff3c-4e5a-98bc-b9c1a390788b" name="To Init SQL Query" id="3a92ac51-8633-4e85-a269-8fbff2578454">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.e70b1021-366f-49dd-b918-ddddb25d7477" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestID" id="2056.e1a995fb-5d2b-4e5e-a146-3da549c50774" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="billNumber" id="2056.a24c91a7-d7b9-44d4-a639-8a85075a1692" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Output Mapping" id="0284bb5b-2792-4b1d-a848-53243a9d81bb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="509" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>98f980ae-b2dd-4428-86d5-f8648c01ce4d</ns16:incoming>
                        
                        
                        <ns16:outgoing>710bae78-7e76-4ae6-9392-89f02ee48401</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.bills[0].rows.listLength&gt;0) {&#xD;
	tw.local.results = 1;&#xD;
}&#xD;
else{&#xD;
	tw.local.results = 0;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0284bb5b-2792-4b1d-a848-53243a9d81bb" targetRef="31bae867-392e-4426-a6e4-8dc68b0ae965" name="To SQL Execute Multiple Statements (SQLResult)" id="710bae78-7e76-4ae6-9392-89f02ee48401">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.13d3f706-502b-499a-82dd-95e246e8ab48" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="bc5e2625-fbc6-4d23-8480-02027b6ddb2f" parallelMultiple="false" name="Error" id="1f683527-f1a4-4fb5-81d9-9f9e951dba8a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="117" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1ce883a9-dbf9-44a3-8e12-fdb259f2a538</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a77f4c27-78a3-4a95-80e7-86fefac4c245" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3a11f1ad-5b35-4c53-890c-5c90a93f91b6" eventImplId="6fabd3df-2b59-43db-88a2-eff8346a9b4f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8a48b78c-ff3c-4e5a-98bc-b9c1a390788b" parallelMultiple="false" name="Error1" id="e1285f4c-166a-4d70-8580-0b4542c6fc02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="242" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c20a130b-e984-40e4-8327-01f1743e1c9d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="aa3c277a-cad7-4044-85c1-58924e23a825" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="2c792ffd-7bd3-4d80-89d5-9b7c407bb1a4" eventImplId="0d4fb349-573c-4c34-8827-32a44f4db820">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8761667c-13e4-409f-a9fc-30d463a5f396" parallelMultiple="false" name="Error2" id="670dbcd4-3c2e-4707-8a78-50420c002894">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2f517510-a94d-4de4-8a3a-d678663c7eea</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="597ee65e-d913-4918-894e-1d32de4e383c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="6fd13d92-8abd-466f-8024-ef6ac4f5383c" eventImplId="56d0a384-703a-4bc4-85c2-4be6d8a3a8fa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0284bb5b-2792-4b1d-a848-53243a9d81bb" parallelMultiple="false" name="Error3" id="76191766-a0c8-4011-8dc7-b1d6e103b6f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="544" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>d5f8aa13-73ac-4380-8956-219e4f9ca0aa</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="aa4886ed-bccd-45ef-88f5-d6dc6e17d954" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c6311bca-a6c5-4668-85e8-fe2a88cb18b7" eventImplId="f2832a1f-a15a-4db6-8b5d-3491de4f32c8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="64d91e47-8aed-42e3-8e9f-6d41194f03ba">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="281" y="188" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1ce883a9-dbf9-44a3-8e12-fdb259f2a538</ns16:incoming>
                        
                        
                        <ns16:incoming>c20a130b-e984-40e4-8327-01f1743e1c9d</ns16:incoming>
                        
                        
                        <ns16:incoming>2f517510-a94d-4de4-8a3a-d678663c7eea</ns16:incoming>
                        
                        
                        <ns16:incoming>d5f8aa13-73ac-4380-8956-219e4f9ca0aa</ns16:incoming>
                        
                        
                        <ns16:outgoing>35f34226-9d0f-45ce-8197-4d72feda5ee4</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1f683527-f1a4-4fb5-81d9-9f9e951dba8a" targetRef="64d91e47-8aed-42e3-8e9f-6d41194f03ba" name="To Catch Errors" id="1ce883a9-dbf9-44a3-8e12-fdb259f2a538">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e1285f4c-166a-4d70-8580-0b4542c6fc02" targetRef="64d91e47-8aed-42e3-8e9f-6d41194f03ba" name="To Catch Errors" id="c20a130b-e984-40e4-8327-01f1743e1c9d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="670dbcd4-3c2e-4707-8a78-50420c002894" targetRef="64d91e47-8aed-42e3-8e9f-6d41194f03ba" name="To Catch Errors" id="2f517510-a94d-4de4-8a3a-d678663c7eea">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="76191766-a0c8-4011-8dc7-b1d6e103b6f8" targetRef="64d91e47-8aed-42e3-8e9f-6d41194f03ba" name="To Catch Errors" id="d5f8aa13-73ac-4380-8956-219e4f9ca0aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="64d91e47-8aed-42e3-8e9f-6d41194f03ba" targetRef="31bae867-392e-4426-a6e4-8dc68b0ae965" name="To End" id="35f34226-9d0f-45ce-8197-4d72feda5ee4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.35f34226-9d0f-45ce-8197-4d72feda5ee4</processLinkId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.31bae867-392e-4426-a6e4-8dc68b0ae965</toProcessItemId>
            <guid>d7eb0223-0233-4304-8866-1e920c7fb727</guid>
            <versionId>3b899d88-02ea-4fda-aa70-755b0fbb3a79</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.64d91e47-8aed-42e3-8e9f-6d41194f03ba</fromProcessItemId>
            <toProcessItemId>2025.31bae867-392e-4426-a6e4-8dc68b0ae965</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.03babc29-4e0e-40db-967f-8a70f66c966e</processLinkId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8761667c-13e4-409f-a9fc-30d463a5f396</toProcessItemId>
            <guid>56d730a7-ac5a-45ab-adec-ee0c64182264</guid>
            <versionId>488e1acc-f5b8-4a96-abb3-395d8c49867c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</fromProcessItemId>
            <toProcessItemId>2025.8761667c-13e4-409f-a9fc-30d463a5f396</toProcessItemId>
        </link>
        <link name="To Init SQL Query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3a92ac51-8633-4e85-a269-8fbff2578454</processLinkId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</toProcessItemId>
            <guid>45ae7516-d2d8-4aa0-afc2-eb6e1c8dcac1</guid>
            <versionId>b3629b21-edf9-4c53-a2b1-a31ff2c659c0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.bc5e2625-fbc6-4d23-8480-02027b6ddb2f</fromProcessItemId>
            <toProcessItemId>2025.8a48b78c-ff3c-4e5a-98bc-b9c1a390788b</toProcessItemId>
        </link>
        <link name="To Output Mapping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.98f980ae-b2dd-4428-86d5-f8648c01ce4d</processLinkId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8761667c-13e4-409f-a9fc-30d463a5f396</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.0284bb5b-2792-4b1d-a848-53243a9d81bb</toProcessItemId>
            <guid>65e01364-fc62-4085-a453-ccccf9933458</guid>
            <versionId>b96e4bab-6bfd-4704-86f7-2ff3ceb1a019</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8761667c-13e4-409f-a9fc-30d463a5f396</fromProcessItemId>
            <toProcessItemId>2025.0284bb5b-2792-4b1d-a848-53243a9d81bb</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.710bae78-7e76-4ae6-9392-89f02ee48401</processLinkId>
            <processId>1.5b59000d-e60c-4dee-87e1-34bfc64da51b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0284bb5b-2792-4b1d-a848-53243a9d81bb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.31bae867-392e-4426-a6e4-8dc68b0ae965</toProcessItemId>
            <guid>f4b350fc-04b4-4cf0-b00b-7e66903e01ef</guid>
            <versionId>f4e7b267-c9a8-43c9-9b3f-96a9b00f08fd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0284bb5b-2792-4b1d-a848-53243a9d81bb</fromProcessItemId>
            <toProcessItemId>2025.31bae867-392e-4426-a6e4-8dc68b0ae965</toProcessItemId>
        </link>
    </process>
</teamworks>

