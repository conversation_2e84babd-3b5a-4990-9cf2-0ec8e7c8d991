<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.1d264c59-eafd-4f05-a029-411f466237ce" name="get advance payment">
        <lastModified>1692725784475</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:4a5a0677488e996f:-34e0d142:18990dcbd71:-1a5f</guid>
        <versionId>e79ea78c-c820-4f80-8789-75c76644ffc9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-496b" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":103,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a24c7a12-0992-4da3-881b-c6a7a4fd2eaa"},{"incoming":["852715b6-bf38-40bf-8237-0fd0e2c1b11f","b7eaad46-5495-4899-8a27-a4d28342b2bb"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":797,"y":103,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:4a5a0677488e996f:-34e0d142:18990dcbd71:-1a5d"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"adaccfe2-1ed2-4a2f-80b7-3a2701cde57f"},{"startQuantity":1,"outgoing":["9e005b92-c102-4d68-8f48-d1208033af1c"],"incoming":["6c04f083-38ae-41bb-871d-08e755926a94"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":287,"y":80,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"18d9e821-21db-43fe-8ae9-d8e7081e0ac6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"SELECT d.ID,d.BENEFICIARY_NAME, d.CURRENCY, d.DOCUMENT_AMOUNT, d.AMOUNT_PAYABLE_BY_NBE, d.ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM BPM.IDC_REQUEST_DETAILS d, BPM.IDC_CUSTOMER_INFORMATION c WHERE d.REQUEST_STATE = 'Final' AND d.REQUEST_STATUS NOT LIKE 'Terminated' AND d.REQUEST_STATUS NOT LIKE 'Canceled' AND  d.REQUEST_NUMBER = ? AND c.IDC_REQUEST_ID = d.ID AND c.CIF = ? AND d.ID NOT IN (SELECT IDC_REQUEST_ID FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE REFERRAL_REQUEST_NUMBER =? )\";\r\nparamInit (\"VARCHAR\",tw.local.requestNumber);\r\nparamInit (\"VARCHAR\",tw.local.CIF);\r\nparamInit (\"VARCHAR\",tw.local.REFERRAL_REQUEST_NUMBER);\r\n\/\/-----------------------------------------------------------------------------------------------------------\r\ni = 0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"SELECT  BPM.IDC_REQUEST_INVOICES.INVOICE_NUMBER FROM BPM.IDC_REQUEST_INVOICES,BPM.IDC_REQUEST_DETAILS WHERE IDC_REQUEST_ID = BPM.IDC_REQUEST_DETAILS.ID AND BPM.IDC_REQUEST_DETAILS.REQUEST_NUMBER = ? ;\";\r\nparamInit (\"VARCHAR\",tw.local.requestNumber);"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.be14f72f-2bac-493d-8ed9-e6963402a382"},{"targetRef":"9a785e5a-e4bf-4737-8afa-7c48123fdd58","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Multiple Statements","declaredType":"sequenceFlow","id":"9e005b92-c102-4d68-8f48-d1208033af1c","sourceRef":"18d9e821-21db-43fe-8ae9-d8e7081e0ac6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\nautoObject[0].sql = \"\";\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0].value = null;\nautoObject[0].parameters[0].type = \"\";\nautoObject[0].parameters[0].mode = \"\";\nautoObject[0].maxRows = 0;\nautoObject"}]},"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.60861c5d-63e8-4509-8b2f-0010b759d7bf"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\nautoObject[0].type = \"\";\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\nautoObject[0].columns[0].catalogName = \"\";\nautoObject[0].columns[0].columnClassName = \"\";\nautoObject[0].columns[0].columnDisplaySize = 0;\nautoObject[0].columns[0].columnLabel = \"\";\nautoObject[0].columns[0].columnName = \"\";\nautoObject[0].columns[0].columnTypeName = \"\";\nautoObject[0].columns[0].precision = 0;\nautoObject[0].columns[0].scale = 0;\nautoObject[0].columns[0].schemaName = \"\";\nautoObject[0].columns[0].tableName = \"\";\nautoObject[0].columns[0].autoIncrement = false;\nautoObject[0].columns[0].caseSensitive = false;\nautoObject[0].columns[0].currency = false;\nautoObject[0].columns[0].definitelyWritable = false;\nautoObject[0].columns[0].nullable = 0;\nautoObject[0].columns[0].readOnly = false;\nautoObject[0].columns[0].searchable = false;\nautoObject[0].columns[0].signed = false;\nautoObject[0].columns[0].writable = false;\nautoObject[0].columnIndexes = null;\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\nautoObject[0].rows[0].data[0] = null;\nautoObject[0].rows[0].indexedMap = null;\nautoObject[0].updateCount = 0;\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\nautoObject[0].outValues[0] = null;\nautoObject"}]},"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"selectedResult","isCollection":true,"declaredType":"dataObject","id":"2056.85472c8f-d735-42d7-87f7-de8b1fa26fbe"},{"startQuantity":1,"outgoing":["852715b6-bf38-40bf-8237-0fd0e2c1b11f"],"incoming":["0d869385-c516-4eb1-8a61-5605bf8f9f61"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":627,"y":80,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Output Maping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0dc1b82e-0a57-49d2-8bae-1e7865dfdca8","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.UsedAdvancePayment();\r\n\ttw.local.results.invoiceCurrency = new tw.object.DBLookup();\r\n\tif ( tw.local.selectedResult[0].rows[0] != null) {\r\n\t\t\/\/tw.local.advancePayment = new tw.object.UsedAdvancePayment();\r\n\t\ttw.local.results.advancePaymentRequestNumber = tw.local.requestNumber;\r\n\t\ttw.local.results.DBID =  tw.local.selectedResult[0].rows[0].data[0];\r\n\t\ttw.local.results.beneficiaryName = tw.local.selectedResult[0].rows[0].data[1];\r\n\t\ttw.local.results.invoiceCurrency.code = tw.local.selectedResult[0].rows[0].data[2];\r\n\t\ttw.local.results.documentAmount = tw.local.selectedResult[0].rows[0].data[3];\r\n\t\ttw.local.results.paidAmount = tw.local.selectedResult[0].rows[0].data[4];\r\n\t\ttw.local.results.outstandingAmount = tw.local.selectedResult[0].rows[0].data[5];\r\n\t\ttw.local.results.invoiceNumber = \"\";\r\n\t\tfor (var i=0; i&lt;tw.local.selectedResult[1].rows.listLength; i++) {\r\n\t\t\t\r\n\t\t\ttw.local.results.invoiceNumber += tw.local.selectedResult[1].rows[i].data[0] + \"\\n\";\r\n\t\t}\r\n\t\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n}\r\n"]}},{"targetRef":"adaccfe2-1ed2-4a2f-80b7-3a2701cde57f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"852715b6-bf38-40bf-8237-0fd0e2c1b11f","sourceRef":"0dc1b82e-0a57-49d2-8bae-1e7865dfdca8"},{"startQuantity":1,"outgoing":["0d869385-c516-4eb1-8a61-5605bf8f9f61"],"incoming":["9e005b92-c102-4d68-8f48-d1208033af1c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":453,"y":80,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9a785e5a-e4bf-4737-8afa-7c48123fdd58","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.selectedResult"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"startQuantity":1,"outgoing":["6c04f083-38ae-41bb-871d-08e755926a94"],"incoming":["74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":150,"y":80,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1cde435f-1bf8-45b0-82c0-f22b19f92821","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.Seperated = new tw.object.listOf.String();\r\n\ttw.local.Seperated = tw.local.data.split(\"-\");\r\n\ttw.local.requestNumber = tw.local.Seperated[0];\r\n\ttw.local.CIF = tw.local.Seperated[1];\r\n\ttw.local.REFERRAL_REQUEST_NUMBER = tw.local.Seperated[2];\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n}\r\n\r\n"]}},{"targetRef":"18d9e821-21db-43fe-8ae9-d8e7081e0ac6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL","declaredType":"sequenceFlow","id":"6c04f083-38ae-41bb-871d-08e755926a94","sourceRef":"1cde435f-1bf8-45b0-82c0-f22b19f92821"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.9022a44d-4c88-4104-8523-2df363726e15"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"declaredType":"dataObject","id":"2056.ceb3baaf-7c4d-4601-8830-53d5abfed51f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"CIF","isCollection":false,"declaredType":"dataObject","id":"2056.d14e9d55-3d38-4dcb-8914-4eca82c1386d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"REFERRAL_REQUEST_NUMBER","isCollection":false,"declaredType":"dataObject","id":"2056.87359ae7-8a1b-45e3-811f-8f7824d4203f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.40def078-e776-437d-83ba-21c85dac2bf2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.771180d0-fc6c-4093-8d2c-7c6cd634652e"},{"targetRef":"0dc1b82e-0a57-49d2-8bae-1e7865dfdca8","extensionElements":{"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Output Maping","declaredType":"sequenceFlow","id":"0d869385-c516-4eb1-8a61-5605bf8f9f61","sourceRef":"9a785e5a-e4bf-4737-8afa-7c48123fdd58"},{"targetRef":"1cde435f-1bf8-45b0-82c0-f22b19f92821","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Split Input","declaredType":"sequenceFlow","id":"74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec","sourceRef":"a24c7a12-0992-4da3-881b-c6a7a4fd2eaa"},{"parallelMultiple":false,"outgoing":["68728364-0b62-428f-8716-9b282d26a1a2"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"da8d808a-484e-4aee-8ca4-09bcd02f1da9"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"17f829e9-8881-4a44-836d-26584d0b68ed","otherAttributes":{"eventImplId":"adeaaaa7-6008-4101-8df6-b0a00badf9ec"}}],"attachedToRef":"9a785e5a-e4bf-4737-8afa-7c48123fdd58","extensionElements":{"nodeVisualInfo":[{"width":24,"x":488,"y":138,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"f898eedd-f7bf-4c09-8a58-829ce5bdbfdb","outputSet":{}},{"startQuantity":1,"outgoing":["b7eaad46-5495-4899-8a27-a4d28342b2bb"],"incoming":["8d1c6783-f508-4775-8251-2894078c3f81","b2ca330e-1c11-4d52-8e82-76a024b6118c","68728364-0b62-428f-8716-9b282d26a1a2","90274894-31a1-4319-8b3b-d035afc3004a"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":452,"y":230,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"d61db0c6-42e2-456a-872b-c187b1509b49","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"parallelMultiple":false,"outgoing":["8d1c6783-f508-4775-8251-2894078c3f81"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"0adc4ee4-81ea-47b4-8547-843c259be062"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6aff322a-ff74-46e2-8fd7-9c34f5e2790a","otherAttributes":{"eventImplId":"078a68fb-3cfc-4f9d-82d8-c413d1ff89bf"}}],"attachedToRef":"1cde435f-1bf8-45b0-82c0-f22b19f92821","extensionElements":{"nodeVisualInfo":[{"width":24,"x":185,"y":138,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"eb167410-ad1c-4b2f-89bd-7cdf512e8bab","outputSet":{}},{"parallelMultiple":false,"outgoing":["b2ca330e-1c11-4d52-8e82-76a024b6118c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a06829ba-5f28-4350-832a-90d8f2209e7c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"731012ea-6b5f-4eb0-8025-10d0e41663fe","otherAttributes":{"eventImplId":"ba66c65c-42f8-4039-88d8-5560e002c9d4"}}],"attachedToRef":"18d9e821-21db-43fe-8ae9-d8e7081e0ac6","extensionElements":{"nodeVisualInfo":[{"width":24,"x":322,"y":138,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"1a441f65-546a-435c-8a1f-86c857229652","outputSet":{}},{"parallelMultiple":false,"outgoing":["90274894-31a1-4319-8b3b-d035afc3004a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f9971197-0d74-4e34-875e-5f0acbfcde07"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"170837c8-0dd0-4445-8547-3cff5c70aa69","otherAttributes":{"eventImplId":"99ebbd41-6ba2-435c-8974-84765bdfbff2"}}],"attachedToRef":"0dc1b82e-0a57-49d2-8bae-1e7865dfdca8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":662,"y":138,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"80899e24-0cf4-4daf-8c89-12a1eef4e27d","outputSet":{}},{"targetRef":"d61db0c6-42e2-456a-872b-c187b1509b49","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"8d1c6783-f508-4775-8251-2894078c3f81","sourceRef":"eb167410-ad1c-4b2f-89bd-7cdf512e8bab"},{"targetRef":"d61db0c6-42e2-456a-872b-c187b1509b49","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"b2ca330e-1c11-4d52-8e82-76a024b6118c","sourceRef":"1a441f65-546a-435c-8a1f-86c857229652"},{"targetRef":"d61db0c6-42e2-456a-872b-c187b1509b49","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"68728364-0b62-428f-8716-9b282d26a1a2","sourceRef":"f898eedd-f7bf-4c09-8a58-829ce5bdbfdb"},{"targetRef":"d61db0c6-42e2-456a-872b-c187b1509b49","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"90274894-31a1-4319-8b3b-d035afc3004a","sourceRef":"80899e24-0cf4-4daf-8c89-12a1eef4e27d"},{"targetRef":"adaccfe2-1ed2-4a2f-80b7-3a2701cde57f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"b7eaad46-5495-4899-8a27-a4d28342b2bb","sourceRef":"d61db0c6-42e2-456a-872b-c187b1509b49"}],"laneSet":[{"id":"a829a88b-c6cc-4ef2-83a8-e55addf86fb1","lane":[{"flowNodeRef":["a24c7a12-0992-4da3-881b-c6a7a4fd2eaa","adaccfe2-1ed2-4a2f-80b7-3a2701cde57f","18d9e821-21db-43fe-8ae9-d8e7081e0ac6","0dc1b82e-0a57-49d2-8bae-1e7865dfdca8","9a785e5a-e4bf-4737-8afa-7c48123fdd58","1cde435f-1bf8-45b0-82c0-f22b19f92821","f898eedd-f7bf-4c09-8a58-829ce5bdbfdb","d61db0c6-42e2-456a-872b-c187b1509b49","eb167410-ad1c-4b2f-89bd-7cdf512e8bab","1a441f65-546a-435c-8a1f-86c857229652","80899e24-0cf4-4daf-8c89-12a1eef4e27d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":561}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"f13f87de-1b03-466f-8156-d8767a125560","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"get advance payment","declaredType":"process","id":"1.1d264c59-eafd-4f05-a029-411f466237ce","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.c375c6f6-a250-4c36-8146-0d60cf710d78"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.1e54a000-d8ec-4013-88f5-7dfcc7deb24f"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/'00102230000575-12345678-22'\r\n12345678"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.b2c1f1bf-3095-443a-8267-e8807a6acf11"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b2c1f1bf-3095-443a-8267-e8807a6acf11</processParameterId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//'00102230000575-12345678-22'&#xD;
12345678</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bb95ba69-947a-4322-ae47-aaa68f7e4aa6</guid>
            <versionId>7707c5cb-0f81-46b7-9c08-ef4895ab43be</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c375c6f6-a250-4c36-8146-0d60cf710d78</processParameterId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>254c92fa-3303-4051-a5b9-3db81163b8ff</guid>
            <versionId>9e4ac136-544e-471c-a28e-3ee4c4a02461</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1e54a000-d8ec-4013-88f5-7dfcc7deb24f</processParameterId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a13d665b-46fe-468f-aa7f-0d37bcb3c151</guid>
            <versionId>03a21627-cd53-4979-be50-39383aa01f41</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.be14f72f-2bac-493d-8ed9-e6963402a382</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d52ee971-d2d1-4fd4-af77-e09c6989d7a4</guid>
            <versionId>a79b48a3-ba69-48fd-bfb9-0f1397863a4c</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.60861c5d-63e8-4509-8b2f-0010b759d7bf</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();
autoObject[0].sql = "";
autoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0].value = null;
autoObject[0].parameters[0].type = "";
autoObject[0].parameters[0].mode = "";
autoObject[0].maxRows = 0;
autoObject</defaultValue>
            <guid>a171b511-35af-4201-ba02-a820f252161b</guid>
            <versionId>99ccc327-eff1-4da5-93ba-9d82f97768cf</versionId>
        </processVariable>
        <processVariable name="selectedResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.85472c8f-d735-42d7-87f7-de8b1fa26fbe</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();
autoObject[0].type = "";
autoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0].catalogName = "";
autoObject[0].columns[0].columnClassName = "";
autoObject[0].columns[0].columnDisplaySize = 0;
autoObject[0].columns[0].columnLabel = "";
autoObject[0].columns[0].columnName = "";
autoObject[0].columns[0].columnTypeName = "";
autoObject[0].columns[0].precision = 0;
autoObject[0].columns[0].scale = 0;
autoObject[0].columns[0].schemaName = "";
autoObject[0].columns[0].tableName = "";
autoObject[0].columns[0].autoIncrement = false;
autoObject[0].columns[0].caseSensitive = false;
autoObject[0].columns[0].currency = false;
autoObject[0].columns[0].definitelyWritable = false;
autoObject[0].columns[0].nullable = 0;
autoObject[0].columns[0].readOnly = false;
autoObject[0].columns[0].searchable = false;
autoObject[0].columns[0].signed = false;
autoObject[0].columns[0].writable = false;
autoObject[0].columnIndexes = null;
autoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].rows[0].data[0] = null;
autoObject[0].rows[0].indexedMap = null;
autoObject[0].updateCount = 0;
autoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].outValues[0] = null;
autoObject</defaultValue>
            <guid>0b96cc7c-f4ff-4b1e-aca9-4b8dc54762eb</guid>
            <versionId>08c2181c-8117-486b-9d6e-83b4df7f744f</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9022a44d-4c88-4104-8523-2df363726e15</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4373a160-c7e1-4ac4-ae36-b7bb20572afd</guid>
            <versionId>9c89e3d3-5af0-4020-b849-984b036aeec6</versionId>
        </processVariable>
        <processVariable name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ceb3baaf-7c4d-4601-8830-53d5abfed51f</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3fce393f-4021-425c-b058-29fd2eae8c9a</guid>
            <versionId>5cbaa944-627d-4661-a44a-1dae6ee9a3d7</versionId>
        </processVariable>
        <processVariable name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d14e9d55-3d38-4dcb-8914-4eca82c1386d</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ea1f99a2-8f1d-4c9e-8e33-bc50b6051237</guid>
            <versionId>b6a3698e-5703-4dd3-9e11-df7a759bdcfb</versionId>
        </processVariable>
        <processVariable name="REFERRAL_REQUEST_NUMBER">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.87359ae7-8a1b-45e3-811f-8f7824d4203f</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cb34eb7c-b46a-473c-90f6-e8fcaeb696ca</guid>
            <versionId>eaa14cc3-0d34-4bb0-ba3f-17a0736c8f12</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.40def078-e776-437d-83ba-21c85dac2bf2</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3ee8ddb8-dc67-4eee-aa7c-54514b4cae7d</guid>
            <versionId>f1944711-3b10-4e2a-88db-31c96cf75326</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.771180d0-fc6c-4093-8d2c-7c6cd634652e</processVariableId>
            <description isNull="true" />
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>83be2796-389a-458b-ad8d-ce916fe3f5b7</guid>
            <versionId>016f0f12-80ff-4471-80e4-8da72fcb23bc</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.716b3d63-3250-4bbf-9a30-025b5cd8fc84</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
            <guid>guid:42738960b3d81f85:8740bc4:189d575e46d:545f</guid>
            <versionId>04c62407-e1d4-4e53-bf8c-79af2cdf7220</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2b74a4c1-f549-468e-a3d9-7a60f33c5713</processItemPrePostId>
                <processItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>961a3b01-c989-4a54-b014-55961bbf5a32</guid>
                <versionId>0372e7ba-2e48-4b75-8603-6215337f93aa</versionId>
            </processPrePosts>
            <layoutData x="150" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dda</errorHandlerItem>
                <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.716b3d63-3250-4bbf-9a30-025b5cd8fc84</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.requestNumber = tw.local.Seperated[0];&#xD;
	tw.local.CIF = tw.local.Seperated[1];&#xD;
	tw.local.REFERRAL_REQUEST_NUMBER = tw.local.Seperated[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>41e361b8-ad72-408b-bfb4-b08ddc329373</guid>
                <versionId>b9c5dfbb-64f8-4d0a-b57f-a977b11bc40b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.18d9e821-21db-43fe-8ae9-d8e7081e0ac6</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>Init SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f428148f-5bb8-4f0e-b4cd-add7e8dafcd6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c84ca897:-28d1</guid>
            <versionId>3e4f7e3c-19d4-48a4-ac26-8a6096c7e492</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="287" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dda</errorHandlerItem>
                <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f428148f-5bb8-4f0e-b4cd-add7e8dafcd6</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT d.ID,d.BENEFICIARY_NAME, d.CURRENCY, d.DOCUMENT_AMOUNT, d.AMOUNT_PAYABLE_BY_NBE, d.ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM BPM.IDC_REQUEST_DETAILS d, BPM.IDC_CUSTOMER_INFORMATION c WHERE d.REQUEST_STATE = 'Final' AND d.REQUEST_STATUS NOT LIKE 'Terminated' AND d.REQUEST_STATUS NOT LIKE 'Canceled' AND  d.REQUEST_NUMBER = ? AND c.IDC_REQUEST_ID = d.ID AND c.CIF = ? AND d.ID NOT IN (SELECT IDC_REQUEST_ID FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE REFERRAL_REQUEST_NUMBER =? )";&#xD;
paramInit ("VARCHAR",tw.local.requestNumber);&#xD;
paramInit ("VARCHAR",tw.local.CIF);&#xD;
paramInit ("VARCHAR",tw.local.REFERRAL_REQUEST_NUMBER);&#xD;
//-----------------------------------------------------------------------------------------------------------&#xD;
i = 0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT  BPM.IDC_REQUEST_INVOICES.INVOICE_NUMBER FROM BPM.IDC_REQUEST_INVOICES,BPM.IDC_REQUEST_DETAILS WHERE IDC_REQUEST_ID = BPM.IDC_REQUEST_DETAILS.ID AND BPM.IDC_REQUEST_DETAILS.REQUEST_NUMBER = ? ;";&#xD;
paramInit ("VARCHAR",tw.local.requestNumber);</script>
                <isRule>false</isRule>
                <guid>c5ac0030-7c04-4982-ba36-811dbe2e9fae</guid>
                <versionId>69eee6e1-3cb3-49ba-bdc3-26a45a1900fc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>Output Maping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.226c196c-527a-48a9-94d4-ab0104efb0ee</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
            <guid>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-1dd1</guid>
            <versionId>9f8a1e03-408e-480a-81e3-fbdd75a986d9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="627" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dda</errorHandlerItem>
                <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.226c196c-527a-48a9-94d4-ab0104efb0ee</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.UsedAdvancePayment();&#xD;
	tw.local.results.invoiceCurrency = new tw.object.DBLookup();&#xD;
	if ( tw.local.selectedResult[0].rows[0] != null) {&#xD;
		//tw.local.advancePayment = new tw.object.UsedAdvancePayment();&#xD;
		tw.local.results.advancePaymentRequestNumber = tw.local.requestNumber;&#xD;
		tw.local.results.DBID =  tw.local.selectedResult[0].rows[0].data[0];&#xD;
		tw.local.results.beneficiaryName = tw.local.selectedResult[0].rows[0].data[1];&#xD;
		tw.local.results.invoiceCurrency.code = tw.local.selectedResult[0].rows[0].data[2];&#xD;
		tw.local.results.documentAmount = tw.local.selectedResult[0].rows[0].data[3];&#xD;
		tw.local.results.paidAmount = tw.local.selectedResult[0].rows[0].data[4];&#xD;
		tw.local.results.outstandingAmount = tw.local.selectedResult[0].rows[0].data[5];&#xD;
		tw.local.results.invoiceNumber = "";&#xD;
		for (var i=0; i&lt;tw.local.selectedResult[1].rows.listLength; i++) {&#xD;
			&#xD;
			tw.local.results.invoiceNumber += tw.local.selectedResult[1].rows[i].data[0] + "\n";&#xD;
		}&#xD;
	&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>133fa506-7706-4c48-a819-e15035886d8c</guid>
                <versionId>6b9fe9f8-ab5b-451f-afd2-7af3aa789ae9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.cb444889-433a-4695-a067-09901be8c9bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dda</guid>
            <versionId>a7c7f4cc-9ac1-455c-a4d6-5f3842262a0f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="452" y="230">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.cb444889-433a-4695-a067-09901be8c9bb</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>490435dc-a5c5-4930-81ea-6c8d5c2108d4</guid>
                <versionId>ab08f98c-30a2-4a15-9b8c-503eb97e49fb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a785e5a-e4bf-4737-8afa-7c48123fdd58</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f8161fb0-b20e-4901-9f20-c798160eb835</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
            <guid>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-1721</guid>
            <versionId>e0b554c3-0d6d-4170-8aec-4f9e5d981810</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="453" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dda</errorHandlerItem>
                <errorHandlerItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f8161fb0-b20e-4901-9f20-c798160eb835</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>f44beb8f-8386-43b7-9540-3a37ca275503</guid>
                <versionId>3df1e037-ab94-4105-bdc7-173698db0b3b</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.de2bc597-d916-46ef-92da-0ac8c54de134</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.f8161fb0-b20e-4901-9f20-c798160eb835</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3e5d1ba2-8ac5-474a-9f83-0453f3f44cd7</guid>
                    <versionId>00e92abe-75bf-4406-a516-89b7b74082b5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5362a5b1-5ac5-4a5d-9af0-cbd180b9aae2</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.f8161fb0-b20e-4901-9f20-c798160eb835</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.selectedResult</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>5a256ffa-3a42-4fce-973f-95f2ff5765c7</guid>
                    <versionId>19b378eb-3a2c-4ef6-a7b1-0b8e69891170</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e9544b3b-fa4e-4bfe-8303-9ef4c93b7de1</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.f8161fb0-b20e-4901-9f20-c798160eb835</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>8a40da62-fa74-4c5f-8f9b-fd2a5a233e45</guid>
                    <versionId>5289e5d2-**************-f5b1139373e0</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</processItemId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5a8c743f-4c44-4aba-bc44-8fd804267bca</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a5a0677488e996f:-34e0d142:18990dcbd71:-1a5d</guid>
            <versionId>ecbe5a7c-e80d-41f1-b8a3-42c980406a3f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="797" y="103">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5a8c743f-4c44-4aba-bc44-8fd804267bca</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ecdd5fe0-a077-420a-a6b6-5898b700c350</guid>
                <versionId>944fea0c-f2b8-4275-88d9-db18bbaa3815</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="103">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="get advance payment" id="1.1d264c59-eafd-4f05-a029-411f466237ce" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.b2c1f1bf-3095-443a-8267-e8807a6acf11">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//'00102230000575-12345678-22'&#xD;
12345678</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.c375c6f6-a250-4c36-8146-0d60cf710d78" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.1e54a000-d8ec-4013-88f5-7dfcc7deb24f" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="a829a88b-c6cc-4ef2-83a8-e55addf86fb1">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="f13f87de-1b03-466f-8156-d8767a125560" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="561" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a24c7a12-0992-4da3-881b-c6a7a4fd2eaa</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>18d9e821-21db-43fe-8ae9-d8e7081e0ac6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9a785e5a-e4bf-4737-8afa-7c48123fdd58</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1cde435f-1bf8-45b0-82c0-f22b19f92821</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f898eedd-f7bf-4c09-8a58-829ce5bdbfdb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d61db0c6-42e2-456a-872b-c187b1509b49</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>eb167410-ad1c-4b2f-89bd-7cdf512e8bab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1a441f65-546a-435c-8a1f-86c857229652</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>80899e24-0cf4-4daf-8c89-12a1eef4e27d</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a24c7a12-0992-4da3-881b-c6a7a4fd2eaa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="103" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="adaccfe2-1ed2-4a2f-80b7-3a2701cde57f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="797" y="103" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:4a5a0677488e996f:-34e0d142:18990dcbd71:-1a5d</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>852715b6-bf38-40bf-8237-0fd0e2c1b11f</ns16:incoming>
                        
                        
                        <ns16:incoming>b7eaad46-5495-4899-8a27-a4d28342b2bb</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL" id="18d9e821-21db-43fe-8ae9-d8e7081e0ac6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="287" y="80" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6c04f083-38ae-41bb-871d-08e755926a94</ns16:incoming>
                        
                        
                        <ns16:outgoing>9e005b92-c102-4d68-8f48-d1208033af1c</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT d.ID,d.BENEFICIARY_NAME, d.CURRENCY, d.DOCUMENT_AMOUNT, d.AMOUNT_PAYABLE_BY_NBE, d.ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM BPM.IDC_REQUEST_DETAILS d, BPM.IDC_CUSTOMER_INFORMATION c WHERE d.REQUEST_STATE = 'Final' AND d.REQUEST_STATUS NOT LIKE 'Terminated' AND d.REQUEST_STATUS NOT LIKE 'Canceled' AND  d.REQUEST_NUMBER = ? AND c.IDC_REQUEST_ID = d.ID AND c.CIF = ? AND d.ID NOT IN (SELECT IDC_REQUEST_ID FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE REFERRAL_REQUEST_NUMBER =? )";&#xD;
paramInit ("VARCHAR",tw.local.requestNumber);&#xD;
paramInit ("VARCHAR",tw.local.CIF);&#xD;
paramInit ("VARCHAR",tw.local.REFERRAL_REQUEST_NUMBER);&#xD;
//-----------------------------------------------------------------------------------------------------------&#xD;
i = 0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT  BPM.IDC_REQUEST_INVOICES.INVOICE_NUMBER FROM BPM.IDC_REQUEST_INVOICES,BPM.IDC_REQUEST_DETAILS WHERE IDC_REQUEST_ID = BPM.IDC_REQUEST_DETAILS.ID AND BPM.IDC_REQUEST_DETAILS.REQUEST_NUMBER = ? ;";&#xD;
paramInit ("VARCHAR",tw.local.requestNumber);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.be14f72f-2bac-493d-8ed9-e6963402a382" />
                    
                    
                    <ns16:sequenceFlow sourceRef="18d9e821-21db-43fe-8ae9-d8e7081e0ac6" targetRef="9a785e5a-e4bf-4737-8afa-7c48123fdd58" name="To SQL Execute Multiple Statements" id="9e005b92-c102-4d68-8f48-d1208033af1c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.60861c5d-63e8-4509-8b2f-0010b759d7bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();
autoObject[0].sql = "";
autoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0].value = null;
autoObject[0].parameters[0].type = "";
autoObject[0].parameters[0].mode = "";
autoObject[0].maxRows = 0;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="selectedResult" id="2056.85472c8f-d735-42d7-87f7-de8b1fa26fbe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();
autoObject[0].type = "";
autoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0].catalogName = "";
autoObject[0].columns[0].columnClassName = "";
autoObject[0].columns[0].columnDisplaySize = 0;
autoObject[0].columns[0].columnLabel = "";
autoObject[0].columns[0].columnName = "";
autoObject[0].columns[0].columnTypeName = "";
autoObject[0].columns[0].precision = 0;
autoObject[0].columns[0].scale = 0;
autoObject[0].columns[0].schemaName = "";
autoObject[0].columns[0].tableName = "";
autoObject[0].columns[0].autoIncrement = false;
autoObject[0].columns[0].caseSensitive = false;
autoObject[0].columns[0].currency = false;
autoObject[0].columns[0].definitelyWritable = false;
autoObject[0].columns[0].nullable = 0;
autoObject[0].columns[0].readOnly = false;
autoObject[0].columns[0].searchable = false;
autoObject[0].columns[0].signed = false;
autoObject[0].columns[0].writable = false;
autoObject[0].columnIndexes = null;
autoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].rows[0].data[0] = null;
autoObject[0].rows[0].indexedMap = null;
autoObject[0].updateCount = 0;
autoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].outValues[0] = null;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Output Maping" id="0dc1b82e-0a57-49d2-8bae-1e7865dfdca8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="627" y="80" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0d869385-c516-4eb1-8a61-5605bf8f9f61</ns16:incoming>
                        
                        
                        <ns16:outgoing>852715b6-bf38-40bf-8237-0fd0e2c1b11f</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.UsedAdvancePayment();&#xD;
	tw.local.results.invoiceCurrency = new tw.object.DBLookup();&#xD;
	if ( tw.local.selectedResult[0].rows[0] != null) {&#xD;
		//tw.local.advancePayment = new tw.object.UsedAdvancePayment();&#xD;
		tw.local.results.advancePaymentRequestNumber = tw.local.requestNumber;&#xD;
		tw.local.results.DBID =  tw.local.selectedResult[0].rows[0].data[0];&#xD;
		tw.local.results.beneficiaryName = tw.local.selectedResult[0].rows[0].data[1];&#xD;
		tw.local.results.invoiceCurrency.code = tw.local.selectedResult[0].rows[0].data[2];&#xD;
		tw.local.results.documentAmount = tw.local.selectedResult[0].rows[0].data[3];&#xD;
		tw.local.results.paidAmount = tw.local.selectedResult[0].rows[0].data[4];&#xD;
		tw.local.results.outstandingAmount = tw.local.selectedResult[0].rows[0].data[5];&#xD;
		tw.local.results.invoiceNumber = "";&#xD;
		for (var i=0; i&lt;tw.local.selectedResult[1].rows.listLength; i++) {&#xD;
			&#xD;
			tw.local.results.invoiceNumber += tw.local.selectedResult[1].rows[i].data[0] + "\n";&#xD;
		}&#xD;
	&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0dc1b82e-0a57-49d2-8bae-1e7865dfdca8" targetRef="adaccfe2-1ed2-4a2f-80b7-3a2701cde57f" name="To End" id="852715b6-bf38-40bf-8237-0fd0e2c1b11f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="SQL Execute Multiple Statements (SQLResult)" id="9a785e5a-e4bf-4737-8afa-7c48123fdd58">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="453" y="80" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9e005b92-c102-4d68-8f48-d1208033af1c</ns16:incoming>
                        
                        
                        <ns16:outgoing>0d869385-c516-4eb1-8a61-5605bf8f9f61</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.selectedResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="1cde435f-1bf8-45b0-82c0-f22b19f92821">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="150" y="80" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec</ns16:incoming>
                        
                        
                        <ns16:outgoing>6c04f083-38ae-41bb-871d-08e755926a94</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.requestNumber = tw.local.Seperated[0];&#xD;
	tw.local.CIF = tw.local.Seperated[1];&#xD;
	tw.local.REFERRAL_REQUEST_NUMBER = tw.local.Seperated[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1cde435f-1bf8-45b0-82c0-f22b19f92821" targetRef="18d9e821-21db-43fe-8ae9-d8e7081e0ac6" name="To Init SQL" id="6c04f083-38ae-41bb-871d-08e755926a94">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.9022a44d-4c88-4104-8523-2df363726e15" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestNumber" id="2056.ceb3baaf-7c4d-4601-8830-53d5abfed51f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="CIF" id="2056.d14e9d55-3d38-4dcb-8914-4eca82c1386d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="REFERRAL_REQUEST_NUMBER" id="2056.87359ae7-8a1b-45e3-811f-8f7824d4203f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.40def078-e776-437d-83ba-21c85dac2bf2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.771180d0-fc6c-4093-8d2c-7c6cd634652e" />
                    
                    
                    <ns16:sequenceFlow sourceRef="9a785e5a-e4bf-4737-8afa-7c48123fdd58" targetRef="0dc1b82e-0a57-49d2-8bae-1e7865dfdca8" name="To Output Maping" id="0d869385-c516-4eb1-8a61-5605bf8f9f61">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a24c7a12-0992-4da3-881b-c6a7a4fd2eaa" targetRef="1cde435f-1bf8-45b0-82c0-f22b19f92821" name="To Split Input" id="74a0b0b7-1ceb-4b1a-80e6-95b1da1dd6ec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9a785e5a-e4bf-4737-8afa-7c48123fdd58" parallelMultiple="false" name="Error" id="f898eedd-f7bf-4c09-8a58-829ce5bdbfdb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="488" y="138" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>68728364-0b62-428f-8716-9b282d26a1a2</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="da8d808a-484e-4aee-8ca4-09bcd02f1da9" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="17f829e9-8881-4a44-836d-26584d0b68ed" eventImplId="adeaaaa7-6008-4101-8df6-b0a00badf9ec">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="d61db0c6-42e2-456a-872b-c187b1509b49">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="452" y="230" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8d1c6783-f508-4775-8251-2894078c3f81</ns16:incoming>
                        
                        
                        <ns16:incoming>b2ca330e-1c11-4d52-8e82-76a024b6118c</ns16:incoming>
                        
                        
                        <ns16:incoming>68728364-0b62-428f-8716-9b282d26a1a2</ns16:incoming>
                        
                        
                        <ns16:incoming>90274894-31a1-4319-8b3b-d035afc3004a</ns16:incoming>
                        
                        
                        <ns16:outgoing>b7eaad46-5495-4899-8a27-a4d28342b2bb</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1cde435f-1bf8-45b0-82c0-f22b19f92821" parallelMultiple="false" name="Error1" id="eb167410-ad1c-4b2f-89bd-7cdf512e8bab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="185" y="138" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8d1c6783-f508-4775-8251-2894078c3f81</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="0adc4ee4-81ea-47b4-8547-843c259be062" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="6aff322a-ff74-46e2-8fd7-9c34f5e2790a" eventImplId="078a68fb-3cfc-4f9d-82d8-c413d1ff89bf">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="18d9e821-21db-43fe-8ae9-d8e7081e0ac6" parallelMultiple="false" name="Error2" id="1a441f65-546a-435c-8a1f-86c857229652">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="322" y="138" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b2ca330e-1c11-4d52-8e82-76a024b6118c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a06829ba-5f28-4350-832a-90d8f2209e7c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="731012ea-6b5f-4eb0-8025-10d0e41663fe" eventImplId="ba66c65c-42f8-4039-88d8-5560e002c9d4">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0dc1b82e-0a57-49d2-8bae-1e7865dfdca8" parallelMultiple="false" name="Error3" id="80899e24-0cf4-4daf-8c89-12a1eef4e27d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="662" y="138" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>90274894-31a1-4319-8b3b-d035afc3004a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f9971197-0d74-4e34-875e-5f0acbfcde07" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="170837c8-0dd0-4445-8547-3cff5c70aa69" eventImplId="99ebbd41-6ba2-435c-8974-84765bdfbff2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="eb167410-ad1c-4b2f-89bd-7cdf512e8bab" targetRef="d61db0c6-42e2-456a-872b-c187b1509b49" name="To Catch Errors" id="8d1c6783-f508-4775-8251-2894078c3f81">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1a441f65-546a-435c-8a1f-86c857229652" targetRef="d61db0c6-42e2-456a-872b-c187b1509b49" name="To Catch Errors" id="b2ca330e-1c11-4d52-8e82-76a024b6118c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f898eedd-f7bf-4c09-8a58-829ce5bdbfdb" targetRef="d61db0c6-42e2-456a-872b-c187b1509b49" name="To Catch Errors" id="68728364-0b62-428f-8716-9b282d26a1a2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="80899e24-0cf4-4daf-8c89-12a1eef4e27d" targetRef="d61db0c6-42e2-456a-872b-c187b1509b49" name="To Catch Errors" id="90274894-31a1-4319-8b3b-d035afc3004a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d61db0c6-42e2-456a-872b-c187b1509b49" targetRef="adaccfe2-1ed2-4a2f-80b7-3a2701cde57f" name="To End" id="b7eaad46-5495-4899-8a27-a4d28342b2bb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute Multiple Statements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9e005b92-c102-4d68-8f48-d1208033af1c</processLinkId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.18d9e821-21db-43fe-8ae9-d8e7081e0ac6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9a785e5a-e4bf-4737-8afa-7c48123fdd58</toProcessItemId>
            <guid>69664d9e-9e1e-4cc9-97d4-7e4010808930</guid>
            <versionId>584ca44d-6cfe-4435-8ee2-413e3aee514a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.18d9e821-21db-43fe-8ae9-d8e7081e0ac6</fromProcessItemId>
            <toProcessItemId>2025.9a785e5a-e4bf-4737-8afa-7c48123fdd58</toProcessItemId>
        </link>
        <link name="To Init SQL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6c04f083-38ae-41bb-871d-08e755926a94</processLinkId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.18d9e821-21db-43fe-8ae9-d8e7081e0ac6</toProcessItemId>
            <guid>f57a1c3b-4e9d-459c-8cef-f6af408090a1</guid>
            <versionId>5b3a5da3-5351-46c4-908f-f3fb63f9c1b0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1cde435f-1bf8-45b0-82c0-f22b19f92821</fromProcessItemId>
            <toProcessItemId>2025.18d9e821-21db-43fe-8ae9-d8e7081e0ac6</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.852715b6-bf38-40bf-8237-0fd0e2c1b11f</processLinkId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</toProcessItemId>
            <guid>e109afe4-04ee-4d0c-8e2e-8df9b28d240e</guid>
            <versionId>6383a379-1735-4d83-aa91-84a6e60af806</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</fromProcessItemId>
            <toProcessItemId>2025.adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b7eaad46-5495-4899-8a27-a4d28342b2bb</processLinkId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</toProcessItemId>
            <guid>9fbfefb0-0128-46e9-a689-8cb38525cc0b</guid>
            <versionId>da0171e9-3e08-4a18-9e8e-f1ade66f5a90</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.d61db0c6-42e2-456a-872b-c187b1509b49</fromProcessItemId>
            <toProcessItemId>2025.adaccfe2-1ed2-4a2f-80b7-3a2701cde57f</toProcessItemId>
        </link>
        <link name="To Output Maping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0d869385-c516-4eb1-8a61-5605bf8f9f61</processLinkId>
            <processId>1.1d264c59-eafd-4f05-a029-411f466237ce</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9a785e5a-e4bf-4737-8afa-7c48123fdd58</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</toProcessItemId>
            <guid>51ff09bf-7f96-4417-baf8-ad4109a52fe6</guid>
            <versionId>ebb8c6c8-fa25-4b3d-8a63-cd247b3016d0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9a785e5a-e4bf-4737-8afa-7c48123fdd58</fromProcessItemId>
            <toProcessItemId>2025.0dc1b82e-0a57-49d2-8bae-1e7865dfdca8</toProcessItemId>
        </link>
    </process>
</teamworks>

