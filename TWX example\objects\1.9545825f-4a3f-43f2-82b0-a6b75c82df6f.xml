<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.9545825f-4a3f-43f2-82b0-a6b75c82df6f" name="getLookups">
        <lastModified>1692507128099</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.cead749d-a192-4763-95cd-240c3884bb8c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>true</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>43200</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>483623fe-b18c-4535-a3b0-175349905fb8</guid>
        <versionId>393eb98b-f609-4418-a6e4-4b41c166b773</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed1" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":195,"y":50,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"00652b95-9223-42f5-a240-145f761d2d7f"},{"incoming":["d86fdabc-7a23-4b40-80d8-30ab4ca40e49","cd5e350f-6a1a-4a8b-849f-d3879ede0920"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":841,"y":44,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6162"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"a6adabe3-b833-42d3-a8fb-1a1a969cc1b5"},{"targetRef":"cead749d-a192-4763-95cd-240c3884bb8c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3","sourceRef":"00652b95-9223-42f5-a240-145f761d2d7f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.da6195b3-0026-4b6c-9a09-bd5175079f9c"},{"itemSubjectRef":"itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d","name":"lookup","isCollection":true,"declaredType":"dataObject","id":"2056.38e384a1-08e6-4a94-a91a-5b9924124bf2"},{"startQuantity":1,"outgoing":["af6c4453-d9f6-4e53-b9dd-c976b440677e"],"incoming":["2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":387,"y":27,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"cead749d-a192-4763-95cd-240c3884bb8c","scriptFormat":"text\/plain","script":{"content":["tw.local.sql\nselect *  from &lt;#= tw.local.data #&gt;"]}},{"startQuantity":1,"outgoing":["a419c10f-426d-4d55-b370-c3d0735db479"],"incoming":["af6c4453-d9f6-4e53-b9dd-c976b440677e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":526,"y":27,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"DBLookup\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3624ab1e-3dc7-4cf5-8560-e64642e45ec1","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d","declaredType":"TFormalExpression","content":["tw.local.lookup"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"startQuantity":1,"outgoing":["d86fdabc-7a23-4b40-80d8-30ab4ca40e49"],"incoming":["a419c10f-426d-4d55-b370-c3d0735db479"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":677,"y":27,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Results","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"71c071e9-1d16-443c-9c3a-7470f6079bbb","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.DBLookup();\r\nfor (var i=0; i&lt;tw.local.lookup.listLength; i++) {\r\n\ttw.local.results[i] = new tw.object.DBLookup();\r\n\ttw.local.results[i] = tw.local.lookup[i];\r\n}\r\n"]}},{"targetRef":"3624ab1e-3dc7-4cf5-8560-e64642e45ec1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"af6c4453-d9f6-4e53-b9dd-c976b440677e","sourceRef":"cead749d-a192-4763-95cd-240c3884bb8c"},{"targetRef":"71c071e9-1d16-443c-9c3a-7470f6079bbb","extensionElements":{"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Results","declaredType":"sequenceFlow","id":"a419c10f-426d-4d55-b370-c3d0735db479","sourceRef":"3624ab1e-3dc7-4cf5-8560-e64642e45ec1"},{"targetRef":"a6adabe3-b833-42d3-a8fb-1a1a969cc1b5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"d86fdabc-7a23-4b40-80d8-30ab4ca40e49","sourceRef":"71c071e9-1d16-443c-9c3a-7470f6079bbb"},{"parallelMultiple":false,"outgoing":["d35e1cd1-fbe6-4411-879a-605a32ac5345"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8ffc6a6b-ac71-4506-8a92-bd921b3d12bb"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"221e4bc6-caa9-4a6e-8778-d95fa7e1de54","otherAttributes":{"eventImplId":"e435b009-3b8c-46f0-8d37-4d05a2ee6388"}}],"attachedToRef":"cead749d-a192-4763-95cd-240c3884bb8c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":422,"y":85,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"db83c048-c085-4d73-8af6-9e03a08582f5","outputSet":{}},{"parallelMultiple":false,"outgoing":["75fa18e1-25f5-48c5-82b5-1c05e305a528"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"dca3b657-b73d-412a-820a-7ef39fa3c475"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9915a974-48b8-4390-8509-5f49d3b05bdc","otherAttributes":{"eventImplId":"10ba676c-a55d-44cc-892a-e01924d9b671"}}],"attachedToRef":"3624ab1e-3dc7-4cf5-8560-e64642e45ec1","extensionElements":{"nodeVisualInfo":[{"width":24,"x":561,"y":85,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"506a0d9b-510e-404b-8997-4611fe9a9d4c","outputSet":{}},{"parallelMultiple":false,"outgoing":["95e10848-e667-4921-8fd7-5ff8ffe3992c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ee8a8bd3-cadd-4a38-883e-c678a23828e1"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4ed2f133-5b2e-4204-805c-bdeb72d14193","otherAttributes":{"eventImplId":"b324b243-ea18-4057-8117-6c90a5f11cbd"}}],"attachedToRef":"71c071e9-1d16-443c-9c3a-7470f6079bbb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":712,"y":85,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"505dabc9-6e97-4936-8f29-6ab4381127ee","outputSet":{}},{"targetRef":"8745c8b8-d071-4cc8-8487-c8a7e02ed469","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"d35e1cd1-fbe6-4411-879a-605a32ac5345","sourceRef":"db83c048-c085-4d73-8af6-9e03a08582f5"},{"targetRef":"8745c8b8-d071-4cc8-8487-c8a7e02ed469","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"75fa18e1-25f5-48c5-82b5-1c05e305a528","sourceRef":"506a0d9b-510e-404b-8997-4611fe9a9d4c"},{"targetRef":"8745c8b8-d071-4cc8-8487-c8a7e02ed469","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"95e10848-e667-4921-8fd7-5ff8ffe3992c","sourceRef":"505dabc9-6e97-4936-8f29-6ab4381127ee"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.68f8016d-2486-4245-8ef5-b1c5d5445abe"},{"startQuantity":1,"outgoing":["cd5e350f-6a1a-4a8b-849f-d3879ede0920"],"incoming":["d35e1cd1-fbe6-4411-879a-605a32ac5345","75fa18e1-25f5-48c5-82b5-1c05e305a528","95e10848-e667-4921-8fd7-5ff8ffe3992c"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":562,"y":172,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8745c8b8-d071-4cc8-8487-c8a7e02ed469","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"\";"]}},{"targetRef":"a6adabe3-b833-42d3-a8fb-1a1a969cc1b5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"cd5e350f-6a1a-4a8b-849f-d3879ede0920","sourceRef":"8745c8b8-d071-4cc8-8487-c8a7e02ed469"}],"laneSet":[{"id":"6a268d05-24e3-4717-a528-9f6e707d0cef","lane":[{"flowNodeRef":["00652b95-9223-42f5-a240-145f761d2d7f","a6adabe3-b833-42d3-a8fb-1a1a969cc1b5","cead749d-a192-4763-95cd-240c3884bb8c","3624ab1e-3dc7-4cf5-8560-e64642e45ec1","71c071e9-1d16-443c-9c3a-7470f6079bbb","db83c048-c085-4d73-8af6-9e03a08582f5","506a0d9b-510e-404b-8997-4611fe9a9d4c","505dabc9-6e97-4936-8f29-6ab4381127ee","8745c8b8-d071-4cc8-8487-c8a7e02ed469"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"8d1edc1e-2bc4-4576-830c-5f335a300434","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"cachingType":[true],"cacheLength":[43200],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"getLookups","declaredType":"process","id":"1.9545825f-4a3f-43f2-82b0-a6b75c82df6f","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.60db9ee9-7bc6-4745-9e61-dcc2f5beb3eb"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.733f1280-c0f2-45a4-866c-6f0b8b9a26fa"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.60db9ee9-7bc6-4745-9e61-dcc2f5beb3eb","2055.733f1280-c0f2-45a4-866c-6f0b8b9a26fa"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"BPM.IDC_Request_Nature\"\r\n\"BPM.IDC_Facility_Type\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.8cb3620a-23f1-4981-b0ca-1b02bc87d587"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8cb3620a-23f1-4981-b0ca-1b02bc87d587</processParameterId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"BPM.IDC_Request_Nature"&#xD;
"BPM.IDC_Facility_Type"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c2002040-6f9e-4ef9-884b-c76f87d1791d</guid>
            <versionId>e21d107d-7de7-4ace-97a6-b4da0465e52e</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.60db9ee9-7bc6-4745-9e61-dcc2f5beb3eb</processParameterId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d4179bf7-9252-4cae-b4db-299e91780f04</guid>
            <versionId>8386d22d-dc30-41fe-afea-291b99f4e6df</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.733f1280-c0f2-45a4-866c-6f0b8b9a26fa</processParameterId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7c3f7fa3-1fe3-4b2a-9cc7-831ed800bf16</guid>
            <versionId>f674e13a-0379-4d8d-aea3-cc5d843d73f8</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.da6195b3-0026-4b6c-9a09-bd5175079f9c</processVariableId>
            <description isNull="true" />
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a5e25160-a245-4754-815a-341eeb4bc82a</guid>
            <versionId>ed688c4c-e94d-4a61-9563-a47633ee628e</versionId>
        </processVariable>
        <processVariable name="lookup">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.38e384a1-08e6-4a94-a91a-5b9924124bf2</processVariableId>
            <description isNull="true" />
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.d249aea6-d076-4da7-887d-0f1dbba9713d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0ea80ab-fd77-4a5a-bf5a-fd96d74696c9</guid>
            <versionId>d0572f4f-6a27-48a8-8f4f-e773ca33c853</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.68f8016d-2486-4245-8ef5-b1c5d5445abe</processVariableId>
            <description isNull="true" />
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>67a1dd94-d405-4727-8b5a-05670233d50f</guid>
            <versionId>f630ae30-760b-4583-a46d-63604db92827</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.71c071e9-1d16-443c-9c3a-7470f6079bbb</processItemId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <name>Set Results</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.971d73b3-c52f-4032-bcf3-fa32178e2f0b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6160</guid>
            <versionId>4708c193-84c2-4b65-919e-4edb0373272b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="677" y="27">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed0</errorHandlerItem>
                <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.971d73b3-c52f-4032-bcf3-fa32178e2f0b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.DBLookup();&#xD;
for (var i=0; i&lt;tw.local.lookup.listLength; i++) {&#xD;
	tw.local.results[i] = new tw.object.DBLookup();&#xD;
	tw.local.results[i] = tw.local.lookup[i];&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>5096299d-8f6e-4ad1-9e02-3f9d906f6136</guid>
                <versionId>f31a3f55-fe2c-4b00-a744-4f4298e19372</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</processItemId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e39d30c8-052c-449c-b67f-feb57a75d55c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed0</guid>
            <versionId>8ce0a803-4dad-4433-bb4a-676b4a094079</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="562" y="172">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e39d30c8-052c-449c-b67f-feb57a75d55c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</script>
                <isRule>false</isRule>
                <guid>44d53dd4-0f77-43c6-b1b8-13b76698c347</guid>
                <versionId>7bcad4f1-6130-45d7-8d5b-ed3091981393</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3624ab1e-3dc7-4cf5-8560-e64642e45ec1</processItemId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-615e</guid>
            <versionId>a641ff86-f415-4fe3-8133-543c8bf64b23</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="526" y="27">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed0</errorHandlerItem>
                <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>3e2d9afa-4b37-46f6-be2f-8581b5548491</guid>
                <versionId>b4e65d2b-208a-4afb-92d0-444b34ad2d19</versionId>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.082fc57d-b3df-4664-abe0-a149be099e49</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"DBLookup"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>935a78a3-a270-42d0-9336-0984df99f774</guid>
                    <versionId>0188d171-1ae5-4b86-bf98-ba3d757bd8e3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a54c520d-1155-4e0a-adf3-0984c92030ab</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>b944fdcd-bc9b-40bb-9740-0a313b5290c2</guid>
                    <versionId>31c723bc-5c6c-487a-b92e-c454fd76bac4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.36332b55-4e24-490e-9fa8-e1ca155929a5</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ee44e548-06d1-4552-9cc7-5f43b2678032</guid>
                    <versionId>4fe2a433-3186-497f-a40f-56e1b1128188</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4f09271d-6060-4afe-95ed-0458b12b9013</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a002273a-3ec6-4ba6-8b3d-beb0a7c1c554</guid>
                    <versionId>*************-470f-983e-c774769001c3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.87d28cdc-dc39-464a-92dc-77050c7f44d6</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.lookup</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>653d8b0d-4769-4ca8-a951-1c35ec507eb3</guid>
                    <versionId>707188b8-b5eb-4f71-a738-b573bdac7c52</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fc80b9ad-1c10-4e1b-b5fa-948b931b06cc</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.aed4b558-6f3b-454b-a3b7-5245416dea92</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>57b346d0-03a6-49e6-9a17-4ada549aab45</guid>
                    <versionId>cc63c9fa-fc06-4307-a445-************</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cead749d-a192-4763-95cd-240c3884bb8c</processItemId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <name>Set Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7d594b69-62de-488c-9255-e0d6618f6ddf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6161</guid>
            <versionId>bc2a4a7c-ea92-48c2-be6b-394d9bfb57ce</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="387" y="27">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ed0</errorHandlerItem>
                <errorHandlerItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7d594b69-62de-488c-9255-e0d6618f6ddf</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql
select *  from &lt;#= tw.local.data #&gt;</script>
                <isRule>false</isRule>
                <guid>b93f4992-ade0-4f27-9c0c-298ff64b3469</guid>
                <versionId>bc393cb3-c12c-4f73-b19e-70b62b7d26eb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</processItemId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6e4b7e61-4380-42da-ab5c-d0e6d914538f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6162</guid>
            <versionId>bc366fff-42f2-4d6f-b324-ecdd5a894685</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="841" y="44">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6e4b7e61-4380-42da-ab5c-d0e6d914538f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>f20b5e93-fa6d-4a39-a0b8-1ad361acb769</guid>
                <versionId>b0a395b4-622f-4e5a-b317-346cb002a12a</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.cead749d-a192-4763-95cd-240c3884bb8c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="195" y="50">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="getLookups" id="1.9545825f-4a3f-43f2-82b0-a6b75c82df6f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:cachingType>true</ns3:cachingType>
                        
                        
                        <ns3:cacheLength>43200</ns3:cacheLength>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.8cb3620a-23f1-4981-b0ca-1b02bc87d587">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"BPM.IDC_Request_Nature"&#xD;
"BPM.IDC_Facility_Type"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.60db9ee9-7bc6-4745-9e61-dcc2f5beb3eb" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.733f1280-c0f2-45a4-866c-6f0b8b9a26fa" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.60db9ee9-7bc6-4745-9e61-dcc2f5beb3eb</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.733f1280-c0f2-45a4-866c-6f0b8b9a26fa</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="6a268d05-24e3-4717-a528-9f6e707d0cef">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="8d1edc1e-2bc4-4576-830c-5f335a300434" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>00652b95-9223-42f5-a240-145f761d2d7f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cead749d-a192-4763-95cd-240c3884bb8c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3624ab1e-3dc7-4cf5-8560-e64642e45ec1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>71c071e9-1d16-443c-9c3a-7470f6079bbb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>db83c048-c085-4d73-8af6-9e03a08582f5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>506a0d9b-510e-404b-8997-4611fe9a9d4c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>505dabc9-6e97-4936-8f29-6ab4381127ee</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8745c8b8-d071-4cc8-8487-c8a7e02ed469</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="00652b95-9223-42f5-a240-145f761d2d7f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="195" y="50" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="a6adabe3-b833-42d3-a8fb-1a1a969cc1b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="841" y="44" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6162</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d86fdabc-7a23-4b40-80d8-30ab4ca40e49</ns16:incoming>
                        
                        
                        <ns16:incoming>cd5e350f-6a1a-4a8b-849f-d3879ede0920</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="00652b95-9223-42f5-a240-145f761d2d7f" targetRef="cead749d-a192-4763-95cd-240c3884bb8c" name="To End" id="2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.da6195b3-0026-4b6c-9a09-bd5175079f9c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d" isCollection="true" name="lookup" id="2056.38e384a1-08e6-4a94-a91a-5b9924124bf2" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Query" id="cead749d-a192-4763-95cd-240c3884bb8c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="387" y="27" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.e3ef963a-85ba-4e9a-962c-5ff83c3a32a3</ns16:incoming>
                        
                        
                        <ns16:outgoing>af6c4453-d9f6-4e53-b9dd-c976b440677e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql
select *  from &lt;#= tw.local.data #&gt;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Statement" id="3624ab1e-3dc7-4cf5-8560-e64642e45ec1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="526" y="27" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>af6c4453-d9f6-4e53-b9dd-c976b440677e</ns16:incoming>
                        
                        
                        <ns16:outgoing>a419c10f-426d-4d55-b370-c3d0735db479</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"DBLookup"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d249aea6-d076-4da7-887d-0f1dbba9713d">tw.local.lookup</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Results" id="71c071e9-1d16-443c-9c3a-7470f6079bbb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="677" y="27" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a419c10f-426d-4d55-b370-c3d0735db479</ns16:incoming>
                        
                        
                        <ns16:outgoing>d86fdabc-7a23-4b40-80d8-30ab4ca40e49</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.DBLookup();&#xD;
for (var i=0; i&lt;tw.local.lookup.listLength; i++) {&#xD;
	tw.local.results[i] = new tw.object.DBLookup();&#xD;
	tw.local.results[i] = tw.local.lookup[i];&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="cead749d-a192-4763-95cd-240c3884bb8c" targetRef="3624ab1e-3dc7-4cf5-8560-e64642e45ec1" name="To SQL Execute Statement" id="af6c4453-d9f6-4e53-b9dd-c976b440677e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3624ab1e-3dc7-4cf5-8560-e64642e45ec1" targetRef="71c071e9-1d16-443c-9c3a-7470f6079bbb" name="To Set Results" id="a419c10f-426d-4d55-b370-c3d0735db479">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="71c071e9-1d16-443c-9c3a-7470f6079bbb" targetRef="a6adabe3-b833-42d3-a8fb-1a1a969cc1b5" name="To End" id="d86fdabc-7a23-4b40-80d8-30ab4ca40e49">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="cead749d-a192-4763-95cd-240c3884bb8c" parallelMultiple="false" name="Error" id="db83c048-c085-4d73-8af6-9e03a08582f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="422" y="85" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>d35e1cd1-fbe6-4411-879a-605a32ac5345</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8ffc6a6b-ac71-4506-8a92-bd921b3d12bb" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="221e4bc6-caa9-4a6e-8778-d95fa7e1de54" eventImplId="e435b009-3b8c-46f0-8d37-4d05a2ee6388">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3624ab1e-3dc7-4cf5-8560-e64642e45ec1" parallelMultiple="false" name="Error1" id="506a0d9b-510e-404b-8997-4611fe9a9d4c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="561" y="85" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>75fa18e1-25f5-48c5-82b5-1c05e305a528</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="dca3b657-b73d-412a-820a-7ef39fa3c475" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9915a974-48b8-4390-8509-5f49d3b05bdc" eventImplId="10ba676c-a55d-44cc-892a-e01924d9b671">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="71c071e9-1d16-443c-9c3a-7470f6079bbb" parallelMultiple="false" name="Error2" id="505dabc9-6e97-4936-8f29-6ab4381127ee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="712" y="85" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>95e10848-e667-4921-8fd7-5ff8ffe3992c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ee8a8bd3-cadd-4a38-883e-c678a23828e1" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4ed2f133-5b2e-4204-805c-bdeb72d14193" eventImplId="b324b243-ea18-4057-8117-6c90a5f11cbd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="db83c048-c085-4d73-8af6-9e03a08582f5" targetRef="8745c8b8-d071-4cc8-8487-c8a7e02ed469" name="To End Event" id="d35e1cd1-fbe6-4411-879a-605a32ac5345">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="506a0d9b-510e-404b-8997-4611fe9a9d4c" targetRef="8745c8b8-d071-4cc8-8487-c8a7e02ed469" name="To End Event" id="75fa18e1-25f5-48c5-82b5-1c05e305a528">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="505dabc9-6e97-4936-8f29-6ab4381127ee" targetRef="8745c8b8-d071-4cc8-8487-c8a7e02ed469" name="To End Event" id="95e10848-e667-4921-8fd7-5ff8ffe3992c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.68f8016d-2486-4245-8ef5-b1c5d5445abe" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="8745c8b8-d071-4cc8-8487-c8a7e02ed469">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="562" y="172" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d35e1cd1-fbe6-4411-879a-605a32ac5345</ns16:incoming>
                        
                        
                        <ns16:incoming>75fa18e1-25f5-48c5-82b5-1c05e305a528</ns16:incoming>
                        
                        
                        <ns16:incoming>95e10848-e667-4921-8fd7-5ff8ffe3992c</ns16:incoming>
                        
                        
                        <ns16:outgoing>cd5e350f-6a1a-4a8b-849f-d3879ede0920</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8745c8b8-d071-4cc8-8487-c8a7e02ed469" targetRef="a6adabe3-b833-42d3-a8fb-1a1a969cc1b5" name="To End" id="cd5e350f-6a1a-4a8b-849f-d3879ede0920">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cd5e350f-6a1a-4a8b-849f-d3879ede0920</processLinkId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</toProcessItemId>
            <guid>0717e84c-f57a-4edc-8053-09582b7b36e0</guid>
            <versionId>3d397a83-736b-4512-978c-48e73f3752e8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.8745c8b8-d071-4cc8-8487-c8a7e02ed469</fromProcessItemId>
            <toProcessItemId>2025.a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</toProcessItemId>
        </link>
        <link name="To Set Results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a419c10f-426d-4d55-b370-c3d0735db479</processLinkId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3624ab1e-3dc7-4cf5-8560-e64642e45ec1</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.71c071e9-1d16-443c-9c3a-7470f6079bbb</toProcessItemId>
            <guid>9cd16add-e95c-41e7-be2a-2f77f5f72d94</guid>
            <versionId>513a6159-2b0d-48b0-af68-66c8f0843e99</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3624ab1e-3dc7-4cf5-8560-e64642e45ec1</fromProcessItemId>
            <toProcessItemId>2025.71c071e9-1d16-443c-9c3a-7470f6079bbb</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.af6c4453-d9f6-4e53-b9dd-c976b440677e</processLinkId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cead749d-a192-4763-95cd-240c3884bb8c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3624ab1e-3dc7-4cf5-8560-e64642e45ec1</toProcessItemId>
            <guid>540b26ec-c4d9-45d9-bfc8-6a5d4ccc9efc</guid>
            <versionId>bdb45787-8220-4d18-bf81-ae2e8982d318</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.cead749d-a192-4763-95cd-240c3884bb8c</fromProcessItemId>
            <toProcessItemId>2025.3624ab1e-3dc7-4cf5-8560-e64642e45ec1</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d86fdabc-7a23-4b40-80d8-30ab4ca40e49</processLinkId>
            <processId>1.9545825f-4a3f-43f2-82b0-a6b75c82df6f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.71c071e9-1d16-443c-9c3a-7470f6079bbb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</toProcessItemId>
            <guid>5f9d16d7-6b61-40be-b704-88e8d950117d</guid>
            <versionId>c55a6468-1c82-4998-a62a-e53b85ec5630</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.71c071e9-1d16-443c-9c3a-7470f6079bbb</fromProcessItemId>
            <toProcessItemId>2025.a6adabe3-b833-42d3-a8fb-1a1a969cc1b5</toProcessItemId>
        </link>
    </process>
</teamworks>

