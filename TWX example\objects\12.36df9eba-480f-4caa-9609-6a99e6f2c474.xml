<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.36df9eba-480f-4caa-9609-6a99e6f2c474" name="IDCReversalRequest">
        <lastModified>1688661260422</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <classId>12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474</externalId>
        <dependencySummary isNull="true" />
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/NBEDCP","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{}],"shadow":[false]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["appInfo"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"appInfo","type":"{http:\/\/NBEC}AppInfo","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6"}}]},"name":"IDCReversalRequest"}],"id":"_12.1f01ac17-3025-45c9-953f-7db1ec783681"}</jsonData>
        <description isNull="true" />
        <guid>7d544049-a653-42a1-927c-390af877131c</guid>
        <versionId>317e6ac4-2627-4504-82ff-537a25747abb</versionId>
        <definition>
            <property>
                <name>appInfo</name>
                <description isNull="true" />
                <classRef>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="IDCReversalRequest">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name isNull="true" />
                <namespace isNull="true" />
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

