<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8ef1af07-5249-42da-b1a2-40f2e2ea490f" name="IDC Request Details UI 2">
        <lastModified>1692799205813</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7010980e-cfd5-4a7b-9702-c7ec6ede0631</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>5</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</description>
        <guid>f26a2b7c-0cf9-4496-965f-f1c2d0476533</guid>
        <versionId>161d88a3-89d1-4f29-8cc4-84d5dfcca131</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e7aaaea9-3552-4530-96fa-8036862f49eb</processParameterId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>183f504c-404c-4606-b71d-849ec5b65fd8</guid>
            <versionId>*************-4899-947e-b86e8192763b</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3c9b75ab-60ea-440a-8f49-ee607e397f68</processParameterId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b8248269-e936-47fa-b593-ef27a0be70f8</guid>
            <versionId>35bacdc0-b9ca-4fd2-be02-d73f2e57c806</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e3f55667-a3c9-467b-a02c-63d4e9f68e88</processParameterId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>abbfe4d3-e613-4cb6-8616-76aa8898f8f4</guid>
            <versionId>f34942ad-2d03-4a8b-87be-ea934b836b8d</versionId>
        </processParameter>
        <processVariable name="activitiesTextFilter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f93c6b14-cc96-4820-b48d-e5841bdf37ed</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a1d4322f-b1fb-442b-89ec-b2f6878af39e</guid>
            <versionId>76220d80-a17d-439f-8dca-14ed8e7cbd8d</versionId>
        </processVariable>
        <processVariable name="breadcrumbs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1cf614b9-39e3-46c9-ae11-2ee9928e2251</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ca42aea0-f6fb-487a-b04e-ee6227233100</guid>
            <versionId>752c8496-e560-4176-9d35-246afe5185d1</versionId>
        </processVariable>
        <processVariable name="buttonClicked_GanttView">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7ffec9c0-c3b0-4396-8663-77b50569e42b</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ef90a11e-8c08-4193-b841-421c2b673a86</guid>
            <versionId>05c98b32-4b4b-491d-b972-08158611172f</versionId>
        </processVariable>
        <processVariable name="currentDate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.efde0470-e569-48fa-8694-8416e1d18ebd</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>db22dbb3-b494-4dfd-9eaf-05db6b54a658</guid>
            <versionId>d728239c-6a17-4b64-900e-3e72123730e0</versionId>
        </processVariable>
        <processVariable name="follow">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.216bf768-d3af-440b-a5d4-b6265b99158c</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>237b3784-4703-45bb-98a2-29a79f631190</guid>
            <versionId>a5479288-6401-4db0-8ce0-cb9a12f7173d</versionId>
        </processVariable>
        <processVariable name="instanceName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.731f5963-05db-4492-a7bc-0b9319725dad</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>04e0f630-**************-d766741754ee</guid>
            <versionId>4b72e8d0-6c1e-42d7-9596-a4636665d960</versionId>
        </processVariable>
        <processVariable name="hideDocumentSection">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.32172cb1-02ff-4883-acbe-09810a3e4bf2</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b7dc12da-9378-4153-993d-df4167246f6d</guid>
            <versionId>964f1ad6-a551-4aa2-9fad-a351aa97c8cf</versionId>
        </processVariable>
        <processVariable name="instanceSummary">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ccc4421c-3bc8-4e40-8e73-af202f649ebb</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>0ba27466-a399-4ffe-9736-824ab864919c/12.9a9d479f-686b-484b-80a6-30b52aa4e935</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8f445ce4-c84c-46a5-a6e3-c9f0fcd136dc</guid>
            <versionId>45de1cfe-36b6-4bf1-bc9f-75377f1b3cdd</versionId>
        </processVariable>
        <processVariable name="selectedActivitiesCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9def62f6-5bdd-4100-b890-7810b79a2f4f</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3d5d29f4-b430-449b-b391-fb0c61cc9f5d</guid>
            <versionId>6a8987f1-f0ba-4a47-98ab-a7892eb29e3e</versionId>
        </processVariable>
        <processVariable name="activitiesCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f3651c5c-0356-4f07-bf4e-10ae78dd53e7</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8e1681f6-114f-45c9-8016-76731206feaa</guid>
            <versionId>5656e3ee-b082-4ea2-aac5-018fd8c7b5fd</versionId>
        </processVariable>
        <processVariable name="selectedTasksCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a4654f4c-a1dd-44e8-bd65-168e61b49f39</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>959b4ed4-9c64-4750-adbc-514e936fbcc8</guid>
            <versionId>fb224a07-63d0-4caf-8913-f2cf803cb8f4</versionId>
        </processVariable>
        <processVariable name="tasksCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d0b66d3e-5544-4dab-b714-f5be838fff10</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>80b7af9c-cdce-4f98-80d8-037f9c32a326</guid>
            <versionId>b06c1521-4aee-4325-a65a-d90a2f5bcffc</versionId>
        </processVariable>
        <processVariable name="selectedInstanceId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fb9a0f5a-5e7e-43f3-ba31-e72c01c76444</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e95e2cdb-542c-42f2-8773-3bb68490ee9c</guid>
            <versionId>c15c6354-9c9b-4442-9254-064973220b64</versionId>
        </processVariable>
        <processVariable name="automaticRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8d057aa6-7fb0-48ce-84e6-3f8b431dc9f8</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2492b51a-a83d-4508-a7dc-bee09d3490a8</guid>
            <versionId>75226e27-b8c3-4926-813f-e86158e50c81</versionId>
        </processVariable>
        <processVariable name="manualRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3bef4017-b4cb-4907-93cc-f59f4fc05b89</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9f5094bb-f9a1-416d-ba01-2c46ae0d798f</guid>
            <versionId>09c47fe3-ed56-4587-81bb-0c20b485c0ee</versionId>
        </processVariable>
        <processVariable name="failedSaveMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.00bc47bd-96f4-4d99-8def-373fe9f8ff8a</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c3c8a906-99dd-44c1-a2ab-bdfcca7d3a32</guid>
            <versionId>ea14101d-8ec2-4b3a-b5ce-09e11299905d</versionId>
        </processVariable>
        <processVariable name="boSaveFailedError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.650645b8-e642-4e3e-8923-f8190f26ea16</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d8fa7561-8636-40a9-bd70-f45128bb7e54</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7bb0a029-bdef-4156-9b7d-d9f873fae239</guid>
            <versionId>c4838451-91e4-40a6-a186-5048bdcae943</versionId>
        </processVariable>
        <processVariable name="canViewDiagram">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2657d0c1-ae90-42aa-87ef-c194123a6045</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>89e67715-2470-478f-9291-681f18ca42ce</guid>
            <versionId>810d6669-22d1-4f09-9210-b7375acbb4fa</versionId>
        </processVariable>
        <processVariable name="navigationURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1a9c1bee-5017-4115-a32f-f416f9fda155</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d9ec14b6-97c1-4aea-ad62-667c9b05fd58</guid>
            <versionId>99386954-ef3a-439f-a9c8-9e0d6683946b</versionId>
        </processVariable>
        <processVariable name="unsavedLocalChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9d1faab8-6393-4e0c-9ae5-d23b034f9c77</processVariableId>
            <description>This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4b8b347d-6a75-4ae0-92ab-8836874f2b79</guid>
            <versionId>12b078bd-87c3-4b2f-93ac-72f60450a104</versionId>
        </processVariable>
        <processVariable name="originalInput">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.364a797c-49fe-4b56-b169-7f46b8ef72fb</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>14434a78-0288-42f6-8f55-8d9aefcb60eb</guid>
            <versionId>67a0b951-8d0b-4234-9251-dd6b95b5055f</versionId>
        </processVariable>
        <processVariable name="incomingChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b6b8391c-f7f2-47a6-8c23-c083b2a7b5f8</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>01469b0a-f553-49a1-832a-d87e9ca998e1</guid>
            <versionId>f111015e-8670-452d-b569-acc7c7bfcc7f</versionId>
        </processVariable>
        <processVariable name="localChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f5382b31-7fba-487a-8b06-7e2385df7293</processVariableId>
            <description>This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d13fffe6-4322-48ed-8960-bceb8748127b</guid>
            <versionId>825ebf79-516c-4f14-864f-f4d75fc167f1</versionId>
        </processVariable>
        <processVariable name="dataSectionBoundaryEventType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f50fb9e9-6534-43b5-bb65-e746fc1636fb</processVariableId>
            <description>This variable indicates the type of boundary event triggered by the Data Section coach view.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>43a5a605-1bb4-47a8-b829-139f9a5e79f0</guid>
            <versionId>4076153f-4230-49da-b6ff-e66ad938321f</versionId>
        </processVariable>
        <processVariable name="helperScriptURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a08d313b-900d-41d6-9658-4ae79b7fd84e</processVariableId>
            <description>This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>da1993f3-d542-418c-839f-6d1c75e47a3f</guid>
            <versionId>6da46dff-0e77-4e15-8cf2-da405390e221</versionId>
        </processVariable>
        <processVariable name="incomingChangesMerged">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2c25f9ba-66c1-45b1-8f38-38875e54b93a</processVariableId>
            <description>This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</description>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f58e110f-6f59-4ff6-b084-0252c8abb046</guid>
            <versionId>cd030973-1887-4d31-97e6-a7f190eb99c0</versionId>
        </processVariable>
        <processVariable name="documentSectionVisibility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ff8d46a2-dcc8-4737-bf0b-46b5bfe807d7</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>98b6d4a7-78cc-4699-b7b8-a46b13a48cf1</guid>
            <versionId>10f900c6-85e3-4c4f-8b63-d05a77b5319b</versionId>
        </processVariable>
        <processVariable name="havePaymentTerm">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.078bab60-4e34-4290-9a13-29de7df92ac9</processVariableId>
            <description isNull="true" />
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2d8a24de-87cc-4d0c-8780-fe39bab80286</guid>
            <versionId>0c2b1394-05da-4a88-bddb-dcc08ab6f357</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.38227107-70e6-4d37-bd1d-86e29c74c751</processItemId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.05a68647-a795-4ab4-925d-05be5bd622e6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-38d6</guid>
            <versionId>0a97e36e-d517-4b1a-a927-bfeab8f021e6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.05a68647-a795-4ab4-925d-05be5bd622e6</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8f84e7ed-8b9b-4279-9b17-f45c3d9ba7d8</guid>
                <versionId>a0e91509-cb98-4422-8f84-e56fb7c7c2de</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.882fb93c-f81f-4bb7-ad89-a2a122882e47</processItemId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <name>Get Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a342cdb4-ca2f-4b3c-9f1d-0688b38e7396</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-38d9</guid>
            <versionId>17a66ba1-d2ff-45e2-a01a-61e3a6e85126</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a342cdb4-ca2f-4b3c-9f1d-0688b38e7396</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>dbf034b8-d7b9-4d90-a114-1941e357f74a</guid>
                <versionId>cd44f126-7190-4560-ad01-75e9a01cbde5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7010980e-cfd5-4a7b-9702-c7ec6ede0631</processItemId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.95c9d15a-a707-4237-8ecf-039791ca756d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-38d5</guid>
            <versionId>54b6e0e1-b08d-40b5-90f1-7a99c241adde</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca</processItemId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <name>Server Side Init Data</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.db239a1e-2523-45c8-9182-c03fedb5301a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-38d7</guid>
            <versionId>577b7a3d-494b-4180-b649-32a95d15c7bf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.db239a1e-2523-45c8-9182-c03fedb5301a</subProcessId>
                <attachedProcessRef>0ba27466-a399-4ffe-9736-824ab864919c/1.554dd7d5-ceb8-4548-91e5-788940b70e0d</attachedProcessRef>
                <guid>0f0e1a50-ccdb-4947-a8b1-6b5a2f4d51a0</guid>
                <versionId>46d5ce9b-c49e-4791-8e2b-a95832881134</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16</processItemId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <name>Update Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.88f9838f-5d3b-469b-b441-e02ae5303618</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-38d8</guid>
            <versionId>e2ad9d78-84ee-4a1f-a354-4ea5e45beef6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.88f9838f-5d3b-469b-b441-e02ae5303618</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>c3f43fd1-3e74-4635-a142-f4d7cd4f15ed</guid>
                <versionId>6a40d808-5cb3-4e18-bdff-bdcaaf2f42bd</versionId>
            </TWComponent>
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.09532f18-04c4-4a94-a181-ef01f1c01d09</resourceProcessLinkId>
            <resourceBundleGroupId>0ba27466-a399-4ffe-9736-824ab864919c/50.4b698a84-427b-4801-9c1d-18ddcc561bc6</resourceBundleGroupId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <guid>57adca89-66cb-4924-bc75-8d09dd117e9f</guid>
            <versionId>99488a75-1b51-4fb7-9d27-7fe46cb18be6</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.7010980e-cfd5-4a7b-9702-c7ec6ede0631</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="d946bef6-36c8-4ff4-8915-ba0e674dc894" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="IDC Request Details UI 2" id="1.8ef1af07-5249-42da-b1a2-40f2e2ea490f">
                    
                    
                    <ns16:documentation>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</ns16:documentation>
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="f6f10f5d-abe1-453e-8a78-c1a5fe20c965">
                            
                            
                            <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="32df8a79-401c-4273-b9bd-8a47b927c9af">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.19e090c7-e4b5-4e46-803d-ec8dd87ac7db</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="activitiesTextFilter" id="2056.f93c6b14-cc96-4820-b48d-e5841bdf37ed" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="breadcrumbs" id="2056.1cf614b9-39e3-46c9-ae11-2ee9928e2251" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="buttonClicked_GanttView" id="2056.7ffec9c0-c3b0-4396-8663-77b50569e42b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="currentDate" id="2056.efde0470-e569-48fa-8694-8416e1d18ebd" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="follow" id="2056.216bf768-d3af-440b-a5d4-b6265b99158c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceName" id="2056.731f5963-05db-4492-a7bc-0b9319725dad" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hideDocumentSection" id="2056.32172cb1-02ff-4883-acbe-09810a3e4bf2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935" isCollection="false" name="instanceSummary" id="2056.ccc4421c-3bc8-4e40-8e73-af202f649ebb" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedActivitiesCategory" id="2056.9def62f6-5bdd-4100-b890-7810b79a2f4f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="activitiesCategoriesSelectionList" id="2056.f3651c5c-0356-4f07-bf4e-10ae78dd53e7" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedTasksCategory" id="2056.a4654f4c-a1dd-44e8-bd65-168e61b49f39" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="tasksCategoriesSelectionList" id="2056.d0b66d3e-5544-4dab-b714-f5be838fff10" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedInstanceId" id="2056.fb9a0f5a-5e7e-43f3-ba31-e72c01c76444" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="automaticRefreshTrigger" id="2056.8d057aa6-7fb0-48ce-84e6-3f8b431dc9f8" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="manualRefreshTrigger" id="2056.3bef4017-b4cb-4907-93cc-f59f4fc05b89" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="failedSaveMessage" id="2056.00bc47bd-96f4-4d99-8def-373fe9f8ff8a" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54" isCollection="false" name="boSaveFailedError" id="2056.650645b8-e642-4e3e-8923-f8190f26ea16" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="canViewDiagram" id="2056.2657d0c1-ae90-42aa-87ef-c194123a6045" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="navigationURL" id="2056.1a9c1bee-5017-4115-a32f-f416f9fda155" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="unsavedLocalChanges" id="2056.9d1faab8-6393-4e0c-9ae5-d23b034f9c77">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="originalInput" id="2056.364a797c-49fe-4b56-b169-7f46b8ef72fb">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="incomingChanges" id="2056.b6b8391c-f7f2-47a6-8c23-c083b2a7b5f8">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="localChanges" id="2056.f5382b31-7fba-487a-8b06-7e2385df7293">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="dataSectionBoundaryEventType" id="2056.f50fb9e9-6534-43b5-bb65-e746fc1636fb">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable indicates the type of boundary event triggered by the Data Section coach view.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="helperScriptURL" id="2056.a08d313b-900d-41d6-9658-4ae79b7fd84e">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="incomingChangesMerged" id="2056.2c25f9ba-66c1-45b1-8f38-38875e54b93a">
                                
                                
                                <ns16:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</ns16:documentation>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="documentSectionVisibility" id="2056.ff8d46a2-dcc8-4737-bf0b-46b5bfe807d7" />
                            
                            
                            <ns16:exclusiveGateway default="2027.c40120ba-3676-4425-adab-b2457d6a8e17" gatewayDirection="Unspecified" name="Data Section Action?" id="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="919" y="235" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ae65cd0f-1a0d-4817-8a30-3f7baf708c0a</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.1e313e67-6141-45fd-92f9-116d892e5b16</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.3f5af4bf-08c3-4c06-ba8a-ec004432149d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.ab8e7574-9d7a-453f-820d-d359c19ab7e3</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.c40120ba-3676-4425-adab-b2457d6a8e17</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.9efb3c9f-7029-4f76-8cc8-3ebe03e9891b" name="Populate BO Save Error" id="2025.9897ea6b-7861-4019-b6e1-e00064a17dc9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1372" y="424" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9da5c4b9-8a75-4805-bd00-710dba822c2a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.9efb3c9f-7029-4f76-8cc8-3ebe03e9891b</ns16:outgoing>
                                
                                
                                <ns16:script>tw.system.coachValidation.populateFromBOSaveFailedError( tw.local.boSaveFailedError );</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.4a66f46b-45ab-484d-9019-8c2767ef1879" name="Init Data Change Support" id="2025.2231561d-1e9c-4428-ba28-f125aff308d0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="733" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b0c01cdf-3020-440f-9d46-ba3e97f53926</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4a66f46b-45ab-484d-9019-8c2767ef1879</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// Initialize variables that support data change syncrhonization.  &#xD;
	&#xD;
	// This can only be done once the URL for the helper functions is returned by the &#xD;
	// Server Sice Init Data.  Since this is can also be invoked as part of auto refresh the initialization&#xD;
	// should only happen if the variables are undefined.&#xD;
	&#xD;
	if(tw.local.originalInput == undefined &amp;&amp; tw.local.localChanges == undefined &amp;&amp; tw.local.incomingChanges == undefined){&#xD;
		initializeDataSyncronizationVariables(tw.local); // Intialize variables&#xD;
	}&#xD;
	&#xD;
}); &#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:subProcess triggeredByEvent="true" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Data Change" id="2025.ea4bc8a0-8f15-4b94-8b51-9acc4ec2bc04">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="261" y="463" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="2025.250af252-1968-41ee-bdc3-6ec0c59cf666">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:outgoing>2027.a8fbd55b-0d3f-4c8a-99f2-5bd51f002afe</ns16:outgoing>
                                    
                                    
                                    <ns3:dataNotificationEventDefinition>
                                        
                                        
                                        <ns16:extensionElements>
                                            
                                            
                                            <ns3:coachEventTriggers enable="true">
                                                
                                                
                                                <ns3:coachEventTriggerBinding id="fcca5235-7475-4761-a076-8cef639c8b39">
                                                    
                                                    
                                                    <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller2</ns3:coachEventPath>
                                                    
                                                    
                                                    <ns3:coachId>2025.bc231a34-597e-4426-b711-8697e592758b</ns3:coachId>
                                                    
                                                
                                                </ns3:coachEventTriggerBinding>
                                                
                                            
                                            </ns3:coachEventTriggers>
                                            
                                        
                                        </ns16:extensionElements>
                                        
                                    
                                    </ns3:dataNotificationEventDefinition>
                                    
                                
                                </ns16:startEvent>
                                
                                
                                <ns16:intermediateThrowEvent name="Stay on page" id="2025.66375f93-fc0a-495e-babf-c9118d86ce4c">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" />
                                        
                                        
                                        <ns3:navigationInstructions>
                                            
                                            
                                            <ns3:targetType>Default</ns3:targetType>
                                            
                                        
                                        </ns3:navigationInstructions>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:incoming>2027.e57beef1-d538-478e-a2b5-34712c2fedd0</ns16:incoming>
                                    
                                    
                                    <ns3:stayOnPageEventDefinition />
                                    
                                
                                </ns16:intermediateThrowEvent>
                                
                                
                                <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.e57beef1-d538-478e-a2b5-34712c2fedd0" name="Collect change information" id="2025.58a64578-dc4a-4507-9fa4-9ca9359a8ccf">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:nodeVisualInfo x="345" y="176" width="95" height="70" />
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                    
                                    <ns16:incoming>2027.a8fbd55b-0d3f-4c8a-99f2-5bd51f002afe</ns16:incoming>
                                    
                                    
                                    <ns16:outgoing>2027.e57beef1-d538-478e-a2b5-34712c2fedd0</ns16:outgoing>
                                    
                                    
                                    <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
	&#xD;
	// Calculate if there are any relevant incoming changes&#xD;
&#xD;
	// There are unsaved changes .  This can be detected by calling tw.system.coachUtils.hasLocallyChangedInputVars().  Here, however, this variable is&#xD;
	// set by the Data Section coach view when it detects that a child view's data binding has changed.&#xD;
	if(tw.local.unsavedLocalChanges ===  true || tw.system.coachUtils.hasLocallyChangedInputVars()){&#xD;
		&#xD;
		if(!tw.local.unsavedLocalChanges)&#xD;
			tw.local.unsavedLocalChanges = true;&#xD;
			&#xD;
		// Call the helper function to update the incoming and local changes.&#xD;
		checkIncomingChanges(tw.local, tw.system.coachUtils.getLocallyChangedVars(),  tw.system.dataChangeUtils.getIncomingVars())		          &#xD;
		&#xD;
	} else if(tw.system.coachUtils.getLocallyChangedVars().length &gt; 0) { // No pending changes to be merged, or local changes&#xD;
	&#xD;
		// Apply incoming changes to the local variables.&#xD;
		tw.system.dataChangeUtils.applyAllIncomingVars();&#xD;
		tw.local.incomingChangesMerged = true;  // Signal that changes were applied automatically.  The Data Section can listen and pop up a notification.&#xD;
	 &#xD;
	}&#xD;
&#xD;
}); </ns16:script>
                                    
                                
                                </ns16:scriptTask>
                                
                                
                                <ns16:sequenceFlow sourceRef="2025.250af252-1968-41ee-bdc3-6ec0c59cf666" targetRef="2025.58a64578-dc4a-4507-9fa4-9ca9359a8ccf" name="To Collect change information" id="2027.a8fbd55b-0d3f-4c8a-99f2-5bd51f002afe">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:linkVisualInfo>
                                            
                                            
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            
                                            
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            
                                            
                                            <ns13:showLabel>false</ns13:showLabel>
                                            
                                            
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            
                                            
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            
                                            
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                            
                                        
                                        </ns13:linkVisualInfo>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:sequenceFlow>
                                
                                
                                <ns16:sequenceFlow sourceRef="2025.58a64578-dc4a-4507-9fa4-9ca9359a8ccf" targetRef="2025.66375f93-fc0a-495e-babf-c9118d86ce4c" name="To Stay on page" id="2027.e57beef1-d538-478e-a2b5-34712c2fedd0">
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns13:linkVisualInfo>
                                            
                                            
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            
                                            
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            
                                            
                                            <ns13:showLabel>false</ns13:showLabel>
                                            
                                            
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            
                                            
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            
                                            
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                            
                                        
                                        </ns13:linkVisualInfo>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:sequenceFlow>
                                
                            
                            </ns16:subProcess>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.35b21b52-ca3a-4161-982e-912212524c75" name="Reset Pending Change Data" id="2025.554b280e-b9b6-4157-a4c4-3f1bf8b29f50">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="765" y="406" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b21e6131-56e6-45d1-b1da-1b45e8ec9441</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.8c8eb19d-fa49-4687-bd60-5240f3288326</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.35b21b52-ca3a-4161-982e-912212524c75</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The local variables have been refreshed with the latest changes from the server,&#xD;
	// or the instance UI has been saved.  There are no pending changes.  Reset merge data.&#xD;
		&#xD;
	resetDataSyncronizationVariables(tw.local); // Reset variables&#xD;
	          &#xD;
}); &#xD;
&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.759c5d6d-7798-4463-8fc4-47aa896f7eff" name="Merge Changes" id="2025.d4f02ea5-45d2-4d37-b974-9a609242f724">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1275" y="213" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c40120ba-3676-4425-adab-b2457d6a8e17</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.759c5d6d-7798-4463-8fc4-47aa896f7eff</ns16:outgoing>
                                
                                
                                <ns16:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The merge the server changes into the local variables based on the merge policy picked by the user.&#xD;
&#xD;
	// Determine what type of merge the user requested&#xD;
	var keepConflictingLocalChanges = false;&#xD;
	if(tw.local.dataSectionBoundaryEventType === "KEEP_LOCAL")&#xD;
		keepConflictingLocalChanges = true;&#xD;
		&#xD;
	merge(tw.local, keepConflictingLocalChanges); // Reset variables&#xD;
	          &#xD;
}); </ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.ccd79f64-632c-4e84-9727-c0564df21d70">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="771" y="519" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.35b21b52-ca3a-4161-982e-912212524c75</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:exclusiveGateway default="2027.6cc54417-66ac-4bb8-8afb-87a06b7821ce" gatewayDirection="Unspecified" name="Validation Error?" id="2025.29b3bfb4-5a4c-402c-9541-7b92bea1c02d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1162" y="317" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6e5eb8a8-4e5f-46fd-b554-ad2ae8d411c5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b0cf9e01-e57d-47df-a334-721e3b2bd33d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.6cc54417-66ac-4bb8-8afb-87a06b7821ce</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.6e5eb8a8-4e5f-46fd-b554-ad2ae8d411c5" name="Validation" id="2025.cc2efe0b-b465-4c13-95fd-5d896dac53c1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1025" y="298" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ab8e7574-9d7a-453f-820d-d359c19ab7e3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.6e5eb8a8-4e5f-46fd-b554-ad2ae8d411c5</ns16:outgoing>
                                
                                
                                <ns16:script>//  Check for validation errors in the instance data. For examle:&#xD;
// if (tw.local.name == "" ) {&#xD;
//    tw.system.coachValidation.addValidationError("tw.local.name", "Name must be specified");&#xD;
// }</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:endEvent name="End" id="2025.adc7b6e9-c90b-4163-9157-e728477c3d98">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1278" y="127" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>ToOtherDashboard</ns3:targetType>
                                        
                                        
                                        <ns3:targetURL>tw.local.navigationURL</ns3:targetURL>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.527b6505-ebed-44ce-8320-bc4348751057</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.1532c382-de12-438b-ba2c-8e3ebf44131e</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.c7a33776-9e0f-4139-a8b8-4ae4bf3847a3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1284" y="321" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b0cf9e01-e57d-47df-a334-721e3b2bd33d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.759c5d6d-7798-4463-8fc4-47aa896f7eff</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.a777a5c2-de6a-49ff-b607-7723696875dd</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.9efb3c9f-7029-4f76-8cc8-3ebe03e9891b</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.8c8eb19d-fa49-4687-bd60-5240f3288326" name="Update Process Variables" id="2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:mode>SaveBPDVariables</ns3:mode>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1166" y="406" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6cc54417-66ac-4bb8-8afb-87a06b7821ce</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8c8eb19d-fa49-4687-bd60-5240f3288326</ns16:outgoing>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b21e6131-56e6-45d1-b1da-1b45e8ec9441" name="Get Process Variables" id="2025.882fb93c-f81f-4bb7-ad89-a2a122882e47">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:mode>RefreshVariables</ns3:mode>
                                    
                                    
                                    <ns13:nodeVisualInfo x="765" y="293" width="95" height="70" />
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.3f5af4bf-08c3-4c06-ba8a-ec004432149d</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b21e6131-56e6-45d1-b1da-1b45e8ec9441</ns16:outgoing>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:endEvent name="End" id="2025.137fd275-471c-4724-bcf4-2566fc802c60">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="611" y="316" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a5ddf0b1-8829-4960-9b60-45a7d35d86ab</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Show Error" id="2025.7219018d-1e4a-4edb-ba58-7aa6d214c94a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="434" y="292" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b9885453-1be5-450e-b927-ace1b694604c</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a5ddf0b1-8829-4960-9b60-45a7d35d86ab</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>78b2ab0b-7b94-46db-8737-8ce857c45be3</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Output_Text1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1fa476df-8872-4212-8eb6-78a4f4ee0c5f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3b1ca332-b7d0-4ac8-81e7-589d693e342f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ebb4f8b6-cc75-4aaa-830c-7cf84c9e566e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6c1c2e85-08d7-47d3-89a6-94fe6b5d5c3d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@htmlOverrides</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.fc2d6e5b-da91-4e0a-b874-3ec8ace34c82</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.resource.Dashboards.defaultInstanceDetails.NoInstance</ns19:binding>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>272ece89-7eb6-4a83-8e48-e6e22e132ae9</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>okButton</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f6970509-b656-457c-86e2-91e5101ed142</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>OK</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e311d86e-e717-4ad1-8042-9806d00d5420</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>649a0446-7ab5-4bef-850a-d509921c432e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.36f46ec6-616b-4e38-86aa-fba20ec6f9b4</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.4de2a799-9e45-440a-b183-ad6484120561" gatewayDirection="Unspecified" name="Instance present?" id="2025.0aa1c70a-88bb-4bac-9535-63ab5e992936">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="148" y="196" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.19e090c7-e4b5-4e46-803d-ec8dd87ac7db</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4de2a799-9e45-440a-b183-ad6484120561</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.b9885453-1be5-450e-b927-ace1b694604c</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.5f9bdcfe-68b8-4b17-97fd-43dcc40cb75f" name="Client Side Init Data" id="2025.ab5221c4-56d6-4979-80f1-e5e0b948041b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="437" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6fdae935-7b33-4e68-b1e0-66bf673115b3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5f9bdcfe-68b8-4b17-97fd-43dcc40cb75f</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
&#xD;
tw.local.selectedInstanceId = tw.system.processInstance.id;&#xD;
if (tw.system.processInstance.parentActivityId &amp;&amp; (tw.system.processInstance.parentActivityId != null)) {&#xD;
	tw.local.documentSectionVisibility = "NONE";&#xD;
}&#xD;
&#xD;
var breadcrumbText = null;&#xD;
if (window.location.href.indexOf('breadcrumbText=') != -1)  {&#xD;
	breadcrumbText = window.location.href.substring(window.location.href.indexOf('breadcrumbText=')+15);&#xD;
	if (breadcrumbText.indexOf('&amp;') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('&amp;'));&#xD;
	}&#xD;
	if (breadcrumbText.indexOf('#') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('#'));&#xD;
	}&#xD;
}
                                &#xD;
var _debug = function(){};&#xD;
// enable following line to log into browser console&#xD;
//var _debug = console.log;&#xD;
&#xD;
try {&#xD;
	// check for breadcrumb&#xD;
	tw.local.breadcrumbs = [];&#xD;
	if (breadcrumbText) {&#xD;
		tw.local.breadcrumbs.push({&#xD;
			name: decodeURI(breadcrumbText),&#xD;
			value: '{"value":"back","navigationDestination":"BACK"}'&#xD;
		});&#xD;
	}&#xD;
    &#xD;
} catch (err) {&#xD;
	console.log("Error within Instance Details initialization: "+err);&#xD;
}&#xD;
&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:callActivity calledElement="1.554dd7d5-ceb8-4548-91e5-788940b70e0d" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b0c01cdf-3020-440f-9d46-ba3e97f53926" name="Server Side Init Data" id="2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca">
                                
                                
                                <ns16:documentation textFormat="text/plain">&lt;br _moz_editor_bogus_node="TRUE" /&gt;</ns16:documentation>
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="582" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5f9bdcfe-68b8-4b17-97fd-43dcc40cb75f</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.be5bf68b-11e3-4640-86a6-7b81b9af39ca</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.03f4d5c6-109c-48c7-b78e-d44ba3a8f98d</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.b0c01cdf-3020-440f-9d46-ba3e97f53926</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.79a0c5f3-08ff-45b1-a99d-3b828a60339d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0344478b-933e-4e7e-8122-349aaeb03f2d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.9918e74b-8220-44bd-a6df-213ae1f96c8f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceName</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.99c78e1f-41aa-4f5b-89e4-9f0449dbb478</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935">tw.local.instanceSummary</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8e52cfcb-db25-4bc4-a016-c8103a1cae4b</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.currentDate</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.83bae48e-f188-476a-bd4f-eee8360c042d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.canViewDiagram</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.b2e6d33b-136e-4c8e-b88f-abe718a89062</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.helperScriptURL</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="View Instance Details" id="2025.bc231a34-597e-4426-b711-8697e592758b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1055" y="103" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.c7c86ffa-cb63-412d-9b19-9f10e961a428</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.be5bf68b-11e3-4640-86a6-7b81b9af39ca</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.527b6505-ebed-44ce-8320-bc4348751057</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.ae65cd0f-1a0d-4817-8a30-3f7baf708c0a</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>312c9e8b-f749-4fc7-89d7-fdf42381a821</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Default_Instance_Details_Template1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f63e14ca-c966-45b2-882e-81bc2b9c8187</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Default Instance Details Template</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2bcc1159-507c-4aac-8738-9e6fba11a6e1</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d179dba8-0882-4a25-8d61-a0fb9c613201</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b690cd77-16bd-4efd-8563-59acf3690f2e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesTextFilter</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesTextFilter</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>039d54a8-4561-4592-86b1-80d78fb8c9af</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>addButtonDropdownSelectedItem</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>18d42900-50f6-42ca-8402-4e73c7058a9a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.breadcrumbs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e753769b-2683-4abf-8949-c0ee9983d269</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>dd8e737f-97d3-4649-8cce-85cd250b15f7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonClicked_GanttView</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.buttonClicked_GanttView</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>90082a09-f8d0-4d81-8f4f-d0978b834eeb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentPage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>75848139-b40b-4f67-83a2-5357fa191e10</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentAddButtonDropdownList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d78f38bf-38b3-4b96-81af-5cd2965170b3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>follow</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.follow</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>adf58ddf-5fba-4503-8024-a73d35ab180c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceName</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3d03a7a7-7ae9-4769-8ca9-19f74bf3af1f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceSummary</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceSummary</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>75345881-e617-4621-8d2a-23fa52df5af3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2ffcd419-851b-484b-82e6-21633b183a06</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedActivitiesCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedActivitiesCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8c5da619-d4d9-4239-8986-3a9ad08ba623</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedInstanceId</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a8a63797-2fbe-4edb-8a03-d1bfd0d586a5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedTasksCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedTasksCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fad4e682-3e04-4ca1-8f63-816879e3b34d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tasksCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.tasksCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>efb4cd08-f86e-40d7-80e9-8cc94e746452</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentDate</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.currentDate</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>db8724e7-9529-45a3-89a0-056aa3f5311e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hideDocumentSection</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hideDocumentSection</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f8422e0b-3194-4e87-854e-97532e1f6f24</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>refreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d509199f-5840-416e-8ba6-e492ec8116d7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>manualRefreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.manualRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c8dc1403-728a-42dc-8a46-e61f16c73af3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>canViewDiagram</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.canViewDiagram</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2b0fcd30-bde7-4bb8-8188-e237917bd138</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>navigationURL</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.navigationURL</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3fc5cd8c-d88c-443f-82d3-51cac3534953</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7e161c1f-1c84-4d83-8d2c-da98b99f38fd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>preventBoundaryEvent</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>12555f37-bbe3-4f0e-8f64-f2cda0392da9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentSectionVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>38852e9d-acc3-4f4b-8709-dd8666a7f53a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unSavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>241e065f-05b5-433a-8062-e5631cd79712</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedProcessInstanceChangesMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.resource.Dashboards.control.datasection.msg.unsaveddatanavigation</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.5f0e6839-f7f9-41a3-b633-541ba91f9b31</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>7b9be36f-a072-4a9f-8e28-5b82b638e3a5</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>bf0bed38-b0b3-4061-86b8-ed9a128a2f74</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>CustomDataSection</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4e2a3c9c-c1b4-46c1-8702-a759801b9a98</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Data Section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>dfa8c6bf-96af-4357-884c-feccd236438e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b1b4c0df-7ee7-4d78-80b3-b304b125bc71</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Show</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>fe1d0946-027e-4431-8e4e-cf61959e7246</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceId</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>812694fc-ec5b-4b28-8cc3-30b5f79a6df8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>refreshTrigger</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4e9831a9-2a2c-42ad-8496-64f8a2986996</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedUpdateMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ba75e726-3020-4deb-801d-97c773689bc1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedSaveMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>5ffdf52f-a81e-4102-8021-e42182614f3b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsible</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>71c3f7d5-5923-43a1-8afb-08d66ef58935</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceStatus</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>completed</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c56113bb-d385-4851-8f6d-845a7e4735cf</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>boundaryEventType</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>"DISCARD"</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>2671d70f-2db5-481a-8f4d-4bfab3eb4c78</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>72d5852e-9d8a-4e7d-874e-4c65a27b99e8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>localChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.localChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ea375ed0-449e-4fc0-832c-1b3b8aceeb78</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChangesMerged</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChangesMerged</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>64dfbd54-2f9b-47e8-82db-68f5ff41050d</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>977f2648-ba2a-456b-8a27-8e7b2daff4bc</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>originalInput</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.originalInput</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b7418614-9401-468e-87e3-62e9470ba109</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>editMode</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>7685bcfd-df0a-4bbf-8bcc-1724f74dd227</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsed</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.0678c7bb-7028-4bca-8111-e0e9977f294d</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding />
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>af5aa920-caaf-4618-895d-b379640f82ef</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:CustomHTML" version="8550">
                                                                
                                                                
                                                                <ns19:id>a41e3024-237d-42b7-88d4-1b9fd158ab75</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>CustomHTML1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a1080271-b07b-4980-827d-3245b005bf78</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@customHTML.contentType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>TEXT</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>df0edb7a-8b29-47c8-8be6-c9bf7d4750fb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@customHTML.textContent</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>&lt;style&gt;
.spark-ui {
    background: rgb(255, 255, 255);
   // width: 99%;
}
.CoachView.Panel&gt;.panel.SPARKPanel {
border-collapse: separate;
border-spacing: 0;
box-shadow: 0px 0px 12px #001B5929!important;
border-top: 5px solid #00643e!important;
border-radius: 10px!important;
}

.panel-primary.panel-dark&gt;.panel-heading {
background: transparent !important;
border-color: transparent;
color: #fff;
}

.panel-primary.panel-dark {
border-color: transparent!important;
}
.panel-primary.panel-dark &gt; .panel-heading .panel-title {
    color: rgb(0, 101, 71);
    font: normal normal 600 24px/45px Cairo;
}

.form-control {
    border-top-color: rgb(205, 205, 205);
    border-bottom-color: rgb(205,205,205);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 12px 12px;
    color: #181A1D !important;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.SPARKWell .stat-cell .bg-icon {
    line-height: normal;
    height: 100%;
    overflow: hidden;
 //   width: 97%;
    border-radius: inherit;
    box-shadow: 0px 0px 12px #001B5929!important;
    margin-bottom: 6px;
    border-radius: 10px!important;
    border-top: 5px solid !important;
   // margin-right: 49px;
}

//.form-control[disabled] {
//    background: rgb(242 242 242);
//}

.bg-success {
    background: #00654726 !important;
}
//.Single_Select select.placeHolder {
//    color: #0002037a;
//}

.panel-group .panel-heading+.panel-collapse .panel-body {
     border-top: 1px solid #fff;
}

.panel {
	     margin-bottom: 18px;
	     border-radius: 2px;
	     border-width: 0px;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {
	     background: #fff;
	     color: #006547;
	     border-color: #fff;
	     padding: 15px;
	     border-radius: 10px!important;
}

.CoachView.Collapsible_Panel&gt;.panel.SPARKCPanel {
	     border-collapse: collapse;
	     box-shadow: 0px 0px 22px #001B5929!important;
}
.SPARKCPanel &gt; .panel-heading {
    padding: 0px;
    border-top-left-radius: 1px;
    border-top-right-radius: 1px;
    box-shadow: 0px 0px 0px #001B5929!important;
    border-top: 5px solid #00643e!important;
    border-radius: 10px!important;
    border-spacing: 0;
    border-collapse: separate;
}
.panel-body {
    background: #fff;
    margin: 0;
    padding-bottom: 15px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 15px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.panel-group .panel {
    border-radius: 10px;
}

.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{
    width: 50%;
}
.radio3 &gt; input + span{
    display: inline-flex;
    flex-direction: row;
    align-items: center;
}
//.input-group.no-border&gt;.input-group-addon {
//    border-radius: 10px;
//    min-width: 54px;
//    height: 54px;
//    top: -25px;
//}

//.form-group .input{
//    padding-left:73px!important;
//}
//.Input_Group .outer, .control-label{
//padding-left:73px;
//}
//.Single_Select, .control-label{
//padding-left:20!important;
//}
.Input_Group .ContentBox {
    width: 100%;
    border-collapse: collapse;
    padding-left: 20px;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {
    background: #fff;
    color: #006547;
    border-color: #fff;
    border-radius: 10px!important;
    font-size: 18px;
    font-weight: 900;
    padding: 15px;
    font: normal normal 600 24px/45px Cairo;
}

.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role="img"]), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):active {
    padding: 12px 25px;
}
.btn-success, .btn-success:not([role="img"]):focus {
    color: #FFFFFF;
    fill: #006547;
    border-color: #006643;
    border-bottom-color: #006643;
    background: #006643;
    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;
    background-repeat: repeat-x;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
letter-spacing: 0px;
color: #181A1D !important;
opacity: 1;
font: normal normal normal 16px/20px Cairo;
background: transparent;
font-weight: bold;
border: 1px solid #000203;
}
//.Single_Select&gt;.form-group&gt;.input&gt;select {
//
//    border-top-style: ridge;
//    border-bottom-style: outset;
//    border-right-style: outset;
//    border-left-style: ridge;
//    border-top-width: revert;
//    border-left-width: revert;
//    border-bottom-width: revert;
//    border-right-width: revert;
//}
select.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {
    height: 40px;
    line-height: 1.33;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    letter-spacing: 0px;
    color: #181A1D !important;
    opacity: 1;
    font: 14px Cairo;
    background: transparent;
    font-weight: lighter;
}


.btn-success:not([role="img"]):hover {
    color: #ffffff;
    fill: #3D8A70;
    border-color: #3D8A70;
    border-bottom-color: #429c42;
    background: #3D8A70;
    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;
    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;
    background-repeat: repeat-x;
}

.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle:hover {
    background: rgb(255 255 255 / 15%);
}
.panel-group .SPARKTable &gt; .panel-heading {
    border-color: rgb(240, 240, 240);
    border-bottom-width: 2px
;
    border-bottom-style: solid;
    background: #006643 0% 0% no-repeat padding-box;
    color: white;
}
.Output_Text&gt;.form-group&gt;.input&gt;p {
  
    padding-right: 1em;
 
}


.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
   
    font-weight: 600;
}




[class*="BPM_Resp_"] .bpm-label-default {
 height: 30px;
    font: normal normal normal 16px/20px Cairo !important;
    letter-spacing: 0px;
    opacity: 1;
    color: #8B8C8E;
}




//.ECMPropertiesContainer{
//display: none; 
//}


.Output_Text&gt;.form-group&gt;.input&gt;p{
unicode-bidi: plaintext ;
text-align: inherit;
}

.CoachViewRTL {
 
    text-align: right !important;
}

.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&gt;* {
    text-align: right;
}

.noIScroll.alignJustify {
    text-align: inherit;
}
.radio3 &gt; input + span {

    unicode-bidi: plaintext;

}

.radio3 &gt; input:checked + span::after {
 
  top: calc(50% - 3px) !important;
}
.switcher-primary .switcher-state-off {
    color: #000203;
}
.CoachViewRTL .Single_Select &gt;{
 unicode-bidi: plaintext;

}

input[type="checkbox"]:checked[disabled] {
    right: 6%;
    opacity: 1;
    width: fit-content;
    left: 3px;
}

.Tab_Section&gt;div&gt;.nav-tabs-mnu {
    position: unset;
   }
   
a {
    color: #333;
    text-decoration: none;
}
.datepicker thead th {
   
    color: #006547;
&lt;/style&gt;</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>baee4c8d-1dce-4d5c-8a36-6c5a2921017d</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f27d60a2-98eb-4487-83a8-3932bce355af</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Tab section</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>94bd11b7-fca5-41a9-85b3-145bbb541004</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a5b5d0b6-f90b-4dcf-8e24-3b25f274c38a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:contentBoxContrib>
                                                                    
                                                                    
                                                                    <ns19:id>dfcf72bd-ccf5-4f18-89d3-f65509dbc3f9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>45786581-27b2-468f-88a6-d0543bb5c8a9</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f606ed0a-9aa6-4aae-84fe-ffb0eb3fad8a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Customer Information</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ca2d1a6-4409-4647-8bfc-19e697eca5d0</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7df5d7a3-3e51-4da8-8e2a-1cbb9f6cc57e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>be6d5372-bac2-4338-8fe8-36d6000659aa</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>3ee6cdf0-b785-4267-8fd3-736d7b0231e6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>441cabe2-3a46-451f-8e4f-0c00b7312fe6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>instanceview</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c388a619-e0d7-4081-83ae-358102374002</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ac08089b-fe45-4ca0-88de-14d5a1f46ece</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Basic Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f2f27ed7-7010-4246-8fe1-ef1ab6a25f92</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>345d391a-b83c-4675-831e-037eed5a2ecc</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ffba317c-8b55-4e92-8dee-44d2d34d588b</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>cce9d3d4-898b-4440-8b2f-cf1cce5c5319</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0dafba76-c784-41e0-858e-cd1fdb9582b3</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ccd42209-8ea9-4787-8016-5d44c58bc756</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>addBill</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>17da7864-9918-4496-8287-ba78026afc53</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>deleteBill</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>bc4b0019-24e5-4735-81c4-193202fdbc4f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>addInvoice</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>db56c9b0-8f8f-4aac-8c3e-b41115d1238c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bec7782b-c964-4a09-b74f-0ec737efa310</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>ad060494-5dd3-4154-825a-6465eddd50e4</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details__Branch_Compliance_Review1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>4bf834c8-32f2-4729-8a92-76071d2972aa</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Branch</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7a6c5a3b-8407-41c6-8d4a-17061cae6fcb</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>beee3dbe-93b3-4a63-8ffe-ab655cd9e0a1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>86c23f9e-1f49-4dae-85e8-5eb854e6b91c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.7020f6c3-7f8d-4052-81e6-d5b6b89f97e9</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>324aaf51-4804-4817-8b1c-ebc2af8f02f9</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details_Trade_FO1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>1e117c55-140f-474b-8354-7fd261d07f2d</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Trade FO</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>ebc55ad2-1138-4f36-85a2-f27e7942b15b</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>2128253b-8c50-45a8-891b-3608c56601c0</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>1741ec52-a475-4a1c-8bac-d0b1609ace5e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>abb07088-5b25-44e5-82e6-4d64ee865bee</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerms</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f4e59b23-f574-4c16-8c40-3d7b7705207c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>requestType</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>5c2a9a05-60d4-46ff-84dc-b8f01e004674</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>haveTradeFOReferenceNumber</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>e69eff61-321c-4c9a-8c88-d2d0a9b7c64e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>beneficiaryDetails</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.financialDetails.beneficiaryDetails</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.848ab487-8214-4d8b-88fd-a9cac5257791</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>ae4b12f1-9714-42bc-84ab-7c3b2e5ccd3b</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Products_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>a611ab59-0902-4902-845f-2e96eb05140c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Products Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>530982b8-4bea-49e7-87db-ebfed30698fe</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>5911acdb-7949-43e6-8210-a9e4a959f804</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7a9449c5-cfa3-4ba0-8c1c-8132fdfcce3c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bd0ada34-acf3-449a-91df-9aa363c2b280</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.productsDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>cb813196-c737-44b6-8cb4-d5209cdced8e</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>fe287dfc-85e0-47ce-85ff-4f5cc2aebd25</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>History</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8dcf0335-6d2d-4e44-8aee-f7342f639271</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>daf1b51e-5d34-4551-8d3a-edd5efd01dbf</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>4fef4a0f-16f3-41cd-871d-1bdc83e4ec44</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                
                                                                </ns19:contentBoxContrib>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16" parallelMultiple="false" name="Error" id="2025.76ed255c-7d9a-495a-bb80-5d6eaebaddcf">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1249" y="447" width="24" height="24" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.9da5c4b9-8a75-4805-bd00-710dba822c2a</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.1ded0c21-b3d2-4e04-adc1-2fc21c78e6ec" />
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54">tw.local.boSaveFailedError</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>false</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16" parallelMultiple="false" name="Error" id="2025.9826a307-913e-4d82-81c5-bc64d302b43e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1249" y="411" width="24" height="24" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.a777a5c2-de6a-49ff-b607-7723696875dd</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.fe10641c-251f-4598-add9-95ba56f2c013" />
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.failedSaveMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>false</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0aa1c70a-88bb-4bac-9535-63ab5e992936" targetRef="2025.5fbb0bc2-ff0a-48c8-b293-a2dfa40688b9" name="To Client-Side Script" id="2027.4de2a799-9e45-440a-b183-ad6484120561">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d" targetRef="2025.d4f02ea5-45d2-4d37-b974-9a609242f724" name="Merge Changes" id="2027.c40120ba-3676-4425-adab-b2457d6a8e17">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.9897ea6b-7861-4019-b6e1-e00064a17dc9" targetRef="2025.c7a33776-9e0f-4139-a8b8-4ae4bf3847a3" name="To Stay on page" id="2027.9efb3c9f-7029-4f76-8cc8-3ebe03e9891b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2231561d-1e9c-4428-ba28-f125aff308d0" targetRef="2025.4114878f-e6c3-470d-9509-c459e3056312" name="To Exclusive Gateway" id="2027.4a66f46b-45ab-484d-9019-8c2767ef1879">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca" targetRef="2025.2231561d-1e9c-4428-ba28-f125aff308d0" name="To Init Data Change Support" id="2027.b0c01cdf-3020-440f-9d46-ba3e97f53926">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d4f02ea5-45d2-4d37-b974-9a609242f724" targetRef="2025.c7a33776-9e0f-4139-a8b8-4ae4bf3847a3" name="To Stay on page" id="2027.759c5d6d-7798-4463-8fc4-47aa896f7eff">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.29b3bfb4-5a4c-402c-9541-7b92bea1c02d" targetRef="2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16" name="No" id="2027.6cc54417-66ac-4bb8-8afb-87a06b7821ce">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">  </ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.554b280e-b9b6-4157-a4c4-3f1bf8b29f50" targetRef="2025.ccd79f64-632c-4e84-9727-c0564df21d70" name="To Stay on page" id="2027.35b21b52-ca3a-4161-982e-912212524c75">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.668561d4-ae2d-451f-a5ec-7d7ca2ed1d16" targetRef="2025.554b280e-b9b6-4157-a4c4-3f1bf8b29f50" name="To Reset Pending Change Data" id="2027.8c8eb19d-fa49-4687-bd60-5240f3288326">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.882fb93c-f81f-4bb7-ad89-a2a122882e47" targetRef="2025.554b280e-b9b6-4157-a4c4-3f1bf8b29f50" name="To Stay on page" id="2027.b21e6131-56e6-45d1-b1da-1b45e8ec9441">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.cc2efe0b-b465-4c13-95fd-5d896dac53c1" targetRef="2025.29b3bfb4-5a4c-402c-9541-7b92bea1c02d" name="To Validation Error" id="2027.6e5eb8a8-4e5f-46fd-b554-ad2ae8d411c5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ab5221c4-56d6-4979-80f1-e5e0b948041b" targetRef="2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca" name="To Server Side Init Data" id="2027.5f9bdcfe-68b8-4b17-97fd-43dcc40cb75f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="32df8a79-401c-4273-b9bd-8a47b927c9af" targetRef="2025.0aa1c70a-88bb-4bac-9535-63ab5e992936" name="To Instance present?" id="2027.19e090c7-e4b5-4e46-803d-ec8dd87ac7db">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.7219018d-1e4a-4edb-ba58-7aa6d214c94a" targetRef="2025.137fd275-471c-4724-bcf4-2566fc802c60" name="To End" id="2027.a5ddf0b1-8829-4960-9b60-45a7d35d86ab">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="d553202e-ae49-48be-8bd0-a5ac7fdd5624">
                                        
                                        
                                        <ns3:coachEventPath>okButton</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0aa1c70a-88bb-4bac-9535-63ab5e992936" targetRef="2025.7219018d-1e4a-4edb-ba58-7aa6d214c94a" name="No" id="2027.b9885453-1be5-450e-b927-ace1b694604c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.processInstance == null</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.29b3bfb4-5a4c-402c-9541-7b92bea1c02d" targetRef="2025.c7a33776-9e0f-4139-a8b8-4ae4bf3847a3" name="Yes" id="2027.b0cf9e01-e57d-47df-a334-721e3b2bd33d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length != 0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.9826a307-913e-4d82-81c5-bc64d302b43e" targetRef="2025.c7a33776-9e0f-4139-a8b8-4ae4bf3847a3" name="To Stay on page" id="2027.a777a5c2-de6a-49ff-b607-7723696875dd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.76ed255c-7d9a-495a-bb80-5d6eaebaddcf" targetRef="2025.9897ea6b-7861-4019-b6e1-e00064a17dc9" name="To Client-Side Script" id="2027.9da5c4b9-8a75-4805-bd00-710dba822c2a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bc231a34-597e-4426-b711-8697e592758b" targetRef="2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca" name="Automatic Refresh" id="2027.be5bf68b-11e3-4640-86a6-7b81b9af39ca">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="c2b3e160-b0d6-49f8-afac-0f393a414f24">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d" targetRef="2025.882fb93c-f81f-4bb7-ad89-a2a122882e47" name="Discard Local Changes" id="2027.3f5af4bf-08c3-4c06-ba8a-ec004432149d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.dataSectionBoundaryEventType == "DISCARD"</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d" targetRef="2025.cc2efe0b-b465-4c13-95fd-5d896dac53c1" name="Save Data" id="2027.ab8e7574-9d7a-453f-820d-d359c19ab7e3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.dataSectionBoundaryEventType == "SAVE"</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bc231a34-597e-4426-b711-8697e592758b" targetRef="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d" name="To Exclusive Gateway" id="2027.ae65cd0f-1a0d-4817-8a30-3f7baf708c0a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="12afeb95-eda4-463b-ab8e-8593d1f77ab4">
                                        
                                        
                                        <ns3:coachEventPath>CustomDataSection</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bc231a34-597e-4426-b711-8697e592758b" targetRef="2025.adc7b6e9-c90b-4163-9157-e728477c3d98" name="To End" id="2027.527b6505-ebed-44ce-8320-bc4348751057">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="ad27bddd-079b-4e99-92ee-bd4beffae415">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Navigation_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.6fdae935-7b33-4e68-b1e0-66bf673115b3" name="Client-Side Script" id="2025.5fbb0bc2-ff0a-48c8-b293-a2dfa40688b9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="315" y="103" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4de2a799-9e45-440a-b183-ad6484120561</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.6fdae935-7b33-4e68-b1e0-66bf673115b3</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.attachment == null) {&#xD;
	tw.local.attachment = [];&#xD;
	tw.local.attachment[0] = {};&#xD;
	tw.local.attachment[0].name = "Customer Request";&#xD;
	tw.local.attachment[0].description = "Customer Request";&#xD;
	tw.local.attachment[0].arabicName = "طلب العميل" ;&#xD;
	&#xD;
	tw.local.attachment[1] = {};&#xD;
	tw.local.attachment[1].name = "Correspondent cover letter";&#xD;
	tw.local.attachment[1].description = "Correspondent cover letter";&#xD;
	tw.local.attachment[1].arabicName = "خطاب المراسل" ;&#xD;
	&#xD;
	tw.local.attachment[2] = {};&#xD;
	tw.local.attachment[2].name = "Invoice";&#xD;
	tw.local.attachment[2].description = "Invoice";&#xD;
	tw.local.attachment[2].arabicName = "فاتورة" ;&#xD;
	&#xD;
	tw.local.attachment[3] = {};&#xD;
	tw.local.attachment[3].name = "Transport document";&#xD;
	tw.local.attachment[3].description = "Transport document";&#xD;
	tw.local.attachment[3].arabicName = "مستند النقل" ;&#xD;
	&#xD;
	tw.local.attachment[4] = {};&#xD;
	tw.local.attachment[4].name = "Packing list";&#xD;
	tw.local.attachment[4].description = "Packing list";&#xD;
	tw.local.attachment[4].arabicName = "قائمة التعبئة" ;&#xD;
	&#xD;
	tw.local.attachment[5] = {};&#xD;
	tw.local.attachment[5].name = "Weight list";&#xD;
	tw.local.attachment[5].description = "Weight list"&#xD;
	tw.local.attachment[5].arabicName = "قائمة الاوزان" ;&#xD;
	&#xD;
	tw.local.attachment[6] = {};&#xD;
	tw.local.attachment[6].name = "Certificate of origin";&#xD;
	tw.local.attachment[6].description = "Certificate of origin";&#xD;
	tw.local.attachment[6].arabicName = "شهادة المنشأ" ;&#xD;
	&#xD;
	tw.local.attachment[7] = {};&#xD;
	tw.local.attachment[7].name = "Certificate of analysis";&#xD;
	tw.local.attachment[7].description = "Certificate of analysis";&#xD;
	tw.local.attachment[7].arabicName = "شهادة التحليل" ;&#xD;
	&#xD;
	tw.local.attachment[8] = {};&#xD;
	tw.local.attachment[8].name = "Inspection certificate";&#xD;
	tw.local.attachment[8].description = "Inspection certificate";&#xD;
	tw.local.attachment[8].arabicName = "شهادة التفتيش" ;&#xD;
	&#xD;
	tw.local.attachment[9] = {};&#xD;
	tw.local.attachment[9].name = "Insurance policy / certificate";&#xD;
	tw.local.attachment[9].description = "Insurance policy / certificate";&#xD;
	tw.local.attachment[9].arabicName = "شهادة / بوليصة التأمين" ;&#xD;
	&#xD;
	tw.local.attachment[10] = {};&#xD;
	tw.local.attachment[10].name = "Bill of exchange/draft";&#xD;
	tw.local.attachment[10].description = "Bill of exchange/draft";&#xD;
	tw.local.attachment[10].arabicName = "الكمبيالة" ;&#xD;
	&#xD;
	tw.local.attachment[11] = {};&#xD;
	tw.local.attachment[11].name = "Compliance ticket";&#xD;
	tw.local.attachment[11].description = "Compliance ticket";&#xD;
	tw.local.attachment[11].arabicName = "موافقة الإلتزام" ;&#xD;
	&#xD;
	tw.local.attachment[12] = {};&#xD;
	tw.local.attachment[12].name = "Form 4";&#xD;
	tw.local.attachment[12].description = "Form 4";&#xD;
	tw.local.attachment[12].arabicName = "نموذج 4 للمستوردين" ;&#xD;
	&#xD;
	tw.local.attachment[13] = {};&#xD;
	tw.local.attachment[13].name = "Customs Letter";&#xD;
	tw.local.attachment[13].description = "Customs Letter";&#xD;
	tw.local.attachment[13].arabicName = "خطاب الجمارك" ;&#xD;
tw.local.idcRequest = {};&#xD;
&#xD;
tw.local.idcRequest.countryOfOrigin = {};&#xD;
&#xD;
tw.local.idcRequest.appInfo = {};&#xD;
&#xD;
tw.local.idcRequest.appInfo.branch = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.HSProduct = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.incoterms = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.CBECommodityClassification = {};&#xD;
&#xD;
tw.local.idcRequest.productsDetails.shipmentMethod = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.paymentTerms = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.financialDetails.usedAdvancePayment = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.beneficiaryDetails = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.beneficiaryDetails.country = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.executionHub = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.documentCurrency = {};&#xD;
&#xD;
tw.local.idcRequest.financialDetails.sourceOfFunds = {};&#xD;
&#xD;
tw.local.idcRequest.IDCRequestType = {};&#xD;
&#xD;
tw.local.idcRequest.billOfLading = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.importPurpose = {};&#xD;
tw.local.idcRequest.IDCRequestNature = {};&#xD;
&#xD;
&#xD;
tw.local.idcRequest.customerInformation = {};&#xD;
&#xD;
tw.local.idcRequest.invoices = [];&#xD;
&#xD;
&#xD;
tw.local.idcRequest.productCategory = {};&#xD;
tw.local.idcRequest.documentsSource = {};&#xD;
&#xD;
tw.local.idcRequest.paymentTerms = {};&#xD;
&#xD;
tw.local.idcRequest.approvals = {};&#xD;
&#xD;
tw.local.idcRequest.appLog = {};&#xD;
&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5fbb0bc2-ff0a-48c8-b293-a2dfa40688b9" targetRef="2025.ab5221c4-56d6-4979-80f1-e5e0b948041b" name="To Client-Side Script" id="2027.6fdae935-7b33-4e68-b1e0-66bf673115b3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="With Attachment View Instance Details" id="2025.20d0f3b3-192e-428f-8c29-1fd7c684ed27">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="974" y="-87" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.64a27ff7-bcc2-428a-8323-be765e20df02</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.1e313e67-6141-45fd-92f9-116d892e5b16</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.03f4d5c6-109c-48c7-b78e-d44ba3a8f98d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.1532c382-de12-438b-ba2c-8e3ebf44131e</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>6d40d66b-ee05-4044-8ade-b213a310d38f</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Default_Instance_Details_Template1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6bb52122-bbbe-4e2b-88c0-10d1ce69519a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Default Instance Details Template</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6bc6023a-f319-4cad-848e-6db8befff612</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b2c4d491-3789-4c77-8b52-ac7c15e10f32</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Show</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a8458e0d-f0eb-4c9a-86fe-8f3c671b2ca2</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesTextFilter</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesTextFilter</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9eb91e7e-d4d6-4e5a-8b85-42fc910fd987</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>addButtonDropdownSelectedItem</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>442fe2dd-3d20-4fd0-88c4-c9c3842caa43</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.breadcrumbs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>943deb11-cca3-43b9-80da-cd9071dd365b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>breadcrumbText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>37d667ad-de89-4b31-8d18-3368e6fefcca</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonClicked_GanttView</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.buttonClicked_GanttView</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>47b6b9f8-605c-4d82-8063-7e4fd46ee968</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentPage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f368d64e-cbc8-41d9-8cb7-7ca8aea28e13</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentAddButtonDropdownList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7d52f37e-fd26-485d-85b0-d78bb6340a1d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>follow</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.follow</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d2fede42-9d24-4df3-810c-e2dde4079cb0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceName</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>db4ee03c-d2c9-49db-8b89-0a240ccb3d8b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>instanceSummary</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.instanceSummary</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9e926513-9fca-4a1b-872c-c68c268c27fc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>activitiesCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.activitiesCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f773d12d-6063-4763-8665-456217c27aa3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedActivitiesCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedActivitiesCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7a65ee97-c4f2-4352-8c8e-7d04e0a7ba31</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedInstanceId</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2a452fec-e656-4259-8673-0eb44c91d479</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedTasksCategory</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedTasksCategory</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7901c347-cd46-4bf1-8fc8-0a97482b90f5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tasksCategoriesSelectionList</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.tasksCategoriesSelectionList[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>22dd432a-b008-4fb5-836c-c7c3dddc1c3d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>currentDate</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.currentDate</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fc0b1ca5-b4c9-452a-895e-66e3d98314a8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hideDocumentSection</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hideDocumentSection</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9fdf0efe-5a33-4238-8b53-2d8411fa6b83</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>refreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d732ac5b-0e12-4591-8c47-69aab9dd2977</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>manualRefreshTrigger</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.manualRefreshTrigger</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a23a582f-8eac-412a-8cc3-2da088e37ff5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>canViewDiagram</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.canViewDiagram</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>124656cc-5105-4197-8f3c-7c1804dd6235</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>navigationURL</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.navigationURL</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>34a7fafe-4c08-40f8-8fb9-c7ce3a1fa5d3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ce13fed7-3342-478e-8e84-cac7b3ad1a3c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>preventBoundaryEvent</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8440f952-9342-4749-86f0-86235a5bdb9f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>documentSectionVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>21da54fa-4530-447d-83c9-034ed9be2cbe</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unSavedLocalChanges</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bff8d14b-9495-41e5-8e38-c8eac3229413</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>unsavedProcessInstanceChangesMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.resource.Dashboards.control.datasection.msg.unsaveddatanavigation</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.5f0e6839-f7f9-41a3-b633-541ba91f9b31</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>ca158dc7-4aac-4e47-8cb2-73984f03a492</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>09103815-dfd6-4f33-8607-0561014ce771</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>CustomDataSection</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>bd65e501-38f8-4fde-8cab-e192dcd78151</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Data Section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>fd80b6db-98ec-4d73-845c-26c2c323bebf</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>cd094414-6f91-42c3-873b-516b5aa8f244</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Show</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>3f577ce5-e734-4305-8e85-3dcd516b7311</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceId</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.selectedInstanceId</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b70997b1-1d58-45f6-8812-f87bb46f822c</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>refreshTrigger</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.automaticRefreshTrigger</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>58304228-fc82-4621-8465-7b012051b5f1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedUpdateMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>9eed4b35-cbb1-44ea-8882-2f9bc6bf1b2b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>failedSaveMessage</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.failedSaveMessage</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4d70cc26-39f6-4b64-817e-a7ab51d1af92</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsible</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>af8e8ebe-ecb6-4425-8eee-905babd83453</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>instanceStatus</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>completed</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c5733400-8d25-4fc7-851c-579a1712be3e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>boundaryEventType</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>"DISCARD"</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>static</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ed8c74ff-c13c-4277-8f94-2526690d862f</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>8eccb4b0-e9aa-421c-851f-2b8481757d9b</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>localChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.localChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d1a9b5eb-9032-4f81-8317-3fc5992b2b54</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>incomingChangesMerged</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.incomingChangesMerged</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>84b7217d-928a-4b1e-8e7c-55704c085ee1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>unsavedLocalChanges</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.unsavedLocalChanges</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>234b3582-ff33-4920-82aa-971d2fd8c304</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>originalInput</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.originalInput</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>4afc566f-0a85-40df-8f6f-24fec59809e0</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>editMode</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>7980e29e-4124-4118-8206-c0957b383690</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>collapsed</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.0678c7bb-7028-4bca-8111-e0e9977f294d</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding />
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>0a5b08da-8634-4b1a-8b55-d76e254a8930</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>9b77cf0a-410c-45fe-8e2d-db887240125a</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>331aca27-c535-454e-8b5b-865a19fef40f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Tab section</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>228ac7c0-ad3b-452c-8977-09edc25ebe2c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c184f0f5-2b50-4251-875f-277b6ad22c2d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:contentBoxContrib>
                                                                    
                                                                    
                                                                    <ns19:id>40a1dc59-38cf-4414-83c1-bf1774bebf7f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>91c51c2d-a3cd-4e8d-89cf-d9d7ebd38723</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>3587380f-6811-476c-8ff2-9b781f77da6f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Customer Information</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>81260423-9338-481a-865c-d4a8d6512006</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>bcbeb859-fb9b-4922-8c2a-eaf60abf219a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>69d464be-86f6-4fcb-8708-f7b0a1342c21</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>11ee9b0f-c41b-4ae9-8d79-2f11d8262a82</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f797224b-7a6a-4e12-8b34-d80d55f4af49</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>instanceview</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>1f505734-0989-459d-898e-cb1e0fb27f3f</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>d116d48e-0b0e-49ab-820d-5b30871f499c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Basic Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>85cf0c81-f35b-43e3-8bcd-f25a0f25234f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>eff90776-6c76-4519-875e-0889ba571e22</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>22fe86bf-efd7-4cee-8a67-9c6e652958e1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>true</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b61d9e52-ced5-4a3b-8ef3-fe8ce6156a2c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7e7d08db-9d4a-4813-8aa9-bc13256140a5</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bec7782b-c964-4a09-b74f-0ec737efa310</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>48b01f20-52c6-43f8-8032-fc14afd2d9da</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details__Branch_Compliance_Review1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>dcd28ec6-6235-4622-8650-12d1830fcc6e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Branch</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>79edc659-54b9-495c-8604-54566be1e872</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>eda95500-b3f3-4c6b-86b6-76b1b500b200</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ad14872-c2f9-45fa-81fc-c573551b10cc</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.7020f6c3-7f8d-4052-81e6-d5b6b89f97e9</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>f4e5f6a0-1699-4b6e-8bf7-7303c5289fe7</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Financial_Details_Trade_FO1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>877ce0d3-34e5-44ed-8e79-513b20eab074</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Financial Details Trade FO</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>c3e4779a-3b93-47b1-80a4-4331ee65df6a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>55ea59b6-04b5-477e-8a66-05f688c2284c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>876fc54b-3356-4a79-813b-d5eaf73c510a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>333d1b41-e335-46cb-8fb4-8d1f23417b3f</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>havePaymentTerms</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.havePaymentTerm</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>129cdc90-0391-47b6-8e81-89dfc27df67e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>requestType</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>7709013e-148a-4cb1-8517-8e2d71b76620</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>beneficiaryDetails</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.idcRequest.financialDetails.beneficiaryDetails</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>15616d3e-d8b0-4e38-808b-6adaf55a411a</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>haveTradeFOReferenceNumber</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.848ab487-8214-4d8b-88fd-a9cac5257791</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>22c75529-8c9c-4cb2-8f87-cae369e70a75</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Products_Details1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b5e9f407-b9c4-42cb-8453-e690928932de</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Products Details</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>828e66a8-1b67-4416-8c0c-9c2bee5900f2</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0bfc1ed3-7ec4-4336-8629-f446eec5d58c</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>03b54376-838a-40dd-860f-3cecff4ced15</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.bd0ada34-acf3-449a-91df-9aa363c2b280</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.productsDetails</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c31ac594-e3dd-43bf-8e25-4dfddd9fe5c0</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>31630e65-71c8-4b3a-888d-80b0838577d5</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>Attachment</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0663c93a-2edd-4956-8d58-6bab8317bca1</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>10f054dd-d5c6-428c-880d-c2ba0e708687</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>669f09e1-2230-4e95-82a8-8d4c9a9a04b4</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility.script</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:valueType>static</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>9154cbd2-dd1e-43ad-8b33-1e4e591f07de</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>visiable</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>94c4354d-a744-40d5-8a8a-5821637167da</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canDelete</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>dfff13cc-419e-47fa-84d0-091584bd19a3</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canCreate</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>8ca8bc39-dba9-43a7-8077-f2ee37fb4d98</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>canUpdate</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>false</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>c8ac15be-97ca-4ad1-8563-ddb4cfecd1ba</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>ECMproperties</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                            
                                                                            
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                    
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        
                                                                        
                                                                        <ns19:id>c252cf11-ab75-494d-8770-8dfc6da1c0a3</ns19:id>
                                                                        
                                                                        
                                                                        <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>f248a611-0847-4f2f-81a4-0f57c20b5737</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>History</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>b1cdfa44-38aa-4496-854c-4cf81ef319a6</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value />
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>307b63b7-93fa-437a-81bc-c4047526108e</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>SHOW</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:configData>
                                                                            
                                                                            
                                                                            <ns19:id>0fa431b6-dc79-454d-81d8-b9445146a1f9</ns19:id>
                                                                            
                                                                            
                                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                                            
                                                                            
                                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                            
                                                                        
                                                                        </ns19:configData>
                                                                        
                                                                        
                                                                        <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                        
                                                                        
                                                                        <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                                        
                                                                    
                                                                    </ns19:contributions>
                                                                    
                                                                
                                                                </ns19:contentBoxContrib>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.20d0f3b3-192e-428f-8c29-1fd7c684ed27" targetRef="2025.147809cb-50da-4d59-ace3-fd75fc4e8d1d" name="Copy of To Exclusive Gateway" id="2027.1e313e67-6141-45fd-92f9-116d892e5b16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns3:coachEventBinding id="9645994f-dc22-46e4-84c0-94102a28c1e1">
                                        
                                        
                                        <ns3:coachEventPath>CustomDataSection</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.20d0f3b3-192e-428f-8c29-1fd7c684ed27" targetRef="2025.c6a3cfdb-07de-4a83-b31c-cb5eee94e2ca" name="Copy of Automatic Refresh" id="2027.03f4d5c6-109c-48c7-b78e-d44ba3a8f98d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns3:coachEventBinding id="58cab8f0-b130-4e7b-b232-7cb94e0c14a5">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.20d0f3b3-192e-428f-8c29-1fd7c684ed27" targetRef="2025.adc7b6e9-c90b-4163-9157-e728477c3d98" name="Copy of To End" id="2027.1532c382-de12-438b-ba2c-8e3ebf44131e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns3:coachEventBinding id="b4605fbe-3a74-46b8-84d9-8bbe142546e8">
                                        
                                        
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Navigation_Controller1</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.64a27ff7-bcc2-428a-8323-be765e20df02" name="Exclusive Gateway" id="2025.4114878f-e6c3-470d-9509-c459e3056312">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="843" y="103" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4a66f46b-45ab-484d-9019-8c2767ef1879</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c7c86ffa-cb63-412d-9b19-9f10e961a428</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.64a27ff7-bcc2-428a-8323-be765e20df02</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4114878f-e6c3-470d-9509-c459e3056312" targetRef="2025.bc231a34-597e-4426-b711-8697e592758b" name="To View Instance Details" id="2027.c7c86ffa-cb63-412d-9b19-9f10e961a428">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.idcRequest.appInfo.instanceID=="" || tw.local.idcRequest.appInfo.instanceID==null</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4114878f-e6c3-470d-9509-c459e3056312" targetRef="2025.20d0f3b3-192e-428f-8c29-1fd7c684ed27" name="To With Attachment View Instance Details" id="2027.64a27ff7-bcc2-428a-8323-be765e20df02">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="havePaymentTerm" id="2056.078bab60-4e34-4290-9a13-29de7df92ac9" />
                            
                            
                            <ns3:htmlHeaderTag id="f16cff9c-f095-4524-9208-e4d813f0f48c">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>InstanceDetailsUI</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyInputs="false" ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.4b698a84-427b-4801-9c1d-18ddcc561bc6</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.46923c36-435d-480a-8627-cca9772b6a02</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.e7aaaea9-3552-4530-96fa-8036862f49eb" />
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.3c9b75ab-60ea-440a-8f49-ee607e397f68" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.e3f55667-a3c9-467b-a02c-63d4e9f68e88" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ec6e86bf-941e-4283-9012-525bccb693ec</processLinkId>
            <processId>1.8ef1af07-5249-42da-b1a2-40f2e2ea490f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7010980e-cfd5-4a7b-9702-c7ec6ede0631</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.38227107-70e6-4d37-bd1d-86e29c74c751</toProcessItemId>
            <guid>7adc7521-7bb8-4e2f-a621-cdd1216a8a9e</guid>
            <versionId>b29a4d34-b912-446f-ba10-f09899c8f238</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.7010980e-cfd5-4a7b-9702-c7ec6ede0631</fromProcessItemId>
            <toProcessItemId>2025.38227107-70e6-4d37-bd1d-86e29c74c751</toProcessItemId>
        </link>
    </process>
</teamworks>

