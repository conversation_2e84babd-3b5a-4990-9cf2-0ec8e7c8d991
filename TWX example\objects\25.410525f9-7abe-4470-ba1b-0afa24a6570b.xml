<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.410525f9-7abe-4470-ba1b-0afa24a6570b" name="IDC Withdrawal">
        <lastModified>1688661261441</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <bpdId>25.410525f9-7abe-4470-ba1b-0afa24a6570b</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="081d5f3c-d5de-4e84-ad9a-90fa514935da" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="IDC Withdrawal" id="25.410525f9-7abe-4470-ba1b-0afa24a6570b" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot;IDC Withdrawal:&amp;quot; + tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="true" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at16840767503881688664857806"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="edecf92e-4feb-405b-ba11-403a50a50c7e" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.a6e09805-0e21-4bb5-8e25-1aa9a5db67ad" epvProcessLinkId="12e166b6-bd18-43c4-864c-3dd3fa9e3e03" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="dfcbfb7e-17c1-4e11-ac52-c4c11ac7cbc0" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="643881b5-9b3d-427d-847e-8dab9cd96718" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="9decb341-4972-4421-94b5-0938c1f6c213"&gt;&lt;ns15:lane name="Branch / Hub Maker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="7f803f55-b6d5-4453-94e1-efed46ec424e" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;ac3fe08d-b76e-44b4-a966-16bacd8ae377&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c8d6fa0c-c1e0-4c5d-b681-d17f6fc17f29&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;92af17c0-8419-43eb-b7e3-16359901304b&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;a57fac1e-a71d-4df3-a654-9725a797579b&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Compliance Representative" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="ef286b70-b2f8-4139-a0b2-f5ffda73444e" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;c614e2f8-baca-448d-a97f-9c25fb318f84&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;56f9d7bb-44f6-43b9-a1a0-2ee94dacb342&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;39c68c9f-038f-42d4-b48b-4b13a8e1cff0&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Trade Front Officer" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="2a72e32e-8806-4f87-a73e-b0a40d829a98" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;253f35e3-e912-4dac-8ed0-0523fc757e86&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;7f962f9e-42ad-461f-8430-4a24499eb467&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;f435b3e7-ed20-4802-a37c-696885725368&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Maker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="7aee63b1-1367-49bf-8a7b-8ace78ef653e" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="603" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;1a9bffe3-0876-4628-a537-7ff1771d8cfb&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Checker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="a0ed0a24-5be1-4a64-a54f-8e0e9d1cf347" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="804" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;d1299553-ba30-4ac6-ae1f-ef8dff39ccac&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;61d8feef-432a-4d48-98f0-2e53241bd591&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;3c0b22cf-36db-4d20-85ee-a5a6a7ed8524&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="54ee73fb-28e9-4081-97b9-7481cc949a11" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="1005" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="ac3fe08d-b76e-44b4-a966-16bacd8ae377"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;dcb7e749-956d-41bb-a627-bd860a391f07&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;dcb7e749-956d-41bb-a627-bd860a391f07&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="Canceled" id="39c68c9f-038f-42d4-b48b-4b13a8e1cff0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="743" y="19" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;73d542bc-4478-4652-9975-9ac25385702d&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="ac3fe08d-b76e-44b4-a966-16bacd8ae377" targetRef="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2" name="To Create IDC Withdrawal Request " id="dcb7e749-956d-41bb-a627-bd860a391f07"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.e2bdefe5-4826-4c86-8897-1bfda517d27d" default="637e9576-8025-42d7-8eb0-4b441be033fd" name="Create IDC Withdrawal Request " id="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="291" y="57" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create IDC Withdrawal Request – إنشاء طلب رد/إعادة توجيه تحصيل مستندى استيراد &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;dcb7e749-956d-41bb-a627-bd860a391f07&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;37fc34c9-526f-40ce-9009-39142bdb932e&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;3145d622-c28d-4846-a88a-56e460c5e87d&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;637e9576-8025-42d7-8eb0-4b441be033fd&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.edfc8822-8c07-4744-a64e-845e8febe86f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.c409b8b1-bd8a-438f-a7e9-a6ca266bfa84&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.3beb46eb-40aa-4939-bf56-225a33b731fd" default="c30a6130-679c-424d-af90-a01e12ed8531" name="Review IDC Withdrawal Request by Compliance Rep" id="c614e2f8-baca-448d-a97f-9c25fb318f84"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="427" y="48" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review IDC Withdrawal Request by Compliance Rep – مراجعة طلب رد/إعادة توجيه تحصيل مستندى استيراد  من ممثل الإلتزام&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c30a6130-679c-424d-af90-a01e12ed8531&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3394d8f8-19d5-454d-b285-6795d04b9ed1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.ac441b92-00e1-4c8e-a6ef-a8206c07a915&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:exclusiveGateway default="37fc34c9-526f-40ce-9009-39142bdb932e" name="Is approved ?" id="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="575" y="67" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c30a6130-679c-424d-af90-a01e12ed8531&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;37fc34c9-526f-40ce-9009-39142bdb932e&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;73d542bc-4478-4652-9975-9ac25385702d&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;a6f7517c-a470-4cda-a540-4b163cbf6c5a&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2" targetRef="92af17c0-8419-43eb-b7e3-16359901304b" name="To Creator?" id="637e9576-8025-42d7-8eb0-4b441be033fd"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="c614e2f8-baca-448d-a97f-9c25fb318f84" targetRef="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342" name="To Is approved ?" id="c30a6130-679c-424d-af90-a01e12ed8531"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342" targetRef="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2" name="Return To Maker" id="37fc34c9-526f-40ce-9009-39142bdb932e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:customBendPoint x="547" y="340" /&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342" targetRef="39c68c9f-038f-42d4-b48b-4b13a8e1cff0" name="To Canceled" id="73d542bc-4478-4652-9975-9ac25385702d"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.canceled&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.53b740e1-0338-4573-9e9b-780485ae5319" default="b900c70d-3cf4-4f06-ad2c-4416a4bd9c32" name="Review IDC Withdrawal Request by Trade FO " id="253f35e3-e912-4dac-8ed0-0523fc757e86"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="622" y="55" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review IDC Withdrawal Request by Trade FO &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;2&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;a6f7517c-a470-4cda-a540-4b163cbf6c5a&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;4cbd6070-726a-4128-a514-eefe886d6392&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;b900c70d-3cf4-4f06-ad2c-4416a4bd9c32&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.88a9888f-ba88-451a-a7d5-8f910d6c1218&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.da277005-23bb-4c78-84f8-161b695335e7&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.4c90d45f-cb2b-4afd-bc7a-b0f825c061a3" default="7012b2de-020a-4f6a-b615-07074e627ee2" name="Execution Hub - Processing Withdrawal Request" id="1a9bffe3-0876-4628-a537-7ff1771d8cfb"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="794" y="55" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Execution Hub - Processing Withdrawal Request&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;9de8068c-057a-4464-9bf0-4d81f4ec4612&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;3b30f6fe-3b46-4760-a421-7da3e1bc3095&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;04432bdf-8f88-490d-8e35-efea906a7a96&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;7012b2de-020a-4f6a-b615-07074e627ee2&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.bd47b5eb-a3c6-4cc3-886f-8d45d1c4b37e&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.a3076611-2921-4d60-a2a7-59357ce96158&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.041e4df2-7de8-4ce4-85a2-24f4482c8a7a" default="7a08c197-9b57-41d7-9f65-f7803df347d9" name="Execution Hub – Processing Withdrawal Request Review" id="d1299553-ba30-4ac6-ae1f-ef8dff39ccac"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="910" y="51" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Execution Hub – Processing Withdrawal Request Review&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;7012b2de-020a-4f6a-b615-07074e627ee2&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;7a08c197-9b57-41d7-9f65-f7803df347d9&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.2987c08f-e776-4335-b8f8-cf00f7baf045&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.3b39115d-83a5-431d-9e31-bae4fe85ba45&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.31a44e81-e812-4dc3-b80d-e2bd710a4bad" default="a8158532-ce6d-46a2-8d4d-98db33c9f414" name="Print Documents" id="c8d6fa0c-c1e0-4c5d-b681-d17f6fc17f29"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="1085" y="65" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Print Documents&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;abe220e7-d105-47e4-8c5d-b6c94d58b303&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;a8158532-ce6d-46a2-8d4d-98db33c9f414&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.e5209a8c-c27b-48d9-8398-385a9fdaa2de&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.9b76ca0a-9a4a-4074-9a93-7d2aa9cf3bdf&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.11032488-ce0d-4b06-aafc-179676224382"&gt;tw.local.idcWithdrawalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342" targetRef="253f35e3-e912-4dac-8ed0-0523fc757e86" name="Approved" id="a6f7517c-a470-4cda-a540-4b163cbf6c5a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingTradeFOReview&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="9de8068c-057a-4464-9bf0-4d81f4ec4612" name="FO Approved ?" id="7f962f9e-42ad-461f-8430-4a24499eb467"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="763" y="74" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b900c70d-3cf4-4f06-ad2c-4416a4bd9c32&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;9de8068c-057a-4464-9bf0-4d81f4ec4612&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;3145d622-c28d-4846-a88a-56e460c5e87d&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;4c07ac66-8edb-4631-bb66-5c410ab91c84&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="253f35e3-e912-4dac-8ed0-0523fc757e86" targetRef="7f962f9e-42ad-461f-8430-4a24499eb467" name="To FO Approved ?" id="b900c70d-3cf4-4f06-ad2c-4416a4bd9c32"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="7f962f9e-42ad-461f-8430-4a24499eb467" targetRef="1a9bffe3-0876-4628-a537-7ff1771d8cfb" name="Approved" id="9de8068c-057a-4464-9bf0-4d81f4ec4612"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="7f962f9e-42ad-461f-8430-4a24499eb467" targetRef="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2" name="Return To Maker" id="3145d622-c28d-4846-a88a-56e460c5e87d"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:customBendPoint x="669" y="545" /&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.returnedToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="FO Cancel" id="f435b3e7-ed20-4802-a37c-696885725368"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="918" y="44" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;4c07ac66-8edb-4631-bb66-5c410ab91c84&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="7f962f9e-42ad-461f-8430-4a24499eb467" targetRef="f435b3e7-ed20-4802-a37c-696885725368" name="Cancel" id="4c07ac66-8edb-4631-bb66-5c410ab91c84"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.terminated&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0" name="Creator?" id="92af17c0-8419-43eb-b7e3-16359901304b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="445" y="76" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;637e9576-8025-42d7-8eb0-4b441be033fd&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;3b30f6fe-3b46-4760-a421-7da3e1bc3095&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="92af17c0-8419-43eb-b7e3-16359901304b" targetRef="c614e2f8-baca-448d-a97f-9c25fb318f84" name="Branch " id="b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="92af17c0-8419-43eb-b7e3-16359901304b" targetRef="1a9bffe3-0876-4628-a537-7ff1771d8cfb" name="Hub and submit to hub direct " id="3b30f6fe-3b46-4760-a421-7da3e1bc3095"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topRight&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingExecutionHubProcessing&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="1a9bffe3-0876-4628-a537-7ff1771d8cfb" targetRef="d1299553-ba30-4ac6-ae1f-ef8dff39ccac" name="To Execution Hub – Processing Withdrawal Request Review" id="7012b2de-020a-4f6a-b615-07074e627ee2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="abe220e7-d105-47e4-8c5d-b6c94d58b303" name="Hub Checker Approved" id="61d8feef-432a-4d48-98f0-2e53241bd591"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1046" y="71" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;7a08c197-9b57-41d7-9f65-f7803df347d9&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;4cbd6070-726a-4128-a514-eefe886d6392&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;04432bdf-8f88-490d-8e35-efea906a7a96&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;abe220e7-d105-47e4-8c5d-b6c94d58b303&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;d905c0dc-f317-489b-a71c-dc3626c587d0&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="d1299553-ba30-4ac6-ae1f-ef8dff39ccac" targetRef="61d8feef-432a-4d48-98f0-2e53241bd591" name="To Hub Checker Approved" id="7a08c197-9b57-41d7-9f65-f7803df347d9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="61d8feef-432a-4d48-98f0-2e53241bd591" targetRef="253f35e3-e912-4dac-8ed0-0523fc757e86" name="Return To FO " id="4cbd6070-726a-4128-a514-eefe886d6392"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topRight&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingTradeFOReview&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="61d8feef-432a-4d48-98f0-2e53241bd591" targetRef="1a9bffe3-0876-4628-a537-7ff1771d8cfb" name="To Execution Hub - Processing Withdrawal Request" id="04432bdf-8f88-490d-8e35-efea906a7a96"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:customBendPoint x="997" y="952" /&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingExecutionHubProcessing&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="61d8feef-432a-4d48-98f0-2e53241bd591" targetRef="c8d6fa0c-c1e0-4c5d-b681-d17f6fc17f29" name="Print Documents" id="abe220e7-d105-47e4-8c5d-b6c94d58b303"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="Hub Checker Cancel OR Approved" id="3c0b22cf-36db-4d20-85ee-a5a6a7ed8524"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1272" y="75" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;d905c0dc-f317-489b-a71c-dc3626c587d0&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="61d8feef-432a-4d48-98f0-2e53241bd591" targetRef="3c0b22cf-36db-4d20-85ee-a5a6a7ed8524" name="Cancel OR Approved" id="d905c0dc-f317-489b-a71c-dc3626c587d0"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.completed&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End" id="a57fac1e-a71d-4df3-a654-9725a797579b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1245" y="88" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;a8158532-ce6d-46a2-8d4d-98db33c9f414&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="c8d6fa0c-c1e0-4c5d-b681-d17f6fc17f29" targetRef="a57fac1e-a71d-4df3-a654-9725a797579b" name="To End" id="a8158532-ce6d-46a2-8d4d-98db33c9f414"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.11032488-ce0d-4b06-aafc-179676224382" isCollection="false" name="idcWithdrawalRequest" id="d4b38049-787a-481e-bf9b-3c6c192acb84" /&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef" /&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef" /&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="IDC WithdrawalInterface" id="b33ea08e-1e4d-44fb-8d36-442df6d1f232" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e5</guid>
        <versionId>7b1eb06e-54ef-4654-abfa-ffb77bee1b6f</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:4042929508233c47:-1c6e1647:188f04d39d0:6258">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>IDC Withdrawal</name>
            <documentation></documentation>
            <name>IDC Withdrawal</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>eslam</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1688664857816</creationDate>
            <modificationDate>1688666092043</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"IDC Withdrawal:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="9decb341-4972-4421-94b5-0938c1f6c213" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e1" />
            <ownerTeamInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e2" />
            <simulationScenario id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6094">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1688664861436</startTime>
            </simulationScenario>
            <flow id="04432bdf-8f88-490d-8e35-efea906a7a96" connectionType="SequenceFlow">
                <name>To Execution Hub - Processing Withdrawal Request</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6599">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingExecutionHubProcessing</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="73d542bc-4478-4652-9975-9ac25385702d" connectionType="SequenceFlow">
                <name>To Canceled</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b6">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.canceled</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="7a08c197-9b57-41d7-9f65-f7803df347d9" connectionType="SequenceFlow">
                <name>To Hub Checker Approved</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6093" />
                </connection>
            </flow>
            <flow id="4cbd6070-726a-4128-a514-eefe886d6392" connectionType="SequenceFlow">
                <name>Return To FO </name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659b">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingTradeFOReview</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="637e9576-8025-42d7-8eb0-4b441be033fd" connectionType="SequenceFlow">
                <name>To Creator?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6092" />
                </connection>
            </flow>
            <flow id="37fc34c9-526f-40ce-9009-39142bdb932e" connectionType="SequenceFlow">
                <name>Return To Maker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6091" />
                </connection>
            </flow>
            <flow id="dcb7e749-956d-41bb-a627-bd860a391f07" connectionType="SequenceFlow">
                <name>To Create IDC Withdrawal Request </name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6090" />
                </connection>
            </flow>
            <flow id="abe220e7-d105-47e4-8c5d-b6c94d58b303" connectionType="SequenceFlow">
                <name>Print Documents</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608f" />
                </connection>
            </flow>
            <flow id="3b30f6fe-3b46-4760-a421-7da3e1bc3095" connectionType="SequenceFlow">
                <name>Hub and submit to hub direct </name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a5">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingExecutionHubProcessing</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="d905c0dc-f317-489b-a71c-dc3626c587d0" connectionType="SequenceFlow">
                <name>Cancel OR Approved</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6596">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.completed</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="9de8068c-057a-4464-9bf0-4d81f4ec4612" connectionType="SequenceFlow">
                <name>Approved</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608e" />
                </connection>
            </flow>
            <flow id="4c07ac66-8edb-4631-bb66-5c410ab91c84" connectionType="SequenceFlow">
                <name>Cancel</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65aa">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.terminated</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="3145d622-c28d-4846-a88a-56e460c5e87d" connectionType="SequenceFlow">
                <name>Return To Maker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ac">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.returnedToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="7012b2de-020a-4f6a-b615-07074e627ee2" connectionType="SequenceFlow">
                <name>To Execution Hub – Processing Withdrawal Request Review</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608d" />
                </connection>
            </flow>
            <flow id="a6f7517c-a470-4cda-a540-4b163cbf6c5a" connectionType="SequenceFlow">
                <name>Approved</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b4">
                        <expression>tw.local.idcWithdrawalRequest.appInfo.subStatus	  ==	  tw.epv.IDCWithdrawalSubStatus.pendingTradeFOReview</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="a8158532-ce6d-46a2-8d4d-98db33c9f414" connectionType="SequenceFlow">
                <name>To End</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608c" />
                </connection>
            </flow>
            <flow id="c30a6130-679c-424d-af90-a01e12ed8531" connectionType="SequenceFlow">
                <name>To Is approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608b" />
                </connection>
            </flow>
            <flow id="b900c70d-3cf4-4f06-ad2c-4416a4bd9c32" connectionType="SequenceFlow">
                <name>To FO Approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-608a" />
                </connection>
            </flow>
            <flow id="b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0" connectionType="SequenceFlow">
                <name>Branch </name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6089" />
                </connection>
            </flow>
            <pool id="9decb341-4972-4421-94b5-0938c1f6c213">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at16840767503881688664857806</restrictedName>
                <dimension>
                    <size w="3000" h="1000" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="7f803f55-b6d5-4453-94e1-efed46ec424e">
                    <name>Branch / Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="c04973e1-7c5e-46c9-a2b2-2fb90d5c76d2" componentType="Activity">
                        <name>Create IDC Withdrawal Request </name>
                        <position>
                            <location x="291" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.e2bdefe5-4826-4c86-8897-1bfda517d27d</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create IDC Withdrawal Request – إنشاء طلب رد/إعادة توجيه تحصيل مستندى استيراد </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65de">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.edfc8822-8c07-4744-a64e-845e8febe86f</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65dd">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.c409b8b1-bd8a-438f-a7e9-a6ca266bfa84</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65dc">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65db">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c0">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="dcb7e749-956d-41bb-a627-bd860a391f07" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b8">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="37fc34c9-526f-40ce-9009-39142bdb932e" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ad">
                            <positionId>leftBottom</positionId>
                            <input>true</input>
                            <flow ref="3145d622-c28d-4846-a88a-56e460c5e87d" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65bf">
                            <positionId>rightCenter</positionId>
                            <flow ref="637e9576-8025-42d7-8eb0-4b441be033fd" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="c8d6fa0c-c1e0-4c5d-b681-d17f6fc17f29" componentType="Activity">
                        <name>Print Documents</name>
                        <position>
                            <location x="1085" y="65" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.31a44e81-e812-4dc3-b80d-e2bd710a4bad</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Print Documents</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c5">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.e5209a8c-c27b-48d9-8398-385a9fdaa2de</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c4">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.9b76ca0a-9a4a-4074-9a93-7d2aa9cf3bdf</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c3">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c2">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6598">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="abe220e7-d105-47e4-8c5d-b6c94d58b303" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6595">
                            <positionId>rightCenter</positionId>
                            <flow ref="a8158532-ce6d-46a2-8d4d-98db33c9f414" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="ac3fe08d-b76e-44b4-a966-16bacd8ae377" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c1">
                            <positionId>rightCenter</positionId>
                            <flow ref="dcb7e749-956d-41bb-a627-bd860a391f07" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="a57fac1e-a71d-4df3-a654-9725a797579b" componentType="Event">
                        <name>End</name>
                        <documentation></documentation>
                        <position>
                            <location x="1245" y="88" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6594">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="a8158532-ce6d-46a2-8d4d-98db33c9f414" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="92af17c0-8419-43eb-b7e3-16359901304b" componentType="Gateway">
                        <name>Creator?</name>
                        <documentation></documentation>
                        <position>
                            <location x="445" y="76" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65be">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="637e9576-8025-42d7-8eb0-4b441be033fd" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a8">
                            <positionId>rightCenter</positionId>
                            <flow ref="3b30f6fe-3b46-4760-a421-7da3e1bc3095" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a9">
                            <positionId>bottomCenter</positionId>
                            <flow ref="b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="ef286b70-b2f8-4139-a0b2-f5ffda73444e">
                    <name>Compliance Representative</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="c614e2f8-baca-448d-a97f-9c25fb318f84" componentType="Activity">
                        <name>Review IDC Withdrawal Request by Compliance Rep</name>
                        <position>
                            <location x="427" y="48" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.3beb46eb-40aa-4939-bf56-225a33b731fd</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC Withdrawal Request by Compliance Rep – مراجعة طلب رد/إعادة توجيه تحصيل مستندى استيراد  من ممثل الإلتزام</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d9">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.3394d8f8-19d5-454d-b285-6795d04b9ed1</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d8">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.ac441b92-00e1-4c8e-a6ef-a8206c07a915</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d7">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d6">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a7">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="b8e5b059-3de3-43fd-a2d5-b2494b7e7ac0" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65bd">
                            <positionId>rightCenter</positionId>
                            <flow ref="c30a6130-679c-424d-af90-a01e12ed8531" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="39c68c9f-038f-42d4-b48b-4b13a8e1cff0" componentType="Event">
                        <name>Canceled</name>
                        <documentation></documentation>
                        <position>
                            <location x="743" y="19" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b7">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="73d542bc-4478-4652-9975-9ac25385702d" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="56f9d7bb-44f6-43b9-a1a0-2ee94dacb342" componentType="Gateway">
                        <name>Is approved ?</name>
                        <documentation></documentation>
                        <position>
                            <location x="575" y="67" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65bc">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="c30a6130-679c-424d-af90-a01e12ed8531" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ba">
                            <positionId>topCenter</positionId>
                            <flow ref="73d542bc-4478-4652-9975-9ac25385702d" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b9">
                            <positionId>rightCenter</positionId>
                            <flow ref="a6f7517c-a470-4cda-a540-4b163cbf6c5a" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65bb">
                            <positionId>bottomCenter</positionId>
                            <flow ref="37fc34c9-526f-40ce-9009-39142bdb932e" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="2a72e32e-8806-4f87-a73e-b0a40d829a98">
                    <name>Trade Front Officer</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="253f35e3-e912-4dac-8ed0-0523fc757e86" componentType="Activity">
                        <name>Review IDC Withdrawal Request by Trade FO </name>
                        <position>
                            <location x="622" y="55" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.53b740e1-0338-4573-9e9b-780485ae5319</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>2</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC Withdrawal Request by Trade FO </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d4">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.88a9888f-ba88-451a-a7d5-8f910d6c1218</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d3">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.da277005-23bb-4c78-84f8-161b695335e7</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d2">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65d1">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b5">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="a6f7517c-a470-4cda-a540-4b163cbf6c5a" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659c">
                            <positionId>topRight</positionId>
                            <input>true</input>
                            <flow ref="4cbd6070-726a-4128-a514-eefe886d6392" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b3">
                            <positionId>rightCenter</positionId>
                            <flow ref="b900c70d-3cf4-4f06-ad2c-4416a4bd9c32" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="f435b3e7-ed20-4802-a37c-696885725368" componentType="Event">
                        <name>FO Cancel</name>
                        <documentation></documentation>
                        <position>
                            <location x="918" y="44" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ab">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="4c07ac66-8edb-4631-bb66-5c410ab91c84" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="7f962f9e-42ad-461f-8430-4a24499eb467" componentType="Gateway">
                        <name>FO Approved ?</name>
                        <documentation></documentation>
                        <position>
                            <location x="763" y="74" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b2">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b900c70d-3cf4-4f06-ad2c-4416a4bd9c32" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b0">
                            <positionId>bottomCenter</positionId>
                            <flow ref="3145d622-c28d-4846-a88a-56e460c5e87d" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65af">
                            <positionId>topCenter</positionId>
                            <flow ref="4c07ac66-8edb-4631-bb66-5c410ab91c84" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65b1">
                            <positionId>rightCenter</positionId>
                            <flow ref="9de8068c-057a-4464-9bf0-4d81f4ec4612" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="7aee63b1-1367-49bf-8a7b-8ace78ef653e">
                    <name>Execution Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="1a9bffe3-0876-4628-a537-7ff1771d8cfb" componentType="Activity">
                        <name>Execution Hub - Processing Withdrawal Request</name>
                        <position>
                            <location x="794" y="55" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.4c90d45f-cb2b-4afd-bc7a-b0f825c061a3</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Execution Hub - Processing Withdrawal Request</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65cf">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.bd47b5eb-a3c6-4cc3-886f-8d45d1c4b37e</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ce">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.a3076611-2921-4d60-a2a7-59357ce96158</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65cd">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65cc">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ae">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="9de8068c-057a-4464-9bf0-4d81f4ec4612" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a6">
                            <positionId>topRight</positionId>
                            <input>true</input>
                            <flow ref="3b30f6fe-3b46-4760-a421-7da3e1bc3095" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659a">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="04432bdf-8f88-490d-8e35-efea906a7a96" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a4">
                            <positionId>bottomCenter</positionId>
                            <flow ref="7012b2de-020a-4f6a-b615-07074e627ee2" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="a0ed0a24-5be1-4a64-a54f-8e0e9d1cf347">
                    <name>Execution Hub Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="d1299553-ba30-4ac6-ae1f-ef8dff39ccac" componentType="Activity">
                        <name>Execution Hub – Processing Withdrawal Request Review</name>
                        <position>
                            <location x="910" y="51" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.041e4df2-7de8-4ce4-85a2-24f4482c8a7a</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Execution Hub – Processing Withdrawal Request Review</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ca">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.2987c08f-e776-4335-b8f8-cf00f7baf045</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c9">
                                    <name>idcWithdrawalRequest</name>
                                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                                    <input>true</input>
                                    <value>tw.local.idcWithdrawalRequest</value>
                                    <parameterId>2055.3b39115d-83a5-431d-9e31-bae4fe85ba45</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c8">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65c7">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a3">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="7012b2de-020a-4f6a-b615-07074e627ee2" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a2">
                            <positionId>rightCenter</positionId>
                            <flow ref="7a08c197-9b57-41d7-9f65-f7803df347d9" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="3c0b22cf-36db-4d20-85ee-a5a6a7ed8524" componentType="Event">
                        <name>Hub Checker Cancel OR Approved</name>
                        <documentation></documentation>
                        <position>
                            <location x="1272" y="75" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6597">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="d905c0dc-f317-489b-a71c-dc3626c587d0" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="61d8feef-432a-4d48-98f0-2e53241bd591" componentType="Gateway">
                        <name>Hub Checker Approved</name>
                        <documentation></documentation>
                        <position>
                            <location x="1046" y="71" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a1">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="7a08c197-9b57-41d7-9f65-f7803df347d9" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65a0">
                            <positionId>topCenter</positionId>
                            <flow ref="4cbd6070-726a-4128-a514-eefe886d6392" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659f">
                            <positionId>bottomCenter</positionId>
                            <flow ref="04432bdf-8f88-490d-8e35-efea906a7a96" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659d">
                            <positionId>rightCenter</positionId>
                            <flow ref="d905c0dc-f317-489b-a71c-dc3626c587d0" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-659e">
                            <positionId>rightCenter</positionId>
                            <flow ref="abe220e7-d105-47e4-8c5d-b6c94d58b303" />
                        </outputPort>
                    </flowObject>
                </lane>
                <privateVariable id="d4b38049-787a-481e-bf9b-3c6c192acb84">
                    <name>idcWithdrawalRequest</name>
                    <description></description>
                    <classId>/12.11032488-ce0d-4b06-aafc-179676224382</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e0">
                    <epvId>/21.a6e09805-0e21-4bb5-8e25-1aa9a5db67ad</epvId>
                </epv>
            </pool>
            <extension id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6593" type="CASE">
                <caseFolder id="edecf92e-4feb-405b-ba11-403a50a50c7e">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

