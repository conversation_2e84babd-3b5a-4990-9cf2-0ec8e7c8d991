<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258" name="Check Large corporate in DB">
        <lastModified>1692530837154</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>1dc95455-3958-49ca-a139-7bba729c9474</guid>
        <versionId>da92b884-e937-45c3-b77b-13a00d6fd441</versionId>
        <dependencySummary>&lt;dependencySummary id="ec6bd906-d386-4f69-b5dd-679ccd96cf85" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e087656c-e882-4af5-8843-8b459ca220c9</processParameterId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"99999999"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1fe4d361-40ed-499a-82f8-c5295ffcd974</guid>
            <versionId>53a5541d-7b43-4d4c-9f90-00dced41ea20</versionId>
        </processParameter>
        <processParameter name="dep_role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9c75881f-44f2-4b85-9c0f-8f6802d36afd</processParameterId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"BPM_CA_LRG_CORP_EXE_MKR"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3f3f323b-36d8-48cd-9d89-914d2d356466</guid>
            <versionId>05ef5e29-37ab-469b-bb74-8e52a72f32c3</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.847e0f41-e473-44cc-b21c-f92a67dd5962</processParameterId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>006d2ba7-5507-4d32-af93-6fc81388fc01</guid>
            <versionId>231118c7-bdef-4df4-b75a-2c21d40503dd</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f68b26ea-5007-4069-ab6b-c921565d4e88</processParameterId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>55e4e49b-d254-4034-b125-733b74e512ec</guid>
            <versionId>7ca80d8e-577c-4c2c-a9dd-e4f7231b5a80</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9f210e88-63d5-4b74-bc37-7b5441eb066a</processVariableId>
            <description isNull="true" />
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fc6bb5cf-dd29-4f6b-9a20-e779c3e1c911</guid>
            <versionId>62f22271-a996-4f8c-b22c-d4da477ac9e2</versionId>
        </processVariable>
        <processVariable name="query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0f3470f5-c55c-47ee-9f21-865cfc35aec0</processVariableId>
            <description isNull="true" />
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e87db13a-f3a0-498b-8c6c-4b3346c29668</guid>
            <versionId>99a52c6b-447b-4595-b8d8-214b9f316a77</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ae0ce21e-2574-4790-a114-9d857bd419c8</processVariableId>
            <description isNull="true" />
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fcb90596-7e85-4b7d-bd89-71510c5ae150</guid>
            <versionId>75a5d44c-44b6-419a-bd71-3c72b46fb3d3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c554b23d-26c4-4353-95ac-0686de8b2eed</processItemId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <name>log Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e62dd0e7-3bd2-4603-acd0-1446db596a31</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f70</guid>
            <versionId>2ea1aa2f-0294-4880-842a-bdbd823c3269</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="459" y="189">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e62dd0e7-3bd2-4603-acd0-1446db596a31</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Check Large corporate in DB -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Check Large corporate in DB -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</script>
                <isRule>false</isRule>
                <guid>cbe0a9dd-be94-4a85-87c8-4f28f4d670c4</guid>
                <versionId>852b1520-3004-446b-b552-d01672139e74</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</processItemId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <name>Init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9e8559c3-2e13-4dc9-bfae-0de6b1e8d33f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f6f</guid>
            <versionId>36e0d7b1-6871-48b9-b539-cde534a425a0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c61d68b9-41f3-4433-a498-e0bffbea8647</processItemPrePostId>
                <processItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName :Check Large corporate in DB : START");</script>
                <guid>f157d515-f79b-4328-b0ba-98b0947c49ab</guid>
                <versionId>a2294572-ef54-4a5b-bb5a-83d61cd3cbd3</versionId>
            </processPrePosts>
            <layoutData x="125" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9e8559c3-2e13-4dc9-bfae-0de6b1e8d33f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.query= new tw.object.SQLStatement();&#xD;
&#xD;
tw.local.query.sql=" select RELATED_USER_NAME as name , RELATED_USER_ID as value from BPM.LARGE_CORPORATE_CUSTOMERS_mapping where  CORPORATE_CIF = ? and TEAM_SUFFIX = ? ;"&#xD;
 &#xD;
tw.local.query.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.query.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[0].mode="IN";&#xD;
tw.local.query.parameters[0].value=tw.local.CIF;&#xD;
&#xD;
tw.local.query.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[1].mode="IN";&#xD;
tw.local.query.parameters[1].value=tw.local.dep_role;&#xD;
   
</script>
                <isRule>false</isRule>
                <guid>4f8aeb04-da8b-41aa-ae1c-3e57eb7f23a2</guid>
                <versionId>d91b26d6-bb22-47f7-b051-fc97eae00b43</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.47f6b307-20d2-4ba3-a45d-1783c90e4517</processItemId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <name>SQL Execute statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c554b23d-26c4-4353-95ac-0686de8b2eed</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f6d</guid>
            <versionId>586f87a5-41e2-4a34-ad4f-c9b9ddb57fef</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="322" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f70</errorHandlerItem>
                <errorHandlerItemId>2025.c554b23d-26c4-4353-95ac-0686de8b2eed</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>d67bb3cc-c42e-480b-9879-39ff2118f8e7</guid>
                <versionId>7cb2e5f4-62b4-438d-a6a9-2d2054bf5abc</versionId>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a687b5e7-fd4a-4ea6-afc0-03d978242115</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7e7af124-5908-4a3a-bbd8-9a9fae5d960d</guid>
                    <versionId>420abf65-039b-4885-8d90-20645412353e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c141f040-7c2b-49f9-a96f-89ad99541ba6</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_APP</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9fc43388-7e69-43b1-bfa3-0dead067e942</guid>
                    <versionId>54268d61-11cc-48de-a0fd-de4dc0e8bf3a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29b7dcd0-fe10-40a4-b424-662acc285a93</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6403fa9f-6b85-42e4-8606-b064e17bd406</guid>
                    <versionId>5731654d-bed1-4da1-b0f2-a55aaf6ee351</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a8a91b2d-ab6c-475a-a067-221c4ce272dd</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0c76fcce-5ad0-4c19-aee9-9488fc36ed67</guid>
                    <versionId>99a42e3b-2aae-41d8-9ef2-067b04937e73</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9d21b13a-0de1-41ba-9f04-ec3d8fdeb550</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>239c9e2c-3828-4553-a538-e95c248c1ef2</guid>
                    <versionId>c10f56e6-27a2-42d5-847c-139cca4de662</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9b17b518-a1eb-438f-8bf1-a5199b647c5f</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.2beb2944-0e84-4f73-9911-1bc1d4260b03</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"NameValuePair"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>468a2085-536b-4d54-abf6-b09d8eef9898</guid>
                    <versionId>edb6a321-3b4b-47bc-9439-8635fb85fb79</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.303599c3-e673-423a-ae3b-2773b0a614e6</processItemId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.46d0ab9e-00b0-4823-913d-47a754466108</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f71</guid>
            <versionId>81905d26-df35-417c-ba4b-3328c8e8501c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.9cdd1827-a1d6-4999-991d-e0eb305eb594</processItemPrePostId>
                <processItemId>2025.303599c3-e673-423a-ae3b-2773b0a614e6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>d5642e55-1ac2-45b9-af07-5216f3998ecf</guid>
                <versionId>dc064c25-39b2-4d8f-9e0a-3e2ac54dc4f7</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c1c31ff6-0d10-4fd9-8ed7-cde623715f4d</processItemPrePostId>
                <processItemId>2025.303599c3-e673-423a-ae3b-2773b0a614e6</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName :Check Large corporate in DB : START");</script>
                <guid>719eb885-c46a-4934-b0e4-8132582db6ab</guid>
                <versionId>e7d45a47-823e-48a8-a8e0-9c80afeced6d</versionId>
            </processPrePosts>
            <layoutData x="650" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.46d0ab9e-00b0-4823-913d-47a754466108</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8507eb21-c521-45c8-8e01-b8ba9bdc98e8</guid>
                <versionId>7e010897-899b-4839-9752-68d436f62752</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.760c860d-a76e-4916-9c67-927fa80a1d96</processItemId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.d06c5aee-ae2a-4e1a-a6e3-7dc24c5e3cbf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f6e</guid>
            <versionId>a75adb33-03de-4e52-864e-5d8003db21f8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="671" y="227">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.d06c5aee-ae2a-4e1a-a6e3-7dc24c5e3cbf</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>b8ce157c-b850-4c23-a90d-7c86697775fa</guid>
                <versionId>ca22d65a-d008-4d40-9cd3-1180f4f8c269</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3b1719db-4c5d-412e-9cef-e72ef84a4b64</parameterMappingId>
                    <processParameterId>2055.f68b26ea-5007-4069-ab6b-c921565d4e88</processParameterId>
                    <parameterMappingParentId>3007.d06c5aee-ae2a-4e1a-a6e3-7dc24c5e3cbf</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>44ce76ce-2127-4f54-9338-df12052f10e0</guid>
                    <versionId>b286e892-95b5-4408-8be2-35f44805e526</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Large corporate in DB" id="1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e087656c-e882-4af5-8843-8b459ca220c9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"99999999"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="dep_role" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9c75881f-44f2-4b85-9c0f-8f6802d36afd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"BPM_CA_LRG_CORP_EXE_MKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.847e0f41-e473-44cc-b21c-f92a67dd5962" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.e087656c-e882-4af5-8843-8b459ca220c9</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.9c75881f-44f2-4b85-9c0f-8f6802d36afd</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.847e0f41-e473-44cc-b21c-f92a67dd5962</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="cea6a9fd-e35b-4672-bce0-9a68222b5202">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="ec7de957-59bf-471f-b95d-39fa0b719d3f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f07994be-a2c4-4935-8a5f-e43d1283a564</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>303599c3-e673-423a-ae3b-2773b0a614e6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>47f6b307-20d2-4ba3-a45d-1783c90e4517</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ff892977-ea16-4a56-ba0c-5d5efc22a989</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c554b23d-26c4-4353-95ac-0686de8b2eed</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>760c860d-a76e-4916-9c67-927fa80a1d96</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f07994be-a2c4-4935-8a5f-e43d1283a564">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="79" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.76307d03-d843-453b-b2d1-7c096c020572</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="303599c3-e673-423a-ae3b-2773b0a614e6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="79" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f71</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName :Check Large corporate in DB : START");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>654b3155-944d-4bcf-9eb2-b24427d10dda</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.9f210e88-63d5-4b74-bc37-7b5441eb066a" />
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute statement" id="47f6b307-20d2-4ba3-a45d-1783c90e4517">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="322" y="56" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>29ada52f-e1d3-4e52-b5c9-ea7cb11ada22</ns16:incoming>
                        
                        
                        <ns16:outgoing>654b3155-944d-4bcf-9eb2-b24427d10dda</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_APP</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"NameValuePair"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.query.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.query.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Init" id="4aebccf8-b5e0-4397-8f74-ad39a5f7b32c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="125" y="56" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName :Check Large corporate in DB : START");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.76307d03-d843-453b-b2d1-7c096c020572</ns16:incoming>
                        
                        
                        <ns16:outgoing>29ada52f-e1d3-4e52-b5c9-ea7cb11ada22</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.query= new tw.object.SQLStatement();&#xD;
&#xD;
tw.local.query.sql=" select RELATED_USER_NAME as name , RELATED_USER_ID as value from BPM.LARGE_CORPORATE_CUSTOMERS_mapping where  CORPORATE_CIF = ? and TEAM_SUFFIX = ? ;"&#xD;
 &#xD;
tw.local.query.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.query.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[0].mode="IN";&#xD;
tw.local.query.parameters[0].value=tw.local.CIF;&#xD;
&#xD;
tw.local.query.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[1].mode="IN";&#xD;
tw.local.query.parameters[1].value=tw.local.dep_role;&#xD;
   
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="47f6b307-20d2-4ba3-a45d-1783c90e4517" targetRef="303599c3-e673-423a-ae3b-2773b0a614e6" name="To Script Task" id="654b3155-944d-4bcf-9eb2-b24427d10dda">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4aebccf8-b5e0-4397-8f74-ad39a5f7b32c" targetRef="47f6b307-20d2-4ba3-a45d-1783c90e4517" name="To SQL Execute statement" id="29ada52f-e1d3-4e52-b5c9-ea7cb11ada22">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f07994be-a2c4-4935-8a5f-e43d1283a564" targetRef="4aebccf8-b5e0-4397-8f74-ad39a5f7b32c" name="To End" id="2027.76307d03-d843-453b-b2d1-7c096c020572">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="false" name="query" id="2056.0f3470f5-c55c-47ee-9f21-865cfc35aec0" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="47f6b307-20d2-4ba3-a45d-1783c90e4517" parallelMultiple="false" name="Error" id="ff892977-ea16-4a56-ba0c-5d5efc22a989">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="357" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a34696ea-2e78-49b8-adbb-f2f0a088fbbf</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="673f4373-a376-4dd1-8a8f-0fb9c2fa6a3c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d1fa48ae-65f6-429f-8383-54647b6f6851" eventImplId="bc5c25f3-1cff-439e-861e-f8ed9e632ea9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="log Error" id="c554b23d-26c4-4353-95ac-0686de8b2eed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="459" y="189" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a34696ea-2e78-49b8-adbb-f2f0a088fbbf</ns16:incoming>
                        
                        
                        <ns16:outgoing>8cfdb10c-f589-4aad-864a-f0debe5889e0</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Check Large corporate in DB -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Check Large corporate in DB -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ff892977-ea16-4a56-ba0c-5d5efc22a989" targetRef="c554b23d-26c4-4353-95ac-0686de8b2eed" name="To log Error" id="a34696ea-2e78-49b8-adbb-f2f0a088fbbf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="760c860d-a76e-4916-9c67-927fa80a1d96">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="671" y="227" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8cfdb10c-f589-4aad-864a-f0debe5889e0</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="cb591870-42b1-4414-8933-feeeae89742a" eventImplId="b3a17736-3477-4628-889a-481e32368f1d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c554b23d-26c4-4353-95ac-0686de8b2eed" targetRef="760c860d-a76e-4916-9c67-927fa80a1d96" name="To End Event" id="8cfdb10c-f589-4aad-864a-f0debe5889e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.ae0ce21e-2574-4790-a114-9d857bd419c8" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4e7b7c8f-2001-4028-9d75-04d782192fa5</processLinkId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.47f6b307-20d2-4ba3-a45d-1783c90e4517</toProcessItemId>
            <guid>68430a50-3714-4e86-9cc3-0fda77f6e09e</guid>
            <versionId>5b4e2af1-6d8a-4205-997b-1be40ac3ea6e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4aebccf8-b5e0-4397-8f74-ad39a5f7b32c</fromProcessItemId>
            <toProcessItemId>2025.47f6b307-20d2-4ba3-a45d-1783c90e4517</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fc54f094-ef27-41bb-b9e5-a773a420e2a8</processLinkId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c554b23d-26c4-4353-95ac-0686de8b2eed</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.760c860d-a76e-4916-9c67-927fa80a1d96</toProcessItemId>
            <guid>cf6d8621-4930-40a9-b861-5190c6f0b823</guid>
            <versionId>61da60f4-aa28-4eba-9b0d-5a2a22eb5d4d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c554b23d-26c4-4353-95ac-0686de8b2eed</fromProcessItemId>
            <toProcessItemId>2025.760c860d-a76e-4916-9c67-927fa80a1d96</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.38092db2-403c-4f11-bd0b-652e9a1f892f</processLinkId>
            <processId>1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.47f6b307-20d2-4ba3-a45d-1783c90e4517</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.303599c3-e673-423a-ae3b-2773b0a614e6</toProcessItemId>
            <guid>1a2f5cef-1549-4cf8-a86c-c8a41340b7c1</guid>
            <versionId>a6ec6f59-f5e1-4b99-a113-1b804ffa3675</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.47f6b307-20d2-4ba3-a45d-1783c90e4517</fromProcessItemId>
            <toProcessItemId>2025.303599c3-e673-423a-ae3b-2773b0a614e6</toProcessItemId>
        </link>
    </process>
</teamworks>

