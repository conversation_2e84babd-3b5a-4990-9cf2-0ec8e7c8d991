<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b" name="Create Liquidation">
        <lastModified>1688914259606</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.e46c2b66-1f9d-4782-87a9-efb837153a1e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:18939e75821:-659</guid>
        <versionId>14b4332a-9733-4a90-b9e2-81cbbcbcddc9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:246979e51bde3d7d:-3bedc06d:18939e75821:1c53" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.73fec3e6-3615-41e8-8140-0449128c4e8b"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"508a669b-ef37-4f96-856b-d85577f6121b"},{"incoming":["aae9b774-a898-4e6a-843b-83acf30f6d1f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:18939e75821:-657"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c1cb6846-2cfa-4b35-8123-13b8b1c97358"},{"targetRef":"e46c2b66-1f9d-4782-87a9-efb837153a1e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.73fec3e6-3615-41e8-8140-0449128c4e8b","sourceRef":"508a669b-ef37-4f96-856b-d85577f6121b"},{"startQuantity":1,"outgoing":["aae9b774-a898-4e6a-843b-83acf30f6d1f"],"incoming":["2027.73fec3e6-3615-41e8-8140-0449128c4e8b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":324,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e46c2b66-1f9d-4782-87a9-efb837153a1e","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.data &gt; 0) {\r\n\ttw.local.results = true;\r\n}else{\r\n\ttw.local.results = false\r\n}"]}},{"targetRef":"c1cb6846-2cfa-4b35-8123-13b8b1c97358","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"aae9b774-a898-4e6a-843b-83acf30f6d1f","sourceRef":"e46c2b66-1f9d-4782-87a9-efb837153a1e"}],"laneSet":[{"id":"1541e965-a6b8-4f90-8774-72e94d19d859","lane":[{"flowNodeRef":["508a669b-ef37-4f96-856b-d85577f6121b","c1cb6846-2cfa-4b35-8123-13b8b1c97358","e46c2b66-1f9d-4782-87a9-efb837153a1e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"7a6269e0-3a77-421a-899d-2e53748a244e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create Liquidation","declaredType":"process","id":"1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.77f111cd-6053-4b0d-8e1b-90b3d06feffc"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.011ebf3e-52f5-4d07-8323-e641c2d5e3c6"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"200"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.2d1e50b3-14b7-466c-8023-58739169cb0b"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2d1e50b3-14b7-466c-8023-58739169cb0b</processParameterId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0c3f8bcc-ed08-4465-b666-1cf4072c2cd7</guid>
            <versionId>a016181e-a8b9-4ca1-a07f-87eebff093e7</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.77f111cd-6053-4b0d-8e1b-90b3d06feffc</processParameterId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>371a036a-a4f1-49f5-b50c-1475d1022753</guid>
            <versionId>4fe7f663-0788-4cf7-9a33-b4b9d929c084</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.011ebf3e-52f5-4d07-8323-e641c2d5e3c6</processParameterId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a85cf715-54f9-4aeb-81b7-e5f6e32211f1</guid>
            <versionId>cf14ed2d-4483-4f46-b396-e9354a0200cd</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c1cb6846-2cfa-4b35-8123-13b8b1c97358</processItemId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.edd644d2-5a30-4a07-b5c0-8085543274f1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18939e75821:-657</guid>
            <versionId>146afc11-2186-4af9-acae-22a8e3269a2e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.edd644d2-5a30-4a07-b5c0-8085543274f1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8f0b65fb-0323-4710-aebc-563b5bf105e3</guid>
                <versionId>c1525a55-081e-4ccf-a4c6-b366f9d7876f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e46c2b66-1f9d-4782-87a9-efb837153a1e</processItemId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2944fbbb-a939-40c0-9acd-90d1a15aa5b9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18939e75821:-655</guid>
            <versionId>9a151d71-cd83-4027-8293-3f487866c387</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="324" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2944fbbb-a939-40c0-9acd-90d1a15aa5b9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.data &gt; 0) {&#xD;
	tw.local.results = true;&#xD;
}else{&#xD;
	tw.local.results = false&#xD;
}</script>
                <isRule>false</isRule>
                <guid>236b0662-a409-4bb8-9e40-4edd75ff708d</guid>
                <versionId>7fe2c685-**************-0089d6683fd3</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.e46c2b66-1f9d-4782-87a9-efb837153a1e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create Liquidation" id="1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.2d1e50b3-14b7-466c-8023-58739169cb0b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">200</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.77f111cd-6053-4b0d-8e1b-90b3d06feffc" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.011ebf3e-52f5-4d07-8323-e641c2d5e3c6" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="1541e965-a6b8-4f90-8774-72e94d19d859">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="7a6269e0-3a77-421a-899d-2e53748a244e" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>508a669b-ef37-4f96-856b-d85577f6121b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c1cb6846-2cfa-4b35-8123-13b8b1c97358</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e46c2b66-1f9d-4782-87a9-efb837153a1e</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="508a669b-ef37-4f96-856b-d85577f6121b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.73fec3e6-3615-41e8-8140-0449128c4e8b</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c1cb6846-2cfa-4b35-8123-13b8b1c97358">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:18939e75821:-657</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>aae9b774-a898-4e6a-843b-83acf30f6d1f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="508a669b-ef37-4f96-856b-d85577f6121b" targetRef="e46c2b66-1f9d-4782-87a9-efb837153a1e" name="To Script Task" id="2027.73fec3e6-3615-41e8-8140-0449128c4e8b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="e46c2b66-1f9d-4782-87a9-efb837153a1e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="324" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.73fec3e6-3615-41e8-8140-0449128c4e8b</ns16:incoming>
                        
                        
                        <ns16:outgoing>aae9b774-a898-4e6a-843b-83acf30f6d1f</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.data &gt; 0) {&#xD;
	tw.local.results = true;&#xD;
}else{&#xD;
	tw.local.results = false&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e46c2b66-1f9d-4782-87a9-efb837153a1e" targetRef="c1cb6846-2cfa-4b35-8123-13b8b1c97358" name="To End" id="aae9b774-a898-4e6a-843b-83acf30f6d1f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.aae9b774-a898-4e6a-843b-83acf30f6d1f</processLinkId>
            <processId>1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e46c2b66-1f9d-4782-87a9-efb837153a1e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c1cb6846-2cfa-4b35-8123-13b8b1c97358</toProcessItemId>
            <guid>bd9c5c08-9d5d-4487-b3be-ddbaf6883aee</guid>
            <versionId>5835bc14-ae6b-4841-ad43-c16a309952fd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e46c2b66-1f9d-4782-87a9-efb837153a1e</fromProcessItemId>
            <toProcessItemId>2025.c1cb6846-2cfa-4b35-8123-13b8b1c97358</toProcessItemId>
        </link>
    </process>
</teamworks>

