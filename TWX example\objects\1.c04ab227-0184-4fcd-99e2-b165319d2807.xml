<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.c04ab227-0184-4fcd-99e2-b165319d2807" name="Validate Required Documents">
        <lastModified>1689415741700</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fbb08abf-6db8-4013-9501-867ba03b47f1</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>86cc40e8-40e3-4e32-9678-a5d7243a6c60</guid>
        <versionId>db717c91-795e-42b4-8aa4-ab91b87ea3f7</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:03b1a2360e7e8990:-4d5b5054:18954a5bdc2:-4162" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"63052e87-18e7-4af0-a16e-e616c1a3f399"},{"incoming":["84263f2a-431f-4013-9c5b-9aff8e3ecb90"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ee8571dc-cc42-4e05-af80-796414a690ff"},{"targetRef":"fbb08abf-6db8-4013-9501-867ba03b47f1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Documents Table","declaredType":"sequenceFlow","id":"2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99","sourceRef":"63052e87-18e7-4af0-a16e-e616c1a3f399"},{"startQuantity":1,"outgoing":["5c34b62f-8fe6-4636-ae59-46b08edc09f7"],"incoming":["2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":129,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"ruleSet":[{"rules":[{"name":"rule 1","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TRule","ruleId":"7dabda14-1430-445d-850f-0b1907e63ded","type":"DECISION_TABLE","locale":"en","decisionTableHash":"5DDtIgN8HwR9Py6LLuvv6BAHwXGacIFeawBgr3vBQvw="}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TRuleSet","locale":"en"}]},"implementation":"##unspecified","name":"Documents Table","isForCompensation":false,"completionQuantity":1,"declaredType":"businessRuleTask","id":"fbb08abf-6db8-4013-9501-867ba03b47f1"},{"targetRef":"07416927-e99f-40e9-82cc-89726eecb560","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Documents List","declaredType":"sequenceFlow","id":"5c34b62f-8fe6-4636-ae59-46b08edc09f7","sourceRef":"fbb08abf-6db8-4013-9501-867ba03b47f1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"result","isCollection":false,"declaredType":"dataObject","id":"2056.9d4325ef-380c-4c88-88de-31a629e1ce15"},{"startQuantity":1,"outgoing":["84263f2a-431f-4013-9c5b-9aff8e3ecb90"],"incoming":["5c34b62f-8fe6-4636-ae59-46b08edc09f7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":280,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Documents List","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"07416927-e99f-40e9-82cc-89726eecb560","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.requiredDocuments = new tw.object.listOf.String();\r\nif (tw.local.result.length &gt; 0) {\r\n\ttw.local.requiredDocuments = tw.local.result.split(\",\")\r\n}"]}},{"targetRef":"ee8571dc-cc42-4e05-af80-796414a690ff","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"84263f2a-431f-4013-9c5b-9aff8e3ecb90","sourceRef":"07416927-e99f-40e9-82cc-89726eecb560"}],"laneSet":[{"id":"b3fdb829-944d-45e3-aa5a-8ba4808a3f2b","lane":[{"flowNodeRef":["63052e87-18e7-4af0-a16e-e616c1a3f399","ee8571dc-cc42-4e05-af80-796414a690ff","fbb08abf-6db8-4013-9501-867ba03b47f1","07416927-e99f-40e9-82cc-89726eecb560"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"4b6373ce-a70d-4c32-88e0-77e7de7b5df9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Validate Required Documents","declaredType":"process","id":"1.c04ab227-0184-4fcd-99e2-b165319d2807","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requiredDocuments","isCollection":true,"id":"2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6"}],"inputSet":[{"dataInputRefs":["2055.ca1eead8-e152-4ac6-8389-4431f391cc84","2055.1388ff03-e35d-4090-b3b9-0281f961e86e"]}],"outputSet":[{"dataOutputRefs":["2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Advance Payment\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.ca1eead8-e152-4ac6-8389-4431f391cc84"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Correspondent\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"documentSource","isCollection":false,"id":"2055.1388ff03-e35d-4090-b3b9-0281f961e86e"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ca1eead8-e152-4ac6-8389-4431f391cc84</processParameterId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Advance Payment"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>db1a93d2-94ad-4e36-bc27-3d54b1b69ffc</guid>
            <versionId>2e37fcbe-e76d-4d02-b13f-8ed7b87b1bbe</versionId>
        </processParameter>
        <processParameter name="documentSource">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1388ff03-e35d-4090-b3b9-0281f961e86e</processParameterId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Correspondent"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>78bc9782-**************-76a96992ba46</guid>
            <versionId>2b55fb61-777d-4e38-bc20-95cb70e167df</versionId>
        </processParameter>
        <processParameter name="requiredDocuments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6</processParameterId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2279b21d-e3a0-48c4-95e9-c4a3d4f97647</guid>
            <versionId>e297c105-1ae2-4050-9c59-5cd1f4acfa2f</versionId>
        </processParameter>
        <processVariable name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9d4325ef-380c-4c88-88de-31a629e1ce15</processVariableId>
            <description isNull="true" />
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>a01af772-84f1-4acc-a9ea-857f3e1e4ab0</guid>
            <versionId>643d954c-fbd0-404f-a3bb-fea45f31c929</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ee8571dc-cc42-4e05-af80-796414a690ff</processItemId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c9f3c8cb-9797-45d4-8f4d-338554055fa3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9</guid>
            <versionId>1f2a3b52-e1cb-4773-94e2-0a4235c3bbe7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c9f3c8cb-9797-45d4-8f4d-338554055fa3</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a48ef9f3-1f65-4655-9d38-870ec2deefc9</guid>
                <versionId>407b5f81-4325-474f-9d17-42b20d17be5b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.07416927-e99f-40e9-82cc-89726eecb560</processItemId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <name>Set Documents List</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f0ad1487-5565-42ab-89d4-094aa32f5504</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d8</guid>
            <versionId>a01cc3fb-60db-4324-b82a-06651286ec11</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="280" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f0ad1487-5565-42ab-89d4-094aa32f5504</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.requiredDocuments = new tw.object.listOf.String();&#xD;
if (tw.local.result.length &gt; 0) {&#xD;
	tw.local.requiredDocuments = tw.local.result.split(",")&#xD;
}</script>
                <isRule>false</isRule>
                <guid>0124da10-9ad2-4edc-afa0-37845b5c1d81</guid>
                <versionId>4d5b0b86-bc37-462d-8c26-37725be1c939</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fbb08abf-6db8-4013-9501-867ba03b47f1</processItemId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <name>Documents Table</name>
            <tWComponentName>ILOGDecision</tWComponentName>
            <tWComponentId>3026.375a0bb1-e330-4eb3-bb2f-640bed0c6f64</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61da</guid>
            <versionId>d350af28-9f44-4945-b90e-394947d4067d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="129" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <iLogDecisionId>3026.375a0bb1-e330-4eb3-bb2f-640bed0c6f64</iLogDecisionId>
                <definition>&lt;iLogDecision&gt;
  &lt;rule&gt;
    &lt;name&gt;rule 1&lt;/name&gt;
    &lt;ruleId&gt;7dabda14-1430-445d-850f-0b1907e63ded&lt;/ruleId&gt;
    &lt;DT xmlns="http://schemas.ilog.com/Rules/7.0/DecisionTable" Version="7.0"&gt;
      &lt;Body&gt;
        &lt;Properties&gt;
          &lt;Property Name="UI.MediaType"&gt;&lt;![CDATA[Web]]&gt;&lt;/Property&gt;
        &lt;/Properties&gt;
        &lt;Structure&gt;
          &lt;ConditionDefinitions&gt;
            &lt;ConditionDefinition Id="C0"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[requestType contains &lt;a string&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ConditionDefinition&gt;
            &lt;ConditionDefinition Id="C2"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[documentSource is one of &lt;strings&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ConditionDefinition&gt;
          &lt;/ConditionDefinitions&gt;
          &lt;ActionDefinitions&gt;
            &lt;ActionDefinition Id="A0"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[set result to &lt;a string&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ActionDefinition&gt;
          &lt;/ActionDefinitions&gt;
        &lt;/Structure&gt;
        &lt;Contents&gt;
          &lt;Partition DefId="C0"&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["Advance Payment"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["ICAP"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Acknowledgement"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Payment"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
          &lt;/Partition&gt;
        &lt;/Contents&gt;
      &lt;/Body&gt;
      &lt;Resources DefaultLocale="en_US"&gt;
        &lt;ResourceSet Locale="en"&gt;
          &lt;Data Name="Definitions(C0)#HeaderText"&gt;&lt;![CDATA[Request Type]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(A0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C2)#HeaderText"&gt;&lt;![CDATA[Document Source]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C2)#Width"&gt;&lt;![CDATA[227]]&gt;&lt;/Data&gt;
        &lt;/ResourceSet&gt;
      &lt;/Resources&gt;
    &lt;/DT&gt;
    &lt;locale&gt;en&lt;/locale&gt;
    &lt;type&gt;DECISION_TABLE&lt;/type&gt;
  &lt;/rule&gt;
  &lt;locale&gt;en&lt;/locale&gt;
&lt;/iLogDecision&gt;</definition>
                <guid>11858523-11b9-4e64-a6eb-4b87d5789e30</guid>
                <versionId>4e9a35e6-68ff-4411-b78d-1de09eac6a9e</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fbb08abf-6db8-4013-9501-867ba03b47f1</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Validate Required Documents" id="1.c04ab227-0184-4fcd-99e2-b165319d2807" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.ca1eead8-e152-4ac6-8389-4431f391cc84">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Advance Payment"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="documentSource" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1388ff03-e35d-4090-b3b9-0281f961e86e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Correspondent"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="requiredDocuments" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" id="2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.ca1eead8-e152-4ac6-8389-4431f391cc84</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.1388ff03-e35d-4090-b3b9-0281f961e86e</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b3fdb829-944d-45e3-aa5a-8ba4808a3f2b">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="4b6373ce-a70d-4c32-88e0-77e7de7b5df9" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>63052e87-18e7-4af0-a16e-e616c1a3f399</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ee8571dc-cc42-4e05-af80-796414a690ff</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fbb08abf-6db8-4013-9501-867ba03b47f1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>07416927-e99f-40e9-82cc-89726eecb560</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="63052e87-18e7-4af0-a16e-e616c1a3f399">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ee8571dc-cc42-4e05-af80-796414a690ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>84263f2a-431f-4013-9c5b-9aff8e3ecb90</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="63052e87-18e7-4af0-a16e-e616c1a3f399" targetRef="fbb08abf-6db8-4013-9501-867ba03b47f1" name="To Documents Table" id="2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:businessRuleTask implementation="##unspecified" name="Documents Table" id="fbb08abf-6db8-4013-9501-867ba03b47f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="129" y="57" width="95" height="70" />
                            
                            
                            <ns3:ruleSet>
                                
                                
                                <ns3:locale>en</ns3:locale>
                                
                                
                                <ns3:rules>
                                    
                                    
                                    <ns3:ruleId>7dabda14-1430-445d-850f-0b1907e63ded</ns3:ruleId>
                                    
                                    
                                    <ns3:name>rule 1</ns3:name>
                                    
                                    
                                    <ns3:type>DECISION_TABLE</ns3:type>
                                    
                                    
                                    <ns3:decisionTableDefinition>&lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;DT xmlns="http://schemas.ilog.com/Rules/7.0/DecisionTable" Version="7.0"&gt;&lt;Body&gt;&lt;Properties&gt;&lt;Property Name="UI.MediaType"&gt;&lt;![CDATA[Web]]&gt;&lt;/Property&gt;&lt;/Properties&gt;&lt;Structure&gt;&lt;ConditionDefinitions&gt;&lt;ConditionDefinition Id="C0"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[requestType contains &lt;a string&gt; ]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ConditionDefinition&gt;&lt;ConditionDefinition Id="C2"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[documentSource is one of &lt;strings&gt; ]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ConditionDefinition&gt;&lt;/ConditionDefinitions&gt;&lt;ActionDefinitions&gt;&lt;ActionDefinition Id="A0"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[set result to &lt;a string&gt; 
]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ActionDefinition&gt;&lt;/ActionDefinitions&gt;&lt;/Structure&gt;&lt;Contents&gt;&lt;Partition DefId="C0"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Advance Payment"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["ICAP"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Acknowledgement"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Payment"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Contents&gt;&lt;/Body&gt;&lt;Resources DefaultLocale="en_US"&gt;&lt;ResourceSet Locale="en"&gt;&lt;Data Name="Definitions(C0)#HeaderText"&gt;&lt;![CDATA[Request Type]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(A0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C2)#HeaderText"&gt;&lt;![CDATA[Document Source]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C2)#Width"&gt;&lt;![CDATA[227]]&gt;&lt;/Data&gt;&lt;/ResourceSet&gt;&lt;/Resources&gt;&lt;/DT&gt;</ns3:decisionTableDefinition>
                                    
                                    
                                    <ns3:decisionTableHash>5DDtIgN8HwR9Py6LLuvv6BAHwXGacIFeawBgr3vBQvw=</ns3:decisionTableHash>
                                    
                                    
                                    <ns3:locale>en</ns3:locale>
                                    
                                
                                </ns3:rules>
                                
                            
                            </ns3:ruleSet>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.e62724dc-e59f-4ef1-9084-c78bdb6ced99</ns16:incoming>
                        
                        
                        <ns16:outgoing>5c34b62f-8fe6-4636-ae59-46b08edc09f7</ns16:outgoing>
                        
                    
                    </ns16:businessRuleTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="fbb08abf-6db8-4013-9501-867ba03b47f1" targetRef="07416927-e99f-40e9-82cc-89726eecb560" name="To Set Documents List" id="5c34b62f-8fe6-4636-ae59-46b08edc09f7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="result" id="2056.9d4325ef-380c-4c88-88de-31a629e1ce15">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Documents List" id="07416927-e99f-40e9-82cc-89726eecb560">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="280" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5c34b62f-8fe6-4636-ae59-46b08edc09f7</ns16:incoming>
                        
                        
                        <ns16:outgoing>84263f2a-431f-4013-9c5b-9aff8e3ecb90</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.requiredDocuments = new tw.object.listOf.String();&#xD;
if (tw.local.result.length &gt; 0) {&#xD;
	tw.local.requiredDocuments = tw.local.result.split(",")&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="07416927-e99f-40e9-82cc-89726eecb560" targetRef="ee8571dc-cc42-4e05-af80-796414a690ff" name="To End" id="84263f2a-431f-4013-9c5b-9aff8e3ecb90">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.84263f2a-431f-4013-9c5b-9aff8e3ecb90</processLinkId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.07416927-e99f-40e9-82cc-89726eecb560</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ee8571dc-cc42-4e05-af80-796414a690ff</toProcessItemId>
            <guid>de971009-ccae-4d5f-9018-74028f382d2a</guid>
            <versionId>73c66c05-8418-40b7-8c1e-d823545b7923</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.07416927-e99f-40e9-82cc-89726eecb560</fromProcessItemId>
            <toProcessItemId>2025.ee8571dc-cc42-4e05-af80-796414a690ff</toProcessItemId>
        </link>
        <link name="To Set Documents List">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5c34b62f-8fe6-4636-ae59-46b08edc09f7</processLinkId>
            <processId>1.c04ab227-0184-4fcd-99e2-b165319d2807</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fbb08abf-6db8-4013-9501-867ba03b47f1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.07416927-e99f-40e9-82cc-89726eecb560</toProcessItemId>
            <guid>cb93ede5-7c54-472f-84fb-e7727fe4fb6f</guid>
            <versionId>e17dc884-5c72-4f6b-bb79-140e57387a49</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fbb08abf-6db8-4013-9501-867ba03b47f1</fromProcessItemId>
            <toProcessItemId>2025.07416927-e99f-40e9-82cc-89726eecb560</toProcessItemId>
        </link>
    </process>
</teamworks>

