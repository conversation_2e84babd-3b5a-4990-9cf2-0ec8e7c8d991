<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.ed284446-1ac5-42cf-9523-6dd8086928a0" name="Validation Helper">
        <lastModified>1714364390848</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;273bba6a-96a1-4e9c-81f3-e2401d025234&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ErrorSection_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ebd8d84-4d17-4e3d-8512-778d93a7de37&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Validation Section&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77591e43-5001-4b69-8a6c-329a3e1b3808&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a7606dc-d49a-4da1-83d1-ff1a2ac3753b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40188bf6-1d4b-4c2e-801b-41fa549c57d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;329ee8c2-3a08-4ee7-864e-81ddc65cae34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setVisible(false,true)&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;82455ba0-5391-4c46-811f-e6014be96d92&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8ee61a1b-a2c5-4c91-893e-81ea74bbb81e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;267d5b17-ea64-4571-8e46-8fe453e62b0f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Display text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2219e09f-a16e-4fa1-851e-de4c9cec9b31&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32db1a16-ad94-4207-8ec7-2d23bca9863f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc208d2a-37ce-4795-81b2-a5b4b2d22729&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ce0143a-b85e-4ed1-83b7-4d2e9aabdd02&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37e1e574-eb74-4aa7-86e3-8cd4a0a767fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;M&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1907ac9-3e08-4136-8154-d81d86bee653&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textWrap&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"K"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction></viewJsFunction>
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction></validateJsFunction>
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>2c36b364-d7bd-4680-9174-13af88a77366</guid>
        <versionId>8042f717-d79f-43f0-a7a6-247937b596c9</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <configOption name="runTimeValid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.fdb3400a-778c-41bd-919c-953d9b6da46c</coachViewConfigOptionId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>aa9e9378-1f1e-4f82-835e-be0635894aee</guid>
            <versionId>72a62865-ba1c-4c74-84d5-bc5d3712085c</versionId>
        </configOption>
        <configOption name="stop">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.5ede0216-8e89-4748-8a93-c3c69083aad9</coachViewConfigOptionId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>ca85797a-ee5a-4dab-ac50-e583a9100ad1</guid>
            <versionId>31b486ab-7a2e-4fc5-a723-4302131ac9f7</versionId>
        </configOption>
        <configOption name="disableSubmit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8492572b-f4ff-42b1-80f6-3f238b98d244</coachViewConfigOptionId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>3f920e46-42e8-46f0-aa58-d8433dd07167</guid>
            <versionId>c177e57e-2452-40cb-a2d1-bbfc8db64e0e</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.80d476d9-11e0-4c5c-a5b5-def3ffc581a7</coachViewInlineScriptId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>_this = this;&#xD;
&#xD;
var displayText = "Display_Text1";&#xD;
const generalTab = "GENERAL";&#xD;
var tabSectionId = "";&#xD;
var tabSection = "";&#xD;
var counter = 0;&#xD;
var errorSectionId = "ErrorSection_Panel1";&#xD;
var stopLib = _this.context.options.stop.get("value") || false;&#xD;
var runTime = _this.context.options.runTimeValid.get("value") || false;&#xD;
//var disableSubmit = _this.context.options.disableSubmit.get("value") || false;&#xD;
&#xD;
_this.startLib2 = function () {&#xD;
	tabSection = document.querySelector('[role="tablist"]');&#xD;
	tabSectionId = _this.getTabSectionId(tabSection);&#xD;
&#xD;
	var viewErrorList = bpmext.ui.getInvalidViews();&#xD;
&#xD;
	//Disable submit until all valid&#xD;
	// if (viewErrorList.length &gt; 0) {&#xD;
	// 	if (disableSubmit) {&#xD;
	// 		bpmext.ui.getView("DC_Templete1").enableSubmit(false);&#xD;
	// 	}&#xD;
	// } else {&#xD;
	// 	_this.resetRedCircle();&#xD;
	// 	bpmext.ui.getView("DC_Templete1").enableSubmit(true);&#xD;
	// }&#xD;
&#xD;
	if (!viewErrorList || viewErrorList.length === 0) {&#xD;
		_this.ui.get(errorSectionId).setVisible(false, true);&#xD;
		return;&#xD;
	}&#xD;
&#xD;
	const errMapList = viewErrorList&#xD;
		.map((view) =&gt; _this.constructErrorMap(view))&#xD;
		.filter(function (obj) {&#xD;
			return obj != null;&#xD;
		});&#xD;
&#xD;
	var viewMapList = [];&#xD;
	_this.resetRedCircle();&#xD;
	viewMapList = _this.organizeErrorsByTab(errMapList);&#xD;
&#xD;
	// Add counter red circle&#xD;
	_this.resetRedCircle();&#xD;
	viewMapList.forEach((viewMap) =&gt; _this.addRedCircleToTab(viewMap));&#xD;
&#xD;
	//Add panel with tabs and messages&#xD;
	_this.ui.get(errorSectionId).setVisible(true, true);&#xD;
	setTimeout(() =&gt; {&#xD;
		_this.constructValidPanel(viewMapList);&#xD;
	}, 200);&#xD;
};&#xD;
&#xD;
_this.getTabInfoFirst = function (tabSection) {&#xD;
	const tabElementList = tabSection?.children;&#xD;
	var tabsInfo = {};&#xD;
	for (var i = 0; i &lt; tabElementList.length; i++) {&#xD;
		var tabElement = tabElementList[i];&#xD;
&#xD;
		var tabInnerText = tabElement.innerText.split("\n")[0].replaceAll(" ", "");&#xD;
		if (!tabInnerText || tabElement.getAttribute("role") !== "tab") continue;&#xD;
&#xD;
		tabsInfo[tabInnerText] = {&#xD;
			tabDomID: tabElement.id,&#xD;
			tabPathId: i,&#xD;
		};&#xD;
	}&#xD;
&#xD;
	return tabsInfo;&#xD;
};&#xD;
&#xD;
_this.resetRedCircle = function () {&#xD;
	const redCircles = document.querySelectorAll(".red-circle");&#xD;
	if (!redCircles) return null;&#xD;
	redCircles.forEach((circle) =&gt; circle.remove());&#xD;
};&#xD;
&#xD;
_this.getTabSectionId = function (tabSection) {&#xD;
	if (!tabSection) return;&#xD;
	var currentElement = tabSection;&#xD;
&#xD;
	while (currentElement &amp;&amp; currentElement !== document.body) {&#xD;
		if (currentElement.classList.contains("Tab_Section")) {&#xD;
			tabSectionId = currentElement.getAttribute("control-name");&#xD;
			break;&#xD;
		}&#xD;
&#xD;
		currentElement = currentElement.parentElement;&#xD;
	}&#xD;
&#xD;
	return tabSectionId;&#xD;
};&#xD;
&#xD;
_this.constructValidPanel = function (viewMapList) {&#xD;
	if (!viewMapList || viewMapList.length == 0) return;&#xD;
	var tabNameListHTML = "";&#xD;
&#xD;
	for (let i = 0; i &lt; viewMapList.length; i++) {&#xD;
		var tabData = viewMapList[i].tab;&#xD;
		var messageList = viewMapList[i].messages;&#xD;
&#xD;
		if (!tabData) continue;&#xD;
&#xD;
		var tabDomId = tabData.domId;&#xD;
		var tabName = tabData.name || generalTab;&#xD;
		var errorListId = `error-list-${tabDomId}`;&#xD;
		var tabIndex = tabData.pathId;&#xD;
		var errorListHTML = generateErrorListHTML(messageList, tabName, tabIndex);&#xD;
&#xD;
		tabNameListHTML += generateTabItemHTML(&#xD;
			tabName,&#xD;
			tabDomId,&#xD;
			errorListId,&#xD;
			tabIndex,&#xD;
			errorListHTML&#xD;
		);&#xD;
	}&#xD;
&#xD;
	tabNameListHTML = `&lt;ul class="tab-list"&gt;${tabNameListHTML}&lt;/ul&gt;`;&#xD;
	_this.ui.get(displayText).setText(tabNameListHTML);&#xD;
&#xD;
	function generateErrorListHTML(listOfErrors, tabName, tabIndex) {&#xD;
		return listOfErrors&#xD;
			.map(function (error, i) {&#xD;
				var fieldDomId = error.field.domId;&#xD;
				var fieldPathId = error.field.pathId;&#xD;
				var targetMessage = error.message;&#xD;
				var panelString = "";&#xD;
				error.field.panels.forEach((element) =&gt; {&#xD;
					panelString += element + "@@";&#xD;
				});&#xD;
&#xD;
				return `&lt;li&gt;&lt;a href="#${fieldDomId}" class="message-link" message-id="${fieldDomId}" onclick="_this.activateField('${tabName}','${panelString}','${tabIndex}','${fieldPathId}');"&gt;${targetMessage}&lt;/a&gt;&lt;/li&gt;`;&#xD;
			})&#xD;
			.join("");&#xD;
	}&#xD;
&#xD;
	function generateTabItemHTML(tabName, tabDomId, errorListId, tabIndex, errorListHTML) {&#xD;
		return `&lt;li class="tab-item"&gt;&#xD;
			&lt;div class="tab-container"&gt;&#xD;
				&lt;div class="tab-header"&gt;&#xD;
				&lt;a href="#${tabDomId}" class="tab-name"&#xD;
					onclick="_this.activateTab('${tabDomId}','${tabName}','${tabIndex}');window.scrollTo(0,document.getElementById('${tabDomId}').offsetTop+150);"&gt;${tabName}:&lt;/a&gt;&#xD;
				&lt;button id="toggleBtn_${errorListId}" class="toggle-btn" onclick="_this.toggleErrorList('${errorListId}');"&gt;&amp;#11165;&lt;/button&gt;&#xD;
				&lt;/div&gt;&#xD;
				&lt;ul id="${errorListId}" class="error-list" style="display:none;"&gt;${errorListHTML}&lt;/ul&gt;&#xD;
			&lt;/div&gt;&#xD;
		&lt;/li&gt;`;&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.activateTab = function (tabDomId, tabName, tabIndex) {&#xD;
	if (!tabName || !tabIndex) return;&#xD;
&#xD;
	if (tabName !== generalTab) {&#xD;
		page.ui.get(tabSectionId).setCurrentPane(tabIndex);&#xD;
		if (tabDomId) {&#xD;
			var tabElement = document.getElementById(tabDomId);&#xD;
			_this.highLighElement(tabElement);&#xD;
		}&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.toggleErrorList = function (errorListId) {&#xD;
	if (!errorListId) return;&#xD;
&#xD;
	var errorList = document.getElementById(errorListId);&#xD;
	var toggleBtn = document.getElementById(`toggleBtn_${errorListId}`);&#xD;
&#xD;
	if (errorList.style.display === "none" || errorList.style.display === "") {&#xD;
		errorList.style.display = "block";&#xD;
		toggleBtn.innerHTML = "&amp;#11167;"; // Change to expanded icon&#xD;
	} else {&#xD;
		errorList.style.display = "none";&#xD;
		toggleBtn.innerHTML = "&amp;#11165;"; // Change to collapsed icon&#xD;
	}&#xD;
&#xD;
	toggleBtn.classList.toggle("collapsed");&#xD;
};&#xD;
&#xD;
_this.activateField = function (tabName, panelString, tabIndex, fieldPathId) {&#xD;
	if (!fieldPathId) return;&#xD;
	var panelList = panelString.split("@@").filter((e) =&gt; e !== "");&#xD;
	// console.dir(panelList);&#xD;
	if (tabIndex &amp;&amp; tabSectionId &amp;&amp; tabName !== generalTab) {&#xD;
		page.ui.get(tabSectionId).setCurrentPane(tabIndex);&#xD;
	}&#xD;
	if (panelList &amp;&amp; panelList.length &gt; 0) {&#xD;
		for (let i = 0; i &lt; panelList.length; i++) {&#xD;
			page.ui.get(panelList[i]).expand();&#xD;
		}&#xD;
		setTimeout(function () {&#xD;
			_this.focusOnElement(fieldPathId);&#xD;
		}, 300);&#xD;
	}&#xD;
	_this.focusOnElement(fieldPathId);&#xD;
};&#xD;
&#xD;
_this.focusOnElement = function (fieldPathId) {&#xD;
	var fieldElement = page.ui.get(fieldPathId).context.element;&#xD;
	_this.highLighElement(fieldElement);&#xD;
&#xD;
	page.ui.get(fieldPathId).focus();&#xD;
};&#xD;
&#xD;
_this.highLighElement = function (fieldElement) {&#xD;
	if (!fieldElement) return;&#xD;
&#xD;
	fieldElement.classList.add("highlighted-field");&#xD;
	setTimeout(function () {&#xD;
		fieldElement.classList.remove("highlighted-field");&#xD;
	}, 1500);&#xD;
};&#xD;
&#xD;
_this.addRedCircleToTab = function (viewMap) {&#xD;
	if (!viewMap.tab.domId) return;&#xD;
&#xD;
	const messagesCount = viewMap.messages.length;&#xD;
	const tabDomId = viewMap.tab.domId;&#xD;
	const tabElement = document.getElementById(tabDomId);&#xD;
	if (!tabElement) return;&#xD;
&#xD;
	const existingCircle = tabElement.querySelector(".red-circle");&#xD;
&#xD;
	if (!existingCircle) {&#xD;
		const redCircle = document.createElement("div");&#xD;
		redCircle.classList.add("red-circle");&#xD;
		redCircle.innerText = messagesCount;&#xD;
		tabElement.appendChild(redCircle);&#xD;
	} else {&#xD;
		existingCircle.innerText = messagesCount;&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.constructErrorMap = function (fieldElement) {&#xD;
	if (!fieldElement || !fieldElement.context.element || !fieldElement._bpmextViewNode)&#xD;
		return null;&#xD;
&#xD;
	var fieldDomId = fieldElement.context.element.id;&#xD;
	var fieldParents = _this.getFieldParents(fieldDomId);&#xD;
	var isField = Object.keys(fieldElement._bpmextViewNode._data.context.subview).length === 0;&#xD;
	if (isField) {&#xD;
		errorMap = {&#xD;
			field: {&#xD;
				message: fieldElement._bpmextVE?.errors?.[0]?.message || "",&#xD;
				domId: fieldDomId,&#xD;
				pathId: fieldElement.context.element.getAttribute("control-name"),&#xD;
				viewId: fieldElement.context.element.getAttribute("data-viewid"),&#xD;
			},&#xD;
&#xD;
			panels: fieldParents.cPanelList /*[list of "SPARKCPanel"]*/,&#xD;
&#xD;
			view: fieldParents.viewObj,&#xD;
		};&#xD;
		return errorMap;&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.getFieldParents = function (elementId) {&#xD;
	var fieldParents = {&#xD;
		viewObj: {&#xD;
			name: "",&#xD;
			domId: "",&#xD;
			pathId: "",&#xD;
		},&#xD;
		cPanelList: [],&#xD;
	};&#xD;
	const cPanelClass = "Collapsible_Panel";&#xD;
	const tabClass = "tab-pane";&#xD;
&#xD;
	let currentElement = document.getElementById(elementId);&#xD;
&#xD;
	while (currentElement &amp;&amp; currentElement !== document.body) {&#xD;
		if (currentElement.classList.contains(tabClass)) {&#xD;
			fieldParents.viewObj.name = currentElement.getAttribute("aria-label");&#xD;
			fieldParents.viewObj.domId = currentElement.id;&#xD;
			fieldParents.viewObj.pathId = currentElement.getAttribute("control-name");&#xD;
		} else if (currentElement.classList.contains(cPanelClass)) {&#xD;
			fieldParents.cPanelList.unshift(currentElement.getAttribute("control-name"));&#xD;
		}&#xD;
&#xD;
		currentElement = currentElement.parentNode;&#xD;
	}&#xD;
	return fieldParents;&#xD;
};&#xD;
&#xD;
_this.organizeErrorsByTab = function (errorList) {&#xD;
	const viewMap = new Map();&#xD;
	var tabsInfo = {};&#xD;
	if (tabSection) {&#xD;
		tabsInfo = _this.getTabInfoFirst(tabSection);&#xD;
	}&#xD;
&#xD;
	errorList.forEach((error) =&gt; {&#xD;
		if (error) {&#xD;
			var viewName = error.view.name;&#xD;
			if (viewName) {&#xD;
				var tabName = viewName?.replaceAll(" ", "") + "";&#xD;
			}&#xD;
&#xD;
			if (!viewMap.has(viewName)) {&#xD;
				viewMap.set(viewName, {&#xD;
					view: {&#xD;
						name: viewName,&#xD;
						domId: error.view.domId,&#xD;
						pathId: error.view.pathId,&#xD;
					},&#xD;
					messages: [],&#xD;
					tab: {&#xD;
						name: viewName,&#xD;
						domId: tabsInfo[tabName]?.tabDomID,&#xD;
						pathId: tabsInfo[tabName]?.tabPathId,&#xD;
					},&#xD;
				});&#xD;
			}&#xD;
&#xD;
			// Add the error message to the corresponding tab entry&#xD;
			viewMap.get(viewName).messages.push({&#xD;
				message: error.field.message,&#xD;
				field: {&#xD;
					domId: error.field.domId,&#xD;
					pathId: error.field.pathId,&#xD;
					viewId: error.field.viewId,&#xD;
					panels: [...error.panels],&#xD;
				},&#xD;
			});&#xD;
		}&#xD;
	});&#xD;
&#xD;
	// Convert the map values to an array of tab objects&#xD;
	const organizedTabs = [...viewMap.values()];&#xD;
	console.dir(organizedTabs);&#xD;
	return organizedTabs;&#xD;
};&#xD;
&#xD;
_this.getTabInfo = function (viewName) {&#xD;
	if (!viewName || viewName == "") return;&#xD;
&#xD;
	const tabElementList = tabSection?.children;&#xD;
&#xD;
	for (var i = 0; i &lt; tabElementList.length; i++) {&#xD;
		var tabElement = tabElementList[i];&#xD;
&#xD;
		var tabInnerText = tabElement.innerText.split("\n")[0].trim();&#xD;
		if (!tabInnerText || tabElement.role != "tab") return;&#xD;
		console.log(tabInnerText.replaceAll(" ", ""));&#xD;
&#xD;
		if (tabInnerText.replaceAll(" ", "") == viewName.replaceAll(" ", "")) {&#xD;
			return `${viewName.trim()}&amp;${tabElement.id}&amp;${i}`;&#xD;
		}&#xD;
	}&#xD;
};&#xD;
&#xD;
//=======================================REQUIRED===============================================//&#xD;
require(["com.ibm.bpm.coach/engine"], function (engine) {&#xD;
	var dve = engine._deliverValidationEvents;&#xD;
	engine._deliverValidationEvents = function (event, viewMap, isClear) {&#xD;
		dve(event, viewMap, isClear); // original processing first&#xD;
		// console.log("_deliverValidationEvents", event, viewMap, isClear);&#xD;
	}.bind(engine);&#xD;
	var hve = engine.handleValidationEvent;&#xD;
	engine.handleValidationEvent = function (event) {&#xD;
		hve(event);&#xD;
		// console.log("handleValidationEvent", event);&#xD;
		if (!stopLib) {&#xD;
			_this.startLib2();&#xD;
		}&#xD;
	}.bind(engine);&#xD;
});&#xD;
&#xD;
var uvvs = bpmext &amp;&amp; bpmext.ui &amp;&amp; bpmext.ui.updateViewValidationState;&#xD;
if (uvvs) {&#xD;
	bpmext.ui.updateViewValidationState = function (view, event) {&#xD;
		uvvs(view, event); //call original handler&#xD;
		// console.log("updateViewValidationState", view, event);&#xD;
		if (!stopLib &amp;&amp; runTime) {&#xD;
			_this.startLib2();&#xD;
		}&#xD;
	};&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>bae8bb9d-5701-4a50-9484-0982c3375387</guid>
            <versionId>e7bcd05b-8ec7-480b-a737-bc2e60171403</versionId>
        </inlineScript>
        <inlineScript name="Inline CSS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.ff591ee8-d67a-41be-9583-499eabd206cc</coachViewInlineScriptId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <scriptType>CSS</scriptType>
            <scriptBlock>/* Style for the red circle counter */&#xD;
.red-circle {&#xD;
	position: absolute;&#xD;
	top: 0;&#xD;
	right: 0;&#xD;
	width: 17px;&#xD;
	height: 17px;&#xD;
	background-color: red;&#xD;
	border-radius: 50%;&#xD;
	display: flex;&#xD;
	justify-content: center;&#xD;
	align-items: center;&#xD;
	color: white;&#xD;
	font-weight: bold;&#xD;
}&#xD;
&#xD;
.tab-link {&#xD;
	font-size: medium;&#xD;
}&#xD;
&#xD;
/* Style for the tab list */&#xD;
.tab-list {&#xD;
	list-style-type: none;&#xD;
	padding: 0;&#xD;
}&#xD;
&#xD;
/* Style for each tab item */&#xD;
.tab-item {&#xD;
	margin-bottom: 10px;&#xD;
	border: none;&#xD;
	padding: 5px;&#xD;
	display: flex;&#xD;
	align-items: center;&#xD;
	list-style: none;&#xD;
}&#xD;
&#xD;
/* Style for the tab name */&#xD;
.tab-name {&#xD;
	font-size: 16px;&#xD;
	margin-right: 10px;&#xD;
}&#xD;
&#xD;
.tab-container {&#xD;
	display: flex;&#xD;
	flex-direction: column;&#xD;
}&#xD;
&#xD;
.tab-header {&#xD;
	display: flex;&#xD;
	align-items: center;&#xD;
}&#xD;
&#xD;
/* Style for the toggle button */&#xD;
.toggle-btn {&#xD;
	font-size: 18px;&#xD;
	cursor: pointer;&#xD;
	background: none; /* Remove background color */&#xD;
	border: none; /* Remove border */&#xD;
	padding: 5px 10px; /* Add padding */&#xD;
	margin-left: -10px;&#xD;
	transition: transform 0.1s ease; /*&#xD;
}&#xD;
&#xD;
.toggle-btn.collapsed {&#xD;
	transform: rotate(-90deg);&#xD;
	/* Rotate the button for the collapsed state */&#xD;
}&#xD;
&#xD;
.toggle-btn:active {&#xD;
	transform: scale(0.95); /* Add a simple click animation */&#xD;
}&#xD;
&#xD;
.toggle-btn:hover {&#xD;
	background-color: #ddd;&#xD;
	/* Change background color on hover */&#xD;
}&#xD;
&#xD;
.tab {&#xD;
	position: relative;&#xD;
}&#xD;
&#xD;
.tab::after {&#xD;
	content: attr(error-count);&#xD;
	color: red;&#xD;
	font-size: 10px;&#xD;
	position: absolute;&#xD;
	right: 5px;&#xD;
	top: 5px;&#xD;
}&#xD;
&#xD;
/* Add animation for the highlighted field */&#xD;
.highlighted-field {&#xD;
	animation-name: highlight;&#xD;
	animation-duration: 1.5s;&#xD;
}&#xD;
&#xD;
@keyframes highlight {&#xD;
	from {&#xD;
		background-color: yellow; /* Change this to the starting highlight color */&#xD;
	}&#xD;
	to {&#xD;
		background-color: initial; /* Change this to the ending background color */&#xD;
	}&#xD;
}&#xD;
&#xD;
.error-list {&#xD;
	display: none;&#xD;
	/* Make the error list a block element */&#xD;
	margin-left: 50px;&#xD;
	/* Remove default margin */&#xD;
	padding: 0;&#xD;
	/* Remove default padding */&#xD;
}&#xD;
&#xD;
.message-link {&#xD;
	cursor: pointer;&#xD;
}&#xD;
</scriptBlock>
            <seq>1</seq>
            <description></description>
            <guid>bbc56fc9-29e7-4057-b588-2e7187aa081d</guid>
            <versionId>ab80e7f1-a2fd-409c-a33b-db6755b4259e</versionId>
        </inlineScript>
        <inlineScript name="Header HTML">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.ef0de374-6a94-4a37-a6a1-bf1ac143d5a8</coachViewInlineScriptId>
            <coachViewId>64.ed284446-1ac5-42cf-9523-6dd8086928a0</coachViewId>
            <scriptType>HTML</scriptType>
            <scriptBlock></scriptBlock>
            <seq>2</seq>
            <description></description>
            <guid>356221b7-8bdd-4c89-a20b-7919054bcaa7</guid>
            <versionId>e3bcdd3c-58f1-4ed2-b401-73b3ebb6a67d</versionId>
        </inlineScript>
    </coachView>
</teamworks>

