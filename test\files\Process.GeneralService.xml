<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.350b261f-7e76-4fd5-abe4-25817b0090f3" name="UCA Service">
        <lastModified>1569970375653</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <processId>1.350b261f-7e76-4fd5-abe4-25817b0090f3</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId isNull="true" />
        <isRootProcess>false</isRootProcess>
        <processType>6</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType isNull="true" />
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>43200</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fea</guid>
        <versionId>1c04d272-5a1d-4c65-8c34-f974aebaa8c9</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <processParameter name="Untitled1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dad7b4bd-b220-418f-b226-f5e7cebd3c26</processParameterId>
            <processId>1.350b261f-7e76-4fd5-abe4-25817b0090f3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2ac263b2-898d-43b3-89e4-d607001afe9f</guid>
            <versionId>4a188fc3-df16-49ab-bc90-8cc1c6b836cc</versionId>
        </processParameter>
        <processParameter name="Untitled1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c47fab1e-fdc7-4651-bbef-ea3c295af63c</processParameterId>
            <processId>1.350b261f-7e76-4fd5-abe4-25817b0090f3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e134dbc9-8060-4923-b750-1b0550499685</guid>
            <versionId>ccc7071d-9ae4-4473-b31a-6d33641d065f</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6702b72d-2580-4616-a871-7c4f9a6a183d</processItemId>
            <processId>1.350b261f-7e76-4fd5-abe4-25817b0090f3</processId>
            <name>Terminar</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3c66d301-0670-48a0-b841-ecb280d2de15</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe9</guid>
            <versionId>93f3cf9b-6a7e-4b5c-9668-b1ccb3a479da</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="700" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3c66d301-0670-48a0-b841-ecb280d2de15</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>996b4acd-b291-4969-bca6-0b70bf56cb5d</guid>
                <versionId>5675faf1-cf14-406c-b06f-257e4c5ecde1</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId isNull="true" />
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="20" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
    </process>
</teamworks>

