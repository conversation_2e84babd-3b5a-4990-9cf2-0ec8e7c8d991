<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b7869400-09ed-4f15-972f-39c44a324089" name="1.b7869400-09ed-4f15-972f-39c44a324089">
        <lastModified>1732629943087</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b7869400-09ed-4f15-972f-39c44a324089</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fdf63a2a-e467-4937-8c92-9c5a7b0142a0</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description></description>
        <guid>guid:2340c4cc899e0880:-54556756:19341292354:37c6</guid>
        <versionId>3d49ff53-f14e-445c-826a-2e1e534e63f3</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c17ea5ee-5c4e-47fb-b152-544c562cb81f"},{"outgoing":["2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13","2027.a4c861c9-ca97-4d00-9865-400b152249d7"],"incoming":["2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af","2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":350,"y":177,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contentBoxId":"TaskInput","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"4bedd238-ff1c-4809-9f2b-5fa93ecbb4d9"},{"contentBoxId":"TaskOutput","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"58bf45e0-a967-46fd-be72-c64df5fc83d0"}],"layoutItemId":"Inline_User_Task_Template","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7577604f-5be8-438f-a9a4-749c681a8dd9","optionName":"@label","value":"Inline User Task Template"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d261750b-1787-4092-a923-eed5e3179f76","optionName":"@helpText","value":""}],"viewUUID":"64.dc5fd75e-5b13-460c-8cb8-1e463c729751","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"96d730a6-7ac9-4386-a5f0-450bd325ae33"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach","isForCompensation":false,"completionQuantity":1,"id":"2025.8395d7b0-27d9-4711-9b1f-f74002d944fd"},{"incoming":["2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":700,"y":200,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"fb5551b1-63a8-424d-97d9-99eaccdbb212"},{"targetRef":"2025.8395d7b0-27d9-4711-9b1f-f74002d944fd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af","sourceRef":"c17ea5ee-5c4e-47fb-b152-544c562cb81f"},{"targetRef":"fb5551b1-63a8-424d-97d9-99eaccdbb212","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"e908c11d-c4a3-4baa-b0af-144d0ae8ea8d","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13","sourceRef":"2025.8395d7b0-27d9-4711-9b1f-f74002d944fd"},{"outgoing":["2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3"],"incoming":["2027.a4c861c9-ca97-4d00-9865-400b152249d7"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":384,"y":315,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Postpone Task","declaredType":"intermediateThrowEvent","id":"496069dd-dbf8-4304-be27-7771294df42f"},{"targetRef":"496069dd-dbf8-4304-be27-7771294df42f","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"ff6d0baa-6872-44a4-a285-d4504548c56e","coachEventPath":"postponebutton"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"declaredType":"sequenceFlow","id":"2027.a4c861c9-ca97-4d00-9865-400b152249d7","sourceRef":"2025.8395d7b0-27d9-4711-9b1f-f74002d944fd"},{"targetRef":"2025.8395d7b0-27d9-4711-9b1f-f74002d944fd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"declaredType":"sequenceFlow","id":"2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3","sourceRef":"496069dd-dbf8-4304-be27-7771294df42f"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"8825ccf4-b0b4-42ae-8679-9b48528e7f2f","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"ce943c91-34c0-4166-a227-4ffbcb25ee35","processType":"None"}],"exposedAs":["NotExposed"],"isShadow":[true]},"implementation":"##unspecified","documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"1.b7869400-09ed-4f15-972f-39c44a324089","declaredType":"globalUserTask","id":"1.b7869400-09ed-4f15-972f-39c44a324089","ioSpecification":{"inputSet":[{"id":"_c2e7b421-7d30-4940-9394-ca8dfb2a4f16"}],"outputSet":[{"id":"_5b68dbf3-d6e0-4149-b61c-6dcc67a2eaf0"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"5af99189-777d-45c1-9870-11f6d451eb74"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fdf63a2a-e467-4937-8c92-9c5a7b0142a0</processItemId>
            <processId>1.b7869400-09ed-4f15-972f-39c44a324089</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.ac4e6659-c27a-4f8c-b0db-ca100ae571de</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2340c4cc899e0880:-54556756:19341292354:37c8</guid>
            <versionId>48af5f69-ff64-4567-b63f-03a5689832c2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.15d44a28-05d4-4afa-9950-49bb267bdf45</processItemId>
            <processId>1.b7869400-09ed-4f15-972f-39c44a324089</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6acf1a43-4b74-4ded-a849-1e3a811492ac</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2340c4cc899e0880:-54556756:19341292354:37c7</guid>
            <versionId>5964c528-fe13-4ab2-8111-ee12087f7ede</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6acf1a43-4b74-4ded-a849-1e3a811492ac</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>363d2ccc-da5e-42d2-bf69-8d53ae7dcedd</guid>
                <versionId>84479396-c9e7-4b1f-a51a-407fb9326857</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fdf63a2a-e467-4937-8c92-9c5a7b0142a0</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="5af99189-777d-45c1-9870-11f6d451eb74" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="1.b7869400-09ed-4f15-972f-39c44a324089" id="1.b7869400-09ed-4f15-972f-39c44a324089">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="ce943c91-34c0-4166-a227-4ffbcb25ee35">
                            <ns16:startEvent name="Start" id="c17ea5ee-5c4e-47fb-b152-544c562cb81f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af</ns16:outgoing>
                            </ns16:startEvent>
                            <ns3:formTask name="Coach" id="2025.8395d7b0-27d9-4711-9b1f-f74002d944fd">
                                <ns16:extensionElements>
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns13:nodeVisualInfo x="350" y="177" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af</ns16:incoming>
                                <ns16:incoming>2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3</ns16:incoming>
                                <ns16:outgoing>2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13</ns16:outgoing>
                                <ns16:outgoing>2027.a4c861c9-ca97-4d00-9865-400b152249d7</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                <ns19:id>96d730a6-7ac9-4386-a5f0-450bd325ae33</ns19:id>
                                                <ns19:layoutItemId>Inline_User_Task_Template</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>7577604f-5be8-438f-a9a4-749c681a8dd9</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Inline User Task Template</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d261750b-1787-4092-a923-eed5e3179f76</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:viewUUID>64.dc5fd75e-5b13-460c-8cb8-1e463c729751</ns19:viewUUID>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>4bedd238-ff1c-4809-9f2b-5fa93ecbb4d9</ns19:id>
                                                    <ns19:contentBoxId>TaskInput</ns19:contentBoxId>
                                                </ns19:contentBoxContrib>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>58bf45e0-a967-46fd-be72-c64df5fc83d0</ns19:id>
                                                    <ns19:contentBoxId>TaskOutput</ns19:contentBoxId>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:endEvent name="End" id="fb5551b1-63a8-424d-97d9-99eaccdbb212">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:sequenceFlow sourceRef="c17ea5ee-5c4e-47fb-b152-544c562cb81f" targetRef="2025.8395d7b0-27d9-4711-9b1f-f74002d944fd" name="To Coach" id="2027.24a7d0d3-41a3-40b9-bfac-52c1138e85af">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.8395d7b0-27d9-4711-9b1f-f74002d944fd" targetRef="fb5551b1-63a8-424d-97d9-99eaccdbb212" name="To End" id="2027.6bcc9c79-5bda-4c6f-a585-98c6c1f90e13">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="e908c11d-c4a3-4baa-b0af-144d0ae8ea8d">
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Postpone Task" id="496069dd-dbf8-4304-be27-7771294df42f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="384" y="315" width="24" height="24" color="#F8F8F8" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a4c861c9-ca97-4d00-9865-400b152249d7</ns16:incoming>
                                <ns16:outgoing>2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.8395d7b0-27d9-4711-9b1f-f74002d944fd" targetRef="496069dd-dbf8-4304-be27-7771294df42f" id="2027.a4c861c9-ca97-4d00-9865-400b152249d7">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="ff6d0baa-6872-44a4-a285-d4504548c56e">
                                        <ns3:coachEventPath>postponebutton</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="496069dd-dbf8-4304-be27-7771294df42f" targetRef="2025.8395d7b0-27d9-4711-9b1f-f74002d944fd" id="2027.f16c3817-d0e3-4e05-9bbb-8d4b48274fc3">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:htmlHeaderTag id="8825ccf4-b0b4-42ae-8679-9b48528e7f2f">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:isShadow>true</ns3:isShadow>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:inputSet id="_c2e7b421-7d30-4940-9394-ca8dfb2a4f16" />
                        <ns16:outputSet id="_5b68dbf3-d6e0-4149-b61c-6dcc67a2eaf0" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.59d96881-b0a4-479a-bed7-5033ff4cd688</processLinkId>
            <processId>1.b7869400-09ed-4f15-972f-39c44a324089</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fdf63a2a-e467-4937-8c92-9c5a7b0142a0</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.15d44a28-05d4-4afa-9950-49bb267bdf45</toProcessItemId>
            <guid>c8e46c26-19d9-4812-b6cb-758e489e33d5</guid>
            <versionId>386ff55d-e8db-4d12-b138-1f19541087de</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.fdf63a2a-e467-4937-8c92-9c5a7b0142a0</fromProcessItemId>
            <toProcessItemId>2025.15d44a28-05d4-4afa-9950-49bb267bdf45</toProcessItemId>
        </link>
    </process>
</teamworks>

