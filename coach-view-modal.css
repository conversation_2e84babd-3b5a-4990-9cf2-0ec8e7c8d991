/* Coach View Modal Styles */

.coach-view-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    width: 1000px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 0;
    overflow-y: auto;
    flex: 1;
}

.coach-view-info {
    padding: 25px;
}

.info-section {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 25px;
}

.info-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-section h3 {
    color: #333;
    font-size: 1.2em;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.info-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin-right: 10px;
    border-radius: 2px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-weight: 600;
    color: #555;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item span {
    color: #333;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    word-break: break-all;
}

.binding-info span {
    background: #e3f2fd;
    color: #1565c0;
    font-weight: 600;
    border-color: #bbdefb;
}

.config-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.config-tag {
    background: #f1f8e9;
    color: #388e3c;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    border: 1px solid #c8e6c9;
}

.code-container {
    background: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #333;
    margin-top: 10px;
}

.code-container pre {
    margin: 0;
    padding: 20px;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #d4d4d4;
    background: #1e1e1e;
}

.code-container code {
    background: none;
    padding: 0;
    border: none;
    color: inherit;
}

.script-block {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.script-block h4 {
    background: #f5f5f5;
    margin: 0;
    padding: 12px 16px;
    font-size: 1em;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
}

.script-block:last-child {
    margin-bottom: 0;
}

/* JavaScript Syntax Highlighting */
.js-keyword {
    color: #569cd6;
    font-weight: bold;
}

.js-string {
    color: #ce9178;
}

.js-comment {
    color: #6a9955;
    font-style: italic;
}

.js-number {
    color: #b5cea8;
}

.js-function {
    color: #dcdcaa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 95vw;
        max-height: 95vh;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-header h2 {
        font-size: 1.2em;
    }
    
    .coach-view-info {
        padding: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .config-list {
        flex-direction: column;
    }
    
    .code-container pre {
        padding: 15px;
        font-size: 12px;
    }
}

/* Scrollbar Styling */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.code-container pre::-webkit-scrollbar {
    height: 8px;
}

.code-container pre::-webkit-scrollbar-track {
    background: #2d2d30;
}

.code-container pre::-webkit-scrollbar-thumb {
    background: #464647;
    border-radius: 4px;
}

.code-container pre::-webkit-scrollbar-thumb:hover {
    background: #5a5a5c;
}
