<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <metric id="49.84290403-04af-4258-a028-700286b03e06" name="KPI">
        <lastModified>1569969508895</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <metricId>49.84290403-04af-4258-a028-700286b03e06</metricId>
        <rollupMetricRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/49.d5da2c80-b2af-40a6-981d-9de4df12ed12</rollupMetricRef>
        <rollupMultiplier>1.00000000</rollupMultiplier>
        <unit>4</unit>
        <description isNull="true" />
        <guid>guid:7903df9268eede95:-3c68ec8a:16d897292b6:-7fea</guid>
        <versionId>00358551-6701-4f1f-bdcf-afde9191a189</versionId>
        <xmlData>
            <itemTypeConfig itemType="2" validAssignmentTypes="47" validThresholdTypes="1" autoInclude="false" parametricAssignmentUnit="0">
                <defaultSettings assignmentType="2" useDefaultAssignments="false" useDefaultThresholds="false">
                    <threshold type="MinExpMaxThreshold" min="0" expected="100" max="1000" />
                </defaultSettings>
            </itemTypeConfig>
            <itemTypeConfig itemType="4" validAssignmentTypes="47" validThresholdTypes="1" autoInclude="false" parametricAssignmentUnit="0">
                <defaultSettings assignmentType="2" useDefaultAssignments="false" useDefaultThresholds="false">
                    <threshold type="MinExpMaxThreshold" min="0" expected="100" max="1000" />
                </defaultSettings>
            </itemTypeConfig>
        </xmlData>
    </metric>
</teamworks>

