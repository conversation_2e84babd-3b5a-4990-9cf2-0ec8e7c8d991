{"name": "twx-parser", "version": "0.0.0-development", "description": "Library for parsing and querying TWX files for IBM BPM", "main": "index.js", "scripts": {"test": "nyc mocha --opts ./mocha.opts \"test/**/*.test.js\"", "lint": "eslint .", "docs": "jsdoc2md src/**/*.js > api.md", "report-coverage": "codecov", "semantic-release": "semantic-release", "commit": "git-cz"}, "repository": {"type": "git", "url": "https://github.com/tigermarques/twx-parse.git"}, "keywords": ["IBM", "BPM", "TWX"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tigermarques/twx-parse/issues"}, "homepage": "https://github.com/tigermarques/twx-parse#readme", "files": ["src", "index.js", "api.md"], "devDependencies": {"all-contributors-cli": "^6.9.3", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-string": "^1.5.0", "chai-subset": "^1.6.0", "codecov": "^3.6.1", "commitizen": "^4.0.3", "cz-conventional-changelog": "^3.0.2", "eslint": "^6.6.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-chai-friendly": "^0.4.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-mocha": "^6.2.0", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^2.7.0", "jsdoc-to-markdown": "^5.0.2", "mocha": "^6.2.2", "nyc": "^14.1.1", "resnap": "^1.0.1", "semantic-release": "^15.13.28", "sinon": "^7.5.0", "sinon-chai": "^3.3.0", "validate-commit-msg": "^2.14.0"}, "dependencies": {"adm-zip": "^0.4.16", "bcrypt": "^3.0.6", "cli-progress": "^3.3.1", "sqlite3": "^4.1.0", "xml2js": "^0.4.23", "xml2js-xpath": "^0.10.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}