<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.76e93686-1290-441b-87b6-9e3624b6ce34" name="Create Folder Structure">
        <lastModified>1691929265836</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>995c3be5-00c5-4d46-b338-957317ffe0e8</guid>
        <versionId>9891c5eb-aed4-470e-8b57-637f29eb7d1f</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:6ad7eb4224455a46:11f23e39:189eef37169:-7550" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9"},{"incoming":["3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":710,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"883b68db-32fb-4b41-84b2-3b5414dc5b99"},{"targetRef":"ca48b7cf-faf2-4172-904e-0969ebb66c4d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Split The Path","declaredType":"sequenceFlow","id":"2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd","sourceRef":"00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"pathSeperated","isCollection":true,"declaredType":"dataObject","id":"2056.0228c216-f33c-4249-8bd7-6e3150c11455"},{"startQuantity":1,"outgoing":["c37a53db-fe07-40aa-a5b1-2d971e7fc0fe"],"incoming":["2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":86,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split The Path","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ca48b7cf-faf2-4172-904e-0969ebb66c4d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.pathSeperated = new tw.object.listOf.String();\r\ntw.local.pathSeperated = tw.local.fullPath.split(\"\/\");\r\ntw.local.itreartor = 0;\r\n"]}},{"targetRef":"b455a64f-cf27-4414-bdb4-0b6f54968f25","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Parent Path","declaredType":"sequenceFlow","id":"c37a53db-fe07-40aa-a5b1-2d971e7fc0fe","sourceRef":"ca48b7cf-faf2-4172-904e-0969ebb66c4d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"itreartor","isCollection":false,"declaredType":"dataObject","id":"2056.9ab44d94-7f65-4fc4-85b7-905379ec9fc1"},{"startQuantity":1,"outgoing":["7092b679-dcd4-4ec1-8347-79684a5a358f"],"incoming":["3726c3b9-72b8-41e0-a33f-268fe7c313d5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":336,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Create Folder FileNet","dataInputAssociation":[{"targetRef":"2055.5b1f380a-9134-4d96-a60a-cdb675b76b45","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.pathSeperated[tw.local.itreartor]"]}}]},{"targetRef":"2055.fc94a5c7-72ab-492e-a419-87a8f509c429","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}]},{"targetRef":"2055.d04abbef-eb9a-436a-8a7f-49652104b0e3","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8a8364dd-ad01-41e4-b4d2-231c016da252","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["2055.21901c1e-19de-4246-994d-52da5502c23f"]}],"calledElement":"1.45e2a149-1d4a-4946-ad59-f81f1424f100"},{"targetRef":"0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Increment Iterator ","declaredType":"sequenceFlow","id":"7092b679-dcd4-4ec1-8347-79684a5a358f","sourceRef":"8a8364dd-ad01-41e4-b4d2-231c016da252"},{"startQuantity":1,"outgoing":["62c7ffea-a529-42bf-9600-874ecde71ff0"],"incoming":["7092b679-dcd4-4ec1-8347-79684a5a358f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":462,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Increment Iterator ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.itreartor += 1;\r\n\r\n"]}},{"targetRef":"6b9b66d5-ec42-40b3-801e-f084aa585966","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Check Length Of List","declaredType":"sequenceFlow","id":"62c7ffea-a529-42bf-9600-874ecde71ff0","sourceRef":"0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d"},{"outgoing":["3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894","89fe5543-3d4c-441c-883c-c393fcc9c6c2"],"incoming":["62c7ffea-a529-42bf-9600-874ecde71ff0"],"default":"3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":587,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Check Length Of List","declaredType":"exclusiveGateway","id":"6b9b66d5-ec42-40b3-801e-f084aa585966"},{"targetRef":"883b68db-32fb-4b41-84b2-3b5414dc5b99","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894","sourceRef":"6b9b66d5-ec42-40b3-801e-f084aa585966"},{"targetRef":"b455a64f-cf27-4414-bdb4-0b6f54968f25","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.itreartor\t  &lt;\t  tw.local.pathSeperated.listLength"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Create Folder FileNet","declaredType":"sequenceFlow","id":"89fe5543-3d4c-441c-883c-c393fcc9c6c2","sourceRef":"6b9b66d5-ec42-40b3-801e-f084aa585966"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentFolderPath","isCollection":false,"declaredType":"dataObject","id":"2056.4deb78c7-3a7c-4dca-87a7-4c10c6480a3b"},{"startQuantity":1,"outgoing":["3726c3b9-72b8-41e0-a33f-268fe7c313d5"],"incoming":["c37a53db-fe07-40aa-a5b1-2d971e7fc0fe","89fe5543-3d4c-441c-883c-c393fcc9c6c2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":207,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Parent Path","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b455a64f-cf27-4414-bdb4-0b6f54968f25","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.itreartor == 0) {\r\n\ttw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;\r\n\r\n}else if (tw.local.itreartor == 1) {\r\n\ttw.local.parentFolderPath += \"\/\"+tw.local.pathSeperated[tw.local.itreartor-1]\r\n}\r\nelse{\r\n\ttw.local.parentFolderPath += \"\/\"+tw.local.pathSeperated[tw.local.itreartor-1] \r\n}"]}},{"targetRef":"8a8364dd-ad01-41e4-b4d2-231c016da252","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Parent Path","declaredType":"sequenceFlow","id":"3726c3b9-72b8-41e0-a33f-268fe7c313d5","sourceRef":"b455a64f-cf27-4414-bdb4-0b6f54968f25"},{"parallelMultiple":false,"outgoing":["4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ad49f92b-dbbf-481f-8903-6c7fa3b35062"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"04ad8f7d-eb65-4cb2-877f-ccd10a3f24cc","otherAttributes":{"eventImplId":"405a8f9a-cf49-410a-8c75-0dc6265d250a"}}],"attachedToRef":"8a8364dd-ad01-41e4-b4d2-231c016da252","extensionElements":{"nodeVisualInfo":[{"width":24,"x":371,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"9669a1b8-4eea-4ffd-81c8-e332796f46ec","outputSet":{}},{"parallelMultiple":false,"outgoing":["618df1d8-b6fb-4394-818a-fe2d1d474ed4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"d2ab59a3-161a-4a4f-8f87-f9967f6894cd"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5601d8bd-8f7b-4fe8-83c9-4538c699faf4","otherAttributes":{"eventImplId":"f1a79149-6441-46a6-869b-862634361fad"}}],"attachedToRef":"0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":497,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"988c4254-6de6-4f82-806f-ee2ae32292dd","outputSet":{}},{"parallelMultiple":false,"outgoing":["78bcb592-ccc9-4067-8a4f-58fce3f5c7ca"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a221a48e-092e-4675-8a55-f0820c496fde"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"90e8634d-f570-4414-8f08-9cf51d0935ee","otherAttributes":{"eventImplId":"6cab58dd-4cb2-487a-8bbb-ded7e3e543c9"}}],"attachedToRef":"b455a64f-cf27-4414-bdb4-0b6f54968f25","extensionElements":{"nodeVisualInfo":[{"width":24,"x":242,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"7ef9ebad-dd53-4280-84aa-7bae91c08670","outputSet":{}},{"parallelMultiple":false,"outgoing":["695fe35f-deac-4c51-866e-130387d3d0de"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"0bbdf76c-d1cd-4f2a-82f0-25511e1aa77a"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"17b64c99-3a4d-480a-8913-59a95237bd22","otherAttributes":{"eventImplId":"29ca4305-5dcb-404c-86c2-e5455d5b5dac"}}],"attachedToRef":"ca48b7cf-faf2-4172-904e-0969ebb66c4d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":121,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"1eacfb2a-9e8b-4dba-82f6-66e54f38bc46","outputSet":{}},{"incoming":["695fe35f-deac-4c51-866e-130387d3d0de","78bcb592-ccc9-4067-8a4f-58fce3f5c7ca","4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4","618df1d8-b6fb-4394-818a-fe2d1d474ed4"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"7a302b82-119f-43fb-8548-213d521c096d","otherAttributes":{"eventImplId":"afeaa411-e4b7-4a32-810e-439474f82422"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":338,"y":221,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Create Folder Structure -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Create Folder Structure -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"b1119a89-78ba-4b7a-8e84-ecae94594ce4"},{"targetRef":"b1119a89-78ba-4b7a-8e84-ecae94594ce4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"695fe35f-deac-4c51-866e-130387d3d0de","sourceRef":"1eacfb2a-9e8b-4dba-82f6-66e54f38bc46"},{"targetRef":"b1119a89-78ba-4b7a-8e84-ecae94594ce4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"78bcb592-ccc9-4067-8a4f-58fce3f5c7ca","sourceRef":"7ef9ebad-dd53-4280-84aa-7bae91c08670"},{"targetRef":"b1119a89-78ba-4b7a-8e84-ecae94594ce4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4","sourceRef":"9669a1b8-4eea-4ffd-81c8-e332796f46ec"},{"targetRef":"b1119a89-78ba-4b7a-8e84-ecae94594ce4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"618df1d8-b6fb-4394-818a-fe2d1d474ed4","sourceRef":"988c4254-6de6-4f82-806f-ee2ae32292dd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.78334161-6fbf-4e61-8fea-6f30dd3ad27a"}],"laneSet":[{"id":"16a3e4da-ed53-4e4b-a514-2463c171786e","lane":[{"flowNodeRef":["00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9","883b68db-32fb-4b41-84b2-3b5414dc5b99","ca48b7cf-faf2-4172-904e-0969ebb66c4d","8a8364dd-ad01-41e4-b4d2-231c016da252","0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d","6b9b66d5-ec42-40b3-801e-f084aa585966","b455a64f-cf27-4414-bdb4-0b6f54968f25","9669a1b8-4eea-4ffd-81c8-e332796f46ec","988c4254-6de6-4f82-806f-ee2ae32292dd","7ef9ebad-dd53-4280-84aa-7bae91c08670","1eacfb2a-9e8b-4dba-82f6-66e54f38bc46","b1119a89-78ba-4b7a-8e84-ecae94594ce4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"de32d9ce-fee6-47d9-afe6-4a63e6985a2c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create Folder Structure","declaredType":"process","id":"1.76e93686-1290-441b-87b6-9e3624b6ce34","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.66e460ea-ed37-4b3f-be42-b56dd24f5205"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.4aab2644-61c0-458f-8b2a-d4e07480c29c"}],"inputSet":[{"dataInputRefs":["2055.879cf3c6-dd65-419d-a83f-0676faf89583"]}],"outputSet":[{"dataOutputRefs":["2055.66e460ea-ed37-4b3f-be42-b56dd24f5205","2055.4aab2644-61c0-458f-8b2a-d4e07480c29c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"02366014\/DC Inward\/test\/test\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fullPath","isCollection":false,"id":"2055.879cf3c6-dd65-419d-a83f-0676faf89583"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.879cf3c6-dd65-419d-a83f-0676faf89583</processParameterId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"02366014/DC Inward/test/test"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>65bc062b-d7dd-45ec-b2e1-6481eff02e3a</guid>
            <versionId>1ade3922-398c-4cdc-9871-6e9d8c53decb</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.66e460ea-ed37-4b3f-be42-b56dd24f5205</processParameterId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>80be3b16-6fb1-4f95-9913-4236c51d4a00</guid>
            <versionId>2c3a85d0-8fb2-4c44-8e94-964abd5c1bf5</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6de6fb6b-a00c-4259-bf6b-0aa297a4f708</processParameterId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>95dec4c8-5812-4b2e-a36a-5aeb79c0ccc0</guid>
            <versionId>48369bf6-e8c1-4fbc-8b0d-3475eb0ead3b</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4aab2644-61c0-458f-8b2a-d4e07480c29c</processParameterId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>45832d49-b744-4986-abf7-fa8d528646c6</guid>
            <versionId>ac7b2c8b-1756-4cc7-96ae-d0465c75c756</versionId>
        </processParameter>
        <processVariable name="pathSeperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0228c216-f33c-4249-8bd7-6e3150c11455</processVariableId>
            <description isNull="true" />
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88bbaff8-3b8a-4ea9-8043-1c143e5c22a7</guid>
            <versionId>9115ea21-0761-48d9-b29c-4f70b1145ea1</versionId>
        </processVariable>
        <processVariable name="itreartor">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9ab44d94-7f65-4fc4-85b7-905379ec9fc1</processVariableId>
            <description isNull="true" />
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2e28d5fa-bd36-4a13-b765-06a2cd5627cd</guid>
            <versionId>55e55aab-dce7-4239-be63-6a59a2452946</versionId>
        </processVariable>
        <processVariable name="parentFolderPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4deb78c7-3a7c-4dca-87a7-4c10c6480a3b</processVariableId>
            <description isNull="true" />
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb0b50f3-d431-4099-a9f2-a143a7b38086</guid>
            <versionId>2192ded9-780e-4333-9b86-8ad00fec1cff</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.78334161-6fbf-4e61-8fea-6f30dd3ad27a</processVariableId>
            <description isNull="true" />
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>19bc1aa9-eb64-4b08-8d5d-5f9c012b9daf</guid>
            <versionId>0e9fbad8-227a-4dea-9c00-b11a7f4c35de</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>Split The Path</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.1c4ed94f-946d-4e6b-a4a1-8577107f33f1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61ab</guid>
            <versionId>20735779-1ead-4c45-bdf3-8d8c44399aa0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c0cb3b85-815f-4484-b813-c6880b644bfa</processItemPrePostId>
                <processItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>45d5c3e8-c68e-47ff-ab25-aeb3585d185b</guid>
                <versionId>c8cfb653-c2d0-4430-ac11-b7cf63edd076</versionId>
            </processPrePosts>
            <layoutData x="86" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:2246</errorHandlerItem>
                <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.1c4ed94f-946d-4e6b-a4a1-8577107f33f1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.pathSeperated = new tw.object.listOf.String();&#xD;
tw.local.pathSeperated = tw.local.fullPath.split("/");&#xD;
tw.local.itreartor = 0;&#xD;
</script>
                <isRule>false</isRule>
                <guid>87daeae6-c8a9-4173-91fd-5263c868bbf2</guid>
                <versionId>0540794d-588c-4df4-8ec0-ef7f9f97842d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a8364dd-ad01-41e4-b4d2-231c016da252</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>Create Folder FileNet</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61ac</guid>
            <versionId>4f2a9f75-1264-4444-b147-c4a8324e52c9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="336" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:2246</errorHandlerItem>
                <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</subProcessId>
                <attachedProcessRef>/1.45e2a149-1d4a-4946-ad59-f81f1424f100</attachedProcessRef>
                <guid>29ff40f7-954a-46b9-8ed0-9c609d7a3959</guid>
                <versionId>930dec7a-e47a-4839-be6e-796f00d190ce</versionId>
                <parameterMapping name="folderName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1117b722-b189-4e9b-bae2-358f5c9d84e9</parameterMappingId>
                    <processParameterId>2055.5b1f380a-9134-4d96-a60a-cdb675b76b45</processParameterId>
                    <parameterMappingParentId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.pathSeperated[tw.local.itreartor]</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>158b49e9-e260-404e-963d-3e9c518377dd</guid>
                    <versionId>40c0febd-7187-494b-ba6f-1a1e2a582c83</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="folderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7820aa8a-d7a3-4ccc-8372-d121cae6988a</parameterMappingId>
                    <processParameterId>2055.21901c1e-19de-4246-994d-52da5502c23f</processParameterId>
                    <parameterMappingParentId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.folderID</value>
                    <classRef>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>adf38ad1-94bf-4fd5-a4fd-241cb4673a1c</guid>
                    <versionId>848ae828-1913-47f3-8af1-76af1968e8b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentFolderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ebb6cd7d-f866-40ab-8795-369ab3b5d435</parameterMappingId>
                    <processParameterId>2055.fc94a5c7-72ab-492e-a419-87a8f509c429</processParameterId>
                    <parameterMappingParentId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.folderID</value>
                    <classRef>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>07a40183-c953-4ce6-b708-7ee444da88e3</guid>
                    <versionId>aceaffb1-866d-4339-b44c-0f815d1412c2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentFolderPath">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d70f7ed1-38ae-4b9f-ac67-19d5ae0da485</parameterMappingId>
                    <processParameterId>2055.d04abbef-eb9a-436a-8a7f-49652104b0e3</processParameterId>
                    <parameterMappingParentId>3012.4f61034d-2590-4d56-8a23-1edd53d5b817</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentFolderPath</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c3be8295-7eeb-4074-bfb0-32130fc410ae</guid>
                    <versionId>bb3d8931-a483-43ac-af9c-ca505c94ce1e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>Check Length Of List</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.19a7c34e-9ba4-43c9-989c-ae04ca6ba103</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61a8</guid>
            <versionId>8e17e64a-c720-4928-8afb-4c0db95f390f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="587" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.19a7c34e-9ba4-43c9-989c-ae04ca6ba103</switchId>
                <guid>962c744a-18e6-4d14-b275-d9cad1687f2d</guid>
                <versionId>b666b5e6-9c46-4208-9ab8-800b4154b2cb</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.d244a0b7-d6e5-47c1-9797-2f270a7f3059</switchConditionId>
                    <switchId>3013.19a7c34e-9ba4-43c9-989c-ae04ca6ba103</switchId>
                    <seq>1</seq>
                    <endStateId>guid:6ad7eb4224455a46:11f23e39:189eef37169:-7551</endStateId>
                    <condition>tw.local.itreartor	  &lt;	  tw.local.pathSeperated.listLength</condition>
                    <guid>36532ee4-abc5-48f4-b239-bc1ddb9c0b37</guid>
                    <versionId>be6149a6-d9c6-41cb-b56a-379deb54a1ca</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.f5641fa0-b721-4dda-a8de-dd88f7b1af8c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:2246</guid>
            <versionId>8eef3a39-20db-43d0-94c0-6df57b340c89</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.15ac3a01-0f11-42ba-a453-c0c4fa627549</processItemPrePostId>
                <processItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Create Folder Structure -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Create Folder Structure -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>514baec0-82ea-4efa-933b-8eac387248cf</guid>
                <versionId>4d5644ca-01dd-4ea8-b3d2-9a477253cc56</versionId>
            </processPrePosts>
            <layoutData x="338" y="221">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.f5641fa0-b721-4dda-a8de-dd88f7b1af8c</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>0e2dfecb-2c41-4869-8d37-d8e8f7f671b3</guid>
                <versionId>9f517ebd-93df-4682-b884-1c53d771c5ef</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cee41722-eb7a-4a6f-a34b-0b224945b455</parameterMappingId>
                    <processParameterId>2055.6de6fb6b-a00c-4259-bf6b-0aa297a4f708</processParameterId>
                    <parameterMappingParentId>3007.f5641fa0-b721-4dda-a8de-dd88f7b1af8c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>23bbbf63-f3e4-4d43-8a32-bf41cf18466d</guid>
                    <versionId>2f959a1d-a066-4091-96ec-457bf8c971d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>Set Parent Path</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.1170671b-ba55-4e1a-93f9-865e9ff7b299</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61ad</guid>
            <versionId>c464496b-f2d4-45c8-b629-b476078b4b73</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="207" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:2246</errorHandlerItem>
                <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.1170671b-ba55-4e1a-93f9-865e9ff7b299</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.itreartor == 0) {&#xD;
	tw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;&#xD;
&#xD;
}else if (tw.local.itreartor == 1) {&#xD;
	tw.local.parentFolderPath += "/"+tw.local.pathSeperated[tw.local.itreartor-1]&#xD;
}&#xD;
else{&#xD;
	tw.local.parentFolderPath += "/"+tw.local.pathSeperated[tw.local.itreartor-1] &#xD;
}</script>
                <isRule>false</isRule>
                <guid>0c482129-1bf7-4d20-bd2b-7646bc3189a7</guid>
                <versionId>3bd88f53-8af9-4f48-903d-fbd624d1d1f0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.883b68db-32fb-4b41-84b2-3b5414dc5b99</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.7fb0cf80-d110-4948-bf50-0457b1fec436</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa</guid>
            <versionId>d5ea17f4-9895-4216-ae73-90efccda4066</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="710" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.7fb0cf80-d110-4948-bf50-0457b1fec436</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b2170483-d824-45d2-8019-6de65647dcde</guid>
                <versionId>1623ca21-8659-4398-942c-a7249c2548a7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</processItemId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <name>Increment Iterator </name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.1891c9ab-dd63-41a3-b5c6-156d0c683814</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61a9</guid>
            <versionId>e80a63b5-aff4-49fc-b80f-7b08ac590baa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="462" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:2246</errorHandlerItem>
                <errorHandlerItemId>2025.b1119a89-78ba-4b7a-8e84-ecae94594ce4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.1891c9ab-dd63-41a3-b5c6-156d0c683814</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.itreartor += 1;&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>c6da3a15-cd42-434d-bf46-4dd520c011fc</guid>
                <versionId>8da09a1c-58ec-4764-844c-b66004343b57</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create Folder Structure" id="1.76e93686-1290-441b-87b6-9e3624b6ce34" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="fullPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.879cf3c6-dd65-419d-a83f-0676faf89583">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"02366014/DC Inward/test/test"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.66e460ea-ed37-4b3f-be42-b56dd24f5205" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.4aab2644-61c0-458f-8b2a-d4e07480c29c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.879cf3c6-dd65-419d-a83f-0676faf89583</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.66e460ea-ed37-4b3f-be42-b56dd24f5205</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.4aab2644-61c0-458f-8b2a-d4e07480c29c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="16a3e4da-ed53-4e4b-a514-2463c171786e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="de32d9ce-fee6-47d9-afe6-4a63e6985a2c" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>883b68db-32fb-4b41-84b2-3b5414dc5b99</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ca48b7cf-faf2-4172-904e-0969ebb66c4d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8a8364dd-ad01-41e4-b4d2-231c016da252</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6b9b66d5-ec42-40b3-801e-f084aa585966</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b455a64f-cf27-4414-bdb4-0b6f54968f25</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9669a1b8-4eea-4ffd-81c8-e332796f46ec</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>988c4254-6de6-4f82-806f-ee2ae32292dd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7ef9ebad-dd53-4280-84aa-7bae91c08670</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1eacfb2a-9e8b-4dba-82f6-66e54f38bc46</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b1119a89-78ba-4b7a-8e84-ecae94594ce4</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="883b68db-32fb-4b41-84b2-3b5414dc5b99">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="710" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="00c7ac72-4d99-41b1-a0cd-e1a818a4dbb9" targetRef="ca48b7cf-faf2-4172-904e-0969ebb66c4d" name="To Split The Path" id="2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="pathSeperated" id="2056.0228c216-f33c-4249-8bd7-6e3150c11455" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Split The Path" id="ca48b7cf-faf2-4172-904e-0969ebb66c4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="86" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.69445284-00d6-4c2e-a9ca-3b0aae00b3fd</ns16:incoming>
                        
                        
                        <ns16:outgoing>c37a53db-fe07-40aa-a5b1-2d971e7fc0fe</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.pathSeperated = new tw.object.listOf.String();&#xD;
tw.local.pathSeperated = tw.local.fullPath.split("/");&#xD;
tw.local.itreartor = 0;&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ca48b7cf-faf2-4172-904e-0969ebb66c4d" targetRef="b455a64f-cf27-4414-bdb4-0b6f54968f25" name="To Set Parent Path" id="c37a53db-fe07-40aa-a5b1-2d971e7fc0fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="itreartor" id="2056.9ab44d94-7f65-4fc4-85b7-905379ec9fc1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false" />
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:callActivity calledElement="1.45e2a149-1d4a-4946-ad59-f81f1424f100" name="Create Folder FileNet" id="8a8364dd-ad01-41e4-b4d2-231c016da252">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="336" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3726c3b9-72b8-41e0-a33f-268fe7c313d5</ns16:incoming>
                        
                        
                        <ns16:outgoing>7092b679-dcd4-4ec1-8347-79684a5a358f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5b1f380a-9134-4d96-a60a-cdb675b76b45</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.pathSeperated[tw.local.itreartor]</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.fc94a5c7-72ab-492e-a419-87a8f509c429</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d04abbef-eb9a-436a-8a7f-49652104b0e3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentFolderPath</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21901c1e-19de-4246-994d-52da5502c23f</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8a8364dd-ad01-41e4-b4d2-231c016da252" targetRef="0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d" name="To Increment Iterator " id="7092b679-dcd4-4ec1-8347-79684a5a358f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Increment Iterator " id="0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="462" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7092b679-dcd4-4ec1-8347-79684a5a358f</ns16:incoming>
                        
                        
                        <ns16:outgoing>62c7ffea-a529-42bf-9600-874ecde71ff0</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.itreartor += 1;&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d" targetRef="6b9b66d5-ec42-40b3-801e-f084aa585966" name="To Check Length Of List" id="62c7ffea-a529-42bf-9600-874ecde71ff0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894" name="Check Length Of List" id="6b9b66d5-ec42-40b3-801e-f084aa585966">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="587" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>62c7ffea-a529-42bf-9600-874ecde71ff0</ns16:incoming>
                        
                        
                        <ns16:outgoing>3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894</ns16:outgoing>
                        
                        
                        <ns16:outgoing>89fe5543-3d4c-441c-883c-c393fcc9c6c2</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="6b9b66d5-ec42-40b3-801e-f084aa585966" targetRef="883b68db-32fb-4b41-84b2-3b5414dc5b99" name="To End" id="3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6b9b66d5-ec42-40b3-801e-f084aa585966" targetRef="b455a64f-cf27-4414-bdb4-0b6f54968f25" name="To Create Folder FileNet" id="89fe5543-3d4c-441c-883c-c393fcc9c6c2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.itreartor	  &lt;	  tw.local.pathSeperated.listLength</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentFolderPath" id="2056.4deb78c7-3a7c-4dca-87a7-4c10c6480a3b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Parent Path" id="b455a64f-cf27-4414-bdb4-0b6f54968f25">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="207" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c37a53db-fe07-40aa-a5b1-2d971e7fc0fe</ns16:incoming>
                        
                        
                        <ns16:incoming>89fe5543-3d4c-441c-883c-c393fcc9c6c2</ns16:incoming>
                        
                        
                        <ns16:outgoing>3726c3b9-72b8-41e0-a33f-268fe7c313d5</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.itreartor == 0) {&#xD;
	tw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;&#xD;
&#xD;
}else if (tw.local.itreartor == 1) {&#xD;
	tw.local.parentFolderPath += "/"+tw.local.pathSeperated[tw.local.itreartor-1]&#xD;
}&#xD;
else{&#xD;
	tw.local.parentFolderPath += "/"+tw.local.pathSeperated[tw.local.itreartor-1] &#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b455a64f-cf27-4414-bdb4-0b6f54968f25" targetRef="8a8364dd-ad01-41e4-b4d2-231c016da252" name="To Set Parent Path" id="3726c3b9-72b8-41e0-a33f-268fe7c313d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8a8364dd-ad01-41e4-b4d2-231c016da252" parallelMultiple="false" name="Error" id="9669a1b8-4eea-4ffd-81c8-e332796f46ec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="371" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ad49f92b-dbbf-481f-8903-6c7fa3b35062" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="04ad8f7d-eb65-4cb2-877f-ccd10a3f24cc" eventImplId="405a8f9a-cf49-410a-8c75-0dc6265d250a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d" parallelMultiple="false" name="Error1" id="988c4254-6de6-4f82-806f-ee2ae32292dd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="497" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>618df1d8-b6fb-4394-818a-fe2d1d474ed4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="d2ab59a3-161a-4a4f-8f87-f9967f6894cd" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5601d8bd-8f7b-4fe8-83c9-4538c699faf4" eventImplId="f1a79149-6441-46a6-869b-862634361fad">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b455a64f-cf27-4414-bdb4-0b6f54968f25" parallelMultiple="false" name="Error2" id="7ef9ebad-dd53-4280-84aa-7bae91c08670">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="242" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>78bcb592-ccc9-4067-8a4f-58fce3f5c7ca</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a221a48e-092e-4675-8a55-f0820c496fde" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="90e8634d-f570-4414-8f08-9cf51d0935ee" eventImplId="6cab58dd-4cb2-487a-8bbb-ded7e3e543c9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ca48b7cf-faf2-4172-904e-0969ebb66c4d" parallelMultiple="false" name="Error3" id="1eacfb2a-9e8b-4dba-82f6-66e54f38bc46">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="121" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>695fe35f-deac-4c51-866e-130387d3d0de</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="0bbdf76c-d1cd-4f2a-82f0-25511e1aa77a" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="17b64c99-3a4d-480a-8913-59a95237bd22" eventImplId="29ca4305-5dcb-404c-86c2-e5455d5b5dac">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="b1119a89-78ba-4b7a-8e84-ecae94594ce4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="338" y="221" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Create Folder Structure -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Create Folder Structure -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>695fe35f-deac-4c51-866e-130387d3d0de</ns16:incoming>
                        
                        
                        <ns16:incoming>78bcb592-ccc9-4067-8a4f-58fce3f5c7ca</ns16:incoming>
                        
                        
                        <ns16:incoming>4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4</ns16:incoming>
                        
                        
                        <ns16:incoming>618df1d8-b6fb-4394-818a-fe2d1d474ed4</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="7a302b82-119f-43fb-8548-213d521c096d" eventImplId="afeaa411-e4b7-4a32-810e-439474f82422">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1eacfb2a-9e8b-4dba-82f6-66e54f38bc46" targetRef="b1119a89-78ba-4b7a-8e84-ecae94594ce4" name="To End Event" id="695fe35f-deac-4c51-866e-130387d3d0de">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ef9ebad-dd53-4280-84aa-7bae91c08670" targetRef="b1119a89-78ba-4b7a-8e84-ecae94594ce4" name="To End Event" id="78bcb592-ccc9-4067-8a4f-58fce3f5c7ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9669a1b8-4eea-4ffd-81c8-e332796f46ec" targetRef="b1119a89-78ba-4b7a-8e84-ecae94594ce4" name="To End Event" id="4997ecc0-d7ff-42fb-8085-1e1d1d43d0b4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="988c4254-6de6-4f82-806f-ee2ae32292dd" targetRef="b1119a89-78ba-4b7a-8e84-ecae94594ce4" name="To End Event" id="618df1d8-b6fb-4394-818a-fe2d1d474ed4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.78334161-6fbf-4e61-8fea-6f30dd3ad27a" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3b7ca9b6-e9e9-4e20-b2ef-e2c11babd894</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.883b68db-32fb-4b41-84b2-3b5414dc5b99</toProcessItemId>
            <guid>860b8208-47ae-416e-bfd2-6d55b812fe46</guid>
            <versionId>0ee87a41-70ba-4b62-8917-d49d4476cbe0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</fromProcessItemId>
            <toProcessItemId>2025.883b68db-32fb-4b41-84b2-3b5414dc5b99</toProcessItemId>
        </link>
        <link name="To Set Parent Path">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c37a53db-fe07-40aa-a5b1-2d971e7fc0fe</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</toProcessItemId>
            <guid>c851f1b3-2358-4ad4-9763-43bb4a1ca952</guid>
            <versionId>41989864-f3de-4fbc-a010-27be9a330e33</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ca48b7cf-faf2-4172-904e-0969ebb66c4d</fromProcessItemId>
            <toProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</toProcessItemId>
        </link>
        <link name="To Increment Iterator ">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7092b679-dcd4-4ec1-8347-79684a5a358f</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8a8364dd-ad01-41e4-b4d2-231c016da252</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b</endStateId>
            <toProcessItemId>2025.0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</toProcessItemId>
            <guid>b2d53e36-0503-4a4c-8dc0-26bba77f1740</guid>
            <versionId>5025bf24-cf12-4ec0-b839-cc01a45c7be1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8a8364dd-ad01-41e4-b4d2-231c016da252</fromProcessItemId>
            <toProcessItemId>2025.0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</toProcessItemId>
        </link>
        <link name="To Check Length Of List">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.62c7ffea-a529-42bf-9600-874ecde71ff0</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</toProcessItemId>
            <guid>e5462dd5-13de-489a-8f76-1c60594520f2</guid>
            <versionId>8ab9fb78-4025-4ed0-90f0-95c3d248baf1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0b9e1ec6-0e7e-4292-b2fe-3fae4da6598d</fromProcessItemId>
            <toProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</toProcessItemId>
        </link>
        <link name="To Create Folder FileNet">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.89fe5543-3d4c-441c-883c-c393fcc9c6c2</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</fromProcessItemId>
            <endStateId>guid:6ad7eb4224455a46:11f23e39:189eef37169:-7551</endStateId>
            <toProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</toProcessItemId>
            <guid>46ee83f7-4fa6-44bd-9e26-5e80226146f4</guid>
            <versionId>a6e3fc34-c44f-4f3e-8f5a-14bad194b5cf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.6b9b66d5-ec42-40b3-801e-f084aa585966</fromProcessItemId>
            <toProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</toProcessItemId>
        </link>
        <link name="To Set Parent Path">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3726c3b9-72b8-41e0-a33f-268fe7c313d5</processLinkId>
            <processId>1.76e93686-1290-441b-87b6-9e3624b6ce34</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8a8364dd-ad01-41e4-b4d2-231c016da252</toProcessItemId>
            <guid>d12356b3-2f98-48c6-b660-61eb2fb41abd</guid>
            <versionId>e485a8de-45cd-4eaf-90da-cae16b7dff8a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b455a64f-cf27-4414-bdb4-0b6f54968f25</fromProcessItemId>
            <toProcessItemId>2025.8a8364dd-ad01-41e4-b4d2-231c016da252</toProcessItemId>
        </link>
    </process>
</teamworks>

