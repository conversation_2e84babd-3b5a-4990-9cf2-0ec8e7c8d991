<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8804be93-df98-44b0-bf01-1f35970dfee0" name="get booked facilities">
        <lastModified>1692621816414</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.8804be93-df98-44b0-bf01-1f35970dfee0</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3a9b</guid>
        <versionId>14d6ae4e-b614-4869-aed1-77097c4f9d22</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3add" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.778235d5-616c-4c43-8601-94a135439624"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f83f8b15-d702-43c5-855e-3b5405f366af"},{"incoming":["7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3a9d"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3e3c097d-080e-4e67-8a72-e7383715a12a"},{"targetRef":"7ccd4a2a-034e-416d-89ec-0dc1216f7cfa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.778235d5-616c-4c43-8601-94a135439624","sourceRef":"f83f8b15-d702-43c5-855e-3b5405f366af"},{"startQuantity":1,"outgoing":["7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff"],"incoming":["2027.778235d5-616c-4c43-8601-94a135439624"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":115,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7ccd4a2a-034e-416d-89ec-0dc1216f7cfa","scriptFormat":"text\/x-javascript"},{"targetRef":"3e3c097d-080e-4e67-8a72-e7383715a12a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff","sourceRef":"7ccd4a2a-034e-416d-89ec-0dc1216f7cfa"}],"laneSet":[{"id":"a58acb33-cb84-4496-885d-c8ac2099dda6","lane":[{"flowNodeRef":["f83f8b15-d702-43c5-855e-3b5405f366af","3e3c097d-080e-4e67-8a72-e7383715a12a","7ccd4a2a-034e-416d-89ec-0dc1216f7cfa"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"52546295-807b-49e5-8f0b-82dacd97a1e5","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"get booked facilities","declaredType":"process","id":"1.8804be93-df98-44b0-bf01-1f35970dfee0","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"inputSet":[{}],"outputSet":[{}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3e3c097d-080e-4e67-8a72-e7383715a12a</processItemId>
            <processId>1.8804be93-df98-44b0-bf01-1f35970dfee0</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c4c4715b-cc4b-4d34-828a-1054e184f463</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3a9d</guid>
            <versionId>70727b9d-cf00-44f5-9fd0-dc19a85b2aba</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c4c4715b-cc4b-4d34-828a-1054e184f463</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0def59d8-6ea9-4297-973e-9f04b93e459d</guid>
                <versionId>582022f8-93a0-4538-98f8-5da7bde332ab</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</processItemId>
            <processId>1.8804be93-df98-44b0-bf01-1f35970dfee0</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7a8a65d8-c235-4ecf-a0f5-b4135a078eb9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3adc</guid>
            <versionId>f733d07d-73f2-4509-9cfa-b2c249f7d7a6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="115" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7a8a65d8-c235-4ecf-a0f5-b4135a078eb9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>false</isActive>
                <script isNull="true" />
                <isRule>false</isRule>
                <guid>1b966f0c-49d7-4940-9c9f-cefb8f42f39f</guid>
                <versionId>8d266b0e-dfac-40d2-9ee3-6819145e5199</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="get booked facilities" id="1.8804be93-df98-44b0-bf01-1f35970dfee0" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="a58acb33-cb84-4496-885d-c8ac2099dda6">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="52546295-807b-49e5-8f0b-82dacd97a1e5" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f83f8b15-d702-43c5-855e-3b5405f366af</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3e3c097d-080e-4e67-8a72-e7383715a12a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f83f8b15-d702-43c5-855e-3b5405f366af">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.778235d5-616c-4c43-8601-94a135439624</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3e3c097d-080e-4e67-8a72-e7383715a12a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:3a9d</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f83f8b15-d702-43c5-855e-3b5405f366af" targetRef="7ccd4a2a-034e-416d-89ec-0dc1216f7cfa" name="To Script Task" id="2027.778235d5-616c-4c43-8601-94a135439624">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="7ccd4a2a-034e-416d-89ec-0dc1216f7cfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="115" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.778235d5-616c-4c43-8601-94a135439624</ns16:incoming>
                        
                        
                        <ns16:outgoing>7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff</ns16:outgoing>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ccd4a2a-034e-416d-89ec-0dc1216f7cfa" targetRef="3e3c097d-080e-4e67-8a72-e7383715a12a" name="To End" id="7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7107e7f3-ee28-4d7c-8356-9cb8d2dde1ff</processLinkId>
            <processId>1.8804be93-df98-44b0-bf01-1f35970dfee0</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3e3c097d-080e-4e67-8a72-e7383715a12a</toProcessItemId>
            <guid>e2de18da-7dfc-4e8e-a408-0eda5304cf17</guid>
            <versionId>2e3fc365-eeb8-4bba-b191-1bfa2ae5bda8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7ccd4a2a-034e-416d-89ec-0dc1216f7cfa</fromProcessItemId>
            <toProcessItemId>2025.3e3c097d-080e-4e67-8a72-e7383715a12a</toProcessItemId>
        </link>
    </process>
</teamworks>

