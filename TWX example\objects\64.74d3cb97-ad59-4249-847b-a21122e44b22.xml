<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.74d3cb97-ad59-4249-847b-a21122e44b22" name="Financial Details  Branch">
        <lastModified>1702138095314</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;e78517cb-19ee-46bc-84bb-4c55b41a52f2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3dff4eba-f4b6-42fa-8ce1-aecba64e75fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Financial Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bdcf316e-6226-4af3-864a-bbf397bcd7a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;418de7e0-0587-43a7-8884-7759fc7fa422&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b81c23b-96cb-4174-8710-589429c71090&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a85e7100-45d9-439c-8685-1ad67aff9773&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15ab8fca-ae0d-49a6-8224-f31341840fbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@overflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d049a60-7c80-4e68-8ef4-1a9e1d201ede&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.visabilityController();
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;0edd1086-f233-401d-8f18-5be7f391261d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;1bbb3699-021b-4d78-847a-8b5b61f111af&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5f5a878d-2470-4cd1-8faf-ff89f26af8d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;846368a3-0dc7-4bd0-89c5-36e04180336b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bea4775c-7917-48e0-8a0d-509b138c8bc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;e041d4b0-a57c-4409-8e93-c137c6ce8f7a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d9a5c383-ffed-43c1-8040-b80cd3558c19&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;bad4e6d4-d652-4a60-871f-5997e356cb24&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DocumentAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4092100-5a99-42fb-8a5e-5569f619c90d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DocumentAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a73bcaf6-115a-4c80-8ac1-d33937138e19&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87ad9134-2541-4f2b-88dc-e8140201c51f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;134b390b-7739-4210-8bfd-4b9fb000cad9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;currency&lt;/ns2:optionName&gt;&lt;ns2:value&gt;NONE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a934df65-95d2-4f19-89d0-d4aa32e3399d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d80cd6d-cd43-4bac-8e4c-431d4a040dd2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.calculateAmountPayablebyNBE();
if(isNaN(Number(me.getData()))){
	me.setValid(false , "must be digits");
	me.setData("");
	return false;
}else
{
	if(me.getData().length &amp;lt;14){
	me.setValid(false , "must be 14 digits");
	return false;
}else{
	me.setValid(true);
	return true;
}
//	me.setValid(true);
//	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;374791b5-9797-49ef-8ef8-743c9403b57a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;numericFormatting&lt;/ns2:optionName&gt;&lt;ns2:value&gt;custom&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.documentAmount&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2c316638-6fc0-4c9d-8468-88efd4d8871e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ChargesCommissionsAccount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a222555-e0ab-44fc-8381-5e12efbce759&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ChargesCommissionsAccount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2bbfeecf-9d9a-4cb3-8cfc-3a35626fbaa6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ed037e0-3a94-44cc-886e-447811b67a26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b852d3a-32c2-421f-8a08-01461c768fe9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;regExp&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;26c0c8f8-1245-4182-8f24-b27727929475&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 19)
{
	me.setValid(false , "max lenght is 19 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dfe4bcad-e80c-4584-8582-31e4e96ddd89&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(isNaN(Number(me.getData()))){
	me.setValid(false , "must be digits");
	me.setData("");
	return false;
}else
{
	if(me.getData().length &amp;lt;19){
	me.setValid(false , "must be 19 digits");
	return false;
}else{
	me.setValid(true);
	return true;
}
//	me.setValid(true);
//	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;30d0160d-89a9-42b5-8fb4-288d75a0d007&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1093609e-dbd0-49e7-8f97-3aa9f45ad760&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7387971a-c6e2-432b-8c96-a53309bd79c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;408b38cb-dd06-4f28-8469-b4530f28c6cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2e1dd39-0233-4aae-8e98-78bcd449a651&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.accountsList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.chargesAccount&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;486cedcd-cbc5-40cc-8e00-3e7b76e8c006&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;PaymentAccount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d7711cb-aa82-43e2-81db-c5ec7217a752&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.PaymentAccount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;26f49e4a-ea28-497d-8881-9d88e6669809&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb87c24a-ff16-49a5-8807-53196e8a1253&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d7c15680-49e3-4a1a-898e-bc0f0f8497bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;28d294ed-1a08-41ae-8685-3c086c3ec281&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 19)
{
	me.setValid(false , "max lenght is 19 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee94e8dc-d0cb-44b7-8dea-df59a3d36805&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ff9e579-1792-4c1f-81a2-0fd67cbb3225&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7eaebde1-a1ff-4c4e-89eb-4dcd01e79f33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0dcd026b-5501-46b2-8d38-6bab4d2b5b2a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;afde1c2f-f420-4494-8ff9-c3c859abda24&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.accountsList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.paymentAccount&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ee3b10b5-715e-4917-8aaa-d5ab935e7b51&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;SelectExistingAdvancePayments&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2079fdf3-2e60-4b3a-8d47-6ec68bbe8f91&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.SelectExistingAdvancePayments&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58e2157c-cebd-483b-8e87-97b1e99626d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f35a2aa8-9441-4d1d-8acc-32bb6b565f8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e157fb1f-9db0-43ff-8385-1ade41fd779f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3124c218-00b5-4248-8dda-56b80889decb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if (!me.getData()){
	${Advance_Payments_Used1}.setVisible(false,true);
}else{
	${Advance_Payments_Used1}.setVisible(true,true);
}
view.haveAmountAdvanced();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;237b3d94-70fa-47ab-8a78-58534ce2348c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if (!me.getData()){
	${Advance_Payments_Used1}.setVisible(false,true);
	view.resetAdvancePayment();
}else{
	${Advance_Payments_Used1}.setVisible(true,true);
}
view.haveAmountAdvanced();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.isAdvancePaymentsUsed&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;3a8de593-1002-4bf3-80bd-58342e4a581d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ff92aa2-7d7e-4439-8732-692bb7fd32db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9d645ae3-97d7-46aa-8e9f-a7285d529ca8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Currency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d91f1235-b109-4c6a-820d-01544f74fc78&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1dbe6721-7d24-445f-825a-fa65e4ab4d97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef7164b3-76a4-40fa-802f-691361fda00e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2c35dc0-d3a2-422e-8b7b-1b54fde5d1ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5bf07f8-28d6-4fb9-8096-67bbb3eebd17&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93226238-15db-4828-84b4-582dc8574ad5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;117044e3-cd97-4ed7-8a0b-d90d36594143&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5096e0b5-dc44-42e7-81a9-51ba3d99c360&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23bd3712-003f-4b5e-848d-a70e849e6040&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetAdvancePayment();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;408ab503-da8a-4382-80ce-44758e3927e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.currencyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb9b5238-7057-4234-805f-0c77ca0caa14&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.documentCurrency&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a9a0c81b-32c8-44e0-884f-f526f04b6b95&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;SourceofForeignCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0aab9fe5-fd8c-400e-8396-4fe96b19d12a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.SourceofForeignCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ccfb7d48-bdb8-4e34-83f2-06c5706be7bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db053371-7f3d-4fa8-8c02-4e90e4c458ef&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15cf3b72-d492-4dc2-8dbc-4161c3883d36&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4bb5fc2-d9cc-4257-8b09-a48c649a39ac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35840874-6106-4a60-8456-77e1664cbd46&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;501fad24-d519-4dc6-8f2d-8ad4e4aa69bd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;14098cf0-e71e-4d2c-8993-c3d5fc39f805&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Source_of_Foreign_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc5fc12c-4bc6-4e19-8b9b-3ef895f6784b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.sourceOfForeignCurrency&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;69b6edb6-9155-4db7-87a7-9f9ea9b76c1e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;SourceofFunds&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c7dd724-dc1d-42f3-8c60-5281d5660dca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.SourceofFunds&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;25a961f9-f11c-4603-89db-f81af51e31bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;280f0809-d40b-4612-84e6-d1110d7a975b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8adb930-1905-4911-8047-ed6348e2a023&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe50814d-aa51-420a-8f70-2a5617d07585&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d04b0eb-696c-4ac7-8ed0-de75db00e7f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;65ec05e0-8548-4677-8b44-9f9713cbf17b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a26a131a-67ee-49c6-8a1d-475f4eade286&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Source_of_Funds&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b33acf16-2c23-477a-8389-9559724396db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.sourceOfFunds&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;570ec6db-ab9c-4445-8671-b935a18be8d8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1fd077b0-d202-48d8-818e-26f84132ae45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c3e60c7a-297a-476a-8bcc-df025044b625&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Advance_Payments_Used1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e2e631b2-2fdb-4d3a-8a61-e26e057fffa1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Advance Payments Used&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ce8cf87-01a4-4a70-8472-1f32e7dc1684&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a50be9cd-ed59-415f-83c1-16770a80c128&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c11cbaf9-5e7a-4173-897c-344c28cce79f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@overflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f351b1e9-9459-4f33-857f-75849bf47d55&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;REQUIRED&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23868ee5-2ceb-44f7-868d-feb3bc72ef8c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tmpUsedAdvancePayment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.tmpUsedAdvancePayment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7991bc4b-3e3b-4334-8434-1d87c40d4afc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;advancePaymentsUsedVisability&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d038fec-630d-4603-82c3-5db9c7ba386c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;TotalAllocatedAmountinRequestCurrency&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.financialDetails.amountAdvanced&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a2da3c2-1739-4bd3-8a59-424e76bd1397&lt;/ns2:id&gt;&lt;ns2:optionName&gt;requestCurrency&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.financialDetails.documentCurrency.code&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e2c345d6-937d-415a-876e-b10d2f78b896&lt;/ns2:id&gt;&lt;ns2:optionName&gt;addBtn&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68cfe772-4f77-40aa-865d-9fc378170de2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;isChecker&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.isChecker&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c08dbf56-72af-426d-8b7e-d392b26a5f77&lt;/ns2:id&gt;&lt;ns2:optionName&gt;vis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.vis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c98dfc06-f29c-45f1-8bd9-b78143176e94&lt;/ns2:id&gt;&lt;ns2:optionName&gt;currencyVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.currencyVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;04db8e76-8d9e-471d-8319-9934491fa3af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;currncy&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.currncy&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;78350103-0bf5-4246-8f58-72f9c7dc0986&lt;/ns2:id&gt;&lt;ns2:optionName&gt;requestID&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.requestID&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;02a01913-a267-41be-8339-d039067a441a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;CIF&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.CIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;832c9571-4b72-453a-8662-8d7c22f8a63f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;alertMessage&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.alertMessage&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;04cee444-104c-4d72-81d0-3b8a53c0e524&lt;/ns2:id&gt;&lt;ns2:optionName&gt;errorVis&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.errorVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ab2ea11b-7c5e-4835-9ed4-18708eee21be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.usedAdvancePayment[]&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;ec035d0e-335f-49f3-8f6d-e840ee733044&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Service_Call1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;28ea7e73-28fb-47dc-8691-a84dd34f7dba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;get accounts&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a3d8f5f-a0ac-4053-80af-6d34c84c0610&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8cbd7248-35e3-4ee2-8436-4a3a37e9114c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4083c29f-202a-40e2-81a9-7e6269bf3c30&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.753ea3e8-d234-409e-9fb2-015400e4c9fb&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56cd7202-6b51-4990-84cd-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.CIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4718f369-0d0a-4825-877f-901afb2bc362&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c4b9a52-c24c-4b5b-83f6-c93b7455d14b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;autoRun&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;27db16c5-1009-47f4-811c-0bade678902e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;80e484bc-6390-4eb3-8eef-2f5b109a9c7b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.accountsList[]&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>7d4dbc31-14c5-4bc2-833d-1085476bdc07</guid>
        <versionId>a9997453-f712-494b-9341-469590d7acc2</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="financialDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.2ef26f01-2fa0-4bb5-96de-f6c59fcb4d29</coachViewBindingTypeId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <classId>/12.47b1b163-7266-46de-9171-529c0b65a377</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>0e61c9a0-f12d-48d2-882b-4616f681a098</guid>
            <versionId>e5480290-e78a-4672-a6ba-cd530cef7734</versionId>
        </bindingType>
        <configOption name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7a60aec1-fccb-4c91-ba1d-eee74b0ba016</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>e7246526-312c-41df-93b6-f1ec07ec8a13</guid>
            <versionId>89082008-70f4-4b17-898a-a8dcd33535e6</versionId>
        </configOption>
        <configOption name="docAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a5aa18ca-b1ed-42b1-90c0-af802d803b8b</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>44a4da38-74ab-4bd9-9a98-40ec1ab159cd</guid>
            <versionId>e567bba7-f8ff-4dc6-a927-4bc17bf0f369</versionId>
        </configOption>
        <configOption name="currncy">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.38ab3006-5feb-4df7-9d0a-53587cf87709</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>d19615a9-c1da-49bb-b7d7-6ef1e001c674</guid>
            <versionId>d6f7caab-43f0-4322-a861-8c3ae64dd3c7</versionId>
        </configOption>
        <configOption name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.aba1be26-ad20-4cd6-8404-0f88ef0663ec</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>8f2caea3-1639-4ae9-9074-b219d448ccec</guid>
            <versionId>4ff61a89-4b16-47b8-8a8e-336695fa9ee0</versionId>
        </configOption>
        <configOption name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.2482ddfb-f899-4be5-8b5a-7171c0939c63</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>b8758c67-774d-48b4-ba46-cb877fc8a644</guid>
            <versionId>df941773-ae52-4ca1-8a84-b5dfc218f6f4</versionId>
        </configOption>
        <configOption name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d1fb84cb-ac82-473c-89eb-4e2c772ebabc</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>ba7fa351-de7f-4b56-9cd3-6c5d0dc30b2d</guid>
            <versionId>5e81685a-a1f1-4e96-808e-73da540a1490</versionId>
        </configOption>
        <configOption name="haveAmountAdvanced">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e1827e35-9d8e-4ce6-9723-03b7b6b4a348</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>58c0fa94-10af-45c6-a2c1-1d6caeabf35d</guid>
            <versionId>d6fe705a-be59-4dfe-9994-563b1de1cb75</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.86d99af7-55e8-4046-80e3-284cbe1dfa06</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>014b61e9-2689-45fe-bc2d-60499a880d3a</guid>
            <versionId>776152f8-7dca-4137-83a4-8599cc12bbfd</versionId>
        </configOption>
        <configOption name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.25a7de02-7a2f-42cf-adb6-3224d0ba9067</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>01cfb397-1ca0-4dad-aa09-1c4f901361e4</guid>
            <versionId>7e07af20-5a4e-41a1-ac14-c83934ce2311</versionId>
        </configOption>
        <configOption name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a26e7b7d-5638-46c5-959e-94336a66545e</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>36cdf72e-7713-4e3f-aa09-c50484aa4571</guid>
            <versionId>79bea367-1122-4c2e-8833-5d832d403368</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.da764d06-ee7c-491d-9791-1b050aa77a08</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>3d5f4c79-f17e-45f7-9918-3c681fa8e46b</guid>
            <versionId>48ebeaf1-fcaf-40d1-8652-ac540a128f1a</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d19344b4-d9fe-45ff-b379-612aa86d6810</coachViewConfigOptionId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>dc84078d-78cf-4afb-b646-b1db2c40c1e4</guid>
            <versionId>6cb469fc-83f8-4458-ad20-c647ec7b9375</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.9f7818a8-5d04-4bc8-a71a-89e7e7441451</coachViewInlineScriptId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.visabilityController = function  () {&#xD;
	var type = this.context.options.requestType.get("value");&#xD;
&#xD;
	if (type == "ICAP" || type == "IDC Execution" || type == "IDC Completion" || type == "IDC Amendment") {&#xD;
&#xD;
		this.ui.get("SelectExistingAdvancePayments").setVisible(true,true);&#xD;
	}else{&#xD;
&#xD;
		this.ui.get("SelectExistingAdvancePayments").setVisible(false,true);&#xD;
	}&#xD;
&#xD;
	if(this.context.options.docAmount.get("value") == false){&#xD;
		&#xD;
		this.ui.get("DocumentAmount").setEnabled(false);&#xD;
	}else{&#xD;
		this.ui.get("DocumentAmount").setEnabled(true);&#xD;
	}&#xD;
	if(this.context.options.currncy.get("value") == false){&#xD;
		this.ui.get("Currency").setEnabled(false);&#xD;
		this.context.options.currencyVis.set("value", "READONLY");&#xD;
	}else{&#xD;
		this.ui.get("Currency").setEnabled(true);&#xD;
		this.context.options.currencyVis.set("value", "DEFAULT");&#xD;
	}&#xD;
}&#xD;
this.calculateAmountPayablebyNBE = function  () {&#xD;
	var payable = this.context.binding.get("value").get("documentAmount") - this.context.binding.get("value").get("amtPaidbyOtherBanks") -  this.context.binding.get("value").get("discountAmt") - this.context.binding.get("value").get("amountAdvanced");&#xD;
	this.context.binding.get("value").set("amtPayableByNBE", payable);&#xD;
	if (this.context.options.requestType == "ICAP") {&#xD;
		if(payable &gt; 0){&#xD;
			document.getElementById("text-input-Financial_Details_Trade_FO1:AmountPayablebyNBE").style.backgroundColor = "#eb8724";&#xD;
			this.ui.get("AmountPayablebyNBE").setValid(false,"the amount is more than 0 and request type is ICAP");&#xD;
		}else{&#xD;
			document.getElementById("text-input-Financial_Details_Trade_FO1:AmountPayablebyNBE").style.backgroundColor = "#ffffff";&#xD;
			this.ui.get("AmountPayablebyNBE").setValid(true);&#xD;
		}&#xD;
	}else if(payable &lt; 0){&#xD;
		this.ui.get("AmountPayablebyNBE").setValid(false,"this field is madatory");&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
this.resetAdvancePayment = function  () {&#xD;
	this.context.options.tmpUsedAdvancePayment.set("value", {});&#xD;
	this.context.options.tmpUsedAdvancePayment.get("value").set("invoiceCurrency", {});&#xD;
	this.context.binding.get("value").set("usedAdvancePayment", []);&#xD;
//	this.context.binding.get("value").get("usedAdvancePayment").set("invoiceCurrency", {});&#xD;
}&#xD;
this.haveAmountAdvanced = function  () {&#xD;
	if (this.context.binding.get("value").get("isAdvancePaymentsUsed") == true) {&#xD;
		this.context.options.haveAmountAdvanced.set("value", "READONLY");&#xD;
	}else{&#xD;
		this.context.options.haveAmountAdvanced.set("value", "DEFAULT");&#xD;
	}&#xD;
}&#xD;
&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>25fa4ac8-83fd-4a53-82c0-74622c56cb91</guid>
            <versionId>24affabe-6e96-453b-8a52-4f6a4923fe95</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.12011027-b47b-45ba-a1f6-a4d96cc41b98</coachViewLocalResId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>aa15f0fc-4fdf-4736-947b-d93ea8d9b860</guid>
            <versionId>dd390dff-c055-4a8e-8337-cd8b633c0ef3</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.61909075-0fe0-410e-81c0-34fc1542a190</coachViewLocalResId>
            <coachViewId>64.74d3cb97-ad59-4249-847b-a21122e44b22</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>0edcd502-548a-44be-9184-e3b358a1dbc8</guid>
            <versionId>5b69d1f8-9a2e-470d-b63d-0a8a6468689a</versionId>
        </localization>
    </coachView>
</teamworks>

