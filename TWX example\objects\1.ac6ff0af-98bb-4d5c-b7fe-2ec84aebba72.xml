<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72" name="Client-Side Human Service_1">
        <lastModified>1692530871625</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.eb3ab9e8-eb92-49f7-ae50-9c8029719df2</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:ef0d56db9ddf1554:-58d13970:189abb703e2:-5478</guid>
        <versionId>70293360-6bc8-4040-a332-b55749401b80</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":107,"y":158,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"64fcb0fc-08f7-4682-a9dd-ea87d15da6ab"},{"outgoing":["2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e","2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e","2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e"],"incoming":["2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98","2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d","2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":384,"y":190,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":[]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Limits_Tracking1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1177a4e2-0a60-48dc-8bbf-f0619a18ab77","optionName":"@label","value":"Limits Tracking"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f0c9bc82-eb08-4103-8045-2877aeae34cb","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"74df85f4-c56d-4c3e-836c-bc054a24f478","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ba138133-1741-43c5-849b-b1694c2a17de","optionName":"liabAndCashCoverVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f33631a9-fade-4bbe-8d5c-00e93778b561","optionName":"facilitiesCodesList","value":"tw.local.facilityCodes[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e7ae2e32-3fe6-4721-83a0-4ad7a59b8453","optionName":"facility","value":"tw.local.facility"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b066966a-07ad-43af-8159-9f0d93ea2b4a","optionName":"SectionVis","value":"tw.local.SectionVis"}],"viewUUID":"64.9e7e0016-3899-48ff-9db4-bea34bea40f0","binding":"tw.local.idccontract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"eb31b363-1f91-4b38-853f-65c37563f4ff","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"e4ff07f8-778f-4933-8d21-9f1cf770793b"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e393f40f-17dc-4142-8de1-7fd39df7652f","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a4436c48-ac4a-452b-8376-1cf643c6e339","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5fd3fede-5ab2-4e8d-8635-6d9846859a6f","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"136e264a-0f67-498b-8150-895a553e9dd6","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"44855e5d-045e-4f3f-aba1-e783cf827f39","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"cdc50894-fab7-430d-8a5f-7d2599f627ac"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach","isForCompensation":false,"completionQuantity":1,"id":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed"},{"incoming":["2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":554,"y":213,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"3bd24fe3-db47-4a22-b948-6d99a9a1fda0"},{"targetRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98","sourceRef":"64fcb0fc-08f7-4682-a9dd-ea87d15da6ab"},{"targetRef":"3bd24fe3-db47-4a22-b948-6d99a9a1fda0","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"d1702074-d672-416e-ac7c-11bea596c6ca","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e","sourceRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"22\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"ICAP\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = {};\nautoObject.customerInformation.facilityType.id = 0;\nautoObject.customerInformation.facilityType.code = \"\";\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\nautoObject.customerInformation.facilityType.englishdescription = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new Date();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idc","isCollection":false,"declaredType":"dataObject","id":"2056.613ec2af-5269-473e-80b8-f107cb295c53"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.AllocatedAmountinRequestCurrency = 0.0;\nautoObject.invoiceCurrency = {};\nautoObject.invoiceCurrency.id = 0;\nautoObject.invoiceCurrency.code = \"\";\nautoObject.invoiceCurrency.arabicdescription = \"\";\nautoObject.invoiceCurrency.englishdescription = \"\";\nautoObject.AmountAllocated = 0.0;\nautoObject.advancePaymentRequestNumber = \"\";\nautoObject.outstandingAmount = 0.0;\nautoObject.invoiceNumber = \"12345\\n67890\\n12345\\n67890\\n12345\\n67890\";\nautoObject.paidAmount = 0.0;\nautoObject.beneficiaryName = \"\";\nautoObject.documentAmount = 0.0;\nautoObject"}]},"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"temp","isCollection":false,"declaredType":"dataObject","id":"2056.f2ecb1a8-1260-48e1-8884-9350c30d6533"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"READONLY\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"vis","isCollection":false,"declaredType":"dataObject","id":"2056.139d5bf1-4593-462a-8061-508cad33f3f1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.9dd94e7d-dd24-400a-8915-b7d3ef27c336"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"docAmount","isCollection":false,"declaredType":"dataObject","id":"2056.8c3290c1-7bc6-47cd-8d40-56f5e9f2be3e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"currncy","isCollection":false,"declaredType":"dataObject","id":"2056.373c43ec-703c-4ccf-8f60-d3f0db274a28"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"testidc","isCollection":false,"declaredType":"dataObject","id":"2056.04cfb59c-fd5f-42eb-8d39-b669a01fd920"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"facilityCodes","isCollection":true,"declaredType":"dataObject","id":"2056.94cc0125-1174-4cef-8754-934521c17c2f"},{"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"applicantAccounteeFacilities","isCollection":true,"declaredType":"dataObject","id":"2056.751fcb92-ce15-44cd-8a99-3243288ef8f8"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idccontract","isCollection":false,"declaredType":"dataObject","id":"2056.********-e916-4ef3-8acb-09a2d7a98f28"},{"itemSubjectRef":"itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08","name":"facility","isCollection":false,"declaredType":"dataObject","id":"2056.b40a9944-99e4-4910-8d7d-3ac1255adde3"},{"startQuantity":1,"outgoing":["2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5"],"incoming":["2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e"],"default":"2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5","extensionElements":{"nodeVisualInfo":[{"width":95,"x":383,"y":312,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set facility","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.04e69484-ba53-4228-86eb-0d501f0dc767","scriptFormat":"text\/x-javascript","script":{"content":["var repeated = false;\r\nfor(var index = 0 ; index&lt;tw.local.customerFacilities.length; index++){\r\n\t\r\n\t\tif(!!tw.local.facility.facilityCode){\r\n\t\t\r\n\t\t\tif(tw.local.facility.facilityCode.value == tw.local.customerFacilities[index].facilityCode){\r\n\t\t\t\t\r\n\t\t\t\tfor (var i=0; i&lt;tw.local.idccontract.facilities.length; i++) {\r\n\t\t\t\t\tif(tw.local.facility.facilityCode.value == tw.local.idccontract.facilities[i].facilityCode){\r\n\t\t\t\t\t\ttw.system.coachValidation.addValidationError(\"tw.local.facility.facilityCode.value\",\"You Can't add a facility already added\");\r\n\t\t\t\t\t\ttw.local.errorMessage += \"You Can't add a facility already added\";\r\n\t\t\t\t\t\trepeated = true;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\tif (repeated == false)\r\n\t\t\t\t\t\ttw.local.idccontract.facilities[tw.local.idccontract.facilities.length] =  tw.local.customerFacilities[index];\r\n\t\t\t}\r\n\t\t}\r\n}"]}},{"targetRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5","sourceRef":"2025.04e69484-ba53-4228-86eb-0d501f0dc767"},{"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"customerFacilities","isCollection":true,"declaredType":"dataObject","id":"2056.9cab2ac1-9b42-42d3-8844-3b3f57cef4a9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.65f977a5-08bd-4ee0-8eef-2b673b37ebda"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"NONE\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"SectionVis","isCollection":false,"declaredType":"dataObject","id":"2056.103f9365-0f25-4119-8935-eb833fc5d078"},{"outgoing":["2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d"],"incoming":["2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e"],"extensionElements":{"mode":["InvokeService"],"postAssignmentScript":["if (tw.local.customerFacilities.length&gt;0) {\r\n\ttw.local.SectionVis = \"DEFAULT\";\r\n}\r\nelse{\r\n\ttw.local.SectionVis = \"NONE\";\r\n}\r\n"],"nodeVisualInfo":[{"width":95,"x":436,"y":40,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d","name":"Get Applicant and Accountee Facility Codes","dataInputAssociation":[{"targetRef":"2055.805a4cbb-3faf-424f-8d2e-0300020c7082","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.cifs"]}}]},{"targetRef":"2055.506476be-4213-48fc-9956-612812bf0ca8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.partyType"]}}]},{"targetRef":"2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.productCode"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.d05653fc-8f37-40dd-8653-9d32680e9c42","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.facilityCodes"]}}],"sourceRef":["2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","declaredType":"TFormalExpression","content":["tw.local.customerFacilities"]}}],"sourceRef":["2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c"]}],"calledElement":"1.e3028b61-b01a-4a56-a086-244083a8d445"},{"targetRef":"2025.04e69484-ba53-4228-86eb-0d501f0dc767","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"0be70765-64ec-491f-8901-40ecdd04cb41","coachEventPath":"Limits_Tracking1\/Credit_Facility_Information1\/addFacility"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script 1","declaredType":"sequenceFlow","id":"2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e","sourceRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed"},{"targetRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d","sourceRef":"2025.d05653fc-8f37-40dd-8653-9d32680e9c42"},{"targetRef":"2025.d05653fc-8f37-40dd-8653-9d32680e9c42","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"a33e7cd4-0d84-4ba3-8bc0-47ea4ab04239","coachEventPath":"Limits_Tracking1\/GetFacility"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Applicant and Accountee Facility Codes","declaredType":"sequenceFlow","id":"2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e","sourceRef":"2025.039c03f1-059b-41f8-a623-142ad6b3d1ed"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cifs","isCollection":true,"declaredType":"dataObject","id":"2056.589f1cdc-b7dd-48f9-8cc8-6c27ada46dcd"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"partyType","isCollection":true,"declaredType":"dataObject","id":"2056.430a5a24-289c-4f8c-8c93-4f8ed424c0a0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"declaredType":"dataObject","id":"2056.77c9961e-3bd9-4730-81d8-f8fbd3749bb2"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"744ec9d7-c0db-44d4-9ca3-57e1dc492114","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"4b433eed-2a2b-472f-b1e7-1ae32471dfd5","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Client-Side Human Service_1","declaredType":"globalUserTask","id":"1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72","ioSpecification":{"inputSet":[{"id":"_d7cd800c-7b5e-4cae-84cc-e90861a429a6"}],"outputSet":[{"id":"_1e7a194d-4ad8-4041-9cae-0fa01e20bc80"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"23d6c1b4-0cc1-4b40-b0c8-f482700dd9ed"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processVariable name="idc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.613ec2af-5269-473e-80b8-f107cb295c53</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fee0370b-321f-4d59-a7ae-0532e04eeeb1</guid>
            <versionId>6bea9f46-4e89-4724-84ed-239145cc872d</versionId>
        </processVariable>
        <processVariable name="temp">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f2ecb1a8-1260-48e1-8884-9350c30d6533</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>827c6f9f-0777-446b-967a-6a3a560dc482</guid>
            <versionId>5ee3de80-e8aa-41c1-a301-36f28d7d6fac</versionId>
        </processVariable>
        <processVariable name="vis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.139d5bf1-4593-462a-8061-508cad33f3f1</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>87cdf81c-ecab-40ff-98ce-109ed9ddb477</guid>
            <versionId>fdd3862c-2c6f-4c15-bf3b-4c399c455815</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9dd94e7d-dd24-400a-8915-b7d3ef27c336</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5dd107fd-d3bc-4042-929b-2f3b4619651e</guid>
            <versionId>091b397c-4d1e-4f1d-a2f5-9a934e2fd8a4</versionId>
        </processVariable>
        <processVariable name="docAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8c3290c1-7bc6-47cd-8d40-56f5e9f2be3e</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>397b0ba5-e131-4215-a6d0-de1fc82bc75a</guid>
            <versionId>e8915a01-e477-4934-8b67-3b55a2af2539</versionId>
        </processVariable>
        <processVariable name="currncy">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.373c43ec-703c-4ccf-8f60-d3f0db274a28</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9c299c30-88f8-46a3-a701-f9a166564335</guid>
            <versionId>aba48d97-1f67-4a3c-a32e-fc522663612d</versionId>
        </processVariable>
        <processVariable name="testidc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.04cfb59c-fd5f-42eb-8d39-b669a01fd920</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc249301-b3c9-407c-b129-5d264dd96d92</guid>
            <versionId>b4f511d7-b8d1-4573-97dc-7e157b917d28</versionId>
        </processVariable>
        <processVariable name="facilityCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.94cc0125-1174-4cef-8754-934521c17c2f</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8e9d178e-65f0-409a-9411-d1aef601d42e</guid>
            <versionId>7c3748d5-0c06-485d-a27b-b732218dd2e2</versionId>
        </processVariable>
        <processVariable name="applicantAccounteeFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.751fcb92-ce15-44cd-8a99-3243288ef8f8</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7f55ddc2-7f30-41ef-8cb8-7a9d9a6daa65</guid>
            <versionId>2d41993e-af2b-4690-b971-2cc14e1aaeec</versionId>
        </processVariable>
        <processVariable name="idccontract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-e916-4ef3-8acb-09a2d7a98f28</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4ff75d79-d9c2-41a5-aa69-0878c55b131d</guid>
            <versionId>4e5da7bc-25af-4147-ad3a-4a9fa75dcbea</versionId>
        </processVariable>
        <processVariable name="facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b40a9944-99e4-4910-8d7d-3ac1255adde3</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.b7087c1b-7f18-4032-b88b-ffe584eafd08</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f99df431-07d7-4959-87f0-7423f6dfeb2c</guid>
            <versionId>6f4ce819-0038-44c6-af56-8818fb396ba0</versionId>
        </processVariable>
        <processVariable name="customerFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9cab2ac1-9b42-42d3-8844-3b3f57cef4a9</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>34bb3877-b1c3-4546-8a3d-3f3e2b5379aa</guid>
            <versionId>8227accc-a779-4a8a-a5c4-bc90b66ddef3</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.65f977a5-08bd-4ee0-8eef-2b673b37ebda</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>91690ba9-9912-469a-9082-9f3941b7c9e0</guid>
            <versionId>0c2240ff-c66d-4a77-b7e3-4b3e999d7369</versionId>
        </processVariable>
        <processVariable name="SectionVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.103f9365-0f25-4119-8935-eb833fc5d078</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4a2e6c04-1d65-46f0-add5-b133abbf002a</guid>
            <versionId>5a4b8f83-1527-4494-a387-5e0886300e4e</versionId>
        </processVariable>
        <processVariable name="cifs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.589f1cdc-b7dd-48f9-8cc8-6c27ada46dcd</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1624a06c-fa5b-4e20-802a-6e49ee26e79e</guid>
            <versionId>ce65905d-ad6c-48cd-a1d1-a0213188702e</versionId>
        </processVariable>
        <processVariable name="partyType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.430a5a24-289c-4f8c-8c93-4f8ed424c0a0</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>811fdaf2-b29e-412c-861e-cf1841585a21</guid>
            <versionId>a87f6bf7-f6d2-496d-aef4-715c45e4500e</versionId>
        </processVariable>
        <processVariable name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.77c9961e-3bd9-4730-81d8-f8fbd3749bb2</processVariableId>
            <description isNull="true" />
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>27e93979-1a70-40bf-ad31-666cc14e980c</guid>
            <versionId>f0ffa810-ab5c-4b60-a6b5-7269d96d773c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d05653fc-8f37-40dd-8653-9d32680e9c42</processItemId>
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <name>Get Applicant and Accountee Facility Codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.51e0e7ec-241c-4c06-841f-cae4b7c459a4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:25fd907f15501a6a:510e674:189f97206cf:-7d5c</guid>
            <versionId>0abbeddc-af55-47a4-902c-dd4b0646c80c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.51e0e7ec-241c-4c06-841f-cae4b7c459a4</subProcessId>
                <attachedProcessRef>/1.e3028b61-b01a-4a56-a086-244083a8d445</attachedProcessRef>
                <guid>91679bc4-bc1e-4d59-996d-3daf04facee4</guid>
                <versionId>e9425146-6bd5-4f45-bfd6-f116ff04c8b7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.eb3ab9e8-eb92-49f7-ae50-9c8029719df2</processItemId>
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.a5f773a7-**************-7134054e73cf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ef0d56db9ddf1554:-58d13970:189abb703e2:-5476</guid>
            <versionId>1a7cd207-631b-47f8-91da-ddad4be60b96</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.418b6b22-7584-41e1-a517-92502584bb22</processItemId>
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2cea54c2-ac39-4f3a-b44b-315e3603870b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ef0d56db9ddf1554:-58d13970:189abb703e2:-5477</guid>
            <versionId>33f76477-3f7e-405b-9158-8ad370dcd332</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2cea54c2-ac39-4f3a-b44b-315e3603870b</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a120270c-09a5-4773-abe7-ca44bfa40537</guid>
                <versionId>898f1e95-0fa3-4e74-8d54-6f9a3906d6a2</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.eb3ab9e8-eb92-49f7-ae50-9c8029719df2</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="23d6c1b4-0cc1-4b40-b0c8-f482700dd9ed" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="Client-Side Human Service_1" id="1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="4b433eed-2a2b-472f-b1e7-1ae32471dfd5">
                            
                            
                            <ns16:startEvent name="Start" id="64fcb0fc-08f7-4682-a9dd-ea87d15da6ab">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="107" y="158" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns3:formTask name="Coach" id="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                    
                                    <ns13:nodeVisualInfo x="384" y="190" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>136e264a-0f67-498b-8150-895a553e9dd6</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e393f40f-17dc-4142-8de1-7fd39df7652f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a4436c48-ac4a-452b-8376-1cf643c6e339</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5fd3fede-5ab2-4e8d-8635-6d9846859a6f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>e4ff07f8-778f-4933-8d21-9f1cf770793b</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>eb31b363-1f91-4b38-853f-65c37563f4ff</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Limits_Tracking1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>1177a4e2-0a60-48dc-8bbf-f0619a18ab77</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Limits Tracking</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f0c9bc82-eb08-4103-8045-2877aeae34cb</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>74df85f4-c56d-4c3e-836c-bc054a24f478</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ba138133-1741-43c5-849b-b1694c2a17de</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>liabAndCashCoverVis</ns19:optionName>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f33631a9-fade-4bbe-8d5c-00e93778b561</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>facilitiesCodesList</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.facilityCodes[]</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>e7ae2e32-3fe6-4721-83a0-4ad7a59b8453</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>facility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.facility</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b066966a-07ad-43af-8159-9f0d93ea2b4a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>SectionVis</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>tw.local.SectionVis</ns19:value>
                                                            
                                                            
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding>tw.local.idccontract</ns19:binding>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                
                                                
                                                <ns19:id>cdc50894-fab7-430d-8a5f-7d2599f627ac</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>44855e5d-045e-4f3f-aba1-e783cf827f39</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>OK</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:endEvent name="End" id="3bd24fe3-db47-4a22-b948-6d99a9a1fda0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="554" y="213" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="64fcb0fc-08f7-4682-a9dd-ea87d15da6ab" targetRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" name="To Client-Side Script" id="2027.ce4b2a82-c5cc-4b9e-a6d4-d18e6f366c98">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" targetRef="3bd24fe3-db47-4a22-b948-6d99a9a1fda0" name="To End" id="2027.58846ce3-30c8-4a29-ad6b-b8fbb3e3878e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="d1702074-d672-416e-ac7c-11bea596c6ca">
                                        
                                        
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" name="idc" id="2056.613ec2af-5269-473e-80b8-f107cb295c53">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "22";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "ICAP";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = {};
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new Date();
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="temp" id="2056.f2ecb1a8-1260-48e1-8884-9350c30d6533">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.AllocatedAmountinRequestCurrency = 0.0;
autoObject.invoiceCurrency = {};
autoObject.invoiceCurrency.id = 0;
autoObject.invoiceCurrency.code = "";
autoObject.invoiceCurrency.arabicdescription = "";
autoObject.invoiceCurrency.englishdescription = "";
autoObject.AmountAllocated = 0.0;
autoObject.advancePaymentRequestNumber = "";
autoObject.outstandingAmount = 0.0;
autoObject.invoiceNumber = "12345\n67890\n12345\n67890\n12345\n67890";
autoObject.paidAmount = 0.0;
autoObject.beneficiaryName = "";
autoObject.documentAmount = 0.0;
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="vis" id="2056.139d5bf1-4593-462a-8061-508cad33f3f1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"READONLY"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.9dd94e7d-dd24-400a-8915-b7d3ef27c336">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="docAmount" id="2056.8c3290c1-7bc6-47cd-8d40-56f5e9f2be3e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="currncy" id="2056.373c43ec-703c-4ccf-8f60-d3f0db274a28">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">true</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="testidc" id="2056.04cfb59c-fd5f-42eb-8d39-b669a01fd920" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="facilityCodes" id="2056.94cc0125-1174-4cef-8754-934521c17c2f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" name="applicantAccounteeFacilities" id="2056.751fcb92-ce15-44cd-8a99-3243288ef8f8" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" name="idccontract" id="2056.********-e916-4ef3-8acb-09a2d7a98f28" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08" isCollection="false" name="facility" id="2056.b40a9944-99e4-4910-8d7d-3ac1255adde3" />
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5" name="set facility" id="2025.04e69484-ba53-4228-86eb-0d501f0dc767">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="383" y="312" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5</ns16:outgoing>
                                
                                
                                <ns16:script>var repeated = false;&#xD;
for(var index = 0 ; index&lt;tw.local.customerFacilities.length; index++){&#xD;
	&#xD;
		if(!!tw.local.facility.facilityCode){&#xD;
		&#xD;
			if(tw.local.facility.facilityCode.value == tw.local.customerFacilities[index].facilityCode){&#xD;
				&#xD;
				for (var i=0; i&lt;tw.local.idccontract.facilities.length; i++) {&#xD;
					if(tw.local.facility.facilityCode.value == tw.local.idccontract.facilities[i].facilityCode){&#xD;
						tw.system.coachValidation.addValidationError("tw.local.facility.facilityCode.value","You Can't add a facility already added");&#xD;
						tw.local.errorMessage += "You Can't add a facility already added";&#xD;
						repeated = true;&#xD;
						break;&#xD;
					}&#xD;
				}&#xD;
					if (repeated == false)&#xD;
						tw.local.idccontract.facilities[tw.local.idccontract.facilities.length] =  tw.local.customerFacilities[index];&#xD;
			}&#xD;
		}&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.04e69484-ba53-4228-86eb-0d501f0dc767" targetRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" name="To Coach" id="2027.fa556b70-5e9a-492b-8e25-f7359a2fddf5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" name="customerFacilities" id="2056.9cab2ac1-9b42-42d3-8844-3b3f57cef4a9" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.65f977a5-08bd-4ee0-8eef-2b673b37ebda" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="SectionVis" id="2056.103f9365-0f25-4119-8935-eb833fc5d078">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"NONE"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:callActivity calledElement="1.e3028b61-b01a-4a56-a086-244083a8d445" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d" name="Get Applicant and Accountee Facility Codes" id="2025.d05653fc-8f37-40dd-8653-9d32680e9c42">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:postAssignmentScript>if (tw.local.customerFacilities.length&gt;0) {&#xD;
	tw.local.SectionVis = "DEFAULT";&#xD;
}&#xD;
else{&#xD;
	tw.local.SectionVis = "NONE";&#xD;
}&#xD;
</ns3:postAssignmentScript>
                                    
                                    
                                    <ns13:nodeVisualInfo x="436" y="40" width="95" height="70" />
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.805a4cbb-3faf-424f-8d2e-0300020c7082</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.cifs</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.506476be-4213-48fc-9956-612812bf0ca8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.partyType</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.facilityCodes</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c">tw.local.customerFacilities</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" targetRef="2025.04e69484-ba53-4228-86eb-0d501f0dc767" name="To Client-Side Script 1" id="2027.00ad7eeb-8e19-46dd-898e-efaee0a9e66e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="0be70765-64ec-491f-8901-40ecdd04cb41">
                                        
                                        
                                        <ns3:coachEventPath>Limits_Tracking1/Credit_Facility_Information1/addFacility</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d05653fc-8f37-40dd-8653-9d32680e9c42" targetRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" name="To Coach" id="2027.9cf119f8-73f2-4362-81a6-f45dfcd75a9d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.039c03f1-059b-41f8-a623-142ad6b3d1ed" targetRef="2025.d05653fc-8f37-40dd-8653-9d32680e9c42" name="To Get Applicant and Accountee Facility Codes" id="2027.bb5a1ce3-f65f-4bd1-866e-39f67f73ec7e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:coachEventBinding id="a33e7cd4-0d84-4ba3-8bc0-47ea4ab04239">
                                        
                                        
                                        <ns3:coachEventPath>Limits_Tracking1/GetFacility</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="cifs" id="2056.589f1cdc-b7dd-48f9-8cc8-6c27ada46dcd" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="partyType" id="2056.430a5a24-289c-4f8c-8c93-4f8ed424c0a0" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="productCode" id="2056.77c9961e-3bd9-4730-81d8-f8fbd3749bb2" />
                            
                            
                            <ns3:htmlHeaderTag id="744ec9d7-c0db-44d4-9ca3-57e1dc492114">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                        
                        <ns3:mobileReady>true</ns3:mobileReady>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:inputSet id="_d7cd800c-7b5e-4cae-84cc-e90861a429a6" />
                        
                        
                        <ns16:outputSet id="_1e7a194d-4ad8-4041-9cae-0fa01e20bc80" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.054de615-7a89-44b5-898a-a667109436a9</processLinkId>
            <processId>1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.eb3ab9e8-eb92-49f7-ae50-9c8029719df2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.418b6b22-7584-41e1-a517-92502584bb22</toProcessItemId>
            <guid>72367acd-20f0-4cd6-965a-538e5d5aa263</guid>
            <versionId>b9f3b843-c0bb-4f2d-894c-90cb31058af4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.eb3ab9e8-eb92-49f7-ae50-9c8029719df2</fromProcessItemId>
            <toProcessItemId>2025.418b6b22-7584-41e1-a517-92502584bb22</toProcessItemId>
        </link>
    </process>
</teamworks>

