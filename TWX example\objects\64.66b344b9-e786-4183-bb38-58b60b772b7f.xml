<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.66b344b9-e786-4183-bb38-58b60b772b7f" name="test">
        <lastModified>1703531723188</lastModified>
        <lastModifiedBy>eslam</lastModifiedBy>
        <coachViewId>64.66b344b9-e786-4183-bb38-58b60b772b7f</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;baadeee7-fffa-4234-8848-d25a44937ba6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Button1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d334cc3-6a77-40c7-83f3-cca9b4e4d55a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Button&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;62d3f34b-5ad4-4b19-89a7-1d27aa88230a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e0cec167-29b5-48c7-8f51-d2acc3e094ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9f1ac989-3d6b-4ae7-8980-0ba7588e1b25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.runJson()&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;7372576d-ee89-420d-86d6-6247ac6f9a1b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CIFNumber1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e159479b-2e49-4ae7-bf14-6cd18ab5a916&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2d046b0-53ac-4c9a-865d-0bce972aa985&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;C I F Number&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;69d53ff1-2110-4bbf-85c8-f9dab7b99c66&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.idc.customerInformation.CIFNumber&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;5a6cc7bb-7d7b-4190-84f6-0967359fc3b8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Service_Call1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da0258f6-7561-4735-835c-314bdaf06339&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Service call&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e34f0d75-6019-4cb3-8a95-b573c00bab35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bfcf528a-2ef7-4ea2-864d-39ce0ddec2aa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0967768a-4b08-45fe-8c56-5e9cdcd3bb37&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.7cd9c833-9de0-441c-987b-2cea94c2b257&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c34019b-f5fc-4223-8b1b-bd65a0f79c3c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>false</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-908</guid>
        <versionId>b2f379a3-f9b6-4dc7-be0f-7e0ffabc035f</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="idc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.d59f77a0-46f2-472f-86f8-07055a79fa90</coachViewBindingTypeId>
            <coachViewId>64.66b344b9-e786-4183-bb38-58b60b772b7f</coachViewId>
            <isList>false</isList>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>5df6ed6f-e94d-4d0d-9f87-ca90e84f7e18</guid>
            <versionId>7287f032-d8fd-48d9-a75a-c480d381c017</versionId>
        </bindingType>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.0ebb7ed9-02e1-4524-acf8-55cb1ba4d944</coachViewInlineScriptId>
            <coachViewId>64.66b344b9-e786-4183-bb38-58b60b772b7f</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.runJson = function  () {&#xD;
console.log(this.context.binding.get("value"));&#xD;
//console.log(JSON.stringify(this.context.binding.get("value")));&#xD;
//	this.ui.get("Service_Call1").execute(JSON.stringify(this.context.binding.get("value")));&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>f98898e4-bc16-455b-b14b-2408956a17ef</guid>
            <versionId>819a0a7e-0388-42c7-bd85-0b6049b0b59f</versionId>
        </inlineScript>
    </coachView>
</teamworks>

