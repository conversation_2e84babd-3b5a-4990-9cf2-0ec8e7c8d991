<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.6d2930cb-0b25-4940-913e-e6643366bf6a" name="Cancel IDC">
        <lastModified>1720862577741</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-6635</guid>
        <versionId>f3308354-323b-4503-8927-8816e96a8247</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:d227ae9e677c32d5:-661aced:190969e48ec:425a" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"8a83db0a-c695-40c9-8549-4e1272a3ebe2"},{"incoming":["4df537f8-0a99-400a-8d21-7b34440bc127","f359425c-d976-46cb-848a-19fd6ab13ece"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:83e1efe624431d49:-7084ad56:189daab98ba:-6633"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"25715225-e09f-496e-8357-1aeda6ec13ea"},{"targetRef":"010bc4ab-0448-4a2b-800c-2a6afa1b899c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL","declaredType":"sequenceFlow","id":"2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d","sourceRef":"8a83db0a-c695-40c9-8549-4e1272a3ebe2"},{"startQuantity":1,"outgoing":["8676d298-6219-4c3c-845c-54a0b4af7cc5"],"incoming":["2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":114,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Init SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"010bc4ab-0448-4a2b-800c-2a6afa1b899c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\n\/\/------------------------------deleteing used advance payment--------------------------\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].sql = \"DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ? ;\";\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.instanceID);\r\n\/\/------------------------------update advance payment----------------------------------\r\ni=0;\r\nj++;\r\nvar n = 0;\r\nfor (; j&lt;tw.local.IDCRequest.financialDetails.usedAdvancePayment.listLength+1; j++) {\r\n\t\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.sqlStatements[j].sql = \"update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?\";\r\n\t\r\n\tparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);\r\n\tparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);\r\n\tparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);\r\n\tn++;\r\n}"]}},{"targetRef":"ef2e22e7-e81c-49a4-83df-cfe9420d24b3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"8676d298-6219-4c3c-845c-54a0b4af7cc5","sourceRef":"010bc4ab-0448-4a2b-800c-2a6afa1b899c"},{"startQuantity":1,"outgoing":["4df537f8-0a99-400a-8d21-7b34440bc127"],"incoming":["8676d298-6219-4c3c-845c-54a0b4af7cc5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":280,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"ef2e22e7-e81c-49a4-83df-cfe9420d24b3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"25715225-e09f-496e-8357-1aeda6ec13ea","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4df537f8-0a99-400a-8d21-7b34440bc127","sourceRef":"ef2e22e7-e81c-49a4-83df-cfe9420d24b3"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.417a2dd2-abc0-49e9-8f6b-0f4b7abd233c"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.5a57b0ab-0c13-47bc-8b34-c9fa5fde11f1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.f723f677-b9e0-48a3-8ad7-de887fc32599"},{"parallelMultiple":false,"outgoing":["f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ba5550e9-8b2e-4167-8375-47e408109e80"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"945234fd-595b-4d01-805c-d5da61a97a31","otherAttributes":{"eventImplId":"27a6f6cc-79c7-4a7f-886a-15ec2cf7adfa"}}],"attachedToRef":"010bc4ab-0448-4a2b-800c-2a6afa1b899c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":149,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"6ccd47d0-2868-4ed0-8c05-2acc82d8e066","outputSet":{}},{"parallelMultiple":false,"outgoing":["26dde0cb-3b53-48c2-82ff-511e4fdc99cc"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"d52ea8d2-2571-4d26-8e06-f43c4cf4a70f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"02b03ef6-9517-41b8-8344-f6096755d4c2","otherAttributes":{"eventImplId":"e4a8f621-adc5-4a5c-8e98-460f6b633910"}}],"attachedToRef":"ef2e22e7-e81c-49a4-83df-cfe9420d24b3","extensionElements":{"nodeVisualInfo":[{"width":24,"x":315,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"5f75aef5-e164-41aa-8f5f-34c2632dd4ba","outputSet":{}},{"startQuantity":1,"outgoing":["f359425c-d976-46cb-848a-19fd6ab13ece"],"incoming":["f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6","26dde0cb-3b53-48c2-82ff-511e4fdc99cc"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":219,"y":169,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4388fcc2-e57b-4d45-8d8e-fbd62a087305","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"4388fcc2-e57b-4d45-8d8e-fbd62a087305","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6","sourceRef":"6ccd47d0-2868-4ed0-8c05-2acc82d8e066"},{"targetRef":"4388fcc2-e57b-4d45-8d8e-fbd62a087305","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"26dde0cb-3b53-48c2-82ff-511e4fdc99cc","sourceRef":"5f75aef5-e164-41aa-8f5f-34c2632dd4ba"},{"targetRef":"25715225-e09f-496e-8357-1aeda6ec13ea","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f359425c-d976-46cb-848a-19fd6ab13ece","sourceRef":"4388fcc2-e57b-4d45-8d8e-fbd62a087305"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.c9b483a8-1995-485f-88df-2d9176846eef"}],"laneSet":[{"id":"99da0940-ab2d-47a1-8ff1-97baa18a4fca","lane":[{"flowNodeRef":["8a83db0a-c695-40c9-8549-4e1272a3ebe2","25715225-e09f-496e-8357-1aeda6ec13ea","010bc4ab-0448-4a2b-800c-2a6afa1b899c","ef2e22e7-e81c-49a4-83df-cfe9420d24b3","6ccd47d0-2868-4ed0-8c05-2acc82d8e066","5f75aef5-e164-41aa-8f5f-34c2632dd4ba","4388fcc2-e57b-4d45-8d8e-fbd62a087305"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"60de525d-95f8-4ea9-8739-b1a428866d34","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Cancel IDC","declaredType":"process","id":"1.6d2930cb-0b25-4940-913e-e6643366bf6a","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.d7b9fe57-c002-4d94-8840-e4dfb51b5e1a"}],"inputSet":[{"dataInputRefs":["2055.9e3ee311-205e-4eb0-8aaa-cf29db6163a3"]}],"outputSet":[{"dataOutputRefs":["2055.d7b9fe57-c002-4d94-8840-e4dfb51b5e1a"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\nautoObject.customerInformation.facilityType.id = 0;\nautoObject.customerInformation.facilityType.code = \"\";\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\nautoObject.customerInformation.facilityType.englishdescription = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new TWDate();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"IDCRequest","isCollection":false,"id":"2055.9e3ee311-205e-4eb0-8aaa-cf29db6163a3"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9e3ee311-205e-4eb0-8aaa-cf29db6163a3</processParameterId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7adbc2bc-f3fa-4904-a028-bb57693052bf</guid>
            <versionId>440ffa62-7c19-4ea8-b57b-b27cb0bb2637</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d7b9fe57-c002-4d94-8840-e4dfb51b5e1a</processParameterId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d1119881-a185-47ba-ac04-9ddbd6fbdd9e</guid>
            <versionId>a23c294d-afd7-44fa-a52c-da983c65dcd9</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.417a2dd2-abc0-49e9-8f6b-0f4b7abd233c</processVariableId>
            <description isNull="true" />
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6b89c56f-991b-440a-ab1b-98389b7b5cf1</guid>
            <versionId>e2147d9d-5072-4f9c-8271-9e29a985004d</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5a57b0ab-0c13-47bc-8b34-c9fa5fde11f1</processVariableId>
            <description isNull="true" />
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2c692080-8613-4aa8-ab06-5b9349ff1403</guid>
            <versionId>3ccc5578-d814-4763-b242-e1204abe34ea</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f723f677-b9e0-48a3-8ad7-de887fc32599</processVariableId>
            <description isNull="true" />
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4cd802f3-e76e-4ee5-85fc-826a4b40fe17</guid>
            <versionId>46d2bdeb-29c3-4c75-8981-57deb8aaf317</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c9b483a8-1995-485f-88df-2d9176846eef</processVariableId>
            <description isNull="true" />
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b6ba3b2c-0745-4d92-bffa-14632174550b</guid>
            <versionId>dc2412f8-dad2-42d9-99f7-51ccb08374c9</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.25715225-e09f-496e-8357-1aeda6ec13ea</processItemId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6c0c8210-4796-4b19-984c-3ed902fc4fea</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-6633</guid>
            <versionId>1d915892-1d6e-4c33-ac15-df9d7ee12dc2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6c0c8210-4796-4b19-984c-3ed902fc4fea</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0d2bee1e-80de-4b0c-9dc1-3e363e0b097b</guid>
                <versionId>99add5b3-0d92-4e7a-a4cc-194c545457e9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ef2e22e7-e81c-49a4-83df-cfe9420d24b3</processItemId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c828c75c-7953-4b11-97e8-52de0bb9393c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-6415</guid>
            <versionId>3a4d7d51-dbd0-4429-9b48-8c09830d2f22</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="280" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d24</errorHandlerItem>
                <errorHandlerItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c828c75c-7953-4b11-97e8-52de0bb9393c</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>0a899bb6-d609-4e66-9789-03bb2791be12</guid>
                <versionId>44c53cd2-9300-4e2e-9753-45a7bed7d62a</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ad7233bd-76de-44d2-8af4-eed40b281209</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.c828c75c-7953-4b11-97e8-52de0bb9393c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ff328e79-1e68-4a12-a390-c7fb9ba85be2</guid>
                    <versionId>07d5b9fd-da21-46c1-a320-dd67cd4915e0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fc58a50d-4c46-4801-8033-66e7f2f3a863</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.c828c75c-7953-4b11-97e8-52de0bb9393c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a71e75a7-9c47-4153-922f-4baf499ba228</guid>
                    <versionId>5ec3b592-c7f9-4deb-8a22-3158fcf0d3a3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.97ade602-d3a4-4bb2-bf49-088716fff5b5</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.c828c75c-7953-4b11-97e8-52de0bb9393c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>800a0f0d-ee2a-46f5-b33d-7fb026a734ab</guid>
                    <versionId>f3428f61-9a8f-4b13-9c9a-3d9df8630cf9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</processItemId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <name>Init SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.65244d2d-3476-4040-a885-a5b6fc6b4108</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-6416</guid>
            <versionId>64dac2b8-00c2-42f1-8af8-4daf4fb0ba49</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.5f53e95b-9828-489a-ad58-53a7b81fd132</processItemPrePostId>
                <processItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>410be02c-6e81-46c2-b59a-d3b74261b700</guid>
                <versionId>85db63de-4438-4c21-bb47-a9282630667e</versionId>
            </processPrePosts>
            <layoutData x="114" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d24</errorHandlerItem>
                <errorHandlerItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.65244d2d-3476-4040-a885-a5b6fc6b4108</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//------------------------------deleteing used advance payment--------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql = "DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ? ;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.instanceID);&#xD;
//------------------------------update advance payment----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
var n = 0;&#xD;
for (; j&lt;tw.local.IDCRequest.financialDetails.usedAdvancePayment.listLength+1; j++) {&#xD;
	&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
	&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	n++;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>a2ed2cad-5e1b-4d7e-98f7-131a5483bbb0</guid>
                <versionId>ec2afcea-b2c9-46e0-bc6c-c0bf198a0a5d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</processItemId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.431ca0c7-1063-44b5-9ff5-2b9bedc27d95</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d24</guid>
            <versionId>d2b4d441-af1b-4cb8-9a5c-05a16ab2177d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="219" y="169">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.431ca0c7-1063-44b5-9ff5-2b9bedc27d95</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>cef0a4cf-abad-4b80-ab69-0d2fa70209b9</guid>
                <versionId>e91969b3-6707-4cfc-aea6-9ef6adf667c5</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Cancel IDC" id="1.6d2930cb-0b25-4940-913e-e6643366bf6a" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.9e3ee311-205e-4eb0-8aaa-cf29db6163a3">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new TWDate();
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.d7b9fe57-c002-4d94-8840-e4dfb51b5e1a" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.9e3ee311-205e-4eb0-8aaa-cf29db6163a3</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.d7b9fe57-c002-4d94-8840-e4dfb51b5e1a</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="99da0940-ab2d-47a1-8ff1-97baa18a4fca">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="60de525d-95f8-4ea9-8739-b1a428866d34" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>8a83db0a-c695-40c9-8549-4e1272a3ebe2</ns16:flowNodeRef>
                            <ns16:flowNodeRef>25715225-e09f-496e-8357-1aeda6ec13ea</ns16:flowNodeRef>
                            <ns16:flowNodeRef>010bc4ab-0448-4a2b-800c-2a6afa1b899c</ns16:flowNodeRef>
                            <ns16:flowNodeRef>ef2e22e7-e81c-49a4-83df-cfe9420d24b3</ns16:flowNodeRef>
                            <ns16:flowNodeRef>6ccd47d0-2868-4ed0-8c05-2acc82d8e066</ns16:flowNodeRef>
                            <ns16:flowNodeRef>5f75aef5-e164-41aa-8f5f-34c2632dd4ba</ns16:flowNodeRef>
                            <ns16:flowNodeRef>4388fcc2-e57b-4d45-8d8e-fbd62a087305</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="8a83db0a-c695-40c9-8549-4e1272a3ebe2">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="25715225-e09f-496e-8357-1aeda6ec13ea">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-6633</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>4df537f8-0a99-400a-8d21-7b34440bc127</ns16:incoming>
                        <ns16:incoming>f359425c-d976-46cb-848a-19fd6ab13ece</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="8a83db0a-c695-40c9-8549-4e1272a3ebe2" targetRef="010bc4ab-0448-4a2b-800c-2a6afa1b899c" name="To Init SQL" id="2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL" id="010bc4ab-0448-4a2b-800c-2a6afa1b899c">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="114" y="57" width="95" height="70" />
                            <ns3:preAssignmentScript />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.d6c47786-61a8-4c23-86f9-d163a1d9ff8d</ns16:incoming>
                        <ns16:outgoing>8676d298-6219-4c3c-845c-54a0b4af7cc5</ns16:outgoing>
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//------------------------------deleteing used advance payment--------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql = "DELETE FROM BPM.IDC_ADVANCED_PAYMENT_USED WHERE BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ? ;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.instanceID);&#xD;
//------------------------------update advance payment----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
var n = 0;&#xD;
for (; j&lt;tw.local.IDCRequest.financialDetails.usedAdvancePayment.listLength+1; j++) {&#xD;
	&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
	&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.usedAdvancePayment[n].DBID);&#xD;
	n++;&#xD;
}</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="010bc4ab-0448-4a2b-800c-2a6afa1b899c" targetRef="ef2e22e7-e81c-49a4-83df-cfe9420d24b3" name="To SQL Execute Multiple Statements (SQLResult)" id="8676d298-6219-4c3c-845c-54a0b4af7cc5">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="SQL Execute Multiple Statements (SQLResult)" id="ef2e22e7-e81c-49a4-83df-cfe9420d24b3">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="280" y="57" width="95" height="70" />
                            <ns4:activityType>CalledProcess</ns4:activityType>
                        </ns16:extensionElements>
                        <ns16:incoming>8676d298-6219-4c3c-845c-54a0b4af7cc5</ns16:incoming>
                        <ns16:outgoing>4df537f8-0a99-400a-8d21-7b34440bc127</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataOutputAssociation>
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            <ns16:assignment>
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                            </ns16:assignment>
                        </ns16:dataOutputAssociation>
                    </ns16:callActivity>
                    <ns16:sequenceFlow sourceRef="ef2e22e7-e81c-49a4-83df-cfe9420d24b3" targetRef="25715225-e09f-496e-8357-1aeda6ec13ea" name="To End" id="4df537f8-0a99-400a-8d21-7b34440bc127">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.417a2dd2-abc0-49e9-8f6b-0f4b7abd233c" />
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.5a57b0ab-0c13-47bc-8b34-c9fa5fde11f1" />
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.f723f677-b9e0-48a3-8ad7-de887fc32599" />
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="010bc4ab-0448-4a2b-800c-2a6afa1b899c" parallelMultiple="false" name="Error" id="6ccd47d0-2868-4ed0-8c05-2acc82d8e066">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="149" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ba5550e9-8b2e-4167-8375-47e408109e80" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="945234fd-595b-4d01-805c-d5da61a97a31" eventImplId="27a6f6cc-79c7-4a7f-886a-15ec2cf7adfa">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ef2e22e7-e81c-49a4-83df-cfe9420d24b3" parallelMultiple="false" name="Error1" id="5f75aef5-e164-41aa-8f5f-34c2632dd4ba">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="315" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>26dde0cb-3b53-48c2-82ff-511e4fdc99cc</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="d52ea8d2-2571-4d26-8e06-f43c4cf4a70f" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="02b03ef6-9517-41b8-8344-f6096755d4c2" eventImplId="e4a8f621-adc5-4a5c-8e98-460f6b633910">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="4388fcc2-e57b-4d45-8d8e-fbd62a087305">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="219" y="169" width="95" height="70" color="#FF7782" />
                        </ns16:extensionElements>
                        <ns16:incoming>f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6</ns16:incoming>
                        <ns16:incoming>26dde0cb-3b53-48c2-82ff-511e4fdc99cc</ns16:incoming>
                        <ns16:outgoing>f359425c-d976-46cb-848a-19fd6ab13ece</ns16:outgoing>
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="6ccd47d0-2868-4ed0-8c05-2acc82d8e066" targetRef="4388fcc2-e57b-4d45-8d8e-fbd62a087305" name="To Catch Errors" id="f2d1ace5-9801-4b76-8fc2-f4aa7c8eb7b6">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="5f75aef5-e164-41aa-8f5f-34c2632dd4ba" targetRef="4388fcc2-e57b-4d45-8d8e-fbd62a087305" name="To Catch Errors" id="26dde0cb-3b53-48c2-82ff-511e4fdc99cc">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="4388fcc2-e57b-4d45-8d8e-fbd62a087305" targetRef="25715225-e09f-496e-8357-1aeda6ec13ea" name="To End" id="f359425c-d976-46cb-848a-19fd6ab13ece">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.c9b483a8-1995-485f-88df-2d9176846eef" />
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8676d298-6219-4c3c-845c-54a0b4af7cc5</processLinkId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ef2e22e7-e81c-49a4-83df-cfe9420d24b3</toProcessItemId>
            <guid>59c00b29-d64a-4f82-b57b-add99bb0983d</guid>
            <versionId>dc262751-7ff2-4a2d-a0e6-9c8e656ea450</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.010bc4ab-0448-4a2b-800c-2a6afa1b899c</fromProcessItemId>
            <toProcessItemId>2025.ef2e22e7-e81c-49a4-83df-cfe9420d24b3</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f359425c-d976-46cb-848a-19fd6ab13ece</processLinkId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.25715225-e09f-496e-8357-1aeda6ec13ea</toProcessItemId>
            <guid>289fd115-94cf-4e8f-98b5-a3148508aab7</guid>
            <versionId>e2b2472b-8bb9-4789-a406-afd85f162421</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.4388fcc2-e57b-4d45-8d8e-fbd62a087305</fromProcessItemId>
            <toProcessItemId>2025.25715225-e09f-496e-8357-1aeda6ec13ea</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4df537f8-0a99-400a-8d21-7b34440bc127</processLinkId>
            <processId>1.6d2930cb-0b25-4940-913e-e6643366bf6a</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ef2e22e7-e81c-49a4-83df-cfe9420d24b3</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.25715225-e09f-496e-8357-1aeda6ec13ea</toProcessItemId>
            <guid>3ee2da7d-abc1-4d11-8e85-70b05a48ec8a</guid>
            <versionId>e73f5a21-6a6a-46e8-915e-50a065bf4848</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ef2e22e7-e81c-49a4-83df-cfe9420d24b3</fromProcessItemId>
            <toProcessItemId>2025.25715225-e09f-496e-8357-1aeda6ec13ea</toProcessItemId>
        </link>
    </process>
</teamworks>

