<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.02d07ad8-f212-4ea3-8b5e-e8457d984314" name="validate accounts and attachment">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.9b0a708c-4b86-4f08-a510-85fd8c66cead</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>ae8bd153-c6a4-4c82-b5de-6b1325033cad</guid>
        <versionId>e7d97031-56df-4548-a0ab-0e5948847fd0</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3efc" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"92acb06a-e33c-4492-8878-08e0da0cb784"},{"incoming":["1401e3a3-6ffd-4066-a2dd-0dfd63535f56"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6146"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"********-d277-48bd-b152-b4809dd8bd9e"},{"targetRef":"9b0a708c-4b86-4f08-a510-85fd8c66cead","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Check Customer Accounts","declaredType":"sequenceFlow","id":"2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9","sourceRef":"92acb06a-e33c-4492-8878-08e0da0cb784"},{"startQuantity":1,"outgoing":["1401e3a3-6ffd-4066-a2dd-0dfd63535f56"],"incoming":["2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":418,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Required Documents","dataInputAssociation":[{"targetRef":"2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.documentsSource.englishdescription"]}}]},{"targetRef":"2055.aae56053-3bba-40b1-abc9-6a441a93f307","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.IDCRequestType.englishdescription"]}}]},{"targetRef":"2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","declaredType":"TFormalExpression","content":["tw.local.documentsTypesSelected"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9b0a708c-4b86-4f08-a510-85fd8c66cead","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.errorExist"]}}],"sourceRef":["2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.74cb9e94-45c3-4ba9-8862-d45286d425b6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749"]}],"calledElement":"1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2"},{"targetRef":"********-d277-48bd-b152-b4809dd8bd9e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"1401e3a3-6ffd-4066-a2dd-0dfd63535f56","sourceRef":"9b0a708c-4b86-4f08-a510-85fd8c66cead"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.446a3539-3839-48d8-83dc-c77f4c045b6f"}],"laneSet":[{"id":"eba85bd0-1f2b-47f0-a21b-03f75a37eb07","lane":[{"flowNodeRef":["92acb06a-e33c-4492-8878-08e0da0cb784","********-d277-48bd-b152-b4809dd8bd9e","9b0a708c-4b86-4f08-a510-85fd8c66cead"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"81285d97-0558-40f6-8592-6cc371fd2897","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"validate accounts and attachment","declaredType":"process","id":"1.02d07ad8-f212-4ea3-8b5e-e8457d984314","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"id":"2055.e4656a2a-4887-444d-8212-c2df402cfe0d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.95c78223-e670-4271-9ed1-0c377642c5c5"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.2b33f4df-f983-4df4-843f-411f47a6c8a8"}],"inputSet":[{"dataInputRefs":["2055.89fca13d-c79b-4425-8595-e7f39c82983e","2055.7a53f20a-455a-406a-afef-2953d3af1b32","2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85"]}],"outputSet":[{"dataOutputRefs":["2055.e4656a2a-4887-444d-8212-c2df402cfe0d","2055.95c78223-e670-4271-9ed1-0c377642c5c5","2055.2b33f4df-f983-4df4-843f-411f47a6c8a8"]}],"dataInput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.89fca13d-c79b-4425-8595-e7f39c82983e"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"id":"2055.7a53f20a-455a-406a-afef-2953d3af1b32"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.89fca13d-c79b-4425-8595-e7f39c82983e</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e3e451e2-eb6c-449d-8609-9182548d01b5</guid>
            <versionId>c40e4d9d-3f6c-4cf5-bb45-5f2acff6eba6</versionId>
        </processParameter>
        <processParameter name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7a53f20a-455a-406a-afef-2953d3af1b32</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>378ea03e-c07f-439f-83ad-67205b645abf</guid>
            <versionId>ef197e63-43d3-4f4e-b779-219089056bde</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>69e706b3-23f7-4f64-a44b-d18926ee4d2c</guid>
            <versionId>ffc8b414-1c78-4130-8f41-0088406006be</versionId>
        </processParameter>
        <processParameter name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e4656a2a-4887-444d-8212-c2df402cfe0d</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7bcc2f41-eff5-4f11-9f4c-374f4fb43b54</guid>
            <versionId>f057ff8c-cfc0-446a-971f-cd2c9f810791</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.95c78223-e670-4271-9ed1-0c377642c5c5</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dfba3b91-23e5-4809-a209-f9f9bd930ec8</guid>
            <versionId>cbde1626-3855-4900-8aae-010080039635</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2b33f4df-f983-4df4-843f-411f47a6c8a8</processParameterId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4ffa9cad-278f-4451-b929-4855f08c7dcf</guid>
            <versionId>f5c15410-8e79-4b8a-89af-af3f6084d71a</versionId>
        </processParameter>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.446a3539-3839-48d8-83dc-c77f4c045b6f</processVariableId>
            <description isNull="true" />
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>be9c30ab-caad-413c-9daf-774a498f911f</guid>
            <versionId>9f17c031-bb85-4e51-bbaa-f8ec09ee2457</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9b0a708c-4b86-4f08-a510-85fd8c66cead</processItemId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <name>Get Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.323bf697-180c-48b5-a377-96f417932420</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6147</guid>
            <versionId>3c283749-93df-4350-8d16-83936d030043</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="418" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.323bf697-180c-48b5-a377-96f417932420</subProcessId>
                <attachedProcessRef>/1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</attachedProcessRef>
                <guid>b22d654a-16f6-4bd8-9fec-fd278f6c0b8f</guid>
                <versionId>6b5553fd-de5f-4b85-81aa-585161a5ffde</versionId>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.352bbac4-a041-41e0-ae8c-************</parameterMappingId>
                    <processParameterId>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>169f2014-d186-4745-8b8e-9f0506aaafb2</guid>
                    <versionId>0ecff24a-5fda-46a5-907c-891ee9377700</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1503fb74-a84f-4010-8d74-a8086e388c95</parameterMappingId>
                    <processParameterId>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7236064f-2be9-452d-ba53-f2623ebe5132</guid>
                    <versionId>a192d5ca-d8b9-4831-bf58-9b69b34f26d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.64824d89-879c-4a82-ba75-4b90d9314dbe</parameterMappingId>
                    <processParameterId>2055.aae56053-3bba-40b1-abc9-6a441a93f307</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.IDCRequestType.englishdescription</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>982834df-2635-48d8-ad62-f2d1f8cc9f35</guid>
                    <versionId>b203163b-e2ee-4c6f-ab62-b12bd72e2018</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="folderId">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e897457f-3631-45a2-97e3-b9020c546e37</parameterMappingId>
                    <processParameterId>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.folderId</value>
                    <classRef>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1bc434af-7699-4598-89ab-2657fcb6b78d</guid>
                    <versionId>c9640b74-2ae3-488d-8828-487b5936e187</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="documentsTypesSelected">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9519e150-9f97-4dbc-96fa-e3818d51594a</parameterMappingId>
                    <processParameterId>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.documentsTypesSelected</value>
                    <classRef>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>33a60a72-fc1e-4346-a3bd-05473d73d82f</guid>
                    <versionId>ce134723-bfc6-4081-a9f4-def91c7be3b8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="documentSource">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.38df71be-e950-47fb-af7a-a5ff24fc7a52</parameterMappingId>
                    <processParameterId>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.documentsSource.englishdescription</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>06ad933d-e8e4-4e1f-914a-8b0bfdf29694</guid>
                    <versionId>e4b61e17-cdd2-461b-8ec4-ec659aac8fd8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorExist">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.71b6dd65-4a18-4f5c-95de-d508ce370677</parameterMappingId>
                    <processParameterId>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</processParameterId>
                    <parameterMappingParentId>3012.323bf697-180c-48b5-a377-96f417932420</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorExist</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a1ed16ef-e628-4ec8-8dbf-9132ff3cb287</guid>
                    <versionId>f691bf7d-19c8-468b-9ad6-bc0750c57e65</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.********-d277-48bd-b152-b4809dd8bd9e</processItemId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.06ff1674-859f-4c0b-abde-081c1a54b19b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6146</guid>
            <versionId>d29a0fd1-cc88-4193-bdf0-6e8b23794d99</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.06ff1674-859f-4c0b-abde-081c1a54b19b</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a9583e47-fa3e-4896-bc46-ec2e6fd84a6e</guid>
                <versionId>6f40bef1-1911-47af-9f77-68c89d00e37e</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.9b0a708c-4b86-4f08-a510-85fd8c66cead</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="validate accounts and attachment" id="1.02d07ad8-f212-4ea3-8b5e-e8457d984314" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.89fca13d-c79b-4425-8595-e7f39c82983e" />
                        
                        
                        <ns16:dataInput name="documentsTypesSelected" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.7a53f20a-455a-406a-afef-2953d3af1b32" />
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85" />
                        
                        
                        <ns16:dataOutput name="errorExist" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.e4656a2a-4887-444d-8212-c2df402cfe0d" />
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.95c78223-e670-4271-9ed1-0c377642c5c5" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.2b33f4df-f983-4df4-843f-411f47a6c8a8" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.89fca13d-c79b-4425-8595-e7f39c82983e</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.7a53f20a-455a-406a-afef-2953d3af1b32</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.e4656a2a-4887-444d-8212-c2df402cfe0d</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.95c78223-e670-4271-9ed1-0c377642c5c5</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.2b33f4df-f983-4df4-843f-411f47a6c8a8</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="eba85bd0-1f2b-47f0-a21b-03f75a37eb07">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="81285d97-0558-40f6-8592-6cc371fd2897" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>92acb06a-e33c-4492-8878-08e0da0cb784</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>********-d277-48bd-b152-b4809dd8bd9e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9b0a708c-4b86-4f08-a510-85fd8c66cead</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="92acb06a-e33c-4492-8878-08e0da0cb784">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="********-d277-48bd-b152-b4809dd8bd9e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6146</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1401e3a3-6ffd-4066-a2dd-0dfd63535f56</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="92acb06a-e33c-4492-8878-08e0da0cb784" targetRef="9b0a708c-4b86-4f08-a510-85fd8c66cead" name="To Check Customer Accounts" id="2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" name="Get Required Documents" id="9b0a708c-4b86-4f08-a510-85fd8c66cead">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="418" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.9b730166-242d-4ef5-9bb6-8aa7a902a5f9</ns16:incoming>
                        
                        
                        <ns16:outgoing>1401e3a3-6ffd-4066-a2dd-0dfd63535f56</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.documentsSource.englishdescription</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.aae56053-3bba-40b1-abc9-6a441a93f307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.IDCRequestType.englishdescription</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f">tw.local.documentsTypesSelected</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.errorExist</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="9b0a708c-4b86-4f08-a510-85fd8c66cead" targetRef="********-d277-48bd-b152-b4809dd8bd9e" name="To Exclusive Gateway" id="1401e3a3-6ffd-4066-a2dd-0dfd63535f56">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.446a3539-3839-48d8-83dc-c77f4c045b6f" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1401e3a3-6ffd-4066-a2dd-0dfd63535f56</processLinkId>
            <processId>1.02d07ad8-f212-4ea3-8b5e-e8457d984314</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9b0a708c-4b86-4f08-a510-85fd8c66cead</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1</endStateId>
            <toProcessItemId>2025.********-d277-48bd-b152-b4809dd8bd9e</toProcessItemId>
            <guid>f117db80-e505-412d-9b84-26a717d66e39</guid>
            <versionId>53f6abe9-9527-425c-b708-a68183ab2d3b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9b0a708c-4b86-4f08-a510-85fd8c66cead</fromProcessItemId>
            <toProcessItemId>2025.********-d277-48bd-b152-b4809dd8bd9e</toProcessItemId>
        </link>
    </process>
</teamworks>

