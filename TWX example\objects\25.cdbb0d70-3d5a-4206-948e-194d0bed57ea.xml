<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.cdbb0d70-3d5a-4206-948e-194d0bed57ea" name="cdbb0d70-3d5a-4206-948e-194d0bed57ea">
        <lastModified>1688665660476</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <bpdId>25.cdbb0d70-3d5a-4206-948e-194d0bed57ea</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>2</type>
        <rootBpdId>25.b7564ab7-fca1-4082-9d20-d5443b9ac86f</rootBpdId>
        <parentBpdId>25.b7564ab7-fca1-4082-9d20-d5443b9ac86f</parentBpdId>
        <parentFlowObjectId>cdbb0d70-3d5a-4206-948e-194d0bed57ea</parentFlowObjectId>
        <xmlData isNull="true" />
        <bpmn2Data isNull="true" />
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5074</guid>
        <versionId>897407a2-f6d8-42f4-90f7-6daf3aa7c9f5</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:4042929508233c47:-1c6e1647:188f04d39d0:6269">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>cdbb0d70-3d5a-4206-948e-194d0bed57ea</name>
            <documentation></documentation>
            <name>cdbb0d70-3d5a-4206-948e-194d0bed57ea</name>
            <dimension>
                <size w="0" h="0" />
            </dimension>
            <author>eslam</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1688669260395</creationDate>
            <modificationDate>1688666092043</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"Hub - Liquidation Treasury Approval:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>1</dueDateTime>
            <dueDateTimeResolution>2</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>false</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="c247b932-5c50-4175-b4ec-9698ef48c98b" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f4b" />
            <ownerTeamInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f4d" />
            <simulationScenario id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f4c">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1688669260467</startTime>
            </simulationScenario>
            <flow id="da7f1c96-4281-48b8-941a-e69e8a3c7865" connectionType="SequenceFlow">
                <name>Copy of To Maker Approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f4a" />
                </connection>
            </flow>
            <flow id="2adde10c-8de5-4ac1-8819-1bd72fd1a662" connectionType="SequenceFlow">
                <name>Copy of To Review Request by Treasury Maker</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f49" />
                </connection>
            </flow>
            <flow id="16e4d32c-947f-4f0e-97b7-e48d28c4e3bc" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Treasury Approval End</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f48" />
                </connection>
            </flow>
            <flow id="91b52665-37ee-48f6-8c11-62ca6f8cc999" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f47" />
                </connection>
            </flow>
            <flow id="8e4b92d6-2ce1-4d5c-9fca-c14626c95fde" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Waiting CBE Approval</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5051">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingCBE</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="e63c52cc-065d-46e5-a08c-e377588cc10e" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f46" />
                </connection>
            </flow>
            <flow id="ae5d7f34-9f77-4ffa-b676-fa9ba7b0df72" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Checker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5048">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingTreasuryMakerReview</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="1d7b17d3-a8a4-4236-8e93-49822c4af1e1" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Checker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5053">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingTreasuryCheckerApproval</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="c3587cc3-0961-4a70-99bc-8c0260d2a90f" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Treasury Approval End</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f45" />
                </connection>
            </flow>
            <flow id="1cf381d6-e0f3-416e-a542-226116b35e18" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Maker</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f44" />
                </connection>
            </flow>
            <flow id="69ffd275-7124-425c-968a-db639f7c691e" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f43" />
                </connection>
            </flow>
            <flow id="61746fda-00be-4e2c-a7ae-df5dcf168b23" connectionType="SequenceFlow">
                <name>Copy 2 of Copy 4 of To End Escalation</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f42" />
                </connection>
            </flow>
            <flow id="1864f53d-b9ea-4bf5-b29b-37e7cbfef02c" connectionType="SequenceFlow">
                <name>Copy of To checker Approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-4f41" />
                </connection>
            </flow>
            <pool id="c247b932-5c50-4175-b4ec-9698ef48c98b">
                <name>Pool</name>
                <documentation></documentation>
                <dimension>
                    <size w="3000" h="844" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="01c7f33e-18f1-4ef6-8a9c-c83886d4d7b6">
                    <name>Treasury Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>/24.b8c8690c-d45a-44c4-b516-a146d62282a0</attachedParticipant>
                    <flowObject id="48f37cbf-1c56-4a54-bb5c-39f1c37e626f" componentType="Activity">
                        <name>Hub - Liquidation Review Request by Treasury Maker</name>
                        <documentation></documentation>
                        <position>
                            <location x="196" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.c9864963-befb-4c80-8731-2c60cd4ef469</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5070">
                                    <serviceType>1</serviceType>
                                    <teamRef>/24.b8c8690c-d45a-44c4-b516-a146d62282a0</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-506f">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-505b">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="2adde10c-8de5-4ac1-8819-1bd72fd1a662" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504f">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="1cf381d6-e0f3-416e-a542-226116b35e18" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5049">
                            <positionId>bottomRight</positionId>
                            <input>true</input>
                            <flow ref="ae5d7f34-9f77-4ffa-b676-fa9ba7b0df72" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-505a">
                            <positionId>rightCenter</positionId>
                            <flow ref="da7f1c96-4281-48b8-941a-e69e8a3c7865" />
                        </outputPort>
                        <attachedEvent id="441bd261-e997-4795-b3aa-724f7fbec2f8" componentType="Event">
                            <name>Boundary Event15</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="8a4a9faa-6e22-4961-b862-aa80a2c15a8c">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="df69e746-d6ca-4169-859c-8f58741f2c0e">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5041">
                                <positionId>bottomCenter</positionId>
                                <flow ref="e63c52cc-065d-46e5-a08c-e377588cc10e" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5065">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="d2e8db69-de47-4895-8e5f-09ed63da1991" componentType="Event">
                        <name>Hub - Liquidation Treasury Approval Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-505c">
                            <positionId>rightCenter</positionId>
                            <flow ref="2adde10c-8de5-4ac1-8819-1bd72fd1a662" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="21f139f4-d040-4223-a443-d655f1179035" componentType="Event">
                        <name>Hub - Liquidation Treasury Approval End</name>
                        <documentation></documentation>
                        <position>
                            <location x="650" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5055">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="16e4d32c-947f-4f0e-97b7-e48d28c4e3bc" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504a">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="c3587cc3-0961-4a70-99bc-8c0260d2a90f" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="4fdbc178-eb7f-4ce9-8337-5044bf894dd2" componentType="Gateway">
                        <name>Hub - Liquidation Maker Approved?</name>
                        <documentation></documentation>
                        <position>
                            <location x="383" y="75" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5059">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="da7f1c96-4281-48b8-941a-e69e8a3c7865" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5057">
                            <positionId>bottomCenter</positionId>
                            <flow ref="1d7b17d3-a8a4-4236-8e93-49822c4af1e1" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5056">
                            <positionId>bottomCenter</positionId>
                            <flow ref="8e4b92d6-2ce1-4d5c-9fca-c14626c95fde" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5058">
                            <positionId>rightCenter</positionId>
                            <flow ref="16e4d32c-947f-4f0e-97b7-e48d28c4e3bc" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="2566f084-07f7-4eae-9d32-b7ae23c246d3">
                    <name>Treasury Checker</name>
                    <height>244</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>/24.ed3121d3-26d1-49da-a2c6-662fdd098a19</attachedParticipant>
                    <flowObject id="fc412f8b-dfd4-4e12-a267-9ec732c71f63" componentType="Activity">
                        <name>Hub - Liquidation Review Request by Treasury Checker</name>
                        <documentation></documentation>
                        <position>
                            <location x="322" y="41" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.9cae1848-9dc5-4c0c-8589-b609e1a67cf3</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-506d">
                                    <serviceType>1</serviceType>
                                    <teamRef>/24.ed3121d3-26d1-49da-a2c6-662fdd098a19</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-506c">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5054">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="1d7b17d3-a8a4-4236-8e93-49822c4af1e1" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504e">
                            <positionId>bottomCenter</positionId>
                            <flow ref="1864f53d-b9ea-4bf5-b29b-37e7cbfef02c" />
                        </outputPort>
                        <attachedEvent id="14043e2d-6499-4902-a84e-439de13caab6" componentType="Event">
                            <name>Boundary Event16</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomLeft</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="89face46-76f9-4fe2-bc53-fd5837c4e416">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="bb12a7ee-798c-48c2-8864-c63504fa634a">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5043">
                                <positionId>bottomCenter</positionId>
                                <flow ref="69ffd275-7124-425c-968a-db639f7c691e" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5062">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="81c571e6-8cb2-4ee0-8bd8-85d91a4c636c" componentType="Gateway">
                        <name>Hub - Liquidation Checker Approved?</name>
                        <documentation></documentation>
                        <position>
                            <location x="335" y="163" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504d">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="1864f53d-b9ea-4bf5-b29b-37e7cbfef02c" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504b">
                            <positionId>leftCenter</positionId>
                            <flow ref="ae5d7f34-9f77-4ffa-b676-fa9ba7b0df72" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-504c">
                            <positionId>rightCenter</positionId>
                            <flow ref="c3587cc3-0961-4a70-99bc-8c0260d2a90f" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="d80f8f42-c17a-4afe-8510-dd63a3dabe37">
                    <name>CPE Queue</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="8dca9729-35e8-4ef6-94e5-dc1d6d2e755b" componentType="Activity">
                        <name>Hub - Liquidation Waiting CBE Approval</name>
                        <documentation></documentation>
                        <position>
                            <location x="521" y="100" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.8d2972cb-0b13-4902-8a8c-b2f9b3f04284</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-506a">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5069">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5052">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="8e4b92d6-2ce1-4d5c-9fca-c14626c95fde" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5050">
                            <positionId>leftCenter</positionId>
                            <flow ref="1cf381d6-e0f3-416e-a542-226116b35e18" />
                        </outputPort>
                        <attachedEvent id="4dfeba06-08c3-4803-85ce-49b87686f111" componentType="Event">
                            <name>Boundary Event17</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="e3bdfb57-8ce4-4a51-bf7d-2dfd1f4feb38">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="af76eb70-5618-4a43-86a7-a67f2e8dbf7c">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5045">
                                <positionId>bottomCenter</positionId>
                                <flow ref="91b52665-37ee-48f6-8c11-62ca6f8cc999" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-505f">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                </lane>
                <lane id="9bebca58-1de6-4566-a7dd-b8c6bf9d676b">
                    <name>System</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="567bcb87-f46a-4887-9506-32e9bd8161fd" componentType="Activity">
                        <name>Copy of Treasury Escalation Mail Service</name>
                        <documentation></documentation>
                        <position>
                            <location x="613" y="99" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.e6f92ee4-8e79-4a4c-9f0d-224b731312bb</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5067">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5066">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5044">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="91b52665-37ee-48f6-8c11-62ca6f8cc999" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5042">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="69ffd275-7124-425c-968a-db639f7c691e" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5040">
                            <positionId>leftBottom</positionId>
                            <input>true</input>
                            <flow ref="e63c52cc-065d-46e5-a08c-e377588cc10e" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5047">
                            <positionId>rightCenter</positionId>
                            <flow ref="61746fda-00be-4e2c-a7ae-df5dcf168b23" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="b3eb84ce-9b8a-457e-9094-0d0905db1fbf" componentType="Event">
                        <name>Copy 2 of Treasury End Escalation</name>
                        <documentation></documentation>
                        <position>
                            <location x="831" y="117" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5046">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="61746fda-00be-4e2c-a7ae-df5dcf168b23" />
                        </inputPort>
                    </flowObject>
                </lane>
                <epv id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5073">
                    <epvId>/21.c5e93628-40a8-440a-b72d-55e73f2f0820</epvId>
                </epv>
            </pool>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

