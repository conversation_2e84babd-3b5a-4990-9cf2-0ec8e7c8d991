<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.473ca24e-c03e-4a25-b37a-58cd047b0fff" name="Get Customer Information">
        <lastModified>1692506617940</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>2011a185-2353-4208-9c32-dca16fc09872</guid>
        <versionId>106b6b3d-c66b-4e95-b1f1-a138f6a138c3</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e78" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.54616b93-a996-4136-a6ab-bcfa72940216"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":33,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c02c6ddd-86b3-409e-93c7-573d9a4eded1"},{"incoming":["48e017c4-2351-4892-8f35-c6664e0698e6","d37a79b6-d018-4088-8ad5-ffd32735823b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":654,"y":33,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d2"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"33f03f01-7b57-42ce-b7d5-e7dd4d775804"},{"targetRef":"b9e39b54-e4d8-46bf-ac4d-21dea042c2aa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.54616b93-a996-4136-a6ab-bcfa72940216","sourceRef":"c02c6ddd-86b3-409e-93c7-573d9a4eded1"},{"startQuantity":1,"outgoing":["48e017c4-2351-4892-8f35-c6664e0698e6"],"incoming":["464b588c-1f09-476f-872f-eedc2db9100b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":484,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Customer","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"93183563-2d8d-4207-97c8-25f03656ad33","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tif (tw.local.isSuccessful) {\r\n\t\ttw.local.customer = new tw.object.CustomerInformation();\r\n\t\ttw.local.customer.addressLine1 = tw.local.customerFullDetails.customerAddress.addressLine1;\r\n\t\ttw.local.customer.addressLine2 = tw.local.customerFullDetails.customerAddress.addressLine2;\r\n\t\ttw.local.customer.CBENumber = tw.local.customerFullDetails.customerCBENumber;\r\n\t\ttw.local.customer.CIFNumber = tw.local.data;\r\n\t\ttw.local.customer.commercialRegistrationNumber = tw.local.customerFullDetails.commercialRegisterNo;\r\n\t\ttw.local.customer.commercialRegistrationOffice = tw.local.customerFullDetails.commercialRegisterOffice;\r\n\t\ttw.local.customer.customerName = tw.local.customerFullDetails.EnglishName;\r\n\t\ttw.local.customer.customerSector = \"\";\r\n\t\ttw.local.customer.customerType = tw.local.customerFullDetails.customerType;\r\n\t\ttw.local.customer.facilityType = new tw.object.DBLookup();\r\n\t\/\/\ttw.local.customer.facilityType.id = 1;\r\n\t\/\/\ttw.local.customer.facilityType.arabicdescription =\"\"; \r\n\t\ttw.local.customer.importCardNumber = \"\";\r\n\t\ttw.local.customer.taxCardNumber = tw.local.customerFullDetails.cardTaxNo;\r\n\t\t\r\n\t\ttw.local.results = tw.local.customer;\r\n\t\t\r\n\t\r\n\t}else{\r\n\t\ttw.local.customer = new tw.object.CustomerInformation();\r\n\t\ttw.local.customer.CIFNumber = tw.local.data;\r\n\t\ttw.local.results = tw.local.customer;\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"33f03f01-7b57-42ce-b7d5-e7dd4d775804","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To MW_FC Retrieve Customer Details","declaredType":"sequenceFlow","id":"48e017c4-2351-4892-8f35-c6664e0698e6","sourceRef":"93183563-2d8d-4207-97c8-25f03656ad33"},{"itemSubjectRef":"itm.12.cc907e5e-4284-4dc5-8dea-8792e0a471c1","name":"customer","isCollection":false,"declaredType":"dataObject","id":"2056.e5860163-c7ce-4ca4-8262-53cf87b5a3a4"},{"startQuantity":1,"outgoing":["d3f97822-3c1f-4546-8f3a-65a84e153572"],"incoming":["2027.54616b93-a996-4136-a6ab-bcfa72940216"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":10,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"MW_FC Retrieve Customer Details","dataInputAssociation":[{"targetRef":"2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.b540e2f5-2008-4705-b4e1-7edcf2a379df","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.9e66331e-0f98-44e0-b836-7f39c9a6d317","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.4fcde37d-b029-4d8e-ae28-d26a621479e6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.40e09d4c-cff9-4d38-bd81-0995df08be6f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"b9e39b54-e4d8-46bf-ac4d-21dea042c2aa","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","declaredType":"TFormalExpression","content":["tw.local.customerFullDetails"]}}],"sourceRef":["2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.3d943bb5-aec9-4b29-862f-735a93741afa"]}],"calledElement":"1.fd9b955b-0237-4cbe-86d6-cd0d295550aa"},{"targetRef":"f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d","extensionElements":{"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2a57"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"d3f97822-3c1f-4546-8f3a-65a84e153572","sourceRef":"b9e39b54-e4d8-46bf-ac4d-21dea042c2aa"},{"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.c7000e0a-e470-4afc-8166-751498a301e4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.87aa2738-7fc9-40d5-bb9d-d5edd6af761e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.14e3b87a-f23b-41e7-99de-61114a58f04c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.e9a6b446-52d8-4104-9164-16abca13d12f"},{"parallelMultiple":false,"outgoing":["2bb2d5c1-1063-43fc-860c-bcdbef72bf52"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8619da31-4cb7-4eb6-8fcf-0b4a7e97a4bb"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"bed1d728-981e-4b07-8f2f-ba53a36ee468","otherAttributes":{"eventImplId":"5a2933ec-c57f-4478-8fb4-89a1c0939c94"}}],"attachedToRef":"b9e39b54-e4d8-46bf-ac4d-21dea042c2aa","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1809e12e-cfb1-462e-8f47-fbbc91d7b2a9","outputSet":{}},{"parallelMultiple":false,"outgoing":["4a74dfe6-22b0-430f-8713-addf464315c7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3da048f5-a6e7-42a6-84eb-8f7f8eef1419"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ce1f2976-58b7-49e8-8d96-4a165cca9933","otherAttributes":{"eventImplId":"dc39de3a-dcd6-48b8-866d-930d3c7e0ccd"}}],"attachedToRef":"93183563-2d8d-4207-97c8-25f03656ad33","extensionElements":{"nodeVisualInfo":[{"width":24,"x":519,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"3e3f0d08-1957-4e36-8f72-d150c83784bc","outputSet":{}},{"targetRef":"2989270a-9626-432a-8e5b-250325c6d31b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"2bb2d5c1-1063-43fc-860c-bcdbef72bf52","sourceRef":"1809e12e-cfb1-462e-8f47-fbbc91d7b2a9"},{"targetRef":"2989270a-9626-432a-8e5b-250325c6d31b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"4a74dfe6-22b0-430f-8713-addf464315c7","sourceRef":"3e3f0d08-1957-4e36-8f72-d150c83784bc"},{"outgoing":["464b588c-1f09-476f-872f-eedc2db9100b","5d573b3a-c0c3-4c20-8d23-d361d9409869"],"incoming":["d3f97822-3c1f-4546-8f3a-65a84e153572"],"default":"464b588c-1f09-476f-872f-eedc2db9100b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":362,"y":29,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d"},{"targetRef":"93183563-2d8d-4207-97c8-25f03656ad33","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Set Customer","declaredType":"sequenceFlow","id":"464b588c-1f09-476f-872f-eedc2db9100b","sourceRef":"f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d"},{"targetRef":"2989270a-9626-432a-8e5b-250325c6d31b","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"5d573b3a-c0c3-4c20-8d23-d361d9409869","sourceRef":"f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d"},{"startQuantity":1,"outgoing":["d37a79b6-d018-4088-8ad5-ffd32735823b"],"incoming":["5d573b3a-c0c3-4c20-8d23-d361d9409869","4a74dfe6-22b0-430f-8713-addf464315c7","2bb2d5c1-1063-43fc-860c-bcdbef72bf52"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":335,"y":110,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2989270a-9626-432a-8e5b-250325c6d31b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"33f03f01-7b57-42ce-b7d5-e7dd4d775804","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"d37a79b6-d018-4088-8ad5-ffd32735823b","sourceRef":"2989270a-9626-432a-8e5b-250325c6d31b"}],"laneSet":[{"id":"a1be402e-6657-41b4-8406-cfd784bf0dd4","lane":[{"flowNodeRef":["c02c6ddd-86b3-409e-93c7-573d9a4eded1","33f03f01-7b57-42ce-b7d5-e7dd4d775804","93183563-2d8d-4207-97c8-25f03656ad33","b9e39b54-e4d8-46bf-ac4d-21dea042c2aa","1809e12e-cfb1-462e-8f47-fbbc91d7b2a9","3e3f0d08-1957-4e36-8f72-d150c83784bc","f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d","2989270a-9626-432a-8e5b-250325c6d31b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":272}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"f6828fac-6c7c-4bcd-83ee-30d85e437567","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Customer Information","declaredType":"process","id":"1.473ca24e-c03e-4a25-b37a-58cd047b0fff","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.698c8352-cc35-46d0-98d4-94c170cbe32a"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.4e422c20-de59-4726-89c2-6597bdee0232"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"02366014\"\r\n\"06316421\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.0174bdda-f5e0-414a-9e53-265eb6df2cf5"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0174bdda-f5e0-414a-9e53-265eb6df2cf5</processParameterId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"02366014"&#xD;
"06316421"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e6452895-4cd1-450b-a795-4b19f97ba009</guid>
            <versionId>c4028669-b7d7-4abd-a16c-ca28daab1f7b</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.698c8352-cc35-46d0-98d4-94c170cbe32a</processParameterId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c1984074-4466-4860-8a03-7cbb1f33fa96</guid>
            <versionId>e7a55f80-7ca2-48b5-8788-48ae60a6d3bb</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4e422c20-de59-4726-89c2-6597bdee0232</processParameterId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>285d772e-5b11-45ed-a11d-7588e5215695</guid>
            <versionId>a79ba8f7-cce8-48bb-9950-a73bf7a900ad</versionId>
        </processParameter>
        <processVariable name="customer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e5860163-c7ce-4ca4-8262-53cf87b5a3a4</processVariableId>
            <description isNull="true" />
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.cc907e5e-4284-4dc5-8dea-8792e0a471c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>719e5757-c340-47f9-8719-c471185b6ecc</guid>
            <versionId>65671641-3d0f-4a90-95df-b61b3ffd0f6a</versionId>
        </processVariable>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c7000e0a-e470-4afc-8166-751498a301e4</processVariableId>
            <description isNull="true" />
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>200fbc78-ff9e-4111-8708-9374fc7770d9</guid>
            <versionId>99f0b9be-ea7e-490c-92a1-ae602ba9cb25</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.87aa2738-7fc9-40d5-bb9d-d5edd6af761e</processVariableId>
            <description isNull="true" />
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4de06bcc-c1b3-455e-81ec-f76d802170a2</guid>
            <versionId>ebdd9aa8-e0a8-4d77-a6df-fae9efbe56a1</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.14e3b87a-f23b-41e7-99de-61114a58f04c</processVariableId>
            <description isNull="true" />
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c95771ac-f752-4c0d-a9a6-42a309c083d6</guid>
            <versionId>cf542635-6577-427e-bb44-ad58efab7e9c</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e9a6b446-52d8-4104-9164-16abca13d12f</processVariableId>
            <description isNull="true" />
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e9b5e405-a773-4926-9083-08e64e543f58</guid>
            <versionId>441d675c-19cc-4900-bbe2-e64398b53808</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.93183563-2d8d-4207-97c8-25f03656ad33</processItemId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <name>Set Customer</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a6783de8-71fa-47f8-960e-32ca9ba9b593</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d3</guid>
            <versionId>23e57dfd-3089-4dda-ae74-cdd007651009</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="484" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e76</errorHandlerItem>
                <errorHandlerItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a6783de8-71fa-47f8-960e-32ca9ba9b593</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.customer = new tw.object.CustomerInformation();&#xD;
		tw.local.customer.addressLine1 = tw.local.customerFullDetails.customerAddress.addressLine1;&#xD;
		tw.local.customer.addressLine2 = tw.local.customerFullDetails.customerAddress.addressLine2;&#xD;
		tw.local.customer.CBENumber = tw.local.customerFullDetails.customerCBENumber;&#xD;
		tw.local.customer.CIFNumber = tw.local.data;&#xD;
		tw.local.customer.commercialRegistrationNumber = tw.local.customerFullDetails.commercialRegisterNo;&#xD;
		tw.local.customer.commercialRegistrationOffice = tw.local.customerFullDetails.commercialRegisterOffice;&#xD;
		tw.local.customer.customerName = tw.local.customerFullDetails.EnglishName;&#xD;
		tw.local.customer.customerSector = "";&#xD;
		tw.local.customer.customerType = tw.local.customerFullDetails.customerType;&#xD;
		tw.local.customer.facilityType = new tw.object.DBLookup();&#xD;
	//	tw.local.customer.facilityType.id = 1;&#xD;
	//	tw.local.customer.facilityType.arabicdescription =""; &#xD;
		tw.local.customer.importCardNumber = "";&#xD;
		tw.local.customer.taxCardNumber = tw.local.customerFullDetails.cardTaxNo;&#xD;
		&#xD;
		tw.local.results = tw.local.customer;&#xD;
		&#xD;
	&#xD;
	}else{&#xD;
		tw.local.customer = new tw.object.CustomerInformation();&#xD;
		tw.local.customer.CIFNumber = tw.local.data;&#xD;
		tw.local.results = tw.local.customer;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>e59f2640-ef5c-4e86-949b-08745fc86e90</guid>
                <versionId>209199a4-f035-4e71-8d9a-7ccea065abcc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</processItemId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.953efbe3-ba50-4298-9c71-faccb4d20f34</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e76</guid>
            <versionId>65d0fded-822a-4fbc-a601-01a7680dd045</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="335" y="110">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.953efbe3-ba50-4298-9c71-faccb4d20f34</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>923df54f-2031-4f4e-8d17-75ec8dca453f</guid>
                <versionId>3396d72d-d7b0-4d70-b3be-afb22d540daf</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</processItemId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.81784b7a-3c1c-4a30-9733-f19c864aaa0a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f1a</guid>
            <versionId>71fad152-fa37-4447-b442-32f40d767c88</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="362" y="29">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.81784b7a-3c1c-4a30-9733-f19c864aaa0a</switchId>
                <guid>e03bd0f9-eeaa-4ab1-a27e-a81eddd3972a</guid>
                <versionId>dce8aace-67f6-4761-88fc-3f001f2c2616</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.1e8280c6-f738-43f7-91ec-7be19f7a2f84</switchConditionId>
                    <switchId>3013.81784b7a-3c1c-4a30-9733-f19c864aaa0a</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e77</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>8db0b83d-1843-437b-b2dd-a7d0a5a4978d</guid>
                    <versionId>7caecb34-92fa-4629-a529-01796d6fdda4</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.33f03f01-7b57-42ce-b7d5-e7dd4d775804</processItemId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.dd2c5d9f-eff7-46dc-a1d1-957958c75bf7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d2</guid>
            <versionId>7c810480-5fbc-46b5-8692-9660381a5395</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="654" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.dd2c5d9f-eff7-46dc-a1d1-957958c75bf7</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9fcf1a38-2218-4a7e-9d39-ff91e49849a1</guid>
                <versionId>c0d19097-3238-48e4-a285-dd2cc824abfb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</processItemId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <name>MW_FC Retrieve Customer Details</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d4</guid>
            <versionId>cd250f98-5af0-4272-9dee-e5d06b5afcb2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.0e8d5946-0abd-4aa4-975e-3ece70c5fb25</processItemPrePostId>
                <processItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>54f7e26b-426b-4f95-b23e-f833b0d997ed</guid>
                <versionId>1207b42b-1866-4e4d-9f28-e720be1129d8</versionId>
            </processPrePosts>
            <layoutData x="177" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e76</errorHandlerItem>
                <errorHandlerItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.fd9b955b-0237-4cbe-86d6-cd0d295550aa</attachedProcessRef>
                <guid>f64d9845-b9cd-4720-8d5b-908a9ee21eaf</guid>
                <versionId>f71d85e5-21e9-4cdb-b1bc-e996a4399d1b</versionId>
                <parameterMapping name="customerFullDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.212c5b50-79ae-4aee-886b-f0d8a7c10b91</parameterMappingId>
                    <processParameterId>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerFullDetails</value>
                    <classRef>/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>048c3309-b0e5-4f0c-b541-d6bf54049ec6</guid>
                    <versionId>2dcf9fb2-328d-409e-91c9-575be01f085f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cfefaedb-288b-4d67-8008-dd4fdcf35811</parameterMappingId>
                    <processParameterId>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cd849bce-762a-4f3d-9561-7c4f9e780a0e</guid>
                    <versionId>3af9d1d5-2533-49e9-b365-137c6ae67d32</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2c5c6da6-87fd-4553-894e-d43f2aa62d7c</parameterMappingId>
                    <processParameterId>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>731191dc-ecdc-4ca3-81fc-65447ff3f754</guid>
                    <versionId>430ce3ae-cfb3-449e-9d7b-a02579bac3d8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.164a2fbb-f7d2-45c2-a35d-0bc55d09fda1</parameterMappingId>
                    <processParameterId>2055.3d943bb5-aec9-4b29-862f-735a93741afa</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d9a91cdd-87f4-4885-97fe-fa4bb681bb39</guid>
                    <versionId>82391f98-0163-457a-b03f-ccd7e8eba737</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6f070845-09ff-49d3-899e-153640485e79</parameterMappingId>
                    <processParameterId>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>794bea43-e192-413b-ae6f-053a7dc4c1e5</guid>
                    <versionId>943c5979-e9c2-4830-a13f-8a892662ee9e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c3f4ed47-77d5-4bc3-b955-1f5bedb2f02c</parameterMappingId>
                    <processParameterId>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>efea1538-43a9-4495-b39d-c51b7d23f793</guid>
                    <versionId>9c5c684d-93bf-499d-a48d-8dde67465d79</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cac20e5b-e366-4fcd-bbed-44fa5052b13b</parameterMappingId>
                    <processParameterId>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fc48232c-c780-4158-8991-16c9aa38d97f</guid>
                    <versionId>9ce29c45-4768-46f8-b775-a43b3d5cb8d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d0d4117a-b443-4d09-a8f0-8914b27de910</parameterMappingId>
                    <processParameterId>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>35c810a9-3575-4406-b852-5ca269e7aad6</guid>
                    <versionId>a9d3d1b0-2f38-448d-9922-72f50bdd7b78</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c8ca4d38-30a6-4d35-ab19-8d9d93a4b29d</parameterMappingId>
                    <processParameterId>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c44fd29d-8420-49d0-aa2c-fec7ec250342</guid>
                    <versionId>ba4f6785-a083-458b-b893-2f5502ae8be1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerCif">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0eacc439-fe76-41b7-afb2-07e4eb31b485</parameterMappingId>
                    <processParameterId>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>09ee4d32-0faf-4479-bc94-91c344a4d8c3</guid>
                    <versionId>e41f5ef2-888e-427f-a602-a2a90570146d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.63b15a7a-8d96-49fc-89c2-4ace20ce6254</parameterMappingId>
                    <processParameterId>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</processParameterId>
                    <parameterMappingParentId>3012.f8026ecc-65f1-4258-b375-ae3e6b28c316</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>03a30a90-5568-49f3-afb3-9f3ffc9c5ca6</guid>
                    <versionId>e9fdb8a0-94ad-4b25-a311-8982f524f30c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Customer Information" id="1.473ca24e-c03e-4a25-b37a-58cd047b0fff" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.0174bdda-f5e0-414a-9e53-265eb6df2cf5">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"02366014"&#xD;
"06316421"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.698c8352-cc35-46d0-98d4-94c170cbe32a" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.4e422c20-de59-4726-89c2-6597bdee0232" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="a1be402e-6657-41b4-8406-cfd784bf0dd4">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="f6828fac-6c7c-4bcd-83ee-30d85e437567" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="272" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>c02c6ddd-86b3-409e-93c7-573d9a4eded1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>33f03f01-7b57-42ce-b7d5-e7dd4d775804</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>93183563-2d8d-4207-97c8-25f03656ad33</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1809e12e-cfb1-462e-8f47-fbbc91d7b2a9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3e3f0d08-1957-4e36-8f72-d150c83784bc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2989270a-9626-432a-8e5b-250325c6d31b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="c02c6ddd-86b3-409e-93c7-573d9a4eded1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="33" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.54616b93-a996-4136-a6ab-bcfa72940216</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="33f03f01-7b57-42ce-b7d5-e7dd4d775804">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="654" y="33" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d2</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>48e017c4-2351-4892-8f35-c6664e0698e6</ns16:incoming>
                        
                        
                        <ns16:incoming>d37a79b6-d018-4088-8ad5-ffd32735823b</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c02c6ddd-86b3-409e-93c7-573d9a4eded1" targetRef="b9e39b54-e4d8-46bf-ac4d-21dea042c2aa" name="To End" id="2027.54616b93-a996-4136-a6ab-bcfa72940216">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Customer" id="93183563-2d8d-4207-97c8-25f03656ad33">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="484" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>464b588c-1f09-476f-872f-eedc2db9100b</ns16:incoming>
                        
                        
                        <ns16:outgoing>48e017c4-2351-4892-8f35-c6664e0698e6</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.customer = new tw.object.CustomerInformation();&#xD;
		tw.local.customer.addressLine1 = tw.local.customerFullDetails.customerAddress.addressLine1;&#xD;
		tw.local.customer.addressLine2 = tw.local.customerFullDetails.customerAddress.addressLine2;&#xD;
		tw.local.customer.CBENumber = tw.local.customerFullDetails.customerCBENumber;&#xD;
		tw.local.customer.CIFNumber = tw.local.data;&#xD;
		tw.local.customer.commercialRegistrationNumber = tw.local.customerFullDetails.commercialRegisterNo;&#xD;
		tw.local.customer.commercialRegistrationOffice = tw.local.customerFullDetails.commercialRegisterOffice;&#xD;
		tw.local.customer.customerName = tw.local.customerFullDetails.EnglishName;&#xD;
		tw.local.customer.customerSector = "";&#xD;
		tw.local.customer.customerType = tw.local.customerFullDetails.customerType;&#xD;
		tw.local.customer.facilityType = new tw.object.DBLookup();&#xD;
	//	tw.local.customer.facilityType.id = 1;&#xD;
	//	tw.local.customer.facilityType.arabicdescription =""; &#xD;
		tw.local.customer.importCardNumber = "";&#xD;
		tw.local.customer.taxCardNumber = tw.local.customerFullDetails.cardTaxNo;&#xD;
		&#xD;
		tw.local.results = tw.local.customer;&#xD;
		&#xD;
	&#xD;
	}else{&#xD;
		tw.local.customer = new tw.object.CustomerInformation();&#xD;
		tw.local.customer.CIFNumber = tw.local.data;&#xD;
		tw.local.results = tw.local.customer;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="93183563-2d8d-4207-97c8-25f03656ad33" targetRef="33f03f01-7b57-42ce-b7d5-e7dd4d775804" name="To MW_FC Retrieve Customer Details" id="48e017c4-2351-4892-8f35-c6664e0698e6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.cc907e5e-4284-4dc5-8dea-8792e0a471c1" isCollection="false" name="customer" id="2056.e5860163-c7ce-4ca4-8262-53cf87b5a3a4" />
                    
                    
                    <ns16:callActivity calledElement="1.fd9b955b-0237-4cbe-86d6-cd0d295550aa" name="MW_FC Retrieve Customer Details" id="b9e39b54-e4d8-46bf-ac4d-21dea042c2aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="10" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.54616b93-a996-4136-a6ab-bcfa72940216</ns16:incoming>
                        
                        
                        <ns16:outgoing>d3f97822-3c1f-4546-8f3a-65a84e153572</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651">tw.local.customerFullDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3d943bb5-aec9-4b29-862f-735a93741afa</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="b9e39b54-e4d8-46bf-ac4d-21dea042c2aa" targetRef="f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d" name="To Exclusive Gateway" id="d3f97822-3c1f-4546-8f3a-65a84e153572">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.c7000e0a-e470-4afc-8166-751498a301e4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.87aa2738-7fc9-40d5-bb9d-d5edd6af761e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.14e3b87a-f23b-41e7-99de-61114a58f04c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.e9a6b446-52d8-4104-9164-16abca13d12f" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b9e39b54-e4d8-46bf-ac4d-21dea042c2aa" parallelMultiple="false" name="Error" id="1809e12e-cfb1-462e-8f47-fbbc91d7b2a9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2bb2d5c1-1063-43fc-860c-bcdbef72bf52</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8619da31-4cb7-4eb6-8fcf-0b4a7e97a4bb" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="bed1d728-981e-4b07-8f2f-ba53a36ee468" eventImplId="5a2933ec-c57f-4478-8fb4-89a1c0939c94">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="93183563-2d8d-4207-97c8-25f03656ad33" parallelMultiple="false" name="Error1" id="3e3f0d08-1957-4e36-8f72-d150c83784bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4a74dfe6-22b0-430f-8713-addf464315c7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3da048f5-a6e7-42a6-84eb-8f7f8eef1419" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ce1f2976-58b7-49e8-8d96-4a165cca9933" eventImplId="dc39de3a-dcd6-48b8-866d-930d3c7e0ccd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1809e12e-cfb1-462e-8f47-fbbc91d7b2a9" targetRef="2989270a-9626-432a-8e5b-250325c6d31b" name="To End Event" id="2bb2d5c1-1063-43fc-860c-bcdbef72bf52">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3e3f0d08-1957-4e36-8f72-d150c83784bc" targetRef="2989270a-9626-432a-8e5b-250325c6d31b" name="To End Event" id="4a74dfe6-22b0-430f-8713-addf464315c7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="464b588c-1f09-476f-872f-eedc2db9100b" name="Exclusive Gateway" id="f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="362" y="29" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d3f97822-3c1f-4546-8f3a-65a84e153572</ns16:incoming>
                        
                        
                        <ns16:outgoing>464b588c-1f09-476f-872f-eedc2db9100b</ns16:outgoing>
                        
                        
                        <ns16:outgoing>5d573b3a-c0c3-4c20-8d23-d361d9409869</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d" targetRef="93183563-2d8d-4207-97c8-25f03656ad33" name="To Set Customer" id="464b588c-1f09-476f-872f-eedc2db9100b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d" targetRef="2989270a-9626-432a-8e5b-250325c6d31b" name="To End Event" id="5d573b3a-c0c3-4c20-8d23-d361d9409869">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="2989270a-9626-432a-8e5b-250325c6d31b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="335" y="110" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5d573b3a-c0c3-4c20-8d23-d361d9409869</ns16:incoming>
                        
                        
                        <ns16:incoming>4a74dfe6-22b0-430f-8713-addf464315c7</ns16:incoming>
                        
                        
                        <ns16:incoming>2bb2d5c1-1063-43fc-860c-bcdbef72bf52</ns16:incoming>
                        
                        
                        <ns16:outgoing>d37a79b6-d018-4088-8ad5-ffd32735823b</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2989270a-9626-432a-8e5b-250325c6d31b" targetRef="33f03f01-7b57-42ce-b7d5-e7dd4d775804" name="To End" id="d37a79b6-d018-4088-8ad5-ffd32735823b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Customer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.464b588c-1f09-476f-872f-eedc2db9100b</processLinkId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.93183563-2d8d-4207-97c8-25f03656ad33</toProcessItemId>
            <guid>15a833c4-08d7-4b0b-a752-83b9080c170b</guid>
            <versionId>2e5cd469-aaad-4138-bfd5-c414fc13991e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</fromProcessItemId>
            <toProcessItemId>2025.93183563-2d8d-4207-97c8-25f03656ad33</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d37a79b6-d018-4088-8ad5-ffd32735823b</processLinkId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.33f03f01-7b57-42ce-b7d5-e7dd4d775804</toProcessItemId>
            <guid>77b232f6-7a9e-4bfb-9e1a-cae5b437ab09</guid>
            <versionId>7c580632-005f-498e-8813-8dc58b71163c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</fromProcessItemId>
            <toProcessItemId>2025.33f03f01-7b57-42ce-b7d5-e7dd4d775804</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d3f97822-3c1f-4546-8f3a-65a84e153572</processLinkId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</endStateId>
            <toProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</toProcessItemId>
            <guid>d508e2ef-e1d8-4b7a-98f0-bfe57ac8e621</guid>
            <versionId>e0ba82ed-257d-46bc-a405-b7fc62d97f21</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b9e39b54-e4d8-46bf-ac4d-21dea042c2aa</fromProcessItemId>
            <toProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5d573b3a-c0c3-4c20-8d23-d361d9409869</processLinkId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e77</endStateId>
            <toProcessItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</toProcessItemId>
            <guid>ae50e908-94e7-4c15-b1f2-6d2bba60b664</guid>
            <versionId>e83d5265-e291-4687-9f56-435f8abc4105</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.f30ca5b3-8e05-48cd-8ef8-6b3aeef18d1d</fromProcessItemId>
            <toProcessItemId>2025.2989270a-9626-432a-8e5b-250325c6d31b</toProcessItemId>
        </link>
        <link name="To MW_FC Retrieve Customer Details">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.48e017c4-2351-4892-8f35-c6664e0698e6</processLinkId>
            <processId>1.473ca24e-c03e-4a25-b37a-58cd047b0fff</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.93183563-2d8d-4207-97c8-25f03656ad33</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.33f03f01-7b57-42ce-b7d5-e7dd4d775804</toProcessItemId>
            <guid>3840e8d1-f21a-4124-95e1-f93286070fa0</guid>
            <versionId>e8f6bb2c-900f-4c7d-ad6c-d52b23dd74e1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.93183563-2d8d-4207-97c8-25f03656ad33</fromProcessItemId>
            <toProcessItemId>2025.33f03f01-7b57-42ce-b7d5-e7dd4d775804</toProcessItemId>
        </link>
    </process>
</teamworks>

