<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165" name="Get Center by branch code">
        <lastModified>1692530460371</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>4e11ec54-75e2-4577-b4aa-6368b1bc17bc</guid>
        <versionId>3d6f0975-26c1-481d-8b98-ef96abe9d20f</versionId>
        <dependencySummary>&lt;dependencySummary id="9e269e26-65d2-4be3-83dd-e0e4bfc5e1b5" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="BranchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cd1847f0-7862-4b2f-814c-daed841758b3</processParameterId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>eee043d8-9890-4bd0-9d0b-5c1c79012ee2</guid>
            <versionId>a42e0ad0-9d8a-4cd7-900c-eecf82c30605</versionId>
        </processParameter>
        <processParameter name="Center">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.108d6047-03b9-47d9-abfc-55c662ab0907</processParameterId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>70e51f9a-a604-4618-8496-b388d68b07f0</guid>
            <versionId>1fdcb6a0-af45-40d4-a879-d14700cd1091</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5da80be8-d64a-4a24-9780-ba997550aa3a</processParameterId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3bfbc3f9-6421-45bc-a908-d3b476c5c942</guid>
            <versionId>959c242b-f89f-4c99-9aad-c720a9e088e2</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.adf008ff-1d86-42c5-9372-cfb8cd3d06e4</processVariableId>
            <description isNull="true" />
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6c35f550-2cdc-4bb1-9eb2-f04ba06a9e45</guid>
            <versionId>868e4f4b-ae2e-441d-ac96-3918a663ac69</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6661c6cb-9dda-4012-9415-fed9d8684625</processVariableId>
            <description isNull="true" />
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88dedf07-618d-4f8d-87b0-17b450b3152a</guid>
            <versionId>3ad1d684-4a1a-4ff1-8dae-a09f0cd6f816</versionId>
        </processVariable>
        <processVariable name="query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.de4b247f-0698-4411-8143-b7733f7d6cf1</processVariableId>
            <description isNull="true" />
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>*************-4cc2-87ca-fc0deaed801e</guid>
            <versionId>a05d754e-36e8-44bf-8638-9162f5b17247</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4518e410-e5c9-47f3-ad69-47d2ccdb0ec5</processVariableId>
            <description isNull="true" />
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3b6dba5a-ad20-4bc5-aded-52f4da56d489</guid>
            <versionId>9ef3c13a-26a8-4cc3-bc71-944260c6fbf0</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.75fa59c3-22b9-4416-8311-3e21dbcc5eb1</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>Results!</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ecc937bf-59d5-4152-8191-48bdab0418bf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b07</guid>
            <versionId>3522c686-0541-48d0-8eb5-046fafc0e663</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="466" y="55">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ecc937bf-59d5-4152-8191-48bdab0418bf</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.results!=null &amp;&amp; tw.local.results.listLength&gt;0){&#xD;
	&#xD;
&#xD;
	var count =0;&#xD;
&#xD;
	for(var index=0;index&lt;tw.local.results.listLength ; index++){&#xD;
		if(tw.local.results[index].rows!=null){&#xD;
			for( var rowIndex=0;rowIndex&lt; tw.local.results[index].rows.listLength;rowIndex++){&#xD;
				if(tw.local.results[index].rows[rowIndex].indexedMap!=null){&#xD;
&#xD;
					tw.local.Center = {};&#xD;
					tw.local.Center.name=tw.local.results[index].rows[rowIndex].indexedMap["SME_CENTER_NAME"];&#xD;
					tw.local.Center.value=tw.local.results[index].rows[rowIndex].indexedMap["SME_CENTER_CODE"];					    &#xD;
					count++;&#xD;
					}&#xD;
				}&#xD;
			}&#xD;
	}&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>2b170891-9e33-48a6-a634-bf7f66cd2b55</guid>
                <versionId>7e98d263-6a74-48a7-afd8-4252bd0ceed5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.02ccfd8e-5753-497a-98d1-0796c93e2685</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>SQL Execute statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.21cf7497-5e60-49f0-9a61-121d6f0f1230</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b09</guid>
            <versionId>*************-4b31-81bd-e933d11333bd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="322" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0c</errorHandlerItem>
                <errorHandlerItemId>2025.21cf7497-5e60-49f0-9a61-121d6f0f1230</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>b3d2bb72-b591-496d-b6f0-63f7481a8c1d</guid>
                <versionId>2774c94e-6851-432e-9c08-4506a5d5c7fc</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dbca6c78-5386-430f-bb2b-226da98ba064</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>acb0d89c-0320-48ca-a50e-857489829c9e</guid>
                    <versionId>108e22af-17a3-4338-afa7-0d29dce23a33</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7a37d096-ad93-477e-9ab6-bef99329c828</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fb83066f-b4cb-4355-8fb5-f67787c4a61a</guid>
                    <versionId>18e22b7c-8757-4024-b9bf-3bfe95eddf47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7e7bb22b-6e82-4c1b-8634-e674d7b1887f</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fd4d00a9-a884-4135-9018-18481722d89d</guid>
                    <versionId>5ee8b7de-5bbc-4a3c-b8ca-62f8ffd72669</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fdcd9fab-796f-4c81-9580-ac7e1cfc3fd4</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>57610f75-fa90-44db-ba94-908afc994c92</guid>
                    <versionId>c07687a3-30f9-4c3f-9f6b-34a47b3118c9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4b93aac0-2e2b-48f1-beba-1907fec530e4</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fe66bb8e-94bc-41e2-ae83-5289221e6c1c</guid>
                    <versionId>e452f93b-462b-4712-b6b7-80453fd4940a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d08b9164-bce3-4b38-ac3a-1a41f6ab0bfa</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.e4b2046f-fd25-4d68-b6c8-8382de8d1d59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_APP</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>436017da-7c72-4e40-9684-d67bb64ccc7d</guid>
                    <versionId>fa58b7f1-610f-4b31-bf67-08774175251c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b1f54df1-282e-4fd8-bb36-ad4e84977c26</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.0a6f5846-d346-4a16-95a4-0d397b937cd4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b08</guid>
            <versionId>390ccfb8-788c-4b4b-bbb8-b6938c21ab20</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="661" y="201">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.0a6f5846-d346-4a16-95a4-0d397b937cd4</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>89b93d7f-2548-449b-a1b3-2c036ff2c735</guid>
                <versionId>9df4caa7-2801-44ac-9602-b59ec796bcd9</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.99a6e314-25ea-41ee-9c84-191d47094725</parameterMappingId>
                    <processParameterId>2055.5da80be8-d64a-4a24-9780-ba997550aa3a</processParameterId>
                    <parameterMappingParentId>3007.0a6f5846-d346-4a16-95a4-0d397b937cd4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ed00317e-1c36-4910-9044-4ae4597e22f1</guid>
                    <versionId>c078418c-4a8f-4c22-b306-0de68f95c03c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.267fa09c-8079-47f9-8c6a-1e9242d19161</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5b1f12bd-9a3a-42f5-894c-576bdc9dab32</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0b</guid>
            <versionId>5716388f-1ae9-41a5-9d3d-13ebbe35ab13</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.114b6d98-9db3-4da6-9406-097edf33492e</processItemPrePostId>
                <processItemId>2025.267fa09c-8079-47f9-8c6a-1e9242d19161</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>7872b10d-5e08-4585-bc3c-cc4dfc6b3550</guid>
                <versionId>17fc7b7c-e671-43cf-acab-dc75cc82e9df</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ecfb7992-9d41-417c-8bd2-cbd1c410b32d</processItemPrePostId>
                <processItemId>2025.267fa09c-8079-47f9-8c6a-1e9242d19161</processItemId>
                <location>2</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Get Center by branch code : END");</script>
                <guid>fc226c48-8f44-4c95-9352-770497409eac</guid>
                <versionId>8fdcfa80-545b-4068-8f7c-acd13f2831a6</versionId>
            </processPrePosts>
            <layoutData x="650" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5b1f12bd-9a3a-42f5-894c-576bdc9dab32</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>16fec40c-5b28-4190-b39a-e74106fa3eb1</guid>
                <versionId>da3df778-fee0-40b1-b44d-130003b060b7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>Init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ef228e40-5ad7-48c4-ad87-7bcac6956171</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0a</guid>
            <versionId>6556c94b-70f1-4cf5-959f-1f143810accf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.a7998513-8eaa-4e52-a8fa-a0005673b595</processItemPrePostId>
                <processItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</processItemId>
                <location>1</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Get Center by branch code : START");</script>
                <guid>139e9da2-f1df-499c-be16-ea747d79765c</guid>
                <versionId>3d4ab8ce-6bca-4fcb-b1cf-e87d605b0c66</versionId>
            </processPrePosts>
            <layoutData x="125" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ef228e40-5ad7-48c4-ad87-7bcac6956171</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.query= new tw.object.SQLStatement();&#xD;
&#xD;
tw.local.query.sql="select SME_CENTER_CODE,SME_CENTER_NAME from BPM.SME_Centers_MAPPING where  LG_BRANCH_CODE = ? ;"&#xD;
 &#xD;
tw.local.query.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.query.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[0].mode="IN";&#xD;
tw.local.query.parameters[0].value=tw.local.BranchCode;
</script>
                <isRule>false</isRule>
                <guid>d2fd909e-57b5-4a72-8301-d963dc27e28c</guid>
                <versionId>c6a83463-3efb-4b20-a33d-aa68ee4f8730</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.21cf7497-5e60-49f0-9a61-121d6f0f1230</processItemId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <name>log Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2fdce438-7529-4e53-967e-b9b44d3baf4b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0c</guid>
            <versionId>e5aa683f-695b-4c60-b659-cf2e828fff0f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="443" y="177">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2fdce438-7529-4e53-967e-b9b44d3baf4b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Get Center by branch code -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Center by branch code -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</script>
                <isRule>false</isRule>
                <guid>0a875a6c-d23e-4a41-9404-98dba66d9edb</guid>
                <versionId>5f38bd50-4f77-4e3f-aa0c-52cbb9542179</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Center by branch code" id="1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="BranchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.cd1847f0-7862-4b2f-814c-daed841758b3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"011"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="Center" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" id="2055.108d6047-03b9-47d9-abfc-55c662ab0907" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.cd1847f0-7862-4b2f-814c-daed841758b3</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.108d6047-03b9-47d9-abfc-55c662ab0907</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="27f8d1b2-df34-454e-8bf7-e0c398bec196">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="b453fc2e-3f4f-4c2f-a49b-5c7ff52a5f88" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>c700df08-2673-4679-a9ef-9e170b9649df</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>267fa09c-8079-47f9-8c6a-1e9242d19161</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d192b3b7-9de7-45dd-9c90-ac421207d56d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>02ccfd8e-5753-497a-98d1-0796c93e2685</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>75fa59c3-22b9-4416-8311-3e21dbcc5eb1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c5fe376a-bef0-4f7a-85c5-ea794efd6bfe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21cf7497-5e60-49f0-9a61-121d6f0f1230</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b1f54df1-282e-4fd8-bb36-ad4e84977c26</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="c700df08-2673-4679-a9ef-9e170b9649df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="79" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.56680980-ec3a-462a-80b1-b6d3eae99bf7</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="267fa09c-8079-47f9-8c6a-1e9242d19161">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="79" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0b</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Get Center by branch code : END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bff73c0c-34cb-4152-8f12-7f5195417e60</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.adf008ff-1d86-42c5-9372-cfb8cd3d06e4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="results" id="2056.6661c6cb-9dda-4012-9415-fed9d8684625" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Results!" id="75fa59c3-22b9-4416-8311-3e21dbcc5eb1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="466" y="55" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8b9c64f0-f51e-4908-b47a-efc48b967425</ns16:incoming>
                        
                        
                        <ns16:outgoing>bff73c0c-34cb-4152-8f12-7f5195417e60</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.results!=null &amp;&amp; tw.local.results.listLength&gt;0){&#xD;
	&#xD;
&#xD;
	var count =0;&#xD;
&#xD;
	for(var index=0;index&lt;tw.local.results.listLength ; index++){&#xD;
		if(tw.local.results[index].rows!=null){&#xD;
			for( var rowIndex=0;rowIndex&lt; tw.local.results[index].rows.listLength;rowIndex++){&#xD;
				if(tw.local.results[index].rows[rowIndex].indexedMap!=null){&#xD;
&#xD;
					tw.local.Center = {};&#xD;
					tw.local.Center.name=tw.local.results[index].rows[rowIndex].indexedMap["SME_CENTER_NAME"];&#xD;
					tw.local.Center.value=tw.local.results[index].rows[rowIndex].indexedMap["SME_CENTER_CODE"];					    &#xD;
					count++;&#xD;
					}&#xD;
				}&#xD;
			}&#xD;
	}&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute statement" id="02ccfd8e-5753-497a-98d1-0796c93e2685">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="322" y="56" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6019c1ed-99bb-439c-abba-6c2ad95ef9dd</ns16:incoming>
                        
                        
                        <ns16:outgoing>8b9c64f0-f51e-4908-b47a-efc48b967425</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_APP</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.query.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.query.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Init" id="d192b3b7-9de7-45dd-9c90-ac421207d56d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="125" y="56" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Get Center by branch code : START");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.56680980-ec3a-462a-80b1-b6d3eae99bf7</ns16:incoming>
                        
                        
                        <ns16:outgoing>6019c1ed-99bb-439c-abba-6c2ad95ef9dd</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.query= new tw.object.SQLStatement();&#xD;
&#xD;
tw.local.query.sql="select SME_CENTER_CODE,SME_CENTER_NAME from BPM.SME_Centers_MAPPING where  LG_BRANCH_CODE = ? ;"&#xD;
 &#xD;
tw.local.query.parameters= new tw.object.listOf.SQLParameter();&#xD;
tw.local.query.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.query.parameters[0].mode="IN";&#xD;
tw.local.query.parameters[0].value=tw.local.BranchCode;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="75fa59c3-22b9-4416-8311-3e21dbcc5eb1" targetRef="267fa09c-8079-47f9-8c6a-1e9242d19161" name="To End" id="bff73c0c-34cb-4152-8f12-7f5195417e60">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="02ccfd8e-5753-497a-98d1-0796c93e2685" targetRef="75fa59c3-22b9-4416-8311-3e21dbcc5eb1" name="To Results!" id="8b9c64f0-f51e-4908-b47a-efc48b967425">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d192b3b7-9de7-45dd-9c90-ac421207d56d" targetRef="02ccfd8e-5753-497a-98d1-0796c93e2685" name="To SQL Execute statement" id="6019c1ed-99bb-439c-abba-6c2ad95ef9dd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c700df08-2673-4679-a9ef-9e170b9649df" targetRef="d192b3b7-9de7-45dd-9c90-ac421207d56d" name="To End" id="2027.56680980-ec3a-462a-80b1-b6d3eae99bf7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="false" name="query" id="2056.de4b247f-0698-4411-8143-b7733f7d6cf1" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="02ccfd8e-5753-497a-98d1-0796c93e2685" parallelMultiple="false" name="Error" id="c5fe376a-bef0-4f7a-85c5-ea794efd6bfe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="357" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e5606ee4-369a-42ed-8550-cca9dfcd8aef</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="57aa591b-4714-4302-9513-074552c42183" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ac26d2ff-2a92-458b-9c81-733d5dc3891e" eventImplId="ec6db6c1-1ab5-4b1b-8729-2683f3ab96bc">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="log Error" id="21cf7497-5e60-49f0-9a61-121d6f0f1230">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="443" y="177" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e5606ee4-369a-42ed-8550-cca9dfcd8aef</ns16:incoming>
                        
                        
                        <ns16:outgoing>8bc7cb6b-a2c5-4ee7-8f9d-7317ca646899</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Get Center by branch code -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Center by branch code -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c5fe376a-bef0-4f7a-85c5-ea794efd6bfe" targetRef="21cf7497-5e60-49f0-9a61-121d6f0f1230" name="To log Error" id="e5606ee4-369a-42ed-8550-cca9dfcd8aef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="b1f54df1-282e-4fd8-bb36-ad4e84977c26">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="661" y="201" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8bc7cb6b-a2c5-4ee7-8f9d-7317ca646899</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="d1ab74e2-1cd9-45e4-a5ec-aed60b6c5b18" eventImplId="44805f73-875c-427e-82ae-9502e29eb84e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="21cf7497-5e60-49f0-9a61-121d6f0f1230" targetRef="b1f54df1-282e-4fd8-bb36-ad4e84977c26" name="To End Event" id="8bc7cb6b-a2c5-4ee7-8f9d-7317ca646899">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.4518e410-e5c9-47f3-ad69-47d2ccdb0ec5" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To SQL Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.15357f1a-c386-48b2-a577-1a945aeb60ca</processLinkId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.02ccfd8e-5753-497a-98d1-0796c93e2685</toProcessItemId>
            <guid>e7161159-ad74-48b0-9611-b14017a69907</guid>
            <versionId>08ac37a3-5e98-4ef2-acba-dd43d07f7130</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d192b3b7-9de7-45dd-9c90-ac421207d56d</fromProcessItemId>
            <toProcessItemId>2025.02ccfd8e-5753-497a-98d1-0796c93e2685</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.80427a27-c84b-4128-bcf2-5c3cf1d59533</processLinkId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.21cf7497-5e60-49f0-9a61-121d6f0f1230</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b1f54df1-282e-4fd8-bb36-ad4e84977c26</toProcessItemId>
            <guid>397d9c5d-9281-48a5-a1dd-35a0c32dd60e</guid>
            <versionId>1f2b8997-9208-462c-87d5-49582b287352</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.21cf7497-5e60-49f0-9a61-121d6f0f1230</fromProcessItemId>
            <toProcessItemId>2025.b1f54df1-282e-4fd8-bb36-ad4e84977c26</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5d54356c-7dc2-4f2a-a1d7-63c7b62a3c80</processLinkId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.75fa59c3-22b9-4416-8311-3e21dbcc5eb1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.267fa09c-8079-47f9-8c6a-1e9242d19161</toProcessItemId>
            <guid>01485630-9f63-47c4-be82-409da72468c5</guid>
            <versionId>4cd42235-c977-4fa9-9ca6-73c439cfafba</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.75fa59c3-22b9-4416-8311-3e21dbcc5eb1</fromProcessItemId>
            <toProcessItemId>2025.267fa09c-8079-47f9-8c6a-1e9242d19161</toProcessItemId>
        </link>
        <link name="To Results!">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5b46ad4c-d97c-4828-932c-70fc47580f6c</processLinkId>
            <processId>1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.02ccfd8e-5753-497a-98d1-0796c93e2685</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.75fa59c3-22b9-4416-8311-3e21dbcc5eb1</toProcessItemId>
            <guid>*************-4f70-93b3-1ac65c81e339</guid>
            <versionId>9dc64ebd-e865-460d-b7ed-ff5b56ce3e99</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.02ccfd8e-5753-497a-98d1-0796c93e2685</fromProcessItemId>
            <toProcessItemId>2025.75fa59c3-22b9-4416-8311-3e21dbcc5eb1</toProcessItemId>
        </link>
    </process>
</teamworks>

