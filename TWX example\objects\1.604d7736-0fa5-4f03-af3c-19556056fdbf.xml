<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.604d7736-0fa5-4f03-af3c-19556056fdbf" name="update facility">
        <lastModified>1692723270906</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.931926ca-f296-4b22-81df-63a0fe3ac77a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-63c9</guid>
        <versionId>0fb863f6-fe34-4648-9789-a8058a27dc3e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-6377" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"4215e47d-b0db-4267-8aa6-bf10307b09f4"},{"incoming":["8f3861e3-0ae0-4a4a-8a82-723e32b02945"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-63c7"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"2699a2d8-e1fe-41ee-87ab-f1215bb949db"},{"targetRef":"931926ca-f296-4b22-81df-63a0fe3ac77a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To INIT SQL","declaredType":"sequenceFlow","id":"2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0","sourceRef":"4215e47d-b0db-4267-8aa6-bf10307b09f4"},{"startQuantity":1,"outgoing":["334a6ef9-9791-4cb5-87b8-bec6f3d1d547"],"incoming":["2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":189,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"INIT SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"931926ca-f296-4b22-81df-63a0fe3ac77a","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\ntw.local.sqlStatements[0] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[0].parameters[0] = {};\r\ntw.local.sqlStatements[0].parameters[0].value = tw.local.DBID;\r\ntw.local.sqlStatements[0].sql = \"DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?\"\r\n\/\/--------------------------------------------------------------------------------------------------\r\nvar i = 0\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\nif (tw.local.idcContract.facilities.listLength &gt; 0) {\r\n\tj++;\r\n\tfor (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {\r\n\t\tfor (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {\r\n\t\t\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\t\t\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;\";\r\n\t\t\t\r\n\t\t\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\t\t\ti = 0;\r\n\t\t\tparamInit(\"INTEGER\",tw.local.DBID);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityID);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);\r\n\t\t\tparamInit(\"DECIMAL\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);\r\n\t\t\tj++;\r\n\t\t}\r\n\t}\r\n}"]}},{"targetRef":"6b0cebe3-2245-4423-8e48-712ea0a968b9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"334a6ef9-9791-4cb5-87b8-bec6f3d1d547","sourceRef":"931926ca-f296-4b22-81df-63a0fe3ac77a"},{"startQuantity":1,"outgoing":["8f3861e3-0ae0-4a4a-8a82-723e32b02945"],"incoming":["334a6ef9-9791-4cb5-87b8-bec6f3d1d547"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":340,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6b0cebe3-2245-4423-8e48-712ea0a968b9","calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"2699a2d8-e1fe-41ee-87ab-f1215bb949db","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8f3861e3-0ae0-4a4a-8a82-723e32b02945","sourceRef":"6b0cebe3-2245-4423-8e48-712ea0a968b9"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.0a30fcd5-6328-4ec6-842b-5f5aaa78f81a"}],"laneSet":[{"id":"ca9afad3-9076-40ec-8398-2cf2bc5d221f","lane":[{"flowNodeRef":["4215e47d-b0db-4267-8aa6-bf10307b09f4","2699a2d8-e1fe-41ee-87ab-f1215bb949db","931926ca-f296-4b22-81df-63a0fe3ac77a","6b0cebe3-2245-4423-8e48-712ea0a968b9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"19f9410c-50af-4fea-8dce-73e9c11e0458","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"update facility","declaredType":"process","id":"1.604d7736-0fa5-4f03-af3c-19556056fdbf","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"inputSet":[{"dataInputRefs":["2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc","2055.264def96-e086-437f-8ebe-4af040a4863d"]}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"121"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"DBID","isCollection":false,"id":"2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.IDCContract();\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();\nautoObject.settlementAccounts[0] = new tw.object.SettlementAccount();\nautoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\nautoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = new tw.object.DBLookup();\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = new tw.object.listOf.Parties();\nautoObject.party[0] = new tw.object.Parties();\nautoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = new tw.object.LiquidationSummary();\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new TWDate();\nautoObject.liquidationSummary.creditValueDate = new TWDate();\nautoObject.IDCProduct = new tw.object.DBLookup();\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new TWDate();\nautoObject.transactionMaturityDate = new TWDate();\nautoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();\nautoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new TWDate();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = new tw.object.DBLookup();\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = new tw.object.SwiftMessageData();\nautoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = new tw.object.listOf.ContractAdvice();\nautoObject.advices[0] = new tw.object.ContractAdvice();\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\nautoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new TWDate();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();\nautoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();\nautoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new TWDate();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\nautoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\nautoObject.facilities[0].facilityCode = \"\";\nautoObject.facilities[0].overallLimit = 0.0;\nautoObject.facilities[0].limitAmount = 0.0;\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\nautoObject.facilities[0].availableAmount = 0.0;\nautoObject.facilities[0].expiryDate = new TWDate();\nautoObject.facilities[0].availableFlag = false;\nautoObject.facilities[0].authorizedFlag = false;\nautoObject.facilities[0].Utilization = 0.0;\nautoObject.facilities[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\nautoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();\nautoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines[0].LGCommission = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\nautoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\nautoObject.facilities[0].status = \"\";\nautoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.facilities[0].facilityCurrency.name = \"\";\nautoObject.facilities[0].facilityCurrency.value = \"\";\nautoObject.facilities[0].facilityID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.264def96-e086-437f-8ebe-4af040a4863d"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="DBID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc</processParameterId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>121</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>72bbe40a-c8b9-454c-8c92-5ccb48000c1f</guid>
            <versionId>c5d3a19a-af05-44ce-9a23-3cfdd5fd301e</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.264def96-e086-437f-8ebe-4af040a4863d</processParameterId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.IDCContract();
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();
autoObject.settlementAccounts[0] = new tw.object.SettlementAccount();
autoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = new tw.object.DBLookup();
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = new tw.object.listOf.Parties();
autoObject.party[0] = new tw.object.Parties();
autoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = new tw.object.LiquidationSummary();
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new TWDate();
autoObject.liquidationSummary.creditValueDate = new TWDate();
autoObject.IDCProduct = new tw.object.DBLookup();
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new TWDate();
autoObject.transactionMaturityDate = new TWDate();
autoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new TWDate();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = new tw.object.DBLookup();
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = new tw.object.SwiftMessageData();
autoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = new tw.object.listOf.ContractAdvice();
autoObject.advices[0] = new tw.object.ContractAdvice();
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();
autoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new TWDate();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();
autoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();
autoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new TWDate();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new TWDate();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();
autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1aa14229-e4bd-4f52-ba36-26cb8f0e31a8</guid>
            <versionId>763a3777-9bf9-4e0e-bcd4-887744727d9e</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0a30fcd5-6328-4ec6-842b-5f5aaa78f81a</processVariableId>
            <description isNull="true" />
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>17a62d79-9d73-48b2-b57a-608599a16251</guid>
            <versionId>51b12c04-f900-48ae-b88b-4354dbae1b0d</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6b0cebe3-2245-4423-8e48-712ea0a968b9</processItemId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3cd88ae0-1948-4092-8323-9d4c40980c5b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-639e</guid>
            <versionId>3f653463-2a51-4ee6-9c1f-f4f1300b9e4e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="340" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3cd88ae0-1948-4092-8323-9d4c40980c5b</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>9d2972f1-4db6-4210-8c34-369e0d800301</guid>
                <versionId>c64ff833-ee69-406a-ab1c-b93c969d62e9</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.38d29896-179a-47ed-9bab-541d658f1119</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.3cd88ae0-1948-4092-8323-9d4c40980c5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>91b8c5d4-1a21-4028-8088-41faf57f236f</guid>
                    <versionId>05e14318-f5e9-4def-afbc-be67c5b1926e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.249937d0-72f0-45fd-961d-f263772c1e70</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.3cd88ae0-1948-4092-8323-9d4c40980c5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>0d663bb5-ee34-46ef-9cd2-f3f0f7b68d81</guid>
                    <versionId>0b6f7900-b1b9-4620-91c2-1f8d7b0a0612</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.98a7a056-bc90-44dc-85f1-d9696bedcb83</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.3cd88ae0-1948-4092-8323-9d4c40980c5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>f7600a5d-dd8c-45dc-9f7d-f5dfc4796daa</guid>
                    <versionId>9f376e8d-bdbd-4c23-a0c6-f808aaea5d14</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.931926ca-f296-4b22-81df-63a0fe3ac77a</processItemId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <name>INIT SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.68b972d0-8ff4-4a2c-9fdc-99e9bf42ed89</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-63a4</guid>
            <versionId>bdb1ffde-c55c-4ad2-b38c-cee1670b2189</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="189" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.68b972d0-8ff4-4a2c-9fdc-99e9bf42ed89</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[0].parameters[0] = {};&#xD;
tw.local.sqlStatements[0].parameters[0].value = tw.local.DBID;&#xD;
tw.local.sqlStatements[0].sql = "DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?"&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
var i = 0&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
	j++;&#xD;
	for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
		for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
			tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
			tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
			&#xD;
			tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
			i = 0;&#xD;
			paramInit("INTEGER",tw.local.DBID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
			paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
			j++;&#xD;
		}&#xD;
	}&#xD;
}</script>
                <isRule>false</isRule>
                <guid>b43ebec4-f780-472f-92b5-aaff2847d706</guid>
                <versionId>3b12a95c-ac50-4cab-8d99-9d247ce7be27</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2699a2d8-e1fe-41ee-87ab-f1215bb949db</processItemId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0b732816-f1cb-42a0-9bc2-21899d2cd806</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-63c7</guid>
            <versionId>c727c83b-9c94-474b-9cbd-abd51f430b15</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0b732816-f1cb-42a0-9bc2-21899d2cd806</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>c5514d25-927b-471a-86ac-ae74b06ea987</guid>
                <versionId>5c9732ba-5290-4231-86a2-2d1fe248cda3</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.931926ca-f296-4b22-81df-63a0fe3ac77a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="update facility" id="1.604d7736-0fa5-4f03-af3c-19556056fdbf" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="DBID" itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" id="2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">121</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.264def96-e086-437f-8ebe-4af040a4863d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.IDCContract();
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();
autoObject.settlementAccounts[0] = new tw.object.SettlementAccount();
autoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = new tw.object.DBLookup();
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = new tw.object.listOf.Parties();
autoObject.party[0] = new tw.object.Parties();
autoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = new tw.object.LiquidationSummary();
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new TWDate();
autoObject.liquidationSummary.creditValueDate = new TWDate();
autoObject.IDCProduct = new tw.object.DBLookup();
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new TWDate();
autoObject.transactionMaturityDate = new TWDate();
autoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new TWDate();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = new tw.object.DBLookup();
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = new tw.object.SwiftMessageData();
autoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = new tw.object.listOf.ContractAdvice();
autoObject.advices[0] = new tw.object.ContractAdvice();
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();
autoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new TWDate();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();
autoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();
autoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new TWDate();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new TWDate();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();
autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.76956dc1-0f9c-4cf2-8bb9-6285ca655ffc</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.264def96-e086-437f-8ebe-4af040a4863d</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="ca9afad3-9076-40ec-8398-2cf2bc5d221f">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="19f9410c-50af-4fea-8dce-73e9c11e0458" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>4215e47d-b0db-4267-8aa6-bf10307b09f4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2699a2d8-e1fe-41ee-87ab-f1215bb949db</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>931926ca-f296-4b22-81df-63a0fe3ac77a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6b0cebe3-2245-4423-8e48-712ea0a968b9</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="4215e47d-b0db-4267-8aa6-bf10307b09f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="2699a2d8-e1fe-41ee-87ab-f1215bb949db">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-63c7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8f3861e3-0ae0-4a4a-8a82-723e32b02945</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="4215e47d-b0db-4267-8aa6-bf10307b09f4" targetRef="931926ca-f296-4b22-81df-63a0fe3ac77a" name="To INIT SQL" id="2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="INIT SQL" id="931926ca-f296-4b22-81df-63a0fe3ac77a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="189" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.86bf7d15-57b0-4f15-8c58-ff7a5baa92c0</ns16:incoming>
                        
                        
                        <ns16:outgoing>334a6ef9-9791-4cb5-87b8-bec6f3d1d547</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[0].parameters[0] = {};&#xD;
tw.local.sqlStatements[0].parameters[0].value = tw.local.DBID;&#xD;
tw.local.sqlStatements[0].sql = "DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?"&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
var i = 0&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
	j++;&#xD;
	for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
		for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
			tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
			tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
			&#xD;
			tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
			i = 0;&#xD;
			paramInit("INTEGER",tw.local.DBID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
			paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
			j++;&#xD;
		}&#xD;
	}&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="931926ca-f296-4b22-81df-63a0fe3ac77a" targetRef="6b0cebe3-2245-4423-8e48-712ea0a968b9" name="To SQL Execute Multiple Statements (SQLResult)" id="334a6ef9-9791-4cb5-87b8-bec6f3d1d547">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="SQL Execute Multiple Statements (SQLResult)" id="6b0cebe3-2245-4423-8e48-712ea0a968b9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="340" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>334a6ef9-9791-4cb5-87b8-bec6f3d1d547</ns16:incoming>
                        
                        
                        <ns16:outgoing>8f3861e3-0ae0-4a4a-8a82-723e32b02945</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6b0cebe3-2245-4423-8e48-712ea0a968b9" targetRef="2699a2d8-e1fe-41ee-87ab-f1215bb949db" name="To End" id="8f3861e3-0ae0-4a4a-8a82-723e32b02945">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.0a30fcd5-6328-4ec6-842b-5f5aaa78f81a" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8f3861e3-0ae0-4a4a-8a82-723e32b02945</processLinkId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6b0cebe3-2245-4423-8e48-712ea0a968b9</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.2699a2d8-e1fe-41ee-87ab-f1215bb949db</toProcessItemId>
            <guid>837505a1-d788-4ae8-b58a-19e37dbc65e7</guid>
            <versionId>0945b489-8d9a-49f7-91a4-513fae026437</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6b0cebe3-2245-4423-8e48-712ea0a968b9</fromProcessItemId>
            <toProcessItemId>2025.2699a2d8-e1fe-41ee-87ab-f1215bb949db</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.334a6ef9-9791-4cb5-87b8-bec6f3d1d547</processLinkId>
            <processId>1.604d7736-0fa5-4f03-af3c-19556056fdbf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.931926ca-f296-4b22-81df-63a0fe3ac77a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6b0cebe3-2245-4423-8e48-712ea0a968b9</toProcessItemId>
            <guid>889b4d9b-68e6-44da-9d14-501b44c357c0</guid>
            <versionId>15aa3a68-0506-4745-a8c4-a687702d05d1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.931926ca-f296-4b22-81df-63a0fe3ac77a</fromProcessItemId>
            <toProcessItemId>2025.6b0cebe3-2245-4423-8e48-712ea0a968b9</toProcessItemId>
        </link>
    </process>
</teamworks>

