<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51" name="Trade Compliance">
        <lastModified>1692698128648</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.6f5f8a8d-823b-4550-ac14-f9f140b519b8</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>26de9b35-78f6-4ba0-93d1-050076049be2</guid>
        <versionId>5a364f9c-7622-48fb-8573-30f0a640056e</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.4befb0c1-fe91-4740-b208-86f0559bebc5"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":188,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f564c6cb-e29a-4db2-8940-58fb27a0354b"},{"incoming":["2027.800f8609-1798-415f-a6df-83143ffb0dbf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":974,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"5c2e7983-2e9c-4879-9a52-54ddfe3ec121"},{"targetRef":"2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Start To Coach","declaredType":"sequenceFlow","id":"2027.4befb0c1-fe91-4740-b208-86f0559bebc5","sourceRef":"f564c6cb-e29a-4db2-8940-58fb27a0354b"},{"startQuantity":1,"outgoing":["2027.75b5270b-a7e2-442d-b332-e2a5864c362e"],"incoming":["2027.0beb42e2-d254-423c-95cc-055e15e51fb5"],"default":"2027.75b5270b-a7e2-442d-b332-e2a5864c362e","extensionElements":{"nodeVisualInfo":[{"width":95,"x":243,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.f020b216-f0ff-4855-aa94-271718469d56","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\ntw.local.errorVIS = \"NONE\";\r\ntw.local.action = [];\r\ntw.local.action[0] = tw.epv.Action.returnToInitiator;\r\ntw.local.action[1] = tw.epv.Action.approveRequest; \r\ntw.local.complianceComments = tw.local.idcRequest.appLog;"]}},{"startQuantity":1,"outgoing":["2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4"],"incoming":["2027.2757e1fa-b57e-4069-861b-a7be7eec859c"],"default":"2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4","extensionElements":{"nodeVisualInfo":[{"width":95,"x":630,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a87ed110-f284-4d08-9da9-5a40e8707d1d","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.intiator == \"fo\"){\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;\r\n}else if (tw.local.intiator == \"Initiation\") {\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;\r\n}else{\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;\r\n}\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Trade Compliance","declaredType":"sequenceFlow","id":"2027.75b5270b-a7e2-442d-b332-e2a5864c362e","sourceRef":"2025.f020b216-f0ff-4855-aa94-271718469d56"},{"targetRef":"2025.0a1c4b7d-1274-414b-9ec2-951408429f15","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4","sourceRef":"2025.a87ed110-f284-4d08-9da9-5a40e8707d1d"},{"outgoing":["2027.96ed7946-fc15-4a81-ad1c-c0808078ad31","2027.6fc60642-c337-4606-a6da-23414e447162"],"incoming":["2027.75b5270b-a7e2-442d-b332-e2a5864c362e","2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633","2027.74a71ea6-a35d-433b-8926-367bc257a633"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":406,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3608991d-5008-416a-8681-b3c10833dfbd","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"63ff07da-b122-41b5-82e8-20a081544b65","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2c591a2-b2f4-47c2-8eb5-78dd0a150b6c","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"56d065cc-**************-69c1dff368d3","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f9bb01dd-ad67-4897-80e5-1f6823658edd","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d14a4d6-0541-4251-85db-c7bb0fb5f3de","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d5bca2d8-54b0-4ed5-8b81-b3d5d378d1d8","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8a46a3de-61d4-4cb3-8b26-c1616004a6b5","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"233f9430-a8a4-4dd7-8fc1-2528eb2722b8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"665b0725-ae5b-4646-8873-0c208bcfadfb","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"15252763-e630-4826-81ba-94e576322c23","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"46da2285-bc81-48a5-8bf5-74e3df36c3d0","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"58e2f721-57b6-4c05-8383-0a033afef2d8","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f9d1579c-9508-46ab-8756-d977b23e59e0","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"42278afa-f9c5-41a6-8054-1d575832f563","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73a48077-b23c-4133-890d-6b112d36cbac","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a7fc7787-3503-477f-8c38-3cfdaa5d82e6","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ed34154-7d4a-41fd-8d8a-30fc694e7c73","optionName":"havePaymentTerm","value":"no"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c4f6031-0046-446a-8a0c-9d6cde3b0c68","optionName":"hasWithdraw","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6dc3052d-254a-440e-85a5-97ba640c33a9","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4c71940d-4e72-4eab-8ea8-61397c332b59","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ade3d8d6-6e64-4611-840a-814fef43edca","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6bb1b10c-03b5-4c82-87ca-507f68c66479","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c2ac03f7-4f36-4687-878d-8eef4ce33ede","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fe769dea-4204-41e1-85a0-1773d150792d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"92e2d68c-fa2c-4d73-8a98-9204847b9629","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"083afada-b0a0-42e1-80d9-76d0a738198e","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1842f0a0-0dd1-4c97-86aa-62ceb2e3088b","optionName":"advancePaymentsUsedOption","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7ee24b08-ed1b-4b81-89c1-d6d4aa3d897d","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ab0419f1-3e34-4c7e-8cb4-b5532249f041","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"39d4d8c3-0e97-4a35-83e2-aec9018d6ab9","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f91f2f89-fede-4c46-8bd7-4878cfc27415","optionName":"accountsList","value":"tw.local.accountsList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1293486f-5a9b-44ad-834a-27f6bb512ea5","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dc7a475c-5deb-4d9d-88ff-b0b21024b12a","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"08f73b80-9ceb-4108-881c-ab783f0ca917","optionName":"tmpUsedAdvancePayment","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"13c5e409-6897-4c88-8765-d8385b4db6a9","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0c97aa1e-8dbe-4fd6-8cbf-0c3b01142e20","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d9a14531-47c2-4a15-8b20-a0ea3363e33f","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f4ff2f30-6c7e-41b7-8887-b440be1f7ad5","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f10e7df3-3808-476b-8776-d27cce502202","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6750a3fa-c7be-4683-82ed-090471ddf529","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"efa4b579-e6d2-44f1-8527-42587b87d570","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59d6de0e-dd9e-45ff-8d12-9612d317ab07","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bf4a9eaa-2d74-47b1-84c1-826d7c8ce9ea","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f1f1972-**************-d0c3db565b9d","optionName":"canCreate","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"47e17298-1ec8-4b45-85e1-13cb68da30d0","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2115702e-f860-4693-8a8e-94c75d01bc3f","optionName":"canDelete","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20de7b08-5f56-4b47-81f8-0f27301078aa","optionName":"visiable","value":"true"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"595e9e3a-e6b6-4546-8b70-2989e108aaf9","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ffbe47ba-5266-4148-8f9c-74a484ac4452","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"91f395d5-aa56-457e-8550-eaf9bb14c718","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3049fdfc-1690-4b3b-8de9-0a16774b3098","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"95a0af51-98ea-4cd2-89fa-fea935a60be9","optionName":"historyVisFlag","value":"None"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.complianceComments[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0710bd5c-bb63-4fb4-8b29-192e09a99b33","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"8787c1a8-6812-42c8-8977-ac8eddf4cee8"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73858464-010b-4e4c-859f-2dce93665c0f","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f96b432-1b0a-4b2f-8461-************","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8b1be132-87f7-4cb5-844d-c5d1fdd7893c","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e48889db-7c14-44cd-8f82-746e1ebac99a","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"891f710f-4e2f-4d84-8526-d1a454c6ec1f","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"a2dd2976-a5b5-4bf5-8731-f2a04bc7a972"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b52bb24d-b7c6-4b9d-8725-7b8beafffd34","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f07543bb-46b1-40b8-8fb8-0ed2d3d08095","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"aebadd45-7865-4c0a-8fe5-7a720eb7fd32","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c3ace49f-d6c1-4b0d-8f60-c3547660be3f","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"feb6c44d-4045-46b3-8205-9179067b2488","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0691be05-6051-48c1-8206-9069fefc7199","optionName":"hasApprovals","value":"tw.local.hasApprovals"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c65eb0d5-8b92-4766-8927-0f49e88eff14","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3cd168a-9343-402b-89a2-c52af6f48d46","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24ae5077-1104-4586-87b2-61845a589d79","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4f50a81d-6a3e-4e4d-8978-cdb8b380f841","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7e36c207-4ed3-4595-87a5-598e8b6a8c64","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"54ff7d8b-34ec-42ca-8885-3c71d566ef72","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a6427721-1cd1-4810-8bd2-23624435caa6","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"13ba8fce-6bbd-4195-8fec-dc4fcc5cd9f0","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Trade Compliance","isForCompensation":false,"completionQuantity":1,"id":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37"},{"outgoing":["2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633"],"incoming":["2027.6fc60642-c337-4606-a6da-23414e447162"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633"],"nodeVisualInfo":[{"width":24,"x":426,"y":49,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.92b88917-3a57-42ab-80fa-bfe0ec00241a"},{"targetRef":"2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"43c8fb11-3d29-41d0-92b9-1bcde65f5096","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Required Documents","declaredType":"sequenceFlow","id":"2027.96ed7946-fc15-4a81-ad1c-c0808078ad31","sourceRef":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37"},{"targetRef":"2025.92b88917-3a57-42ab-80fa-bfe0ec00241a","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"46cf610c-898d-482e-8742-3ab392414a9a","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.6fc60642-c337-4606-a6da-23414e447162","sourceRef":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37"},{"targetRef":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Trade Compliance","declaredType":"sequenceFlow","id":"2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633","sourceRef":"2025.92b88917-3a57-42ab-80fa-bfe0ec00241a"},{"outgoing":["2027.800f8609-1798-415f-a6df-83143ffb0dbf"],"incoming":["2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":794,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.800f8609-1798-415f-a6df-83143ffb0dbf","name":"Update History","dataInputAssociation":[{"targetRef":"2055.648598d0-2039-40d4-b60b-3753a273a378","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}]},{"targetRef":"2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.complianceComments"]}}]},{"targetRef":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Trade Compliance\""]}}]},{"targetRef":"2055.322bdb97-0698-43d7-8172-71cbc933103d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.complianceComments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.0a1c4b7d-1274-414b-9ec2-951408429f15","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}],"sourceRef":["2055.65675974-9215-43be-8dce-3b75511a591d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.complianceComments"]}}],"sourceRef":["2055.8fcdef92-a110-407f-aff8-5693f497f953"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}],"sourceRef":["2055.fce152d9-1c42-43bc-8bff-44f6b45aba67"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.complianceComments"]}}],"sourceRef":["2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]}],"calledElement":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59"},{"targetRef":"5c2e7983-2e9c-4879-9a52-54ddfe3ec121","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.800f8609-1798-415f-a6df-83143ffb0dbf","sourceRef":"2025.0a1c4b7d-1274-414b-9ec2-951408429f15"},{"startQuantity":1,"outgoing":["2027.0beb42e2-d254-423c-95cc-055e15e51fb5"],"incoming":["2027.4befb0c1-fe91-4740-b208-86f0559bebc5"],"default":"2027.0beb42e2-d254-423c-95cc-055e15e51fb5","extensionElements":{"nodeVisualInfo":[{"width":95,"x":118,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n"]}},{"targetRef":"2025.f020b216-f0ff-4855-aa94-271718469d56","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Initialization Script","declaredType":"sequenceFlow","id":"2027.0beb42e2-d254-423c-95cc-055e15e51fb5","sourceRef":"2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.f7ecf515-5097-49ce-b0ce-9d655ce2bc72"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.c8954fd5-cc8d-41c7-8bc1-ad1fe738678f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasApprovals","isCollection":false,"declaredType":"dataObject","id":"2056.d59cd1a5-f6b3-40c2-ab0b-fe5643d4e541"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasReturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.342dbf14-4b55-4e38-b728-267ca45e4379"},{"outgoing":["2027.2757e1fa-b57e-4069-861b-a7be7eec859c","2027.74a71ea6-a35d-433b-8926-367bc257a633"],"incoming":["2027.cc17a0bf-b3ab-4129-ad26-d436e200221d"],"default":"2027.2757e1fa-b57e-4069-861b-a7be7eec859c","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":519,"y":381,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.893ccffa-900f-45ea-bc4a-739996eb37cb"},{"startQuantity":1,"outgoing":["2027.cc17a0bf-b3ab-4129-ad26-d436e200221d"],"incoming":["2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e"],"default":"2027.cc17a0bf-b3ab-4129-ad26-d436e200221d","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":404,"y":362,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\ntw.local.validationMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\ntw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index , tabName)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\tif (tw.local.validationMessage.length == 0) {\r\n\t\t\ttw.local.validationMessage += \"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.validationMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\n\/\/mandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\/\/\/\/------------------------------------financial Details fo -------------------------------------------------------------------\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,\"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.name\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank\" );\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.account\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,\"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber\" );\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.submitRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.executionHub.code,\"tw.local.idcRequest.financialDetails.executionHub.code\");\r\n\/\/}\r\n\/\/var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;\r\n\/\/if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.cashAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\r\n\/\/}\r\n\/\/\r\n\/\/var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;\r\n\/\/if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtSight\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/}\r\n\/\/for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {\r\n\/\/\t\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentDate\");\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentAmount\");\r\n\/\/}\r\n\/\/-------------------------------action-----------------------------------------------------------------------\r\nif (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {\r\n\tmandatory(tw.local.idcRequest.stepLog.returnReason,\"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n\/\/}\r\n\r\n\/\/if ((tw.local.idcRequest.approvals.CAD==true|| \r\n\/\/    tw.local.idcRequest.approvals.compliance==true ||\r\n\/\/    tw.local.idcRequest.approvals.treasury==true))\r\n\/\/{   \r\n\/\/    if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n\/\/       addError(\"tw.local.selectedAction\", \"Please uncheck Approvals\");\r\n\/\/    }\r\n\/\/}\r\n\/\/else if (tw.local.selectedAction == \"Obtain Approvals\")\r\n\/\/{\r\n\/\/    addError(\"tw.local.selectedAction\", \"Please check Approvals\");\r\n\/\/}\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");"]}},{"targetRef":"2025.a87ed110-f284-4d08-9da9-5a40e8707d1d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.2757e1fa-b57e-4069-861b-a7be7eec859c","sourceRef":"2025.893ccffa-900f-45ea-bc4a-739996eb37cb"},{"targetRef":"2025.0be391b6-0376-4cb6-92e3-def1ca01bd37","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.74a71ea6-a35d-433b-8926-367bc257a633","sourceRef":"2025.893ccffa-900f-45ea-bc4a-739996eb37cb"},{"targetRef":"2025.893ccffa-900f-45ea-bc4a-739996eb37cb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.cc17a0bf-b3ab-4129-ad26-d436e200221d","sourceRef":"2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.8f528f5c-4dcc-46cb-b8cd-952623d6792f"},{"outgoing":["2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e"],"incoming":["2027.96ed7946-fc15-4a81-ad1c-c0808078ad31"],"extensionElements":{"postAssignmentScript":["if (tw.local.errorExist) {\r\n\talert(tw.local.errorMessage);\r\n}"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":404,"y":271,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;"]},"declaredType":"callActivity","startQuantity":1,"default":"2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e","name":"Get Required Documents","dataInputAssociation":[{"targetRef":"2055.aae56053-3bba-40b1-abc9-6a441a93f307","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.IDCRequestType.englishdescription"]}}]},{"targetRef":"2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","declaredType":"TFormalExpression","content":["tw.local.documentsTypesSelected"]}}]},{"targetRef":"2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.documentsSource.englishdescription"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.errorExist"]}}],"sourceRef":["2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6"]}],"calledElement":"1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2"},{"targetRef":"2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation","declaredType":"sequenceFlow","id":"2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e","sourceRef":"2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.4074de33-5391-4ada-9870-ae0774b6dc2b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.f99c7306-e85b-4fc7-96da-72488e618fb8"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.e35c4e5d-4d95-414a-a54c-3c98f7e401d6"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.6567920d-a662-4b39-b289-0beff620c542"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.d761c6a9-1443-48db-8b63-df107e91ea43"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.dc7ac6a3-6fe8-4753-87db-d2a8be8ec46d"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.fe768e48-eaa8-4876-8c13-2db1ab69831b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.b0afc7c1-075f-49c8-88a1-a11e67ff44b0"},{"startQuantity":1,"outgoing":["2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48"],"incoming":["2027.9a829394-b543-45aa-8218-ec7ba3684c08","2027.926cd246-1b13-4a54-8dfc-e72952cc76a8"],"default":"2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":724,"y":393,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.9a829394-b543-45aa-8218-ec7ba3684c08"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.38a5b1ed-d636-454f-888b-b6ccea732769"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.0a1c4b7d-1274-414b-9ec2-951408429f15","extensionElements":{"default":["2027.9a829394-b543-45aa-8218-ec7ba3684c08"],"nodeVisualInfo":[{"width":24,"x":829,"y":223,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.facf184e-209f-4018-875b-98ba497d8bdd","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.926cd246-1b13-4a54-8dfc-e72952cc76a8"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.bd5b9616-0753-4934-82ed-55f1de5eed37"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec","extensionElements":{"default":["2027.926cd246-1b13-4a54-8dfc-e72952cc76a8"],"nodeVisualInfo":[{"width":24,"x":392,"y":294,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 1","declaredType":"boundaryEvent","id":"2025.54249bb6-071e-431f-8674-238a20293d62","outputSet":{}},{"targetRef":"2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.9a829394-b543-45aa-8218-ec7ba3684c08","sourceRef":"2025.facf184e-209f-4018-875b-98ba497d8bdd"},{"targetRef":"2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false,"customBendPoint":[{"x":627,"y":479}]}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.926cd246-1b13-4a54-8dfc-e72952cc76a8","sourceRef":"2025.54249bb6-071e-431f-8674-238a20293d62"},{"incoming":["2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":731,"y":307,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.e68bce8d-9d00-4702-8065-ded79ab91a2f"},{"targetRef":"2025.e68bce8d-9d00-4702-8065-ded79ab91a2f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48","sourceRef":"2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.0d9f3715-bb1d-41de-8d55-59b7c12dfc2e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.e2567688-ed6e-4ec4-8db2-d5e561982ae2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.0e4df6f1-a604-47bd-828c-52d5b4ca5da0"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.d4f9747a-8a0f-4ab0-808e-043ca636fad9"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"81757e6c-c986-42df-96a7-bcd5c29d604b","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"fb0a11f9-f93b-4218-8cff-96a5ce700a77","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Trade Compliance","declaredType":"globalUserTask","id":"1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.c15e3a71-f7f4-40bc-8035-78c78a0a6057"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.2c5a45ad-0871-4a23-8029-a559122e16dc"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"complianceComments","isCollection":true,"id":"2055.3629713d-4131-4e1e-92a5-58565c110223"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"c3a3c21b-e04e-49b4-891b-117534c45a9b","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"63aebe98-65a1-4c50-8bb6-b67beda8b167","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"f7f921bd-19de-4816-8cb6-b885daa6d4df","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.b49e2ddf-2b30-495d-b3e5-97267ac03e92"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.74f1e9d6-2c3b-4b03-8501-7c74079fd417"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"intiator","isCollection":false,"id":"2055.09c55a0c-8b22-493e-815c-8219a69099cb"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.59f6444f-39ba-4674-83f0-96360e86664f"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.b1c41503-5989-45cb-ae22-e0dcd7fdc79a"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.12d31e9f-aff0-45fb-85a2-57810153f0e5"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b49e2ddf-2b30-495d-b3e5-97267ac03e92</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>38d279c1-98dd-43ad-bcdf-0b3efc0d56e7</guid>
            <versionId>9111ab2f-2547-4ee3-8e52-ed9110583417</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.74f1e9d6-2c3b-4b03-8501-7c74079fd417</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f5a6c47e-**************-b83e66671890</guid>
            <versionId>3bf9256c-45d1-4dd1-af2c-5b3c3b2d9ecb</versionId>
        </processParameter>
        <processParameter name="intiator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.09c55a0c-8b22-493e-815c-8219a69099cb</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2dee42f5-edb3-4499-a3d6-b9b7b500e4fa</guid>
            <versionId>8e844585-f9e8-4a31-a87e-199ed17b5411</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.59f6444f-39ba-4674-83f0-96360e86664f</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c1233546-e483-4d7d-a2cf-2f7eb522f8ce</guid>
            <versionId>33c4f98b-dc44-41d2-8c8e-695159502b85</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b1c41503-5989-45cb-ae22-e0dcd7fdc79a</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>670fd719-2ebf-45a0-9c41-6b700a3df77c</guid>
            <versionId>92016782-4eb7-42a5-a95e-************</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c15e3a71-f7f4-40bc-8035-78c78a0a6057</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0129360c-4caf-4492-b5dd-0d2fff8f694b</guid>
            <versionId>41f03cca-8934-42be-b85e-0c8a1f4cab21</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2c5a45ad-0871-4a23-8029-a559122e16dc</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>08a954dc-257d-40d8-826d-cff93aec7edc</guid>
            <versionId>4bba7bcc-b9e6-4a9a-aeaa-f1392d8678ff</versionId>
        </processParameter>
        <processParameter name="complianceComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3629713d-4131-4e1e-92a5-58565c110223</processParameterId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>48f4c05c-2cb7-41b6-a3b1-6bb8c34c9992</guid>
            <versionId>50608148-b5ca-4290-ae1f-664fd6c53e7c</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f7ecf515-5097-49ce-b0ce-9d655ce2bc72</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ad32f531-da05-4fc1-82a8-53f47b8898b6</guid>
            <versionId>49d5befc-214c-49c7-9f41-023ff783ce0a</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c8954fd5-cc8d-41c7-8bc1-ad1fe738678f</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>25778409-cd7d-480a-aacf-e7afb1457185</guid>
            <versionId>a1318f5d-2405-4a75-9655-bf40c0c749fe</versionId>
        </processVariable>
        <processVariable name="hasApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d59cd1a5-f6b3-40c2-ab0b-fe5643d4e541</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8a9f3ec5-5db0-46d9-9079-333565502bea</guid>
            <versionId>12072399-ad71-4612-8230-3494c8e6e772</versionId>
        </processVariable>
        <processVariable name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.342dbf14-4b55-4e38-b728-267ca45e4379</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b110e8d0-70a0-46b2-8700-2f072fd0b7c7</guid>
            <versionId>94a52062-f0e1-4c89-81f6-adaedbb68580</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8f528f5c-4dcc-46cb-b8cd-952623d6792f</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>aee84142-f089-4ca9-a503-097925aa6846</guid>
            <versionId>95ce3c42-788d-43fa-aa32-c976f5411d7d</versionId>
        </processVariable>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4074de33-5391-4ada-9870-ae0774b6dc2b</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>90fa10c0-6189-4524-a15e-bec33c638cff</guid>
            <versionId>f6634161-b3fa-40dc-a2fc-f0a72e999fcc</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f99c7306-e85b-4fc7-96da-72488e618fb8</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d7a484a6-9e38-4b7b-972d-21dd21305324</guid>
            <versionId>dd3f871d-db9d-4fb7-9733-ad28583c518d</versionId>
        </processVariable>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e35c4e5d-4d95-414a-a54c-3c98f7e401d6</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>878f4cae-0027-46f8-bcf4-5946dffc09db</guid>
            <versionId>5db14690-a9d9-4abe-b6f2-88fa60bb3428</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6567920d-a662-4b39-b289-0beff620c542</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>********-bd22-4dbb-973e-f9043bbebde5</guid>
            <versionId>3dff9575-d9d8-4bb8-a0e1-dc299dedbfc2</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d761c6a9-1443-48db-8b63-df107e91ea43</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9ed659dd-3d56-41d9-9ec8-d980c7dc10ea</guid>
            <versionId>acd7e30a-ac55-4ae9-ace3-fda72d5aa79a</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dc7ac6a3-6fe8-4753-87db-d2a8be8ec46d</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bf8e4486-01da-4dfb-b309-329071a83e6d</guid>
            <versionId>5f201c3c-b092-4799-9b4c-42d7a10ec683</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fe768e48-eaa8-4876-8c13-2db1ab69831b</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c9f6e4e9-3d76-4c1e-b887-fc3be8b021e7</guid>
            <versionId>629b7cf9-0c37-4a69-8ea1-cd4f59c132b7</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b0afc7c1-075f-49c8-88a1-a11e67ff44b0</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>24ae8627-46de-428c-92b5-4aa1e5e7293f</guid>
            <versionId>3c33a8e2-aab0-4498-9380-c00f80492f8f</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d9f3715-bb1d-41de-8d55-59b7c12dfc2e</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>438fec33-3f60-425e-8706-aa10888b3e47</guid>
            <versionId>6382d875-436a-4b2c-aff7-7612a7312ec7</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e2567688-ed6e-4ec4-8db2-d5e561982ae2</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8b5c8bd7-5854-4d3c-b81e-4c2a584ec7c7</guid>
            <versionId>700edf26-0d6b-4863-88c9-52292b602caf</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0e4df6f1-a604-47bd-828c-52d5b4ca5da0</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a346cfa2-5500-4dc6-b1b3-3316f2aefb91</guid>
            <versionId>9e1ad80d-9186-489b-841b-099243412190</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d4f9747a-8a0f-4ab0-808e-043ca636fad9</processVariableId>
            <description isNull="true" />
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>69609c70-f7a3-4051-be67-242cbae29e32</guid>
            <versionId>a76079f0-f4fc-4c3f-9ec8-8f71fabb01ff</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.23306d53-2e50-4441-8f82-c0417ab87221</processItemId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ca37fc09-0f2a-460f-bed9-1e4fe4c8514a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ce</guid>
            <versionId>249e9d10-10d6-4548-8038-b5f7d6971f41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ca37fc09-0f2a-460f-bed9-1e4fe4c8514a</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b05f0637-8720-422d-9542-38bff4ae29cf</guid>
                <versionId>4ff91787-5a17-4c59-8d33-6d9a9966cd04</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6f5f8a8d-823b-4550-ac14-f9f140b519b8</processItemId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.08f2ac9d-2655-415e-bbf5-e72ef5bf6bc6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60cd</guid>
            <versionId>94195e9b-65fe-4688-9069-32c7d3abd86a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0a1c4b7d-1274-414b-9ec2-951408429f15</processItemId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <name>Update History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c46bb2f1-2895-4255-b891-31f22fc4747a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60cc</guid>
            <versionId>a6e9dc31-62d4-4d5a-9ae3-88ec37c4f59c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c46bb2f1-2895-4255-b891-31f22fc4747a</subProcessId>
                <attachedProcessRef>/1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</attachedProcessRef>
                <guid>e20ec971-49ae-4882-81de-a6f2582dcf8e</guid>
                <versionId>aadd0fe2-3877-4e28-8c6b-ea0deda6d42f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec</processItemId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <name>Get Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.dcb3afa4-e446-4837-90e1-34f39b8dde75</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60cf</guid>
            <versionId>efcf9305-1533-46fe-8efa-36b86564c8dd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.dcb3afa4-e446-4837-90e1-34f39b8dde75</subProcessId>
                <attachedProcessRef>/1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</attachedProcessRef>
                <guid>0f22d77b-c5af-40eb-b1a2-d7e3331f248a</guid>
                <versionId>aad7f941-b5f5-46b3-b23e-6dadb8e623c6</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.89fab034-3b06-43f9-994f-62ee9daacd52</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <guid>05b071ca-b5ca-4cb7-aec5-6b288b713eb5</guid>
            <versionId>6d908a8d-a645-4ff1-9e66-465237b8e82e</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.112caeee-b969-4a8f-bcb5-8434246f1220</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <guid>be1ee4ec-248f-4e07-b7de-7d0691b3159f</guid>
            <versionId>b0cce2ba-3e93-44b7-921e-dff7049ef70b</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.440de804-4bdc-405a-8f9e-75703a2e92bd</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <guid>ec43ff3c-eff8-48c5-8b0b-3c44a83bb432</guid>
            <versionId>cf84efdd-923d-4303-b4ee-3aa5c2cedc15</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.6f5f8a8d-823b-4550-ac14-f9f140b519b8</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.12d31e9f-aff0-45fb-85a2-57810153f0e5" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Trade Compliance" id="1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="fb0a11f9-f93b-4218-8cff-96a5ce700a77">
                            
                            
                            <ns16:startEvent name="Start" id="f564c6cb-e29a-4db2-8940-58fb27a0354b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.4befb0c1-fe91-4740-b208-86f0559bebc5</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="5c2e7983-2e9c-4879-9a52-54ddfe3ec121">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="974" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.800f8609-1798-415f-a6df-83143ffb0dbf</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="f564c6cb-e29a-4db2-8940-58fb27a0354b" targetRef="2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc" name="Start To Coach" id="2027.4befb0c1-fe91-4740-b208-86f0559bebc5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.75b5270b-a7e2-442d-b332-e2a5864c362e" name="Initialization Script" id="2025.f020b216-f0ff-4855-aa94-271718469d56">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="243" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0beb42e2-d254-423c-95cc-055e15e51fb5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.75b5270b-a7e2-442d-b332-e2a5864c362e</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
tw.local.errorVIS = "NONE";&#xD;
tw.local.action = [];&#xD;
tw.local.action[0] = tw.epv.Action.returnToInitiator;&#xD;
tw.local.action[1] = tw.epv.Action.approveRequest; &#xD;
tw.local.complianceComments = tw.local.idcRequest.appLog;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4" name="Set Status " id="2025.a87ed110-f284-4d08-9da9-5a40e8707d1d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="630" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.2757e1fa-b57e-4069-861b-a7be7eec859c</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4</ns16:outgoing>
                                
                                
                                <ns16:script>if(tw.local.intiator == "fo"){&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;&#xD;
}else if (tw.local.intiator == "Initiation") {&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;&#xD;
}else{&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;&#xD;
}&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f020b216-f0ff-4855-aa94-271718469d56" targetRef="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37" name="To Trade Compliance" id="2027.75b5270b-a7e2-442d-b332-e2a5864c362e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a87ed110-f284-4d08-9da9-5a40e8707d1d" targetRef="2025.0a1c4b7d-1274-414b-9ec2-951408429f15" name="To End" id="2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Trade Compliance" id="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="406" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.75b5270b-a7e2-442d-b332-e2a5864c362e</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.74a71ea6-a35d-433b-8926-367bc257a633</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.96ed7946-fc15-4a81-ad1c-c0808078ad31</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.6fc60642-c337-4606-a6da-23414e447162</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>d5bca2d8-54b0-4ed5-8b81-b3d5d378d1d8</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3608991d-5008-416a-8681-b3c10833dfbd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>63ff07da-b122-41b5-82e8-20a081544b65</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d2c591a2-b2f4-47c2-8eb5-78dd0a150b6c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>56d065cc-**************-69c1dff368d3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f9bb01dd-ad67-4897-80e5-1f6823658edd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3d14a4d6-0541-4251-85db-c7bb0fb5f3de</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>13ba8fce-6bbd-4195-8fec-dc4fcc5cd9f0</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b52bb24d-b7c6-4b9d-8725-7b8beafffd34</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f07543bb-46b1-40b8-8fb8-0ed2d3d08095</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>aebadd45-7865-4c0a-8fe5-7a720eb7fd32</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c3ace49f-d6c1-4b0d-8f60-c3547660be3f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>feb6c44d-4045-46b3-8205-9179067b2488</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>0691be05-6051-48c1-8206-9069fefc7199</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasApprovals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c65eb0d5-8b92-4766-8927-0f49e88eff14</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a3cd168a-9343-402b-89a2-c52af6f48d46</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>24ae5077-1104-4586-87b2-61845a589d79</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>4f50a81d-6a3e-4e4d-8978-cdb8b380f841</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7e36c207-4ed3-4595-87a5-598e8b6a8c64</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>54ff7d8b-34ec-42ca-8885-3c71d566ef72</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a6427721-1cd1-4810-8bd2-23624435caa6</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>a2dd2976-a5b5-4bf5-8731-f2a04bc7a972</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>891f710f-4e2f-4d84-8526-d1a454c6ec1f</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>73858464-010b-4e4c-859f-2dce93665c0f</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>1f96b432-1b0a-4b2f-8461-************</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>8b1be132-87f7-4cb5-844d-c5d1fdd7893c</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>e48889db-7c14-44cd-8f82-746e1ebac99a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>8787c1a8-6812-42c8-8977-ac8eddf4cee8</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f9d1579c-9508-46ab-8756-d977b23e59e0</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8a46a3de-61d4-4cb3-8b26-c1616004a6b5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>233f9430-a8a4-4dd7-8fc1-2528eb2722b8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>665b0725-ae5b-4646-8873-0c208bcfadfb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>15252763-e630-4826-81ba-94e576322c23</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>46da2285-bc81-48a5-8bf5-74e3df36c3d0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>58e2f721-57b6-4c05-8383-0a033afef2d8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6bb1b10c-03b5-4c82-87ca-507f68c66479</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>42278afa-f9c5-41a6-8054-1d575832f563</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>73a48077-b23c-4133-890d-6b112d36cbac</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a7fc7787-3503-477f-8c38-3cfdaa5d82e6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2ed34154-7d4a-41fd-8d8a-30fc694e7c73</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>no</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5c4f6031-0046-446a-8a0c-9d6cde3b0c68</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6dc3052d-254a-440e-85a5-97ba640c33a9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4c71940d-4e72-4eab-8ea8-61397c332b59</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ade3d8d6-6e64-4611-840a-814fef43edca</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f10e7df3-3808-476b-8776-d27cce502202</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c2ac03f7-4f36-4687-878d-8eef4ce33ede</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fe769dea-4204-41e1-85a0-1773d150792d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>92e2d68c-fa2c-4d73-8a98-9204847b9629</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>083afada-b0a0-42e1-80d9-76d0a738198e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1842f0a0-0dd1-4c97-86aa-62ceb2e3088b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7ee24b08-ed1b-4b81-89c1-d6d4aa3d897d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ab0419f1-3e34-4c7e-8cb4-b5532249f041</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>39d4d8c3-0e97-4a35-83e2-aec9018d6ab9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f91f2f89-fede-4c46-8bd7-4878cfc27415</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1293486f-5a9b-44ad-834a-27f6bb512ea5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>dc7a475c-5deb-4d9d-88ff-b0b21024b12a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>08f73b80-9ceb-4108-881c-ab783f0ca917</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>13c5e409-6897-4c88-8765-d8385b4db6a9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0c97aa1e-8dbe-4fd6-8cbf-0c3b01142e20</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d9a14531-47c2-4a15-8b20-a0ea3363e33f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f4ff2f30-6c7e-41b7-8887-b440be1f7ad5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>595e9e3a-e6b6-4546-8b70-2989e108aaf9</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6750a3fa-c7be-4683-82ed-090471ddf529</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>efa4b579-e6d2-44f1-8527-42587b87d570</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>59d6de0e-dd9e-45ff-8d12-9612d317ab07</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bf4a9eaa-2d74-47b1-84c1-826d7c8ce9ea</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1f1f1972-**************-d0c3db565b9d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>47e17298-1ec8-4b45-85e1-13cb68da30d0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2115702e-f860-4693-8a8e-94c75d01bc3f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>20de7b08-5f56-4b47-81f8-0f27301078aa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>0710bd5c-bb63-4fb4-8b29-192e09a99b33</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ffbe47ba-5266-4148-8f9c-74a484ac4452</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>91f395d5-aa56-457e-8550-eaf9bb14c718</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3049fdfc-1690-4b3b-8de9-0a16774b3098</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>95a0af51-98ea-4cd2-89fa-fea935a60be9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.complianceComments[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.92b88917-3a57-42ab-80fa-bfe0ec00241a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="426" y="49" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6fc60642-c337-4606-a6da-23414e447162</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37" targetRef="2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec" name="To Get Required Documents" id="2027.96ed7946-fc15-4a81-ad1c-c0808078ad31">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="43c8fb11-3d29-41d0-92b9-1bcde65f5096">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37" targetRef="2025.92b88917-3a57-42ab-80fa-bfe0ec00241a" name="To Postpone" id="2027.6fc60642-c337-4606-a6da-23414e447162">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="46cf610c-898d-482e-8742-3ab392414a9a">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.92b88917-3a57-42ab-80fa-bfe0ec00241a" targetRef="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37" name="To Trade Compliance" id="2027.ed39cb33-77c5-4eb4-8877-e0dbfeb2f633">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.800f8609-1798-415f-a6df-83143ffb0dbf" name="Update History" id="2025.0a1c4b7d-1274-414b-9ec2-951408429f15">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="794" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0d026e85-d4a0-48c6-bd92-ea81b7eeb0d4</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.800f8609-1798-415f-a6df-83143ffb0dbf</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.648598d0-2039-40d4-b60b-3753a273a378</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.complianceComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Trade Compliance"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.complianceComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.65675974-9215-43be-8dce-3b75511a591d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8fcdef92-a110-407f-aff8-5693f497f953</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.complianceComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fce152d9-1c42-43bc-8bff-44f6b45aba67</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.complianceComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0a1c4b7d-1274-414b-9ec2-951408429f15" targetRef="5c2e7983-2e9c-4879-9a52-54ddfe3ec121" name="To End" id="2027.800f8609-1798-415f-a6df-83143ffb0dbf">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.0beb42e2-d254-423c-95cc-055e15e51fb5" name="Set Step Name" id="2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="118" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4befb0c1-fe91-4740-b208-86f0559bebc5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0beb42e2-d254-423c-95cc-055e15e51fb5</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4f74c428-d996-4eb7-8dfc-58b2ca5018dc" targetRef="2025.f020b216-f0ff-4855-aa94-271718469d56" name="To Initialization Script" id="2027.0beb42e2-d254-423c-95cc-055e15e51fb5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.f7ecf515-5097-49ce-b0ce-9d655ce2bc72" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.c8954fd5-cc8d-41c7-8bc1-ad1fe738678f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasApprovals" id="2056.d59cd1a5-f6b3-40c2-ab0b-fe5643d4e541">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasReturnReason" id="2056.342dbf14-4b55-4e38-b728-267ca45e4379">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">true</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:exclusiveGateway default="2027.2757e1fa-b57e-4069-861b-a7be7eec859c" gatewayDirection="Unspecified" name="Have Errors" id="2025.893ccffa-900f-45ea-bc4a-739996eb37cb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="519" y="381" width="32" height="32" />
                                    
                                    
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.cc17a0bf-b3ab-4129-ad26-d436e200221d</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.2757e1fa-b57e-4069-861b-a7be7eec859c</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.74a71ea6-a35d-433b-8926-367bc257a633</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.cc17a0bf-b3ab-4129-ad26-d436e200221d" name="validation" id="2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="404" y="362" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.cc17a0bf-b3ab-4129-ad26-d436e200221d</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.message = "";&#xD;
tw.local.validationMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index , tabName)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		if (tw.local.validationMessage.length == 0) {&#xD;
			tw.local.validationMessage += "&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.validationMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
////validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
////----------------------------------basic details------------------------------------------------------------------------------&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
////----------------------------------------app info------------------------------------------------------------------------------------&#xD;
//mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
////------------------------------------financial Details fo -------------------------------------------------------------------	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,"tw.local.idcRequest.financialDetails.beneficiaryDetails.name");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank" );	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,"tw.local.idcRequest.financialDetails.beneficiaryDetails.account" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber" );&#xD;
//if (tw.local.selectedAction == tw.epv.Action.submitRequest) {&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.executionHub.code,"tw.local.idcRequest.financialDetails.executionHub.code");&#xD;
//}&#xD;
//var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;&#xD;
//if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
//	addError("tw.local.idcRequest.financialDetails.cashAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//&#xD;
//}&#xD;
//&#xD;
//var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;&#xD;
//if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtSight" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//}&#xD;
//for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {&#xD;
//	&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentDate");&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentAmount");&#xD;
//}&#xD;
//-------------------------------action-----------------------------------------------------------------------&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.returnReason,"tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
//if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
//	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
//}&#xD;
&#xD;
//if ((tw.local.idcRequest.approvals.CAD==true|| &#xD;
//    tw.local.idcRequest.approvals.compliance==true ||&#xD;
//    tw.local.idcRequest.approvals.treasury==true))&#xD;
//{   &#xD;
//    if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
//       addError("tw.local.selectedAction", "Please uncheck Approvals");&#xD;
//    }&#xD;
//}&#xD;
//else if (tw.local.selectedAction == "Obtain Approvals")&#xD;
//{&#xD;
//    addError("tw.local.selectedAction", "Please check Approvals");&#xD;
//}&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.893ccffa-900f-45ea-bc4a-739996eb37cb" targetRef="2025.a87ed110-f284-4d08-9da9-5a40e8707d1d" name="no" id="2027.2757e1fa-b57e-4069-861b-a7be7eec859c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.893ccffa-900f-45ea-bc4a-739996eb37cb" targetRef="2025.0be391b6-0376-4cb6-92e3-def1ca01bd37" name="yes" id="2027.74a71ea6-a35d-433b-8926-367bc257a633">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea" targetRef="2025.893ccffa-900f-45ea-bc4a-739996eb37cb" name="To Have Errors" id="2027.cc17a0bf-b3ab-4129-ad26-d436e200221d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.8f528f5c-4dcc-46cb-b8cd-952623d6792f" />
                            
                            
                            <ns16:callActivity calledElement="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" default="2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e" name="Get Required Documents" id="2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="404" y="271" width="95" height="70" color="#95D087" />
                                    
                                    
                                    <ns3:preAssignmentScript>tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;</ns3:preAssignmentScript>
                                    
                                    
                                    <ns3:postAssignmentScript>if (tw.local.errorExist) {&#xD;
	alert(tw.local.errorMessage);&#xD;
}</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.96ed7946-fc15-4a81-ad1c-c0808078ad31</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.aae56053-3bba-40b1-abc9-6a441a93f307</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.IDCRequestType.englishdescription</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f">tw.local.documentsTypesSelected</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.documentsSource.englishdescription</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.errorExist</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec" targetRef="2025.a45c0daa-b1e6-4c54-9fe6-8bdaffd0a0ea" name="To validation" id="2027.22355e78-ea2f-4ba7-9563-d60d0a6abe0e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.4074de33-5391-4ada-9870-ae0774b6dc2b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.f99c7306-e85b-4fc7-96da-72488e618fb8" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="documentsTypesSelected" id="2056.e35c4e5d-4d95-414a-a54c-3c98f7e401d6" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.6567920d-a662-4b39-b289-0beff620c542" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.d761c6a9-1443-48db-8b63-df107e91ea43" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.dc7ac6a3-6fe8-4753-87db-d2a8be8ec46d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.fe768e48-eaa8-4876-8c13-2db1ab69831b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.b0afc7c1-075f-49c8-88a1-a11e67ff44b0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48" name="Handling Error" id="2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="724" y="393" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9a829394-b543-45aa-8218-ec7ba3684c08</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.926cd246-1b13-4a54-8dfc-e72952cc76a8</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.0a1c4b7d-1274-414b-9ec2-951408429f15" parallelMultiple="false" name="Error" id="2025.facf184e-209f-4018-875b-98ba497d8bdd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="829" y="223" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.9a829394-b543-45aa-8218-ec7ba3684c08</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.9a829394-b543-45aa-8218-ec7ba3684c08</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.38a5b1ed-d636-454f-888b-b6ccea732769" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.9a8f4dc9-008e-4d67-9c72-b09a58e045ec" parallelMultiple="false" name="Error 1" id="2025.54249bb6-071e-431f-8674-238a20293d62">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="392" y="294" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.926cd246-1b13-4a54-8dfc-e72952cc76a8</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.926cd246-1b13-4a54-8dfc-e72952cc76a8</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.bd5b9616-0753-4934-82ed-55f1de5eed37" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.facf184e-209f-4018-875b-98ba497d8bdd" targetRef="2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd" name="To Handling Error" id="2027.9a829394-b543-45aa-8218-ec7ba3684c08">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.54249bb6-071e-431f-8674-238a20293d62" targetRef="2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd" name="To Handling Error" id="2027.926cd246-1b13-4a54-8dfc-e72952cc76a8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:customBendPoint x="627" y="479" />
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.e68bce8d-9d00-4702-8065-ded79ab91a2f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="731" y="307" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.aa1a51a9-0897-41a0-8e9d-e57878c372dd" targetRef="2025.e68bce8d-9d00-4702-8065-ded79ab91a2f" name="To Stay on page" id="2027.70fe3d21-d53b-4616-8f44-9275ee6a4b48">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.0d9f3715-bb1d-41de-8d55-59b7c12dfc2e" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.e2567688-ed6e-4ec4-8db2-d5e561982ae2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.0e4df6f1-a604-47bd-828c-52d5b4ca5da0" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.d4f9747a-8a0f-4ab0-808e-043ca636fad9" />
                            
                            
                            <ns3:htmlHeaderTag id="81757e6c-c986-42df-96a7-bcd5c29d604b">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="c3a3c21b-e04e-49b4-891b-117534c45a9b" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="63aebe98-65a1-4c50-8bb6-b67beda8b167" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="f7f921bd-19de-4816-8cb6-b885daa6d4df" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.b49e2ddf-2b30-495d-b3e5-97267ac03e92" />
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.74f1e9d6-2c3b-4b03-8501-7c74079fd417" />
                        
                        
                        <ns16:dataInput name="intiator" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.09c55a0c-8b22-493e-815c-8219a69099cb" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.59f6444f-39ba-4674-83f0-96360e86664f" />
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.b1c41503-5989-45cb-ae22-e0dcd7fdc79a" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.c15e3a71-f7f4-40bc-8035-78c78a0a6057" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.2c5a45ad-0871-4a23-8029-a559122e16dc" />
                        
                        
                        <ns16:dataOutput name="complianceComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.3629713d-4131-4e1e-92a5-58565c110223" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8e8e8537-d476-4252-8fb1-025ff8369a6b</processLinkId>
            <processId>1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6f5f8a8d-823b-4550-ac14-f9f140b519b8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.23306d53-2e50-4441-8f82-c0417ab87221</toProcessItemId>
            <guid>cd33682a-0ac4-43eb-84fe-3d5d28144726</guid>
            <versionId>b227457c-86cf-4a3b-b6b7-154efff6d56d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.6f5f8a8d-823b-4550-ac14-f9f140b519b8</fromProcessItemId>
            <toProcessItemId>2025.23306d53-2e50-4441-8f82-c0417ab87221</toProcessItemId>
        </link>
    </process>
</teamworks>

