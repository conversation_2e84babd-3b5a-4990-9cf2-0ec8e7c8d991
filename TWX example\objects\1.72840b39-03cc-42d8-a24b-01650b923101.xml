<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.72840b39-03cc-42d8-a24b-01650b923101" name="Validate BIC">
        <lastModified>1692539781113</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c81</guid>
        <versionId>acfb07f7-5517-43f9-a493-0bdd78068752</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:57e9" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.240464da-d510-457e-8c69-83fade37dc0e"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050"},{"incoming":["b3ec852a-e60c-4309-8191-d4c835f309c4","e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":956,"y":118,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c7f"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"12ab068b-2bae-43ff-8700-067c4e7076c5"},{"targetRef":"90e1d8a8-e6ea-4b7a-844e-2aa49324caa3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To init","declaredType":"sequenceFlow","id":"2027.240464da-d510-457e-8c69-83fade37dc0e","sourceRef":"b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050"},{"startQuantity":1,"outgoing":["e692f4fa-9b1a-48a2-8349-191d502eb670"],"incoming":["8a5909e8-fc03-4a05-8488-7fa5e5c6137a","23804f29-ed90-4eba-854d-ee96704d1a31"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":92,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"MW_FC Verify Bank Bic Codes","dataInputAssociation":[{"targetRef":"2055.eeb6d244-dfcd-4636-8e8a-35bf9302f544","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.BICcodes[tw.local.iterator].value"]}}]},{"targetRef":"2055.9366d5b6-7327-4ce6-8d2e-6f1a97f58b13","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.ceca8d8c-9d5f-48bf-891c-0cb500864329","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.98c3ed47-ba29-4e9f-8fad-177a4fac28d4","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.cb83c814-9f82-4ed8-8e8a-1615a27c64c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.6cd6ca14-6b5c-4577-85f9-5484239a661a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.8b9a0f6c-a79e-4bdc-8cad-65c1be0097aa","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"4aef9d05-d8d9-4090-8733-d04a2c907886","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.46ac92bd-1702-4b54-a984-d23fd4f07e1e","declaredType":"TFormalExpression","content":["tw.local.bankDetails"]}}],"sourceRef":["2055.f0c79392-3e66-465f-88cc-000c55b27946"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.65bd3bdd-d81b-4da3-8aca-0a6e28dce6f4"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.9cb71c49-55b3-4b55-81a3-a631fb8be2ff"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.f0e139e3-a4e5-4cf2-8717-00465b910710"]}],"calledElement":"1.97d9ac59-02bb-4969-8d24-6a7ac50478eb"},{"targetRef":"1c764939-7ee6-44e4-842e-aa2d80c48514","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-581f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Exclusive Gateway1","declaredType":"sequenceFlow","id":"e692f4fa-9b1a-48a2-8349-191d502eb670","sourceRef":"4aef9d05-d8d9-4090-8733-d04a2c907886"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"iterator","isCollection":false,"declaredType":"dataObject","id":"2056.05cf9d4e-355b-4f96-8ff4-1fbef3fb5452"},{"startQuantity":1,"outgoing":["9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66"],"incoming":["e692f4fa-9b1a-48a2-8349-191d502eb670"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":616,"y":97,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Result","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1c764939-7ee6-44e4-842e-aa2d80c48514","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tif (tw.local.isSuccessful) {\r\n\t\tif (tw.local.bankDetails.bankName == null || tw.local.bankDetails.bankName == undefined || tw.local.bankDetails.bankName == \"\" ) {\r\n\t\t\ttw.local.result[tw.local.iterator] = false;\r\n\t\t}\r\n\t\telse{\r\n\t\t\ttw.local.result[tw.local.iterator] = true;\r\n\t\t}\r\n\t}else{\r\n\t\t\ttw.local.result[tw.local.iterator] = false;\r\n\t\t}\r\n\ttw.local.iterator +=1;\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"4b183c21-5c24-45ca-87fe-35c1d100f0f5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66","sourceRef":"1c764939-7ee6-44e4-842e-aa2d80c48514"},{"itemSubjectRef":"itm.12.46ac92bd-1702-4b54-a984-d23fd4f07e1e","name":"bankDetails","isCollection":false,"declaredType":"dataObject","id":"2056.fc3eead2-472b-43bb-8fc8-a4c959971582"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.795f45ae-12c8-43af-80c4-0f56dca0421d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.ab7cc296-1687-4013-8fa9-85a467e4d43d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.c5348944-52e4-4c58-8c9a-31d696363570"},{"outgoing":["b3ec852a-e60c-4309-8191-d4c835f309c4","8a5909e8-fc03-4a05-8488-7fa5e5c6137a"],"incoming":["9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66"],"default":"b3ec852a-e60c-4309-8191-d4c835f309c4","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":812,"y":117,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"4b183c21-5c24-45ca-87fe-35c1d100f0f5"},{"targetRef":"12ab068b-2bae-43ff-8700-067c4e7076c5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"b3ec852a-e60c-4309-8191-d4c835f309c4","sourceRef":"4b183c21-5c24-45ca-87fe-35c1d100f0f5"},{"targetRef":"4aef9d05-d8d9-4090-8733-d04a2c907886","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.iterator\t  &lt;\t  tw.local.BICcodes.listLength"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To MW_FC Verify Bank Bic Codes","declaredType":"sequenceFlow","id":"8a5909e8-fc03-4a05-8488-7fa5e5c6137a","sourceRef":"4b183c21-5c24-45ca-87fe-35c1d100f0f5"},{"startQuantity":1,"outgoing":["23804f29-ed90-4eba-854d-ee96704d1a31"],"incoming":["2027.240464da-d510-457e-8c69-83fade37dc0e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":92,"declaredType":"TNodeVisualInfo","height":70}]},"name":"init","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"90e1d8a8-e6ea-4b7a-844e-2aa49324caa3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.result = [];"]}},{"targetRef":"4aef9d05-d8d9-4090-8733-d04a2c907886","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To MW_FC Verify Bank Bic Codes","declaredType":"sequenceFlow","id":"23804f29-ed90-4eba-854d-ee96704d1a31","sourceRef":"90e1d8a8-e6ea-4b7a-844e-2aa49324caa3"},{"parallelMultiple":false,"outgoing":["f58d4483-18fc-4589-84d2-16539e2e4884"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"9def7546-0d03-47e0-89a5-74d2947314c1"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a10f232a-a529-483c-8ad9-ad7d8d5e2525","otherAttributes":{"eventImplId":"4fce72f0-1371-45a3-8c1e-79c17679c9b2"}}],"attachedToRef":"90e1d8a8-e6ea-4b7a-844e-2aa49324caa3","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":150,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8","outputSet":{}},{"parallelMultiple":false,"outgoing":["84cc0dbc-5e88-4888-8c0d-918cfc0f137d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3f9b3167-fc21-4de2-87b3-f3e85e58652b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fe329112-05f5-449f-841b-eb8f8568e920","otherAttributes":{"eventImplId":"285ad832-7480-49b3-8099-bcb868990439"}}],"attachedToRef":"4aef9d05-d8d9-4090-8733-d04a2c907886","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":150,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"21660292-55d1-4038-8e9b-899432e7b7b3","outputSet":{}},{"parallelMultiple":false,"outgoing":["173331ee-6d28-4b0c-8c54-b88cccfe833b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"1a716936-ba43-4e6a-8afb-cd35a4bb1448"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"583475e2-e8bd-4878-8f53-977fdd7dec75","otherAttributes":{"eventImplId":"56369708-efa0-4ac9-8553-b11915672d9f"}}],"attachedToRef":"1c764939-7ee6-44e4-842e-aa2d80c48514","extensionElements":{"nodeVisualInfo":[{"width":24,"x":651,"y":155,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"0b1dc456-0387-403b-8448-9039955eb705","outputSet":{}},{"targetRef":"b6cf3354-08b5-4d5d-8c91-8857217dc5e8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f58d4483-18fc-4589-84d2-16539e2e4884","sourceRef":"1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8"},{"targetRef":"b6cf3354-08b5-4d5d-8c91-8857217dc5e8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"84cc0dbc-5e88-4888-8c0d-918cfc0f137d","sourceRef":"21660292-55d1-4038-8e9b-899432e7b7b3"},{"targetRef":"b6cf3354-08b5-4d5d-8c91-8857217dc5e8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"173331ee-6d28-4b0c-8c54-b88cccfe833b","sourceRef":"0b1dc456-0387-403b-8448-9039955eb705"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.8587d735-f3d0-457d-8c68-f99436227ef3"},{"startQuantity":1,"outgoing":["e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3"],"incoming":["173331ee-6d28-4b0c-8c54-b88cccfe833b","84cc0dbc-5e88-4888-8c0d-918cfc0f137d","f58d4483-18fc-4589-84d2-16539e2e4884"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":463,"y":256,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b6cf3354-08b5-4d5d-8c91-8857217dc5e8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"\";"]}},{"targetRef":"12ab068b-2bae-43ff-8700-067c4e7076c5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3","sourceRef":"b6cf3354-08b5-4d5d-8c91-8857217dc5e8"}],"laneSet":[{"id":"5c9ead18-a428-4671-8b5a-b2273a0c30ea","lane":[{"flowNodeRef":["b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050","12ab068b-2bae-43ff-8700-067c4e7076c5","4aef9d05-d8d9-4090-8733-d04a2c907886","1c764939-7ee6-44e4-842e-aa2d80c48514","4b183c21-5c24-45ca-87fe-35c1d100f0f5","90e1d8a8-e6ea-4b7a-844e-2aa49324caa3","1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8","21660292-55d1-4038-8e9b-899432e7b7b3","0b1dc456-0387-403b-8448-9039955eb705","b6cf3354-08b5-4d5d-8c91-8857217dc5e8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":730}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"f06db042-1ad1-43a1-8b91-660260c8a923","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Validate BIC","declaredType":"process","id":"1.72840b39-03cc-42d8-a24b-01650b923101","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"result","isCollection":true,"id":"2055.876be99d-0b55-4982-8dae-2a839dc36cb6"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.20a3aced-fb07-4762-8811-1554963aba30"}],"inputSet":[{"dataInputRefs":["2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43"]}],"outputSet":[{"dataOutputRefs":["2055.876be99d-0b55-4982-8dae-2a839dc36cb6","2055.20a3aced-fb07-4762-8811-1554963aba30"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject[0].name = \"\";\nautoObject[0].value = \"CRVOIT21020\";\r\n\r\nautoObject[1] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[1].name = \"\";\r\nautoObject[1].value = \"00000000000\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"BICcodes","isCollection":true,"id":"2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="BICcodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43</processParameterId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "";
autoObject[0].value = "CRVOIT21020";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject[1].name = "";&#xD;
autoObject[1].value = "00000000000";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0ca22bf1-0807-4b39-b477-83c90d3c620a</guid>
            <versionId>76040ae2-3c0a-48a8-89c9-56988741e6ab</versionId>
        </processParameter>
        <processParameter name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.876be99d-0b55-4982-8dae-2a839dc36cb6</processParameterId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8007452a-b0eb-4288-b3f0-ea3293d37df7</guid>
            <versionId>e60e4c58-077c-4f5a-9d20-efcfacaca0b2</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.20a3aced-fb07-4762-8811-1554963aba30</processParameterId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>327eb05d-3872-41ca-a7cf-ea9bffa8cfd5</guid>
            <versionId>d41bb745-cc1f-474f-b4da-9a577905f28b</versionId>
        </processParameter>
        <processVariable name="iterator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.05cf9d4e-355b-4f96-8ff4-1fbef3fb5452</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>0</defaultValue>
            <guid>8575568b-f447-4181-bc69-8bec5ff687ee</guid>
            <versionId>61ebe383-2546-45e4-9745-5d36b29d4f39</versionId>
        </processVariable>
        <processVariable name="bankDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fc3eead2-472b-43bb-8fc8-a4c959971582</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.46ac92bd-1702-4b54-a984-d23fd4f07e1e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4690bc27-09b0-4dcd-829e-ce4dd2edc69e</guid>
            <versionId>10e84441-041a-427c-a485-40208c39f677</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.795f45ae-12c8-43af-80c4-0f56dca0421d</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2bdbf96e-0543-4f12-9bf7-27427a8eb4d9</guid>
            <versionId>5c4630c6-a0f3-46c1-b07c-ab850d736b4e</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab7cc296-1687-4013-8fa9-85a467e4d43d</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8c81ff56-fd62-401a-957c-67d1091b72a5</guid>
            <versionId>c2ad14fb-dbb9-41e1-a0ca-76492c67c05e</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c5348944-52e4-4c58-8c9a-31d696363570</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>false</defaultValue>
            <guid>a712f368-3968-4b18-a9ea-00ed889cbbc9</guid>
            <versionId>23490470-b599-4b7d-b6ac-de652189fbd0</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8587d735-f3d0-457d-8c68-f99436227ef3</processVariableId>
            <description isNull="true" />
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>be5017c6-cc60-4fc1-9d53-dec1c961c04d</guid>
            <versionId>86b4e1a9-4402-479e-8906-c661a5392080</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.12ab068b-2bae-43ff-8700-067c4e7076c5</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.40f5819b-5a94-43f1-b9e6-0223c250bbd5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c7f</guid>
            <versionId>000b0586-cf1c-4230-b3de-b06d8aa2ee98</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="956" y="118">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.40f5819b-5a94-43f1-b9e6-0223c250bbd5</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5e36cf94-7a23-4c46-92a3-f193784cdf06</guid>
                <versionId>c73db555-7ad5-418f-98df-ff4a367ce96b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1c764939-7ee6-44e4-842e-aa2d80c48514</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>Set Result</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.30e43a1f-ad88-4d4e-9a22-95d1fdf858a1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c5b</guid>
            <versionId>11e24f25-7287-474d-a54b-3f05e4171a0f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="616" y="97">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eff</errorHandlerItem>
                <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.30e43a1f-ad88-4d4e-9a22-95d1fdf858a1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	if (tw.local.isSuccessful) {&#xD;
		if (tw.local.bankDetails.bankName == null || tw.local.bankDetails.bankName == undefined || tw.local.bankDetails.bankName == "" ) {&#xD;
			tw.local.result[tw.local.iterator] = false;&#xD;
		}&#xD;
		else{&#xD;
			tw.local.result[tw.local.iterator] = true;&#xD;
		}&#xD;
	}else{&#xD;
			tw.local.result[tw.local.iterator] = false;&#xD;
		}&#xD;
	tw.local.iterator +=1;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>b83ec47b-9aa1-4380-8ce8-c0b160e71ea1</guid>
                <versionId>87585abe-2fe2-4854-9321-9c7d360695d4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6e0cc31c-fb27-4c59-83a4-5ae645611795</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eff</guid>
            <versionId>1660c06b-e71a-492a-945c-d57a2a51582a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="463" y="256">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6e0cc31c-fb27-4c59-83a4-5ae645611795</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</script>
                <isRule>false</isRule>
                <guid>7e086748-850d-4fcb-802c-a17edc85a961</guid>
                <versionId>1f4f4a07-ac00-4ce0-be6c-a024062a3843</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>MW_FC Verify Bank Bic Codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c74</guid>
            <versionId>3ba52955-f430-49eb-b7e4-29d03cbee662</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="92">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eff</errorHandlerItem>
                <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.97d9ac59-02bb-4969-8d24-6a7ac50478eb</attachedProcessRef>
                <guid>780271ce-23b8-47d1-accd-34035c78bb2a</guid>
                <versionId>4662ba23-2a5c-4648-86c7-c3311e86469b</versionId>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e01db1bb-4a4f-4ac1-98ef-61aab9ccc1ff</parameterMappingId>
                    <processParameterId>2055.9366d5b6-7327-4ce6-8d2e-6f1a97f58b13</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1134249d-7af4-4e7e-a0bf-147a0d08b178</guid>
                    <versionId>0686b8c1-e436-4a77-a726-01324f36ea64</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.04544092-1dad-4876-ac72-016e57af459a</parameterMappingId>
                    <processParameterId>2055.ceca8d8c-9d5f-48bf-891c-0cb500864329</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>58a7ed78-18e8-4c81-89ca-8a1cd24b5153</guid>
                    <versionId>14b5ba70-7c7c-4b8e-bf89-b9654775e29f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="bankDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.24772cf7-aa17-40a0-9da4-9f6dd4596ff7</parameterMappingId>
                    <processParameterId>2055.f0c79392-3e66-465f-88cc-000c55b27946</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.bankDetails</value>
                    <classRef>/12.46ac92bd-1702-4b54-a984-d23fd4f07e1e</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>febacaf4-5b3f-481b-b01c-072095941b05</guid>
                    <versionId>2934b2d2-cd20-4821-8d6e-edb25148e371</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ab0a20c2-5d58-492e-b3fc-efa3065d4750</parameterMappingId>
                    <processParameterId>2055.9cb71c49-55b3-4b55-81a3-a631fb8be2ff</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3bc96f74-27bb-45ee-a895-b600376a2566</guid>
                    <versionId>6d501ef9-00e9-4ea9-a82e-313b9a3b96d8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d3c5b8c3-**************-5a1a21f8cedd</parameterMappingId>
                    <processParameterId>2055.cb83c814-9f82-4ed8-8e8a-1615a27c64c6</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>95dcedf8-2e5d-4ffd-9620-d4b05d596074</guid>
                    <versionId>874221d8-85b2-4f1b-a46c-ae305d3dd630</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f2df6ee1-288d-4382-bebf-10814cc21e16</parameterMappingId>
                    <processParameterId>2055.8b9a0f6c-a79e-4bdc-8cad-65c1be0097aa</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4295f9f8-9500-46da-a01a-b71800b386ee</guid>
                    <versionId>8e60fd7a-efab-49e7-a270-f451066ea3f2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8e71d228-6df1-4a85-a663-b790f20fda7c</parameterMappingId>
                    <processParameterId>2055.98c3ed47-ba29-4e9f-8fad-177a4fac28d4</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>49f0c775-c7e8-48c1-973e-2e0a2f7147e4</guid>
                    <versionId>9e8c1c5d-bc7b-4390-8647-4d0ea78d3e88</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dab7b69e-208c-46d0-af7f-864b5a474eb8</parameterMappingId>
                    <processParameterId>2055.6cd6ca14-6b5c-4577-85f9-5484239a661a</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3d4fe907-5b5b-488b-bc30-4d1341f193f8</guid>
                    <versionId>a75eeb9b-1736-4dab-8d77-db38c4ab7d3c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="bicCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.df6f134a-7203-479e-86e6-dd3370730e8f</parameterMappingId>
                    <processParameterId>2055.eeb6d244-dfcd-4636-8e8a-35bf9302f544</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.BICcodes[tw.local.iterator].value</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>012829ac-6467-47db-a85d-1678e6e17e35</guid>
                    <versionId>b6c7d9f5-0536-4360-ab0e-88e669ad777e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.75f9f5b2-fd87-4bbb-8e70-3fcbfcdffb99</parameterMappingId>
                    <processParameterId>2055.f0e139e3-a4e5-4cf2-8717-00465b910710</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>fa2c4c0c-78ce-4657-8b77-de09263a0672</guid>
                    <versionId>ceda6baa-1630-42a3-bb0b-389afb73aa02</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.462e8cd5-d005-4af5-a1f7-68617656d244</parameterMappingId>
                    <processParameterId>2055.65bd3bdd-d81b-4da3-8aca-0a6e28dce6f4</processParameterId>
                    <parameterMappingParentId>3012.43fe8998-cd44-4f15-b12c-ae9fc47a486b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>fc5b4b53-afe4-4605-ace5-917fad1a4771</guid>
                    <versionId>fa757bd2-ceba-48dd-ba4f-6a75e99e2561</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e9ca69e9-d8e8-4e88-8e67-c9f0e6c794ce</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c33</guid>
            <versionId>96cee965-b561-4ac0-9039-5790ca672983</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="177" y="92">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eff</errorHandlerItem>
                <errorHandlerItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e9ca69e9-d8e8-4e88-8e67-c9f0e6c794ce</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.result = [];</script>
                <isRule>false</isRule>
                <guid>aa687edb-548f-4fb5-9aa9-35a3f966ccc0</guid>
                <versionId>c2396041-238d-4121-9220-cefceeffc9cc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</processItemId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.18a715a5-16e6-4135-ae1f-ea1785ae937e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c4a</guid>
            <versionId>b755943b-6f60-4088-bcba-ca3584835f98</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="812" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.18a715a5-16e6-4135-ae1f-ea1785ae937e</switchId>
                <guid>9e83fcb5-40ff-4428-b7a7-eb9ec1c3a659</guid>
                <versionId>a12b024e-add9-4edb-b8b5-ee800e0dd022</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.e0774169-0d3e-4570-a08b-51aaffa53662</switchConditionId>
                    <switchId>3013.18a715a5-16e6-4135-ae1f-ea1785ae937e</switchId>
                    <seq>1</seq>
                    <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:57e8</endStateId>
                    <condition>tw.local.iterator	  &lt;	  tw.local.BICcodes.listLength</condition>
                    <guid>e392d0af-e6a4-4a25-8945-a526e2b2bee4</guid>
                    <versionId>9ab5e8ad-0b43-49cf-a5f1-d1fed7500f91</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="115">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Validate BIC" id="1.72840b39-03cc-42d8-a24b-01650b923101" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="BICcodes" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "";
autoObject[0].value = "CRVOIT21020";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject[1].name = "";&#xD;
autoObject[1].value = "00000000000";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="result" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" id="2055.876be99d-0b55-4982-8dae-2a839dc36cb6" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.20a3aced-fb07-4762-8811-1554963aba30" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.876be99d-0b55-4982-8dae-2a839dc36cb6</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.20a3aced-fb07-4762-8811-1554963aba30</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5c9ead18-a428-4671-8b5a-b2273a0c30ea">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="f06db042-1ad1-43a1-8b91-660260c8a923" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="730" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>12ab068b-2bae-43ff-8700-067c4e7076c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4aef9d05-d8d9-4090-8733-d04a2c907886</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1c764939-7ee6-44e4-842e-aa2d80c48514</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4b183c21-5c24-45ca-87fe-35c1d100f0f5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21660292-55d1-4038-8e9b-899432e7b7b3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0b1dc456-0387-403b-8448-9039955eb705</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b6cf3354-08b5-4d5d-8c91-8857217dc5e8</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="115" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.240464da-d510-457e-8c69-83fade37dc0e</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="12ab068b-2bae-43ff-8700-067c4e7076c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="956" y="118" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c7f</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b3ec852a-e60c-4309-8191-d4c835f309c4</ns16:incoming>
                        
                        
                        <ns16:incoming>e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b5b2ae7d-7f11-4b54-81f2-bab6c6e3e050" targetRef="90e1d8a8-e6ea-4b7a-844e-2aa49324caa3" name="To init" id="2027.240464da-d510-457e-8c69-83fade37dc0e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.97d9ac59-02bb-4969-8d24-6a7ac50478eb" name="MW_FC Verify Bank Bic Codes" id="4aef9d05-d8d9-4090-8733-d04a2c907886">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="92" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8a5909e8-fc03-4a05-8488-7fa5e5c6137a</ns16:incoming>
                        
                        
                        <ns16:incoming>23804f29-ed90-4eba-854d-ee96704d1a31</ns16:incoming>
                        
                        
                        <ns16:outgoing>e692f4fa-9b1a-48a2-8349-191d502eb670</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.eeb6d244-dfcd-4636-8e8a-35bf9302f544</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.BICcodes[tw.local.iterator].value</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9366d5b6-7327-4ce6-8d2e-6f1a97f58b13</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ceca8d8c-9d5f-48bf-891c-0cb500864329</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.98c3ed47-ba29-4e9f-8fad-177a4fac28d4</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cb83c814-9f82-4ed8-8e8a-1615a27c64c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6cd6ca14-6b5c-4577-85f9-5484239a661a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b9a0f6c-a79e-4bdc-8cad-65c1be0097aa</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f0c79392-3e66-465f-88cc-000c55b27946</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.46ac92bd-1702-4b54-a984-d23fd4f07e1e">tw.local.bankDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.65bd3bdd-d81b-4da3-8aca-0a6e28dce6f4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9cb71c49-55b3-4b55-81a3-a631fb8be2ff</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f0e139e3-a4e5-4cf2-8717-00465b910710</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="4aef9d05-d8d9-4090-8733-d04a2c907886" targetRef="1c764939-7ee6-44e4-842e-aa2d80c48514" name="To Exclusive Gateway1" id="e692f4fa-9b1a-48a2-8349-191d502eb670">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-581f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="iterator" id="2056.05cf9d4e-355b-4f96-8ff4-1fbef3fb5452">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">0</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Result" id="1c764939-7ee6-44e4-842e-aa2d80c48514">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="616" y="97" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e692f4fa-9b1a-48a2-8349-191d502eb670</ns16:incoming>
                        
                        
                        <ns16:outgoing>9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	if (tw.local.isSuccessful) {&#xD;
		if (tw.local.bankDetails.bankName == null || tw.local.bankDetails.bankName == undefined || tw.local.bankDetails.bankName == "" ) {&#xD;
			tw.local.result[tw.local.iterator] = false;&#xD;
		}&#xD;
		else{&#xD;
			tw.local.result[tw.local.iterator] = true;&#xD;
		}&#xD;
	}else{&#xD;
			tw.local.result[tw.local.iterator] = false;&#xD;
		}&#xD;
	tw.local.iterator +=1;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c764939-7ee6-44e4-842e-aa2d80c48514" targetRef="4b183c21-5c24-45ca-87fe-35c1d100f0f5" name="To Exclusive Gateway" id="9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.46ac92bd-1702-4b54-a984-d23fd4f07e1e" isCollection="false" name="bankDetails" id="2056.fc3eead2-472b-43bb-8fc8-a4c959971582" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.795f45ae-12c8-43af-80c4-0f56dca0421d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.ab7cc296-1687-4013-8fa9-85a467e4d43d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.c5348944-52e4-4c58-8c9a-31d696363570">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:exclusiveGateway default="b3ec852a-e60c-4309-8191-d4c835f309c4" name="Exclusive Gateway" id="4b183c21-5c24-45ca-87fe-35c1d100f0f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="812" y="117" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66</ns16:incoming>
                        
                        
                        <ns16:outgoing>b3ec852a-e60c-4309-8191-d4c835f309c4</ns16:outgoing>
                        
                        
                        <ns16:outgoing>8a5909e8-fc03-4a05-8488-7fa5e5c6137a</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b183c21-5c24-45ca-87fe-35c1d100f0f5" targetRef="12ab068b-2bae-43ff-8700-067c4e7076c5" name="To End" id="b3ec852a-e60c-4309-8191-d4c835f309c4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b183c21-5c24-45ca-87fe-35c1d100f0f5" targetRef="4aef9d05-d8d9-4090-8733-d04a2c907886" name="To MW_FC Verify Bank Bic Codes" id="8a5909e8-fc03-4a05-8488-7fa5e5c6137a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.iterator	  &lt;	  tw.local.BICcodes.listLength</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="init" id="90e1d8a8-e6ea-4b7a-844e-2aa49324caa3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="92" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.240464da-d510-457e-8c69-83fade37dc0e</ns16:incoming>
                        
                        
                        <ns16:outgoing>23804f29-ed90-4eba-854d-ee96704d1a31</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.result = [];</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="90e1d8a8-e6ea-4b7a-844e-2aa49324caa3" targetRef="4aef9d05-d8d9-4090-8733-d04a2c907886" name="To MW_FC Verify Bank Bic Codes" id="23804f29-ed90-4eba-854d-ee96704d1a31">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="90e1d8a8-e6ea-4b7a-844e-2aa49324caa3" parallelMultiple="false" name="Error" id="1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="150" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f58d4483-18fc-4589-84d2-16539e2e4884</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9def7546-0d03-47e0-89a5-74d2947314c1" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a10f232a-a529-483c-8ad9-ad7d8d5e2525" eventImplId="4fce72f0-1371-45a3-8c1e-79c17679c9b2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="4aef9d05-d8d9-4090-8733-d04a2c907886" parallelMultiple="false" name="Error1" id="21660292-55d1-4038-8e9b-899432e7b7b3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="150" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>84cc0dbc-5e88-4888-8c0d-918cfc0f137d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3f9b3167-fc21-4de2-87b3-f3e85e58652b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fe329112-05f5-449f-841b-eb8f8568e920" eventImplId="285ad832-7480-49b3-8099-bcb868990439">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1c764939-7ee6-44e4-842e-aa2d80c48514" parallelMultiple="false" name="Error2" id="0b1dc456-0387-403b-8448-9039955eb705">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="651" y="155" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>173331ee-6d28-4b0c-8c54-b88cccfe833b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="1a716936-ba43-4e6a-8afb-cd35a4bb1448" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="583475e2-e8bd-4878-8f53-977fdd7dec75" eventImplId="56369708-efa0-4ac9-8553-b11915672d9f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c0d79ac-4ab7-4c41-8e2e-2e1160bfb6e8" targetRef="b6cf3354-08b5-4d5d-8c91-8857217dc5e8" name="To End Event" id="f58d4483-18fc-4589-84d2-16539e2e4884">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="21660292-55d1-4038-8e9b-899432e7b7b3" targetRef="b6cf3354-08b5-4d5d-8c91-8857217dc5e8" name="To End Event" id="84cc0dbc-5e88-4888-8c0d-918cfc0f137d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0b1dc456-0387-403b-8448-9039955eb705" targetRef="b6cf3354-08b5-4d5d-8c91-8857217dc5e8" name="To End Event" id="173331ee-6d28-4b0c-8c54-b88cccfe833b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.8587d735-f3d0-457d-8c68-f99436227ef3" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="b6cf3354-08b5-4d5d-8c91-8857217dc5e8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="463" y="256" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>173331ee-6d28-4b0c-8c54-b88cccfe833b</ns16:incoming>
                        
                        
                        <ns16:incoming>84cc0dbc-5e88-4888-8c0d-918cfc0f137d</ns16:incoming>
                        
                        
                        <ns16:incoming>f58d4483-18fc-4589-84d2-16539e2e4884</ns16:incoming>
                        
                        
                        <ns16:outgoing>e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b6cf3354-08b5-4d5d-8c91-8857217dc5e8" targetRef="12ab068b-2bae-43ff-8700-067c4e7076c5" name="To End" id="e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e22d85ec-aa9e-4fe7-8c6a-0b09b740b3a3</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.12ab068b-2bae-43ff-8700-067c4e7076c5</toProcessItemId>
            <guid>74bfafe0-924d-45fe-81aa-0e61ef7eedc7</guid>
            <versionId>3bbe3573-dc30-4a20-8edc-fe47ef8569c9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.b6cf3354-08b5-4d5d-8c91-8857217dc5e8</fromProcessItemId>
            <toProcessItemId>2025.12ab068b-2bae-43ff-8700-067c4e7076c5</toProcessItemId>
        </link>
        <link name="To MW_FC Verify Bank Bic Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8a5909e8-fc03-4a05-8488-7fa5e5c6137a</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:57e8</endStateId>
            <toProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</toProcessItemId>
            <guid>6dd60f49-1b5f-4576-bc38-0f1b4a0324b2</guid>
            <versionId>64b1b310-31a0-4404-9777-8757e50ee952</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</fromProcessItemId>
            <toProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9db8e6d2-ae39-4b66-8ee0-0f7a13d19c66</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1c764939-7ee6-44e4-842e-aa2d80c48514</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</toProcessItemId>
            <guid>fa1f63b0-6efe-4936-967b-614ad9e7e30a</guid>
            <versionId>7761d4a0-88fe-4c96-96e5-40e181f3b4c0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1c764939-7ee6-44e4-842e-aa2d80c48514</fromProcessItemId>
            <toProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</toProcessItemId>
        </link>
        <link name="To MW_FC Verify Bank Bic Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.23804f29-ed90-4eba-854d-ee96704d1a31</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</toProcessItemId>
            <guid>e94dc118-6802-4071-98ea-dc746b14393f</guid>
            <versionId>8a9a55c1-cd3f-4609-9a4f-6a782dde6dfc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.90e1d8a8-e6ea-4b7a-844e-2aa49324caa3</fromProcessItemId>
            <toProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b3ec852a-e60c-4309-8191-d4c835f309c4</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.12ab068b-2bae-43ff-8700-067c4e7076c5</toProcessItemId>
            <guid>de284c65-9060-4e1a-ba0d-08d539af4dbf</guid>
            <versionId>e9c99c86-3b34-4dfd-af01-730232a8b896</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b183c21-5c24-45ca-87fe-35c1d100f0f5</fromProcessItemId>
            <toProcessItemId>2025.12ab068b-2bae-43ff-8700-067c4e7076c5</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e692f4fa-9b1a-48a2-8349-191d502eb670</processLinkId>
            <processId>1.72840b39-03cc-42d8-a24b-01650b923101</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</fromProcessItemId>
            <endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-581f</endStateId>
            <toProcessItemId>2025.1c764939-7ee6-44e4-842e-aa2d80c48514</toProcessItemId>
            <guid>88fc9e5d-0fe1-496c-adac-df216f7584ea</guid>
            <versionId>eb25078b-3405-4c9e-9f10-525df507e17e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4aef9d05-d8d9-4090-8733-d04a2c907886</fromProcessItemId>
            <toProcessItemId>2025.1c764939-7ee6-44e4-842e-aa2d80c48514</toProcessItemId>
        </link>
    </process>
</teamworks>

