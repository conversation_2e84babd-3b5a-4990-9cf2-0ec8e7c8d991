<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5" name="Contract Liquidation">
        <lastModified>1692257399791</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;64008dad-b373-44c9-8a49-2cfa730e00d4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ContractLiquidation&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9eb6ce8f-7d47-4321-82df-afc2f093651e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Contract Liquidation&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b2a8b52-56b4-45fa-82f5-06be9bf86d3f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0286872-0b23-40c1-8212-a112083428e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06b0e628-48e1-4e09-8ff6-5e0399711d3c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ec7c5e89-3eef-4583-87de-6c8ab599ec39&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3049375-b305-43d3-8f8c-a1b9ad61f625&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;19128fcb-79f8-48eb-8d90-0dda7a561f1c&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;dd73714c-93f3-4cf9-83bd-7d3afe4cc77c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;IsChecker&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e03a567-e7de-4f0f-8e81-704e88ef6863&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Is Checker&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;62927d69-5ed0-4169-8f46-808637a8cc2c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;24d4fad8-f4be-4fcf-886a-6f907405a531&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.9b679256-e93b-4400-89f2-bd15b0c5578d&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.isChecker&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;017b7adc-a02f-493d-87d4-ca2dcd32f1b5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0bf9bee2-8ffb-4692-8ef6-46b58ec503bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Liquidation Summary&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b299696-77b7-4bc4-8b75-67cfdac1644c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;29f2e6f0-2750-47a8-89e8-cfce1577a3b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f07d10b7-b399-46fe-80de-7006d09dc047&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;2b43c343-23c4-4056-8623-85bdba368c9f&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;9a67fe50-bc2c-4289-8962-375e1840805a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db093927-756f-4df5-851f-2b64d8ae48e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;fbd77cd5-ec24-401f-877c-3d21318254b5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;02c1a42e-6611-4851-8c24-a6f154e3325d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;63aed7aa-b098-4dc5-820b-90c218678235&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;39543b26-894e-4d50-8798-73af29198d8f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e22f1db5-829e-48de-8c4e-3419aec89280&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;LiquidationAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d26862f-0e1e-446e-a3a9-1e338c01e87a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ebd61d8-81c2-4a03-837a-b178c6f208a8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.LiquidationAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bf0e6cd5-d1f7-4b50-88c6-baccd48ed21b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4f08811-67ee-4acf-8948-da3423ae7dda&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;846f5b6d-eba8-44aa-84b6-bcf6dbaf1c49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.dAmtLiqCurSum();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.liquidationSummary.liquidationAmt&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d1c2b908-96af-4c8f-8077-832e65eeb246&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitValueDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3f031915-9e31-4389-868b-4bcf06517bbd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitValueDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e7ba6bd-3cd2-4849-85de-bc8e8764b3bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47b1d34a-77db-4f6e-8a8d-50350d4edea0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36cccd08-c5cb-41cb-8cc1-b4dff5195b58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d78f95bc-425d-486a-8241-c16f2a492294&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;268aa232-ab6a-4071-8aad-0b6e92c7a606&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89335233-9dc4-44f5-8473-fa216a73dfe7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;startDate&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.liquidationSummary.debitValueDate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;adea9d34-cdea-4c65-8707-6ace80b98a94&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitBasisbyAmountPercentage&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35597965-ed1e-46b1-8d74-474aa0ed9e61&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitBasisbyAmountPercentage&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b64634d7-36fd-4040-8fe3-c59eb84a5182&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f9cc5f0-06dd-4362-8ee3-dc6f99498d42&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6d35aeeb-a033-40a1-8532-dbb0a095882b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;559f992a-8bae-452e-8fe4-12e3122a0a6c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8c42b263-f239-4cd8-8efa-d34194bd9970&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4d23170-23c6-4b13-8756-46a0c18952ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31018bef-ffc5-43d6-854b-2797f45fac1d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Amount","value":"Amount"},{"name":"Percentage","value":"Percentage"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9810758e-9708-497e-85a7-c096b7e0279a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setDebitPercAmount(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e2e60d8-40e6-4baf-8ccd-8ec49d8070c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.liquidationSummary.debitBasisby&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3efa373f-cc38-4a41-8052-afc52462a7f0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AddAccountBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b46487e-625e-409a-81c4-c29176face6a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Add Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;953ff384-46fe-414a-8b43-9551e3504dbd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;114c2312-e2cf-4a21-8930-3955c2be3af2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b0833c1-4411-45af-8255-663a36aeeac1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;759054a2-f16b-4564-80ad-066816255caf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0784adc9-e457-476d-8386-702049cd72bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2af488f0-1ecc-4096-8468-55e0e862b865&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.addAccount();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;9f682ba3-9d47-4256-89f2-be773c2a1d31&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;44a6ae82-c6e1-4f7c-89f6-65b74c10b9a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2d4f8cfb-a991-4916-8bda-6efe2395a8e3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;LiquidateionCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc89dcc9-8212-423a-81d3-793edf2237f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.LiquidationCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b79f0eb-d4b0-4001-8961-7dadbbfd62cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a217931-e333-44e5-8017-d672a5e8876d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8459bb0c-8cd9-4561-8728-fa5dfee89df4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.liquidationSummary.liquidationCurrency&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cc804b0b-3fa9-4b58-8cfa-abbf32ecd643&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CreditValueDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d476bb94-c96b-445f-8ee1-1387690cecfd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CreditValueDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ec494eb7-0865-471a-80fd-e0e96a7b4de5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b3351a3-e4d7-4b4b-8ff3-22c0030b4c90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b858bba5-8c96-4c1a-8f93-d1164c9916ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4d5ab61-3d66-448a-8c6b-8b4bbc3fe5da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;24c2dde9-0184-4732-8eae-eab7f939bd0c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.liquidationSummary.creditValueDate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8466b9ed-dee5-44af-888c-2904e82209dc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Settlement&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;669bcaac-3bf8-456a-8483-14f39d884305&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea3ebea6-9d9b-49d4-8b6f-f239af434cb3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc1aaa70-3ce9-44df-82b4-3f64d13a933e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;658d3a28-2d8c-4959-8961-4f6277e37f46&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;6ac38661-e2e2-492c-8a6e-c304fb35dc27&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2083048a-1d49-4fb8-8385-00bc022064f9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_panel2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ecebc3e-040f-46f1-8eee-de28ea4cb301&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Settlement (Debited) Accounts&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df676eb7-6a2b-40be-86a4-e3a736505875&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a36b5be7-ff3e-4ed4-816a-badafbf0786b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9ea3c802-595f-4e90-8921-e73d05ee040b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;94ab3cb5-f315-4a04-8525-cda5678de443&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;5acf21f6-b1c4-4008-8917-ef0478209f41&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5168919f-f54b-45ee-8349-540e1b6b3e9a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAccountDetails1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;711ef860-b0b6-412f-827e-f5e7f91790f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Account Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50021d36-58a5-45e3-88fc-0120178297af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b5582074-800d-4645-8c8d-23339ed1af64&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e7cb4f8-a4e9-4ee8-8da7-6f0837235a4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;688b27a1-efd6-4538-8d2c-5c7408cc34b4&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5a6c41df-420a-4821-8290-c2552aadc4b4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;059a6021-b8e1-46f3-8eee-5967ea2702b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4178e81-a1f5-49df-8e5e-9eb7ef27d821&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c2d28ae7-306d-423b-8ac0-f8f0b7d2581a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f445ce1-1e30-424c-8ada-a7b71002f749&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@margin&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"0px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;18853e01-d2a0-4fc1-853e-fe1e8e76bb33&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;115db6b4-b894-464f-8936-a4c1e0e82122&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87569bb2-e7e4-4dc9-885d-b6b102b625ba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;562f34d9-0f80-48b6-8084-b672c5af1d55&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a230880e-fdce-4d34-8219-81fb02daa0ba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ea7fc60-0215-46e1-81f6-e6facd9cab5c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"50%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;fd63a3a9-d8a7-4d8a-8273-c9ef0c3e920d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6fe4b163-7e1c-443b-86da-94ee16f77a1c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountClass&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c7d0e18-de8d-4968-8fe5-d071f4a2c16f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountClass&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb3f6a60-9f5f-4741-8c08-033078d98057&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c78ac4e6-79ec-498d-8604-0790b767e371&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d93a9830-8d71-4fc6-862c-bbc4a904fb24&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89c7932d-080a-4651-8f5f-5f1d75910ce5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8dc7324-72a5-43df-8d19-5e6e046549b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b07eb84-8a38-4dca-8091-0323fb873602&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Customer Account","value":"Customer Account"},{"name":"GL Account","value":"GL Account"}]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03e22311-758d-431b-89e4-dcdf7a1b122c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeService(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.accountClass&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fff1b78d-4658-487c-8411-285bfab42c51&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c31e815b-9cca-4d4a-8951-2033ca8785eb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93b16fc0-54d8-4c1b-8250-f20add322a97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9edf7903-a46e-4dcf-86df-0cf8a26bc481&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d730d183-3506-431c-8c98-905db2d5be96&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;eb6b3a97-a443-4b9f-8f7f-92a0f46eac9e&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8ba525d0-93e3-4ac4-8dba-87abb28e7fd9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountNumberGL&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a92000b-53a3-4137-8d29-14a858830aec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b0c0c52a-f852-4020-846e-ce59f04682d8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.GLAccountNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0ff2d137-be95-4e82-8188-8fa1cbf20829&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93e2dc73-5d44-460a-8c3e-960526afdf6f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1730a1fb-ccf8-4ffa-8a15-49e3aabf3698&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1acf9134-d8d6-44d1-8144-94e6ac02898b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetGLButton(me);


&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee576e6d-a11c-4606-86ec-b2d3bc183e77&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 9){
	me.setValid(false , "must be 9 characters");
//	me.setData("");
	return false;
}else{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.GLAccountNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;184a9deb-5f24-4663-8957-289c527a6008&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;VerifyGLAccountBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a53819fc-4b21-4778-8ebe-88218a4dfa7f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Verify GL Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ca03850-ead3-4278-8b19-9afa776b6ead&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;98a08649-99bf-4cec-88fd-5d53b7e8fb0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b4e733d-20d8-4f31-8766-3c39306d6acf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;304138ec-dbc6-426a-8fa5-eea82d25f292&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d0328e4a-d958-4ff9-8101-8bb0cffca98f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeServ(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e66c01d3-9b56-4553-8825-c94a014daa84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bf4950cc-e5af-4eb7-8a9c-38c0aec256e3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77551d96-9c5b-40c5-8d71-742ce1f5a901&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e835ba8-9a2f-4a03-86e1-8864333b209f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.isGLVerifiedC&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;755776d0-e7fd-4e7d-8400-9acd3014b447&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4eda7b15-5a51-4849-8da5-9c3a1d3d302a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5dcd40fa-47f2-4a0e-8ee4-0009d16f2910&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;098b198c-de12-4c0d-802e-a2cf8a54ddc8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af4de72a-4f68-44d4-8ec1-d93d5bea70d4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a1910de-7785-4ccc-844b-9493003d3007&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a9e2c9d-7bcc-4e82-822a-0026918be770&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6a1ced53-e512-403d-8de2-16e53aa25444&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b0bda98-e854-42b3-8aca-bf530ea9ee58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;44b79f0b-06c2-402c-8654-49883c4ddb79&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExchangeRate(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38b47343-b003-461c-842f-0f398daae853&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.accountCurrency.code&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;87d95487-8d85-4009-8046-9a9471ea5178&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a6a3697-062f-401b-8363-9ade59d2fa99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BalanceSign&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;149fe0bc-7b42-4e6c-845c-ddb7313797fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59e761b9-63e3-4a16-827e-55f0d09018e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6dfe788-5549-4aa6-8a48-aeb38464fa16&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.balanceSign&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;23b7503d-cd0b-4eb2-81bc-145e678f3ed6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetCustomerAccount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c6323b4-2ba8-4e60-86b6-5fe2ebb302cd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Customer Account Info&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c003202-877f-4841-8318-67703d8aad93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5761177-47fe-471e-86f7-3a49a29eb694&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;219d066b-2ca5-4b5f-88ce-3aab461e6264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;autoRun&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b596eeb-7bf9-4aad-8ad4-20b4d368bdc4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.753ea3e8-d234-409e-9fb2-015400e4c9fb&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;200ee373-0584-45ed-862c-3447e669bbcb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b6f9a6ad-9724-438f-8cb8-347ac11aec95&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setSettAccounts(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;55d7f710-024d-4d2d-87f2-8580401a6fab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83aa348f-2281-4a34-8df0-2a7991b3d85d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9318c321-2a82-4c46-84ab-881aff0f9cc2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.sAccountList[]&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7bec45b6-4c9f-446b-8718-39abb1212723&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f9692171-b7d4-4094-826c-1099a16d6229&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6e7df79a-8d13-4884-84ac-ef0f8527a6d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5187d560-3780-45c6-81fe-7d6fc4934e45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c6054020-1c0f-40d6-8a19-57006c743a54&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9f2cd60e-2ff0-40fe-80c1-ade0abf0f389&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Owner1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;710f4934-9445-429d-8acd-6c9fa189c73e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ownerAccount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f8503fe6-37a8-46dc-847c-c2356a280d65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af653e82-5488-4923-8ed1-994ac88c1985&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6a9f63f9-b75a-482a-8ba3-426c6534b873&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9653e231-3f5f-4998-89b6-fd216ed740d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Drawee","value":"Drawee"},{"name":"Case In Need","value":"Case In Need"},{"name":"Accountee","value":"Accountee"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b0ca1dcf-ef73-4262-873c-b771f4eba3f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setOwnerCIF(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba73eb53-7638-4471-8321-5cbeedad1611&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;42784a79-7caf-45a6-803a-f17d7338c71f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6944154f-bbbd-4fbb-84f1-9aadc6e67f8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.ownerAccounts&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b282db09-c352-443b-81cc-faa093de70e2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d53264ce-00e5-488c-83b0-898c6710a2e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc7e2b71-b707-4e38-8e7b-636a859903f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6eab5c4a-9c5a-46ee-804a-ac05ae08fd84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee146d60-8eca-4b9d-8800-81118891a1dc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d9202da-d716-4aa4-88a6-8d64825d1a7b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;390b3ba7-db27-4ec0-8a9c-d731f4463eab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d563f47-60e0-4773-8968-8a691fde4e0c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.accountNumberList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;80696bcd-f5ea-4ad4-8164-87c9045accc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.accountVis(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95db1201-0ca0-4d3a-8850-b1e2d6671f65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setAccountInfo(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6dfb782e-d77f-4510-8dda-b6cec0e30194&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.accountNumberList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d24b3c81-30d9-4f1b-87c3-ddeaeea47193&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.accountNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;63cccbbb-9c8c-4852-8a2a-168a6a986a98&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountBranchCode&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9dbf809a-06c7-4fe3-841e-82beec2b325d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;214e2c12-8f90-4685-8b8f-ad4a82e9420d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountBranchCode&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;78aa0de3-8822-4ae4-8eeb-207cd993393e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1560a804-35a0-49f2-86ce-eaf89f91515b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;96e26a31-cf4b-4ca1-8c0b-a7cb67e23029&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateDigits(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fedbf534-d85c-4696-8959-c91fb8006392&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e82a882a-cb45-4c6e-8cdd-7ddf091d561d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 3)
{
	me.setValid(false , "max lenght 3 Digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.accountBranchCode&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0e5d960e-923b-4407-8432-c7903cce39f5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountBalance&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d778acb-998d-4b50-85e5-62c238449ca8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d2838c3e-3482-43bc-80f9-2bcda9f9453b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountBalance&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ca025eb-8227-4cee-899a-f56ed44f767f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;60d889ba-0090-4f68-8bbc-a817fa1eef22&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5743478d-8d86-4641-8535-e73f04bf8798&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e205b312-40ad-4a4e-87ec-cb6a222a80ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.accountBalance&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f7204641-8a2f-427a-8576-d6d02bfcbd69&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CheckexistingGLAccount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e9694e0-79d0-4439-8316-39e8e6770ab9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Check existing GL Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;208cae0b-def1-4630-8327-f97a2d5aed61&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae228780-b84d-4e76-8a2b-0f58d38435a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2979b681-e41d-403d-8a07-f70efd306ca6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.417ae6ff-837a-426f-b651-d8e3e8810d90&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-8b14-46a6-8eb8-c62163d26cac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.GLAccountNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d3b85e0-fe92-4560-8758-a5895657e57f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validGlAccount(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b368d76a-7660-4d47-8c88-bad3c9204827&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAccount.isGLFoundC&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;779ea393-4e15-4b17-8141-679110add35f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAmount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c35a326b-5dac-4659-89ea-5f8e37121466&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Amount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e344b31b-9d3f-4092-85e5-4a37aebf20ac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8869bc86-d265-45e9-8cc5-128da94492cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3fb518d0-09b8-478e-8eb4-c9f1f1ceea6d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;899b02d0-b847-4642-829a-f586cde2df0e&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;7e00f622-efbe-4897-85a3-1608a0ce99b5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe31a566-8a4b-4c3a-8c64-63b00456c044&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;7f14d494-5407-4717-82b4-7ec68301d5a5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2c04609-cbde-44da-8431-baba69559f0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;17d941ec-65ef-49e2-8f7b-1ebab1d4aff6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4dbd474-8eb6-4737-8b83-d1436b9eccdd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"SmallID","value":12},{"deviceConfigID":"LargeID","value":6}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;98084f4e-7845-445b-8bfa-8877a533100d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;StandardExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51eab65e-ad73-4cb3-8cc3-4040ccc37f7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a10d514-1a94-4a98-8782-052e60a096db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.StandardExchangeRate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;25b960a5-d464-4122-8bed-c97429474b6d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb031753-bf21-4cba-8fa1-b617b89156f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c9bde61d-b13d-4f80-8233-7cd5a3ace601&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAmount.standardExchangeRate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ae56dc7b-a626-42ed-83f9-199716f954e6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitPercentage&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;60f0eb0d-150d-43cd-aaf8-d07368001c5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6a4ce07-4f01-491e-882a-14c37770a580&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitPercentage&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b98ae26d-cfb8-4402-8276-fa88e479b433&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;351421c6-bef7-4adf-8ff8-09888d40faf8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8d974d7-cf11-4fb0-8965-73bdf42add84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(${DebitBasisbyAmountPercentage}.getData() == "Percentage" &amp;amp;&amp;amp; ${IsChecker}.getData() == false){
	me.setEnabled(true);
}else{
	me.setEnabled(false);
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f3f83c2f-653d-439b-860b-d661d1bf4a25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.dAmtLiqCurSum();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAmount.debitPercentage&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0594c6eb-03ac-4459-8c4f-aa3441613abd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAmountinAccountCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f79fc614-dfc6-4e8f-b84c-e5eca4f7e6d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72166a5e-0568-4a6c-85c3-3ca51d42d07b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitedAmountinAccountCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b01cdd11-03e5-415d-8dbd-b8bc1f1593e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;79e34316-b82f-43bd-86a5-abee1bd3672a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f508c6a7-8167-4fe4-81cb-134beb847976&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cf243a85-a094-4f2a-820e-a89b25b2994e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateOverDraft(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAmount.debitedAmtinAccountCurrency&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;31b559a3-191e-4686-85af-5edaaa3f23ed&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22759b9b-2189-4d53-8224-9143140a432f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;54f3bfca-8248-40ea-8279-d449cc8c5579&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;NegotiatedExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82d97378-e12d-495e-870b-25b64e8ed76e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43daafb4-f03c-4bfa-8c82-b89ec9ee138a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.NegotiatedExchangeRate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0438f462-fc20-4c63-8877-0fa0f2cd864d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;63bed372-8a22-4836-8b00-a6e5be88775f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f662c1a7-e625-455d-8bd6-0f5757607615&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hideThousandsSeparator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0106b610-3334-428a-8982-9b3785a9243e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.dAmntAccountSum(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d7ed04c-d47a-4910-8187-b4d1c87845fb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAmount.negotiatedExchangeRate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2f235387-b491-4399-88f4-dd86791c472a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAmountinLiquidationCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0179bc41-b679-4f9c-8c8c-a177dfdb52ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef285078-d32a-47c7-8b38-e6ae21e2dd33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitedAmountinLiquidationCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f6fb40b-fa32-4929-822a-63ae2c7efd68&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;74f64075-56fc-4a1c-898e-6eb79a26d36a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff664839-afc2-4795-848c-ee28b22254b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2e453fad-9db9-4c47-847e-b92f7602859f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.dAmntAccountSum(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;734a690b-1854-4c0f-88a9-84278386dfc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(${DebitBasisbyAmountPercentage}.getData() == "Amount" &amp;amp;&amp;amp; ${IsChecker}.getData() == false){
	me.setEnabled(true);
}else{
	me.setEnabled(false);
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.settlementAccounts.currentItem.debitedAmount.debitedAmtinLiquidationCurrency&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ac2ea645-f6fc-4d75-8e31-35a95d52e84b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;74f6e74f-3083-4a32-8ce6-b811fc274d00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Exchange Rate&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;243f2129-e09d-4271-892c-a13bd6fc78de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8b1786e-af78-4782-81a6-aa4cf3aa32da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f21c0c03-88bd-4bfb-824b-f7d13d91ec4e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.55393dcd-3352-41df-9690-75cb207d48b8&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ca34fe2-5079-4ee1-80a2-65d137415fdb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;037e0a0b-80cf-486e-82da-fbe2e6767628&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExServiceResults(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d3c1008e-a008-4880-8b6a-3bd15212acd2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.exRate&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;25a9f90f-bb56-4b5d-8187-59fe0f20fbab&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b67b2bb1-693d-45a0-8c0f-ee86d0dd3c4b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;beee35e4-1f2f-42e2-81a1-5ee01ad929a8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9edb20ab-f332-46ee-8a37-bba525ad3bac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f9e8e57-d161-4229-82ed-98f832beeec2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ef9444c2-6ebd-49e8-81d6-8a216e648122&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;67fcf923-c0e4-4523-83c9-6d89d4ecbaef&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;RemoveBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd81aa15-115e-4cf0-8345-54ffa04812c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Remove&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f3841701-7747-4a49-860a-79a583f71b45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e89cfa7a-b4fb-4dbf-8981-65ebca345fae&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bcf3f769-9a62-495c-8d64-0eb7f838f23e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4007438a-d7b7-40df-833c-1849b87e5bb9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad01b34c-102b-480a-8f4b-14963afc4adb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a82b2171-74c1-4b0d-8c7b-b4019588767a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.removeAccount(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;df3f18f0-ec3c-4aeb-8d9e-975dc3535f65&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CreateLiquidation1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ac8d31e4-2927-428d-8e40-63376fca6cb1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Create Liquidation&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0508a8e9-f3cd-4332-8188-2b4ee4f1007f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9cdf4373-98ac-4dc1-8318-3e4ed2bdfed2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3974d83b-506b-47af-822e-fc76b40f876c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4e8c85f-ca93-4feb-87a6-9213c9a66669&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.IDCContract.liquidationSummary.liquidationAmt&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.liqFound&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction>if (this.context.options.isChecker.get("value") == true) {&#xD;
	this.ui.get("Vertical_Settlement").setEnabled(false);&#xD;
//	this.ui.get("DebitedAccountDetails1").setEnabled(false);&#xD;
//	this.ui.get("DebitedAmount1").setEnabled(false);&#xD;
}else{&#xD;
	this.ui.get("Vertical_Settlement").setEnabled(true);&#xD;
//	this.ui.get("DebitedAccountDetails1").setEnabled(true);&#xD;
//	this.ui.get("DebitedAmount1").setEnabled(true);&#xD;
}&#xD;
&#xD;
&#xD;
//if (this.context.options.liquidationVis.get("value")){&#xD;
//	this.ui.get("ContractLiquidation").setVisible(true,true);&#xD;
//}else{&#xD;
//	this.ui.get("ContractLiquidation").setVisible(false,true);&#xD;
//}&#xD;
&#xD;
	&#xD;
</loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>b4700579-ae43-4813-8576-4a7bf2d336be</guid>
        <versionId>2fee23dd-76fc-485e-8d35-7a43b7a22e07</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="IDCContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.746dd7fc-ddeb-4b40-aa25-2ebf593cadd1</coachViewBindingTypeId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>2237cb0d-054f-4fa9-87bc-b3485082fa9a</guid>
            <versionId>199472f7-b7fc-4263-8319-7e9417eb6650</versionId>
        </bindingType>
        <configOption name="sAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.6902185d-476d-4156-babb-0ee63ca86183</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>187a92fb-ba54-475f-a630-f09f437186a6</guid>
            <versionId>6a246e37-58a4-481d-a8e7-cd31de2cb354</versionId>
        </configOption>
        <configOption name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.f62ae6de-b668-4b1d-8743-1e420bead6a7</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>a81637ae-184a-4785-97e8-0d6f1d853887</guid>
            <versionId>0582a4b2-dfb7-4212-a558-0de36fe9d906</versionId>
        </configOption>
        <configOption name="isGLFound">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.47727f7c-cef7-4fab-b27e-dba6d6a1aaac</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>e6f8fa7a-618c-4219-ac9c-ed108ee9e51e</guid>
            <versionId>5384a245-f9ac-4f38-808d-ae5d73169541</versionId>
        </configOption>
        <configOption name="exchangeCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7cbb3056-7145-4d95-8134-05a0121adf3a</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.1c5aeb6a-6abf-4187-85bf-d2c79a8bffa1</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>9f8260d6-0056-458a-90bc-c670dff82efc</guid>
            <versionId>5978898d-aa4e-4aca-9b5e-74da67f77c31</versionId>
        </configOption>
        <configOption name="liquidationVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.14873433-307b-48f5-9aa2-7dd251647187</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>c7e1a0a1-dfec-4cdb-8353-7dd6d0b92bc0</guid>
            <versionId>e2315ad0-eea7-4db4-853e-d11eab7fc226</versionId>
        </configOption>
        <configOption name="liqDone">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.fe3fbcfb-a860-4f4d-a3bf-35cbb1c27c63</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>5b4c8ea3-ff2f-428d-90cb-b188c281fea6</guid>
            <versionId>4f07d336-4eab-46ee-9f76-af48fff67662</versionId>
        </configOption>
        <configOption name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8318e63c-f945-41a9-a057-6d2586e89187</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>2102d44b-6365-43ab-a5bf-b2b9f1f40eb5</guid>
            <versionId>34d48249-bcd8-47f3-9fd3-3bdd9209aad6</versionId>
        </configOption>
        <configOption name="accountIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8f0c3bd9-6ebb-4422-928c-1d6e46bc6e58</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>26da4ca1-3e58-49bc-a56c-3bb9966285de</guid>
            <versionId>8597334d-ce0b-4e6d-9f51-64ad1e173e8f</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9dfadf7c-7ef2-4505-93a9-f7434c5c8b42</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>41d1b5be-2ab5-4a29-a5ce-b061de4665c1</guid>
            <versionId>83c37023-58f2-460d-bbcf-382af1f1e03b</versionId>
        </configOption>
        <configOption name="tempSettlement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d908715d-bfce-46e7-903f-94b9ac1a94c4</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.36aae433-b480-4a1f-861b-6d30c09351a1</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>a9b09555-162c-4790-aa56-2efc5611f60d</guid>
            <versionId>28e1bb68-f2f5-42c5-8783-6d6c01f49057</versionId>
        </configOption>
        <configOption name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.913ffe34-fa0e-4f7d-bdff-db5141e1e5b8</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>40e1572f-0d89-4c61-8a5b-eadbdc9a3f41</guid>
            <versionId>a9f95afe-d0a4-47a1-a4cc-5ac960617213</versionId>
        </configOption>
        <configOption name="concatExCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ca6c880d-5e47-4666-aaee-a4c06d0f06a3</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>a78c1548-1a5b-4b3c-ac63-ca5aaeab551c</guid>
            <versionId>6421f456-3c6f-434d-ae50-391e30c07a92</versionId>
        </configOption>
        <configOption name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c7671ae1-ea1c-46dd-b724-b9c653c0751f</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>12</seq>
            <description></description>
            <groupName></groupName>
            <guid>890416b1-c23f-410e-b394-1421d0d9e394</guid>
            <versionId>f3fdcee4-0ba4-4f9e-8e3b-1f22e353f33f</versionId>
        </configOption>
        <configOption name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ea10f776-4bbc-4a40-a16a-5502a4f1e353</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>13</seq>
            <description></description>
            <groupName></groupName>
            <guid>f9a4f112-5779-4d29-8925-9bffc2870b25</guid>
            <versionId>b0e2e023-f867-45c1-875a-4f23943d7b68</versionId>
        </configOption>
        <configOption name="draweeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c3a2d6f1-8d11-4390-83e1-2b9057d91e66</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>14</seq>
            <description></description>
            <groupName></groupName>
            <guid>16d2c5e7-c8b0-419b-a259-db511b48b001</guid>
            <versionId>6f215189-fd3e-4ff7-922a-b589ff340e4b</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.fcb6bd81-d680-4f75-9548-edd74fee85cc</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>15</seq>
            <description></description>
            <groupName></groupName>
            <guid>2a5cf2e6-893b-45af-b5f9-54663bba52d2</guid>
            <versionId>ddc7c456-90d6-4682-ab63-086a2ed5308a</versionId>
        </configOption>
        <configOption name="accountClassCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.76b461b7-79a7-4275-a3dc-b2267d56d360</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>16</seq>
            <description></description>
            <groupName></groupName>
            <guid>9c58b4b1-bf7f-439c-a20f-44404d98e744</guid>
            <versionId>88ef0e47-1f81-427f-93ea-5da1c10cb231</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.3095e967-f265-4b50-a5fb-b1578f88b618</coachViewConfigOptionId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>17</seq>
            <description></description>
            <groupName></groupName>
            <guid>b4461afb-efc6-4cd1-8eaa-2920eaf3b888</guid>
            <versionId>4c247f18-dc8d-4db8-a934-f5b7051fb057</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.79b379e5-53a9-49d2-9519-0603766b469f</coachViewInlineScriptId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.executeService = function (value){&#xD;
    var index = value.ui.getIndex();&#xD;
    if (value.getData() == "Customer Account"){&#xD;
    		this.ui.get("AccountBranchCode["+index+"]").setEnabled(false);&#xD;
       	this.ui.get("AccountCurrency["+index+"]").setEnabled(false);&#xD;
		this.ui.get("AccountNumberGL["+index+"]").setEnabled(false);&#xD;
        	this.ui.get("VerifyGLAccountBtn["+index+"]").setEnabled(false);&#xD;
       	 this.ui.get("Owner1["+index+"]").setEnabled(true);&#xD;
    		if (this.ui.get("Owner1["+index+"]").getData() != "") {&#xD;
   	  		this.ui.get("AccountNumber["+index+"]").setEnabled(true);&#xD;
    	  		this.ui.get("AccountNumberGL["+index+"]").setData("");&#xD;
    		}&#xD;
	}else if (value.getData() == "GL Account"){&#xD;
		this.ui.get("AccountBranchCode["+index+"]").setEnabled(true);&#xD;
       	this.ui.get("AccountCurrency["+index+"]").setEnabled(true);&#xD;
//		this.ui.get("Owner1["+index+"]").setData("");&#xD;
		this.ui.get("AccountNumber["+index+"]").setEnabled(false);&#xD;
		this.ui.get("Owner1["+index+"]").setEnabled(false);&#xD;
   	  	this.ui.get("VerifyGLAccountBtn["+index+"]").setEnabled(true);&#xD;
		this.ui.get("AccountNumberGL["+index+"]").setEnabled(true);&#xD;
		this.ui.get("AccountNumber["+index+"]").setData("");&#xD;
		this.ui.get("Decimal1["+index+"]").setData("");&#xD;
		this.ui.get("Output_Text1["+index+"]").setData("");&#xD;
		this.ui.get("Text2["+index+"]").setData("");&#xD;
		this.ui.get("AccountCurrency1["+index+"]").setData("");&#xD;
    }&#xD;
}&#xD;
&#xD;
this.debitPercentageRule = function (value){&#xD;
	if(value.getData() == "Percentage"){&#xD;
		this.ui.get("DebitPercentage").setEnabled(true);&#xD;
		this.ui.get("DebitedAmountinLiquidationCurrency").setEnabled(false);&#xD;
		&#xD;
	}else{&#xD;
        this.ui.get("DebitPercentage").setEnabled(false);&#xD;
        this.ui.get("DebitedAmountinLiquidationCurrency").setEnabled(true);&#xD;
   	}&#xD;
&#xD;
} &#xD;
&#xD;
this.dAmntAccountSum = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	var a = this.ui.get("DebitedAmountinLiquidationCurrency["+record+"]").getData();&#xD;
	var b = this.ui.get("NegotiatedExchangeRate["+record+"]").getData();&#xD;
	if ( (a != null &amp;&amp; a != undefined) &amp;&amp; (b != null &amp;&amp; b != undefined)) {&#xD;
		this.ui.get("DebitedAmountinAccountCurrency["+record+"]").setData(this.ui.get("DebitedAmountinLiquidationCurrency["+record+"]").getData() * this.ui.get("NegotiatedExchangeRate["+record+"]").getData());&#xD;
	}		&#xD;
}&#xD;
&#xD;
this.setAccountInfo = function (value){&#xD;
	for (var i=0; i&lt;this.context.options.sAccountList.get("value").length(); i++) {&#xD;
		if (this.context.options.sAccountList.get("value").get(i).get("accountNO") == value.getData()) {&#xD;
			this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("accountBalance", this.context.options.sAccountList.get("value").get(i).get("balance"));&#xD;
			this.context.options.accountClassCode.set("value", this.context.options.sAccountList.get("value").get(i).get("accountClassCode"));&#xD;
			var accountClassCode =  this.context.options.accountClassCode.get("value");//SA04&#xD;
			var code = accountClassCode.substring(0,1);&#xD;
			&#xD;
			if (code == "O" || code == "D"){		&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("isOverDraft", true);&#xD;
			}else{&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("isOverDraft", false);&#xD;
			}&#xD;
			alert(this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").get("isOverDraft"));&#xD;
			if (this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").get("accountClass") == "Customer Account") {&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("accountCurrency", {});&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").get("accountCurrency").set("code", this.context.options.sAccountList.get("value").get(i).get("currencyCode"));&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("accountBranchCode", this.context.options.sAccountList.get("value").get(i).get("branchCode"));&#xD;
				this.context.binding.get("value").get("settlementAccounts").get(value.ui.getIndex()).get("debitedAccount").set("balanceSign", this.context.options.sAccountList.get("value").get(i).get("balanceType"));&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.validGlAccount = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	if (this.context.binding.get("value").get("settlementAccounts").get(record).get("debitedAccount").get("isGLFoundC") == false) {&#xD;
		this.ui.get("AccountNumberGL["+record+"]").setValid(false, "Account Not Found!");&#xD;
	}else{&#xD;
		this.ui.get("AccountNumberGL["+record+"]").setValid(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setExchangeOnResult = function (){&#xD;
	this.ui.get("NegotiatedExchangeRate").setData(this.ui.get("NegotiatedExchangeRate").getData());&#xD;
}&#xD;
&#xD;
this.executeServ = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	var data = this.context.binding.get("value").get("settlementAccounts").get(record).get("debitedAccount").get("GLAccountNumber");&#xD;
	this.ui.get("CheckexistingGLAccount1["+record+"]").execute(data);&#xD;
}&#xD;
&#xD;
this.addAccount = function (){&#xD;
	this.context.binding.get("value").get("settlementAccounts").add({});&#xD;
	this.context.binding.get("value").get("settlementAccounts").get(this.context.binding.get("value").get("settlementAccounts").length()-1).set("debitedAccount",{});&#xD;
	this.context.binding.get("value").get("settlementAccounts").get(this.context.binding.get("value").get("settlementAccounts").length()-1).get("debitedAccount").set("accountClass","Customer Account");&#xD;
	this.context.binding.get("value").get("settlementAccounts").get(this.context.binding.get("value").get("settlementAccounts").length()-1).get("debitedAccount").set("accountCurrency",{});&#xD;
	this.context.binding.get("value").get("settlementAccounts").get(this.context.binding.get("value").get("settlementAccounts").length()-1).set("debitedAmount",{});&#xD;
}&#xD;
&#xD;
this.removeAccount = function (value){&#xD;
	index = value.ui.getIndex();&#xD;
	this.context.options.tempSettlement.set("value", []);&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").get("settlementAccounts").length(); i++) {&#xD;
		if (i != index) {&#xD;
			this.context.options.tempSettlement.get("value").add(this.context.binding.get("value").get("settlementAccounts").get(i));&#xD;
		}&#xD;
	}&#xD;
	this.context.binding.get("value").get("settlementAccounts").remove(index);&#xD;
	for (var i=0; i&lt;this.context.options.tempSettlement.get("value").length(); i++) {&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(i).set("debitedAccount", this.context.options.tempSettlement.get("value").get(i).get("debitedAccount"));&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(i).set("debitedAmount", this.context.options.tempSettlement.get("value").get(i).get("debitedAmount"));&#xD;
	}&#xD;
	console.dir(this.context.binding.get("value").get("settlementAccounts"));&#xD;
}&#xD;
&#xD;
this.settlementVis = function (){&#xD;
	if (this.context.options.isChecker.get("value") == true) {&#xD;
		this.ui.get("DebitedAccountDetails1").setEnabled(false);&#xD;
		this.ui.get("DebitedAmount1").setEnabled(false);&#xD;
	}else{&#xD;
		this.ui.get("DebitedAccountDetails1").setEnabled(true);&#xD;
		this.ui.get("DebitedAmount1").setEnabled(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setExchangeRate = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if (this.ui.get("LiquidateionCurrency").getData() == value.getData()) {&#xD;
		this.ui.get("StandardExchangeRate["+index+"]").setData(1.0);&#xD;
		this.ui.get("NegotiatedExchangeRate["+index+"]").setData(1.0);&#xD;
	}else{&#xD;
		var defaultCurrency = this.ui.get("LiquidateionCurrency").getData();&#xD;
		var accountCurrency = value.getData();&#xD;
		concatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:"TRANSFER" , sType:"S"};&#xD;
		inputCurr = JSON.stringify(concatedCurrency);&#xD;
		this.ui.get("GetExchangeRate["+index+"]").execute(inputCurr);&#xD;
	}	&#xD;
}&#xD;
&#xD;
this.setExServiceResults = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.ui.get("StandardExchangeRate["+index+"]").setData(this.context.options.exRate.get("value"));&#xD;
	this.ui.get("NegotiatedExchangeRate["+index+"]").setData(this.ui.get("StandardExchangeRate["+index+"]").getData());&#xD;
	this.ui.get("NegotiatedExchangeRate["+index+"]").setEnabled(true);&#xD;
}&#xD;
&#xD;
this.setOwnerCIF = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	if (this.context.options.isChecker.get("value") == false) {&#xD;
		this.ui.get("AccountNumber["+record+"]").setEnabled(true);&#xD;
	}&#xD;
	if (value.getData() == "Accountee") {&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(record).set("settCIF", this.context.options.accounteeCIF.get("value"));&#xD;
	}else if (value.getData() == "Case In Need") {&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(record).set("settCIF", this.context.options.caseCIF.get("value"));&#xD;
	}else{&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(record).set("settCIF", this.context.options.draweeCIF.get("value"));&#xD;
	}&#xD;
	this.ui.get("GetCustomerAccount1["+record+"]").execute(this.context.binding.get("value").get("settlementAccounts").get(record).get("settCIF"));&#xD;
}&#xD;
&#xD;
this.setSettAccounts = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.context.binding.get("value").get("settlementAccounts").get(index).set("accountNumberList" , []);&#xD;
	for (var i=0; i&lt;this.context.options.sAccountList.get("value").length(); i++) {&#xD;
		this.context.binding.get("value").get("settlementAccounts").get(index).get("accountNumberList").add({name:this.context.options.sAccountList.get("value").get(i).get("accountNO") , value:this.context.options.sAccountList.get("value").get(i).get("accountNO")});&#xD;
	}&#xD;
}&#xD;
&#xD;
this.resetGLButton = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.ui.get("VerifyGLAccountBtn["+index+"]").setData(false);&#xD;
}&#xD;
&#xD;
this.accountVis = function(value){&#xD;
	value.setEnabled(false);&#xD;
	var index = value.ui.getIndex();&#xD;
	if(this.context.options.isChecker.get("value") == true || this.ui.get("Owner1["+index+"]").getData() == null || this.ui.get("Owner1["+index+"]").getData() == undefined || this.ui.get("Owner1["+index+"]").getData() == ""){&#xD;
		value.setEnabled(false);&#xD;
	}else{&#xD;
		value.setEnabled(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.validateDigits = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if(isNaN(Number(value.getData()))){&#xD;
		value.setData("");&#xD;
		value.setValid(false ,"must be digits");&#xD;
		return false;&#xD;
	}else&#xD;
	{&#xD;
		value.setValid(true);&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setDebitPercAmount = function (value){&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").get("settlementAccounts").length(); i++) {		&#xD;
		if (value.getData() == "Percentage") {&#xD;
			this.ui.get("DebitPercentage["+i+"]").setEnabled(true);&#xD;
			this.ui.get("DebitedAmountinLiquidationCurrency["+i+"]").setEnabled(false);&#xD;
			&#xD;
		}else{&#xD;
			this.ui.get("DebitPercentage["+i+"]").setEnabled(false);&#xD;
			this.ui.get("DebitedAmountinLiquidationCurrency["+i+"]").setEnabled(true);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.dAmtLiqCurSum = function (){&#xD;
	if (this.context.binding.get("value").get("liquidationSummary").get("debitBasisby") == "Percentage") {&#xD;
		for (var i=0; i&lt;this.context.binding.get("value").get("settlementAccounts").length(); i++) {&#xD;
			var liqAmnt = this.context.binding.get("value").get("liquidationSummary").get("liquidationAmt");&#xD;
			var perc = this.context.binding.get("value").get("settlementAccounts").get(i).get("debitedAmount").get("debitPercentage");&#xD;
			var result = liqAmnt * perc;&#xD;
			this.context.binding.get("value").get("settlementAccounts").get(i).get("debitedAmount").set("debitedAmtinLiquidationCurrency", result);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.validateOverDraft = function (value) {&#xD;
	var index = value.ui.getIndex();&#xD;
	if (this.context.binding.get("value").get("settlementAccounts").get(index).get("debitedAccount").get("isOverDraft") == true) {&#xD;
		this.ui.get("DebitedAmountinAccountCurrency["+index+"]").setValid(false,"WARNING: Must be &lt; Account Balance");&#xD;
	}else{&#xD;
		this.ui.get("DebitedAmountinAccountCurrency["+index+"]").setValid(true);&#xD;
	}&#xD;
}&#xD;
//----------------------------------------------------------------Drop_2----------------------------------------------------------------------&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>ccad17b3-5ac0-4c57-9894-36e632a4471f</guid>
            <versionId>ba210d7f-c615-4c4b-a7cb-15dc8bd5f5c5</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.88fb1e7e-a65b-42ca-a50f-a6438139900c</coachViewLocalResId>
            <coachViewId>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>616bd8e6-cd4d-41c1-8c98-99416cf1494d</guid>
            <versionId>36f67dc1-691d-47d3-a342-45912ce96087</versionId>
        </localization>
    </coachView>
</teamworks>

