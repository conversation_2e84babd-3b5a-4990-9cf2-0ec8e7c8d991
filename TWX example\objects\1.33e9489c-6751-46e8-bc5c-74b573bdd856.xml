<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.33e9489c-6751-46e8-bc5c-74b573bdd856" name="update advance payment">
        <lastModified>1692507352930</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7fe6</guid>
        <versionId>8373c3f5-cf50-4f0d-9cb4-d5aed9bc8bc1</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee9" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.1214845f-2e66-409b-8fdb-f807518a0280"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"812dea2c-c018-4edc-80eb-6fa3d770430f"},{"incoming":["b0e1f314-0f86-4fd4-8bba-0669321aa36c","28c7d060-4095-4b85-87fe-a3df5d10dd34"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":680,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7fe4"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ab7733d9-9a8e-41cd-8c12-66fb71d9794e"},{"targetRef":"7d884c4d-7c4c-4711-8b25-8c351bb2e2f9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.1214845f-2e66-409b-8fdb-f807518a0280","sourceRef":"812dea2c-c018-4edc-80eb-6fa3d770430f"},{"startQuantity":1,"outgoing":["9ce7df7a-3916-4d3d-8fb4-75f0400944cf"],"incoming":["2027.1214845f-2e66-409b-8fdb-f807518a0280"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":110,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7d884c4d-7c4c-4711-8b25-8c351bb2e2f9","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.Seperated = new tw.object.listOf.String();\r\ntw.local.Seperated = tw.local.data.split(\"-\");\r\ntw.local.requestID = tw.local.Seperated[0];\r\ntw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();\r\ntw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];\r\ntw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];\r\ntw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];\r\n\r\n"]}},{"startQuantity":1,"outgoing":["ac17d4eb-6550-4c79-869a-f47f398aa554"],"incoming":["9ce7df7a-3916-4d3d-8fb4-75f0400944cf"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":255,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b7ce4f6e-c980-48cb-8176-951e8b7b3967","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\n\/\/---------------------------------------------UPDATE Amount---------------------------------\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"UPDATE BPM.IDC_ADVANCED_PAYMENT_USED SET AMOUNT_ALLOCATED_FOR_REQUEST= ? ,ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY = ? WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;\";\r\n\r\nparamInit (\"DECIMAL\",tw.local.tmpAdvancePayment.AmountAllocated);\r\nparamInit (\"DECIMAL\",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);\r\nparamInit (\"INTEGER\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.requestID);\r\n\/\/----------------------------------------------UPDATE Advance Payment----------------------------------\r\ni=0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\n\/\/-------------------------------------------Select-----------------------------------\r\ni=0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\n\r\n"]}},{"startQuantity":1,"outgoing":["0a15d58a-430e-40e1-8aa2-1aa73b0f32c5"],"incoming":["ac17d4eb-6550-4c79-869a-f47f398aa554"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":415,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"40afb475-164f-49bb-8664-0101a58c18cb","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.queryResult"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"startQuantity":1,"outgoing":["b0e1f314-0f86-4fd4-8bba-0669321aa36c"],"incoming":["0a15d58a-430e-40e1-8aa2-1aa73b0f32c5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":541,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e7a18128-1c99-4abd-8c46-57aef5af818a","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = tw.local.queryResult[2].rows[0].data[0];"]}},{"targetRef":"40afb475-164f-49bb-8664-0101a58c18cb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"ac17d4eb-6550-4c79-869a-f47f398aa554","sourceRef":"b7ce4f6e-c980-48cb-8176-951e8b7b3967"},{"targetRef":"e7a18128-1c99-4abd-8c46-57aef5af818a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map Output","declaredType":"sequenceFlow","id":"0a15d58a-430e-40e1-8aa2-1aa73b0f32c5","sourceRef":"40afb475-164f-49bb-8664-0101a58c18cb"},{"targetRef":"b7ce4f6e-c980-48cb-8176-951e8b7b3967","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"9ce7df7a-3916-4d3d-8fb4-75f0400944cf","sourceRef":"7d884c4d-7c4c-4711-8b25-8c351bb2e2f9"},{"targetRef":"ab7733d9-9a8e-41cd-8c12-66fb71d9794e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"b0e1f314-0f86-4fd4-8bba-0669321aa36c","sourceRef":"e7a18128-1c99-4abd-8c46-57aef5af818a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.366a051c-d5e0-40c0-8923-22d501eb28ba"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.cb484a9d-68e0-4d37-8d1b-6ebd7079a9cb"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.03de7774-850a-4458-8ad2-54587df860a4"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.5b163a03-3714-4c04-8ed1-3e053db66ec8"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"queryResult","isCollection":true,"declaredType":"dataObject","id":"2056.7c4c8d27-8591-4e6e-8a4a-f747073f5fb1"},{"parallelMultiple":false,"outgoing":["82cba171-29e7-4ab7-8157-217481272973"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"eaa9ab8a-c09d-48de-8e40-aee822208779"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fb64280d-e934-4f39-800c-c164634d4454","otherAttributes":{"eventImplId":"3389afe1-d663-450d-87ff-6224dbcc31f4"}}],"attachedToRef":"7d884c4d-7c4c-4711-8b25-8c351bb2e2f9","extensionElements":{"nodeVisualInfo":[{"width":24,"x":145,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"24d58011-39bb-458d-8af0-7cef453d542c","outputSet":{}},{"parallelMultiple":false,"outgoing":["1fdba31f-6e25-49e1-892b-90515a3826da"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"eae0ece5-9432-4c11-82af-085871223d6f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d566cdb7-2355-4f7b-8761-2f629b9a0bff","otherAttributes":{"eventImplId":"7e3c7545-76d6-4926-804b-d511087603fd"}}],"attachedToRef":"b7ce4f6e-c980-48cb-8176-951e8b7b3967","extensionElements":{"nodeVisualInfo":[{"width":24,"x":290,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"18d5a721-e15f-4be3-8e23-fc2f7671d6f2","outputSet":{}},{"parallelMultiple":false,"outgoing":["2edd105b-cf23-4377-87d9-4b6cfc9496c8"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"df634062-5021-4e3d-8132-70dcf105c880"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9c4858a4-8d48-4c88-8270-ba66d6f8eef3","otherAttributes":{"eventImplId":"40a1a368-1e9b-4911-85e6-beb7fee85fcb"}}],"attachedToRef":"40afb475-164f-49bb-8664-0101a58c18cb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":450,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"ea6af822-8059-4729-8fed-6a2b91a294dc","outputSet":{}},{"parallelMultiple":false,"outgoing":["6c96ef8f-e81a-4ef0-8f80-fa369b177dd9"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e94ce2c6-0c89-4272-8a23-773f6574ee27"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"dac907f0-9c38-46eb-8620-84c1b2e7b611","otherAttributes":{"eventImplId":"8c8b6630-8bf0-496b-80a9-882fb9ab4445"}}],"attachedToRef":"e7a18128-1c99-4abd-8c46-57aef5af818a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":576,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"025f6d56-b938-4e05-86d6-95dbdcbad066","outputSet":{}},{"targetRef":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"82cba171-29e7-4ab7-8157-217481272973","sourceRef":"24d58011-39bb-458d-8af0-7cef453d542c"},{"targetRef":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"1fdba31f-6e25-49e1-892b-90515a3826da","sourceRef":"18d5a721-e15f-4be3-8e23-fc2f7671d6f2"},{"targetRef":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"2edd105b-cf23-4377-87d9-4b6cfc9496c8","sourceRef":"ea6af822-8059-4729-8fed-6a2b91a294dc"},{"targetRef":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"6c96ef8f-e81a-4ef0-8f80-fa369b177dd9","sourceRef":"025f6d56-b938-4e05-86d6-95dbdcbad066"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.43e6ed70-f9dc-4444-87f2-4fbb971d4212"},{"startQuantity":1,"outgoing":["28c7d060-4095-4b85-87fe-a3df5d10dd34"],"incoming":["6c96ef8f-e81a-4ef0-8f80-fa369b177dd9","1fdba31f-6e25-49e1-892b-90515a3826da","82cba171-29e7-4ab7-8157-217481272973","2edd105b-cf23-4377-87d9-4b6cfc9496c8"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":441,"y":209,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"\";"]}},{"targetRef":"ab7733d9-9a8e-41cd-8c12-66fb71d9794e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"28c7d060-4095-4b85-87fe-a3df5d10dd34","sourceRef":"3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd"}],"laneSet":[{"id":"2470f090-10af-407c-873f-0b470285030a","lane":[{"flowNodeRef":["812dea2c-c018-4edc-80eb-6fa3d770430f","ab7733d9-9a8e-41cd-8c12-66fb71d9794e","7d884c4d-7c4c-4711-8b25-8c351bb2e2f9","b7ce4f6e-c980-48cb-8176-951e8b7b3967","40afb475-164f-49bb-8664-0101a58c18cb","e7a18128-1c99-4abd-8c46-57aef5af818a","24d58011-39bb-458d-8af0-7cef453d542c","18d5a721-e15f-4be3-8e23-fc2f7671d6f2","ea6af822-8059-4729-8fed-6a2b91a294dc","025f6d56-b938-4e05-86d6-95dbdcbad066","3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"ee69acf7-2973-458b-8806-a6fe2f66777c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"update advance payment","declaredType":"process","id":"1.33e9489c-6751-46e8-bc5c-74b573bdd856","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.d6417f73-07b0-487f-8548-e63ca22f5eca"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.be277fb2-52dd-4c9d-8a19-ac6dfac1c2e4"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"55-0.4-20-56\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.bb84e19f-2bcb-451d-81aa-4023ee3b1088"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bb84e19f-2bcb-451d-81aa-4023ee3b1088</processParameterId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"55-0.4-20-56"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5442a24f-1c23-4874-acf3-2b359fd1f943</guid>
            <versionId>4fdd5bfd-ae35-4983-ba06-6f56f84c8f60</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d6417f73-07b0-487f-8548-e63ca22f5eca</processParameterId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f0bf4356-7391-426b-a3de-ae8686adda3c</guid>
            <versionId>89c99930-504b-48e6-8558-4d295524aeb4</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.be277fb2-52dd-4c9d-8a19-ac6dfac1c2e4</processParameterId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>93a4260c-d13a-4d9a-88c1-148566d05da5</guid>
            <versionId>e8a2eeff-c7e1-4075-8f40-78533756d1b9</versionId>
        </processParameter>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.366a051c-d5e0-40c0-8923-22d501eb28ba</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4bcbecf9-0480-4204-9c54-653c0fa9822c</guid>
            <versionId>1cc10094-d406-412a-8a7b-af432f89ba19</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb484a9d-68e0-4d37-8d1b-6ebd7079a9cb</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1d962510-**************-24c8cf7a5724</guid>
            <versionId>5eea840a-e71b-45cb-816c-24f8cde46cb1</versionId>
        </processVariable>
        <processVariable name="tmpAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.03de7774-850a-4458-8ad2-54587df860a4</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f146ff3f-45d5-404e-975f-af84bc95600b</guid>
            <versionId>5dcbcbe6-a7a0-4fb3-a79b-edf31d778ef1</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5b163a03-3714-4c04-8ed1-3e053db66ec8</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c129962c-f83b-4ba9-8e87-8e655f3e8e35</guid>
            <versionId>02fcafdb-a15c-42dd-9261-ffbf7acb4fa2</versionId>
        </processVariable>
        <processVariable name="queryResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7c4c8d27-8591-4e6e-8a4a-f747073f5fb1</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>59ba957f-e2ce-45a9-8852-b3ee7aacf896</guid>
            <versionId>b5efb392-aeed-4689-b9eb-a281e38bc5b2</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.43e6ed70-f9dc-4444-87f2-4fbb971d4212</processVariableId>
            <description isNull="true" />
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc6cc334-e082-4aa5-b4d4-a306fe28d9c4</guid>
            <versionId>5cfadcea-8190-489f-bc9b-97c1d386ac75</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b7ce4f6e-c980-48cb-8176-951e8b7b3967</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>Init SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.94167ec5-785b-41e0-98f9-6d001482216d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7e19</guid>
            <versionId>34d83b66-12a5-4269-a864-a9e543754354</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="255" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee5</errorHandlerItem>
                <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.94167ec5-785b-41e0-98f9-6d001482216d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------UPDATE Amount---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "UPDATE BPM.IDC_ADVANCED_PAYMENT_USED SET AMOUNT_ALLOCATED_FOR_REQUEST= ? ,ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY = ? WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;";&#xD;
&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AmountAllocated);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);&#xD;
paramInit ("INTEGER",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE Advance Payment----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
//-------------------------------------------Select-----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>f8d51638-7b9f-42d3-ace7-951936a69d58</guid>
                <versionId>09696d32-469a-4f7e-b0c1-6291b1d3c678</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e7a18128-1c99-4abd-8c46-57aef5af818a</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>Map Output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4ae88ecf-08a2-4cb2-85fe-154179367e01</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7e18</guid>
            <versionId>5d55fac6-74ca-4b5f-a9b2-ed55e476aed7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="541" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee5</errorHandlerItem>
                <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4ae88ecf-08a2-4cb2-85fe-154179367e01</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = tw.local.queryResult[2].rows[0].data[0];</script>
                <isRule>false</isRule>
                <guid>2bd4455d-97c8-46ff-8904-7a1d78c8c6f8</guid>
                <versionId>32c539f4-3f62-4eda-8673-c431af1fef14</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ab7733d9-9a8e-41cd-8c12-66fb71d9794e</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3bc4a620-0693-45c1-b1b0-5cfda8b0e604</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7fe4</guid>
            <versionId>a5748c67-2edb-4b3d-b511-746ae0c942ea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="680" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3bc4a620-0693-45c1-b1b0-5cfda8b0e604</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>4ecd5f5f-5022-4b38-8a4c-2aefad1a2a02</guid>
                <versionId>e5c2f872-0db4-4ccf-8a1b-27e7378326e2</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8b962297-f4fe-4ab6-a015-4be06d5d00ff</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee5</guid>
            <versionId>a71325cc-e38d-4cd4-a624-99490a421017</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="441" y="209">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8b962297-f4fe-4ab6-a015-4be06d5d00ff</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</script>
                <isRule>false</isRule>
                <guid>48b3c41d-f9a5-43a6-b706-d123c6fb166f</guid>
                <versionId>812ba3a5-595b-4b32-afc3-6dd57f908f1f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.40afb475-164f-49bb-8664-0101a58c18cb</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0715332c-a193-489a-8800-78f6cca1954d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7e17</guid>
            <versionId>dd3a07bd-3edd-4d18-b402-4363fb40b26a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="415" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee5</errorHandlerItem>
                <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0715332c-a193-489a-8800-78f6cca1954d</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>df9e1c63-dc3c-4e23-acc8-b3d0851f7114</guid>
                <versionId>02de779c-237c-4e0e-8148-a614a2480bc4</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7c2dfeba-b8e1-4a14-b220-************</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.0715332c-a193-489a-8800-78f6cca1954d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c78929f2-2434-438a-9c31-71470c4554e1</guid>
                    <versionId>1875dba1-4e0d-4316-9b51-a853e5823d92</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.80632fbe-fd6e-4bba-80d2-3740505c7755</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.0715332c-a193-489a-8800-78f6cca1954d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>f4e332cd-0b33-464e-92e4-5bdc698e71fc</guid>
                    <versionId>510f54f5-6f67-411f-9e48-16532b1a9876</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9f606ac6-b2d8-4457-bdc4-364015220e81</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.0715332c-a193-489a-8800-78f6cca1954d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryResult</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>65b7b83c-5adf-45db-97c7-f54d730e8997</guid>
                    <versionId>b73297ac-ece8-44e5-b3f1-01c9ef4e37b9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</processItemId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.75550293-cae1-4ae3-ac42-31ee9f6cfaaa</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7e1a</guid>
            <versionId>f0000840-f446-4ae9-978d-c56d655d5e58</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="110" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ee5</errorHandlerItem>
                <errorHandlerItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.75550293-cae1-4ae3-ac42-31ee9f6cfaaa</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];&#xD;
tw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>a2a1ee9b-1d79-4152-ba90-5f1841eb933d</guid>
                <versionId>bbce8a63-5d4e-44ee-809e-f279df7e0d27</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="update advance payment" id="1.33e9489c-6751-46e8-bc5c-74b573bdd856" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.bb84e19f-2bcb-451d-81aa-4023ee3b1088">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"55-0.4-20-56"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d6417f73-07b0-487f-8548-e63ca22f5eca" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.be277fb2-52dd-4c9d-8a19-ac6dfac1c2e4" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2470f090-10af-407c-873f-0b470285030a">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="ee69acf7-2973-458b-8806-a6fe2f66777c" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>812dea2c-c018-4edc-80eb-6fa3d770430f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ab7733d9-9a8e-41cd-8c12-66fb71d9794e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b7ce4f6e-c980-48cb-8176-951e8b7b3967</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>40afb475-164f-49bb-8664-0101a58c18cb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e7a18128-1c99-4abd-8c46-57aef5af818a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>24d58011-39bb-458d-8af0-7cef453d542c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>18d5a721-e15f-4be3-8e23-fc2f7671d6f2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ea6af822-8059-4729-8fed-6a2b91a294dc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>025f6d56-b938-4e05-86d6-95dbdcbad066</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="812dea2c-c018-4edc-80eb-6fa3d770430f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.1214845f-2e66-409b-8fdb-f807518a0280</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ab7733d9-9a8e-41cd-8c12-66fb71d9794e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="680" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:e5873b94e72cc4db:-66d3a9fb:189d50b55ef:-7fe4</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b0e1f314-0f86-4fd4-8bba-0669321aa36c</ns16:incoming>
                        
                        
                        <ns16:incoming>28c7d060-4095-4b85-87fe-a3df5d10dd34</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="812dea2c-c018-4edc-80eb-6fa3d770430f" targetRef="7d884c4d-7c4c-4711-8b25-8c351bb2e2f9" name="To Script Task" id="2027.1214845f-2e66-409b-8fdb-f807518a0280">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="7d884c4d-7c4c-4711-8b25-8c351bb2e2f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="110" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.1214845f-2e66-409b-8fdb-f807518a0280</ns16:incoming>
                        
                        
                        <ns16:outgoing>9ce7df7a-3916-4d3d-8fb4-75f0400944cf</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];&#xD;
tw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Init SQL Query" id="b7ce4f6e-c980-48cb-8176-951e8b7b3967">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="255" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9ce7df7a-3916-4d3d-8fb4-75f0400944cf</ns16:incoming>
                        
                        
                        <ns16:outgoing>ac17d4eb-6550-4c79-869a-f47f398aa554</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------UPDATE Amount---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "UPDATE BPM.IDC_ADVANCED_PAYMENT_USED SET AMOUNT_ALLOCATED_FOR_REQUEST= ? ,ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY = ? WHERE BPM.IDC_ADVANCED_PAYMENT_USED.IDC_REQUEST_ID = ? AND BPM.IDC_ADVANCED_PAYMENT_USED.REFERRAL_REQUEST_NUMBER = ?;";&#xD;
&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AmountAllocated);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);&#xD;
paramInit ("INTEGER",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE Advance Payment----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
//-------------------------------------------Select-----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Multiple Statements (SQLResult)" id="40afb475-164f-49bb-8664-0101a58c18cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="415" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ac17d4eb-6550-4c79-869a-f47f398aa554</ns16:incoming>
                        
                        
                        <ns16:outgoing>0a15d58a-430e-40e1-8aa2-1aa73b0f32c5</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.queryResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Output" id="e7a18128-1c99-4abd-8c46-57aef5af818a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="541" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0a15d58a-430e-40e1-8aa2-1aa73b0f32c5</ns16:incoming>
                        
                        
                        <ns16:outgoing>b0e1f314-0f86-4fd4-8bba-0669321aa36c</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = tw.local.queryResult[2].rows[0].data[0];</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b7ce4f6e-c980-48cb-8176-951e8b7b3967" targetRef="40afb475-164f-49bb-8664-0101a58c18cb" name="To SQL Execute Multiple Statements (SQLResult)" id="ac17d4eb-6550-4c79-869a-f47f398aa554">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="40afb475-164f-49bb-8664-0101a58c18cb" targetRef="e7a18128-1c99-4abd-8c46-57aef5af818a" name="To Map Output" id="0a15d58a-430e-40e1-8aa2-1aa73b0f32c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7d884c4d-7c4c-4711-8b25-8c351bb2e2f9" targetRef="b7ce4f6e-c980-48cb-8176-951e8b7b3967" name="To Init SQL Query" id="9ce7df7a-3916-4d3d-8fb4-75f0400944cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e7a18128-1c99-4abd-8c46-57aef5af818a" targetRef="ab7733d9-9a8e-41cd-8c12-66fb71d9794e" name="To End" id="b0e1f314-0f86-4fd4-8bba-0669321aa36c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.366a051c-d5e0-40c0-8923-22d501eb28ba" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestID" id="2056.cb484a9d-68e0-4d37-8d1b-6ebd7079a9cb" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpAdvancePayment" id="2056.03de7774-850a-4458-8ad2-54587df860a4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.5b163a03-3714-4c04-8ed1-3e053db66ec8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="queryResult" id="2056.7c4c8d27-8591-4e6e-8a4a-f747073f5fb1" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7d884c4d-7c4c-4711-8b25-8c351bb2e2f9" parallelMultiple="false" name="Error" id="24d58011-39bb-458d-8af0-7cef453d542c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="145" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>82cba171-29e7-4ab7-8157-217481272973</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="eaa9ab8a-c09d-48de-8e40-aee822208779" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fb64280d-e934-4f39-800c-c164634d4454" eventImplId="3389afe1-d663-450d-87ff-6224dbcc31f4">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b7ce4f6e-c980-48cb-8176-951e8b7b3967" parallelMultiple="false" name="Error1" id="18d5a721-e15f-4be3-8e23-fc2f7671d6f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="290" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1fdba31f-6e25-49e1-892b-90515a3826da</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="eae0ece5-9432-4c11-82af-085871223d6f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d566cdb7-2355-4f7b-8761-2f629b9a0bff" eventImplId="7e3c7545-76d6-4926-804b-d511087603fd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="40afb475-164f-49bb-8664-0101a58c18cb" parallelMultiple="false" name="Error2" id="ea6af822-8059-4729-8fed-6a2b91a294dc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="450" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2edd105b-cf23-4377-87d9-4b6cfc9496c8</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="df634062-5021-4e3d-8132-70dcf105c880" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9c4858a4-8d48-4c88-8270-ba66d6f8eef3" eventImplId="40a1a368-1e9b-4911-85e6-beb7fee85fcb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e7a18128-1c99-4abd-8c46-57aef5af818a" parallelMultiple="false" name="Error3" id="025f6d56-b938-4e05-86d6-95dbdcbad066">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="576" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6c96ef8f-e81a-4ef0-8f80-fa369b177dd9</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e94ce2c6-0c89-4272-8a23-773f6574ee27" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="dac907f0-9c38-46eb-8620-84c1b2e7b611" eventImplId="8c8b6630-8bf0-496b-80a9-882fb9ab4445">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="24d58011-39bb-458d-8af0-7cef453d542c" targetRef="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd" name="To End Event" id="82cba171-29e7-4ab7-8157-217481272973">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="18d5a721-e15f-4be3-8e23-fc2f7671d6f2" targetRef="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd" name="To End Event" id="1fdba31f-6e25-49e1-892b-90515a3826da">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ea6af822-8059-4729-8fed-6a2b91a294dc" targetRef="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd" name="To End Event" id="2edd105b-cf23-4377-87d9-4b6cfc9496c8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="025f6d56-b938-4e05-86d6-95dbdcbad066" targetRef="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd" name="To End Event" id="6c96ef8f-e81a-4ef0-8f80-fa369b177dd9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.43e6ed70-f9dc-4444-87f2-4fbb971d4212" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="441" y="209" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6c96ef8f-e81a-4ef0-8f80-fa369b177dd9</ns16:incoming>
                        
                        
                        <ns16:incoming>1fdba31f-6e25-49e1-892b-90515a3826da</ns16:incoming>
                        
                        
                        <ns16:incoming>82cba171-29e7-4ab7-8157-217481272973</ns16:incoming>
                        
                        
                        <ns16:incoming>2edd105b-cf23-4377-87d9-4b6cfc9496c8</ns16:incoming>
                        
                        
                        <ns16:outgoing>28c7d060-4095-4b85-87fe-a3df5d10dd34</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd" targetRef="ab7733d9-9a8e-41cd-8c12-66fb71d9794e" name="To End" id="28c7d060-4095-4b85-87fe-a3df5d10dd34">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b0e1f314-0f86-4fd4-8bba-0669321aa36c</processLinkId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e7a18128-1c99-4abd-8c46-57aef5af818a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ab7733d9-9a8e-41cd-8c12-66fb71d9794e</toProcessItemId>
            <guid>4b02007c-00c9-4fb6-a124-36a743eb8c42</guid>
            <versionId>3a001ab5-27fd-409a-92e9-b7d58b587e67</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e7a18128-1c99-4abd-8c46-57aef5af818a</fromProcessItemId>
            <toProcessItemId>2025.ab7733d9-9a8e-41cd-8c12-66fb71d9794e</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ac17d4eb-6550-4c79-869a-f47f398aa554</processLinkId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b7ce4f6e-c980-48cb-8176-951e8b7b3967</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.40afb475-164f-49bb-8664-0101a58c18cb</toProcessItemId>
            <guid>1d193fcc-ec9d-47e4-9c21-51306ab86234</guid>
            <versionId>4ca9dd59-f7f2-4f9a-ae86-32399b493f4e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b7ce4f6e-c980-48cb-8176-951e8b7b3967</fromProcessItemId>
            <toProcessItemId>2025.40afb475-164f-49bb-8664-0101a58c18cb</toProcessItemId>
        </link>
        <link name="To Map Output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0a15d58a-430e-40e1-8aa2-1aa73b0f32c5</processLinkId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.40afb475-164f-49bb-8664-0101a58c18cb</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.e7a18128-1c99-4abd-8c46-57aef5af818a</toProcessItemId>
            <guid>f9e1f9d0-e71f-45a6-9a6a-437e3c934fa1</guid>
            <versionId>b3295fed-c675-412b-af0f-f4bf5dfd6ad0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.40afb475-164f-49bb-8664-0101a58c18cb</fromProcessItemId>
            <toProcessItemId>2025.e7a18128-1c99-4abd-8c46-57aef5af818a</toProcessItemId>
        </link>
        <link name="To Init SQL Query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9ce7df7a-3916-4d3d-8fb4-75f0400944cf</processLinkId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b7ce4f6e-c980-48cb-8176-951e8b7b3967</toProcessItemId>
            <guid>cae7b404-53e9-4e68-a4de-18d5ccfea1d4</guid>
            <versionId>c9d4d000-d97b-4b30-93df-68067352aeca</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7d884c4d-7c4c-4711-8b25-8c351bb2e2f9</fromProcessItemId>
            <toProcessItemId>2025.b7ce4f6e-c980-48cb-8176-951e8b7b3967</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.28c7d060-4095-4b85-87fe-a3df5d10dd34</processLinkId>
            <processId>1.33e9489c-6751-46e8-bc5c-74b573bdd856</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ab7733d9-9a8e-41cd-8c12-66fb71d9794e</toProcessItemId>
            <guid>4fc21dbf-8a38-479d-b7ba-3eb8b6a7bc15</guid>
            <versionId>e753ff9d-a9c9-414e-9caa-3caa84a50496</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.3d05b13a-f6d4-4076-8b70-f0c1a30eb5fd</fromProcessItemId>
            <toProcessItemId>2025.ab7733d9-9a8e-41cd-8c12-66fb71d9794e</toProcessItemId>
        </link>
    </process>
</teamworks>

