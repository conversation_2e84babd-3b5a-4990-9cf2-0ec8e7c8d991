<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.c3124e3b-0cdd-4710-a44b-6071a04e76dc" name="Get Party Details">
        <lastModified>1692506932942</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d1982c17-fceb-4802-8fd3-cc1edd233f93</guid>
        <versionId>6a1c2fa3-a60c-4e20-bb2d-08875bb8983e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ebc" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04"},{"incoming":["b49532c7-f205-4599-8ca6-1a542994fd6e","eaffeca6-8f44-4ef1-8ef8-963484b84426"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6134"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3e591b0c-b5fd-4e94-89d9-422dae8fc6f4"},{"targetRef":"6556439f-ec75-41b0-81a5-0b5f8422fd32","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Party Details","declaredType":"sequenceFlow","id":"2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29","sourceRef":"0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04"},{"startQuantity":1,"outgoing":["d0de32e9-98c6-4962-a41a-e436c296e7af"],"incoming":["2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29"],"extensionElements":{"postAssignmentScript":["tw.local.results = new tw.object.CustomerFullDetails();\r\ntw.local.results.customerAddress = new tw.object.CustomerAddress();\r\nif (tw.local.isSuccessful) {\r\ntw.local.results = tw.local.customerFullDetails;\r\n}"],"nodeVisualInfo":[{"width":95,"x":173,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Party Details","dataInputAssociation":[{"targetRef":"2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.9e66331e-0f98-44e0-b836-7f39c9a6d317","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.b540e2f5-2008-4705-b4e1-7edcf2a379df","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.4fcde37d-b029-4d8e-ae28-d26a621479e6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.40e09d4c-cff9-4d38-bd81-0995df08be6f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6556439f-ec75-41b0-81a5-0b5f8422fd32","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","declaredType":"TFormalExpression","content":["tw.local.customerFullDetails"]}}],"sourceRef":["2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.3d943bb5-aec9-4b29-862f-735a93741afa"]}],"calledElement":"1.fd9b955b-0237-4cbe-86d6-cd0d295550aa"},{"targetRef":"994aa20b-57dd-494f-8d51-c3278a53d901","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2a57"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"d0de32e9-98c6-4962-a41a-e436c296e7af","sourceRef":"6556439f-ec75-41b0-81a5-0b5f8422fd32"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();\nautoObject.customerNo = \"\";\nautoObject.customerType = \"\";\nautoObject.customerName = \"\";\nautoObject.customerCategory = \"\";\nautoObject.customerBranch = \"\";\nautoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.customerStatus.name = \"\";\nautoObject.customerStatus.value = \"\";\nautoObject.privateCustomer = \"\";\nautoObject.faciliyBranchCode = \"\";\nautoObject.frozen = false;\nautoObject.deceased = false;\nautoObject.sName = \"\";\nautoObject.customerFullName = \"\";\nautoObject.nationality = \"\";\nautoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.country.name = \"\";\nautoObject.country.value = \"\";\nautoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();\nautoObject.customerAddress.addressLine1 = \"\";\nautoObject.customerAddress.addressLine2 = \"\";\nautoObject.customerAddress.addressLine3 = \"\";\nautoObject.customerAddress.addressLine4 = \"\";\nautoObject.customerAddress.customerNO = \"\";\nautoObject.fullName = \"\";\nautoObject.customerArabicName = \"\";\nautoObject.customerEnglishName = \"\";\nautoObject.customerNationalID = \"\";\nautoObject.tradingTitle = \"\";\nautoObject.commercialRegisterNo = \"\";\nautoObject.cardTaxNo = \"\";\nautoObject.businessActivity = \"\";\nautoObject.arabicName = \"\";\nautoObject.EnglishName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.9b2753cb-bcf8-4dcc-bac3-00f302fd0913"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.8945954b-0856-4eac-ace5-4a9adb216c02"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.c90c9e63-3051-46ed-bbec-a41d63214d31"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.c0fc1827-1e8b-4daa-be7a-1761b466d188"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.438c64cc-73eb-4fd4-8a1d-0abd855f4e98"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.30b4c164-2ab1-48a8-870b-bbe3c6a035b2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.ba8d6d66-5dbd-4ff6-93f6-1df302629361"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.b46d9a24-e65e-4137-aa81-c84e1b2326d6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.7acf4694-8fad-49b0-b9fa-912dfda9af2d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.7502c780-1f05-4d89-807b-6614c060ab7e"},{"parallelMultiple":false,"outgoing":["f2934ef0-be2f-4cae-8513-9ef33c63436e"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3a8a9e51-1679-458b-8fec-ba66bdd78968"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"dd1740de-f87c-4d31-8d1a-02d0ef117cb4","otherAttributes":{"eventImplId":"af2a221a-6c75-4858-8a05-faf2d2e7be09"}}],"attachedToRef":"6556439f-ec75-41b0-81a5-0b5f8422fd32","extensionElements":{"nodeVisualInfo":[{"width":24,"x":208,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2","outputSet":{}},{"targetRef":"62674236-f9e1-4b49-8cfe-f5acaacc326b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f2934ef0-be2f-4cae-8513-9ef33c63436e","sourceRef":"4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2"},{"outgoing":["b49532c7-f205-4599-8ca6-1a542994fd6e","270a8893-f918-4a61-8c6b-918493830ced"],"incoming":["d0de32e9-98c6-4962-a41a-e436c296e7af"],"default":"b49532c7-f205-4599-8ca6-1a542994fd6e","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":376,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"994aa20b-57dd-494f-8d51-c3278a53d901"},{"targetRef":"3e591b0c-b5fd-4e94-89d9-422dae8fc6f4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"b49532c7-f205-4599-8ca6-1a542994fd6e","sourceRef":"994aa20b-57dd-494f-8d51-c3278a53d901"},{"targetRef":"62674236-f9e1-4b49-8cfe-f5acaacc326b","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"270a8893-f918-4a61-8c6b-918493830ced","sourceRef":"994aa20b-57dd-494f-8d51-c3278a53d901"},{"startQuantity":1,"outgoing":["eaffeca6-8f44-4ef1-8ef8-963484b84426"],"incoming":["270a8893-f918-4a61-8c6b-918493830ced","f2934ef0-be2f-4cae-8513-9ef33c63436e"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":364,"y":220,"declaredType":"TNodeVisualInfo","height":69}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"62674236-f9e1-4b49-8cfe-f5acaacc326b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"3e591b0c-b5fd-4e94-89d9-422dae8fc6f4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"eaffeca6-8f44-4ef1-8ef8-963484b84426","sourceRef":"62674236-f9e1-4b49-8cfe-f5acaacc326b"}],"laneSet":[{"id":"374d6fd7-4c5e-48f8-a510-68d8ef03229c","lane":[{"flowNodeRef":["0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04","3e591b0c-b5fd-4e94-89d9-422dae8fc6f4","6556439f-ec75-41b0-81a5-0b5f8422fd32","4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2","994aa20b-57dd-494f-8d51-c3278a53d901","62674236-f9e1-4b49-8cfe-f5acaacc326b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c70f8628-25d3-4720-89a1-6005c0f0fd0d","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Party Details","declaredType":"process","id":"1.c3124e3b-0cdd-4710-a44b-6071a04e76dc","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.0822aaaa-ef50-497a-a22e-2f565518ef60"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.7384924e-6a05-43d9-9c7d-807ebadfe15f"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.0822aaaa-ef50-497a-a22e-2f565518ef60","2055.7384924e-6a05-43d9-9c7d-807ebadfe15f"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"02366014\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.5ade16e0-04ef-48e8-8290-c7e68eecf43d"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5ade16e0-04ef-48e8-8290-c7e68eecf43d</processParameterId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"02366014"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0aab76f6-1575-44de-ae65-b38e3f602fee</guid>
            <versionId>38c3ffb3-3c65-4bfb-a22b-672c81b251bb</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0822aaaa-ef50-497a-a22e-2f565518ef60</processParameterId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bdb8dc37-62e7-4fc0-b917-21cd8b40ad20</guid>
            <versionId>5355cf77-7de0-444d-b90e-07643d67c369</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7384924e-6a05-43d9-9c7d-807ebadfe15f</processParameterId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7c1c2e5a-4ce1-4137-b3b2-793853f809ff</guid>
            <versionId>1f829947-1c76-4242-a377-8380695f9124</versionId>
        </processParameter>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b2753cb-bcf8-4dcc-bac3-00f302fd0913</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();
autoObject.customerNo = "";
autoObject.customerType = "";
autoObject.customerName = "";
autoObject.customerCategory = "";
autoObject.customerBranch = "";
autoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerStatus.name = "";
autoObject.customerStatus.value = "";
autoObject.privateCustomer = "";
autoObject.faciliyBranchCode = "";
autoObject.frozen = false;
autoObject.deceased = false;
autoObject.sName = "";
autoObject.customerFullName = "";
autoObject.nationality = "";
autoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.country.name = "";
autoObject.country.value = "";
autoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();
autoObject.customerAddress.addressLine1 = "";
autoObject.customerAddress.addressLine2 = "";
autoObject.customerAddress.addressLine3 = "";
autoObject.customerAddress.addressLine4 = "";
autoObject.customerAddress.customerNO = "";
autoObject.fullName = "";
autoObject.customerArabicName = "";
autoObject.customerEnglishName = "";
autoObject.customerNationalID = "";
autoObject.tradingTitle = "";
autoObject.commercialRegisterNo = "";
autoObject.cardTaxNo = "";
autoObject.businessActivity = "";
autoObject.arabicName = "";
autoObject.EnglishName = "";
autoObject</defaultValue>
            <guid>ab5f51ed-fad7-4723-8187-a6728700960d</guid>
            <versionId>7c9057e0-04b4-4ae1-9f25-4cdf7ba739a6</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8945954b-0856-4eac-ace5-4a9adb216c02</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2717962f-17d2-44bd-b383-00a4d4b85757</guid>
            <versionId>f376f354-ddf5-40fa-8fff-d8624cb9a657</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c90c9e63-3051-46ed-bbec-a41d63214d31</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>db4a8ca1-3a80-45cd-a512-9338293d1b97</guid>
            <versionId>c9167a88-7d09-445f-8ff6-92776e288014</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c0fc1827-1e8b-4daa-be7a-1761b466d188</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>afc74720-7f60-4c26-971e-2e3fa43d90eb</guid>
            <versionId>c91682ec-e57c-4b26-ac70-97d99e89e0c0</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.438c64cc-73eb-4fd4-8a1d-0abd855f4e98</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9a50582c-bdd5-47b6-be78-62779cff1a8f</guid>
            <versionId>f7986637-6a04-4656-8aac-439e6c7a9d20</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.30b4c164-2ab1-48a8-870b-bbe3c6a035b2</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6280e36d-3ec6-4129-abfa-0401b87cc42d</guid>
            <versionId>111b766e-e226-41a0-b5d1-94b6db583f27</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ba8d6d66-5dbd-4ff6-93f6-1df302629361</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e68e4f9f-50b5-454a-b148-9f84892b3c7c</guid>
            <versionId>72cf0167-dee5-4568-b812-77a3f69b1e56</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b46d9a24-e65e-4137-aa81-c84e1b2326d6</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2cc25682-9554-4ae2-a2ec-331b657a051c</guid>
            <versionId>503c507d-3613-44d1-9d85-ef163d152c1e</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7acf4694-8fad-49b0-b9fa-912dfda9af2d</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>91a0ea6f-1133-4c7e-a8b6-a6417a7a2123</guid>
            <versionId>365144fd-b144-4712-bbca-46bc835da994</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7502c780-1f05-4d89-807b-6614c060ab7e</processVariableId>
            <description isNull="true" />
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0b244f82-eda0-43a5-a05f-eb542842ba7d</guid>
            <versionId>5f83fab3-a7a8-4681-ad90-bf583880dd8a</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</processItemId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2175b27f-2441-4c2b-9dc5-0826ddccd041</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6134</guid>
            <versionId>61e746f7-3a38-444a-8f64-950ba4bbfa6b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2175b27f-2441-4c2b-9dc5-0826ddccd041</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>7f09f42c-dbd2-4dea-9961-fd389b5bd71b</guid>
                <versionId>fee0c145-914d-4ec7-ad14-37e941480a54</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</processItemId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.132c169e-6bb9-4e02-864f-d93011effd92</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-ee5</guid>
            <versionId>7479f73c-58d8-48c2-8623-250dff18b05c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="376" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.132c169e-6bb9-4e02-864f-d93011effd92</switchId>
                <guid>669ad1ea-918d-430b-acf7-3df787ebdd58</guid>
                <versionId>86ff56db-1496-43a0-9c06-0018061e73fd</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.72062640-7f6d-4b04-8121-e04476bf0b69</switchConditionId>
                    <switchId>3013.132c169e-6bb9-4e02-864f-d93011effd92</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ebb</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>4b51e1cb-b3ec-4455-a5ed-909a9ae8d034</guid>
                    <versionId>2abd646e-06ad-450d-87bb-0406e39d1fdf</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</processItemId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c1b1b9a9-43be-47c8-badb-635f64ed25dc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eba</guid>
            <versionId>77bf2c6b-bad5-4026-812f-20eb12cf24e1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="364" y="220">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c1b1b9a9-43be-47c8-badb-635f64ed25dc</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>5ad5931a-9f98-4fda-a58d-0feba04a7b80</guid>
                <versionId>6536074b-8b0a-43c2-beb5-a50991eb22dc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</processItemId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <name>Party Details</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6133</guid>
            <versionId>cbdefdfd-71d3-4f29-93bf-06eff7923dbb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.7ff91aed-7b56-4516-9d36-e290d00eeda1</processItemPrePostId>
                <processItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</processItemId>
                <location>2</location>
                <script>tw.local.results = new tw.object.CustomerFullDetails();&#xD;
tw.local.results.customerAddress = new tw.object.CustomerAddress();&#xD;
if (tw.local.isSuccessful) {&#xD;
tw.local.results = tw.local.customerFullDetails;&#xD;
}</script>
                <guid>8c782993-8236-40e6-84c5-cbb5638c4b44</guid>
                <versionId>9e08cdb4-e426-427b-9cc3-639916786dc1</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.acb5b4b1-90b5-4238-8974-855c64385e62</processItemPrePostId>
                <processItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>bd85c112-92d9-4d73-aa80-1fd14338514f</guid>
                <versionId>c0876eaa-82dd-4b56-961c-30319600d5f4</versionId>
            </processPrePosts>
            <layoutData x="173" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eba</errorHandlerItem>
                <errorHandlerItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.fd9b955b-0237-4cbe-86d6-cd0d295550aa</attachedProcessRef>
                <guid>ce774033-e496-4fa9-ac9c-1ce8a4eb169b</guid>
                <versionId>204759a5-1777-47a5-9243-ec762a59b654</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fec52a1f-196a-43f3-b285-a0e6f7106e52</parameterMappingId>
                    <processParameterId>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>aeab1254-5e6b-4fac-9ed2-8216db71ad4f</guid>
                    <versionId>26934925-e384-4c6e-ab24-372fa28d66e6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerFullDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.201c9cbc-ae37-4d14-bd49-998ea218b8fd</parameterMappingId>
                    <processParameterId>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerFullDetails</value>
                    <classRef>/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b7ce2186-12f9-4b82-b426-847d09da4b1a</guid>
                    <versionId>357eed75-72e7-4b1c-9942-026c19bec77f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8b390cbd-d7f6-42ed-9511-50b3c9e5b5fb</parameterMappingId>
                    <processParameterId>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ca3b3ea0-f7a0-443e-ae1c-91a0d1fa3854</guid>
                    <versionId>58e658fb-06b3-4a5d-8a9b-7421711fecaf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.993924f9-95ba-4581-a6af-22d0bd70f4e2</parameterMappingId>
                    <processParameterId>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>dbc71bbd-b1a6-4281-bddb-e7d2526e2b65</guid>
                    <versionId>6b8e0121-ce78-4194-93da-3e5b3aa5b6a0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a30ca297-c313-43d9-83a0-415ed62cd57c</parameterMappingId>
                    <processParameterId>2055.3d943bb5-aec9-4b29-862f-735a93741afa</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2ad417b6-6ace-47a2-9758-e8140a7c02ff</guid>
                    <versionId>6c489923-d41d-4e72-9c61-136a3ff8bc29</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerCif">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e9b58f1b-60dc-4438-bc09-10f5fedf39f9</parameterMappingId>
                    <processParameterId>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e682bc4a-5b72-4f66-ab44-f4ca9aa34fd4</guid>
                    <versionId>76808ca7-2d74-44ff-ba73-0c9cbd0be97a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3125a8b1-ea7f-40b6-8d0b-45e49aa9775c</parameterMappingId>
                    <processParameterId>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ab0f251f-73a3-4a59-858a-c31e6a956852</guid>
                    <versionId>7f672b1f-8866-4bac-9c53-2ae74aeac90a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.68e69abf-0e92-49a9-a429-aa0e3d38f724</parameterMappingId>
                    <processParameterId>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a23c5ec1-557f-4355-94be-7c0bfc6cec8c</guid>
                    <versionId>93cd8b8c-26a9-4b13-97b5-c729b215de87</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cf00c44d-598d-4494-8ae1-d69ee7d16030</parameterMappingId>
                    <processParameterId>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>07228175-1b62-48cf-9c86-0ca7007772e6</guid>
                    <versionId>d0708a8b-7136-4c21-8220-140014917c5f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6ae636de-aaa8-4a9c-8a4e-b18f9b53f504</parameterMappingId>
                    <processParameterId>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8a1cfb8d-e648-4597-8270-d8e4e5ee3f92</guid>
                    <versionId>e8418408-9bc3-4257-b6f5-5b1bd2ddc220</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.12bb5230-**************-97109fd13596</parameterMappingId>
                    <processParameterId>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</processParameterId>
                    <parameterMappingParentId>3012.4dec5714-5ad2-48b8-b7cf-c0225be542c9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6ed18283-6122-4428-a28f-d12e8d8043ee</guid>
                    <versionId>f532a7eb-5242-4b52-956d-8c48eaafa8ea</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Party Details" id="1.c3124e3b-0cdd-4710-a44b-6071a04e76dc" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.5ade16e0-04ef-48e8-8290-c7e68eecf43d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"02366014"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.0822aaaa-ef50-497a-a22e-2f565518ef60" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.7384924e-6a05-43d9-9c7d-807ebadfe15f" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.0822aaaa-ef50-497a-a22e-2f565518ef60</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.7384924e-6a05-43d9-9c7d-807ebadfe15f</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="374d6fd7-4c5e-48f8-a510-68d8ef03229c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c70f8628-25d3-4720-89a1-6005c0f0fd0d" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6556439f-ec75-41b0-81a5-0b5f8422fd32</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>994aa20b-57dd-494f-8d51-c3278a53d901</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>62674236-f9e1-4b49-8cfe-f5acaacc326b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3e591b0c-b5fd-4e94-89d9-422dae8fc6f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6134</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b49532c7-f205-4599-8ca6-1a542994fd6e</ns16:incoming>
                        
                        
                        <ns16:incoming>eaffeca6-8f44-4ef1-8ef8-963484b84426</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="0b02b0b3-0279-49b0-a9b8-bb9ffb12fb04" targetRef="6556439f-ec75-41b0-81a5-0b5f8422fd32" name="To Party Details" id="2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.fd9b955b-0237-4cbe-86d6-cd0d295550aa" name="Party Details" id="6556439f-ec75-41b0-81a5-0b5f8422fd32">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="173" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>tw.local.results = new tw.object.CustomerFullDetails();&#xD;
tw.local.results.customerAddress = new tw.object.CustomerAddress();&#xD;
if (tw.local.isSuccessful) {&#xD;
tw.local.results = tw.local.customerFullDetails;&#xD;
}</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.f7147a30-0590-4d2e-97db-c75b5c2dcb29</ns16:incoming>
                        
                        
                        <ns16:outgoing>d0de32e9-98c6-4962-a41a-e436c296e7af</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651">tw.local.customerFullDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3d943bb5-aec9-4b29-862f-735a93741afa</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6556439f-ec75-41b0-81a5-0b5f8422fd32" targetRef="994aa20b-57dd-494f-8d51-c3278a53d901" name="To is Successful" id="d0de32e9-98c6-4962-a41a-e436c296e7af">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.9b2753cb-bcf8-4dcc-bac3-00f302fd0913">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();
autoObject.customerNo = "";
autoObject.customerType = "";
autoObject.customerName = "";
autoObject.customerCategory = "";
autoObject.customerBranch = "";
autoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerStatus.name = "";
autoObject.customerStatus.value = "";
autoObject.privateCustomer = "";
autoObject.faciliyBranchCode = "";
autoObject.frozen = false;
autoObject.deceased = false;
autoObject.sName = "";
autoObject.customerFullName = "";
autoObject.nationality = "";
autoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.country.name = "";
autoObject.country.value = "";
autoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();
autoObject.customerAddress.addressLine1 = "";
autoObject.customerAddress.addressLine2 = "";
autoObject.customerAddress.addressLine3 = "";
autoObject.customerAddress.addressLine4 = "";
autoObject.customerAddress.customerNO = "";
autoObject.fullName = "";
autoObject.customerArabicName = "";
autoObject.customerEnglishName = "";
autoObject.customerNationalID = "";
autoObject.tradingTitle = "";
autoObject.commercialRegisterNo = "";
autoObject.cardTaxNo = "";
autoObject.businessActivity = "";
autoObject.arabicName = "";
autoObject.EnglishName = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.8945954b-0856-4eac-ace5-4a9adb216c02" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.c90c9e63-3051-46ed-bbec-a41d63214d31" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.c0fc1827-1e8b-4daa-be7a-1761b466d188" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.438c64cc-73eb-4fd4-8a1d-0abd855f4e98" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.30b4c164-2ab1-48a8-870b-bbe3c6a035b2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.ba8d6d66-5dbd-4ff6-93f6-1df302629361" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.b46d9a24-e65e-4137-aa81-c84e1b2326d6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.7acf4694-8fad-49b0-b9fa-912dfda9af2d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.7502c780-1f05-4d89-807b-6614c060ab7e" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6556439f-ec75-41b0-81a5-0b5f8422fd32" parallelMultiple="false" name="Error" id="4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="208" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f2934ef0-be2f-4cae-8513-9ef33c63436e</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3a8a9e51-1679-458b-8fec-ba66bdd78968" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="dd1740de-f87c-4d31-8d1a-02d0ef117cb4" eventImplId="af2a221a-6c75-4858-8a05-faf2d2e7be09">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="4ca27e48-6b6b-45ea-8b0c-83b9e94d28f2" targetRef="62674236-f9e1-4b49-8cfe-f5acaacc326b" name="To End Event" id="f2934ef0-be2f-4cae-8513-9ef33c63436e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="b49532c7-f205-4599-8ca6-1a542994fd6e" name="is Successful" id="994aa20b-57dd-494f-8d51-c3278a53d901">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="376" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d0de32e9-98c6-4962-a41a-e436c296e7af</ns16:incoming>
                        
                        
                        <ns16:outgoing>b49532c7-f205-4599-8ca6-1a542994fd6e</ns16:outgoing>
                        
                        
                        <ns16:outgoing>270a8893-f918-4a61-8c6b-918493830ced</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="994aa20b-57dd-494f-8d51-c3278a53d901" targetRef="3e591b0c-b5fd-4e94-89d9-422dae8fc6f4" name="To Script Task" id="b49532c7-f205-4599-8ca6-1a542994fd6e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="994aa20b-57dd-494f-8d51-c3278a53d901" targetRef="62674236-f9e1-4b49-8cfe-f5acaacc326b" name="To End Event" id="270a8893-f918-4a61-8c6b-918493830ced">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="62674236-f9e1-4b49-8cfe-f5acaacc326b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="220" width="95" height="69" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>270a8893-f918-4a61-8c6b-918493830ced</ns16:incoming>
                        
                        
                        <ns16:incoming>f2934ef0-be2f-4cae-8513-9ef33c63436e</ns16:incoming>
                        
                        
                        <ns16:outgoing>eaffeca6-8f44-4ef1-8ef8-963484b84426</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="62674236-f9e1-4b49-8cfe-f5acaacc326b" targetRef="3e591b0c-b5fd-4e94-89d9-422dae8fc6f4" name="To End" id="eaffeca6-8f44-4ef1-8ef8-963484b84426">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.eaffeca6-8f44-4ef1-8ef8-963484b84426</processLinkId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</toProcessItemId>
            <guid>8abb39ab-b9bb-4d2f-bbe7-ce1fe1b5b789</guid>
            <versionId>0c80d88d-2b5f-4bb9-ab7f-11f4cc804f58</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</fromProcessItemId>
            <toProcessItemId>2025.3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d0de32e9-98c6-4962-a41a-e436c296e7af</processLinkId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</endStateId>
            <toProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</toProcessItemId>
            <guid>f32cd13c-eb0f-4363-a45b-6b65a76d8921</guid>
            <versionId>5d3322b2-ce7f-4213-9c97-87b7c21ae11e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6556439f-ec75-41b0-81a5-0b5f8422fd32</fromProcessItemId>
            <toProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b49532c7-f205-4599-8ca6-1a542994fd6e</processLinkId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</toProcessItemId>
            <guid>49d1b317-64c1-4782-bd65-46d3686a3fb4</guid>
            <versionId>dc59ddb8-c16d-4586-9fde-4262fe913e0b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</fromProcessItemId>
            <toProcessItemId>2025.3e591b0c-b5fd-4e94-89d9-422dae8fc6f4</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.270a8893-f918-4a61-8c6b-918493830ced</processLinkId>
            <processId>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ebb</endStateId>
            <toProcessItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</toProcessItemId>
            <guid>f1a49d43-fead-4a41-9ab0-56ddaf402d12</guid>
            <versionId>eb66693b-9a9b-42bb-a767-655dc643312b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.994aa20b-57dd-494f-8d51-c3278a53d901</fromProcessItemId>
            <toProcessItemId>2025.62674236-f9e1-4b49-8cfe-f5acaacc326b</toProcessItemId>
        </link>
    </process>
</teamworks>

