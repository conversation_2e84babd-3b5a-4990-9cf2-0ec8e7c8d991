<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b" name="Escalation Mail Service">
        <lastModified>1692505468608</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>181e5b20-560c-4ef0-a128-9156b90faaaf</guid>
        <versionId>dde5b93d-5f58-413a-9c4e-335d330420ad</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d85" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.b80aa8d1-78c2-4208-979c-f31164cc49e2"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b533202e-8f1b-4eb7-9ce4-1b0c23af5396"},{"incoming":["42fc4672-0f74-4c08-88b7-03145d9785fd","e608579a-06b4-44d4-8c79-ed193572a2bf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":670,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ae"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"fbed97d9-2f21-41a1-94d5-d0b3f633f9f0"},{"targetRef":"79b6fb37-a581-4efd-9db7-2fa1e0f6b963","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.b80aa8d1-78c2-4208-979c-f31164cc49e2","sourceRef":"b533202e-8f1b-4eb7-9ce4-1b0c23af5396"},{"startQuantity":1,"outgoing":["42fc4672-0f74-4c08-88b7-03145d9785fd"],"incoming":["5d53ef1e-01c8-445d-a775-a7596f8f6413"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":364,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Send Email","dataInputAssociation":[{"targetRef":"2055.a7c74b41-811f-4581-94ef-69a84c74eb84","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.ae81d59d-2fde-4526-9476-d0598d6e8472","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.subject"]}}]},{"targetRef":"2055.6d9bd911-88b8-4ea3-8823-421a4f690290","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.msgBody"]}}]},{"targetRef":"2055.268afc2e-a651-49ef-8704-9a6ff22065c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.20348cf5-023e-4d3a-826e-5b92143ec224","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.1da05789-2131-46bc-aacf-34d84ca37def","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailDebugMode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"bfb17a6c-5904-46ce-bcda-572d634fa8ea","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.ec26c46c-d70b-4881-98d8-40e694dd7362"]}],"calledElement":"1.338e9f4d-8538-4ceb-a155-c288604435d4"},{"targetRef":"fbed97d9-2f21-41a1-94d5-d0b3f633f9f0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2970"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"42fc4672-0f74-4c08-88b7-03145d9785fd","sourceRef":"bfb17a6c-5904-46ce-bcda-572d634fa8ea"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"2056.d937ab94-7808-4f47-ac9c-e1c020af489b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"msgBody","isCollection":false,"declaredType":"dataObject","id":"2056.0201e287-5639-4e69-bc9b-335df0e77087"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"subject","isCollection":false,"declaredType":"dataObject","id":"2056.abb6198a-f241-4ca2-8cb1-7ba3e3168d3b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.11263eb6-a3af-49f5-815f-21cdddfbd464"},{"startQuantity":1,"outgoing":["5d53ef1e-01c8-445d-a775-a7596f8f6413"],"incoming":["2027.b80aa8d1-78c2-4208-979c-f31164cc49e2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":218,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"79b6fb37-a581-4efd-9db7-2fa1e0f6b963","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\nif (tw.local.idcRequest == null) {\r\n\ttw.local.idcRequest = new tw.object.IDCRequest();\r\n\t\r\n\ttw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.appInfo = new tw.object.AppInfo();\r\n\ttw.local.idcRequest.appInfo.instanceID = \"\";\r\n\t\r\n\ttw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();\r\n\t\r\n\ttw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();\r\n\r\n\ttw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\n\t\r\n\t\r\n\ttw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();\r\n\t\r\n\t\r\n\ttw.local.idcRequest.importPurpose = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();\r\n\t\r\n\t\r\n\ttw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();\r\n\ttw.local.idcRequest.customerInformation.customerName = \"\";\r\n\t\r\n\ttw.local.idcRequest.invoices = new tw.object.listOf.Invoice();\r\n\t\r\n\t\r\n\ttw.local.idcRequest.productCategory = new tw.object.DBLookup();\r\n\ttw.local.idcRequest.documentsSource = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.paymentTerms = new tw.object.DBLookup();\r\n\t\r\n\ttw.local.idcRequest.approvals = new tw.object.Approvals();\r\n\t\r\n\ttw.local.idcRequest.appLog = new tw.object.listOf.AppLog();\r\n}\r\n\r\nvar receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;\r\nvar dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;\r\n\/\/var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;\r\n\r\nvar taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;\r\n\r\n\r\nvar owner = tw.system.findTaskByID(tw.local.taskid).owner;\r\n\r\nif (owner == null){owner = \"\"}\r\n\r\ntw.local.subject = \"IDC Request No. \"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t+\" for Customer \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t+\" is Past Due \"\r\n\t\t\t+\"\u0637\u0644\u0628 \u0627\u0644\u062a\u062d\u0635\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u0646\u062f\u0649 \u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0631\u0642\u0645 \"+tw.local.idcRequest.appInfo.instanceID+\r\n\t\t\t\" \u0644\u0644\u0639\u0645\u064a\u0644 \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t+\" \u0642\u062f \u062a\u062e\u0637\u0649 \u0627\u0644\u0648\u0642\u062a \u0627\u0644\u0645\u0639\u064a\u0646 \u0644\u0644\u0645\u0647\u0645\u0629\";\r\n\r\ntw.local.msgBody = '&lt;html dir=\"ltl\" lang=\"en\"&gt;'\r\n\t\t\t+\"&lt;p&gt;Dear Sir \/ Madam,&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;p&gt;Kindly be informed that the Request &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.IDCRequestType.englishdescription\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; with request number &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; is now past due and below are the request details:&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;ul&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Activity Name: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+taskSubject+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Participant: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+owner+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Received Date: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+receivedDate+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Request Status: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.status+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Request Sub-status: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.subStatus+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;Due Date: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+dueDate+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t+\"&lt;\/ul&gt;\"\r\n\t\t\t+\"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;br&gt;&lt;\/br&gt;&lt;p&gt;\u0627\u0644\u0633\u064a\u062f \/ \u0627\u0644\u0633\u064a\u062f\u0629&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;p&gt;\u0628\u0631\u062c\u0627\u0621 \u0627\u0644\u0639\u0644\u0645 \u0623\u0646 \u0637\u0644\u0628 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.IDCRequestType.englishdescription\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; \u0631\u0642\u0645 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; \u0644\u0644\u0639\u0645\u064a\u0644 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; \u0642\u062f \u062a\u062e\u0637\u0649 \u0627\u0644\u0648\u0642\u062a \u0627\u0644\u0645\u0639\u064a\u0646 \u0644\u0644\u0645\u0647\u0645\u0629.&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;p&gt;\u064a\u0648\u062c\u062f \u0623\u062f\u0646\u0627\u0647 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u0623\u0633\u0627\u0633\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0627\u0644\u0637\u0644\u0628.&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;ul&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u0627\u0633\u0645 \u0627\u0644\u0645\u0647\u0645\u0629: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+taskSubject+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+owner+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u062a\u0627\u0631\u064a\u062e \u0627\u0633\u062a\u0644\u0627\u0645 \u0627\u0644\u0645\u0647\u0645\u0629: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+receivedDate+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u062d\u0627\u0644\u0629 \u0627\u0644\u0637\u0644\u0628: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.status+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u0627\u0644\u062d\u0627\u0644\u0629 \u0627\u0644\u0641\u0631\u0639\u064a\u0629 \u0644\u0644\u0637\u0644\u0628: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.subStatus+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t\t+\"&lt;li&gt;\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0646\u062a\u0647\u0627\u0621 \u0627\u0644\u0645\u0639\u064a\u0646: &lt;strong&gt;&lt;em&gt;&amp;lt;\"+dueDate+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt;&lt;\/li&gt;\"\r\n\t\t\t+\"&lt;\/ul&gt;\"\r\n\t\t\t+\"&lt;p&gt;\u0627\u0644\u0631\u062c\u0627\u0621 \u0639\u062f\u0645 \u0627\u0644\u0631\u062f \u0639\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0631\u0633\u0627\u0644\u0629. \u0647\u0630\u0627 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627.&lt;\/p&gt;\"\r\n\t\t+\"&lt;\/html&gt;\"\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"bfb17a6c-5904-46ce-bcda-572d634fa8ea","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Send Email","declaredType":"sequenceFlow","id":"5d53ef1e-01c8-445d-a775-a7596f8f6413","sourceRef":"79b6fb37-a581-4efd-9db7-2fa1e0f6b963"},{"parallelMultiple":false,"outgoing":["24b71c48-7c0d-4ec5-8e19-9002b22b6402"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e76d4fff-d0b8-44dd-89a5-f98fa9f24d6b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d23493b4-c4cd-46a8-8abd-12985a1ab588","otherAttributes":{"eventImplId":"7c05c6f9-c05c-4fcc-8a3c-088c450a28fe"}}],"attachedToRef":"79b6fb37-a581-4efd-9db7-2fa1e0f6b963","extensionElements":{"nodeVisualInfo":[{"width":24,"x":253,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"4a4869ea-6043-46de-81a9-9cd7596734f1","outputSet":{}},{"parallelMultiple":false,"outgoing":["377dac56-6efb-4dd7-81f1-b1f08ac1248b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"450bd5af-ce29-4a81-86d3-b03b36fbc6dc"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"eefe1ee2-7a97-4d84-8ff2-94d7636423cc","otherAttributes":{"eventImplId":"d28ada9c-4b9e-45f4-8c4d-a2eac397b6c8"}}],"attachedToRef":"bfb17a6c-5904-46ce-bcda-572d634fa8ea","extensionElements":{"nodeVisualInfo":[{"width":24,"x":399,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"c818f9cf-a1d2-4154-8909-2861cbc08c66","outputSet":{}},{"startQuantity":1,"outgoing":["e608579a-06b4-44d4-8c79-ed193572a2bf"],"incoming":["24b71c48-7c0d-4ec5-8e19-9002b22b6402","377dac56-6efb-4dd7-81f1-b1f08ac1248b"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":310,"y":174,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0cc2de75-5ff6-4230-82ce-3e43fcbbfda3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"0cc2de75-5ff6-4230-82ce-3e43fcbbfda3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"24b71c48-7c0d-4ec5-8e19-9002b22b6402","sourceRef":"4a4869ea-6043-46de-81a9-9cd7596734f1"},{"targetRef":"0cc2de75-5ff6-4230-82ce-3e43fcbbfda3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"377dac56-6efb-4dd7-81f1-b1f08ac1248b","sourceRef":"c818f9cf-a1d2-4154-8909-2861cbc08c66"},{"targetRef":"fbed97d9-2f21-41a1-94d5-d0b3f633f9f0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e608579a-06b4-44d4-8c79-ed193572a2bf","sourceRef":"0cc2de75-5ff6-4230-82ce-3e43fcbbfda3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.a00e33f6-dc7c-43b2-834d-8e3ed294a0db"}],"laneSet":[{"id":"11a630c2-0f7d-4b64-a2d8-966d278a89a5","lane":[{"flowNodeRef":["b533202e-8f1b-4eb7-9ce4-1b0c23af5396","fbed97d9-2f21-41a1-94d5-d0b3f633f9f0","bfb17a6c-5904-46ce-bcda-572d634fa8ea","79b6fb37-a581-4efd-9db7-2fa1e0f6b963","4a4869ea-6043-46de-81a9-9cd7596734f1","c818f9cf-a1d2-4154-8909-2861cbc08c66","0cc2de75-5ff6-4230-82ce-3e43fcbbfda3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"a0a4bb2d-7849-4918-9bc3-26d965f0eaa6","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Escalation Mail Service","declaredType":"process","id":"1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.ed16834a-e23e-47b7-8cd0-5ebdec1f1d4e"}],"inputSet":[{"dataInputRefs":["2055.fb6c9e80-2768-4eb3-b290-28ef566eafed","2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9","2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8"]}],"outputSet":[{"dataOutputRefs":["2055.ed16834a-e23e-47b7-8cd0-5ebdec1f1d4e"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Y\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailDebugMode","isCollection":false,"id":"2055.fb6c9e80-2768-4eb3-b290-28ef566eafed"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"2078.70913\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskid","isCollection":false,"id":"2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"12345678909876\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"test\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="mailDebugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fb6c9e80-2768-4eb3-b290-28ef566eafed</processParameterId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Y"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>20176c7f-8994-458b-92a1-816b9a3c747d</guid>
            <versionId>ff32756e-ae6b-4a18-9ba0-741e8b2330c0</versionId>
        </processParameter>
        <processParameter name="taskid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9</processParameterId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"2078.70913"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ad36ffbe-c925-40ec-a100-2a52d3d93440</guid>
            <versionId>3c407970-65d3-4508-befb-f727509bebfb</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8</processParameterId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1cc33ce0-cc80-4918-b0c3-ddf17f1a9e4a</guid>
            <versionId>70eadbfc-9f73-4137-a229-f820b3268303</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ed16834a-e23e-47b7-8cd0-5ebdec1f1d4e</processParameterId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2eb264bb-2f04-4bea-af4a-5d48fbccd241</guid>
            <versionId>3ad08fbc-4a52-45cb-8b37-ee3d3b39718d</versionId>
        </processParameter>
        <processVariable name="mailTo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d937ab94-7808-4f47-ac9c-e1c020af489b</processVariableId>
            <description isNull="true" />
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>2e4b2531-6065-4899-866b-0a3e5db800d5</guid>
            <versionId>51d155bb-2a7c-4f92-bcff-6e77df057af9</versionId>
        </processVariable>
        <processVariable name="msgBody">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0201e287-5639-4e69-bc9b-335df0e77087</processVariableId>
            <description isNull="true" />
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>5c9f1899-3748-4f90-b814-3734bb160ae9</guid>
            <versionId>0ced7146-3d27-452c-8ff2-0bb8838816c7</versionId>
        </processVariable>
        <processVariable name="subject">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.abb6198a-f241-4ca2-8cb1-7ba3e3168d3b</processVariableId>
            <description isNull="true" />
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>23f19734-3890-4a0f-ab78-33bcd0d84685</guid>
            <versionId>5da8ca00-62ca-4d58-8486-4ff2c231afbc</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.11263eb6-a3af-49f5-815f-21cdddfbd464</processVariableId>
            <description isNull="true" />
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eb4ce993-8679-436f-95b4-7318ede653f4</guid>
            <versionId>ec90070f-12b9-459b-bc07-b5560bdda44b</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a00e33f6-dc7c-43b2-834d-8e3ed294a0db</processVariableId>
            <description isNull="true" />
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fed7624a-b899-418e-a746-e8684f4243ce</guid>
            <versionId>7b569f03-8c63-4940-a3fd-e4936324cd7c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bfb17a6c-5904-46ce-bcda-572d634fa8ea</processItemId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <name>Send Email</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ac</guid>
            <versionId>0bf4bd29-fdb1-42ed-ad8f-c96f19bfb3d7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d84</errorHandlerItem>
                <errorHandlerItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.338e9f4d-8538-4ceb-a155-c288604435d4</attachedProcessRef>
                <guid>233650c0-a8b8-4ee3-974f-97fe9f5746eb</guid>
                <versionId>34f49aae-ddb3-4a1d-97e5-ab7687808359</versionId>
                <parameterMapping name="mailTo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6f6a293a-bd5f-4df0-8d30-4a7f85bdeb55</parameterMappingId>
                    <processParameterId>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailTo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d84eeae3-7bd0-4324-8960-ad9f7636ddff</guid>
                    <versionId>095b47b5-f146-4112-bbcd-3616dae807d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.858ea508-f159-4224-b993-41adb0a856bb</parameterMappingId>
                    <processParameterId>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4b1fe1c0-742a-4278-af35-459ab017a2d8</guid>
                    <versionId>134091f7-a47a-4ed1-9a48-2012195b5bf5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="attachments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ff8795f2-5951-43c2-8c61-f07d786e3dac</parameterMappingId>
                    <processParameterId>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.8a11240b-682f-4caf-9f03-5ce6a64d720b</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>bf8fa1de-0626-48cb-909c-98d804c9e0b4</guid>
                    <versionId>28228b1d-6fc8-4a1d-8cb3-476e08943bcc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="subject">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ac37148f-5e39-4b5a-86d6-382c870a76e9</parameterMappingId>
                    <processParameterId>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subject</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5b0c6593-7eaf-45f4-a3e3-e6257895bba2</guid>
                    <versionId>5906a28d-ad68-41ab-aefb-d53265bd2885</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="msgBody">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.56066d45-9373-4479-90dc-117b25046f90</parameterMappingId>
                    <processParameterId>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.msgBody</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ec45f4e3-d34d-40f2-83b5-51a0a8c306e1</guid>
                    <versionId>cbdf445c-7077-429c-979c-ad1e271b0191</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6d7cace5-fda3-49c0-b99a-318cf5fd58b1</parameterMappingId>
                    <processParameterId>2055.20348cf5-023e-4d3a-826e-5b92143ec224</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8f687568-bffc-4626-bff3-1532762632bd</guid>
                    <versionId>d54eaa98-1547-42bc-b9ee-5320d194ed22</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailDebugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5af2c014-1ca4-44fe-9aca-da456c561695</parameterMappingId>
                    <processParameterId>2055.1da05789-2131-46bc-aacf-34d84ca37def</processParameterId>
                    <parameterMappingParentId>3012.b5e749c4-81ab-426e-958a-be0e83cd9a2e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailDebugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1d40bb3a-86f9-4217-a3e4-62948d0dd1bc</guid>
                    <versionId>ddc03b8b-0c76-4c7e-bdcc-65943f79e6a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</processItemId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.66e640c8-8ac3-4bf5-b344-69c40d682835</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ae</guid>
            <versionId>404b2090-d914-419d-806b-b177e1d8684e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="670" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.66e640c8-8ac3-4bf5-b344-69c40d682835</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>98e18d57-8c69-4a74-a033-7ff13e562bb8</guid>
                <versionId>73c12c25-c9b2-40e5-ae7e-609f6151e2f8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</processItemId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.03796496-8ddc-41a8-8465-e7946fa6b549</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ad</guid>
            <versionId>b3310bf9-b94a-4e73-9006-0f2b89894d18</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.42da7275-64c1-45bc-9a52-b05423054186</processItemPrePostId>
                <processItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>894e917b-fa8a-4f9d-aa6b-120b992a423e</guid>
                <versionId>4fe7de9f-40e8-47c4-b8d5-9128d751cfbf</versionId>
            </processPrePosts>
            <layoutData x="218" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d84</errorHandlerItem>
                <errorHandlerItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.03796496-8ddc-41a8-8465-e7946fa6b549</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	&#xD;
	tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
	tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
&#xD;
	tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.customerName = "";&#xD;
	&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
}&#xD;
&#xD;
var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
&#xD;
var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
&#xD;
&#xD;
var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
&#xD;
if (owner == null){owner = ""}&#xD;
&#xD;
tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" is Past Due "&#xD;
			+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
			" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" قد تخطى الوقت المعين للمهمة";&#xD;
&#xD;
tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
			+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Kindly be informed that the Request &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.IDCRequestType.englishdescription&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; is now past due and below are the request details:&lt;/p&gt;"&#xD;
			+"&lt;ul&gt;"&#xD;
				+"&lt;li&gt;Activity Name: &lt;strong&gt;&lt;em&gt;&amp;lt;"+taskSubject+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Participant: &lt;strong&gt;&lt;em&gt;&amp;lt;"+owner+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Received Date: &lt;strong&gt;&lt;em&gt;&amp;lt;"+receivedDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Request Status: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.status+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Request Sub-status: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.subStatus+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Due Date: &lt;strong&gt;&lt;em&gt;&amp;lt;"+dueDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
			+"&lt;/ul&gt;"&#xD;
			+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
			+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
			+"&lt;p&gt;برجاء العلم أن طلب &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.IDCRequestType.englishdescription&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; قد تخطى الوقت المعين للمهمة.&lt;/p&gt;"&#xD;
			+"&lt;p&gt;يوجد أدناه البيانات الأساسية الخاصة بالطلب.&lt;/p&gt;"&#xD;
			+"&lt;ul&gt;"&#xD;
				+"&lt;li&gt;اسم المهمة: &lt;strong&gt;&lt;em&gt;&amp;lt;"+taskSubject+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;المستخدم: &lt;strong&gt;&lt;em&gt;&amp;lt;"+owner+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;تاريخ استلام المهمة: &lt;strong&gt;&lt;em&gt;&amp;lt;"+receivedDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;حالة الطلب: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.status+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;الحالة الفرعية للطلب: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.subStatus+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;تاريخ الانتهاء المعين: &lt;strong&gt;&lt;em&gt;&amp;lt;"+dueDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
			+"&lt;/ul&gt;"&#xD;
			+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
		+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>0b253f71-3990-4e19-8be8-9976671d0cc4</guid>
                <versionId>a3f26695-aa05-4d77-9be2-02638033c607</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</processItemId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f65c903f-645a-454c-8645-6c0c776a7057</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d84</guid>
            <versionId>d6241caf-a1fa-46c7-bad2-a768602d61bf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="310" y="174">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f65c903f-645a-454c-8645-6c0c776a7057</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>ea36329b-6358-4eb0-b271-e255a26d91e1</guid>
                <versionId>9c417544-8788-42d4-99a5-785ca40417ad</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Escalation Mail Service" id="1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="mailDebugMode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.fb6c9e80-2768-4eb3-b290-28ef566eafed">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Y"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="taskid" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"2078.70913"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "12345678909876";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "test";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.ed16834a-e23e-47b7-8cd0-5ebdec1f1d4e" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.fb6c9e80-2768-4eb3-b290-28ef566eafed</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.ed16834a-e23e-47b7-8cd0-5ebdec1f1d4e</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="11a630c2-0f7d-4b64-a2d8-966d278a89a5">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="a0a4bb2d-7849-4918-9bc3-26d965f0eaa6" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b533202e-8f1b-4eb7-9ce4-1b0c23af5396</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bfb17a6c-5904-46ce-bcda-572d634fa8ea</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>79b6fb37-a581-4efd-9db7-2fa1e0f6b963</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4a4869ea-6043-46de-81a9-9cd7596734f1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c818f9cf-a1d2-4154-8909-2861cbc08c66</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b533202e-8f1b-4eb7-9ce4-1b0c23af5396">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.b80aa8d1-78c2-4208-979c-f31164cc49e2</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="fbed97d9-2f21-41a1-94d5-d0b3f633f9f0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="670" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ae</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>42fc4672-0f74-4c08-88b7-03145d9785fd</ns16:incoming>
                        
                        
                        <ns16:incoming>e608579a-06b4-44d4-8c79-ed193572a2bf</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b533202e-8f1b-4eb7-9ce4-1b0c23af5396" targetRef="79b6fb37-a581-4efd-9db7-2fa1e0f6b963" name="To Exclusive Gateway" id="2027.b80aa8d1-78c2-4208-979c-f31164cc49e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.338e9f4d-8538-4ceb-a155-c288604435d4" name="Send Email" id="bfb17a6c-5904-46ce-bcda-572d634fa8ea">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5d53ef1e-01c8-445d-a775-a7596f8f6413</ns16:incoming>
                        
                        
                        <ns16:outgoing>42fc4672-0f74-4c08-88b7-03145d9785fd</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailTo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.subject</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.msgBody</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.20348cf5-023e-4d3a-826e-5b92143ec224</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1da05789-2131-46bc-aacf-34d84ca37def</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailDebugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="bfb17a6c-5904-46ce-bcda-572d634fa8ea" targetRef="fbed97d9-2f21-41a1-94d5-d0b3f633f9f0" name="To End" id="42fc4672-0f74-4c08-88b7-03145d9785fd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="2056.d937ab94-7808-4f47-ac9c-e1c020af489b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="msgBody" id="2056.0201e287-5639-4e69-bc9b-335df0e77087">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="subject" id="2056.abb6198a-f241-4ca2-8cb1-7ba3e3168d3b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.11263eb6-a3af-49f5-815f-21cdddfbd464" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="79b6fb37-a581-4efd-9db7-2fa1e0f6b963">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="218" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.b80aa8d1-78c2-4208-979c-f31164cc49e2</ns16:incoming>
                        
                        
                        <ns16:outgoing>5d53ef1e-01c8-445d-a775-a7596f8f6413</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
if (tw.local.idcRequest == null) {&#xD;
	tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
	&#xD;
	tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
	tw.local.idcRequest.appInfo.instanceID = "";&#xD;
	&#xD;
	tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
&#xD;
	tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
	tw.local.idcRequest.customerInformation.customerName = "";&#xD;
	&#xD;
	tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
	&#xD;
	&#xD;
	tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
	tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
	&#xD;
	tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
	&#xD;
	tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
}&#xD;
&#xD;
var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
&#xD;
var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
&#xD;
&#xD;
var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
&#xD;
if (owner == null){owner = ""}&#xD;
&#xD;
tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" is Past Due "&#xD;
			+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
			" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
			+" قد تخطى الوقت المعين للمهمة";&#xD;
&#xD;
tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
			+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
			+"&lt;p&gt;Kindly be informed that the Request &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.IDCRequestType.englishdescription&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; is now past due and below are the request details:&lt;/p&gt;"&#xD;
			+"&lt;ul&gt;"&#xD;
				+"&lt;li&gt;Activity Name: &lt;strong&gt;&lt;em&gt;&amp;lt;"+taskSubject+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Participant: &lt;strong&gt;&lt;em&gt;&amp;lt;"+owner+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Received Date: &lt;strong&gt;&lt;em&gt;&amp;lt;"+receivedDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Request Status: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.status+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Request Sub-status: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.subStatus+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;Due Date: &lt;strong&gt;&lt;em&gt;&amp;lt;"+dueDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
			+"&lt;/ul&gt;"&#xD;
			+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
			+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
			+"&lt;p&gt;برجاء العلم أن طلب &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.IDCRequestType.englishdescription&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
			+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; قد تخطى الوقت المعين للمهمة.&lt;/p&gt;"&#xD;
			+"&lt;p&gt;يوجد أدناه البيانات الأساسية الخاصة بالطلب.&lt;/p&gt;"&#xD;
			+"&lt;ul&gt;"&#xD;
				+"&lt;li&gt;اسم المهمة: &lt;strong&gt;&lt;em&gt;&amp;lt;"+taskSubject+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;المستخدم: &lt;strong&gt;&lt;em&gt;&amp;lt;"+owner+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;تاريخ استلام المهمة: &lt;strong&gt;&lt;em&gt;&amp;lt;"+receivedDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;حالة الطلب: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.status+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;الحالة الفرعية للطلب: &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.subStatus+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
				+"&lt;li&gt;تاريخ الانتهاء المعين: &lt;strong&gt;&lt;em&gt;&amp;lt;"+dueDate+"&amp;gt;&lt;/em&gt;&lt;/strong&gt;&lt;/li&gt;"&#xD;
			+"&lt;/ul&gt;"&#xD;
			+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
		+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="79b6fb37-a581-4efd-9db7-2fa1e0f6b963" targetRef="bfb17a6c-5904-46ce-bcda-572d634fa8ea" name="To Send Email" id="5d53ef1e-01c8-445d-a775-a7596f8f6413">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="79b6fb37-a581-4efd-9db7-2fa1e0f6b963" parallelMultiple="false" name="Error" id="4a4869ea-6043-46de-81a9-9cd7596734f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="253" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>24b71c48-7c0d-4ec5-8e19-9002b22b6402</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e76d4fff-d0b8-44dd-89a5-f98fa9f24d6b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d23493b4-c4cd-46a8-8abd-12985a1ab588" eventImplId="7c05c6f9-c05c-4fcc-8a3c-088c450a28fe">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="bfb17a6c-5904-46ce-bcda-572d634fa8ea" parallelMultiple="false" name="Error1" id="c818f9cf-a1d2-4154-8909-2861cbc08c66">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="399" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>377dac56-6efb-4dd7-81f1-b1f08ac1248b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="450bd5af-ce29-4a81-86d3-b03b36fbc6dc" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="eefe1ee2-7a97-4d84-8ff2-94d7636423cc" eventImplId="d28ada9c-4b9e-45f4-8c4d-a2eac397b6c8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="0cc2de75-5ff6-4230-82ce-3e43fcbbfda3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="310" y="174" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>24b71c48-7c0d-4ec5-8e19-9002b22b6402</ns16:incoming>
                        
                        
                        <ns16:incoming>377dac56-6efb-4dd7-81f1-b1f08ac1248b</ns16:incoming>
                        
                        
                        <ns16:outgoing>e608579a-06b4-44d4-8c79-ed193572a2bf</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4a4869ea-6043-46de-81a9-9cd7596734f1" targetRef="0cc2de75-5ff6-4230-82ce-3e43fcbbfda3" name="To Catch Errors" id="24b71c48-7c0d-4ec5-8e19-9002b22b6402">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c818f9cf-a1d2-4154-8909-2861cbc08c66" targetRef="0cc2de75-5ff6-4230-82ce-3e43fcbbfda3" name="To Catch Errors" id="377dac56-6efb-4dd7-81f1-b1f08ac1248b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0cc2de75-5ff6-4230-82ce-3e43fcbbfda3" targetRef="fbed97d9-2f21-41a1-94d5-d0b3f633f9f0" name="To End" id="e608579a-06b4-44d4-8c79-ed193572a2bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.a00e33f6-dc7c-43b2-834d-8e3ed294a0db" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Send Email">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5d53ef1e-01c8-445d-a775-a7596f8f6413</processLinkId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bfb17a6c-5904-46ce-bcda-572d634fa8ea</toProcessItemId>
            <guid>dd7707ea-f78a-454a-8395-cb28e78c2473</guid>
            <versionId>47436c08-7cc7-4217-8dda-774e4335e007</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.79b6fb37-a581-4efd-9db7-2fa1e0f6b963</fromProcessItemId>
            <toProcessItemId>2025.bfb17a6c-5904-46ce-bcda-572d634fa8ea</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.42fc4672-0f74-4c08-88b7-03145d9785fd</processLinkId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.bfb17a6c-5904-46ce-bcda-572d634fa8ea</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</endStateId>
            <toProcessItemId>2025.fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</toProcessItemId>
            <guid>70987475-6f70-4184-a41f-6b43c51cf45b</guid>
            <versionId>80cbdb2d-69f7-4c89-aaf9-274872e8caf9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.bfb17a6c-5904-46ce-bcda-572d634fa8ea</fromProcessItemId>
            <toProcessItemId>2025.fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e608579a-06b4-44d4-8c79-ed193572a2bf</processLinkId>
            <processId>1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</toProcessItemId>
            <guid>6e035ffc-83d3-4739-aae2-7546f779055b</guid>
            <versionId>8da5a233-0bdd-4ac8-8fce-857ffc13e788</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.0cc2de75-5ff6-4230-82ce-3e43fcbbfda3</fromProcessItemId>
            <toProcessItemId>2025.fbed97d9-2f21-41a1-94d5-d0b3f633f9f0</toProcessItemId>
        </link>
    </process>
</teamworks>

