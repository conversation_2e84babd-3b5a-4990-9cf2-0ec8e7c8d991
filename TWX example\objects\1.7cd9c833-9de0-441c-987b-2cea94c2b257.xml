<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.7cd9c833-9de0-441c-987b-2cea94c2b257" name="test json">
        <lastModified>1703531620701</lastModified>
        <lastModifiedBy>eslam</lastModifiedBy>
        <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8a06adae-983e-44ee-8eb7-45b9a835b9fd</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-8fd</guid>
        <versionId>7065b81a-a1fb-4044-a3d4-9c906e371585</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:1b737c4ee9445496:-128a99a6:18c88c30de4:-846" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.2ef3cf54-6b83-4990-8823-e85af73df3e9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"6f38e899-0195-4843-8b08-124b2902acc7"},{"incoming":["801f05cd-4bc0-40c7-83de-ab305bc00a87"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-8fb"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"2b98ac93-7c25-40fd-89d7-38bc8d4e05f1"},{"targetRef":"8a06adae-983e-44ee-8eb7-45b9a835b9fd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.2ef3cf54-6b83-4990-8823-e85af73df3e9","sourceRef":"6f38e899-0195-4843-8b08-124b2902acc7"},{"startQuantity":1,"outgoing":["801f05cd-4bc0-40c7-83de-ab305bc00a87"],"incoming":["2027.2ef3cf54-6b83-4990-8823-e85af73df3e9"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":266,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8a06adae-983e-44ee-8eb7-45b9a835b9fd","scriptFormat":"text\/x-javascript","script":{"content":["var obj = JSON.parse(tw.local.data);;\r\n\r\nobj.customerInformation.CIFNumber = \"12345678\";\r\ntw.local.results = obj;"]}},{"targetRef":"2b98ac93-7c25-40fd-89d7-38bc8d4e05f1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"801f05cd-4bc0-40c7-83de-ab305bc00a87","sourceRef":"8a06adae-983e-44ee-8eb7-45b9a835b9fd"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\nautoObject.customerInformation.facilityType.id = 0;\nautoObject.customerInformation.facilityType.code = \"\";\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\nautoObject.customerInformation.facilityType.englishdescription = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new TWDate();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idc","isCollection":false,"declaredType":"dataObject","id":"2056.174575a4-befe-47b0-8913-34a1beffa7f2"}],"laneSet":[{"id":"89b2e281-7160-4b5d-86ba-a7fc746d8807","lane":[{"flowNodeRef":["6f38e899-0195-4843-8b08-124b2902acc7","2b98ac93-7c25-40fd-89d7-38bc8d4e05f1","8a06adae-983e-44ee-8eb7-45b9a835b9fd"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"9e2269d9-cf39-4e4a-8cb7-847b4f59e1d9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"test json","declaredType":"process","id":"1.7cd9c833-9de0-441c-987b-2cea94c2b257","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.f5d22427-252b-460c-8989-ec126886e528"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.5a87f3d7-44bd-48ed-8169-33debd0d8e11"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\r\n'{\"_inherited\":{\"p\":1},\"childrenCache\":{\"customerInformation\":{\"_inherited\":{\"p\":1},\"childrenCache\":{},\"_objectPath\":\"local.idc.customerInformation\",\"_systemCallbackHandle\":{},\"CIFNumber\":\"\",\"importCardNumber\":\"\",\"commercialRegistrationNumber\":\"\",\"customerName\":\"\",\"isCustomeSanctionedbyCBE\":\"\",\"CBENumber\":\"\",\"customerSector\":\"\",\"facilityType\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"commercialRegistrationOffice\":\"\",\"taxCardNumber\":\"\",\"customerType\":\"\",\"addressLine1\":\"\",\"addressLine2\":\"\"}},\"_objectPath\":\"local.idc\",\"_systemCallbackHandle\":{},\"IDCRequestState\":\"\",\"commodityDescription\":\"\",\"countryOfOrigin\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"appInfo\":{\"requestDate\":\"\",\"status\":\"\",\"subStatus\":\"\",\"initiator\":\"\",\"branch\":{\"name\":\"\",\"value\":\"\"},\"requestName\":\"\",\"requestType\":\"\",\"stepName\":\"\",\"appRef\":\"\",\"appID\":\"\",\"instanceID\":\"\"},\"productsDetails\":{\"destinationPort\":\"\",\"shippingDate\":\"2023-12-25T19:55:09.888Z\",\"HSProduct\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"incoterms\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"ACID\":\"\",\"CBECommodityClassification\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"shipmentMethod\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"}},\"financialDetails\":{\"isAdvancePaymentsUsed\":false,\"paymentTerms\":{\"items\":[{\"installmentAmount\":0,\"installmentDate\":\"2023-12-25T19:55:09.888Z\"}],\"listAllSelectedIndices\":[],\"te\":\"Pay\"},\"usedAdvancePayment\":{\"items\":[{\"AllocatedAmountinRequestCurrency\":0,\"invoiceCurrency\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"AmountAllocated\":0,\"advancePaymentRequestNumber\":\"\",\"outstandingAmount\":0,\"invoiceNumber\":\"\",\"paidAmount\":0,\"beneficiaryName\":\"\",\"documentAmount\":0,\"DBID\":0}],\"listAllSelectedIndices\":[],\"te\":\"Use\"},\"discountAmt\":0,\"firstInstallementMaturityDate\":\"2023-12-25T19:55:09.888Z\",\"facilityAmtInDocCurrency\":0,\"amtSight\":0,\"beneficiaryDetails\":{\"account\":\"\",\"bank\":\"\",\"correspondentRefNum\":\"\",\"name\":\"\",\"country\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"}},\"amtDeferredNoAvalized\":0,\"amtPayableByNBE\":0,\"executionHub\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"sourceOfForeignCurrency\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"facilityAmtWithNoCurrency\":0,\"documentCurrency\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"daysTillMaturity\":0,\"tradeFinanceApprovalNumber\":\"\",\"amountAdvanced\":0,\"paymentAccount\":\"\",\"tradeFOReferenceNumber\":\"\",\"documentAmount\":0,\"sourceOfFunds\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"chargesAccount\":\"\",\"CashAmtWithNoCurrency\":0,\"amtDeferredAvalized\":0,\"amtPaidbyOtherBanks\":0,\"cashAmtInDocCurrency\":0},\"IDCRequestType\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"isIDCWithdrawn\":false,\"IDCRequestStage\":\"\",\"FCContractNumber\":\"\",\"billOfLading\":{\"items\":[{\"date\":\"2023-12-25T19:55:09.888Z\",\"number\":\"\"}],\"listAllSelectedIndices\":[],\"te\":\"Inv\"},\"importPurpose\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"IDCRequestNature\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"customerInformation\":{\"CIFNumber\":\"\",\"importCardNumber\":\"\",\"commercialRegistrationNumber\":\"\",\"customerName\":\"\",\"isCustomeSanctionedbyCBE\":\"\",\"CBENumber\":\"\",\"customerSector\":\"\",\"facilityType\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"commercialRegistrationOffice\":\"\",\"taxCardNumber\":\"\",\"customerType\":\"\",\"addressLine1\":\"\",\"addressLine2\":\"\"},\"invoices\":{\"items\":[{\"date\":\"2023-12-25T19:55:09.888Z\",\"number\":\"\"}],\"listAllSelectedIndices\":[],\"te\":\"Inv\"},\"productCategory\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"documentsSource\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"ParentIDCRequestNumber\":\"\",\"paymentTerms\":{\"id\":0,\"code\":\"\",\"arabicdescription\":\"\",\"englishdescription\":\"\"},\"approvals\":{\"CAD\":false,\"treasury\":false,\"compliance\":false},\"appLog\":{\"items\":[{\"startTime\":\"2023-12-25T19:55:09.888Z\",\"endTime\":\"2023-12-25T19:55:09.888Z\",\"userName\":\"\",\"role\":\"\",\"step\":\"\",\"action\":\"\",\"comment\":\"\",\"terminateReason\":\"\",\"returnReason\":\"\"}],\"listAllSelectedIndices\":[],\"te\":\"App2\"},\"stepLog\":{\"startTime\":\"2023-12-25T19:55:09.888Z\",\"endTime\":\"2023-12-25T19:55:09.888Z\",\"userName\":\"\",\"role\":\"\",\"step\":\"\",\"action\":\"\",\"comment\":\"\",\"terminateReason\":\"\",\"returnReason\":\"\"},\"DBID\":0,\"requestDate\":\"2023-12-25T19:55:09.888Z\"}'"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.b45c6a0c-a5d8-4d8f-80b7-a992d6a66772"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b45c6a0c-a5d8-4d8f-80b7-a992d6a66772</processParameterId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>&#xD;
'{"_inherited":{"p":1},"childrenCache":{"customerInformation":{"_inherited":{"p":1},"childrenCache":{},"_objectPath":"local.idc.customerInformation","_systemCallbackHandle":{},"CIFNumber":"","importCardNumber":"","commercialRegistrationNumber":"","customerName":"","isCustomeSanctionedbyCBE":"","CBENumber":"","customerSector":"","facilityType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"commercialRegistrationOffice":"","taxCardNumber":"","customerType":"","addressLine1":"","addressLine2":""}},"_objectPath":"local.idc","_systemCallbackHandle":{},"IDCRequestState":"","commodityDescription":"","countryOfOrigin":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"appInfo":{"requestDate":"","status":"","subStatus":"","initiator":"","branch":{"name":"","value":""},"requestName":"","requestType":"","stepName":"","appRef":"","appID":"","instanceID":""},"productsDetails":{"destinationPort":"","shippingDate":"2023-12-25T19:55:09.888Z","HSProduct":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"incoterms":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"ACID":"","CBECommodityClassification":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"shipmentMethod":{"id":0,"code":"","arabicdescription":"","englishdescription":""}},"financialDetails":{"isAdvancePaymentsUsed":false,"paymentTerms":{"items":[{"installmentAmount":0,"installmentDate":"2023-12-25T19:55:09.888Z"}],"listAllSelectedIndices":[],"te":"Pay"},"usedAdvancePayment":{"items":[{"AllocatedAmountinRequestCurrency":0,"invoiceCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"AmountAllocated":0,"advancePaymentRequestNumber":"","outstandingAmount":0,"invoiceNumber":"","paidAmount":0,"beneficiaryName":"","documentAmount":0,"DBID":0}],"listAllSelectedIndices":[],"te":"Use"},"discountAmt":0,"firstInstallementMaturityDate":"2023-12-25T19:55:09.888Z","facilityAmtInDocCurrency":0,"amtSight":0,"beneficiaryDetails":{"account":"","bank":"","correspondentRefNum":"","name":"","country":{"id":0,"code":"","arabicdescription":"","englishdescription":""}},"amtDeferredNoAvalized":0,"amtPayableByNBE":0,"executionHub":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"sourceOfForeignCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"facilityAmtWithNoCurrency":0,"documentCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"daysTillMaturity":0,"tradeFinanceApprovalNumber":"","amountAdvanced":0,"paymentAccount":"","tradeFOReferenceNumber":"","documentAmount":0,"sourceOfFunds":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"chargesAccount":"","CashAmtWithNoCurrency":0,"amtDeferredAvalized":0,"amtPaidbyOtherBanks":0,"cashAmtInDocCurrency":0},"IDCRequestType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"isIDCWithdrawn":false,"IDCRequestStage":"","FCContractNumber":"","billOfLading":{"items":[{"date":"2023-12-25T19:55:09.888Z","number":""}],"listAllSelectedIndices":[],"te":"Inv"},"importPurpose":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"IDCRequestNature":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"customerInformation":{"CIFNumber":"","importCardNumber":"","commercialRegistrationNumber":"","customerName":"","isCustomeSanctionedbyCBE":"","CBENumber":"","customerSector":"","facilityType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"commercialRegistrationOffice":"","taxCardNumber":"","customerType":"","addressLine1":"","addressLine2":""},"invoices":{"items":[{"date":"2023-12-25T19:55:09.888Z","number":""}],"listAllSelectedIndices":[],"te":"Inv"},"productCategory":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"documentsSource":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"ParentIDCRequestNumber":"","paymentTerms":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"approvals":{"CAD":false,"treasury":false,"compliance":false},"appLog":{"items":[{"startTime":"2023-12-25T19:55:09.888Z","endTime":"2023-12-25T19:55:09.888Z","userName":"","role":"","step":"","action":"","comment":"","terminateReason":"","returnReason":""}],"listAllSelectedIndices":[],"te":"App2"},"stepLog":{"startTime":"2023-12-25T19:55:09.888Z","endTime":"2023-12-25T19:55:09.888Z","userName":"","role":"","step":"","action":"","comment":"","terminateReason":"","returnReason":""},"DBID":0,"requestDate":"2023-12-25T19:55:09.888Z"}'</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b0b10c5b-4f67-49f1-ba2a-33ff034cc9ca</guid>
            <versionId>35fd4b88-e457-43d7-9a67-8f0a3d587996</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f5d22427-252b-460c-8989-ec126886e528</processParameterId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6553ab16-8885-4a09-826d-1de258e7f787</guid>
            <versionId>2c9fb03f-871c-4a4e-b548-182841434e2d</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5a87f3d7-44bd-48ed-8169-33debd0d8e11</processParameterId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>161bc456-34bc-46db-a5f0-658e123a78f4</guid>
            <versionId>1b6e49b2-2df2-4637-83b3-a8f4a2115279</versionId>
        </processParameter>
        <processVariable name="idc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.174575a4-befe-47b0-8913-34a1beffa7f2</processVariableId>
            <description isNull="true" />
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new TWDate();
autoObject</defaultValue>
            <guid>520e7e99-26c1-43a4-9f99-64411f81005e</guid>
            <versionId>a1243a20-aa3a-46c8-9308-ca80eb56a0c1</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2b98ac93-7c25-40fd-89d7-38bc8d4e05f1</processItemId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0f09ccb1-d263-4969-9fb7-c5cf4b38c114</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-8fb</guid>
            <versionId>499dadf2-8ef0-44da-a309-75b0ff353ddf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0f09ccb1-d263-4969-9fb7-c5cf4b38c114</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>529a9d42-a695-427e-9cc5-b531047ed645</guid>
                <versionId>4d5125bb-7ff4-473d-b17e-911b6bd78204</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a06adae-983e-44ee-8eb7-45b9a835b9fd</processItemId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2ee8ba8a-d0b9-49a1-a92f-61b8b4415e74</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-8f3</guid>
            <versionId>d9614c83-4b00-43d1-a90c-d2e72fb1ce38</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="266" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2ee8ba8a-d0b9-49a1-a92f-61b8b4415e74</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var obj = JSON.parse(tw.local.data);;&#xD;
&#xD;
obj.customerInformation.CIFNumber = "12345678";&#xD;
tw.local.results = obj;</script>
                <isRule>false</isRule>
                <guid>f11b4d6a-6980-42c5-aef8-0e535ad6f92c</guid>
                <versionId>233b558d-f1db-4042-bbb9-820306d1b3c7</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8a06adae-983e-44ee-8eb7-45b9a835b9fd</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="test json" id="1.7cd9c833-9de0-441c-987b-2cea94c2b257" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        <ns3:isSecured>false</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.b45c6a0c-a5d8-4d8f-80b7-a992d6a66772">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">&#xD;
'{"_inherited":{"p":1},"childrenCache":{"customerInformation":{"_inherited":{"p":1},"childrenCache":{},"_objectPath":"local.idc.customerInformation","_systemCallbackHandle":{},"CIFNumber":"","importCardNumber":"","commercialRegistrationNumber":"","customerName":"","isCustomeSanctionedbyCBE":"","CBENumber":"","customerSector":"","facilityType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"commercialRegistrationOffice":"","taxCardNumber":"","customerType":"","addressLine1":"","addressLine2":""}},"_objectPath":"local.idc","_systemCallbackHandle":{},"IDCRequestState":"","commodityDescription":"","countryOfOrigin":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"appInfo":{"requestDate":"","status":"","subStatus":"","initiator":"","branch":{"name":"","value":""},"requestName":"","requestType":"","stepName":"","appRef":"","appID":"","instanceID":""},"productsDetails":{"destinationPort":"","shippingDate":"2023-12-25T19:55:09.888Z","HSProduct":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"incoterms":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"ACID":"","CBECommodityClassification":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"shipmentMethod":{"id":0,"code":"","arabicdescription":"","englishdescription":""}},"financialDetails":{"isAdvancePaymentsUsed":false,"paymentTerms":{"items":[{"installmentAmount":0,"installmentDate":"2023-12-25T19:55:09.888Z"}],"listAllSelectedIndices":[],"te":"Pay"},"usedAdvancePayment":{"items":[{"AllocatedAmountinRequestCurrency":0,"invoiceCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"AmountAllocated":0,"advancePaymentRequestNumber":"","outstandingAmount":0,"invoiceNumber":"","paidAmount":0,"beneficiaryName":"","documentAmount":0,"DBID":0}],"listAllSelectedIndices":[],"te":"Use"},"discountAmt":0,"firstInstallementMaturityDate":"2023-12-25T19:55:09.888Z","facilityAmtInDocCurrency":0,"amtSight":0,"beneficiaryDetails":{"account":"","bank":"","correspondentRefNum":"","name":"","country":{"id":0,"code":"","arabicdescription":"","englishdescription":""}},"amtDeferredNoAvalized":0,"amtPayableByNBE":0,"executionHub":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"sourceOfForeignCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"facilityAmtWithNoCurrency":0,"documentCurrency":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"daysTillMaturity":0,"tradeFinanceApprovalNumber":"","amountAdvanced":0,"paymentAccount":"","tradeFOReferenceNumber":"","documentAmount":0,"sourceOfFunds":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"chargesAccount":"","CashAmtWithNoCurrency":0,"amtDeferredAvalized":0,"amtPaidbyOtherBanks":0,"cashAmtInDocCurrency":0},"IDCRequestType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"isIDCWithdrawn":false,"IDCRequestStage":"","FCContractNumber":"","billOfLading":{"items":[{"date":"2023-12-25T19:55:09.888Z","number":""}],"listAllSelectedIndices":[],"te":"Inv"},"importPurpose":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"IDCRequestNature":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"customerInformation":{"CIFNumber":"","importCardNumber":"","commercialRegistrationNumber":"","customerName":"","isCustomeSanctionedbyCBE":"","CBENumber":"","customerSector":"","facilityType":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"commercialRegistrationOffice":"","taxCardNumber":"","customerType":"","addressLine1":"","addressLine2":""},"invoices":{"items":[{"date":"2023-12-25T19:55:09.888Z","number":""}],"listAllSelectedIndices":[],"te":"Inv"},"productCategory":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"documentsSource":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"ParentIDCRequestNumber":"","paymentTerms":{"id":0,"code":"","arabicdescription":"","englishdescription":""},"approvals":{"CAD":false,"treasury":false,"compliance":false},"appLog":{"items":[{"startTime":"2023-12-25T19:55:09.888Z","endTime":"2023-12-25T19:55:09.888Z","userName":"","role":"","step":"","action":"","comment":"","terminateReason":"","returnReason":""}],"listAllSelectedIndices":[],"te":"App2"},"stepLog":{"startTime":"2023-12-25T19:55:09.888Z","endTime":"2023-12-25T19:55:09.888Z","userName":"","role":"","step":"","action":"","comment":"","terminateReason":"","returnReason":""},"DBID":0,"requestDate":"2023-12-25T19:55:09.888Z"}'</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.f5d22427-252b-460c-8989-ec126886e528" />
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5a87f3d7-44bd-48ed-8169-33debd0d8e11" />
                        <ns16:inputSet />
                        <ns16:outputSet />
                    </ns16:ioSpecification>
                    <ns16:laneSet id="89b2e281-7160-4b5d-86ba-a7fc746d8807">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="9e2269d9-cf39-4e4a-8cb7-847b4f59e1d9" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>6f38e899-0195-4843-8b08-124b2902acc7</ns16:flowNodeRef>
                            <ns16:flowNodeRef>2b98ac93-7c25-40fd-89d7-38bc8d4e05f1</ns16:flowNodeRef>
                            <ns16:flowNodeRef>8a06adae-983e-44ee-8eb7-45b9a835b9fd</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="6f38e899-0195-4843-8b08-124b2902acc7">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.2ef3cf54-6b83-4990-8823-e85af73df3e9</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="2b98ac93-7c25-40fd-89d7-38bc8d4e05f1">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:1b737c4ee9445496:-128a99a6:18c88c30de4:-8fb</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>801f05cd-4bc0-40c7-83de-ab305bc00a87</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="6f38e899-0195-4843-8b08-124b2902acc7" targetRef="8a06adae-983e-44ee-8eb7-45b9a835b9fd" name="To Script Task" id="2027.2ef3cf54-6b83-4990-8823-e85af73df3e9">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="8a06adae-983e-44ee-8eb7-45b9a835b9fd">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="266" y="57" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.2ef3cf54-6b83-4990-8823-e85af73df3e9</ns16:incoming>
                        <ns16:outgoing>801f05cd-4bc0-40c7-83de-ab305bc00a87</ns16:outgoing>
                        <ns16:script>var obj = JSON.parse(tw.local.data);;&#xD;
&#xD;
obj.customerInformation.CIFNumber = "12345678";&#xD;
tw.local.results = obj;</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="8a06adae-983e-44ee-8eb7-45b9a835b9fd" targetRef="2b98ac93-7c25-40fd-89d7-38bc8d4e05f1" name="To End" id="801f05cd-4bc0-40c7-83de-ab305bc00a87">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" name="idc" id="2056.174575a4-befe-47b0-8913-34a1beffa7f2">
                        <ns16:extensionElements>
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new TWDate();
autoObject</ns3:defaultValue>
                        </ns16:extensionElements>
                    </ns16:dataObject>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.801f05cd-4bc0-40c7-83de-ab305bc00a87</processLinkId>
            <processId>1.7cd9c833-9de0-441c-987b-2cea94c2b257</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8a06adae-983e-44ee-8eb7-45b9a835b9fd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.2b98ac93-7c25-40fd-89d7-38bc8d4e05f1</toProcessItemId>
            <guid>85aadabb-685e-4500-8239-16210b976a64</guid>
            <versionId>9325669a-ce8e-4fe7-bf87-fcf7fd311ac1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8a06adae-983e-44ee-8eb7-45b9a835b9fd</fromProcessItemId>
            <toProcessItemId>2025.2b98ac93-7c25-40fd-89d7-38bc8d4e05f1</toProcessItemId>
        </link>
    </process>
</teamworks>

