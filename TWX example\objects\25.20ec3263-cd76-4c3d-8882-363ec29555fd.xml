<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.20ec3263-cd76-4c3d-8882-363ec29555fd" name="IDC Customs Release">
        <lastModified>1692798944594</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <bpdId>25.20ec3263-cd76-4c3d-8882-363ec29555fd</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="50787c03-40ec-42c7-a556-967d6d21ee1a" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="IDC Customs Release" id="25.20ec3263-cd76-4c3d-8882-363ec29555fd" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot;IDC Withdrawal:&amp;quot; + tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="true" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1684076750388161688664857760"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="decc7b11-29c9-4e8d-be34-3a2be5ec6d16" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.2dce7af7-b766-40ab-acc0-0e77449191aa" epvProcessLinkId="b8b1c8c3-058c-451f-8e6a-0766db09dfa1" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="37866dd1-51c8-4998-b138-002e56e10735" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="11345455-8d49-462e-85cd-863419e1d148" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="409b66bf-f679-4dda-b3bb-f6fb45f6c26a"&gt;&lt;ns15:lane name="Branch / Hub Maker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="27c60805-468c-45e5-83f7-a6d384e82c62" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;ca260c3f-dee2-4bbc-a22f-34ea32517e32&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;90a89a7f-e597-4e7a-b118-b3f7cc46f164&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Compliance Representative" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="0f70fc1c-cfd1-444e-9765-ef458842700f" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;b8314117-983a-4db9-866f-953592cb0ec2&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;d416e16f-954a-477d-8718-cc841088a29d&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8764e807-a3ce-415c-8a15-33d8b260e369&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Trade Front Officer" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="dc496d0e-4323-4b77-ace0-819af3fe6858" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;7047a115-ab38-4011-a1a3-ff52ca67f9da&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;21c3f85a-dfc6-410a-b792-0b7155e9c595&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;dccc9af2-297b-49e1-88b8-183ee3e4aafe&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2ee693e9-6c72-4728-b591-a3f01fcfab54" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="603" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="ca260c3f-dee2-4bbc-a22f-34ea32517e32"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;d0f315ff-a638-437c-a536-66d6fe036b76&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;d0f315ff-a638-437c-a536-66d6fe036b76&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="Canceled" id="8764e807-a3ce-415c-8a15-33d8b260e369"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="914" y="73" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;ad88eee0-7849-4982-84f1-a7f2ea27bdf7&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="ca260c3f-dee2-4bbc-a22f-34ea32517e32" targetRef="90a89a7f-e597-4e7a-b118-b3f7cc46f164" name="To Create IDC Customs Release Request" id="d0f315ff-a638-437c-a536-66d6fe036b76"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25" default="c2912ca4-2437-44ff-a028-fa9530cd768f" name="Create IDC Customs Release Request" id="90a89a7f-e597-4e7a-b118-b3f7cc46f164"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="145" y="111" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create IDC Customs Release Request – تسجيل افراج جمركى &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;d0f315ff-a638-437c-a536-66d6fe036b76&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;cd449068-71e6-40cd-a502-8ff3383c21b0&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;f35859b5-6cd9-498b-95be-85c900589ef9&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c2912ca4-2437-44ff-a028-fa9530cd768f&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.787cd339-1200-4284-9c39-59ac7434b298&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.aaa930c1-4bcd-4305-865c-9ac6b03399d1&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.16931d1e-3b6a-4725-9805-a04464e7991e" default="c9afd2dd-d628-48b6-add1-5d6d702c6d6f" name="Review IDC Customs Release Request" id="b8314117-983a-4db9-866f-953592cb0ec2"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="418" y="48" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review IDC Customs Release Request – مراجعة تسجيل افراج جمركى &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c2912ca4-2437-44ff-a028-fa9530cd768f&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c9afd2dd-d628-48b6-add1-5d6d702c6d6f&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b135931d-fe00-4119-b413-588aeaa095fd&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.a81c735f-9f6d-40f5-b594-65d5846e9b37&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:exclusiveGateway default="cd449068-71e6-40cd-a502-8ff3383c21b0" name="Is Canceled" id="d416e16f-954a-477d-8718-cc841088a29d"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="715" y="68" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c9afd2dd-d628-48b6-add1-5d6d702c6d6f&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;cd449068-71e6-40cd-a502-8ff3383c21b0&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;ad88eee0-7849-4982-84f1-a7f2ea27bdf7&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;260ffe10-afde-4084-afc1-b7993106a9b8&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="90a89a7f-e597-4e7a-b118-b3f7cc46f164" targetRef="b8314117-983a-4db9-866f-953592cb0ec2" name="To Review IDC Customs Release Request" id="c2912ca4-2437-44ff-a028-fa9530cd768f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="b8314117-983a-4db9-866f-953592cb0ec2" targetRef="d416e16f-954a-477d-8718-cc841088a29d" name="To Is Canceled" id="c9afd2dd-d628-48b6-add1-5d6d702c6d6f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="d416e16f-954a-477d-8718-cc841088a29d" targetRef="90a89a7f-e597-4e7a-b118-b3f7cc46f164" name="To Create IDC Customs Release Request" id="cd449068-71e6-40cd-a502-8ff3383c21b0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="d416e16f-954a-477d-8718-cc841088a29d" targetRef="8764e807-a3ce-415c-8a15-33d8b260e369" name="To Canceled" id="ad88eee0-7849-4982-84f1-a7f2ea27bdf7"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.canceled&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.0903f902-3a18-4b6d-9e59-1d4ad6437937" default="82885782-e561-4192-938b-144f24b54cf9" name="Review IDC Customs Release Request by Trade FO" id="7047a115-ab38-4011-a1a3-ff52ca67f9da"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="702" y="51" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review IDC Customs Release Request by Trade FO &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;260ffe10-afde-4084-afc1-b7993106a9b8&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;82885782-e561-4192-938b-144f24b54cf9&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.7a419aa3-7d1e-4ebb-9255-d2066c364839&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.973ec08b-973c-4de9-9768-8667831668cf&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1"&gt;tw.local.idcCustomsReleaseRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="d416e16f-954a-477d-8718-cc841088a29d" targetRef="7047a115-ab38-4011-a1a3-ff52ca67f9da" name="To Review IDC Customs Release Request by Trade FO" id="260ffe10-afde-4084-afc1-b7993106a9b8"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.awaitingTradeFoApproval&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="f35859b5-6cd9-498b-95be-85c900589ef9" name="Is Approved" id="21c3f85a-dfc6-410a-b792-0b7155e9c595"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="952" y="71" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;82885782-e561-4192-938b-144f24b54cf9&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;f35859b5-6cd9-498b-95be-85c900589ef9&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;14ce93d4-59e5-416c-b139-f0409c8d9803&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="7047a115-ab38-4011-a1a3-ff52ca67f9da" targetRef="21c3f85a-dfc6-410a-b792-0b7155e9c595" name="To Is Approved" id="82885782-e561-4192-938b-144f24b54cf9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="21c3f85a-dfc6-410a-b792-0b7155e9c595" targetRef="90a89a7f-e597-4e7a-b118-b3f7cc46f164" name="To Create IDC Customs Release Request" id="f35859b5-6cd9-498b-95be-85c900589ef9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:customBendPoint x="607" y="554" /&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="Approved" id="dccc9af2-297b-49e1-88b8-183ee3e4aafe"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1069" y="71" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;14ce93d4-59e5-416c-b139-f0409c8d9803&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="21c3f85a-dfc6-410a-b792-0b7155e9c595" targetRef="dccc9af2-297b-49e1-88b8-183ee3e4aafe" name="To Approved" id="14ce93d4-59e5-416c-b139-f0409c8d9803"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.completed&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1" isCollection="false" name="idcCustomsReleaseRequest" id="677107b6-9ffa-4ad9-8253-17c3d83a8a7c" /&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef" /&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef" /&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="IDC WithdrawalInterface" id="fa3808a7-bf63-4d4f-a941-dfde5971223c" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["d0f315ff-a638-437c-a536-66d6fe036b76"],"isInterrupting":true,"extensionElements":{"default":["d0f315ff-a638-437c-a536-66d6fe036b76"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ca260c3f-dee2-4bbc-a22f-34ea32517e32"},{"incoming":["ad88eee0-7849-4982-84f1-a7f2ea27bdf7"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":914,"y":73,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Canceled","declaredType":"endEvent","id":"8764e807-a3ce-415c-8a15-33d8b260e369"},{"targetRef":"90a89a7f-e597-4e7a-b118-b3f7cc46f164","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Create IDC Customs Release Request","declaredType":"sequenceFlow","id":"d0f315ff-a638-437c-a536-66d6fe036b76","sourceRef":"ca260c3f-dee2-4bbc-a22f-34ea32517e32"},{"outgoing":["c2912ca4-2437-44ff-a028-fa9530cd768f"],"incoming":["d0f315ff-a638-437c-a536-66d6fe036b76","cd449068-71e6-40cd-a502-8ff3383c21b0","f35859b5-6cd9-498b-95be-85c900589ef9"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":145,"y":111,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Create IDC Customs Release Request \u2013 \u062a\u0633\u062c\u064a\u0644 \u0627\u0641\u0631\u0627\u062c \u062c\u0645\u0631\u0643\u0649 ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"c2912ca4-2437-44ff-a028-fa9530cd768f","name":"Create IDC Customs Release Request","dataInputAssociation":[{"targetRef":"2055.787cd339-1200-4284-9c39-59ac7434b298","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"90a89a7f-e597-4e7a-b118-b3f7cc46f164","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}],"sourceRef":["2055.aaa930c1-4bcd-4305-865c-9ac6b03399d1"]}],"calledElement":"1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25"},{"outgoing":["c9afd2dd-d628-48b6-add1-5d6d702c6d6f"],"incoming":["c2912ca4-2437-44ff-a028-fa9530cd768f"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":418,"y":48,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review IDC Customs Release Request \u2013 \u0645\u0631\u0627\u062c\u0639\u0629 \u062a\u0633\u062c\u064a\u0644 \u0627\u0641\u0631\u0627\u062c \u062c\u0645\u0631\u0643\u0649 ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"c9afd2dd-d628-48b6-add1-5d6d702c6d6f","name":"Review IDC Customs Release Request","dataInputAssociation":[{"targetRef":"2055.b135931d-fe00-4119-b413-588aeaa095fd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"b8314117-983a-4db9-866f-953592cb0ec2","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}],"sourceRef":["2055.a81c735f-9f6d-40f5-b594-65d5846e9b37"]}],"calledElement":"1.16931d1e-3b6a-4725-9805-a04464e7991e"},{"outgoing":["cd449068-71e6-40cd-a502-8ff3383c21b0","ad88eee0-7849-4982-84f1-a7f2ea27bdf7","260ffe10-afde-4084-afc1-b7993106a9b8"],"incoming":["c9afd2dd-d628-48b6-add1-5d6d702c6d6f"],"default":"cd449068-71e6-40cd-a502-8ff3383c21b0","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":715,"y":68,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Is Canceled","declaredType":"exclusiveGateway","id":"d416e16f-954a-477d-8718-cc841088a29d"},{"targetRef":"b8314117-983a-4db9-866f-953592cb0ec2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Review IDC Customs Release Request","declaredType":"sequenceFlow","id":"c2912ca4-2437-44ff-a028-fa9530cd768f","sourceRef":"90a89a7f-e597-4e7a-b118-b3f7cc46f164"},{"targetRef":"d416e16f-954a-477d-8718-cc841088a29d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Is Canceled","declaredType":"sequenceFlow","id":"c9afd2dd-d628-48b6-add1-5d6d702c6d6f","sourceRef":"b8314117-983a-4db9-866f-953592cb0ec2"},{"targetRef":"90a89a7f-e597-4e7a-b118-b3f7cc46f164","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To Create IDC Customs Release Request","declaredType":"sequenceFlow","id":"cd449068-71e6-40cd-a502-8ff3383c21b0","sourceRef":"d416e16f-954a-477d-8718-cc841088a29d"},{"targetRef":"8764e807-a3ce-415c-8a15-33d8b260e369","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest.appInfo.subStatus\t  ==\t  tw.epv.IDCCustomsReleaseSuStatus.canceled"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To Canceled","declaredType":"sequenceFlow","id":"ad88eee0-7849-4982-84f1-a7f2ea27bdf7","sourceRef":"d416e16f-954a-477d-8718-cc841088a29d"},{"outgoing":["82885782-e561-4192-938b-144f24b54cf9"],"incoming":["260ffe10-afde-4084-afc1-b7993106a9b8"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":702,"y":51,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review IDC Customs Release Request by Trade FO ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"82885782-e561-4192-938b-144f24b54cf9","name":"Review IDC Customs Release Request by Trade FO","dataInputAssociation":[{"targetRef":"2055.7a419aa3-7d1e-4ebb-9255-d2066c364839","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"7047a115-ab38-4011-a1a3-ff52ca67f9da","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest"]}}],"sourceRef":["2055.973ec08b-973c-4de9-9768-8667831668cf"]}],"calledElement":"1.0903f902-3a18-4b6d-9e59-1d4ad6437937"},{"targetRef":"7047a115-ab38-4011-a1a3-ff52ca67f9da","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest.appInfo.subStatus\t  ==\t  tw.epv.IDCCustomsReleaseSuStatus.awaitingTradeFoApproval"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To Review IDC Customs Release Request by Trade FO","declaredType":"sequenceFlow","id":"260ffe10-afde-4084-afc1-b7993106a9b8","sourceRef":"d416e16f-954a-477d-8718-cc841088a29d"},{"outgoing":["f35859b5-6cd9-498b-95be-85c900589ef9","14ce93d4-59e5-416c-b139-f0409c8d9803"],"incoming":["82885782-e561-4192-938b-144f24b54cf9"],"default":"f35859b5-6cd9-498b-95be-85c900589ef9","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":952,"y":71,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Is Approved","declaredType":"exclusiveGateway","id":"21c3f85a-dfc6-410a-b792-0b7155e9c595"},{"targetRef":"21c3f85a-dfc6-410a-b792-0b7155e9c595","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Is Approved","declaredType":"sequenceFlow","id":"82885782-e561-4192-938b-144f24b54cf9","sourceRef":"7047a115-ab38-4011-a1a3-ff52ca67f9da"},{"targetRef":"90a89a7f-e597-4e7a-b118-b3f7cc46f164","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true,"customBendPoint":[{"x":607,"y":554}]}],"happySequence":[true]},"name":"To Create IDC Customs Release Request","declaredType":"sequenceFlow","id":"f35859b5-6cd9-498b-95be-85c900589ef9","sourceRef":"21c3f85a-dfc6-410a-b792-0b7155e9c595"},{"incoming":["14ce93d4-59e5-416c-b139-f0409c8d9803"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1069,"y":71,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Approved","declaredType":"endEvent","id":"dccc9af2-297b-49e1-88b8-183ee3e4aafe"},{"targetRef":"dccc9af2-297b-49e1-88b8-183ee3e4aafe","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.idcCustomsReleaseRequest.appInfo.subStatus\t  ==\t  tw.epv.IDCCustomsReleaseSuStatus.completed"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To Approved","declaredType":"sequenceFlow","id":"14ce93d4-59e5-416c-b139-f0409c8d9803","sourceRef":"21c3f85a-dfc6-410a-b792-0b7155e9c595"},{"itemSubjectRef":"itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1","name":"idcCustomsReleaseRequest","isCollection":false,"declaredType":"dataObject","id":"677107b6-9ffa-4ad9-8253-17c3d83a8a7c"}],"laneSet":[{"id":"409b66bf-f679-4dda-b3bb-f6fb45f6c26a","lane":[{"flowNodeRef":["ca260c3f-dee2-4bbc-a22f-34ea32517e32","90a89a7f-e597-4e7a-b118-b3f7cc46f164"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Branch \/ Hub Maker","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"27c60805-468c-45e5-83f7-a6d384e82c62","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["b8314117-983a-4db9-866f-953592cb0ec2","d416e16f-954a-477d-8718-cc841088a29d","8764e807-a3ce-415c-8a15-33d8b260e369"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Compliance Representative","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"0f70fc1c-cfd1-444e-9765-ef458842700f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["7047a115-ab38-4011-a1a3-ff52ca67f9da","21c3f85a-dfc6-410a-b792-0b7155e9c595","dccc9af2-297b-49e1-88b8-183ee3e4aafe"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Trade Front Officer","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"dc496d0e-4323-4b77-ace0-819af3fe6858","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":603,"declaredType":"TNodeVisualInfo","height":200}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"2ee693e9-6c72-4728-b591-a3f01fcfab54","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":true,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"instanceName":"\"IDC Withdrawal:\" + tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1684076750388161688664857760","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":true,"atRiskCalcEnabled":true,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"decc7b11-29c9-4e8d-be34-3a2be5ec6d16","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"IDC Customs Release","declaredType":"process","id":"25.20ec3263-cd76-4c3d-8882-363ec29555fd","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.2dce7af7-b766-40ab-acc0-0e77449191aa","epvProcessLinkId":"b8b1c8c3-058c-451f-8e6a-0766db09dfa1","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{},{"id":"37866dd1-51c8-4998-b138-002e56e10735"}],"outputSet":[{},{"id":"11345455-8d49-462e-85cd-863419e1d148"}]}},{"name":"IDC WithdrawalInterface","declaredType":"interface","id":"fa3808a7-bf63-4d4f-a941-dfde5971223c"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"50787c03-40ec-42c7-a556-967d6d21ee1a"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-662f</guid>
        <versionId>05b2e5f1-ff1b-4750-b084-e72f5bfe325d</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:ee21eb31e0163954:-121b3b42:18a036b92b9:1648">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>IDC Customs Release</name>
            <documentation></documentation>
            <name>IDC Customs Release</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>eslam</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1688664857767</creationDate>
            <modificationDate>1692800045947</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"IDC Withdrawal:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="409b66bf-f679-4dda-b3bb-f6fb45f6c26a" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fad" />
            <ownerTeamInstanceUI id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fae" />
            <simulationScenario id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6084">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1688664861619</startTime>
            </simulationScenario>
            <flow id="c9afd2dd-d628-48b6-add1-5d6d702c6d6f" connectionType="SequenceFlow">
                <name>To Is Canceled</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f86" />
                </connection>
            </flow>
            <flow id="cd449068-71e6-40cd-a502-8ff3383c21b0" connectionType="SequenceFlow">
                <name>To Create IDC Customs Release Request</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f85" />
                </connection>
            </flow>
            <flow id="260ffe10-afde-4084-afc1-b7993106a9b8" connectionType="SequenceFlow">
                <name>To Review IDC Customs Release Request by Trade FO</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8f">
                        <expression>tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.awaitingTradeFoApproval</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="c2912ca4-2437-44ff-a028-fa9530cd768f" connectionType="SequenceFlow">
                <name>To Review IDC Customs Release Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f84" />
                </connection>
            </flow>
            <flow id="f35859b5-6cd9-498b-95be-85c900589ef9" connectionType="SequenceFlow">
                <name>To Create IDC Customs Release Request</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f83" />
                </connection>
            </flow>
            <flow id="d0f315ff-a638-437c-a536-66d6fe036b76" connectionType="SequenceFlow">
                <name>To Create IDC Customs Release Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f82" />
                </connection>
            </flow>
            <flow id="82885782-e561-4192-938b-144f24b54cf9" connectionType="SequenceFlow">
                <name>To Is Approved</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f81" />
                </connection>
            </flow>
            <flow id="14ce93d4-59e5-416c-b139-f0409c8d9803" connectionType="SequenceFlow">
                <name>To Approved</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f88">
                        <expression>tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.completed</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="ad88eee0-7849-4982-84f1-a7f2ea27bdf7" connectionType="SequenceFlow">
                <name>To Canceled</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f91">
                        <expression>tw.local.idcCustomsReleaseRequest.appInfo.subStatus	  ==	  tw.epv.IDCCustomsReleaseSuStatus.canceled</expression>
                    </condition>
                </connection>
            </flow>
            <pool id="409b66bf-f679-4dda-b3bb-f6fb45f6c26a">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at1684076750388161688664857760</restrictedName>
                <dimension>
                    <size w="3000" h="600" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="27c60805-468c-45e5-83f7-a6d384e82c62">
                    <name>Branch / Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="90a89a7f-e597-4e7a-b118-b3f7cc46f164" componentType="Activity">
                        <name>Create IDC Customs Release Request</name>
                        <position>
                            <location x="145" y="111" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create IDC Customs Release Request – تسجيل افراج جمركى </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3faa">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.787cd339-1200-4284-9c39-59ac7434b298</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa9">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.aaa930c1-4bcd-4305-865c-9ac6b03399d1</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa8">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa7">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9b">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="d0f315ff-a638-437c-a536-66d6fe036b76" />
                        </inputPort>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f93">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="cd449068-71e6-40cd-a502-8ff3383c21b0" />
                        </inputPort>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8a">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="f35859b5-6cd9-498b-95be-85c900589ef9" />
                        </inputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9a">
                            <positionId>rightCenter</positionId>
                            <flow ref="c2912ca4-2437-44ff-a028-fa9530cd768f" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="ca260c3f-dee2-4bbc-a22f-34ea32517e32" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9c">
                            <positionId>rightCenter</positionId>
                            <flow ref="d0f315ff-a638-437c-a536-66d6fe036b76" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="0f70fc1c-cfd1-444e-9765-ef458842700f">
                    <name>Compliance Representative</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="b8314117-983a-4db9-866f-953592cb0ec2" componentType="Activity">
                        <name>Review IDC Customs Release Request</name>
                        <position>
                            <location x="418" y="48" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.16931d1e-3b6a-4725-9805-a04464e7991e</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC Customs Release Request – مراجعة تسجيل افراج جمركى </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa5">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.b135931d-fe00-4119-b413-588aeaa095fd</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa4">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.a81c735f-9f6d-40f5-b594-65d5846e9b37</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa3">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa2">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f99">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="c2912ca4-2437-44ff-a028-fa9530cd768f" />
                        </inputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f98">
                            <positionId>rightCenter</positionId>
                            <flow ref="c9afd2dd-d628-48b6-add1-5d6d702c6d6f" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="8764e807-a3ce-415c-8a15-33d8b260e369" componentType="Event">
                        <name>Canceled</name>
                        <documentation></documentation>
                        <position>
                            <location x="914" y="73" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f92">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="ad88eee0-7849-4982-84f1-a7f2ea27bdf7" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="d416e16f-954a-477d-8718-cc841088a29d" componentType="Gateway">
                        <name>Is Canceled</name>
                        <documentation></documentation>
                        <position>
                            <location x="715" y="68" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f97">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="c9afd2dd-d628-48b6-add1-5d6d702c6d6f" />
                        </inputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f95">
                            <positionId>rightCenter</positionId>
                            <flow ref="ad88eee0-7849-4982-84f1-a7f2ea27bdf7" />
                        </outputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f94">
                            <positionId>bottomCenter</positionId>
                            <flow ref="260ffe10-afde-4084-afc1-b7993106a9b8" />
                        </outputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f96">
                            <positionId>topCenter</positionId>
                            <flow ref="cd449068-71e6-40cd-a502-8ff3383c21b0" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="dc496d0e-4323-4b77-ace0-819af3fe6858">
                    <name>Trade Front Officer</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="7047a115-ab38-4011-a1a3-ff52ca67f9da" componentType="Activity">
                        <name>Review IDC Customs Release Request by Trade FO</name>
                        <position>
                            <location x="702" y="51" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.0903f902-3a18-4b6d-9e59-1d4ad6437937</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC Customs Release Request by Trade FO </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fa0">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.7a419aa3-7d1e-4ebb-9255-d2066c364839</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9f">
                                    <name>idcCustomsReleaseRequest</name>
                                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                                    <input>true</input>
                                    <value>tw.local.idcCustomsReleaseRequest</value>
                                    <parameterId>2055.973ec08b-973c-4de9-9768-8667831668cf</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9e">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f9d">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f90">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="260ffe10-afde-4084-afc1-b7993106a9b8" />
                        </inputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8e">
                            <positionId>rightCenter</positionId>
                            <flow ref="82885782-e561-4192-938b-144f24b54cf9" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="dccc9af2-297b-49e1-88b8-183ee3e4aafe" componentType="Event">
                        <name>Approved</name>
                        <documentation></documentation>
                        <position>
                            <location x="1069" y="71" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f89">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="14ce93d4-59e5-416c-b139-f0409c8d9803" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="21c3f85a-dfc6-410a-b792-0b7155e9c595" componentType="Gateway">
                        <name>Is Approved</name>
                        <documentation></documentation>
                        <position>
                            <location x="952" y="71" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8d">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="82885782-e561-4192-938b-144f24b54cf9" />
                        </inputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8b">
                            <positionId>rightCenter</positionId>
                            <flow ref="14ce93d4-59e5-416c-b139-f0409c8d9803" />
                        </outputPort>
                        <outputPort id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f8c">
                            <positionId>bottomCenter</positionId>
                            <flow ref="f35859b5-6cd9-498b-95be-85c900589ef9" />
                        </outputPort>
                    </flowObject>
                </lane>
                <privateVariable id="677107b6-9ffa-4ad9-8253-17c3d83a8a7c">
                    <name>idcCustomsReleaseRequest</name>
                    <description></description>
                    <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3fac">
                    <epvId>/21.2dce7af7-b766-40ab-acc0-0e77449191aa</epvId>
                </epv>
            </pool>
            <extension id="bpdid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-3f87" type="CASE">
                <caseFolder id="decc7b11-29c9-4e8d-be34-3a2be5ec6d16">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

