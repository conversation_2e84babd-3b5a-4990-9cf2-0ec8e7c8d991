<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5" name="FC_Get Account Number">
        <lastModified>*************</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.27039d40-db8b-4f7e-9f4b-eda4e20ff658</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>028763b4-a7d0-493d-8bed-f0671bca2d40</guid>
        <versionId>1396a188-ab01-4a12-8965-b83193544c74</versionId>
        <dependencySummary>&lt;dependencySummary id="459b8d64-1740-48ec-a7a5-7097263cfb2e" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d43ae376-67a9-416e-92bf-e1166c343d58</processParameterId>
            <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>70aab08d-d411-48aa-ad08-0c0fa7ab465d</guid>
            <versionId>fe18ceaa-9229-4f28-962c-22eb0bf86174</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6c75b4a0-1542-48b4-9fa7-e796859e903f</processParameterId>
            <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b3fc0e0d-3ac3-46e4-bba2-346e31eab6c6</guid>
            <versionId>0cb127e6-e914-4c8b-94fe-96d50141382e</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a</processItemId>
            <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.b4be1dd4-8ce1-4a41-a43c-8647b43a9d4f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae8</guid>
            <versionId>704775c5-94e1-47f5-aee9-ed47f2c44cd3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.b4be1dd4-8ce1-4a41-a43c-8647b43a9d4f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>db9e5655-d52c-42cb-93e1-b7b973ed010e</guid>
                <versionId>758e370a-6bbe-4c86-8082-c6a4bdd33425</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.27039d40-db8b-4f7e-9f4b-eda4e20ff658</processItemId>
            <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b170e1fc-5c75-420b-ac9f-cc45d2f388bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae9</guid>
            <versionId>e1758636-bf84-4ea3-93ac-28780b5bf844</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="272" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b170e1fc-5c75-420b-ac9f-cc45d2f388bb</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
tw.local.results[0] = new tw.object.NameValuePair();&#xD;
tw.local.results[0].name = "1";&#xD;
tw.local.results[0].value = "1";&#xD;
&#xD;
tw.local.results[1].name = "2";&#xD;
tw.local.results[1].value = "2";&#xD;
&#xD;
tw.local.results[2].name = "3";&#xD;
tw.local.results[2].value = "3";</script>
                <isRule>false</isRule>
                <guid>e5bfd59e-eb07-4261-8738-2f23187e331c</guid>
                <versionId>ff626009-e7c2-41da-86cc-3c8da4924ced</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.27039d40-db8b-4f7e-9f4b-eda4e20ff658</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="FC_Get Account Number" id="1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d43ae376-67a9-416e-92bf-e1166c343d58" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.6c75b4a0-1542-48b4-9fa7-e796859e903f" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c20cf25a-6651-474d-aa68-1831c0c89b7e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="6a73056f-d66e-4dff-9cdc-27a4db54025e" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1f773a37-6dc4-4b4d-8025-723539d032aa</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>27039d40-db8b-4f7e-9f4b-eda4e20ff658</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1f773a37-6dc4-4b4d-8025-723539d032aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.63c4c0d5-069e-4679-b419-f3daa2a30f4e</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3d4ef6b1-8f9e-4882-abfd-5bbe3301beac</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1f773a37-6dc4-4b4d-8025-723539d032aa" targetRef="27039d40-db8b-4f7e-9f4b-eda4e20ff658" name="To Script Task" id="2027.63c4c0d5-069e-4679-b419-f3daa2a30f4e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="27039d40-db8b-4f7e-9f4b-eda4e20ff658">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="272" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.63c4c0d5-069e-4679-b419-f3daa2a30f4e</ns16:incoming>
                        
                        
                        <ns16:outgoing>3d4ef6b1-8f9e-4882-abfd-5bbe3301beac</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
tw.local.results[0] = new tw.object.NameValuePair();&#xD;
tw.local.results[0].name = "1";&#xD;
tw.local.results[0].value = "1";&#xD;
&#xD;
tw.local.results[1].name = "2";&#xD;
tw.local.results[1].value = "2";&#xD;
&#xD;
tw.local.results[2].name = "3";&#xD;
tw.local.results[2].value = "3";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="27039d40-db8b-4f7e-9f4b-eda4e20ff658" targetRef="88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a" name="To End" id="3d4ef6b1-8f9e-4882-abfd-5bbe3301beac">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3ebd680b-e1c8-4d46-9b63-704409d53f07</processLinkId>
            <processId>1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.27039d40-db8b-4f7e-9f4b-eda4e20ff658</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a</toProcessItemId>
            <guid>b0280fee-66ee-4960-bdca-385fb859764c</guid>
            <versionId>fa09a220-07ec-4db0-b7d0-b20ff332fdb2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.27039d40-db8b-4f7e-9f4b-eda4e20ff658</fromProcessItemId>
            <toProcessItemId>2025.88f0d4e3-e09e-4c4a-83c1-9c388fa5a70a</toProcessItemId>
        </link>
    </process>
</teamworks>

