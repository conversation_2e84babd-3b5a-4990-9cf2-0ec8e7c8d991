<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.0d77a33d-fe75-4164-87ca-b9d0a0b91640" name="Get Interest Amount">
        <lastModified>1692506911928</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.45fa282f-02ae-4924-867a-8822d8e91318</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:8a32e7e0f533ea09:-114388d5:18944d56ab0:11c1</guid>
        <versionId>e1b8f376-4c18-4c89-9511-78d482f982ae</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb9" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.6ffb10bb-d153-4261-8d8e-65463ec599e4"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":33,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"6c487b13-268b-4dea-8d88-c0eea3874771"},{"incoming":["ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff","0dba1b3c-a597-4c6f-865f-77fc56a3e74a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":649,"y":36,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:8a32e7e0f533ea09:-114388d5:18944d56ab0:11c3"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"e027d8fc-84d2-4b8e-8c1c-1db18e788d6d"},{"targetRef":"45fa282f-02ae-4924-867a-8822d8e91318","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calc Interest Amount","declaredType":"sequenceFlow","id":"2027.6ffb10bb-d153-4261-8d8e-65463ec599e4","sourceRef":"6c487b13-268b-4dea-8d88-c0eea3874771"},{"startQuantity":1,"outgoing":["5ddd0512-0a0a-4f99-8463-b7e7970f9c72"],"incoming":["2027.6ffb10bb-d153-4261-8d8e-65463ec599e4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"45fa282f-02ae-4924-867a-8822d8e91318","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tvar splittedArray = tw.local.data.split(\",\");\r\n\ttw.local.interestRate = splittedArray[0];\r\n\ttw.local.contractAmount = splittedArray[1];\r\n\ttw.local.numberOfDays = splittedArray[2];\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"startQuantity":1,"outgoing":["3a2c6946-ea1a-48d3-891b-ae52c88233b7"],"incoming":["5ddd0512-0a0a-4f99-8463-b7e7970f9c72"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":10,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calc Interest Amount","dataInputAssociation":[{"targetRef":"2055.c5fd3217-96b2-49f2-836c-21caa3faa604","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.prefix"]}}]},{"targetRef":"2055.67e53230-32b5-4c78-a136-6ca623062f09","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.userID"]}}]},{"targetRef":"2055.46177c25-f032-405c-8698-f3ea556a80cd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceID"]}}]},{"targetRef":"2055.7a10f7fc-eb93-4a88-9d3f-e4a90e49ba04","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.processName"]}}]},{"targetRef":"2055.9fc52508-794a-4e64-a550-16939f77ace4","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.snapshot"]}}]},{"targetRef":"2055.69b75103-5816-4269-b337-b5e6054eb512","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestAppID"]}}]},{"targetRef":"2055.43634d2e-7060-46c9-856e-3a70c18a5843","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.debugMode"]}}]},{"targetRef":"2055.85368963-39d7-4ffe-8cad-6cc051bd0369","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.contractAmount"]}}]},{"targetRef":"2055.a4dc5a59-5755-4fe2-8c71-d4bf6d6d732c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.interestRate"]}}]},{"targetRef":"2055.5b2a0a73-a2ed-4917-87c4-dfbc7c4f7df2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.numberOfDays"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2a376fa0-e8c5-4c0e-891e-2c373a3e5e17","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.5ddb54c4-2023-42e8-8b81-8c4c86645dfb"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.a4cdaf76-a9f3-49d9-ba5f-42881ee68472"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.688aefc6-3ead-458e-95cf-d6e0fd1df5b1"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.08a77e96-1d61-43ad-a94d-3b5999425177"]}],"calledElement":"1.e8fecec0-27a7-428c-8a39-56e91a3fe975"},{"targetRef":"f0bcc080-11b9-4399-824c-df260328a0c1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d694a63221635d5b:6baf87c4:18969a9a6e2:-6a84"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"3a2c6946-ea1a-48d3-891b-ae52c88233b7","sourceRef":"2a376fa0-e8c5-4c0e-891e-2c373a3e5e17"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractAmount","isCollection":false,"declaredType":"dataObject","id":"2056.505a272e-683f-4a87-82e7-603540ab1814"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"debugMode","isCollection":false,"declaredType":"dataObject","id":"2056.f99615a1-d7f7-4251-82ab-a9f9f97a31f8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.c5cdd5f9-8dea-4923-88b8-0f01b2d263ef"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"interestRate","isCollection":false,"declaredType":"dataObject","id":"2056.f02805d9-30e5-437b-86ce-394cf316414a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"numberOfDays","isCollection":false,"declaredType":"dataObject","id":"2056.821b82e5-9ca9-40cb-8206-703904b63534"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.63ab96df-fe0c-4f2c-8a9a-a3eeef6994f7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.15fe5ace-bca2-438b-88d1-0643d8569bd8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.ceb94201-31b0-4f89-8a87-896b6f9cba29"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.ad111e2e-f7f0-431f-8644-c0b9eb520835"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.4b5fe476-4278-48aa-8cf9-362b152fb697"},{"targetRef":"2a376fa0-e8c5-4c0e-891e-2c373a3e5e17","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calc Interest Amount","declaredType":"sequenceFlow","id":"5ddd0512-0a0a-4f99-8463-b7e7970f9c72","sourceRef":"45fa282f-02ae-4924-867a-8822d8e91318"},{"parallelMultiple":false,"outgoing":["df6bb3ce-735e-497f-8c4a-c88a23a14678"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"30d7c603-e54b-406d-864d-c5c77822e556"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8382f693-4616-4949-86b0-5a464d7a73db","otherAttributes":{"eventImplId":"2d1a9f0d-ac0a-4798-8521-35487ae1f5e7"}}],"attachedToRef":"45fa282f-02ae-4924-867a-8822d8e91318","extensionElements":{"nodeVisualInfo":[{"width":24,"x":238,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"e4d06b76-3a1a-4748-8214-8219e3405e16","outputSet":{}},{"parallelMultiple":false,"outgoing":["a6f768f2-494e-4d98-8994-104c98b14e30"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5ccfeaa3-9270-4bc9-88ec-aacc178dd45a"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"06bd0411-f564-4105-8f53-e6b9325d08d0","otherAttributes":{"eventImplId":"31f8e159-3878-4277-8443-b43b27917a17"}}],"attachedToRef":"2a376fa0-e8c5-4c0e-891e-2c373a3e5e17","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"33168201-e363-4bef-8769-09ee2a1463dc","outputSet":{}},{"targetRef":"738c9c61-60a0-4dcc-8ffa-f0f92b7b8448","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"df6bb3ce-735e-497f-8c4a-c88a23a14678","sourceRef":"e4d06b76-3a1a-4748-8214-8219e3405e16"},{"targetRef":"738c9c61-60a0-4dcc-8ffa-f0f92b7b8448","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a6f768f2-494e-4d98-8994-104c98b14e30","sourceRef":"33168201-e363-4bef-8769-09ee2a1463dc"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.0b8c3529-8523-44fa-81cf-ed3e74f2a3c0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.1da269fb-9a38-4a7c-8cac-d2766b8b5a9a"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.6d814a74-8a5f-4579-8b61-ceb904b7846b"},{"outgoing":["ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff","f07d3535-e75b-4e04-8ed4-4b533632a2df"],"incoming":["3a2c6946-ea1a-48d3-891b-ae52c88233b7"],"default":"ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":517,"y":32,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"f0bcc080-11b9-4399-824c-df260328a0c1"},{"targetRef":"e027d8fc-84d2-4b8e-8c1c-1db18e788d6d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff","sourceRef":"f0bcc080-11b9-4399-824c-df260328a0c1"},{"targetRef":"738c9c61-60a0-4dcc-8ffa-f0f92b7b8448","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f07d3535-e75b-4e04-8ed4-4b533632a2df","sourceRef":"f0bcc080-11b9-4399-824c-df260328a0c1"},{"startQuantity":1,"outgoing":["0dba1b3c-a597-4c6f-865f-77fc56a3e74a"],"incoming":["df6bb3ce-735e-497f-8c4a-c88a23a14678","f07d3535-e75b-4e04-8ed4-4b533632a2df","a6f768f2-494e-4d98-8994-104c98b14e30"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":354,"y":210,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"738c9c61-60a0-4dcc-8ffa-f0f92b7b8448","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"e027d8fc-84d2-4b8e-8c1c-1db18e788d6d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"0dba1b3c-a597-4c6f-865f-77fc56a3e74a","sourceRef":"738c9c61-60a0-4dcc-8ffa-f0f92b7b8448"}],"laneSet":[{"id":"673799e8-72f2-4896-8cc6-f3921820c56c","lane":[{"flowNodeRef":["6c487b13-268b-4dea-8d88-c0eea3874771","e027d8fc-84d2-4b8e-8c1c-1db18e788d6d","45fa282f-02ae-4924-867a-8822d8e91318","2a376fa0-e8c5-4c0e-891e-2c373a3e5e17","e4d06b76-3a1a-4748-8214-8219e3405e16","33168201-e363-4bef-8769-09ee2a1463dc","f0bcc080-11b9-4399-824c-df260328a0c1","738c9c61-60a0-4dcc-8ffa-f0f92b7b8448"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":325}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"1de82fb2-7966-40f9-8a6c-027dbfe56139","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Interest Amount","declaredType":"process","id":"1.0d77a33d-fe75-4164-87ca-b9d0a0b91640","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.231395e2-9065-4f46-8ac0-df1371b3c9b6"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.0d7c1a8c-31f0-4c9a-8131-cc9b8c697399"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"4,3000,15.9\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"textFormat":"text\/plain"}],"name":"data","isCollection":false,"id":"2055.718220fc-cc4b-4b13-8268-1cff10654a3c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.718220fc-cc4b-4b13-8268-1cff10654a3c</processParameterId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"4,3000,15.9"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1c24ba0c-8988-409b-8680-85f7ce213e0a</guid>
            <versionId>6d94fae2-733c-486d-8c4e-5688fca4ac4e</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.231395e2-9065-4f46-8ac0-df1371b3c9b6</processParameterId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dcdbccbd-b67d-42fa-873a-26bd1ca2957c</guid>
            <versionId>966653bf-b53b-4e67-a83b-3c29593adb41</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0d7c1a8c-31f0-4c9a-8131-cc9b8c697399</processParameterId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8ea6f6d4-6dd3-4bef-b0ab-1dbd8286e2c5</guid>
            <versionId>80a1638a-f221-400a-8c7c-52d5b89d5bdd</versionId>
        </processParameter>
        <processVariable name="contractAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.505a272e-683f-4a87-82e7-603540ab1814</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2b6944cc-62ae-4777-97ac-62f3828a4447</guid>
            <versionId>98bb632a-101e-4bf3-a6da-c5cfb36b11d8</versionId>
        </processVariable>
        <processVariable name="debugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f99615a1-d7f7-4251-82ab-a9f9f97a31f8</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>777feac3-e4f1-47a4-b581-240e945d8304</guid>
            <versionId>415e7c39-c20c-4811-bc43-559efaee37ba</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c5cdd5f9-8dea-4923-88b8-0f01b2d263ef</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>*************-48c0-9397-7cbfe25ec8a7</guid>
            <versionId>478db13f-407a-4ff5-82fe-ad7300fc5d4c</versionId>
        </processVariable>
        <processVariable name="interestRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f02805d9-30e5-437b-86ce-394cf316414a</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>464e6f8f-d61e-4e5a-9a66-bb7c3dc05e8f</guid>
            <versionId>d22474e1-5796-4594-8d94-dad50295551e</versionId>
        </processVariable>
        <processVariable name="numberOfDays">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.821b82e5-9ca9-40cb-8206-703904b63534</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ecc9294-1d69-4f79-83ca-53f05b86721f</guid>
            <versionId>f23b9442-4d85-4d19-a368-4f4f2c4d2a20</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63ab96df-fe0c-4f2c-8a9a-a3eeef6994f7</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a091e6bc-eaae-4ce6-9b17-610139335eec</guid>
            <versionId>20cdfe54-650a-476a-b621-e55fcef440de</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.15fe5ace-bca2-438b-88d1-0643d8569bd8</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>871fcfb3-1fe8-43af-83c3-66a5b73063cf</guid>
            <versionId>a85e5400-1fab-4737-8fe9-082eb58be655</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ceb94201-31b0-4f89-8a87-896b6f9cba29</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>805c2cd8-4d17-437c-a2f2-66e397a41cba</guid>
            <versionId>e4dfb1d0-4383-4d25-a587-51fa76913912</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ad111e2e-f7f0-431f-8644-c0b9eb520835</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0e997028-8257-421f-9007-f1c09c32a3f8</guid>
            <versionId>30c89842-f32e-434d-a12c-1fe4b43a3a45</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4b5fe476-4278-48aa-8cf9-362b152fb697</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6787ce6d-52c0-4f43-bff0-abecb5b5aa95</guid>
            <versionId>e3f9ad98-a9dd-4f29-b156-f7db96a67897</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0b8c3529-8523-44fa-81cf-ed3e74f2a3c0</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>da797423-f380-4c03-99c9-c79fd2e9e628</guid>
            <versionId>40ea18d1-00f6-45b3-a526-aa23efe5a23c</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1da269fb-9a38-4a7c-8cac-d2766b8b5a9a</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>18a1c4e1-7e93-46dd-8a5d-************</guid>
            <versionId>bdacdf9d-c259-42cb-a426-8c0d86ec561a</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6d814a74-8a5f-4579-8b61-ceb904b7846b</processVariableId>
            <description isNull="true" />
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9225f374-8b52-4b6e-874c-c19e7ce08d03</guid>
            <versionId>eed37777-3e5a-49a3-8b41-1d87044fba8f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</processItemId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.c66241f5-e646-4798-8881-768b2efcc418</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-eee</guid>
            <versionId>0f2c6b43-727f-4d95-97de-71d8d971a53d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="517" y="32">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.c66241f5-e646-4798-8881-768b2efcc418</switchId>
                <guid>0059c2d1-0be9-4bf8-9385-6138f214871e</guid>
                <versionId>06186e2e-fd95-4e42-b5e8-e66605b19fcd</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.*************-4d5f-8877-b5c926b4f7bf</switchConditionId>
                    <switchId>3013.c66241f5-e646-4798-8881-768b2efcc418</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb8</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>3974f580-9a9d-4f26-8989-49ce402e44b9</guid>
                    <versionId>7fe28262-2fdd-4b4e-8c5e-2819ed1975ae</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</processItemId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.257fff89-d519-4567-b7e7-775a4586f35a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb3</guid>
            <versionId>7463a9c6-fd3f-45ea-8dcf-5aaf6d8db6ff</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="354" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.257fff89-d519-4567-b7e7-775a4586f35a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>a7a20e4f-32b6-4a82-b13f-116579b37493</guid>
                <versionId>1f973f98-fe95-49a2-b9d4-2bf1e8fd93b4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.45fa282f-02ae-4924-867a-8822d8e91318</processItemId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.d9b9ce1c-7d9d-4acd-a50d-6d0b420b86a3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18944d56ab0:11c7</guid>
            <versionId>7c185490-592c-4096-830f-4b893510700a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="177" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomRight</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb3</errorHandlerItem>
                <errorHandlerItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.d9b9ce1c-7d9d-4acd-a50d-6d0b420b86a3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	var splittedArray = tw.local.data.split(",");&#xD;
	tw.local.interestRate = splittedArray[0];&#xD;
	tw.local.contractAmount = splittedArray[1];&#xD;
	tw.local.numberOfDays = splittedArray[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>be52a0dd-115e-4664-887b-1f3c78b29a76</guid>
                <versionId>ce8d3503-60bf-42a6-addd-fae289506547</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</processItemId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.65317ec2-5a03-4a17-b897-8f189ba41a1b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:8a32e7e0f533ea09:-114388d5:18944d56ab0:11c3</guid>
            <versionId>7d233f0d-bb1e-45e0-865d-1e1f70bac55a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="649" y="36">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.65317ec2-5a03-4a17-b897-8f189ba41a1b</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0d258bac-5f5f-4103-96ac-6260adac34d7</guid>
                <versionId>ea3622d0-c0dd-457c-9823-3e9dd7611f52</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</processItemId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <name>Calc Interest Amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</errorHandlerItemId>
            <guid>guid:03b1a2360e7e8990:-4d5b5054:18954a5bdc2:-4543</guid>
            <versionId>bca507d2-62a2-49a3-ac81-8c01fe47225a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb3</errorHandlerItem>
                <errorHandlerItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.e8fecec0-27a7-428c-8a39-56e91a3fe975</attachedProcessRef>
                <guid>c1d4d9e1-880d-477c-8bcc-a51f330c8605</guid>
                <versionId>4f5e962f-4e7c-435e-9a78-efd784525cce</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.98c380a5-91ab-481c-b07d-752df1828979</parameterMappingId>
                    <processParameterId>2055.69b75103-5816-4269-b337-b5e6054eb512</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestAppID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1370376f-737e-4e99-a4c7-98711b875f3d</guid>
                    <versionId>17988c9c-2949-479a-bfc1-449cfb3f5278</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="interest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3936fb55-f4ac-4783-9c09-2cd8f63b55d7</parameterMappingId>
                    <processParameterId>2055.5ddb54c4-2023-42e8-8b81-8c4c86645dfb</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e5fc1bcf-5cdf-4c9b-bf29-311f1f1e81a6</guid>
                    <versionId>4b785db2-fb80-4938-b141-028878f5f91b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="debugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.23f3b7e5-679d-4b55-ae97-6af68358cd42</parameterMappingId>
                    <processParameterId>2055.43634d2e-7060-46c9-856e-3a70c18a5843</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.debugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cde9ea9f-e01c-4c09-903a-581eef2d086f</guid>
                    <versionId>6c6a2e77-87b9-43b9-981b-622b78d6c5b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4bd9e5cc-f0d8-41de-9ab0-74276be63235</parameterMappingId>
                    <processParameterId>2055.7a10f7fc-eb93-4a88-9d3f-e4a90e49ba04</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.processName</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e443ce84-c4fc-46db-9de0-99bf2380c84c</guid>
                    <versionId>76a00256-9006-410e-9d7a-ca96a1cbee64</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="interestRate">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5a22761f-cff0-4bf6-9348-00eee3dc965e</parameterMappingId>
                    <processParameterId>2055.a4dc5a59-5755-4fe2-8c71-d4bf6d6d732c</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.interestRate</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>91d0e2a3-fa6f-4e7f-bef9-2188b26c5b3a</guid>
                    <versionId>7e062e33-b6d5-416a-88ca-18706e22caf8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0efc63e2-721e-4d93-b0a9-d9700820f783</parameterMappingId>
                    <processParameterId>2055.46177c25-f032-405c-8698-f3ea556a80cd</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ea45a8b7-ff6b-4411-b150-5ecd53dd18e7</guid>
                    <versionId>80032ff2-578d-45d5-8b5f-a900a5cb4121</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.345016f8-7717-49e7-9e33-4adf8b1e7282</parameterMappingId>
                    <processParameterId>2055.08a77e96-1d61-43ad-a94d-3b5999425177</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cf47e6ea-47c1-4cbf-ab4a-230ee72c92eb</guid>
                    <versionId>8937d307-1bb4-4c74-bfa7-e8fa95a70cca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="numberOfDays">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef98e865-59ac-4803-ad04-c01e511603b3</parameterMappingId>
                    <processParameterId>2055.5b2a0a73-a2ed-4917-87c4-dfbc7c4f7df2</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.numberOfDays</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2760a7c0-530c-44e3-84cd-b5bd36b5434c</guid>
                    <versionId>8ec5f097-9858-48fc-bd8b-42520234cd3f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.28fa1660-b4d2-4297-9926-8532b31ced97</parameterMappingId>
                    <processParameterId>2055.67e53230-32b5-4c78-a136-6ca623062f09</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.userID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b17c5ef5-2cc8-440a-8c0b-8c7002d62c6f</guid>
                    <versionId>94670287-c1dd-41c2-973b-b21739eeaae9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bf4ddfd3-c4ac-4073-8cb1-72849d29b288</parameterMappingId>
                    <processParameterId>2055.9fc52508-794a-4e64-a550-16939f77ace4</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.snapshot</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e59f254b-2bfd-4ce9-8c1c-e24c0f3319e7</guid>
                    <versionId>99e46c24-ae28-447f-a17e-a5a4c11f9171</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4604ea73-899a-4f65-98fa-86e42dc31106</parameterMappingId>
                    <processParameterId>2055.c5fd3217-96b2-49f2-836c-21caa3faa604</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ae979c6d-43c3-42c1-b03c-7076180c4cc5</guid>
                    <versionId>af9775e2-a7af-48ed-86ec-9e0921f14a78</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="contractAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29418770-a190-43be-87f2-26736b377ccd</parameterMappingId>
                    <processParameterId>2055.85368963-39d7-4ffe-8cad-6cc051bd0369</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.contractAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>771ff5e0-64e0-4618-922f-d6d7b32bfea8</guid>
                    <versionId>dc7abda8-6013-43c9-b842-f7cf391f3424</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e54cd36f-6a08-4af6-aec1-764e2fa99621</parameterMappingId>
                    <processParameterId>2055.688aefc6-3ead-458e-95cf-d6e0fd1df5b1</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>aae4f235-ba02-4b46-8c08-95a5c95d9e28</guid>
                    <versionId>dde18fd2-4cd0-4c7c-8276-0e1b6c5248eb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.079ad52c-910a-4d13-a06f-aaad3ed3e943</parameterMappingId>
                    <processParameterId>2055.a4cdaf76-a9f3-49d9-ba5f-42881ee68472</processParameterId>
                    <parameterMappingParentId>3012.92bf4b96-fd5f-4e81-9268-4383817beb79</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>580064be-d13c-4bb1-b14f-1c17fd3b50ee</guid>
                    <versionId>e076b9f9-e4f3-4cbd-9207-fbae72238191</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.45fa282f-02ae-4924-867a-8822d8e91318</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Interest Amount" id="1.0d77a33d-fe75-4164-87ca-b9d0a0b91640" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.718220fc-cc4b-4b13-8268-1cff10654a3c">
                            
                            
                            <ns16:documentation textFormat="text/plain" />
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"4,3000,15.9"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.231395e2-9065-4f46-8ac0-df1371b3c9b6" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.0d7c1a8c-31f0-4c9a-8131-cc9b8c697399" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="673799e8-72f2-4896-8cc6-f3921820c56c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="1de82fb2-7966-40f9-8a6c-027dbfe56139" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="325" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>6c487b13-268b-4dea-8d88-c0eea3874771</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>45fa282f-02ae-4924-867a-8822d8e91318</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e4d06b76-3a1a-4748-8214-8219e3405e16</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>33168201-e363-4bef-8769-09ee2a1463dc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f0bcc080-11b9-4399-824c-df260328a0c1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="6c487b13-268b-4dea-8d88-c0eea3874771">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="33" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.6ffb10bb-d153-4261-8d8e-65463ec599e4</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="e027d8fc-84d2-4b8e-8c1c-1db18e788d6d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="649" y="36" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:8a32e7e0f533ea09:-114388d5:18944d56ab0:11c3</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff</ns16:incoming>
                        
                        
                        <ns16:incoming>0dba1b3c-a597-4c6f-865f-77fc56a3e74a</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="6c487b13-268b-4dea-8d88-c0eea3874771" targetRef="45fa282f-02ae-4924-867a-8822d8e91318" name="To Calc Interest Amount" id="2027.6ffb10bb-d153-4261-8d8e-65463ec599e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="45fa282f-02ae-4924-867a-8822d8e91318">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.6ffb10bb-d153-4261-8d8e-65463ec599e4</ns16:incoming>
                        
                        
                        <ns16:outgoing>5ddd0512-0a0a-4f99-8463-b7e7970f9c72</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	var splittedArray = tw.local.data.split(",");&#xD;
	tw.local.interestRate = splittedArray[0];&#xD;
	tw.local.contractAmount = splittedArray[1];&#xD;
	tw.local.numberOfDays = splittedArray[2];&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.e8fecec0-27a7-428c-8a39-56e91a3fe975" name="Calc Interest Amount" id="2a376fa0-e8c5-4c0e-891e-2c373a3e5e17">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="10" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5ddd0512-0a0a-4f99-8463-b7e7970f9c72</ns16:incoming>
                        
                        
                        <ns16:outgoing>3a2c6946-ea1a-48d3-891b-ae52c88233b7</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c5fd3217-96b2-49f2-836c-21caa3faa604</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.67e53230-32b5-4c78-a136-6ca623062f09</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.userID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.46177c25-f032-405c-8698-f3ea556a80cd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7a10f7fc-eb93-4a88-9d3f-e4a90e49ba04</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.processName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9fc52508-794a-4e64-a550-16939f77ace4</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.snapshot</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.69b75103-5816-4269-b337-b5e6054eb512</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestAppID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.43634d2e-7060-46c9-856e-3a70c18a5843</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.debugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.85368963-39d7-4ffe-8cad-6cc051bd0369</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.contractAmount</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a4dc5a59-5755-4fe2-8c71-d4bf6d6d732c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.interestRate</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5b2a0a73-a2ed-4917-87c4-dfbc7c4f7df2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.numberOfDays</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.5ddb54c4-2023-42e8-8b81-8c4c86645dfb</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a4cdaf76-a9f3-49d9-ba5f-42881ee68472</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.688aefc6-3ead-458e-95cf-d6e0fd1df5b1</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.08a77e96-1d61-43ad-a94d-3b5999425177</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="2a376fa0-e8c5-4c0e-891e-2c373a3e5e17" targetRef="f0bcc080-11b9-4399-824c-df260328a0c1" name="To is Successful" id="3a2c6946-ea1a-48d3-891b-ae52c88233b7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:-6a84</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractAmount" id="2056.505a272e-683f-4a87-82e7-603540ab1814" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="debugMode" id="2056.f99615a1-d7f7-4251-82ab-a9f9f97a31f8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.c5cdd5f9-8dea-4923-88b8-0f01b2d263ef" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="interestRate" id="2056.f02805d9-30e5-437b-86ce-394cf316414a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="numberOfDays" id="2056.821b82e5-9ca9-40cb-8206-703904b63534" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.63ab96df-fe0c-4f2c-8a9a-a3eeef6994f7" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.15fe5ace-bca2-438b-88d1-0643d8569bd8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.ceb94201-31b0-4f89-8a87-896b6f9cba29" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.ad111e2e-f7f0-431f-8644-c0b9eb520835" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.4b5fe476-4278-48aa-8cf9-362b152fb697" />
                    
                    
                    <ns16:sequenceFlow sourceRef="45fa282f-02ae-4924-867a-8822d8e91318" targetRef="2a376fa0-e8c5-4c0e-891e-2c373a3e5e17" name="To Calc Interest Amount" id="5ddd0512-0a0a-4f99-8463-b7e7970f9c72">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="45fa282f-02ae-4924-867a-8822d8e91318" parallelMultiple="false" name="Error" id="e4d06b76-3a1a-4748-8214-8219e3405e16">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="238" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>df6bb3ce-735e-497f-8c4a-c88a23a14678</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="30d7c603-e54b-406d-864d-c5c77822e556" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8382f693-4616-4949-86b0-5a464d7a73db" eventImplId="2d1a9f0d-ac0a-4798-8521-35487ae1f5e7">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="2a376fa0-e8c5-4c0e-891e-2c373a3e5e17" parallelMultiple="false" name="Error1" id="33168201-e363-4bef-8769-09ee2a1463dc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a6f768f2-494e-4d98-8994-104c98b14e30</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5ccfeaa3-9270-4bc9-88ec-aacc178dd45a" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="06bd0411-f564-4105-8f53-e6b9325d08d0" eventImplId="31f8e159-3878-4277-8443-b43b27917a17">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e4d06b76-3a1a-4748-8214-8219e3405e16" targetRef="738c9c61-60a0-4dcc-8ffa-f0f92b7b8448" name="To End Event" id="df6bb3ce-735e-497f-8c4a-c88a23a14678">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="33168201-e363-4bef-8769-09ee2a1463dc" targetRef="738c9c61-60a0-4dcc-8ffa-f0f92b7b8448" name="To End Event" id="a6f768f2-494e-4d98-8994-104c98b14e30">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.0b8c3529-8523-44fa-81cf-ed3e74f2a3c0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.1da269fb-9a38-4a7c-8cac-d2766b8b5a9a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.6d814a74-8a5f-4579-8b61-ceb904b7846b" />
                    
                    
                    <ns16:exclusiveGateway default="ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff" name="is Successful" id="f0bcc080-11b9-4399-824c-df260328a0c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="517" y="32" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3a2c6946-ea1a-48d3-891b-ae52c88233b7</ns16:incoming>
                        
                        
                        <ns16:outgoing>ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff</ns16:outgoing>
                        
                        
                        <ns16:outgoing>f07d3535-e75b-4e04-8ed4-4b533632a2df</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f0bcc080-11b9-4399-824c-df260328a0c1" targetRef="e027d8fc-84d2-4b8e-8c1c-1db18e788d6d" name="To End" id="ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f0bcc080-11b9-4399-824c-df260328a0c1" targetRef="738c9c61-60a0-4dcc-8ffa-f0f92b7b8448" name="To End Event" id="f07d3535-e75b-4e04-8ed4-4b533632a2df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="738c9c61-60a0-4dcc-8ffa-f0f92b7b8448">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="354" y="210" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>df6bb3ce-735e-497f-8c4a-c88a23a14678</ns16:incoming>
                        
                        
                        <ns16:incoming>f07d3535-e75b-4e04-8ed4-4b533632a2df</ns16:incoming>
                        
                        
                        <ns16:incoming>a6f768f2-494e-4d98-8994-104c98b14e30</ns16:incoming>
                        
                        
                        <ns16:outgoing>0dba1b3c-a597-4c6f-865f-77fc56a3e74a</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="738c9c61-60a0-4dcc-8ffa-f0f92b7b8448" targetRef="e027d8fc-84d2-4b8e-8c1c-1db18e788d6d" name="To End" id="0dba1b3c-a597-4c6f-865f-77fc56a3e74a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Calc Interest Amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5ddd0512-0a0a-4f99-8463-b7e7970f9c72</processLinkId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.45fa282f-02ae-4924-867a-8822d8e91318</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</toProcessItemId>
            <guid>92e81836-dc62-44ba-9af3-2da7db6db6c6</guid>
            <versionId>1eef4a00-3f4c-4ba8-a30a-5694983b4107</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.45fa282f-02ae-4924-867a-8822d8e91318</fromProcessItemId>
            <toProcessItemId>2025.2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f07d3535-e75b-4e04-8ed4-4b533632a2df</processLinkId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3eb8</endStateId>
            <toProcessItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</toProcessItemId>
            <guid>982692d1-4111-465e-9017-68f499e1614a</guid>
            <versionId>338df5ff-ba93-4b6f-a425-fa817076f45c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topRight" portType="2" />
            <fromProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</fromProcessItemId>
            <toProcessItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0dba1b3c-a597-4c6f-865f-77fc56a3e74a</processLinkId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</toProcessItemId>
            <guid>4437a334-ea7a-4ae6-b8ac-30816d8f5e49</guid>
            <versionId>6018f251-ef28-4986-9e16-7e913a105469</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.738c9c61-60a0-4dcc-8ffa-f0f92b7b8448</fromProcessItemId>
            <toProcessItemId>2025.e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ff4ea71c-adaa-44a3-8d70-60ff7f1b01ff</processLinkId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</toProcessItemId>
            <guid>9b8f38d4-4af4-4155-bfa8-d32ab9dcd4cb</guid>
            <versionId>c55a7d13-89c2-4953-963a-637fe2002fdf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</fromProcessItemId>
            <toProcessItemId>2025.e027d8fc-84d2-4b8e-8c1c-1db18e788d6d</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3a2c6946-ea1a-48d3-891b-ae52c88233b7</processLinkId>
            <processId>1.0d77a33d-fe75-4164-87ca-b9d0a0b91640</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</fromProcessItemId>
            <endStateId>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:-6a84</endStateId>
            <toProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</toProcessItemId>
            <guid>288f16c8-fd82-49ab-a7ff-a15ba6c7d1ac</guid>
            <versionId>fafb39db-c793-4309-9995-5dfce029e0e3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2a376fa0-e8c5-4c0e-891e-2c373a3e5e17</fromProcessItemId>
            <toProcessItemId>2025.f0bcc080-11b9-4399-824c-df260328a0c1</toProcessItemId>
        </link>
    </process>
</teamworks>

