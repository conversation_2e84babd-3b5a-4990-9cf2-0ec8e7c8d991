<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.97607171-1a4e-436c-8b92-7a6920219722" name="Customs Release Approval Mail Service">
        <lastModified>1692505335230</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>84468ca8-1d67-4c53-b6bb-46bc12f4ee64</guid>
        <versionId>8ebcbfcb-ab1c-4985-b703-1a40cd13a88e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d77" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"09987d69-cb05-4d54-aee4-03934432c356"},{"incoming":["a2200d8e-0add-477c-8ef5-df2cdd132efe","82aa9143-0b38-4b0e-8c67-7dcb942d9fe6"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":670,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6226"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"4aa3d500-538e-4737-a425-1e9a2382ef2e"},{"targetRef":"a5275662-4e32-4e05-8d6c-83575a3983c1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb","sourceRef":"09987d69-cb05-4d54-aee4-03934432c356"},{"startQuantity":1,"outgoing":["a2200d8e-0add-477c-8ef5-df2cdd132efe"],"incoming":["89b283a7-f7d8-43a7-8cb2-062229361c8d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":364,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Send Email","dataInputAssociation":[{"targetRef":"2055.a7c74b41-811f-4581-94ef-69a84c74eb84","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.ae81d59d-2fde-4526-9476-d0598d6e8472","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.subject"]}}]},{"targetRef":"2055.6d9bd911-88b8-4ea3-8823-421a4f690290","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.msgBody"]}}]},{"targetRef":"2055.268afc2e-a651-49ef-8704-9a6ff22065c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.20348cf5-023e-4d3a-826e-5b92143ec224","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.1da05789-2131-46bc-aacf-34d84ca37def","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailDebugMode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"24b92796-3d2c-4b43-a02b-2718fc5e2755","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.ec26c46c-d70b-4881-98d8-40e694dd7362"]}],"calledElement":"1.338e9f4d-8538-4ceb-a155-c288604435d4"},{"targetRef":"4aa3d500-538e-4737-a425-1e9a2382ef2e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2970"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a2200d8e-0add-477c-8ef5-df2cdd132efe","sourceRef":"24b92796-3d2c-4b43-a02b-2718fc5e2755"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"2056.ed905b23-8b99-4d38-a2cb-cb070d2bd814"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"msgBody","isCollection":false,"declaredType":"dataObject","id":"2056.140ae692-b58f-4eb7-8820-848aceaf2a33"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"subject","isCollection":false,"declaredType":"dataObject","id":"2056.eae1fadd-29f8-4c9b-bb5a-06c41704f46c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.4cbca3c4-06e5-478e-b558-02d939c0941f"},{"startQuantity":1,"outgoing":["89b283a7-f7d8-43a7-8cb2-062229361c8d"],"incoming":["2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":218,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"a5275662-4e32-4e05-8d6c-83575a3983c1","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tif (tw.local.idcRequest == null) {\r\n\t\ttw.local.idcRequest = new tw.object.IDCRequest();\r\n\t\t\r\n\t\ttw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.appInfo = new tw.object.AppInfo();\r\n\t\ttw.local.idcRequest.appInfo.instanceID = \"\";\r\n\t\t\r\n\t\ttw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();\r\n\t\r\n\t\ttw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.importPurpose = new tw.object.DBLookup();\r\n\t\ttw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();\r\n\t\ttw.local.idcRequest.customerInformation.customerName = \"\";\r\n\t\t\r\n\t\ttw.local.idcRequest.invoices = new tw.object.listOf.Invoice();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.productCategory = new tw.object.DBLookup();\r\n\t\ttw.local.idcRequest.documentsSource = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.paymentTerms = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.approvals = new tw.object.Approvals();\r\n\t\t\r\n\t\ttw.local.idcRequest.appLog = new tw.object.listOf.AppLog();\r\n\t}\r\n\t\r\n\tvar receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;\r\n\tvar dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;\r\n\t\/\/var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;\r\n\t\r\n\tvar taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;\r\n\t\r\n\t\r\n\tvar owner = tw.system.findTaskByID(tw.local.taskid).owner;\r\n\t\r\n\tif (owner == null){owner = \"\"}\r\n\t\r\n\ttw.local.subject = \"Customs Release  No. \"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\" for Customer \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\" is approved \"\r\n\t\t\t\t+\"\u0637\u0644\u0628 \u0627\u0644\u062a\u062d\u0635\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u0646\u062f\u0649 \u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0631\u0642\u0645 \"+tw.local.idcRequest.appInfo.instanceID+\r\n\t\t\t\t\" \u0644\u0644\u0639\u0645\u064a\u0644 \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\" \u062a\u0645\u062a \u0627\u0644\u0645\u0648\u0627\u0641\u0642\u0629 \u0639\u0644\u0649 \u0625\u0639\u0627\u062f\u0629 \u0642\u064a\u062f\u0647\";\r\n\t\r\n\ttw.local.msgBody = '&lt;html dir=\"ltl\" lang=\"en\"&gt;'\r\n\t\t\t\t+\"&lt;p&gt;Dear Sir \/ Madam,&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;Kindly be informed that the Customs Release  \"\r\n\t\t\t\t+\"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; has been approved &lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;br&gt;&lt;\/br&gt;&lt;p&gt;\u0627\u0644\u0633\u064a\u062f \/ \u0627\u0644\u0633\u064a\u062f\u0629&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;\u0628\u0631\u062c\u0627\u0621 \u0627\u0644\u0639\u0644\u0645 \u0623\u0646 \u0637\u0644\u0628 \u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u0625\u0641\u0631\u0627\u062c \u0627\u0644\u062c\u0645\u0631\u0643\u0649  &lt;strong&gt;&lt;em&gt;&amp;lt;\"\r\n\t\t\t\t+\" \u0631\u0642\u0645 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; \u0644\u0644\u0639\u0645\u064a\u0644 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\"&lt;p&gt;\u062a\u0645\u062a \u0627\u0644\u0645\u0648\u0627\u0641\u0642\u0629 \u0639\u0644\u064a\u0647&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;\u0627\u0644\u0631\u062c\u0627\u0621 \u0639\u062f\u0645 \u0627\u0644\u0631\u062f \u0639\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0631\u0633\u0627\u0644\u0629. \u0647\u0630\u0627 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627.&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;\/html&gt;\"\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"24b92796-3d2c-4b43-a02b-2718fc5e2755","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Send Email","declaredType":"sequenceFlow","id":"89b283a7-f7d8-43a7-8cb2-062229361c8d","sourceRef":"a5275662-4e32-4e05-8d6c-83575a3983c1"},{"parallelMultiple":false,"outgoing":["bdde03b3-0f3c-4252-8dbc-813e0e83a658"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2d2e6d4f-4150-4fb8-882b-46c8bb9931e5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ccfe93bd-5c73-4057-854e-180b248069db","otherAttributes":{"eventImplId":"2c43fd3b-55a4-4cba-8452-551b973c9e6d"}}],"attachedToRef":"24b92796-3d2c-4b43-a02b-2718fc5e2755","extensionElements":{"nodeVisualInfo":[{"width":24,"x":399,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"c5101f4c-3b8b-48d3-8a98-f188b024f962","outputSet":{}},{"parallelMultiple":false,"outgoing":["c0bb24cf-0de7-4032-8fc4-4e659ba766d3"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3866b16c-1ba8-4f0f-8b0a-d8d1fe378d5d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3f057f2f-9c51-4adc-8086-2ba730e8edb8","otherAttributes":{"eventImplId":"ad6807da-2e96-4d66-883c-dad7e6307041"}}],"attachedToRef":"a5275662-4e32-4e05-8d6c-83575a3983c1","extensionElements":{"nodeVisualInfo":[{"width":24,"x":253,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"73905cfb-5cdb-4592-8a25-880aa45309a5","outputSet":{}},{"startQuantity":1,"outgoing":["82aa9143-0b38-4b0e-8c67-7dcb942d9fe6"],"incoming":["c0bb24cf-0de7-4032-8fc4-4e659ba766d3","bdde03b3-0f3c-4252-8dbc-813e0e83a658"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":327,"y":196,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8b8225b5-2063-4aa8-862f-9d7bbb9db11b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"8b8225b5-2063-4aa8-862f-9d7bbb9db11b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c0bb24cf-0de7-4032-8fc4-4e659ba766d3","sourceRef":"73905cfb-5cdb-4592-8a25-880aa45309a5"},{"targetRef":"8b8225b5-2063-4aa8-862f-9d7bbb9db11b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"bdde03b3-0f3c-4252-8dbc-813e0e83a658","sourceRef":"c5101f4c-3b8b-48d3-8a98-f188b024f962"},{"targetRef":"4aa3d500-538e-4737-a425-1e9a2382ef2e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"82aa9143-0b38-4b0e-8c67-7dcb942d9fe6","sourceRef":"8b8225b5-2063-4aa8-862f-9d7bbb9db11b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.e732cec5-e392-4534-850b-fb2381ae79ee"}],"laneSet":[{"id":"2db6fcd6-6f35-40f0-9ed3-2e6fe351fe22","lane":[{"flowNodeRef":["09987d69-cb05-4d54-aee4-03934432c356","4aa3d500-538e-4737-a425-1e9a2382ef2e","24b92796-3d2c-4b43-a02b-2718fc5e2755","a5275662-4e32-4e05-8d6c-83575a3983c1","c5101f4c-3b8b-48d3-8a98-f188b024f962","73905cfb-5cdb-4592-8a25-880aa45309a5","8b8225b5-2063-4aa8-862f-9d7bbb9db11b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"89d6ebc3-441b-4b76-bbd5-9bdd1c5e7945","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Customs Release Approval Mail Service","declaredType":"process","id":"1.97607171-1a4e-436c-8b92-7a6920219722","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.6da3c677-4582-4e72-8331-ca5621dfecce"}],"inputSet":[{"dataInputRefs":["2055.f5645e31-5ae3-4eae-afdb-0b90bfaec8bb","2055.0e0c059d-5c4c-4e97-b53f-e35822b07a27","2055.5a264147-9d81-4ea0-b43c-b8951b3cb319"]}],"outputSet":[{"dataOutputRefs":["2055.6da3c677-4582-4e72-8331-ca5621dfecce"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Y\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailDebugMode","isCollection":false,"id":"2055.f5645e31-5ae3-4eae-afdb-0b90bfaec8bb"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"2078.70913\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskid","isCollection":false,"id":"2055.0e0c059d-5c4c-4e97-b53f-e35822b07a27"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"12345678909876\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"test\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.5a264147-9d81-4ea0-b43c-b8951b3cb319"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="mailDebugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f5645e31-5ae3-4eae-afdb-0b90bfaec8bb</processParameterId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Y"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0fbdfa0c-dc78-4789-a7f0-48b8c94dbc16</guid>
            <versionId>637d4717-114d-4b0e-a83a-a47de35534fa</versionId>
        </processParameter>
        <processParameter name="taskid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0e0c059d-5c4c-4e97-b53f-e35822b07a27</processParameterId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"2078.70913"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>daaf8235-ce01-4724-9e8e-487156a52f45</guid>
            <versionId>9a715f64-44f9-40cb-8bf7-2ea763e0ec9d</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5a264147-9d81-4ea0-b43c-b8951b3cb319</processParameterId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cb8c7535-ae7e-483e-ad3c-0ac5e2e09c88</guid>
            <versionId>153739c1-0347-4231-9a02-47da6cdf5d83</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6da3c677-4582-4e72-8331-ca5621dfecce</processParameterId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d3fdeee2-f683-4761-aa90-b1c5bac68040</guid>
            <versionId>4ccb0f18-e502-410d-8e88-c40c246d3534</versionId>
        </processParameter>
        <processVariable name="mailTo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ed905b23-8b99-4d38-a2cb-cb070d2bd814</processVariableId>
            <description isNull="true" />
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>d8d39a89-ab83-420c-a3c4-9f7e1d0d8c8c</guid>
            <versionId>b4a372a5-4159-42f7-9721-2259d3a6515b</versionId>
        </processVariable>
        <processVariable name="msgBody">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.140ae692-b58f-4eb7-8820-848aceaf2a33</processVariableId>
            <description isNull="true" />
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>64bebdda-d878-4134-8f00-77ce451488c8</guid>
            <versionId>d44f6f7e-b60d-41cf-b03d-b4c44f86230c</versionId>
        </processVariable>
        <processVariable name="subject">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eae1fadd-29f8-4c9b-bb5a-06c41704f46c</processVariableId>
            <description isNull="true" />
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>bc1207bb-3968-40f4-bcdc-28e9ca390906</guid>
            <versionId>5550dc27-9db5-4a6c-b07c-ac6051f05940</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4cbca3c4-06e5-478e-b558-02d939c0941f</processVariableId>
            <description isNull="true" />
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a52a1371-1f7d-4660-8075-0ddd3e9f721e</guid>
            <versionId>20f602e9-bf27-4593-bd6a-3d77a7b08fda</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e732cec5-e392-4534-850b-fb2381ae79ee</processVariableId>
            <description isNull="true" />
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3cb7766e-7fe4-4272-9556-501e2b8dc69d</guid>
            <versionId>b74943a7-19da-4e29-953f-30eca1850237</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</processItemId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9b534640-6f23-4155-a06c-39c53f559f06</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d76</guid>
            <versionId>4fb26b31-5905-41cd-864d-9af55e6b3ecf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="327" y="196">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9b534640-6f23-4155-a06c-39c53f559f06</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>e360d821-60aa-4fa1-b70d-11cb74978bda</guid>
                <versionId>da58b0f4-fdcd-438a-ac55-aa3bec3b3952</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.24b92796-3d2c-4b43-a02b-2718fc5e2755</processItemId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <name>Send Email</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6224</guid>
            <versionId>74c8d615-e870-44e8-a626-1ca602e7fdf6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d76</errorHandlerItem>
                <errorHandlerItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.338e9f4d-8538-4ceb-a155-c288604435d4</attachedProcessRef>
                <guid>ce7082db-aac3-497c-a259-908493da628e</guid>
                <versionId>cf11fa7f-4e7a-4543-8f00-e21ed3bf2dd7</versionId>
                <parameterMapping name="subject">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0ccbf620-11df-415c-bb88-8d15bc02b89d</parameterMappingId>
                    <processParameterId>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subject</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>66c2e652-d083-4700-897d-6268b3a1582e</guid>
                    <versionId>04d97be1-1238-46de-8149-ffe2cda477d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ba2c423-837b-46b2-8e73-6ff40141343c</parameterMappingId>
                    <processParameterId>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>caf73013-9f82-4855-b4c2-2a7cd73545b6</guid>
                    <versionId>11f165ca-1a34-474f-afb8-e47c43861e31</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="msgBody">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b3e106b1-aedf-47a5-a8e9-2a94729d9f51</parameterMappingId>
                    <processParameterId>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.msgBody</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>619dd49b-3bc1-494c-b909-3a1fc7cba0ec</guid>
                    <versionId>35645ab6-9f25-4e1f-952a-6d43aae309f4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailTo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.16d43bbc-**************-1e7cf1931539</parameterMappingId>
                    <processParameterId>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailTo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4e188029-c5a8-449d-b684-ce3c5aa7514d</guid>
                    <versionId>70d416ca-399d-433c-950d-cb25acae86b6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="attachments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8a5a7bd9-b1ce-4093-afe9-8dc32f8838d3</parameterMappingId>
                    <processParameterId>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.8a11240b-682f-4caf-9f03-5ce6a64d720b</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>ce5e2112-b7b7-49bc-a463-50c0539de204</guid>
                    <versionId>782b3db4-e6fa-4fe2-bfd4-e631fec08075</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b19e545c-110f-4ac7-b953-5459a4793909</parameterMappingId>
                    <processParameterId>2055.20348cf5-023e-4d3a-826e-5b92143ec224</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d2697d27-38ac-4cf6-8266-2258eecd4a8b</guid>
                    <versionId>921226a6-6689-4fb9-ac33-150570ad0a83</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailDebugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.134a9718-74c3-4688-8257-bdbcd40363c2</parameterMappingId>
                    <processParameterId>2055.1da05789-2131-46bc-aacf-34d84ca37def</processParameterId>
                    <parameterMappingParentId>3012.8e325bec-ff06-4c06-b96d-d9e7bc987057</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailDebugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c25bae05-8e07-4d5f-a856-176914bc5cff</guid>
                    <versionId>b0a2fc3d-6555-495d-a8ed-047927ab3764</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</processItemId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a6d77200-48cc-4f41-8a00-24a6675572bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6225</guid>
            <versionId>c9d693c8-c5cd-4733-b074-ee82e253e790</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.012ba1c2-81b3-4105-8ace-455554acdd07</processItemPrePostId>
                <processItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>ecf9f74e-17bb-4546-9750-fecafb242676</guid>
                <versionId>8d2cdddb-a257-4e6a-a3d8-7a6a10f21e40</versionId>
            </processPrePosts>
            <layoutData x="218" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d76</errorHandlerItem>
                <errorHandlerItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a6d77200-48cc-4f41-8a00-24a6675572bb</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	if (tw.local.idcRequest == null) {&#xD;
		tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
		&#xD;
		tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
		tw.local.idcRequest.appInfo.instanceID = "";&#xD;
		&#xD;
		tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
	&#xD;
		tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
		tw.local.idcRequest.customerInformation.customerName = "";&#xD;
		&#xD;
		tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
		&#xD;
		tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	}&#xD;
	&#xD;
	var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
	var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
	//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
	&#xD;
	var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
	&#xD;
	&#xD;
	var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
	&#xD;
	if (owner == null){owner = ""}&#xD;
	&#xD;
	tw.local.subject = "Customs Release  No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
				+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" is approved "&#xD;
				+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
				" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" تمت الموافقة على إعادة قيده";&#xD;
	&#xD;
	tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
				+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Kindly be informed that the Customs Release  "&#xD;
				+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved &lt;/p&gt;"&#xD;
				+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
				+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
				+"&lt;p&gt;برجاء العلم أن طلب تسجيل الإفراج الجمركى  &lt;strong&gt;&lt;em&gt;&amp;lt;"&#xD;
				+" رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&lt;p&gt;تمت الموافقة عليه&lt;/p&gt;"&#xD;
				+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
			+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>05497c8d-eea1-4e46-b74e-605808cca57b</guid>
                <versionId>c403b20c-f29c-4a11-b89a-838188d16c8e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4aa3d500-538e-4737-a425-1e9a2382ef2e</processItemId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.df5b2e29-e087-4468-bbb8-a5c08eb229a0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6226</guid>
            <versionId>f992e71e-eb8b-4e3b-9829-bda4bd157b73</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="670" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.df5b2e29-e087-4468-bbb8-a5c08eb229a0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b42af325-81a7-4d16-9e26-b239284f3ccd</guid>
                <versionId>c25fb1f7-fe17-42f0-b0d7-ff02ac30c40f</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Customs Release Approval Mail Service" id="1.97607171-1a4e-436c-8b92-7a6920219722" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="mailDebugMode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f5645e31-5ae3-4eae-afdb-0b90bfaec8bb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Y"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="taskid" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0e0c059d-5c4c-4e97-b53f-e35822b07a27">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"2078.70913"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.5a264147-9d81-4ea0-b43c-b8951b3cb319">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "12345678909876";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "test";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.6da3c677-4582-4e72-8331-ca5621dfecce" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.f5645e31-5ae3-4eae-afdb-0b90bfaec8bb</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0e0c059d-5c4c-4e97-b53f-e35822b07a27</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5a264147-9d81-4ea0-b43c-b8951b3cb319</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.6da3c677-4582-4e72-8331-ca5621dfecce</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2db6fcd6-6f35-40f0-9ed3-2e6fe351fe22">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="89d6ebc3-441b-4b76-bbd5-9bdd1c5e7945" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>09987d69-cb05-4d54-aee4-03934432c356</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4aa3d500-538e-4737-a425-1e9a2382ef2e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>24b92796-3d2c-4b43-a02b-2718fc5e2755</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a5275662-4e32-4e05-8d6c-83575a3983c1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c5101f4c-3b8b-48d3-8a98-f188b024f962</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>73905cfb-5cdb-4592-8a25-880aa45309a5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8b8225b5-2063-4aa8-862f-9d7bbb9db11b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="09987d69-cb05-4d54-aee4-03934432c356">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="4aa3d500-538e-4737-a425-1e9a2382ef2e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="670" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6226</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a2200d8e-0add-477c-8ef5-df2cdd132efe</ns16:incoming>
                        
                        
                        <ns16:incoming>82aa9143-0b38-4b0e-8c67-7dcb942d9fe6</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="09987d69-cb05-4d54-aee4-03934432c356" targetRef="a5275662-4e32-4e05-8d6c-83575a3983c1" name="To Exclusive Gateway" id="2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.338e9f4d-8538-4ceb-a155-c288604435d4" name="Send Email" id="24b92796-3d2c-4b43-a02b-2718fc5e2755">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>89b283a7-f7d8-43a7-8cb2-062229361c8d</ns16:incoming>
                        
                        
                        <ns16:outgoing>a2200d8e-0add-477c-8ef5-df2cdd132efe</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailTo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.subject</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.msgBody</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.20348cf5-023e-4d3a-826e-5b92143ec224</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1da05789-2131-46bc-aacf-34d84ca37def</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailDebugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="24b92796-3d2c-4b43-a02b-2718fc5e2755" targetRef="4aa3d500-538e-4737-a425-1e9a2382ef2e" name="To End" id="a2200d8e-0add-477c-8ef5-df2cdd132efe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="2056.ed905b23-8b99-4d38-a2cb-cb070d2bd814">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="msgBody" id="2056.140ae692-b58f-4eb7-8820-848aceaf2a33">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="subject" id="2056.eae1fadd-29f8-4c9b-bb5a-06c41704f46c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.4cbca3c4-06e5-478e-b558-02d939c0941f" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="a5275662-4e32-4e05-8d6c-83575a3983c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="218" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.30e9a068-e9fc-4a6b-9f03-acb5f9e785fb</ns16:incoming>
                        
                        
                        <ns16:outgoing>89b283a7-f7d8-43a7-8cb2-062229361c8d</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	if (tw.local.idcRequest == null) {&#xD;
		tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
		&#xD;
		tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
		tw.local.idcRequest.appInfo.instanceID = "";&#xD;
		&#xD;
		tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
	&#xD;
		tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
		tw.local.idcRequest.customerInformation.customerName = "";&#xD;
		&#xD;
		tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
		&#xD;
		tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	}&#xD;
	&#xD;
	var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
	var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
	//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
	&#xD;
	var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
	&#xD;
	&#xD;
	var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
	&#xD;
	if (owner == null){owner = ""}&#xD;
	&#xD;
	tw.local.subject = "Customs Release  No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
				+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" is approved "&#xD;
				+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
				" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" تمت الموافقة على إعادة قيده";&#xD;
	&#xD;
	tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
				+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Kindly be informed that the Customs Release  "&#xD;
				+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved &lt;/p&gt;"&#xD;
				+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
				+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
				+"&lt;p&gt;برجاء العلم أن طلب تسجيل الإفراج الجمركى  &lt;strong&gt;&lt;em&gt;&amp;lt;"&#xD;
				+" رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&lt;p&gt;تمت الموافقة عليه&lt;/p&gt;"&#xD;
				+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
			+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="a5275662-4e32-4e05-8d6c-83575a3983c1" targetRef="24b92796-3d2c-4b43-a02b-2718fc5e2755" name="To Send Email" id="89b283a7-f7d8-43a7-8cb2-062229361c8d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="24b92796-3d2c-4b43-a02b-2718fc5e2755" parallelMultiple="false" name="Error" id="c5101f4c-3b8b-48d3-8a98-f188b024f962">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="399" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>bdde03b3-0f3c-4252-8dbc-813e0e83a658</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2d2e6d4f-4150-4fb8-882b-46c8bb9931e5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ccfe93bd-5c73-4057-854e-180b248069db" eventImplId="2c43fd3b-55a4-4cba-8452-551b973c9e6d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a5275662-4e32-4e05-8d6c-83575a3983c1" parallelMultiple="false" name="Error1" id="73905cfb-5cdb-4592-8a25-880aa45309a5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="253" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c0bb24cf-0de7-4032-8fc4-4e659ba766d3</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3866b16c-1ba8-4f0f-8b0a-d8d1fe378d5d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3f057f2f-9c51-4adc-8086-2ba730e8edb8" eventImplId="ad6807da-2e96-4d66-883c-dad7e6307041">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="8b8225b5-2063-4aa8-862f-9d7bbb9db11b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="327" y="196" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c0bb24cf-0de7-4032-8fc4-4e659ba766d3</ns16:incoming>
                        
                        
                        <ns16:incoming>bdde03b3-0f3c-4252-8dbc-813e0e83a658</ns16:incoming>
                        
                        
                        <ns16:outgoing>82aa9143-0b38-4b0e-8c67-7dcb942d9fe6</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="73905cfb-5cdb-4592-8a25-880aa45309a5" targetRef="8b8225b5-2063-4aa8-862f-9d7bbb9db11b" name="To Catch Errors" id="c0bb24cf-0de7-4032-8fc4-4e659ba766d3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c5101f4c-3b8b-48d3-8a98-f188b024f962" targetRef="8b8225b5-2063-4aa8-862f-9d7bbb9db11b" name="To Catch Errors" id="bdde03b3-0f3c-4252-8dbc-813e0e83a658">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8b8225b5-2063-4aa8-862f-9d7bbb9db11b" targetRef="4aa3d500-538e-4737-a425-1e9a2382ef2e" name="To End" id="82aa9143-0b38-4b0e-8c67-7dcb942d9fe6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.e732cec5-e392-4534-850b-fb2381ae79ee" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.82aa9143-0b38-4b0e-8c67-7dcb942d9fe6</processLinkId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4aa3d500-538e-4737-a425-1e9a2382ef2e</toProcessItemId>
            <guid>0c89469b-eb0d-486e-97d9-d39610dcd9ff</guid>
            <versionId>353273f9-a3e5-4f50-bea7-bfa2e6d8bfa6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.8b8225b5-2063-4aa8-862f-9d7bbb9db11b</fromProcessItemId>
            <toProcessItemId>2025.4aa3d500-538e-4737-a425-1e9a2382ef2e</toProcessItemId>
        </link>
        <link name="To Send Email">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.89b283a7-f7d8-43a7-8cb2-062229361c8d</processLinkId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.24b92796-3d2c-4b43-a02b-2718fc5e2755</toProcessItemId>
            <guid>da42df96-18b9-4724-8e99-edf37fad5beb</guid>
            <versionId>621fe722-46b3-4b0d-8bb5-30521f553bae</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a5275662-4e32-4e05-8d6c-83575a3983c1</fromProcessItemId>
            <toProcessItemId>2025.24b92796-3d2c-4b43-a02b-2718fc5e2755</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a2200d8e-0add-477c-8ef5-df2cdd132efe</processLinkId>
            <processId>1.97607171-1a4e-436c-8b92-7a6920219722</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.24b92796-3d2c-4b43-a02b-2718fc5e2755</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</endStateId>
            <toProcessItemId>2025.4aa3d500-538e-4737-a425-1e9a2382ef2e</toProcessItemId>
            <guid>2f086d67-6a9a-497e-8bb5-ae6e8d54de5e</guid>
            <versionId>9db0ed39-0122-442c-822e-b73a46c4e67b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.24b92796-3d2c-4b43-a02b-2718fc5e2755</fromProcessItemId>
            <toProcessItemId>2025.4aa3d500-538e-4737-a425-1e9a2382ef2e</toProcessItemId>
        </link>
    </process>
</teamworks>

