<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058" name="Create CIF Folder">
        <lastModified>1692505286971</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b8</guid>
        <versionId>44205fb7-bc66-494c-aa1f-cfd44732b820</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d71" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":163,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"4e5cad75-42a8-4400-8676-a9359374928f"},{"incoming":["126f04d7-661d-4f3c-8760-a7589d3dadde","49bfec97-68d5-4fe2-806f-236256927d5c","8eaf5474-f4e1-4a43-837f-2152e975f39d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":687,"y":163,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"bd613f70-393c-482e-831e-496e6d1764ce"},{"targetRef":"3a4cb149-2e32-4dd2-847b-40a9835e8d83","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9","sourceRef":"4e5cad75-42a8-4400-8676-a9359374928f"},{"outgoing":["0c95b955-6c8d-4898-84d3-7849abb15b07"],"incoming":["2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9"],"matchAllSearchCriteria":true,"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Get Customer Folder If Exists : END\");"],"nodeVisualInfo":[{"width":95,"x":177,"y":140,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Get Customer Folder If Exists : START\");\r\n"],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Customer Folder If Exists","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FILENET_ROOT_PATH+\"\/\"+tw.local.CIF"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"3a4cb149-2e32-4dd2-847b-40a9835e8d83","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.customerFolder"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"parallelMultiple":false,"outgoing":["3f468580-e7fb-42b0-81ed-8212d13085ce"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"cc0c43c5-d81e-4dc4-a9c7-7cfcb4702630"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"68368cfe-8764-4ad8-85e8-e5357c65dd1d","otherAttributes":{"eventImplId":"1cc4e42a-34f9-4ea8-8fea-242f5c0276ae"}}],"attachedToRef":"3a4cb149-2e32-4dd2-847b-40a9835e8d83","extensionElements":{"nodeVisualInfo":[{"width":24,"x":260,"y":163,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error5","declaredType":"boundaryEvent","id":"675fb609-bf3f-4308-8971-7982da3832f8","outputSet":{}},{"outgoing":["126f04d7-661d-4f3c-8760-a7589d3dadde"],"incoming":["f10ad8a1-8091-4ee3-8fd9-f21c80f818ff"],"matchAllSearchCriteria":true,"extensionElements":{"postAssignmentScript":["java.lang.Thread.sleep(\"2000\");\r\nlog.info(\" ServiceName : Create Customer Folder : END\");"],"nodeVisualInfo":[{"width":95,"x":517,"y":140,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Create Customer Folder : START\");"],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_CREATE_FOLDER","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Create Customer Folder","dataInputAssociation":[{"targetRef":"NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.CIF"]}}]},{"targetRef":"PROPERTIES","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9","declaredType":"TFormalExpression","content":["tw.local.properties"]}}]},{"targetRef":"PARENT_FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FILENET_ROOT_PATH"]}}]},{"targetRef":"OBJECT_TYPE_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\"\"+tw.epv.ECMProperties.customerFolder"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"3dcf241e-c319-4c48-82ba-373857341bed","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}],"sourceRef":["FOLDER_ID"]}],"orderOverride":false},{"startQuantity":1,"outgoing":["f10ad8a1-8091-4ee3-8fd9-f21c80f818ff"],"incoming":["3f468580-e7fb-42b0-81ed-8212d13085ce"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":140,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Customer Folder Properties","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"5c4c64d3-fc0d-41ee-81c2-7a03006796bb","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.properties = new tw.object.listOf.ECMProperty();\r\n\r\nfunction addProp(typeID , value)\r\n{\r\n\tvar porp = new tw.object.ECMProperty();\r\n\tporp.objectTypeId = typeID;\r\n\tporp.value = value;\r\n\ttw.local.properties.insertIntoList(tw.local.properties.listLength, porp);\r\n}\r\n\r\naddProp(\"\"+tw.epv.ECMProperties.arabicName , tw.local.customerName);\r\naddProp(\"\"+tw.epv.ECMProperties.customerCIF , tw.local.CIF);\r\naddProp(\"\"+tw.epv.ECMProperties.branchCode , tw.local.branchCode);\r\naddProp(\"\"+tw.epv.ECMProperties.customerType , \"\"+tw.epv.ECMProperties.Corporate);\r\n\r\n"]}},{"targetRef":"3dcf241e-c319-4c48-82ba-373857341bed","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Customer Folder","declaredType":"sequenceFlow","id":"f10ad8a1-8091-4ee3-8fd9-f21c80f818ff","sourceRef":"5c4c64d3-fc0d-41ee-81c2-7a03006796bb"},{"targetRef":"5c4c64d3-fc0d-41ee-81c2-7a03006796bb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Customer Folder Properties","declaredType":"sequenceFlow","id":"3f468580-e7fb-42b0-81ed-8212d13085ce","sourceRef":"675fb609-bf3f-4308-8971-7982da3832f8"},{"targetRef":"bd613f70-393c-482e-831e-496e6d1764ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"126f04d7-661d-4f3c-8760-a7589d3dadde","sourceRef":"3dcf241e-c319-4c48-82ba-373857341bed"},{"targetRef":"7a0e647d-5643-45be-8268-5ffcd060d8a8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To set folder id","declaredType":"sequenceFlow","id":"0c95b955-6c8d-4898-84d3-7849abb15b07","sourceRef":"3a4cb149-2e32-4dd2-847b-40a9835e8d83"},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"customerFolder","isCollection":false,"declaredType":"dataObject","id":"2056.bef4939a-88b7-4220-8920-711eba537317"},{"itemSubjectRef":"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9","name":"properties","isCollection":true,"declaredType":"dataObject","id":"2056.f246a114-09f5-4f7a-8237-dc73d260ca33"},{"startQuantity":1,"outgoing":["49bfec97-68d5-4fe2-806f-236256927d5c"],"incoming":["0c95b955-6c8d-4898-84d3-7849abb15b07"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":410,"y":45,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set folder id","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7a0e647d-5643-45be-8268-5ffcd060d8a8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.folderId = tw.local.customerFolder.objectId;"]}},{"targetRef":"bd613f70-393c-482e-831e-496e6d1764ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"49bfec97-68d5-4fe2-806f-236256927d5c","sourceRef":"7a0e647d-5643-45be-8268-5ffcd060d8a8"},{"parallelMultiple":false,"outgoing":["4280dfd3-184a-48ab-871f-60bb253a4074"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"b4407007-03b8-4f08-8389-fdc8202010e0"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a0efac75-5285-478e-8875-3d34f9a01786","otherAttributes":{"eventImplId":"ca3526b7-54bb-4ace-808e-f65cf0ca0e29"}}],"attachedToRef":"5c4c64d3-fc0d-41ee-81c2-7a03006796bb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":198,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1e7a16bc-0850-41cd-8450-a7243387c37f","outputSet":{}},{"parallelMultiple":false,"outgoing":["ec8eb19d-8b63-4685-82c9-0eea3602035f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a017813c-dfd5-489f-8eb3-ece228076bf2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"bbe7f0d4-4d13-4f62-84eb-0504b60c431f","otherAttributes":{"eventImplId":"c8832a14-5137-445f-8748-ee1dcdf9cc17"}}],"attachedToRef":"7a0e647d-5643-45be-8268-5ffcd060d8a8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":445,"y":103,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"e11a2777-5fdb-4ee6-8e31-6d61306212c3","outputSet":{}},{"parallelMultiple":false,"outgoing":["bead2534-2689-4f5c-8f5f-58ee5b8db324"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"9a6962bb-ce44-4e46-8812-0a7847e90fc3"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8b2ba449-8ffc-4ef0-883a-8b99e2f1ef4a","otherAttributes":{"eventImplId":"82198551-688a-4410-82a3-94b03aa2a7c1"}}],"attachedToRef":"3dcf241e-c319-4c48-82ba-373857341bed","extensionElements":{"nodeVisualInfo":[{"width":24,"x":552,"y":198,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"da9046a5-9551-40ea-80a4-224d77225c8c","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c076aebb-70e2-480d-8f36-2cfffe9e2c82"},{"startQuantity":1,"outgoing":["8eaf5474-f4e1-4a43-837f-2152e975f39d"],"incoming":["4280dfd3-184a-48ab-871f-60bb253a4074","ec8eb19d-8b63-4685-82c9-0eea3602035f","bead2534-2689-4f5c-8f5f-58ee5b8db324"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":427,"y":266,"declaredType":"TNodeVisualInfo","height":69}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2f08deeb-873d-4c43-81e9-75d39c5ddab5","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"2f08deeb-873d-4c43-81e9-75d39c5ddab5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"4280dfd3-184a-48ab-871f-60bb253a4074","sourceRef":"1e7a16bc-0850-41cd-8450-a7243387c37f"},{"targetRef":"2f08deeb-873d-4c43-81e9-75d39c5ddab5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"ec8eb19d-8b63-4685-82c9-0eea3602035f","sourceRef":"e11a2777-5fdb-4ee6-8e31-6d61306212c3"},{"targetRef":"2f08deeb-873d-4c43-81e9-75d39c5ddab5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"bead2534-2689-4f5c-8f5f-58ee5b8db324","sourceRef":"da9046a5-9551-40ea-80a4-224d77225c8c"},{"targetRef":"bd613f70-393c-482e-831e-496e6d1764ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8eaf5474-f4e1-4a43-837f-2152e975f39d","sourceRef":"2f08deeb-873d-4c43-81e9-75d39c5ddab5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.495d7778-597d-4497-8be7-9e1752ae6220"}],"laneSet":[{"id":"63e17a8c-c17f-462c-8bd7-1bb2b95e0607","lane":[{"flowNodeRef":["4e5cad75-42a8-4400-8676-a9359374928f","bd613f70-393c-482e-831e-496e6d1764ce","3a4cb149-2e32-4dd2-847b-40a9835e8d83","675fb609-bf3f-4308-8971-7982da3832f8","3dcf241e-c319-4c48-82ba-373857341bed","5c4c64d3-fc0d-41ee-81c2-7a03006796bb","7a0e647d-5643-45be-8268-5ffcd060d8a8","1e7a16bc-0850-41cd-8450-a7243387c37f","e11a2777-5fdb-4ee6-8e31-6d61306212c3","da9046a5-9551-40ea-80a4-224d77225c8c","2f08deeb-873d-4c43-81e9-75d39c5ddab5"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":356}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"60bf3b44-7c91-4d4a-885e-5e7f0cd65130","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create CIF Folder","declaredType":"process","id":"1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.24091ff7-f2c4-4cd3-8703-3723202bc92b"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.c7b9680f-d22f-48b4-8325-2716ee46bef1","epvProcessLinkId":"90a4cbdd-3f39-4544-8689-c5d2450d2279","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.71adeef9-e810-4f45-8463-2e3b6390ee1f","2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa","2055.561bfa1f-a99e-4008-8288-671ae7cfca79","2055.736854dc-22ba-47b3-802a-e3a3b05ad425"]}],"outputSet":[{"dataOutputRefs":["2055.24091ff7-f2c4-4cd3-8703-3723202bc92b","2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"00000000\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"CIF","isCollection":false,"id":"2055.71adeef9-e810-4f45-8463-2e3b6390ee1f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"samir\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerName","isCollection":false,"id":"2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"001\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.561bfa1f-a99e-4008-8288-671ae7cfca79"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"I\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerType","isCollection":false,"id":"2055.736854dc-22ba-47b3-802a-e3a3b05ad425"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.71adeef9-e810-4f45-8463-2e3b6390ee1f</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>60fed666-4ea7-464b-a510-af615978d5c2</guid>
            <versionId>54a6c8ee-eef7-4267-8461-ff94a8790426</versionId>
        </processParameter>
        <processParameter name="customerName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bfcfc5be-feb6-4cd7-916b-b6c7b47e7d22</guid>
            <versionId>8fd31f43-bd33-412e-bdc4-852b287e4bc4</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.561bfa1f-a99e-4008-8288-671ae7cfca79</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4b12d6c8-ce92-4fed-9f42-680326746a06</guid>
            <versionId>e2768a5f-86b6-46e5-924e-ce96feecb912</versionId>
        </processParameter>
        <processParameter name="customerType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.736854dc-22ba-47b3-802a-e3a3b05ad425</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cbaabd7c-f652-4587-ba1b-e487d9359ae9</guid>
            <versionId>568d0141-8231-40d2-82b1-ebde10e32a90</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.24091ff7-f2c4-4cd3-8703-3723202bc92b</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>920773bf-7245-4ab7-acaf-86d6cf9de1ce</guid>
            <versionId>c8070adf-5782-4d9d-af5e-15cad7249187</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4c014561-4efc-40ac-bde4-c12b8ec6cbc6</guid>
            <versionId>3601ec78-d378-405a-a433-e5956aeefa4b</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.16ca8166-34a1-4008-94ca-d3b867a44f24</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>27</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5fd60757-cc95-4902-b22b-e071692d4be9</guid>
            <versionId>8862bad9-9cde-4afc-abab-3dbfadd64d5b</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.570ba481-d4da-4fe2-bb4d-c3b277c8d876</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>28</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e7882b37-abd7-456b-bedf-6ed8bdf20fa3</guid>
            <versionId>4524f4f6-2406-4fad-ac96-57416ecead26</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1716d76d-8934-42da-8f23-7aa29a046003</processParameterId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>29</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2b0e34f5-b67c-4c6d-992c-aed049cd5edc</guid>
            <versionId>e6f79c7d-f78f-4658-b427-8d6b3cc407b1</versionId>
        </processParameter>
        <processVariable name="customerFolder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bef4939a-88b7-4220-8920-711eba537317</processVariableId>
            <description isNull="true" />
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b3e7f09b-e47a-44db-bc65-0417352ba748</guid>
            <versionId>6ec99e63-c310-4ded-829a-f1ee2ed26359</versionId>
        </processVariable>
        <processVariable name="properties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f246a114-09f5-4f7a-8237-dc73d260ca33</processVariableId>
            <description isNull="true" />
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.a4275847-1a37-4d3c-9289-0462d5afbca9</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf68b215-791c-4b37-bec1-2f8cedf8b289</guid>
            <versionId>e5aace5c-a93c-42c8-8f84-59a1bcb67396</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c076aebb-70e2-480d-8f36-2cfffe9e2c82</processVariableId>
            <description isNull="true" />
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>09a83ee1-b7c4-418e-83a4-3c104bf8c661</guid>
            <versionId>3f61726b-fae3-40dd-8c9e-c348492be355</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.495d7778-597d-4497-8be7-9e1752ae6220</processVariableId>
            <description isNull="true" />
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fac6a949-2884-4833-b71c-af8989e53b47</guid>
            <versionId>d810e953-84b5-411f-8c1a-64e95fda19e4</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>Create Customer Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.ec956fc4-561d-47f0-b3e4-fdfb5fd19d95</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-2561</guid>
            <versionId>26b63948-661c-4da0-906b-90489c9315f7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.013f5010-9e88-41d9-9d90-b10300ebd1d6</processItemPrePostId>
                <processItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</processItemId>
                <location>2</location>
                <script>java.lang.Thread.sleep("2000");&#xD;
log.info(" ServiceName : Create Customer Folder : END");</script>
                <guid>7f136e0e-951b-4add-a773-fcdfd489d52c</guid>
                <versionId>5f3a49dc-f6a2-49b6-aaa1-9aa2c87a67b6</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.6c1ef222-1593-4804-a47d-e00f535c08a1</processItemPrePostId>
                <processItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Create Customer Folder : START");</script>
                <guid>34e6c0c0-1a92-4c0b-97c4-35527ca5fb28</guid>
                <versionId>be22d3cc-084c-4999-a335-58a8d22e4367</versionId>
            </processPrePosts>
            <layoutData x="517" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d70</errorHandlerItem>
                <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.ec956fc4-561d-47f0-b3e4-fdfb5fd19d95</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;objectTypeId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;""+tw.epv.ECMProperties.customerFolder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;parentFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FILENET_ROOT_PATH&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;name&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.CIF&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;properties&lt;/name&gt;&#xD;
      &lt;type&gt;ECMProperty&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.properties&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderId&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_CREATE_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.570ba481-d4da-4fe2-bb4d-c3b277c8d876&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>418ec2c5-8fdc-4519-9007-d1b853e493ca</guid>
                <versionId>ac9f8d89-da19-46bc-92c0-df699522a6f9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5c4c64d3-fc0d-41ee-81c2-7a03006796bb</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>Set Customer Folder Properties</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b7138e07-2779-496b-81d9-4d9084d35e95</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-2560</guid>
            <versionId>5c24b61b-36ff-4d3a-bee6-0a50e3e26127</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d70</errorHandlerItem>
                <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b7138e07-2779-496b-81d9-4d9084d35e95</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.properties = new tw.object.listOf.ECMProperty();&#xD;
&#xD;
function addProp(typeID , value)&#xD;
{&#xD;
	var porp = new tw.object.ECMProperty();&#xD;
	porp.objectTypeId = typeID;&#xD;
	porp.value = value;&#xD;
	tw.local.properties.insertIntoList(tw.local.properties.listLength, porp);&#xD;
}&#xD;
&#xD;
addProp(""+tw.epv.ECMProperties.arabicName , tw.local.customerName);&#xD;
addProp(""+tw.epv.ECMProperties.customerCIF , tw.local.CIF);&#xD;
addProp(""+tw.epv.ECMProperties.branchCode , tw.local.branchCode);&#xD;
addProp(""+tw.epv.ECMProperties.customerType , ""+tw.epv.ECMProperties.Corporate);&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>15be5975-e21e-47a9-a619-70760c1404d6</guid>
                <versionId>028d9027-19b1-41e4-8fa8-c6764eb97658</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7a0e647d-5643-45be-8268-5ffcd060d8a8</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>set folder id</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.fdbcfeef-ce40-47cb-a393-67d0d782b168</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-17bd</guid>
            <versionId>699613fa-5837-48a6-a374-e6de59f76d2d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="410" y="45">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d70</errorHandlerItem>
                <errorHandlerItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.fdbcfeef-ce40-47cb-a393-67d0d782b168</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.folderId = tw.local.customerFolder.objectId;</script>
                <isRule>false</isRule>
                <guid>f6d5c1b1-b57d-4619-9df1-b43d5d5fb427</guid>
                <versionId>3314ee15-6b40-4820-a547-1a4e7dc62fc1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>Get Customer Folder If Exists</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.8880cecd-cf67-4baf-ac47-15182b524f68</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.5c4c64d3-fc0d-41ee-81c2-7a03006796bb</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-2562</guid>
            <versionId>a5aa2d96-b652-4e02-8173-f4b4caca534c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.45db08fd-861d-42ca-89f6-e77e28a32b9f</processItemPrePostId>
                <processItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Get Customer Folder If Exists : START");&#xD;
</script>
                <guid>040df6bd-cec3-45f8-8fee-9cc803cac33e</guid>
                <versionId>295a1200-da07-4b1c-854f-e5d23aa113a9</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.94732faa-f1f7-42d7-bc2a-8265babb14e1</processItemPrePostId>
                <processItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Get Customer Folder If Exists : END");</script>
                <guid>6f8844cd-e2b9-41a6-a0b1-3f50984b75b5</guid>
                <versionId>b5305e76-45f0-48e3-b202-8fb76030a495</versionId>
            </processPrePosts>
            <layoutData x="177" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>rightCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189daab98ba:-2560</errorHandlerItem>
                <errorHandlerItemId>2025.5c4c64d3-fc0d-41ee-81c2-7a03006796bb</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.8880cecd-cf67-4baf-ac47-15182b524f68</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FILENET_ROOT_PATH+"/"+tw.local.CIF&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.customerFolder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.1716d76d-8934-42da-8f23-7aa29a046003&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>cc2f8487-aaf0-4c1a-99a2-2c2b038c4e50</guid>
                <versionId>d4cc58ce-9e6c-465b-9867-7a2209e2fa1a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.bafa9cf9-db4b-416a-90e3-83ef4244da90</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6</guid>
            <versionId>bce9760a-61b2-4314-a84b-************</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="687" y="163">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.bafa9cf9-db4b-416a-90e3-83ef4244da90</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ec47065e-6e6a-42cd-948b-b932fd701f92</guid>
                <versionId>796246d1-e26b-4455-ac72-e47f5343e791</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</processItemId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ee6ae603-af76-4e51-949b-973f3bb0a46a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d70</guid>
            <versionId>e92b196f-dd23-4197-887b-2fe556b41bd2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="427" y="266">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ee6ae603-af76-4e51-949b-973f3bb0a46a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>456e40f2-5b45-411b-9a9f-730b7426cdce</guid>
                <versionId>4b5f84e5-1140-42c4-820f-30c55b2ab32d</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.e73bbc69-a667-4888-92f5-c1087379acd1</epvProcessLinkId>
            <epvId>/21.c7b9680f-d22f-48b4-8325-2716ee46bef1</epvId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <guid>3e55374b-3c80-499e-af01-86e8974b946f</guid>
            <versionId>e2cf44f1-0b58-4ca8-82af-9b90f7c54c63</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="163">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create CIF Folder" id="1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.c7b9680f-d22f-48b4-8325-2716ee46bef1" epvProcessLinkId="90a4cbdd-3f39-4544-8689-c5d2450d2279" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.71adeef9-e810-4f45-8463-2e3b6390ee1f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"00000000"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="customerName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"samir"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.561bfa1f-a99e-4008-8288-671ae7cfca79">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"001"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="customerType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.736854dc-22ba-47b3-802a-e3a3b05ad425">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"I"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.24091ff7-f2c4-4cd3-8703-3723202bc92b" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.71adeef9-e810-4f45-8463-2e3b6390ee1f</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.561bfa1f-a99e-4008-8288-671ae7cfca79</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.736854dc-22ba-47b3-802a-e3a3b05ad425</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.24091ff7-f2c4-4cd3-8703-3723202bc92b</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="63e17a8c-c17f-462c-8bd7-1bb2b95e0607">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="60bf3b44-7c91-4d4a-885e-5e7f0cd65130" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="356" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>4e5cad75-42a8-4400-8676-a9359374928f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bd613f70-393c-482e-831e-496e6d1764ce</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3a4cb149-2e32-4dd2-847b-40a9835e8d83</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>675fb609-bf3f-4308-8971-7982da3832f8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3dcf241e-c319-4c48-82ba-373857341bed</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5c4c64d3-fc0d-41ee-81c2-7a03006796bb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7a0e647d-5643-45be-8268-5ffcd060d8a8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1e7a16bc-0850-41cd-8450-a7243387c37f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e11a2777-5fdb-4ee6-8e31-6d61306212c3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>da9046a5-9551-40ea-80a4-224d77225c8c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2f08deeb-873d-4c43-81e9-75d39c5ddab5</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="4e5cad75-42a8-4400-8676-a9359374928f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="163" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="bd613f70-393c-482e-831e-496e6d1764ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="687" y="163" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>126f04d7-661d-4f3c-8760-a7589d3dadde</ns16:incoming>
                        
                        
                        <ns16:incoming>49bfec97-68d5-4fe2-806f-236256927d5c</ns16:incoming>
                        
                        
                        <ns16:incoming>8eaf5474-f4e1-4a43-837f-2152e975f39d</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="4e5cad75-42a8-4400-8676-a9359374928f" targetRef="3a4cb149-2e32-4dd2-847b-40a9835e8d83" name="To End" id="2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="FileNet" useMappedVariable="true" matchAllSearchCriteria="true" orderOverride="false" implementation="##WebService" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Customer Folder If Exists" id="3a4cb149-2e32-4dd2-847b-40a9835e8d83">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="140" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Get Customer Folder If Exists : END");</ns3:postAssignmentScript>
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Get Customer Folder If Exists : START");&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.e9b29d6f-2281-454c-8d07-cacce33eb2d9</ns16:incoming>
                        
                        
                        <ns16:outgoing>0c95b955-6c8d-4898-84d3-7849abb15b07</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FILENET_ROOT_PATH+"/"+tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.customerFolder</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3a4cb149-2e32-4dd2-847b-40a9835e8d83" parallelMultiple="false" name="Error5" id="675fb609-bf3f-4308-8971-7982da3832f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="260" y="163" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>3f468580-e7fb-42b0-81ed-8212d13085ce</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="cc0c43c5-d81e-4dc4-a9c7-7cfcb4702630" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="68368cfe-8764-4ad8-85e8-e5357c65dd1d" eventImplId="1cc4e42a-34f9-4ea8-8fea-242f5c0276ae">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns4:contentTask serverName="FileNet" useMappedVariable="true" matchAllSearchCriteria="true" orderOverride="false" implementation="##WebService" operationRef="FOLDER_OP_CREATE_FOLDER" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Create Customer Folder" id="3dcf241e-c319-4c48-82ba-373857341bed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="517" y="140" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript>java.lang.Thread.sleep("2000");&#xD;
log.info(" ServiceName : Create Customer Folder : END");</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Create Customer Folder : START");</ns3:preAssignmentScript>
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f10ad8a1-8091-4ee3-8fd9-f21c80f818ff</ns16:incoming>
                        
                        
                        <ns16:outgoing>126f04d7-661d-4f3c-8760-a7589d3dadde</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PROPERTIES</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9">tw.local.properties</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PARENT_FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FILENET_ROOT_PATH</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>OBJECT_TYPE_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">""+tw.epv.ECMProperties.customerFolder</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER_ID</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Set Customer Folder Properties" id="5c4c64d3-fc0d-41ee-81c2-7a03006796bb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="140" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3f468580-e7fb-42b0-81ed-8212d13085ce</ns16:incoming>
                        
                        
                        <ns16:outgoing>f10ad8a1-8091-4ee3-8fd9-f21c80f818ff</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.properties = new tw.object.listOf.ECMProperty();&#xD;
&#xD;
function addProp(typeID , value)&#xD;
{&#xD;
	var porp = new tw.object.ECMProperty();&#xD;
	porp.objectTypeId = typeID;&#xD;
	porp.value = value;&#xD;
	tw.local.properties.insertIntoList(tw.local.properties.listLength, porp);&#xD;
}&#xD;
&#xD;
addProp(""+tw.epv.ECMProperties.arabicName , tw.local.customerName);&#xD;
addProp(""+tw.epv.ECMProperties.customerCIF , tw.local.CIF);&#xD;
addProp(""+tw.epv.ECMProperties.branchCode , tw.local.branchCode);&#xD;
addProp(""+tw.epv.ECMProperties.customerType , ""+tw.epv.ECMProperties.Corporate);&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="5c4c64d3-fc0d-41ee-81c2-7a03006796bb" targetRef="3dcf241e-c319-4c48-82ba-373857341bed" name="To Create Customer Folder" id="f10ad8a1-8091-4ee3-8fd9-f21c80f818ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="675fb609-bf3f-4308-8971-7982da3832f8" targetRef="5c4c64d3-fc0d-41ee-81c2-7a03006796bb" name="To Set Customer Folder Properties" id="3f468580-e7fb-42b0-81ed-8212d13085ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3dcf241e-c319-4c48-82ba-373857341bed" targetRef="bd613f70-393c-482e-831e-496e6d1764ce" name="To End" id="126f04d7-661d-4f3c-8760-a7589d3dadde">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3a4cb149-2e32-4dd2-847b-40a9835e8d83" targetRef="7a0e647d-5643-45be-8268-5ffcd060d8a8" name="To set folder id" id="0c95b955-6c8d-4898-84d3-7849abb15b07">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="customerFolder" id="2056.bef4939a-88b7-4220-8920-711eba537317" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9" isCollection="true" name="properties" id="2056.f246a114-09f5-4f7a-8237-dc73d260ca33" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="set folder id" id="7a0e647d-5643-45be-8268-5ffcd060d8a8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="410" y="45" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0c95b955-6c8d-4898-84d3-7849abb15b07</ns16:incoming>
                        
                        
                        <ns16:outgoing>49bfec97-68d5-4fe2-806f-236256927d5c</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.folderId = tw.local.customerFolder.objectId;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7a0e647d-5643-45be-8268-5ffcd060d8a8" targetRef="bd613f70-393c-482e-831e-496e6d1764ce" name="To End" id="49bfec97-68d5-4fe2-806f-236256927d5c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="5c4c64d3-fc0d-41ee-81c2-7a03006796bb" parallelMultiple="false" name="Error" id="1e7a16bc-0850-41cd-8450-a7243387c37f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="198" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4280dfd3-184a-48ab-871f-60bb253a4074</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="b4407007-03b8-4f08-8389-fdc8202010e0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a0efac75-5285-478e-8875-3d34f9a01786" eventImplId="ca3526b7-54bb-4ace-808e-f65cf0ca0e29">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7a0e647d-5643-45be-8268-5ffcd060d8a8" parallelMultiple="false" name="Error1" id="e11a2777-5fdb-4ee6-8e31-6d61306212c3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="445" y="103" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ec8eb19d-8b63-4685-82c9-0eea3602035f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a017813c-dfd5-489f-8eb3-ece228076bf2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="bbe7f0d4-4d13-4f62-84eb-0504b60c431f" eventImplId="c8832a14-5137-445f-8748-ee1dcdf9cc17">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3dcf241e-c319-4c48-82ba-373857341bed" parallelMultiple="false" name="Error2" id="da9046a5-9551-40ea-80a4-224d77225c8c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="552" y="198" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>bead2534-2689-4f5c-8f5f-58ee5b8db324</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9a6962bb-ce44-4e46-8812-0a7847e90fc3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8b2ba449-8ffc-4ef0-883a-8b99e2f1ef4a" eventImplId="82198551-688a-4410-82a3-94b03aa2a7c1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c076aebb-70e2-480d-8f36-2cfffe9e2c82" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="2f08deeb-873d-4c43-81e9-75d39c5ddab5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="427" y="266" width="95" height="69" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4280dfd3-184a-48ab-871f-60bb253a4074</ns16:incoming>
                        
                        
                        <ns16:incoming>ec8eb19d-8b63-4685-82c9-0eea3602035f</ns16:incoming>
                        
                        
                        <ns16:incoming>bead2534-2689-4f5c-8f5f-58ee5b8db324</ns16:incoming>
                        
                        
                        <ns16:outgoing>8eaf5474-f4e1-4a43-837f-2152e975f39d</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1e7a16bc-0850-41cd-8450-a7243387c37f" targetRef="2f08deeb-873d-4c43-81e9-75d39c5ddab5" name="To Catch Errors" id="4280dfd3-184a-48ab-871f-60bb253a4074">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e11a2777-5fdb-4ee6-8e31-6d61306212c3" targetRef="2f08deeb-873d-4c43-81e9-75d39c5ddab5" name="To Catch Errors" id="ec8eb19d-8b63-4685-82c9-0eea3602035f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="da9046a5-9551-40ea-80a4-224d77225c8c" targetRef="2f08deeb-873d-4c43-81e9-75d39c5ddab5" name="To Catch Errors" id="bead2534-2689-4f5c-8f5f-58ee5b8db324">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="2f08deeb-873d-4c43-81e9-75d39c5ddab5" targetRef="bd613f70-393c-482e-831e-496e6d1764ce" name="To End" id="8eaf5474-f4e1-4a43-837f-2152e975f39d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.495d7778-597d-4497-8be7-9e1752ae6220" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.49bfec97-68d5-4fe2-806f-236256927d5c</processLinkId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7a0e647d-5643-45be-8268-5ffcd060d8a8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
            <guid>2bdd5ad3-f4c3-402d-8468-91e2a1e0b369</guid>
            <versionId>3b40d1d1-9bf3-4d86-b216-45e5edc04a29</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.7a0e647d-5643-45be-8268-5ffcd060d8a8</fromProcessItemId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.126f04d7-661d-4f3c-8760-a7589d3dadde</processLinkId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
            <guid>4d6746ff-7ebf-453a-b1ec-c0a123dcc1cf</guid>
            <versionId>c4fd9f30-8615-4ea4-a74e-ca3861c663f2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</fromProcessItemId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8eaf5474-f4e1-4a43-837f-2152e975f39d</processLinkId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
            <guid>bbe100c6-9e57-465e-9c75-d55fd02ed6bb</guid>
            <versionId>c6f22983-cfb8-40e5-97f9-6a5a4c49a073</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.2f08deeb-873d-4c43-81e9-75d39c5ddab5</fromProcessItemId>
            <toProcessItemId>2025.bd613f70-393c-482e-831e-496e6d1764ce</toProcessItemId>
        </link>
        <link name="To Create Customer Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f10ad8a1-8091-4ee3-8fd9-f21c80f818ff</processLinkId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5c4c64d3-fc0d-41ee-81c2-7a03006796bb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</toProcessItemId>
            <guid>a56c7184-abc5-4d25-96b4-08ef9d28f9a2</guid>
            <versionId>cdf48f56-bf44-4b50-8e58-425514870c83</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5c4c64d3-fc0d-41ee-81c2-7a03006796bb</fromProcessItemId>
            <toProcessItemId>2025.3dcf241e-c319-4c48-82ba-373857341bed</toProcessItemId>
        </link>
        <link name="To set folder id">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0c95b955-6c8d-4898-84d3-7849abb15b07</processLinkId>
            <processId>1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7a0e647d-5643-45be-8268-5ffcd060d8a8</toProcessItemId>
            <guid>959571ce-b740-4917-b705-7613736f3c0a</guid>
            <versionId>ec34ac6e-3773-4468-a0ce-c08abed2fd46</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3a4cb149-2e32-4dd2-847b-40a9835e8d83</fromProcessItemId>
            <toProcessItemId>2025.7a0e647d-5643-45be-8268-5ffcd060d8a8</toProcessItemId>
        </link>
    </process>
</teamworks>

