<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e5318bc6-7232-4e45-a258-184ebb476098" name="IDC test">
        <lastModified>1701959853647</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.14ba508a-9fa4-4051-91ca-5240db85c961</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>b6692d02-0f2b-4d16-b502-9c247479b010</guid>
        <versionId>6532a569-a7f8-46fc-9b2b-4e37e36ae8f5</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7f4c651b-243b-48e4-b102-1bff65046ec1"],"isInterrupting":true,"extensionElements":{"default":["2027.7f4c651b-243b-48e4-b102-1bff65046ec1"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":91,"y":78,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"05293831-1900-447c-9dae-db103ddb11ba"},{"incoming":["2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1372,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"aeb7df2a-3806-47d2-bc75-7ce9686f7779"},{"startQuantity":1,"outgoing":["2027.d345bf85-d5da-423e-9861-13d879a4869a"],"incoming":["2027.c98fc708-f161-41e5-9ec1-9c854f15401a"],"default":"2027.d345bf85-d5da-423e-9861-13d879a4869a","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1055,"y":265,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.selectedAction == tw.epv.Action.returnToTradeFO || tw.local.selectedAction == tw.epv.Action.terminateRequest || tw.local.selectedAction == tw.epv.Action.submitLiquidation) {\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidationReview;\r\n\t\r\n}else if (tw.local.selectedAction == tw.epv.Action.obtainApprovals) {\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidationReview;\t\r\n}\r\n\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;\r\n\r\n"]}},{"outgoing":["2027.2ce1a836-65df-417f-940c-58d90afb3217","2027.1b70557b-d508-4482-9baf-8dd19382b330"],"incoming":["2027.12baadf1-2c96-4552-8ee1-96443743a1a6","2027.8b956d76-afeb-4feb-a446-7203a4c13724","2027.b6798d99-1841-432a-b1f0-350f34b64500","2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":706,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9369a3a6-092d-4631-8298-d6f096b3f12e","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fca3ab11-d501-4b7e-8268-75b742c716cb","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6670edad-7319-40af-8bce-981d7b638e27","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"163a4473-e28c-466c-8319-75ef69363911","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df851c1d-16bb-4a7d-8c27-ca6abe0980a9","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ea68df26-61cc-441c-8d4f-b0ed26741375","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a70bfd8e-84e6-46ba-82ce-15c282da0b89","version":"8550"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"LiquidationVisData","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2d5d4efc-df35-4a26-8ba8-73d37149916b","optionName":"@label","value":"Liquidation Vis"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6a54d401-7bd3-44c1-89e8-74108e72fdd8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"80e7042e-9ac2-4328-8186-39778f11bb68","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.9b679256-e93b-4400-89f2-bd15b0c5578d","binding":"tw.local.liquidationVis","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"26be2624-d910-469a-824c-5c9be158c714","version":"8550"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"5","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6ffeb8df-076f-4bb0-87aa-2a7e486fcc31","optionName":"@label","value":"Contract Liquidation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"92d2d71a-3bf2-43c2-85f0-b34050a29c2b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"669c1a07-69ec-40bf-823e-41daf02220f7","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"48441b53-d1a1-4424-8c5c-a92b726c6b5f","optionName":"isFound","value":"tw.local.isFound"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7eaffe01-9d5f-4f23-8f13-c1107e35838d","optionName":"exchangeCurrency","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"db9ae035-434a-43cd-8cdd-b348e7dca730","optionName":"customerCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"343ad0a5-9918-446f-80ed-7c5bfb93dfc4","optionName":"liquidationVis","value":"tw.local.liquidationVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"619c0ac8-a875-4c25-8e9a-8dce0382150a","optionName":"liqDone","value":"tw.local.liqDone"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ce1db920-f4df-4245-8321-83ec73c26686","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4ef668ab-ef17-49f0-84b8-5f0a18cd21a6","optionName":"accountList","value":"tw.local.accountList[]"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73e51367-001f-4ac3-88d2-345360d107e3","optionName":"@visibility","value":""},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3f6d737-4757-4eec-8ea1-cc14b4ab5c3a","optionName":"@visibility.script"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d18c848d-8ffc-43b9-8578-4296db1b5683","optionName":"isGLFound","value":"tw.local.isGLFound"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7aa38590-247e-4071-8a65-1c290d596ac6","optionName":"accountIndex","value":"tw.local.accountIndex"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d4b97e5-7743-45f8-8f63-0daa870954de","optionName":"tempSettlement","value":"tw.local.tempSettlement[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6fef4b8d-9297-4986-81c1-c739cd570e7c","optionName":"isChecker","value":"tw.local.isChecker"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"baa1605e-68a0-4750-8282-20c16ee44d69","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e70a9421-06c6-46f8-8112-efe1ed9337f4","optionName":"concatExCurrency","value":"tw.local.concatExCurrency"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2769ee7c-ffa4-48da-8924-c6434d288c97","optionName":"sAccountList","value":"tw.local.sAccountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5b1b72d9-5e70-4fd2-83ed-964d5a0e304f","optionName":"accounteeCIF","value":"tw.local.acounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cadf20f5-ecc5-44ee-87e0-2c43893a8ec4","optionName":"caseCIF","value":"tw.local.caseCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5179984d-5586-47dd-8b98-7d3e625e4b58","optionName":"draweeCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d9a642ea-bdd2-44dd-88b9-fe1979f71b59","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7b088c22-f300-4583-818d-0e7313cee6a2","optionName":"accountClassCode","value":"tw.local.accountClassCode"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7cf96743-15d5-4a87-86a6-21bf01a05d45","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ee0e873f-8b53-491c-80a8-edabb09cff16","version":"8550"},{"layoutItemId":"6","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ad49f1d8-cee0-4125-833f-8ca17a09d50f","optionName":"@label","value":"Charges And Commissions "},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"024ee696-6fc9-402f-8329-f03d1c131001","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"********-e702-4718-8f7f-8f741665cb8a","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cdb824a5-54c9-486e-80b2-cf132b3d0cde","optionName":"accountList","value":"tw.local.accountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9bfd6b14-0002-445e-8f2a-5009a5e1b475","optionName":"accountNumberList","value":"tw.local.accountNumList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0eec0356-127f-47dd-8ffa-ef8602ca379e","optionName":"accounteeCIF","value":"tw.local.acounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c80ecc2a-229e-4dd0-8864-2405411e9f17","optionName":"isChecker","value":"tw.local.isChecker"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9d1cff23-b398-4617-8eca-80a097ee40b5","optionName":"tempCommissions","value":"tw.local.tempCommissions[]"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64ec9255-ec7f-4966-86fa-efdcb9086067","optionName":"exCurrency","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c84c72cd-2288-49f2-8282-f870553b014a","optionName":"draweeCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4824a863-8801-4020-8fad-5a7fbf6e0591","optionName":"caseCIF","value":"tw.local.caseCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"877845a9-a757-446a-84ec-2809cd3fb09d","optionName":"isFound","value":"tw.local.isFound"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2707258c-3d99-40fc-80ef-7794cc985c43","optionName":"customerCIF","value":"tw.local.customerCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"56a99118-64ae-4986-8422-2c8c41b922a2","optionName":"isGLFoundC","value":"tw.local.isGLFoundC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"68212ae5-dad9-440b-882f-82d54144b687","optionName":"concatExCurrency","value":"tw.local.concatExCurrency"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"526b9350-4961-4e24-89f7-72e509c98a52","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8cb08ae8-1688-400c-80c3-6b35b2312748","optionName":"accountIndexC","value":"tw.local.accountIndexC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"acf5d79a-1ebd-4897-846e-f32c21ad8236","optionName":"commCIF","value":"tw.local.commCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"469f6445-f77d-4afa-8af6-f8361e988686","optionName":"CommAccountList","value":"tw.local.commAccountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"45b527ac-032e-4dda-81f0-af4e80f690c4","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"aaee2328-3d0a-4616-8b64-4179b9ccaf5f","optionName":"commClassCode","value":"tw.local.commClassCode"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e6ac98d8-af60-4486-845c-8edb3fde0769","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.e45b18e8-a83a-4484-8089-b2ab3c33146a","binding":"tw.local.idcContract.commissionsAndCharges[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"155b5fa7-f0ea-4fee-8040-00abf1723d63","version":"8550"},{"layoutItemId":"7","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0beadb40-0441-4af2-8a09-6eab40ef243d","optionName":"@label","value":"Swift Message Data"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc4fc9ad-03cf-4f1e-8cff-7ee62ff02921","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"38ae9157-26ba-4c63-8b3a-eccae89f9f6a","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9de55a95-7e03-4d16-8fca-2e4404277744","optionName":"selctedBic","value":"tw.local.selectedBIC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2a2b8d92-fa06-47f2-89d4-66b3e028d927","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.122789dd-9d59-4a0d-b507-23e1fe2141c4","binding":"tw.local.idcContract.swiftMessageData","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6f39b0e1-78ad-4587-8d57-219576b38fa6","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"bc33ef1a-9ba2-400b-822a-9e8b248633ba"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df6861da-25bd-4547-8474-609d93878804","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a7ce0700-52ad-4ecd-83ba-3b0d003d8442","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4ce93c7e-e5be-4352-82ba-f1975896afbf","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"79ccee8f-4a23-4446-8e57-799b6e54fbb4","optionName":"eventON_LOAD","value":"if(${LiquidationVisData}.getData() == false){\r\n\tthis.context.element.querySelectorAll(\"li[role = 'tab']\")[5].classList.add(\"hidden\");\r\n\tthis.context.element.querySelectorAll(\"li[role = 'tab']\")[6].classList.add(\"hidden\");\r\n\tthis.context.element.querySelectorAll(\"li[role = 'tab']\")[7].classList.add(\"hidden\");\r\n}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"53fba797-5887-4043-859c-00fd0351ca5c","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"56232432-a0da-41a2-8b21-bd3b59599325","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"2e2f5828-6df9-4236-8697-30acae7b1425"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"902621ab-12e5-4153-8acf-a1b0f9317d62","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4f49dfd9-bb4a-4c1a-8369-722ee96be562","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d3a61fb3-25f8-4e3e-81e4-25cf96596d44","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"93f3fcce-e34f-4ecb-8703-d9afa8050293","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"575b4c8b-4ba6-48c6-85a4-d5920314bd3a","optionName":"buttonName","value":"Submit"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"82eed8a8-4ad1-4996-80fe-bf71f95e6186","optionName":"hasApprovals","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a67c505f-4577-4c34-8569-3b8cda044b74","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6a8c5c27-7b95-4746-83e2-65e5a6d3987d","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2c8c93c4-6816-4450-80aa-7d88d2616275","optionName":"selectedAction","value":"tw.local.selectedAction"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"32b2bd10-11db-4f5e-876d-3e88a68049a0","optionName":"approvalsReadOnly","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a00a2862-d0a3-4cd8-83a0-cfc6fbfdad71","optionName":"hasReturnReason","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6de35cc5-090f-40eb-8f5d-81d71cf82f35","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8baa3fa1-5618-41b4-8e8b-8743c204351f","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e74de34f-4b5c-44f9-8f69-d6c6325aa16c","optionName":"isCAD","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4c64366b-87e4-4d99-86b5-2b5a763f712e","optionName":"successMessage","value":"tw.local.successMessage"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"e13d1921-2c1a-452a-8ef8-e7472b2b38d3","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"IDC Execution Hub Liquidation","isForCompensation":false,"completionQuantity":1,"id":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78"},{"outgoing":["2027.b6798d99-1841-432a-b1f0-350f34b64500"],"incoming":["2027.2ce1a836-65df-417f-940c-58d90afb3217"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.b6798d99-1841-432a-b1f0-350f34b64500"],"nodeVisualInfo":[{"width":24,"x":691,"y":97,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b"},{"targetRef":"aeb7df2a-3806-47d2-bc75-7ce9686f7779","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1","sourceRef":"2025.c509974e-91c4-4716-b581-52df7b962df9"},{"startQuantity":1,"outgoing":["2027.fc15d34d-501f-448a-ad67-7096c60bf136"],"incoming":["2027.d765aa1f-fbcb-4212-ad87-b808df66577a","2027.2010813b-e869-4e0e-85a6-085e4d9b87c6"],"default":"2027.fc15d34d-501f-448a-ad67-7096c60bf136","extensionElements":{"nodeVisualInfo":[{"width":95,"x":411,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\n"]}},{"targetRef":"2025.dbd95394-4a05-4961-a959-2e014f62bd3c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Initialization Script","declaredType":"sequenceFlow","id":"2027.fc15d34d-501f-448a-ad67-7096c60bf136","sourceRef":"2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b"},{"outgoing":["2027.19c28870-24e2-44c0-903e-a0486b19cdb2","2027.d765aa1f-fbcb-4212-ad87-b808df66577a"],"default":"2027.d765aa1f-fbcb-4212-ad87-b808df66577a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":84,"x":155,"y":184,"declaredType":"TNodeVisualInfo","height":66}]},"name":"is Returned From Approvals","declaredType":"exclusiveGateway","id":"2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9"},{"outgoing":["2027.2010813b-e869-4e0e-85a6-085e4d9b87c6"],"incoming":["2027.19c28870-24e2-44c0-903e-a0486b19cdb2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":251,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.2010813b-e869-4e0e-85a6-085e4d9b87c6","name":"Approvals Comments","dataInputAssociation":[{"targetRef":"2055.792bf3d4-e1dc-4b70-afbc-5214492c915c","assignment":[{"from":{"evaluatesToTypeRef":"12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}]},{"targetRef":"2055.59289b25-4be8-4329-b825-d40969b0afee","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.complianceComments"]}}]},{"targetRef":"2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}]},{"targetRef":"2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.7dc18078-74d0-4489-b097-5d157ac8efdb","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}],"sourceRef":["2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.2eefde76-5898-4e8d-87b1-0f434cabef87"]}],"calledElement":"1.421a8257-34a5-45f9-b03f-a68e989d4ab8"},{"targetRef":"2025.7dc18078-74d0-4489-b097-5d157ac8efdb","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.idcRequest.approvals.CAD || tw.local.idcRequest.approvals.compliance || tw.local.idcRequest.approvals.treasury"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of Yes","declaredType":"sequenceFlow","id":"2027.19c28870-24e2-44c0-903e-a0486b19cdb2","sourceRef":"2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9"},{"targetRef":"2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Copy of Start To Coach","declaredType":"sequenceFlow","id":"2027.2010813b-e869-4e0e-85a6-085e4d9b87c6","sourceRef":"2025.7dc18078-74d0-4489-b097-5d157ac8efdb"},{"targetRef":"2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of No","declaredType":"sequenceFlow","id":"2027.d765aa1f-fbcb-4212-ad87-b808df66577a","sourceRef":"2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9"},{"startQuantity":1,"outgoing":["2027.f536401d-8027-46bd-b599-79ea698e4386"],"default":"2027.f536401d-8027-46bd-b599-79ea698e4386","extensionElements":{"mode":["InvokeService"],"postAssignmentScript":["\r\nif (tw.local.errorExist) {\r\n\talert(tw.local.errorMessage);\r\n}"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":706,"y":276,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true],"preAssignmentScript":["tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;"]},"name":"validate accounts and attachment","dataInputAssociation":[{"targetRef":"2055.89fca13d-c79b-4425-8595-e7f39c82983e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"2055.7a53f20a-455a-406a-afef-2953d3af1b32","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","declaredType":"TFormalExpression","content":["tw.local.documentsTypesSelected"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.85bbb779-1d74-43b3-ab72-52e6488707d2","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.exist"]}}],"sourceRef":["2055.a447f38b-3bd6-4fec-be92-03d02c8e6d75"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.errorExist"]}}],"sourceRef":["2055.e4656a2a-4887-444d-8212-c2df402cfe0d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.2b33f4df-f983-4df4-843f-411f47a6c8a8"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.95c78223-e670-4271-9ed1-0c377642c5c5"]}],"calledElement":"1.02d07ad8-f212-4ea3-8b5e-e8457d984314"},{"outgoing":["2027.c98fc708-f161-41e5-9ec1-9c854f15401a","2027.12baadf1-2c96-4552-8ee1-96443743a1a6","2027.d6a4fc27-765f-47a7-8cfb-c60cce983456"],"incoming":["2027.6131b81e-be01-4bd1-9638-1edc0ddd1112","2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6"],"default":"2027.c98fc708-f161-41e5-9ec1-9c854f15401a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":874,"y":235,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9"},{"targetRef":"2025.00c49a15-46c5-4bc4-b730-69bda4aa1258","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Copy of Have Errors","declaredType":"sequenceFlow","id":"2027.f536401d-8027-46bd-b599-79ea698e4386","sourceRef":"2025.85bbb779-1d74-43b3-ab72-52e6488707d2"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.662c9f43-1e4c-4967-aa8a-8866a409c38d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.89788273-c82c-4ed9-86a9-82e2f257dfdf"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.e4ef79d6-ce54-425b-a026-89baa260095d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"exist","isCollection":true,"declaredType":"dataObject","id":"2056.c0d157bb-bd45-41da-bc26-e2618aa3c85c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.3bc21bb5-bd46-4916-a7e8-342e6fa535e2"},{"startQuantity":1,"outgoing":["2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a"],"incoming":["2027.f536401d-8027-46bd-b599-79ea698e4386","2027.1b70557b-d508-4482-9baf-8dd19382b330"],"default":"2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":706,"y":386,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.00c49a15-46c5-4bc4-b730-69bda4aa1258","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\ntw.local.validationMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0;\r\ntw.local.invalidTabs = [];\r\n\r\n\/\/ ====================================================================================================== \/\/\r\n\/*\r\n* =====================\r\n* |\tVALIDATE HERE   |\r\n* =====================\r\n*\/\r\n\/\/------------------------------------------Liquidation[5]--------------------------\r\n\/\/Liquidation - Summary\r\nmandatory(tw.local.idcContract.liquidationSummary.liquidationAmt, \"tw.local.idcContract.liquidationSummary.liquidationAmt\");\r\nmandatory(tw.local.idcContract.liquidationSummary.debitValueDate, \"tw.local.idcContract.liquidationSummary.debitValueDate\");\r\nmandatory(tw.local.idcContract.liquidationSummary.creditValueDate, \"tw.local.idcContract.liquidationSummary.creditValueDate\");\r\n\r\nnotPastDate(tw.local.idcContract.liquidationSummary.debitValueDate, \"tw.local.idcContract.liquidationSummary.debitValueDate\", \"Mandatory and Must be &gt;= system date\", false);\r\nnotPastDate(tw.local.idcContract.liquidationSummary.creditValueDate, \"tw.local.idcContract.liquidationSummary.creditValueDate\", \"Mandatory and Must be &gt;= system date\", false);\r\n\r\nmandatory(tw.local.idcContract.liquidationSummary.debitBasisby, \"tw.local.idcContract.liquidationSummary.debitBasisby\");\r\n\r\n\/\/Liquidation - Debited Account Details\r\nfor (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {\r\n      mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.accountClass\");\r\n      if (tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass == \"Customer Account\") {\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountNumber, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.accountNumber\");\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.ownerAccounts, \"tw.local.idcContract.settlementAccounts[i].debitedAccount.ownerAccounts\")\r\n\r\n      } else if (tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass == \"GL Account\") {\r\n            if (tw.local.idcContract.settlementAccounts[i].debitedAccount.isGLFoundC == false) {\r\n                  addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.GLAccountNumber\", \"This Account Is Not Valid\");\r\n            }\r\n            if (tw.local.idcContract.settlementAccounts[i].debitedAccount.isGLVerifiedC == false) {\r\n                  addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.GLAccountNumber\", \"Please Click the Verify Button\");\r\n            }\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.GLAccountNumber, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.GLAccountNumber\");\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountBranchCode, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.accountBranchCode\");\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountCurrency, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAccount.accountCurrency\");\r\n      }\r\n\r\n      \/\/Liquidation - Debited Amount\r\n      if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage != null || tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage != undefined) {\r\n            if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage &lt;= 0 ||\r\n                  tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage &gt; 100) {\r\n                  addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitPercentage\", \"Mandatory and Must be &gt; 0 and &lt;= 100\")\r\n            }\r\n      }\r\n      if (tw.local.idcContract.liquidationSummary.debitBasisby == \"Percentage\") {\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitPercentage\");\r\n      } else {\r\n            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitedAmtinLiquidationCurrency\");\r\n            if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency &lt;= 0 ||\r\n                  tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency &gt; tw.local.idcContract.liquidationSummary.liquidationAmt) {\r\n                  addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitedAmtinLiquidationCurrency\", \"Mandatory and must be &gt; 0 and &lt;= Liquidation Amount\")\r\n            }\r\n      }\r\n      if (tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate != undefined) {\r\n            validateDecimal(tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate, \"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.negotiatedExchangeRate\", \"Must be Decimal (10,6)\");\r\n      }\r\n      if ((tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinAccountCurrency &gt; tw.local.idcContract.settlementAccounts[i].debitedAccount.accountBalance) &amp;&amp; tw.local.idcContract.settlementAccounts[i].debitedAccount.isOverDraft == false) {\r\n            addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitedAmtinAccountCurrency\", \"ERROR: Must be &lt;= Account Balance\");\r\n      }\r\n}\r\n\r\n\/\/Validate Sum of (Debited Amt in Liquidation Currency)\r\nvar sum = 0.0;\r\nfor (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {\r\n      \/\/\talert(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency);\r\n      sum = tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency + sum;\r\n}\r\n\/\/alert(sum);\r\nif (tw.local.idcContract.liquidationSummary.liquidationAmt &lt; sum) {\r\n      for (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {\r\n            addError(\"tw.local.idcContract.settlementAccounts[\" + i + \"].debitedAmount.debitedAmtinLiquidationCurrency\", \"the sum of this field must be less than or equal Liquidation Amount\", \"the sum of this field must be less than or equal Liquidation Amount\")\r\n      }\r\n}\r\n\r\nvalidateTab(5, \"Contract Liquidation\");\r\n\/\/-----------------------------------Commissions and Charges[6]---------------------------------------------- \r\nfor (var i = 0; i &lt; tw.local.idcContract.commissionsAndCharges.length; i++) {\r\n      if (tw.local.idcContract.commissionsAndCharges[i].chargeAmount &lt; 0) {\r\n            addError(\"tw.local.idcContract.commissionsAndCharges[\" + i + \"].chargeAmount\", \"Must be &gt;= 0\");\r\n      }\r\n      if (tw.local.idcContract.commissionsAndCharges[i].waiver == false &amp;&amp; tw.local.idcContract.commissionsAndCharges[i].chargeAmount &gt; 0) {\r\n            \/\/\tmandatory(tw.local.idcContract.commissionsAndCharges[i].waiver,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].waiver\");\r\n            mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.accountClass\");\r\n\r\n            if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == \"Customer Account\") {\r\n                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountNumber, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.accountNumber\");\r\n                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts, \"tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts\")\r\n\r\n            } else if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == \"GL Account\") {\r\n                  if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLVerifiedC == false) {\r\n                        addError(\"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.GLAccountNumber\", \"Please Click the Verify Button\");\r\n                  }\r\n                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.GLAccountNumber\");\r\n                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountBranchCode, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.accountBranchCode\");\r\n                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountCurrency, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.accountCurrency\");\r\n            }\r\n            if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLFoundC == false) {\r\n                  addError(\"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAccount.GLAccountNumber\", \"Account Not Found\");\r\n            }\r\n            if (tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != undefined) {\r\n                  validateDecimal(tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate, \"tw.local.idcContract.commissionsAndCharges[\" + i + \"].debitedAmount.negotiatedExchangeRate\", \"Must be Decimal (10,6)\");\r\n            }\r\n      }\r\n}\r\nvalidateTab(5, \"Charges And Commissions \");\r\n\/\/-----------------------------------------swift validation[7]--------------------------------------\r\nfunction swiftValidation(field, fieldName) {\r\n      regexString = \/^(?![\\s])[a-zA-Z0-9.,()\\\/='+:?!\\\"%&amp;*&lt;&gt;;{@#_ \\r\\n-\\s]*$\/;\r\n      regex = new RegExp(regexString);\r\n\r\n      if (field != \"\") {\r\n            if (!regex.test(field)) {\r\n                  addError(fieldName, \"Not Valid Swift Format\", \"Not Valid Swift Format\");\r\n            }\r\n      }\r\n}\r\n\/\/--------------------------------------------------------------------------------------------------------\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line2, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line3, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line4, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line4\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line1, \"tw.local.idcContract.swiftMessageData.senderToReciever.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line2, \"tw.local.idcContract.swiftMessageData.senderToReciever.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line3, \"tw.local.idcContract.swiftMessageData.senderToReciever.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line4, \"tw.local.idcContract.swiftMessageData.senderToReciever.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line5, \"tw.local.idcContract.swiftMessageData.senderToReciever.line5\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line6, \"tw.local.idcContract.swiftMessageData.senderToReciever.line6\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line1, \"tw.local.idcContract.swiftMessageData.intermediary.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line2, \"tw.local.idcContract.swiftMessageData.intermediary.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line3, \"tw.local.idcContract.swiftMessageData.intermediary.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line4, \"tw.local.idcContract.swiftMessageData.intermediary.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line5, \"tw.local.idcContract.swiftMessageData.intermediary.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line1, \"tw.local.idcContract.swiftMessageData.receiverCorrespondent.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line2, \"tw.local.idcContract.swiftMessageData.receiverCorrespondent.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line3, \"tw.local.idcContract.swiftMessageData.receiverCorrespondent.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line4, \"tw.local.idcContract.swiftMessageData.receiverCorrespondent.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line5, \"tw.local.idcContract.swiftMessageData.receiverCorrespondent.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiverOfCover, \"tw.local.idcContract.swiftMessageData.receiverOfCover\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.receiver, \"tw.local.idcContract.swiftMessageData.receiver\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line1, \"tw.local.idcContract.swiftMessageData.accountWithInstitution.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line2, \"tw.local.idcContract.swiftMessageData.accountWithInstitution.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line3, \"tw.local.idcContract.swiftMessageData.accountWithInstitution.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line4, \"tw.local.idcContract.swiftMessageData.accountWithInstitution.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line5, \"tw.local.idcContract.swiftMessageData.accountWithInstitution.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line1, \"tw.local.idcContract.swiftMessageData.orderingInstitution.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line2, \"tw.local.idcContract.swiftMessageData.orderingInstitution.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line3, \"tw.local.idcContract.swiftMessageData.orderingInstitution.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line4, \"tw.local.idcContract.swiftMessageData.orderingInstitution.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line5, \"tw.local.idcContract.swiftMessageData.orderingInstitution.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line3, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line4, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line5, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line3, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line4, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line5, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line5\");\r\n\r\nswiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line3, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line3\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line4, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line4\");\r\nswiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line5, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line5\");\r\n\r\nmandatory(tw.local.idcContract.swiftMessageData.swiftMessageOption, \"tw.local.idcContract.swiftMessageData.swiftMessageOption\");\r\ntw.local.needBicVal = false;\r\ntw.local.bicFields = [];\r\nif (tw.local.idcContract.swiftMessageData.swiftMessageOption == \"Option 1: Serial Payment \u2013 Send MT 103\") {\r\n      mandatory(tw.local.idcContract.swiftMessageData.detailsOfCharge, \"tw.local.idcContract.swiftMessageData.detailsOfCharge\");\r\n\r\n      mandatory(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line1\");\r\n\r\n      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line1\");\r\n      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line2\");\r\n\r\n      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1\");\r\n      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2\");\r\n\r\n\r\n} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == \"Option 2: Cover Payment \u2013 Send MT 103 and MT 202 COV\") {\r\n      tw.local.needBicVal = true;\r\n      tw.local.bicFields[0] = {};\r\n      tw.local.bicFields[0].name = \"tw.local.idcContract.swiftMessageData.receiverOfCover\";\r\n      tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.receiverOfCover;\r\n      tw.local.bicFields[1] = {};\r\n      tw.local.bicFields[1].name = \"tw.local.idcContract.swiftMessageData.receiver\";\r\n      tw.local.bicFields[1].value = tw.local.idcContract.swiftMessageData.receiver;\r\n      mandatory(tw.local.idcContract.swiftMessageData.detailsOfCharge, \"tw.local.idcContract.swiftMessageData.detailsOfCharge\");\r\n      mandatory(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, \"tw.local.idcContract.swiftMessageData.detailsOfPayment.line1\");\r\n\r\n      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line1\");\r\n      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, \"tw.local.idcContract.swiftMessageData.orderingCustomer.line2\");\r\n\r\n      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1\");\r\n      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, \"tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2\");\r\n\r\n} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == \"Option 3: Bank to Bank Payment (1) \u2013 Send MT 202 and MT 400\") {\r\n\r\n      if (tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == \"\" || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == null) {\r\n            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1\");\r\n            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2\");\r\n      } else {\r\n            tw.local.needBicVal = true;\r\n            tw.local.bicFields[0] = {};\r\n            tw.local.bicFields[0].name = \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1\";\r\n            tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1;\r\n      }\r\n\r\n\r\n} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == \"Option 4: Bank to Bank Payment (2) \u2013 Send MT 400\") {\r\n\r\n} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == \"Option 5: Bank to Bank (Local) \u2013 Send MT 202\") {\r\n\r\n\r\n      if (tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == \"\" || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == null) {\r\n            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1\");\r\n            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2\");\r\n      } else {\r\n            tw.local.needBicVal = true;\r\n            tw.local.bicFields[0] = {};\r\n            tw.local.bicFields[0].name = \"tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1\";\r\n            tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1;\r\n      }\r\n}\r\n\r\nvalidateTab(7, \"Swift Message Data\");\r\n\/\/---------------------------------------Action List Validation---------------------------------------\r\nmandatory(tw.local.selectedAction, \"tw.local.selectedAction\");\r\nif (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n      mandatory(tw.local.idcRequest.stepLog.comment, \"tw.local.idcRequest.stepLog.comment\");\r\n}\r\n\r\nif (tw.local.selectedAction == tw.epv.Action.returnToTradeFO) {\r\n      mandatory(tw.local.idcRequest.stepLog.returnReason, \"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\n\r\nif (tw.local.idcRequest.approvals.CAD == true &amp;&amp; tw.local.selectedAction == \"Obtain Approvals\" &amp;&amp; tw.local.idcContract.facilities.length == 0) {\r\n      addError(\"tw.local.selectedAction\", \"This requset does not has facility to acquire CAD\");\r\n}\r\n\r\nif ((tw.local.idcRequest.approvals.CAD == true ||\r\n      tw.local.idcRequest.approvals.compliance == true ||\r\n      tw.local.idcRequest.approvals.treasury == true)) {\r\n\r\n      if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n            addError(\"tw.local.selectedAction\", \"Please Check at least one of Approvals\");\r\n\r\n      } else if (tw.local.selectedAction == \"Create Liquidation\" || tw.local.selectedAction == \"Submit Liquidation\") {\r\n            addError(\"tw.local.selectedAction\", \"Not applicable if one of the Approvals is checked\");\r\n      }\r\n}\r\n\r\n\r\n\/\/=====================================================================================\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\n      regexString = `^\\\\d{1,10}(\\\\.\\\\d{1,6})?$`;\r\n      regex = new RegExp(regexString);\r\n\r\n      if (!regex.test(field)) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\n      regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n      regex = new RegExp(regexString);\r\n\r\n      if (!regex.test(field) &amp;&amp; field &gt;= 0) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\nfunction isNumber(field, fieldName) {\r\n      if (!isNaN(parseFloat(field)) &amp;&amp; isFinite(field)) {\r\n            return true;\r\n      }\r\n      addError(fieldName, \"This Field Must Be Number\", \"validationMessage\");\r\n      return false;\r\n}\r\n\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\n      tw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n      \/\/\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date, fieldName, controlMessage, validationMessage, exclude) {\r\n      if (exclude) {\r\n            if (date != null &amp;&amp; date &lt;= new Date()) {\r\n                  addError(fieldName, controlMessage, validationMessage);\r\n                  return false;\r\n            }\r\n            return true;\r\n      }\r\n      else {\r\n            var today = new Date();\r\n            today.setHours(0, 0, 0, 0);\r\n            \/\/\t\talert(\"today = \"+today);\r\n            \/\/\t\talert(\"date = \"+ date);\r\n            if (date != null &amp;&amp; date &lt; today) {\r\n                  addError(fieldName, controlMessage, validationMessage);\r\n                  return false;\r\n            }\r\n            return true;\r\n      }\r\n}\r\n\r\n\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field, date1, date2, fieldName, controlMessage, validationMessage) {\r\n      if (field &lt; date1 &amp;&amp; field &gt; date2) {\r\n            return true;\r\n      }\r\n      addError(fieldName, controlMessage, validationMessage);\r\n      return false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date, fieldName, controlMessage, validationMessage, exculde) {\r\n      if (exculde) {\r\n            if (date != null &amp;&amp; date &gt;= new Date()) {\r\n                  addError(fieldName, controlMessage, validationMessage);\r\n                  return false;\r\n            }\r\n            return true;\r\n      }\r\n      else {\r\n            if (date != null &amp;&amp; date &gt; new Date()) {\r\n                  addError(fieldName, controlMessage, validationMessage);\r\n                  return false;\r\n            }\r\n            return true;\r\n      }\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\n      if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\n      if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field, fieldName, max, controlMessage, validationMessage) {\r\n      if (field &gt; max) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field, fieldName, min, controlMessage, validationMessage) {\r\n      if (field &lt; min) {\r\n            addError(fieldName, controlMessage, validationMessage);\r\n            return false;\r\n      }\r\n      return true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field, fieldName) {\r\n      if (field == null || field == undefined) {\r\n            addError(fieldName, \"This Field Is Mandatory\", \"Mandatory Fields\", true);\r\n            mandatoryTriggered = true;\r\n            return false;\r\n      }\r\n      else {\r\n            switch (typeof field) {\r\n                  case \"string\":\r\n                        if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0) {\r\n                              addError(fieldName, \"This Field Is Mandatory\", \"Mandatory Fields\", true);\r\n                              mandatoryTriggered = true;\r\n                              return false;\r\n                        }\r\n                        break;\r\n                  case \"number\":\r\n                        if (field == 0.0) {\r\n                              addError(fieldName, \"This Field Is Mandatory\", \"Mandatory Fields\", true);\r\n                              mandatoryTriggered = true;\r\n                              return false;\r\n                        }\r\n                        break;\r\n            }\r\n      }\r\n      return true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index, tabName) {\r\n      if (tw.system.coachValidation.validationErrors.length &gt; tempLength) {\r\n            if (tw.local.validationMessage.length == 0) {\r\n                  tw.local.validationMessage += \"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n            }\r\n            tw.local.validationMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n            tempLength = tw.system.coachValidation.validationErrors.length;\r\n            tw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n      }\r\n}\r\n"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.63749ad7-bc66-47aa-9889-18b4a904ba6f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.ab5ea275-f0f5-42aa-aed6-912d37b82394"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"havePaymentTerm","isCollection":false,"declaredType":"dataObject","id":"2056.29351361-0c42-4565-ac68-6db4f9a20e80"},{"itemSubjectRef":"itm.12.a025468c-38bc-4809-b337-57da9e95dacb","name":"beneficiaryDetails","isCollection":false,"declaredType":"dataObject","id":"2056.2dfc4e1d-031f-48fc-a0d6-b1497d05abbe"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedBIC","isCollection":false,"declaredType":"dataObject","id":"2056.820be4c9-9efb-45a4-bc3e-435e75a3ab87"},{"targetRef":"2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Need Bic","declaredType":"sequenceFlow","id":"2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a","sourceRef":"2025.00c49a15-46c5-4bc4-b730-69bda4aa1258"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"needBicVal","isCollection":false,"declaredType":"dataObject","id":"2056.21735005-8f10-4c5c-9688-0ee5400123f5"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"bicFields","isCollection":true,"declaredType":"dataObject","id":"2056.5389b400-0b86-495a-91b0-4f1ebd731689"},{"outgoing":["2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c","2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6"],"incoming":["2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a"],"default":"2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":835,"y":405,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Need Bic","declaredType":"exclusiveGateway","id":"2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927"},{"targetRef":"2025.d5cb5c7e-3110-4656-9b58-d1f5440be786","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.needBicVal\t  !=\t  true"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Validate BIC","declaredType":"sequenceFlow","id":"2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c","sourceRef":"2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927"},{"outgoing":["2027.6131b81e-be01-4bd1-9638-1edc0ddd1112"],"incoming":["2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c"],"extensionElements":{"postAssignmentScript":["for (var i=0; i&lt;tw.local.result.length; i++) {\r\n\tif (!tw.local.result[i]) {\r\n\t\r\n\t\ttw.system.coachValidation.addValidationError(tw.local.bicFields[i].name, \"Please enter a valid BIC code\");\r\n\t}\r\n}"],"nodeVisualInfo":[{"width":95,"x":962,"y":386,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.6131b81e-be01-4bd1-9638-1edc0ddd1112","name":"Validate BIC","dataInputAssociation":[{"targetRef":"2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.bicFields"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.d5cb5c7e-3110-4656-9b58-d1f5440be786","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.result"]}}],"sourceRef":["2055.876be99d-0b55-4982-8dae-2a839dc36cb6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.20a3aced-fb07-4762-8811-1554963aba30"]}],"calledElement":"1.72840b39-03cc-42d8-a24b-01650b923101"},{"targetRef":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.6131b81e-be01-4bd1-9638-1edc0ddd1112","sourceRef":"2025.d5cb5c7e-3110-4656-9b58-d1f5440be786"},{"targetRef":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.needBicVal\t  !=\t  true"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6","sourceRef":"2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"result","isCollection":true,"declaredType":"dataObject","id":"2056.25b709ec-98fe-4102-8d83-bb2d5f671d51"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"liquidationVis","isCollection":false,"declaredType":"dataObject","id":"2056.000017c8-7535-424f-9d15-dec51d2fc6c9"},{"targetRef":"2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"305e9dc7-90dd-4628-949b-64f915bfaf51","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.2ce1a836-65df-417f-940c-58d90afb3217","sourceRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78"},{"targetRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Execution Hub Liquidation","declaredType":"sequenceFlow","id":"2027.b6798d99-1841-432a-b1f0-350f34b64500","sourceRef":"2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b"},{"outgoing":["2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00"],"incoming":["2027.d6a4fc27-765f-47a7-8cfb-c60cce983456"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1056,"y":169,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00","name":"Create Liquidation","dataInputAssociation":[{"targetRef":"2055.2d1e50b3-14b7-466c-8023-58739169cb0b","assignment":[{"from":{"evaluatesToTypeRef":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.idcContract.liquidationSummary.liquidationAmt"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.bba748b9-9b8e-42be-b54d-e4f738e77149","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.liqDone"]}}],"sourceRef":["2055.77f111cd-6053-4b0d-8e1b-90b3d06feffc"]}],"calledElement":"1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b"},{"startQuantity":1,"outgoing":["2027.8b956d76-afeb-4feb-a446-7203a4c13724"],"incoming":["2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00"],"default":"2027.8b956d76-afeb-4feb-a446-7203a4c13724","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1055,"y":77,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Liquidation Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.2a96f5a8-7e67-4431-b57c-2df330fb037f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\/\/ ====================================================================================================== \/\/\r\n\/*\r\n* =====================\r\n* |\tVALIDATE HERE   |\r\n* =====================\r\n*\/\r\n\r\nif (tw.local.liqDone == false) {\r\n\taddError(\"tw.local.selectedAction\",\"Create Liquidation Failed\");\r\n\ttw.local.successMessage = \"\";\r\n}else{\r\n\ttw.local.action = [];\r\n\ttw.local.action[0] = tw.epv.Action.obtainApprovals+\"\";\r\n\ttw.local.action[1] = tw.epv.Action.returnToTradeFO+\"\";\r\n\ttw.local.action[2] = tw.epv.Action.terminateRequest+\"\";\r\n\ttw.local.action[3] = tw.epv.Action.submitLiquidation+\"\";\r\n\ttw.local.successMessage = \"Liquidation Created Successfully\";\r\n\t\r\n\/\/\ttw.local.action[3] = tw.epv.Action.createLiquidation+\"\";\r\n\/\/\ttw.local.action.pop(\"Create Liquidation\");\r\n\/\/\ttw.local.action.push(\"Submit Liquidation\");\r\n}\n"]}},{"targetRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.12baadf1-2c96-4552-8ee1-96443743a1a6","sourceRef":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9"},{"targetRef":"2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true,"customBendPoint":[{"x":1033,"y":286}]}]},"name":"No","declaredType":"sequenceFlow","id":"2027.c98fc708-f161-41e5-9ec1-9c854f15401a","sourceRef":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9"},{"targetRef":"2025.2a96f5a8-7e67-4431-b57c-2df330fb037f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Liquidation Script","declaredType":"sequenceFlow","id":"2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00","sourceRef":"2025.bba748b9-9b8e-42be-b54d-e4f738e77149"},{"targetRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Execution Hub Liquidation","declaredType":"sequenceFlow","id":"2027.8b956d76-afeb-4feb-a446-7203a4c13724","sourceRef":"2025.2a96f5a8-7e67-4431-b57c-2df330fb037f"},{"targetRef":"2025.c509974e-91c4-4716-b581-52df7b962df9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightTop","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Update History","declaredType":"sequenceFlow","id":"2027.d345bf85-d5da-423e-9861-13d879a4869a","sourceRef":"2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"liqDone","isCollection":false,"declaredType":"dataObject","id":"2056.a4f00e20-9327-47a3-8f94-64753c9b1a3d"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountList","isCollection":true,"declaredType":"dataObject","id":"2056.09cbbf71-69cb-42c0-853c-66091dd50234"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"********\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerCIF","isCollection":false,"declaredType":"dataObject","id":"2056.253ef14b-0760-4e7e-b936-cb7f36ad444e"},{"itemSubjectRef":"itm.12.89a53d06-50b8-41df-a5cc-5e4f61147b6d","name":"tempCommissions","isCollection":true,"declaredType":"dataObject","id":"2056.106d42a3-f08a-43a7-8fa6-1ecb3930b4a8"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isChecker","isCollection":false,"declaredType":"dataObject","id":"2056.dd499e38-e88e-4d28-846f-6ae30f3e3ee3"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"accountNumList","isCollection":true,"declaredType":"dataObject","id":"2056.968805d2-4e91-40a8-b87d-5870b45e3aca"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"********\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"caseCIF","isCollection":false,"declaredType":"dataObject","id":"2056.af13d8ca-0438-438d-876e-677e498accf7"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"********\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"draweeCIF","isCollection":false,"declaredType":"dataObject","id":"2056.36d841e8-c1d0-4500-b33b-4fb1546dfaa1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"********\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"acounteeCIF","isCollection":false,"declaredType":"dataObject","id":"2056.3852274b-ef3c-4d62-b40b-1d2f0edfd59b"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isGLFound","isCollection":false,"declaredType":"dataObject","id":"2056.c082b7d7-23e0-4d58-8686-d93e1db893f5"},{"targetRef":"2025.00c49a15-46c5-4bc4-b730-69bda4aa1258","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"e9762cf5-1c12-4d1a-bb49-903d4734a0a4","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validate accounts and attachment","declaredType":"sequenceFlow","id":"2027.1b70557b-d508-4482-9baf-8dd19382b330","sourceRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"accountIndex","isCollection":false,"declaredType":"dataObject","id":"2056.a6f30203-9a66-48f8-a824-a2c383ebc209"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isGLFoundC","isCollection":false,"declaredType":"dataObject","id":"2056.17fca5c0-b4bd-488c-876a-ca87eb46014c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatExCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.5d58c049-40e0-45b9-8800-42d07f021cb0"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"exRate","isCollection":false,"declaredType":"dataObject","id":"2056.0dd47a10-0a6c-4bac-a17e-08bef3951c77"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"accountIndexC","isCollection":false,"declaredType":"dataObject","id":"2056.66ff55eb-5557-400a-8eab-176d299f1fea"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.df911b37-cd5c-4963-92e5-6d1bd46f52b8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].debitedAccount = {};\nautoObject[0].debitedAccount.balanceSign = \"\";\nautoObject[0].debitedAccount.GLAccountNumber = \"\";\nautoObject[0].debitedAccount.accountCurrency = {};\nautoObject[0].debitedAccount.accountCurrency.id = 0;\nautoObject[0].debitedAccount.accountCurrency.code = \"\";\nautoObject[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject[0].debitedAccount.accountBranchCode = \"\";\nautoObject[0].debitedAccount.accountBalance = 0.0;\nautoObject[0].debitedAccount.accountNumber = \"\";\nautoObject[0].debitedAccount.accountClass = \"\";\nautoObject[0].debitedAccount.ownerAccounts = \"\";\nautoObject[0].debitedAccount.currency = {};\nautoObject[0].debitedAccount.currency.name = \"\";\nautoObject[0].debitedAccount.currency.value = \"\";\nautoObject[0].debitedAmount = {};\nautoObject[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject[0].debitedAmount.debitPercentage = 0.0;\nautoObject[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject"}]},"itemSubjectRef":"itm.12.36aae433-b480-4a1f-861b-6d30c09351a1","name":"tempSettlement","isCollection":true,"declaredType":"dataObject","id":"2056.9b45c992-7b19-4cfd-8aac-3a030c5b8f05"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"commCIF","isCollection":false,"declaredType":"dataObject","id":"2056.cadbac87-0e88-4dd4-9e0a-9b3867f42378"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"commAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.495498ae-972e-48bc-a989-2ebd3076e4bb"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"sAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.7821e7b8-3abc-4739-a46a-2f7439649eb0"},{"startQuantity":1,"outgoing":["2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8"],"incoming":["2027.fc15d34d-501f-448a-ad67-7096c60bf136","2027.7f4c651b-243b-48e4-b102-1bff65046ec1"],"default":"2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8","extensionElements":{"nodeVisualInfo":[{"width":95,"x":563,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"First Init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.dbd95394-4a05-4961-a959-2e014f62bd3c","scriptFormat":"text\/x-javascript","script":{"content":["\/\/Dummy\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = \"Customer Account\";\r\ntw.local.idcContract.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\r\ntw.local.idcRequest.customerInformation.CIFNumber = \"********\";\r\ntw.local.idcRequest.financialDetails.amtPayableByNBE = 2000;\r\ntw.local.idcRequest.financialDetails.amtSight = 1000;\r\ntw.local.idcRequest.financialDetails.documentCurrency.code = \"EGP\";\r\ntw.local.idcContract.liquidationSummary.liquidationCurrency = \"USD\";\r\ntw.local.idcContract.liquidationSummary.liquidationAmt = 2000;\r\ntw.local.caseCIF = \"********\";\r\ntw.local.idcContract.IDCProduct.code = \"IAVC\";\r\ntw.local.acounteeCIF = \"********\";\r\ntw.local.idcRequest.appInfo.subStatus = \"test\";\r\n\/\/--------------------------------------------------------------------------------------------------------------------------------------------------------------------\r\ntw.local.idcRequest.approvals.CAD=false;\r\ntw.local.idcRequest.approvals.compliance=false;\r\ntw.local.idcRequest.approvals.treasury=false;\r\n\r\ntw.local.errorVIS = \"NONE\";\r\ntw.local.isChecker = false;\r\ntw.local.liquidationVis = true;\r\nif (tw.local.idcRequest.IDCRequestType == \"IDC Execution\" &amp;&amp; tw.local.idcRequest.paymentTerms.englishdescription == \"Sight\") {\r\n\ttw.local.liquidationVis = true;\r\n}else if (tw.local.idcRequest.IDCRequestType.code == \"IDC Acknowledgement\" || tw.local.idcRequest.IDCRequestType.code == \"ICAP\"){\r\n\ttw.local.liquidationVis = false;\r\n}\r\n\r\ntw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\n\/\/Party CIF\r\nfor (var i=0; i&lt;tw.local.idcContract.party.length; i++) {\r\n\tif (tw.local.idcContract.party[i].partyType.name == \"Accountee\") {\r\n\t\ttw.local.acounteeCIF = tw.local.idcContract.party[i].partyCIF;\r\n\t}else if (tw.local.idcContract.party[i].partyType.name == \"Case In Need\") {\r\n\t\ttw.local.caseCIF = tw.local.idcContract.party[i].partyCIF;\r\n\t}\r\n}\r\n\r\n\/\/\/\/Liquidation Summary\r\nif (tw.local.idcRequest.paymentTerms.englishdescription == \"Sight\") {\r\n    tw.local.idcContract.liquidationSummary.liquidationAmt = tw.local.idcRequest.financialDetails.amtPayableByNBE;\r\n}else if (tw.local.idcRequest.financialDetails.amtSight &gt; 0) {\r\n    tw.local.idcContract.liquidationSummary.liquidationAmt = tw.local.idcRequest.financialDetails.amtSight;\r\n}\r\ntw.local.idcContract.liquidationSummary.liquidationCurrency = tw.local.idcRequest.financialDetails.documentCurrency.code;\r\ntw.local.idcContract.liquidationSummary.debitBasisby = \"Amount\";\r\n\r\n\/\/Action List\r\ntw.local.action = [];\r\n\r\ntw.local.action[0] = tw.epv.Action.obtainApprovals+\"\";\r\ntw.local.action[1] = tw.epv.Action.returnToTradeFO+\"\";\r\ntw.local.action[2] = tw.epv.Action.terminateRequest+\"\";\r\ntw.local.action[3] = tw.epv.Action.createLiquidation+\"\";\r\n\/\/tw.local.action[4] = tw.epv.Action.submitLiquidation+\"\";"]}},{"targetRef":"2025.2666e607-54b9-4c2f-aa54-3adaf9432d78","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Execution Hub Liquidation","declaredType":"sequenceFlow","id":"2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8","sourceRef":"2025.dbd95394-4a05-4961-a959-2e014f62bd3c"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.309730d7-5433-42e1-9c6b-a2daa37412b1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"haveAmountAdvanced","isCollection":false,"declaredType":"dataObject","id":"2056.6530d4da-7048-4280-b38e-ce03a55db192"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.4ac3a712-7fe8-4535-a553-2d7b17d9a3f3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.76e40c7e-a91c-4abc-bb98-b1d6cfd76b09"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"accountClassCode","isCollection":false,"declaredType":"dataObject","id":"2056.fcec3165-4edb-4412-8054-a4edd9d88b2b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.ee9e66e0-82dc-4a11-a746-73394e0b1d19"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"commClassCode","isCollection":false,"declaredType":"dataObject","id":"2056.2fffa4d6-224b-434d-8d8d-fb4de390f7f5"},{"targetRef":"2025.bba748b9-9b8e-42be-b54d-e4f738e77149","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation == false &amp;&amp; tw.local.selectedAction == tw.epv.Action.createLiquidation"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Create Liquidation","declaredType":"sequenceFlow","id":"2027.d6a4fc27-765f-47a7-8cfb-c60cce983456","sourceRef":"2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9"},{"startQuantity":1,"outgoing":["2027.10f30b6b-718e-4798-8500-636a30d61a3d"],"incoming":["2027.5edebd23-4531-4fab-8f26-0f4d02da88f8","2027.d161860c-0fc6-43ed-8b5c-5fa52146d097","2027.23d98e10-52f3-467d-8448-691d429c2b5a","2027.cfc2555f-ae66-4036-aec3-0e1706a776e9"],"default":"2027.10f30b6b-718e-4798-8500-636a30d61a3d","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":435,"y":309,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.d161860c-0fc6-43ed-8b5c-5fa52146d097"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.e7db2f52-2c08-4580-9a29-19b21af8ec12"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.d5cb5c7e-3110-4656-9b58-d1f5440be786","extensionElements":{"default":["2027.d161860c-0fc6-43ed-8b5c-5fa52146d097"],"nodeVisualInfo":[{"width":24,"x":997,"y":444,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.1de00211-1f8e-4d2a-97fa-d00536d00c96","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.23d98e10-52f3-467d-8448-691d429c2b5a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.2fb0b475-ab69-4b43-ade2-8c1bc910f8e6"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.c509974e-91c4-4716-b581-52df7b962df9","extensionElements":{"default":["2027.23d98e10-52f3-467d-8448-691d429c2b5a"],"nodeVisualInfo":[{"width":24,"x":1235,"y":219,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 1","declaredType":"boundaryEvent","id":"2025.042452ee-934b-4db2-837f-a47d748db57d","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.5edebd23-4531-4fab-8f26-0f4d02da88f8"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.0d25c64c-1176-4197-9928-383e40cf25bb"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.7dc18078-74d0-4489-b097-5d157ac8efdb","extensionElements":{"default":["2027.5edebd23-4531-4fab-8f26-0f4d02da88f8"],"nodeVisualInfo":[{"width":24,"x":286,"y":223,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 2","declaredType":"boundaryEvent","id":"2025.8ec978af-bbad-4528-a1aa-9fe0222ed5d8","outputSet":{}},{"targetRef":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.5edebd23-4531-4fab-8f26-0f4d02da88f8","sourceRef":"2025.8ec978af-bbad-4528-a1aa-9fe0222ed5d8"},{"targetRef":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.d161860c-0fc6-43ed-8b5c-5fa52146d097","sourceRef":"2025.1de00211-1f8e-4d2a-97fa-d00536d00c96"},{"targetRef":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false,"customBendPoint":[{"x":1104,"y":511}]}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.23d98e10-52f3-467d-8448-691d429c2b5a","sourceRef":"2025.042452ee-934b-4db2-837f-a47d748db57d"},{"parallelMultiple":false,"outgoing":["2027.cfc2555f-ae66-4036-aec3-0e1706a776e9"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.09a977f3-640e-440d-bd98-906c49c7c87a"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.85bbb779-1d74-43b3-ab72-52e6488707d2","extensionElements":{"default":["2027.cfc2555f-ae66-4036-aec3-0e1706a776e9"],"nodeVisualInfo":[{"width":24,"x":694,"y":317,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 3","declaredType":"boundaryEvent","id":"2025.f031175a-fd68-4095-8a8e-071760637316","outputSet":{}},{"targetRef":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.cfc2555f-ae66-4036-aec3-0e1706a776e9","sourceRef":"2025.f031175a-fd68-4095-8a8e-071760637316"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.7e883b0e-93b8-4d2f-8014-4139c73d07e3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.b7fb1468-2a78-49b9-8460-64a059d876f1"},{"incoming":["2027.10f30b6b-718e-4798-8500-636a30d61a3d"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":359,"y":399,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.df98cb6c-957a-4af1-b10a-53ca34c90896"},{"targetRef":"2025.df98cb6c-957a-4af1-b10a-53ca34c90896","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.10f30b6b-718e-4798-8500-636a30d61a3d","sourceRef":"2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.7bba3525-afc7-4a5c-9344-6799d1dbaa25"},{"targetRef":"2025.dbd95394-4a05-4961-a959-2e014f62bd3c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To is Returned From Approvals","declaredType":"sequenceFlow","id":"2027.7f4c651b-243b-48e4-b102-1bff65046ec1","sourceRef":"05293831-1900-447c-9dae-db103ddb11ba"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.063df764-**************-0a8a30f8277b"},{"outgoing":["2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1"],"incoming":["2027.d345bf85-d5da-423e-9861-13d879a4869a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1200,"y":161,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1","name":"Database Integration","dataInputAssociation":[{"targetRef":"2055.d3b53e19-8f75-427c-8e43-fe25518ef721","assignment":[{"from":{"evaluatesToTypeRef":"12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}]},{"targetRef":"2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.79f352fc-0629-430a-87cd-5b10dbdc4454","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["false"]}}]},{"targetRef":"2055.3fafd6ca-c323-45ea-8653-0015f902d198","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.selectedAction"]}}]},{"targetRef":"2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Hub Maker\""]}}]},{"targetRef":"2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.c509974e-91c4-4716-b581-52df7b962df9","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}],"sourceRef":["2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}],"sourceRef":["2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0"]}],"calledElement":"1.9f0a859b-5010-4ab6-947a-81ad99803cf1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"successMessage","isCollection":false,"declaredType":"dataObject","id":"2056.c97acf08-89e0-45b2-9e4b-d5afccc61886"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"d4b37e69-2447-4352-a0ed-17f2f7b424e6","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"e62d3305-67fd-4556-9e37-4476115ad7bb","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"IDC test","declaredType":"globalUserTask","id":"1.e5318bc6-7232-4e45-a258-184ebb476098","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.29a18c6d-9022-434f-9a70-a464b8e167bd"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.4b1e7b1e-d8f1-4ca1-b2c2-578cb7d0e60f"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.cb3993e8-3e3e-44ea-87f7-46257e0c43af"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"34d95e45-849e-44e8-8555-e42a5cb9e0fa","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"77fe6cfd-5eab-4afe-82f9-c5b79d9e6e63","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"9da01b2f-2ea2-487d-841e-cc0ecc6ef6ad","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"IDC Execution\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"Sight\";\nautoObject.paymentTerms.arabicdescription = \"Sight\";\nautoObject.paymentTerms.englishdescription = \"Sight\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.ced678ef-99fc-4a39-afda-b9aa3dc9e399"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.3c9ffa86-4000-468f-9be7-0f80cda238fc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = [];\nautoObject.settlementAccounts[0] = {};\nautoObject.settlementAccounts[0].debitedAccount = {};\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = {};\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAmount = {};\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = [];\nautoObject.settlementAccounts[0].accountNumberList[0] = {};\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = {};\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = [];\nautoObject.party[0] = {};\nautoObject.party[0].partyType = {};\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = {};\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = {};\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new Date();\nautoObject.liquidationSummary.creditValueDate = new Date();\nautoObject.IDCProduct = {};\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new Date();\nautoObject.transactionMaturityDate = new Date();\nautoObject.commissionsAndCharges = [];\nautoObject.commissionsAndCharges[0] = {};\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = {};\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = {};\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = {};\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = [];\nautoObject.commissionsAndCharges[0].commAccountList[0] = {};\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new Date();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = {};\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = {};\nautoObject.swiftMessageData.intermediary = {};\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = {};\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = {};\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = {};\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = {};\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = {};\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = {};\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = {};\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = {};\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = {};\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.advices = [];\nautoObject.advices[0] = {};\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = {};\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = [];\nautoObject.cashCollateralAccounts[0] = {};\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = {};\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new Date();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = [];\nautoObject.contractLimitsTracking[0] = {};\nautoObject.contractLimitsTracking[0].partyType = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new Date();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = [];\nautoObject.accountNumberList[0] = {};\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.a2500e27-4bdb-46aa-b903-94761e5b9e29"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.c7e9180b-5e37-4c18-bbb3-9f8419d72148"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"complianceComments","isCollection":true,"id":"2055.e658d584-01ca-42ba-b607-b7567c0c5d22"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.0f187b12-d50e-45b1-b17b-a087a0f53a27"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.fullPath = \"\";\nautoObject.cmisQuery = \"\";\nautoObject.defaultProperties = [];\nautoObject.defaultProperties[0] = {};\nautoObject.defaultProperties[0].name = \"\";\nautoObject.defaultProperties[0].value = null;\nautoObject.defaultProperties[0].editable = false;\nautoObject.defaultProperties[0].hidden = false;\nautoObject"}]},"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.2597cac2-7b7a-4e32-9c9f-eecbfcfec168"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.ca1aa0ae-1095-4164-8bd2-94949d0619fe"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.cbefd4c1-892e-4ca3-858b-4a7d4ef83141"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ced678ef-99fc-4a39-afda-b9aa3dc9e399</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d11850da-284a-441a-a0ee-05dbc56abf25</guid>
            <versionId>6f8b8002-5e4c-4997-b4eb-a9b1c3a633f2</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3c9ffa86-4000-468f-9be7-0f80cda238fc</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>028cd92f-1068-4c31-b951-c4452312df88</guid>
            <versionId>74eebdae-ecca-4855-987e-48a13d6532c0</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a2500e27-4bdb-46aa-b903-94761e5b9e29</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>42c7ec84-7b73-4f1b-b8c5-9227b0a70d81</guid>
            <versionId>77caf9bd-f77b-4833-9336-46c3fee6fac6</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c7e9180b-5e37-4c18-bbb3-9f8419d72148</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cebdd6aa-407b-4d9b-837a-ab11da29b429</guid>
            <versionId>e7c81ab5-a11f-4c65-a7c7-17077fdafd3c</versionId>
        </processParameter>
        <processParameter name="complianceComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e658d584-01ca-42ba-b607-b7567c0c5d22</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>02cd08f6-c8f7-4577-afe9-d66a2484fc18</guid>
            <versionId>5dd58989-963b-410f-a62d-6d3a9055ce5b</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0f187b12-d50e-45b1-b17b-a087a0f53a27</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5e06d160-65b0-4e1c-a994-1e9a7f44c0af</guid>
            <versionId>2a4f85d7-b1fa-441c-aa88-347e6ff2717d</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2597cac2-7b7a-4e32-9c9f-eecbfcfec168</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>77ee3c9d-6b40-4d18-b8dc-8210d97f49f0</guid>
            <versionId>750aa847-d3cf-45ee-98b5-c71f29603f5d</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ca1aa0ae-1095-4164-8bd2-94949d0619fe</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2d0f663a-e444-4131-827d-c95fc7fdf6f6</guid>
            <versionId>15d75af9-36da-4c77-94ce-2c6e38f69ad8</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.29a18c6d-9022-434f-9a70-a464b8e167bd</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7a4dd781-621a-4af7-89db-647df73dc8b7</guid>
            <versionId>2788e12a-a310-403c-9353-6f0a90ac5650</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4b1e7b1e-d8f1-4ca1-b2c2-578cb7d0e60f</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fa5267ff-97a1-4140-bc9a-ad74ea03499b</guid>
            <versionId>bb865bb6-047e-4e38-9846-9a189fba5945</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cb3993e8-3e3e-44ea-87f7-46257e0c43af</processParameterId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3440dfca-f287-4642-a58a-a9e554f5e4a2</guid>
            <versionId>31239b33-f1ed-4b2a-8e84-f4e7c0f0b22e</versionId>
        </processParameter>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.662c9f43-1e4c-4967-aa8a-8866a409c38d</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b97493c0-efe0-4066-976f-871b74f52e01</guid>
            <versionId>82ace144-7e7a-4a32-ba45-072235dc28f7</versionId>
        </processVariable>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.89788273-c82c-4ed9-86a9-82e2f257dfdf</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf1de303-7c5b-4382-8563-710a07bc1eb9</guid>
            <versionId>990070b7-b92e-4eb4-a487-8972857c1834</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e4ef79d6-ce54-425b-a026-89baa260095d</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7e492a3a-e1e3-4aee-a405-2c28115c5a97</guid>
            <versionId>bb934d4c-f00a-4394-a16e-d780295a51ca</versionId>
        </processVariable>
        <processVariable name="exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c0d157bb-bd45-41da-bc26-e2618aa3c85c</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4d3e6363-6bcd-4c16-8285-5c4c61a27837</guid>
            <versionId>1bc7fb08-785a-420e-ab2d-8ba88a3490a8</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3bc21bb5-bd46-4916-a7e8-342e6fa535e2</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ef147bce-7b2c-46a6-8e1e-40c8591f7cb5</guid>
            <versionId>ce38cea9-a61c-464b-91b4-e5d9eab58136</versionId>
        </processVariable>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63749ad7-bc66-47aa-9889-18b4a904ba6f</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>22034b0d-d3ac-4493-aa78-c2ac1c2a3b61</guid>
            <versionId>8c742cc5-d552-4663-9489-38893fb46700</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab5ea275-f0f5-42aa-aed6-912d37b82394</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1a9f26e9-c129-42fe-bca3-ab963cd80555</guid>
            <versionId>e86203e8-6ea2-4bb7-9e06-5edd6aa86214</versionId>
        </processVariable>
        <processVariable name="havePaymentTerm">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.29351361-0c42-4565-ac68-6db4f9a20e80</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>feb086df-9a98-480c-837d-72a714668cae</guid>
            <versionId>5deb0269-49e3-4592-b182-73d2f3a69a5d</versionId>
        </processVariable>
        <processVariable name="beneficiaryDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2dfc4e1d-031f-48fc-a0d6-b1497d05abbe</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a025468c-38bc-4809-b337-57da9e95dacb</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bf63e65f-a9d3-409a-b808-f129f22755ef</guid>
            <versionId>601e7d3a-fb0f-4730-8b7d-90b110008973</versionId>
        </processVariable>
        <processVariable name="selectedBIC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.820be4c9-9efb-45a4-bc3e-435e75a3ab87</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>262f2845-646e-4e90-a55d-906568ee4725</guid>
            <versionId>3f23864d-a01b-46ac-ae2b-fa7f52eacd83</versionId>
        </processVariable>
        <processVariable name="needBicVal">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.21735005-8f10-4c5c-9688-0ee5400123f5</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a1a34a75-97bb-41d2-aa55-2adfe8e4d5e4</guid>
            <versionId>6192de13-1a2e-4234-a264-ccaaf314098f</versionId>
        </processVariable>
        <processVariable name="bicFields">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5389b400-0b86-495a-91b0-4f1ebd731689</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9e7eb1ed-64ff-4387-822f-771ab5d1800f</guid>
            <versionId>ac54d01b-5695-463e-9a07-5cf089e0b1d8</versionId>
        </processVariable>
        <processVariable name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.25b709ec-98fe-4102-8d83-bb2d5f671d51</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9ad6d30e-ef61-48bf-8bc1-2627049a65ec</guid>
            <versionId>72d445c4-6866-4767-9b12-7b901b967d61</versionId>
        </processVariable>
        <processVariable name="liquidationVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.000017c8-7535-424f-9d15-dec51d2fc6c9</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9026048b-5898-4154-ac10-3da47be482c6</guid>
            <versionId>704b909e-4467-4815-93e5-1efbcc18e923</versionId>
        </processVariable>
        <processVariable name="liqDone">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a4f00e20-9327-47a3-8f94-64753c9b1a3d</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>77a937ac-6e69-4917-9ed3-ca09c2a16504</guid>
            <versionId>27a08d4e-088c-40a2-9ce6-292a022202be</versionId>
        </processVariable>
        <processVariable name="accountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.09cbbf71-69cb-42c0-853c-66091dd50234</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ec353902-fbe6-40c6-962b-dc3ca17d6031</guid>
            <versionId>f1957f0a-b213-42e1-808d-4bd2eda6ea2d</versionId>
        </processVariable>
        <processVariable name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.253ef14b-0760-4e7e-b936-cb7f36ad444e</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8a84941d-5e43-4739-b762-c90798c00ca5</guid>
            <versionId>7e4d8849-fc54-405c-bfc5-2aeb244cd1ec</versionId>
        </processVariable>
        <processVariable name="tempCommissions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.106d42a3-f08a-43a7-8fa6-1ecb3930b4a8</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.89a53d06-50b8-41df-a5cc-5e4f61147b6d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>331da700-f21a-4654-8bdf-887348ce5eda</guid>
            <versionId>dddbbb3d-943e-4a8a-a2e9-89d5b2c6338b</versionId>
        </processVariable>
        <processVariable name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dd499e38-e88e-4d28-846f-6ae30f3e3ee3</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f50e98d4-620d-43b2-ae01-3d609e267ba6</guid>
            <versionId>4e4abec2-d4b6-458c-90e8-1129f684b9bb</versionId>
        </processVariable>
        <processVariable name="accountNumList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.968805d2-4e91-40a8-b87d-5870b45e3aca</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ceba756-8749-45f6-9fef-a704ca1806e0</guid>
            <versionId>e1178f0f-13c2-433f-a28e-0e6850ea2589</versionId>
        </processVariable>
        <processVariable name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.af13d8ca-0438-438d-876e-677e498accf7</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7b4e570a-0e5c-4936-9b80-662604bda158</guid>
            <versionId>6968bfea-bd4c-4332-aaa7-01ff7dca08cd</versionId>
        </processVariable>
        <processVariable name="draweeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.36d841e8-c1d0-4500-b33b-4fb1546dfaa1</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a0ef6a5c-897c-40a9-892b-28f8f6d64785</guid>
            <versionId>497837b0-3179-44ad-a4e2-ad3381b25483</versionId>
        </processVariable>
        <processVariable name="acounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3852274b-ef3c-4d62-b40b-1d2f0edfd59b</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5da27431-94f9-4ec0-901a-4da850b4a8f5</guid>
            <versionId>b5b35796-a57c-4273-874d-43bed9b1c531</versionId>
        </processVariable>
        <processVariable name="isGLFound">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c082b7d7-23e0-4d58-8686-d93e1db893f5</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4eb32e4d-ecb0-47ec-8f2a-c626e3668817</guid>
            <versionId>1c967b56-b585-4124-a7c9-ecadb1c2fa4f</versionId>
        </processVariable>
        <processVariable name="accountIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a6f30203-9a66-48f8-a824-a2c383ebc209</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a6fdbdb2-b250-42a9-9188-7dab5303b114</guid>
            <versionId>7092998e-673e-469b-93c8-55b1798dd0bd</versionId>
        </processVariable>
        <processVariable name="isGLFoundC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.17fca5c0-b4bd-488c-876a-ca87eb46014c</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>48fe4661-a82d-40c2-907c-8101eb65dcd0</guid>
            <versionId>90b860d1-7933-4232-af3c-68042cb1c230</versionId>
        </processVariable>
        <processVariable name="concatExCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d58c049-40e0-45b9-8800-42d07f021cb0</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>52f7356c-775c-4b9d-ad19-810ffc14ea6c</guid>
            <versionId>1b66cc09-488a-416d-b6fa-d078b39852d9</versionId>
        </processVariable>
        <processVariable name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0dd47a10-0a6c-4bac-a17e-08bef3951c77</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7c49f6f9-67ed-4f98-89d7-48579102cde0</guid>
            <versionId>a4e8cbd4-0f3f-484c-96db-804e98a791b9</versionId>
        </processVariable>
        <processVariable name="accountIndexC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.66ff55eb-5557-400a-8eab-176d299f1fea</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9eb2d593-fde2-4445-acb7-36795dad684d</guid>
            <versionId>d4c72982-a9fc-4241-9618-cc1d239c06ee</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.df911b37-cd5c-4963-92e5-6d1bd46f52b8</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>db5d723c-1f56-4a03-afc8-b629fb7ce91a</guid>
            <versionId>6787f536-c0f6-4b45-b54f-404aa6625f65</versionId>
        </processVariable>
        <processVariable name="tempSettlement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b45c992-7b19-4cfd-8aac-3a030c5b8f05</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>31</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.36aae433-b480-4a1f-861b-6d30c09351a1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>99bac1c6-8ca4-4636-bfc9-aeb4a91d6b2e</guid>
            <versionId>cf751cb9-37ed-4836-aeee-de4ff054aa9d</versionId>
        </processVariable>
        <processVariable name="commCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cadbac87-0e88-4dd4-9e0a-9b3867f42378</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>32</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>60b0284e-6072-4203-88cb-679384bf3f22</guid>
            <versionId>cb8627cb-534c-4271-8293-e4a94b3b0b43</versionId>
        </processVariable>
        <processVariable name="commAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.495498ae-972e-48bc-a989-2ebd3076e4bb</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>33</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eff2ed6a-38b1-4273-9922-ef2367a40958</guid>
            <versionId>7b5d3fe4-1d1b-465f-992e-82d7a3eb28d2</versionId>
        </processVariable>
        <processVariable name="sAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7821e7b8-3abc-4739-a46a-2f7439649eb0</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>34</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6e282dc4-c197-4105-aaef-a7d976a1f7ad</guid>
            <versionId>c69f2394-12f4-423d-bdee-273602c8ed74</versionId>
        </processVariable>
        <processVariable name="tmpAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.309730d7-5433-42e1-9c6b-a2daa37412b1</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>35</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2b0e957e-c367-4f9a-8698-0abbfcabf762</guid>
            <versionId>dbd506b7-3a5c-4369-9b88-2eab1bdefc44</versionId>
        </processVariable>
        <processVariable name="haveAmountAdvanced">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6530d4da-7048-4280-b38e-ce03a55db192</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>36</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb228cc1-e138-4d2d-ad7d-c2a208eb36ed</guid>
            <versionId>9e336084-5c8c-4b7d-a194-99ad5fe1cf3f</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4ac3a712-7fe8-4535-a553-2d7b17d9a3f3</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>37</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b7fdca03-bd30-4505-ace6-02b002e9f89c</guid>
            <versionId>68be3445-5c7a-4596-876d-7129c10b087b</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.76e40c7e-a91c-4abc-bb98-b1d6cfd76b09</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>38</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dd1bc0e1-9397-4e03-a53f-7ec42fcedc95</guid>
            <versionId>ce7e4eb0-5964-4661-be51-4a1660dbeedb</versionId>
        </processVariable>
        <processVariable name="accountClassCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fcec3165-4edb-4412-8054-a4edd9d88b2b</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>39</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3d1cb00b-35dc-4316-a75d-7cfb8385c740</guid>
            <versionId>d80a563a-1a87-4031-94b4-b9ed2f11a790</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ee9e66e0-82dc-4a11-a746-73394e0b1d19</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>40</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4e389049-7654-44aa-8e61-172d2f94e23a</guid>
            <versionId>35082f36-3242-4c7f-bae4-6a2acdef11d9</versionId>
        </processVariable>
        <processVariable name="commClassCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2fffa4d6-224b-434d-8d8d-fb4de390f7f5</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>41</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b7411b97-7dc6-4fea-b36d-680371ac5295</guid>
            <versionId>7ad8239e-3e7f-430d-a836-56f9eec426a2</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7e883b0e-93b8-4d2f-8014-4139c73d07e3</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>42</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6353f4ba-f45e-4e4d-b087-773b92c76304</guid>
            <versionId>2854985f-2000-4691-b368-b5a8cd482d35</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b7fb1468-2a78-49b9-8460-64a059d876f1</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>43</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>90cfeef9-e2da-4480-9b0c-5017c51c7958</guid>
            <versionId>d6ca47c2-a542-44dd-a34d-d25df16e9cad</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7bba3525-afc7-4a5c-9344-6799d1dbaa25</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>44</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6332f7a0-a8d0-4820-87ab-45c52a42d274</guid>
            <versionId>05f4495e-5cff-4d12-b155-22025d47b107</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.063df764-**************-0a8a30f8277b</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>45</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f7386ee1-03c5-4eeb-9ba8-8e97aa5ff548</guid>
            <versionId>af92a398-b699-42ec-80e5-52b1c9999153</versionId>
        </processVariable>
        <processVariable name="successMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c97acf08-89e0-45b2-9e4b-d5afccc61886</processVariableId>
            <description isNull="true" />
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <namespace>2</namespace>
            <seq>46</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>68cbb21d-ec2c-4fb2-91e8-427c0ac936c0</guid>
            <versionId>18224a33-a7ff-4710-854b-0f3608f0306d</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.14ba508a-9fa4-4051-91ca-5240db85c961</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.07775b59-b16f-472a-bfe7-6e708e75a8b1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7fd</guid>
            <versionId>14132000-8f9d-4c78-90d4-39c71b76c128</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bba748b9-9b8e-42be-b54d-e4f738e77149</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>Create Liquidation</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.84544fde-22e7-4c8c-ae02-8af0ce0bf82e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7ff</guid>
            <versionId>1872067a-610b-44c9-84cf-0aa13290b09b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.84544fde-22e7-4c8c-ae02-8af0ce0bf82e</subProcessId>
                <attachedProcessRef>/1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b</attachedProcessRef>
                <guid>5c9bef93-82be-4db3-a2c0-a7fd02f224c0</guid>
                <versionId>994f8604-bbcb-4cfa-a60a-f7988b491cb0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d5cb5c7e-3110-4656-9b58-d1f5440be786</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>Validate BIC</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0f26a7d9-1dd3-4284-89b5-e950223f3ae0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7fe</guid>
            <versionId>195f9f64-fb06-4288-b243-36bfe7107a34</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0f26a7d9-1dd3-4284-89b5-e950223f3ae0</subProcessId>
                <attachedProcessRef>/1.72840b39-03cc-42d8-a24b-01650b923101</attachedProcessRef>
                <guid>7b84948d-8644-4df2-b0a3-fcfa3f7166b6</guid>
                <versionId>f0dca840-0b30-46ce-ad0b-f533139ee06e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c509974e-91c4-4716-b581-52df7b962df9</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>Database Integration</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.67fc2a25-466a-425d-9706-5eefe3c89ecd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7fb</guid>
            <versionId>5c5bee23-dd80-4922-b06a-9fccd2c9acad</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.67fc2a25-466a-425d-9706-5eefe3c89ecd</subProcessId>
                <attachedProcessRef>/1.9f0a859b-5010-4ab6-947a-81ad99803cf1</attachedProcessRef>
                <guid>e3bee59a-e8d0-4c6f-a64c-b200fb055320</guid>
                <versionId>ec5148ac-5ad5-435d-b27a-4f9a6e8e80bb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7dc18078-74d0-4489-b097-5d157ac8efdb</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>Approvals Comments</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0ef1fa9d-9303-44ba-8575-70642b15af28</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7f9</guid>
            <versionId>a0e3bf2e-6fe1-4698-ac18-821d87b5bf1d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0ef1fa9d-9303-44ba-8575-70642b15af28</subProcessId>
                <attachedProcessRef>/1.421a8257-34a5-45f9-b03f-a68e989d4ab8</attachedProcessRef>
                <guid>fd1e9bab-79ed-4d2a-b708-0d61fe97911b</guid>
                <versionId>57c573a4-38b6-4037-b326-ea4ed898f582</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.85bbb779-1d74-43b3-ab72-52e6488707d2</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>validate accounts and attachment</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.04fd92fc-b11c-4031-9440-d8ed596bf681</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7fa</guid>
            <versionId>c9d0ac27-05b3-4516-b409-e9b994589a8f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.04fd92fc-b11c-4031-9440-d8ed596bf681</subProcessId>
                <attachedProcessRef>/1.02d07ad8-f212-4ea3-8b5e-e8457d984314</attachedProcessRef>
                <guid>f8a94c61-573d-4c34-9a94-0d7b48f6821d</guid>
                <versionId>88eae32f-66ee-481b-b3c6-362b782df4e8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.004a7da5-eded-490a-b4b8-126453446bb6</processItemId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0f949e9c-3b22-4945-bb72-dca436cd5f23</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-7fc</guid>
            <versionId>f5e80c75-7151-4579-90f1-832d5692f341</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0f949e9c-3b22-4945-bb72-dca436cd5f23</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>c362e41e-4f7f-41b5-a5a2-d98469cd3121</guid>
                <versionId>931a6059-ea23-4480-9069-924f5718a823</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.730d4915-2707-41aa-b3ef-fdedddea6de1</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <guid>8a90045d-7173-4ae2-9740-f94964de762d</guid>
            <versionId>a08511bc-68a3-431a-854e-5c8b40275fa4</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.06b9a59f-b08b-485f-86ee-d2ff98e0c711</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <guid>44ea5447-f003-4bd5-829b-f7df2584cf98</guid>
            <versionId>e25d768b-e62e-4088-b7b2-470dd91930d0</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.c813c2f6-5fe2-45a7-aa5d-d8fb672cfeb3</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <guid>33997021-c2c5-4318-9ea9-b9872c06e78a</guid>
            <versionId>e7950397-e7ba-406c-956d-3bce4b65aaa0</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.14ba508a-9fa4-4051-91ca-5240db85c961</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.cbefd4c1-892e-4ca3-858b-4a7d4ef83141" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:globalUserTask implementation="##unspecified" name="IDC test" id="1.e5318bc6-7232-4e45-a258-184ebb476098">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="e62d3305-67fd-4556-9e37-4476115ad7bb">
                            <ns16:startEvent name="Start" id="05293831-1900-447c-9dae-db103ddb11ba">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="91" y="78" width="24" height="24" color="#F8F8F8" />
                                    <ns3:default>2027.7f4c651b-243b-48e4-b102-1bff65046ec1</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.7f4c651b-243b-48e4-b102-1bff65046ec1</ns16:outgoing>
                            </ns16:startEvent>
                            <ns16:endEvent name="End" id="aeb7df2a-3806-47d2-bc75-7ce9686f7779">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1372" y="188" width="24" height="44" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.d345bf85-d5da-423e-9861-13d879a4869a" name="Set Status " id="2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1055" y="265" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.c98fc708-f161-41e5-9ec1-9c854f15401a</ns16:incoming>
                                <ns16:outgoing>2027.d345bf85-d5da-423e-9861-13d879a4869a</ns16:outgoing>
                                <ns16:script>if (tw.local.selectedAction == tw.epv.Action.returnToTradeFO || tw.local.selectedAction == tw.epv.Action.terminateRequest || tw.local.selectedAction == tw.epv.Action.submitLiquidation) {&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidationReview;&#xD;
	&#xD;
}else if (tw.local.selectedAction == tw.epv.Action.obtainApprovals) {&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidationReview;	&#xD;
}&#xD;
&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns3:formTask name="IDC Execution Hub Liquidation" id="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="706" y="165" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.12baadf1-2c96-4552-8ee1-96443743a1a6</ns16:incoming>
                                <ns16:incoming>2027.8b956d76-afeb-4feb-a446-7203a4c13724</ns16:incoming>
                                <ns16:incoming>2027.b6798d99-1841-432a-b1f0-350f34b64500</ns16:incoming>
                                <ns16:incoming>2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8</ns16:incoming>
                                <ns16:outgoing>2027.2ce1a836-65df-417f-940c-58d90afb3217</ns16:outgoing>
                                <ns16:outgoing>2027.1b70557b-d508-4482-9baf-8dd19382b330</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>a70bfd8e-84e6-46ba-82ce-15c282da0b89</ns19:id>
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>9369a3a6-092d-4631-8298-d6f096b3f12e</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Error Message</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>fca3ab11-d501-4b7e-8268-75b742c716cb</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6670edad-7319-40af-8bce-981d7b638e27</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>163a4473-e28c-466c-8319-75ef69363911</ns19:id>
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>df851c1d-16bb-4a7d-8c27-ca6abe0980a9</ns19:id>
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>ea68df26-61cc-441c-8d4f-b0ed26741375</ns19:id>
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>e13d1921-2c1a-452a-8ef8-e7472b2b38d3</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>902621ab-12e5-4153-8acf-a1b0f9317d62</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4f49dfd9-bb4a-4c1a-8369-722ee96be562</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d3a61fb3-25f8-4e3e-81e4-25cf96596d44</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>93f3fcce-e34f-4ecb-8703-d9afa8050293</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>575b4c8b-4ba6-48c6-85a4-d5920314bd3a</ns19:id>
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    <ns19:value>Submit</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>82eed8a8-4ad1-4996-80fe-bf71f95e6186</ns19:id>
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a67c505f-4577-4c34-8569-3b8cda044b74</ns19:id>
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6a8c5c27-7b95-4746-83e2-65e5a6d3987d</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>2c8c93c4-6816-4450-80aa-7d88d2616275</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>32b2bd10-11db-4f5e-876d-3e88a68049a0</ns19:id>
                                                    <ns19:optionName>approvalsReadOnly</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a00a2862-d0a3-4cd8-83a0-cfc6fbfdad71</ns19:id>
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6de35cc5-090f-40eb-8f5d-81d71cf82f35</ns19:id>
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8baa3fa1-5618-41b4-8e8b-8743c204351f</ns19:id>
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>e74de34f-4b5c-44f9-8f69-d6c6325aa16c</ns19:id>
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4c64366b-87e4-4d99-86b5-2b5a763f712e</ns19:id>
                                                    <ns19:optionName>successMessage</ns19:optionName>
                                                    <ns19:value>tw.local.successMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>2e2f5828-6df9-4236-8697-30acae7b1425</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>26be2624-d910-469a-824c-5c9be158c714</ns19:id>
                                                        <ns19:layoutItemId>LiquidationVisData</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>2d5d4efc-df35-4a26-8ba8-73d37149916b</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Liquidation Vis</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>6a54d401-7bd3-44c1-89e8-74108e72fdd8</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>80e7042e-9ac2-4328-8186-39778f11bb68</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.9b679256-e93b-4400-89f2-bd15b0c5578d</ns19:viewUUID>
                                                        <ns19:binding>tw.local.liquidationVis</ns19:binding>
                                                    </ns19:contributions>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>56232432-a0da-41a2-8b21-bd3b59599325</ns19:id>
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>df6861da-25bd-4547-8474-609d93878804</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tab section</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>a7ce0700-52ad-4ecd-83ba-3b0d003d8442</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>4ce93c7e-e5be-4352-82ba-f1975896afbf</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>79ccee8f-4a23-4446-8e57-799b6e54fbb4</ns19:id>
                                                            <ns19:optionName>eventON_LOAD</ns19:optionName>
                                                            <ns19:value>if(${LiquidationVisData}.getData() == false){&#xD;
	this.context.element.querySelectorAll("li[role = 'tab']")[5].classList.add("hidden");&#xD;
	this.context.element.querySelectorAll("li[role = 'tab']")[6].classList.add("hidden");&#xD;
	this.context.element.querySelectorAll("li[role = 'tab']")[7].classList.add("hidden");&#xD;
}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>53fba797-5887-4043-859c-00fd0351ca5c</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>bc33ef1a-9ba2-400b-822a-9e8b248633ba</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>ee0e873f-8b53-491c-80a8-edabb09cff16</ns19:id>
                                                                <ns19:layoutItemId>5</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>6ffeb8df-076f-4bb0-87aa-2a7e486fcc31</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Contract Liquidation</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>92d2d71a-3bf2-43c2-85f0-b34050a29c2b</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>669c1a07-69ec-40bf-823e-41daf02220f7</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>48441b53-d1a1-4424-8c5c-a92b726c6b5f</ns19:id>
                                                                    <ns19:optionName>isFound</ns19:optionName>
                                                                    <ns19:value>tw.local.isFound</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7eaffe01-9d5f-4f23-8f13-c1107e35838d</ns19:id>
                                                                    <ns19:optionName>exchangeCurrency</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>db9ae035-434a-43cd-8cdd-b348e7dca730</ns19:id>
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>343ad0a5-9918-446f-80ed-7c5bfb93dfc4</ns19:id>
                                                                    <ns19:optionName>liquidationVis</ns19:optionName>
                                                                    <ns19:value>tw.local.liquidationVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>619c0ac8-a875-4c25-8e9a-8dce0382150a</ns19:id>
                                                                    <ns19:optionName>liqDone</ns19:optionName>
                                                                    <ns19:value>tw.local.liqDone</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ce1db920-f4df-4245-8321-83ec73c26686</ns19:id>
                                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4ef668ab-ef17-49f0-84b8-5f0a18cd21a6</ns19:id>
                                                                    <ns19:optionName>accountList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>73e51367-001f-4ac3-88d2-345360d107e3</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a3f6d737-4757-4eec-8ea1-cc14b4ab5c3a</ns19:id>
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d18c848d-8ffc-43b9-8578-4296db1b5683</ns19:id>
                                                                    <ns19:optionName>isGLFound</ns19:optionName>
                                                                    <ns19:value>tw.local.isGLFound</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7aa38590-247e-4071-8a65-1c290d596ac6</ns19:id>
                                                                    <ns19:optionName>accountIndex</ns19:optionName>
                                                                    <ns19:value>tw.local.accountIndex</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3d4b97e5-7743-45f8-8f63-0daa870954de</ns19:id>
                                                                    <ns19:optionName>tempSettlement</ns19:optionName>
                                                                    <ns19:value>tw.local.tempSettlement[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6fef4b8d-9297-4986-81c1-c739cd570e7c</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>tw.local.isChecker</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>baa1605e-68a0-4750-8282-20c16ee44d69</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e70a9421-06c6-46f8-8112-efe1ed9337f4</ns19:id>
                                                                    <ns19:optionName>concatExCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.concatExCurrency</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2769ee7c-ffa4-48da-8924-c6434d288c97</ns19:id>
                                                                    <ns19:optionName>sAccountList</ns19:optionName>
                                                                    <ns19:value>tw.local.sAccountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5b1b72d9-5e70-4fd2-83ed-964d5a0e304f</ns19:id>
                                                                    <ns19:optionName>accounteeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.acounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cadf20f5-ecc5-44ee-87e0-2c43893a8ec4</ns19:id>
                                                                    <ns19:optionName>caseCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.caseCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5179984d-5586-47dd-8b98-7d3e625e4b58</ns19:id>
                                                                    <ns19:optionName>draweeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d9a642ea-bdd2-44dd-88b9-fe1979f71b59</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7b088c22-f300-4583-818d-0e7313cee6a2</ns19:id>
                                                                    <ns19:optionName>accountClassCode</ns19:optionName>
                                                                    <ns19:value>tw.local.accountClassCode</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7cf96743-15d5-4a87-86a6-21bf01a05d45</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.9940a6bf-7b99-412b-a9c2-2484d4c53ed5</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>155b5fa7-f0ea-4fee-8040-00abf1723d63</ns19:id>
                                                                <ns19:layoutItemId>6</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>ad49f1d8-cee0-4125-833f-8ca17a09d50f</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Charges And Commissions </ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>024ee696-6fc9-402f-8329-f03d1c131001</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>********-e702-4718-8f7f-8f741665cb8a</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cdb824a5-54c9-486e-80b2-cf132b3d0cde</ns19:id>
                                                                    <ns19:optionName>accountList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9bfd6b14-0002-445e-8f2a-5009a5e1b475</ns19:id>
                                                                    <ns19:optionName>accountNumberList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountNumList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0eec0356-127f-47dd-8ffa-ef8602ca379e</ns19:id>
                                                                    <ns19:optionName>accounteeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.acounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c80ecc2a-229e-4dd0-8864-2405411e9f17</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>tw.local.isChecker</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9d1cff23-b398-4617-8eca-80a097ee40b5</ns19:id>
                                                                    <ns19:optionName>tempCommissions</ns19:optionName>
                                                                    <ns19:value>tw.local.tempCommissions[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>64ec9255-ec7f-4966-86fa-efdcb9086067</ns19:id>
                                                                    <ns19:optionName>exCurrency</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c84c72cd-2288-49f2-8282-f870553b014a</ns19:id>
                                                                    <ns19:optionName>draweeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4824a863-8801-4020-8fad-5a7fbf6e0591</ns19:id>
                                                                    <ns19:optionName>caseCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.caseCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>877845a9-a757-446a-84ec-2809cd3fb09d</ns19:id>
                                                                    <ns19:optionName>isFound</ns19:optionName>
                                                                    <ns19:value>tw.local.isFound</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2707258c-3d99-40fc-80ef-7794cc985c43</ns19:id>
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.customerCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>56a99118-64ae-4986-8422-2c8c41b922a2</ns19:id>
                                                                    <ns19:optionName>isGLFoundC</ns19:optionName>
                                                                    <ns19:value>tw.local.isGLFoundC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>68212ae5-dad9-440b-882f-82d54144b687</ns19:id>
                                                                    <ns19:optionName>concatExCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.concatExCurrency</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>526b9350-4961-4e24-89f7-72e509c98a52</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8cb08ae8-1688-400c-80c3-6b35b2312748</ns19:id>
                                                                    <ns19:optionName>accountIndexC</ns19:optionName>
                                                                    <ns19:value>tw.local.accountIndexC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>acf5d79a-1ebd-4897-846e-f32c21ad8236</ns19:id>
                                                                    <ns19:optionName>commCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.commCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>469f6445-f77d-4afa-8af6-f8361e988686</ns19:id>
                                                                    <ns19:optionName>CommAccountList</ns19:optionName>
                                                                    <ns19:value>tw.local.commAccountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>45b527ac-032e-4dda-81f0-af4e80f690c4</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>aaee2328-3d0a-4616-8b64-4179b9ccaf5f</ns19:id>
                                                                    <ns19:optionName>commClassCode</ns19:optionName>
                                                                    <ns19:value>tw.local.commClassCode</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e6ac98d8-af60-4486-845c-8edb3fde0769</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract.commissionsAndCharges[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>6f39b0e1-78ad-4587-8d57-219576b38fa6</ns19:id>
                                                                <ns19:layoutItemId>7</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>0beadb40-0441-4af2-8a09-6eab40ef243d</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Swift Message Data</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cc4fc9ad-03cf-4f1e-8cff-7ee62ff02921</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>38ae9157-26ba-4c63-8b3a-eccae89f9f6a</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9de55a95-7e03-4d16-8fca-2e4404277744</ns19:id>
                                                                    <ns19:optionName>selctedBic</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedBIC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2a2b8d92-fa06-47f2-89d4-66b3e028d927</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.122789dd-9d59-4a0d-b507-23e1fe2141c4</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract.swiftMessageData</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="691" y="97" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.b6798d99-1841-432a-b1f0-350f34b64500</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2ce1a836-65df-417f-940c-58d90afb3217</ns16:incoming>
                                <ns16:outgoing>2027.b6798d99-1841-432a-b1f0-350f34b64500</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.c509974e-91c4-4716-b581-52df7b962df9" targetRef="aeb7df2a-3806-47d2-bc75-7ce9686f7779" name="To End" id="2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.fc15d34d-501f-448a-ad67-7096c60bf136" name="Set Step Name" id="2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="411" y="165" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d765aa1f-fbcb-4212-ad87-b808df66577a</ns16:incoming>
                                <ns16:incoming>2027.2010813b-e869-4e0e-85a6-085e4d9b87c6</ns16:incoming>
                                <ns16:outgoing>2027.fc15d34d-501f-448a-ad67-7096c60bf136</ns16:outgoing>
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b" targetRef="2025.dbd95394-4a05-4961-a959-2e014f62bd3c" name="To Initialization Script" id="2027.fc15d34d-501f-448a-ad67-7096c60bf136">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.d765aa1f-fbcb-4212-ad87-b808df66577a" gatewayDirection="Unspecified" name="is Returned From Approvals" id="2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="155" y="184" width="84" height="66" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.19c28870-24e2-44c0-903e-a0486b19cdb2</ns16:outgoing>
                                <ns16:outgoing>2027.d765aa1f-fbcb-4212-ad87-b808df66577a</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:callActivity calledElement="1.421a8257-34a5-45f9-b03f-a68e989d4ab8" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.2010813b-e869-4e0e-85a6-085e4d9b87c6" name="Approvals Comments" id="2025.7dc18078-74d0-4489-b097-5d157ac8efdb">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="251" y="165" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.19c28870-24e2-44c0-903e-a0486b19cdb2</ns16:incoming>
                                <ns16:outgoing>2027.2010813b-e869-4e0e-85a6-085e4d9b87c6</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.792bf3d4-e1dc-4b70-afbc-5214492c915c</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.59289b25-4be8-4329-b825-d40969b0afee</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.complianceComments</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.2eefde76-5898-4e8d-87b1-0f434cabef87</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9" targetRef="2025.7dc18078-74d0-4489-b097-5d157ac8efdb" name="Copy of Yes" id="2027.19c28870-24e2-44c0-903e-a0486b19cdb2">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.idcRequest.approvals.CAD || tw.local.idcRequest.approvals.compliance || tw.local.idcRequest.approvals.treasury</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.7dc18078-74d0-4489-b097-5d157ac8efdb" targetRef="2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b" name="Copy of Start To Coach" id="2027.2010813b-e869-4e0e-85a6-085e4d9b87c6">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.dc1bc456-bb43-4e42-a2ba-8edf64d16ba9" targetRef="2025.4d4a7c8f-a611-4dc4-a1f0-cd372c5ac49b" name="Copy of No" id="2027.d765aa1f-fbcb-4212-ad87-b808df66577a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.02d07ad8-f212-4ea3-8b5e-e8457d984314" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f536401d-8027-46bd-b599-79ea698e4386" name="validate accounts and attachment" id="2025.85bbb779-1d74-43b3-ab72-52e6488707d2">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="706" y="276" width="95" height="70" color="#95D087" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                    <ns3:preAssignmentScript>tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;</ns3:preAssignmentScript>
                                    <ns3:postAssignmentScript>&#xD;
if (tw.local.errorExist) {&#xD;
	alert(tw.local.errorMessage);&#xD;
}</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.f536401d-8027-46bd-b599-79ea698e4386</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.89fca13d-c79b-4425-8595-e7f39c82983e</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.52df16f4-9dba-4cab-841a-a8ee6ce8ce85</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.7a53f20a-455a-406a-afef-2953d3af1b32</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f">tw.local.documentsTypesSelected</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.a447f38b-3bd6-4fec-be92-03d02c8e6d75</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.exist</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.e4656a2a-4887-444d-8212-c2df402cfe0d</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.errorExist</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.2b33f4df-f983-4df4-843f-411f47a6c8a8</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.95c78223-e670-4271-9ed1-0c377642c5c5</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:exclusiveGateway default="2027.c98fc708-f161-41e5-9ec1-9c854f15401a" gatewayDirection="Unspecified" name="Have Errors" id="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="874" y="235" width="32" height="32" />
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.6131b81e-be01-4bd1-9638-1edc0ddd1112</ns16:incoming>
                                <ns16:incoming>2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6</ns16:incoming>
                                <ns16:outgoing>2027.c98fc708-f161-41e5-9ec1-9c854f15401a</ns16:outgoing>
                                <ns16:outgoing>2027.12baadf1-2c96-4552-8ee1-96443743a1a6</ns16:outgoing>
                                <ns16:outgoing>2027.d6a4fc27-765f-47a7-8cfb-c60cce983456</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.85bbb779-1d74-43b3-ab72-52e6488707d2" targetRef="2025.00c49a15-46c5-4bc4-b730-69bda4aa1258" name="To Copy of Have Errors" id="2027.f536401d-8027-46bd-b599-79ea698e4386">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="documentsTypesSelected" id="2056.662c9f43-1e4c-4967-aa8a-8866a409c38d" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.89788273-c82c-4ed9-86a9-82e2f257dfdf" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.e4ef79d6-ce54-425b-a026-89baa260095d" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" name="exist" id="2056.c0d157bb-bd45-41da-bc26-e2618aa3c85c" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.3bc21bb5-bd46-4916-a7e8-342e6fa535e2" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a" name="Validation" id="2025.00c49a15-46c5-4bc4-b730-69bda4aa1258">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="706" y="386" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.f536401d-8027-46bd-b599-79ea698e4386</ns16:incoming>
                                <ns16:incoming>2027.1b70557b-d508-4482-9baf-8dd19382b330</ns16:incoming>
                                <ns16:outgoing>2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a</ns16:outgoing>
                                <ns16:script>tw.local.message = "";&#xD;
tw.local.validationMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0;&#xD;
tw.local.invalidTabs = [];&#xD;
&#xD;
// ====================================================================================================== //&#xD;
/*&#xD;
* =====================&#xD;
* |	VALIDATE HERE   |&#xD;
* =====================&#xD;
*/&#xD;
//------------------------------------------Liquidation[5]--------------------------&#xD;
//Liquidation - Summary&#xD;
mandatory(tw.local.idcContract.liquidationSummary.liquidationAmt, "tw.local.idcContract.liquidationSummary.liquidationAmt");&#xD;
mandatory(tw.local.idcContract.liquidationSummary.debitValueDate, "tw.local.idcContract.liquidationSummary.debitValueDate");&#xD;
mandatory(tw.local.idcContract.liquidationSummary.creditValueDate, "tw.local.idcContract.liquidationSummary.creditValueDate");&#xD;
&#xD;
notPastDate(tw.local.idcContract.liquidationSummary.debitValueDate, "tw.local.idcContract.liquidationSummary.debitValueDate", "Mandatory and Must be &gt;= system date", false);&#xD;
notPastDate(tw.local.idcContract.liquidationSummary.creditValueDate, "tw.local.idcContract.liquidationSummary.creditValueDate", "Mandatory and Must be &gt;= system date", false);&#xD;
&#xD;
mandatory(tw.local.idcContract.liquidationSummary.debitBasisby, "tw.local.idcContract.liquidationSummary.debitBasisby");&#xD;
&#xD;
//Liquidation - Debited Account Details&#xD;
for (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {&#xD;
      mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.accountClass");&#xD;
      if (tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass == "Customer Account") {&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountNumber, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.accountNumber");&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.ownerAccounts, "tw.local.idcContract.settlementAccounts[i].debitedAccount.ownerAccounts")&#xD;
&#xD;
      } else if (tw.local.idcContract.settlementAccounts[i].debitedAccount.accountClass == "GL Account") {&#xD;
            if (tw.local.idcContract.settlementAccounts[i].debitedAccount.isGLFoundC == false) {&#xD;
                  addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.GLAccountNumber", "This Account Is Not Valid");&#xD;
            }&#xD;
            if (tw.local.idcContract.settlementAccounts[i].debitedAccount.isGLVerifiedC == false) {&#xD;
                  addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.GLAccountNumber", "Please Click the Verify Button");&#xD;
            }&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.GLAccountNumber, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.GLAccountNumber");&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountBranchCode, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.accountBranchCode");&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAccount.accountCurrency, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAccount.accountCurrency");&#xD;
      }&#xD;
&#xD;
      //Liquidation - Debited Amount&#xD;
      if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage != null || tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage != undefined) {&#xD;
            if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage &lt;= 0 ||&#xD;
                  tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage &gt; 100) {&#xD;
                  addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitPercentage", "Mandatory and Must be &gt; 0 and &lt;= 100")&#xD;
            }&#xD;
      }&#xD;
      if (tw.local.idcContract.liquidationSummary.debitBasisby == "Percentage") {&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitPercentage, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitPercentage");&#xD;
      } else {&#xD;
            mandatory(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitedAmtinLiquidationCurrency");&#xD;
            if (tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency &lt;= 0 ||&#xD;
                  tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency &gt; tw.local.idcContract.liquidationSummary.liquidationAmt) {&#xD;
                  addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitedAmtinLiquidationCurrency", "Mandatory and must be &gt; 0 and &lt;= Liquidation Amount")&#xD;
            }&#xD;
      }&#xD;
      if (tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate != undefined) {&#xD;
            validateDecimal(tw.local.idcContract.settlementAccounts[i].debitedAmount.negotiatedExchangeRate, "tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.negotiatedExchangeRate", "Must be Decimal (10,6)");&#xD;
      }&#xD;
      if ((tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinAccountCurrency &gt; tw.local.idcContract.settlementAccounts[i].debitedAccount.accountBalance) &amp;&amp; tw.local.idcContract.settlementAccounts[i].debitedAccount.isOverDraft == false) {&#xD;
            addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitedAmtinAccountCurrency", "ERROR: Must be &lt;= Account Balance");&#xD;
      }&#xD;
}&#xD;
&#xD;
//Validate Sum of (Debited Amt in Liquidation Currency)&#xD;
var sum = 0.0;&#xD;
for (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {&#xD;
      //	alert(tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency);&#xD;
      sum = tw.local.idcContract.settlementAccounts[i].debitedAmount.debitedAmtinLiquidationCurrency + sum;&#xD;
}&#xD;
//alert(sum);&#xD;
if (tw.local.idcContract.liquidationSummary.liquidationAmt &lt; sum) {&#xD;
      for (var i = 0; i &lt; tw.local.idcContract.settlementAccounts.length; i++) {&#xD;
            addError("tw.local.idcContract.settlementAccounts[" + i + "].debitedAmount.debitedAmtinLiquidationCurrency", "the sum of this field must be less than or equal Liquidation Amount", "the sum of this field must be less than or equal Liquidation Amount")&#xD;
      }&#xD;
}&#xD;
&#xD;
validateTab(5, "Contract Liquidation");&#xD;
//-----------------------------------Commissions and Charges[6]---------------------------------------------- &#xD;
for (var i = 0; i &lt; tw.local.idcContract.commissionsAndCharges.length; i++) {&#xD;
      if (tw.local.idcContract.commissionsAndCharges[i].chargeAmount &lt; 0) {&#xD;
            addError("tw.local.idcContract.commissionsAndCharges[" + i + "].chargeAmount", "Must be &gt;= 0");&#xD;
      }&#xD;
      if (tw.local.idcContract.commissionsAndCharges[i].waiver == false &amp;&amp; tw.local.idcContract.commissionsAndCharges[i].chargeAmount &gt; 0) {&#xD;
            //	mandatory(tw.local.idcContract.commissionsAndCharges[i].waiver,"tw.local.idcContract.commissionsAndCharges["+i+"].waiver");&#xD;
            mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.accountClass");&#xD;
&#xD;
            if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == "Customer Account") {&#xD;
                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountNumber, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.accountNumber");&#xD;
                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts, "tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts")&#xD;
&#xD;
            } else if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == "GL Account") {&#xD;
                  if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLVerifiedC == false) {&#xD;
                        addError("tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.GLAccountNumber", "Please Click the Verify Button");&#xD;
                  }&#xD;
                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.GLAccountNumber");&#xD;
                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountBranchCode, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.accountBranchCode");&#xD;
                  mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountCurrency, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.accountCurrency");&#xD;
            }&#xD;
            if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLFoundC == false) {&#xD;
                  addError("tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAccount.GLAccountNumber", "Account Not Found");&#xD;
            }&#xD;
            if (tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != undefined) {&#xD;
                  validateDecimal(tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate, "tw.local.idcContract.commissionsAndCharges[" + i + "].debitedAmount.negotiatedExchangeRate", "Must be Decimal (10,6)");&#xD;
            }&#xD;
      }&#xD;
}&#xD;
validateTab(5, "Charges And Commissions ");&#xD;
//-----------------------------------------swift validation[7]--------------------------------------&#xD;
function swiftValidation(field, fieldName) {&#xD;
      regexString = /^(?![\s])[a-zA-Z0-9.,()\/='+:?!\"%&amp;*&lt;&gt;;{@#_ \r\n-\s]*$/;&#xD;
      regex = new RegExp(regexString);&#xD;
&#xD;
      if (field != "") {&#xD;
            if (!regex.test(field)) {&#xD;
                  addError(fieldName, "Not Valid Swift Format", "Not Valid Swift Format");&#xD;
            }&#xD;
      }&#xD;
}&#xD;
//--------------------------------------------------------------------------------------------------------&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line2, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line3, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.detailsOfPayment.line4, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line4");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line1, "tw.local.idcContract.swiftMessageData.senderToReciever.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line2, "tw.local.idcContract.swiftMessageData.senderToReciever.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line3, "tw.local.idcContract.swiftMessageData.senderToReciever.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line4, "tw.local.idcContract.swiftMessageData.senderToReciever.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line5, "tw.local.idcContract.swiftMessageData.senderToReciever.line5");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.senderToReciever.line6, "tw.local.idcContract.swiftMessageData.senderToReciever.line6");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line1, "tw.local.idcContract.swiftMessageData.intermediary.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line2, "tw.local.idcContract.swiftMessageData.intermediary.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line3, "tw.local.idcContract.swiftMessageData.intermediary.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line4, "tw.local.idcContract.swiftMessageData.intermediary.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.intermediary.line5, "tw.local.idcContract.swiftMessageData.intermediary.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line1, "tw.local.idcContract.swiftMessageData.receiverCorrespondent.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line2, "tw.local.idcContract.swiftMessageData.receiverCorrespondent.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line3, "tw.local.idcContract.swiftMessageData.receiverCorrespondent.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line4, "tw.local.idcContract.swiftMessageData.receiverCorrespondent.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverCorrespondent.line5, "tw.local.idcContract.swiftMessageData.receiverCorrespondent.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiverOfCover, "tw.local.idcContract.swiftMessageData.receiverOfCover");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.receiver, "tw.local.idcContract.swiftMessageData.receiver");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line1, "tw.local.idcContract.swiftMessageData.accountWithInstitution.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line2, "tw.local.idcContract.swiftMessageData.accountWithInstitution.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line3, "tw.local.idcContract.swiftMessageData.accountWithInstitution.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line4, "tw.local.idcContract.swiftMessageData.accountWithInstitution.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.accountWithInstitution.line5, "tw.local.idcContract.swiftMessageData.accountWithInstitution.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line1, "tw.local.idcContract.swiftMessageData.orderingInstitution.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line2, "tw.local.idcContract.swiftMessageData.orderingInstitution.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line3, "tw.local.idcContract.swiftMessageData.orderingInstitution.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line4, "tw.local.idcContract.swiftMessageData.orderingInstitution.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingInstitution.line5, "tw.local.idcContract.swiftMessageData.orderingInstitution.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line3, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line4, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line5, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, "tw.local.idcContract.swiftMessageData.orderingCustomer.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, "tw.local.idcContract.swiftMessageData.orderingCustomer.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line3, "tw.local.idcContract.swiftMessageData.orderingCustomer.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line4, "tw.local.idcContract.swiftMessageData.orderingCustomer.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.orderingCustomer.line5, "tw.local.idcContract.swiftMessageData.orderingCustomer.line5");&#xD;
&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line3, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line3");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line4, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line4");&#xD;
swiftValidation(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line5, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line5");&#xD;
&#xD;
mandatory(tw.local.idcContract.swiftMessageData.swiftMessageOption, "tw.local.idcContract.swiftMessageData.swiftMessageOption");&#xD;
tw.local.needBicVal = false;&#xD;
tw.local.bicFields = [];&#xD;
if (tw.local.idcContract.swiftMessageData.swiftMessageOption == "Option 1: Serial Payment – Send MT 103") {&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.detailsOfCharge, "tw.local.idcContract.swiftMessageData.detailsOfCharge");&#xD;
&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line1");&#xD;
&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, "tw.local.idcContract.swiftMessageData.orderingCustomer.line1");&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, "tw.local.idcContract.swiftMessageData.orderingCustomer.line2");&#xD;
&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1");&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2");&#xD;
&#xD;
&#xD;
} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == "Option 2: Cover Payment – Send MT 103 and MT 202 COV") {&#xD;
      tw.local.needBicVal = true;&#xD;
      tw.local.bicFields[0] = {};&#xD;
      tw.local.bicFields[0].name = "tw.local.idcContract.swiftMessageData.receiverOfCover";&#xD;
      tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.receiverOfCover;&#xD;
      tw.local.bicFields[1] = {};&#xD;
      tw.local.bicFields[1].name = "tw.local.idcContract.swiftMessageData.receiver";&#xD;
      tw.local.bicFields[1].value = tw.local.idcContract.swiftMessageData.receiver;&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.detailsOfCharge, "tw.local.idcContract.swiftMessageData.detailsOfCharge");&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.detailsOfPayment.line1, "tw.local.idcContract.swiftMessageData.detailsOfPayment.line1");&#xD;
&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line1, "tw.local.idcContract.swiftMessageData.orderingCustomer.line1");&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.orderingCustomer.line2, "tw.local.idcContract.swiftMessageData.orderingCustomer.line2");&#xD;
&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line1");&#xD;
      mandatory(tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2, "tw.local.idcContract.swiftMessageData.ultimateBeneficiary.line2");&#xD;
&#xD;
} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == "Option 3: Bank to Bank Payment (1) – Send MT 202 and MT 400") {&#xD;
&#xD;
      if (tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == "" || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == null) {&#xD;
            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1");&#xD;
            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2");&#xD;
      } else {&#xD;
            tw.local.needBicVal = true;&#xD;
            tw.local.bicFields[0] = {};&#xD;
            tw.local.bicFields[0].name = "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1";&#xD;
            tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1;&#xD;
      }&#xD;
&#xD;
&#xD;
} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == "Option 4: Bank to Bank Payment (2) – Send MT 400") {&#xD;
&#xD;
} else if (tw.local.idcContract.swiftMessageData.swiftMessageOption == "Option 5: Bank to Bank (Local) – Send MT 202") {&#xD;
&#xD;
&#xD;
      if (tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == "" || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1 == null) {&#xD;
            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1");&#xD;
            mandatory(tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2, "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line2");&#xD;
      } else {&#xD;
            tw.local.needBicVal = true;&#xD;
            tw.local.bicFields[0] = {};&#xD;
            tw.local.bicFields[0].name = "tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1";&#xD;
            tw.local.bicFields[0].value = tw.local.idcContract.swiftMessageData.beneficiaryInstitution.line1;&#xD;
      }&#xD;
}&#xD;
&#xD;
validateTab(7, "Swift Message Data");&#xD;
//---------------------------------------Action List Validation---------------------------------------&#xD;
mandatory(tw.local.selectedAction, "tw.local.selectedAction");&#xD;
if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
      mandatory(tw.local.idcRequest.stepLog.comment, "tw.local.idcRequest.stepLog.comment");&#xD;
}&#xD;
&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToTradeFO) {&#xD;
      mandatory(tw.local.idcRequest.stepLog.returnReason, "tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
&#xD;
if (tw.local.idcRequest.approvals.CAD == true &amp;&amp; tw.local.selectedAction == "Obtain Approvals" &amp;&amp; tw.local.idcContract.facilities.length == 0) {&#xD;
      addError("tw.local.selectedAction", "This requset does not has facility to acquire CAD");&#xD;
}&#xD;
&#xD;
if ((tw.local.idcRequest.approvals.CAD == true ||&#xD;
      tw.local.idcRequest.approvals.compliance == true ||&#xD;
      tw.local.idcRequest.approvals.treasury == true)) {&#xD;
&#xD;
      if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
            addError("tw.local.selectedAction", "Please Check at least one of Approvals");&#xD;
&#xD;
      } else if (tw.local.selectedAction == "Create Liquidation" || tw.local.selectedAction == "Submit Liquidation") {&#xD;
            addError("tw.local.selectedAction", "Not applicable if one of the Approvals is checked");&#xD;
      }&#xD;
}&#xD;
&#xD;
&#xD;
//=====================================================================================&#xD;
function validateDecimal(field, fieldName, controlMessage, validationMessage) {&#xD;
      regexString = `^\\d{1,10}(\\.\\d{1,6})?$`;&#xD;
      regex = new RegExp(regexString);&#xD;
&#xD;
      if (!regex.test(field)) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
function validateDecimal2(field, fieldName, controlMessage, validationMessage) {&#xD;
      regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
      regex = new RegExp(regexString);&#xD;
&#xD;
      if (!regex.test(field) &amp;&amp; field &gt;= 0) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
function isNumber(field, fieldName) {&#xD;
      if (!isNaN(parseFloat(field)) &amp;&amp; isFinite(field)) {&#xD;
            return true;&#xD;
      }&#xD;
      addError(fieldName, "This Field Must Be Number", "validationMessage");&#xD;
      return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName, controlMessage, validationMessage, fromMandatory) {&#xD;
      tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
      //	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date, fieldName, controlMessage, validationMessage, exclude) {&#xD;
      if (exclude) {&#xD;
            if (date != null &amp;&amp; date &lt;= new Date()) {&#xD;
                  addError(fieldName, controlMessage, validationMessage);&#xD;
                  return false;&#xD;
            }&#xD;
            return true;&#xD;
      }&#xD;
      else {&#xD;
            var today = new Date();&#xD;
            today.setHours(0, 0, 0, 0);&#xD;
            //		alert("today = "+today);&#xD;
            //		alert("date = "+ date);&#xD;
            if (date != null &amp;&amp; date &lt; today) {&#xD;
                  addError(fieldName, controlMessage, validationMessage);&#xD;
                  return false;&#xD;
            }&#xD;
            return true;&#xD;
      }&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field, date1, date2, fieldName, controlMessage, validationMessage) {&#xD;
      if (field &lt; date1 &amp;&amp; field &gt; date2) {&#xD;
            return true;&#xD;
      }&#xD;
      addError(fieldName, controlMessage, validationMessage);&#xD;
      return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date, fieldName, controlMessage, validationMessage, exculde) {&#xD;
      if (exculde) {&#xD;
            if (date != null &amp;&amp; date &gt;= new Date()) {&#xD;
                  addError(fieldName, controlMessage, validationMessage);&#xD;
                  return false;&#xD;
            }&#xD;
            return true;&#xD;
      }&#xD;
      else {&#xD;
            if (date != null &amp;&amp; date &gt; new Date()) {&#xD;
                  addError(fieldName, controlMessage, validationMessage);&#xD;
                  return false;&#xD;
            }&#xD;
            return true;&#xD;
      }&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
      if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
      if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field, fieldName, max, controlMessage, validationMessage) {&#xD;
      if (field &gt; max) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field, fieldName, min, controlMessage, validationMessage) {&#xD;
      if (field &lt; min) {&#xD;
            addError(fieldName, controlMessage, validationMessage);&#xD;
            return false;&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field, fieldName) {&#xD;
      if (field == null || field == undefined) {&#xD;
            addError(fieldName, "This Field Is Mandatory", "Mandatory Fields", true);&#xD;
            mandatoryTriggered = true;&#xD;
            return false;&#xD;
      }&#xD;
      else {&#xD;
            switch (typeof field) {&#xD;
                  case "string":&#xD;
                        if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0) {&#xD;
                              addError(fieldName, "This Field Is Mandatory", "Mandatory Fields", true);&#xD;
                              mandatoryTriggered = true;&#xD;
                              return false;&#xD;
                        }&#xD;
                        break;&#xD;
                  case "number":&#xD;
                        if (field == 0.0) {&#xD;
                              addError(fieldName, "This Field Is Mandatory", "Mandatory Fields", true);&#xD;
                              mandatoryTriggered = true;&#xD;
                              return false;&#xD;
                        }&#xD;
                        break;&#xD;
            }&#xD;
      }&#xD;
      return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index, tabName) {&#xD;
      if (tw.system.coachValidation.validationErrors.length &gt; tempLength) {&#xD;
            if (tw.local.validationMessage.length == 0) {&#xD;
                  tw.local.validationMessage += "&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
            }&#xD;
            tw.local.validationMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
            tempLength = tw.system.coachValidation.validationErrors.length;&#xD;
            tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
      }&#xD;
}&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.63749ad7-bc66-47aa-9889-18b4a904ba6f" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.ab5ea275-f0f5-42aa-aed6-912d37b82394" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="havePaymentTerm" id="2056.29351361-0c42-4565-ac68-6db4f9a20e80" />
                            <ns16:dataObject itemSubjectRef="itm.12.a025468c-38bc-4809-b337-57da9e95dacb" isCollection="false" name="beneficiaryDetails" id="2056.2dfc4e1d-031f-48fc-a0d6-b1497d05abbe" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedBIC" id="2056.820be4c9-9efb-45a4-bc3e-435e75a3ab87" />
                            <ns16:sequenceFlow sourceRef="2025.00c49a15-46c5-4bc4-b730-69bda4aa1258" targetRef="2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927" name="To Need Bic" id="2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="needBicVal" id="2056.21735005-8f10-4c5c-9688-0ee5400123f5" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="bicFields" id="2056.5389b400-0b86-495a-91b0-4f1ebd731689" />
                            <ns16:exclusiveGateway default="2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c" name="Need Bic" id="2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="835" y="405" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.0de16547-9eba-49a7-8ea1-b10c92dd4f0a</ns16:incoming>
                                <ns16:outgoing>2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c</ns16:outgoing>
                                <ns16:outgoing>2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927" targetRef="2025.d5cb5c7e-3110-4656-9b58-d1f5440be786" name="To Validate BIC" id="2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.needBicVal	  !=	  true</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.72840b39-03cc-42d8-a24b-01650b923101" default="2027.6131b81e-be01-4bd1-9638-1edc0ddd1112" name="Validate BIC" id="2025.d5cb5c7e-3110-4656-9b58-d1f5440be786">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="962" y="386" width="95" height="70" />
                                    <ns3:postAssignmentScript>for (var i=0; i&lt;tw.local.result.length; i++) {&#xD;
	if (!tw.local.result[i]) {&#xD;
	&#xD;
		tw.system.coachValidation.addValidationError(tw.local.bicFields[i].name, "Please enter a valid BIC code");&#xD;
	}&#xD;
}</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2c9cee57-1b30-4556-9252-5fcc4a5c833c</ns16:incoming>
                                <ns16:outgoing>2027.6131b81e-be01-4bd1-9638-1edc0ddd1112</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.bicFields</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.876be99d-0b55-4982-8dae-2a839dc36cb6</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.result</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.20a3aced-fb07-4762-8811-1554963aba30</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.d5cb5c7e-3110-4656-9b58-d1f5440be786" targetRef="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9" name="To Have Errors" id="2027.6131b81e-be01-4bd1-9638-1edc0ddd1112">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.8977f7b3-09ef-4626-9e3c-7ddd6496b927" targetRef="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9" name="To Have Errors" id="2027.0d5a4ef9-26af-483b-aeab-6da3542e47a6">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.needBicVal	  !=	  true</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" name="result" id="2056.25b709ec-98fe-4102-8d83-bb2d5f671d51" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="liquidationVis" id="2056.000017c8-7535-424f-9d15-dec51d2fc6c9" />
                            <ns16:sequenceFlow sourceRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" targetRef="2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b" name="To Stay on page" id="2027.2ce1a836-65df-417f-940c-58d90afb3217">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="305e9dc7-90dd-4628-949b-64f915bfaf51">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.092fdd30-de09-4f49-b246-d7ffbb95bb5b" targetRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" name="To IDC Execution Hub Liquidation" id="2027.b6798d99-1841-432a-b1f0-350f34b64500">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b" default="2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00" name="Create Liquidation" id="2025.bba748b9-9b8e-42be-b54d-e4f738e77149">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1056" y="169" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d6a4fc27-765f-47a7-8cfb-c60cce983456</ns16:incoming>
                                <ns16:outgoing>2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.2d1e50b3-14b7-466c-8023-58739169cb0b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.idcContract.liquidationSummary.liquidationAmt</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.77f111cd-6053-4b0d-8e1b-90b3d06feffc</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.liqDone</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.8b956d76-afeb-4feb-a446-7203a4c13724" name="Liquidation Script" id="2025.2a96f5a8-7e67-4431-b57c-2df330fb037f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1055" y="77" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00</ns16:incoming>
                                <ns16:outgoing>2027.8b956d76-afeb-4feb-a446-7203a4c13724</ns16:outgoing>
                                <ns16:script>tw.local.message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
// ====================================================================================================== //&#xD;
/*&#xD;
* =====================&#xD;
* |	VALIDATE HERE   |&#xD;
* =====================&#xD;
*/&#xD;
&#xD;
if (tw.local.liqDone == false) {&#xD;
	addError("tw.local.selectedAction","Create Liquidation Failed");&#xD;
	tw.local.successMessage = "";&#xD;
}else{&#xD;
	tw.local.action = [];&#xD;
	tw.local.action[0] = tw.epv.Action.obtainApprovals+"";&#xD;
	tw.local.action[1] = tw.epv.Action.returnToTradeFO+"";&#xD;
	tw.local.action[2] = tw.epv.Action.terminateRequest+"";&#xD;
	tw.local.action[3] = tw.epv.Action.submitLiquidation+"";&#xD;
	tw.local.successMessage = "Liquidation Created Successfully";&#xD;
	&#xD;
//	tw.local.action[3] = tw.epv.Action.createLiquidation+"";&#xD;
//	tw.local.action.pop("Create Liquidation");&#xD;
//	tw.local.action.push("Submit Liquidation");&#xD;
}
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9" targetRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" name="Yes" id="2027.12baadf1-2c96-4552-8ee1-96443743a1a6">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9" targetRef="2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259" name="No" id="2027.c98fc708-f161-41e5-9ec1-9c854f15401a">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:customBendPoint x="1033" y="286" />
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.bba748b9-9b8e-42be-b54d-e4f738e77149" targetRef="2025.2a96f5a8-7e67-4431-b57c-2df330fb037f" name="To Liquidation Script" id="2027.0f89717a-d303-4e5e-8f46-1fbf4dc6aa00">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.2a96f5a8-7e67-4431-b57c-2df330fb037f" targetRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" name="To IDC Execution Hub Liquidation" id="2027.8b956d76-afeb-4feb-a446-7203a4c13724">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.d8ab7c7f-6cdf-45e9-b1e9-445bc1b41259" targetRef="2025.c509974e-91c4-4716-b581-52df7b962df9" name="To Update History" id="2027.d345bf85-d5da-423e-9861-13d879a4869a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightTop</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="liqDone" id="2056.a4f00e20-9327-47a3-8f94-64753c9b1a3d" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountList" id="2056.09cbbf71-69cb-42c0-853c-66091dd50234" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="customerCIF" id="2056.253ef14b-0760-4e7e-b936-cb7f36ad444e">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">"********"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.89a53d06-50b8-41df-a5cc-5e4f61147b6d" isCollection="true" name="tempCommissions" id="2056.106d42a3-f08a-43a7-8fa6-1ecb3930b4a8" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isChecker" id="2056.dd499e38-e88e-4d28-846f-6ae30f3e3ee3" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="accountNumList" id="2056.968805d2-4e91-40a8-b87d-5870b45e3aca" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="caseCIF" id="2056.af13d8ca-0438-438d-876e-677e498accf7">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"********"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="draweeCIF" id="2056.36d841e8-c1d0-4500-b33b-4fb1546dfaa1">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"********"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="acounteeCIF" id="2056.3852274b-ef3c-4d62-b40b-1d2f0edfd59b">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"********"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isGLFound" id="2056.c082b7d7-23e0-4d58-8686-d93e1db893f5" />
                            <ns16:sequenceFlow sourceRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" targetRef="2025.00c49a15-46c5-4bc4-b730-69bda4aa1258" name="To validate accounts and attachment" id="2027.1b70557b-d508-4482-9baf-8dd19382b330">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="e9762cf5-1c12-4d1a-bb49-903d4734a0a4">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="accountIndex" id="2056.a6f30203-9a66-48f8-a824-a2c383ebc209" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isGLFoundC" id="2056.17fca5c0-b4bd-488c-876a-ca87eb46014c" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatExCurrency" id="2056.5d58c049-40e0-45b9-8800-42d07f021cb0" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="exRate" id="2056.0dd47a10-0a6c-4bac-a17e-08bef3951c77" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="accountIndexC" id="2056.66ff55eb-5557-400a-8eab-176d299f1fea" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.df911b37-cd5c-4963-92e5-6d1bd46f52b8" />
                            <ns16:dataObject itemSubjectRef="itm.12.36aae433-b480-4a1f-861b-6d30c09351a1" isCollection="true" name="tempSettlement" id="2056.9b45c992-7b19-4cfd-8aac-3a030c5b8f05">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].debitedAccount = {};
autoObject[0].debitedAccount.balanceSign = "";
autoObject[0].debitedAccount.GLAccountNumber = "";
autoObject[0].debitedAccount.accountCurrency = {};
autoObject[0].debitedAccount.accountCurrency.id = 0;
autoObject[0].debitedAccount.accountCurrency.code = "";
autoObject[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject[0].debitedAccount.accountBranchCode = "";
autoObject[0].debitedAccount.accountBalance = 0.0;
autoObject[0].debitedAccount.accountNumber = "";
autoObject[0].debitedAccount.accountClass = "";
autoObject[0].debitedAccount.ownerAccounts = "";
autoObject[0].debitedAccount.currency = {};
autoObject[0].debitedAccount.currency.name = "";
autoObject[0].debitedAccount.currency.value = "";
autoObject[0].debitedAmount = {};
autoObject[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject[0].debitedAmount.debitPercentage = 0.0;
autoObject[0].debitedAmount.standardExchangeRate = 0.0;
autoObject[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="commCIF" id="2056.cadbac87-0e88-4dd4-9e0a-9b3867f42378" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="commAccountList" id="2056.495498ae-972e-48bc-a989-2ebd3076e4bb" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="sAccountList" id="2056.7821e7b8-3abc-4739-a46a-2f7439649eb0" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8" name="First Init Script" id="2025.dbd95394-4a05-4961-a959-2e014f62bd3c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="563" y="165" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.fc15d34d-501f-448a-ad67-7096c60bf136</ns16:incoming>
                                <ns16:incoming>2027.7f4c651b-243b-48e4-b102-1bff65046ec1</ns16:incoming>
                                <ns16:outgoing>2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8</ns16:outgoing>
                                <ns16:script>//Dummy&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "Customer Account";&#xD;
tw.local.idcContract.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;&#xD;
tw.local.idcRequest.customerInformation.CIFNumber = "********";&#xD;
tw.local.idcRequest.financialDetails.amtPayableByNBE = 2000;&#xD;
tw.local.idcRequest.financialDetails.amtSight = 1000;&#xD;
tw.local.idcRequest.financialDetails.documentCurrency.code = "EGP";&#xD;
tw.local.idcContract.liquidationSummary.liquidationCurrency = "USD";&#xD;
tw.local.idcContract.liquidationSummary.liquidationAmt = 2000;&#xD;
tw.local.caseCIF = "********";&#xD;
tw.local.idcContract.IDCProduct.code = "IAVC";&#xD;
tw.local.acounteeCIF = "********";&#xD;
tw.local.idcRequest.appInfo.subStatus = "test";&#xD;
//--------------------------------------------------------------------------------------------------------------------------------------------------------------------&#xD;
tw.local.idcRequest.approvals.CAD=false;&#xD;
tw.local.idcRequest.approvals.compliance=false;&#xD;
tw.local.idcRequest.approvals.treasury=false;&#xD;
&#xD;
tw.local.errorVIS = "NONE";&#xD;
tw.local.isChecker = false;&#xD;
tw.local.liquidationVis = true;&#xD;
if (tw.local.idcRequest.IDCRequestType == "IDC Execution" &amp;&amp; tw.local.idcRequest.paymentTerms.englishdescription == "Sight") {&#xD;
	tw.local.liquidationVis = true;&#xD;
}else if (tw.local.idcRequest.IDCRequestType.code == "IDC Acknowledgement" || tw.local.idcRequest.IDCRequestType.code == "ICAP"){&#xD;
	tw.local.liquidationVis = false;&#xD;
}&#xD;
&#xD;
tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
//Party CIF&#xD;
for (var i=0; i&lt;tw.local.idcContract.party.length; i++) {&#xD;
	if (tw.local.idcContract.party[i].partyType.name == "Accountee") {&#xD;
		tw.local.acounteeCIF = tw.local.idcContract.party[i].partyCIF;&#xD;
	}else if (tw.local.idcContract.party[i].partyType.name == "Case In Need") {&#xD;
		tw.local.caseCIF = tw.local.idcContract.party[i].partyCIF;&#xD;
	}&#xD;
}&#xD;
&#xD;
////Liquidation Summary&#xD;
if (tw.local.idcRequest.paymentTerms.englishdescription == "Sight") {&#xD;
    tw.local.idcContract.liquidationSummary.liquidationAmt = tw.local.idcRequest.financialDetails.amtPayableByNBE;&#xD;
}else if (tw.local.idcRequest.financialDetails.amtSight &gt; 0) {&#xD;
    tw.local.idcContract.liquidationSummary.liquidationAmt = tw.local.idcRequest.financialDetails.amtSight;&#xD;
}&#xD;
tw.local.idcContract.liquidationSummary.liquidationCurrency = tw.local.idcRequest.financialDetails.documentCurrency.code;&#xD;
tw.local.idcContract.liquidationSummary.debitBasisby = "Amount";&#xD;
&#xD;
//Action List&#xD;
tw.local.action = [];&#xD;
&#xD;
tw.local.action[0] = tw.epv.Action.obtainApprovals+"";&#xD;
tw.local.action[1] = tw.epv.Action.returnToTradeFO+"";&#xD;
tw.local.action[2] = tw.epv.Action.terminateRequest+"";&#xD;
tw.local.action[3] = tw.epv.Action.createLiquidation+"";&#xD;
//tw.local.action[4] = tw.epv.Action.submitLiquidation+"";</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.dbd95394-4a05-4961-a959-2e014f62bd3c" targetRef="2025.2666e607-54b9-4c2f-aa54-3adaf9432d78" name="To IDC Execution Hub Liquidation" id="2027.b6936a49-b3c2-4c60-9274-8aa28ed8b5b8">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpAdvancePayment" id="2056.309730d7-5433-42e1-9c6b-a2daa37412b1" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="haveAmountAdvanced" id="2056.6530d4da-7048-4280-b38e-ce03a55db192" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.4ac3a712-7fe8-4535-a553-2d7b17d9a3f3" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.76e40c7e-a91c-4abc-bb98-b1d6cfd76b09" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="accountClassCode" id="2056.fcec3165-4edb-4412-8054-a4edd9d88b2b" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.ee9e66e0-82dc-4a11-a746-73394e0b1d19" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="commClassCode" id="2056.2fffa4d6-224b-434d-8d8d-fb4de390f7f5" />
                            <ns16:sequenceFlow sourceRef="2025.046090f9-bb26-45b6-bccf-7fa846d2e2e9" targetRef="2025.bba748b9-9b8e-42be-b54d-e4f738e77149" name="Create Liquidation" id="2027.d6a4fc27-765f-47a7-8cfb-c60cce983456">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation == false &amp;&amp; tw.local.selectedAction == tw.epv.Action.createLiquidation</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.10f30b6b-718e-4798-8500-636a30d61a3d" name="Handling Error" id="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="435" y="309" width="95" height="70" color="#FF7782" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.5edebd23-4531-4fab-8f26-0f4d02da88f8</ns16:incoming>
                                <ns16:incoming>2027.d161860c-0fc6-43ed-8b5c-5fa52146d097</ns16:incoming>
                                <ns16:incoming>2027.23d98e10-52f3-467d-8448-691d429c2b5a</ns16:incoming>
                                <ns16:incoming>2027.cfc2555f-ae66-4036-aec3-0e1706a776e9</ns16:incoming>
                                <ns16:outgoing>2027.10f30b6b-718e-4798-8500-636a30d61a3d</ns16:outgoing>
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                            </ns16:scriptTask>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.d5cb5c7e-3110-4656-9b58-d1f5440be786" parallelMultiple="false" name="Error" id="2025.1de00211-1f8e-4d2a-97fa-d00536d00c96">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="997" y="444" width="24" height="24" />
                                    <ns3:default>2027.d161860c-0fc6-43ed-8b5c-5fa52146d097</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.d161860c-0fc6-43ed-8b5c-5fa52146d097</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.e7db2f52-2c08-4580-9a29-19b21af8ec12" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.c509974e-91c4-4716-b581-52df7b962df9" parallelMultiple="false" name="Error 1" id="2025.042452ee-934b-4db2-837f-a47d748db57d">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1235" y="219" width="24" height="24" />
                                    <ns3:default>2027.23d98e10-52f3-467d-8448-691d429c2b5a</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.23d98e10-52f3-467d-8448-691d429c2b5a</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.2fb0b475-ab69-4b43-ade2-8c1bc910f8e6" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.7dc18078-74d0-4489-b097-5d157ac8efdb" parallelMultiple="false" name="Error 2" id="2025.8ec978af-bbad-4528-a1aa-9fe0222ed5d8">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="286" y="223" width="24" height="24" />
                                    <ns3:default>2027.5edebd23-4531-4fab-8f26-0f4d02da88f8</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.5edebd23-4531-4fab-8f26-0f4d02da88f8</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.0d25c64c-1176-4197-9928-383e40cf25bb" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:sequenceFlow sourceRef="2025.8ec978af-bbad-4528-a1aa-9fe0222ed5d8" targetRef="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5" name="To Handling Error" id="2027.5edebd23-4531-4fab-8f26-0f4d02da88f8">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.1de00211-1f8e-4d2a-97fa-d00536d00c96" targetRef="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5" name="To Handling Error" id="2027.d161860c-0fc6-43ed-8b5c-5fa52146d097">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.042452ee-934b-4db2-837f-a47d748db57d" targetRef="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5" name="To Handling Error" id="2027.23d98e10-52f3-467d-8448-691d429c2b5a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:customBendPoint x="1104" y="511" />
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.85bbb779-1d74-43b3-ab72-52e6488707d2" parallelMultiple="false" name="Error 3" id="2025.f031175a-fd68-4095-8a8e-071760637316">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="694" y="317" width="24" height="24" />
                                    <ns3:default>2027.cfc2555f-ae66-4036-aec3-0e1706a776e9</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.cfc2555f-ae66-4036-aec3-0e1706a776e9</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.09a977f3-640e-440d-bd98-906c49c7c87a" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:sequenceFlow sourceRef="2025.f031175a-fd68-4095-8a8e-071760637316" targetRef="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5" name="To Handling Error" id="2027.cfc2555f-ae66-4036-aec3-0e1706a776e9">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.7e883b0e-93b8-4d2f-8014-4139c73d07e3" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.b7fb1468-2a78-49b9-8460-64a059d876f1" />
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.df98cb6c-957a-4af1-b10a-53ca34c90896">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="359" y="399" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.10f30b6b-718e-4798-8500-636a30d61a3d</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.c6f7e896-d1d2-43c4-8ffb-1d10d3ae71d5" targetRef="2025.df98cb6c-957a-4af1-b10a-53ca34c90896" name="To Stay on page" id="2027.10f30b6b-718e-4798-8500-636a30d61a3d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.7bba3525-afc7-4a5c-9344-6799d1dbaa25">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:sequenceFlow sourceRef="05293831-1900-447c-9dae-db103ddb11ba" targetRef="2025.dbd95394-4a05-4961-a959-2e014f62bd3c" name="To is Returned From Approvals" id="2027.7f4c651b-243b-48e4-b102-1bff65046ec1">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.063df764-**************-0a8a30f8277b">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:callActivity calledElement="1.9f0a859b-5010-4ab6-947a-81ad99803cf1" default="2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1" name="Database Integration" id="2025.c509974e-91c4-4716-b581-52df7b962df9">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1200" y="161" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d345bf85-d5da-423e-9861-13d879a4869a</ns16:incoming>
                                <ns16:outgoing>2027.8379c3d0-1f07-46d3-942d-9fd6e87026e1</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.d3b53e19-8f75-427c-8e43-fe25518ef721</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.79f352fc-0629-430a-87cd-5b10dbdc4454</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">false</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.3fafd6ca-c323-45ea-8653-0015f902d198</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedAction</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Hub Maker"</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="successMessage" id="2056.c97acf08-89e0-45b2-9e4b-d5afccc61886" />
                            <ns3:htmlHeaderTag id="d4b37e69-2447-4352-a0ed-17f2f7b424e6">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="34d95e45-849e-44e8-8555-e42a5cb9e0fa" />
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="77fe6cfd-5eab-4afe-82f9-c5b79d9e6e63" />
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="9da01b2f-2ea2-487d-841e-cc0ecc6ef6ad" />
                            </ns3:epvProcessLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.ced678ef-99fc-4a39-afda-b9aa3dc9e399">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "IDC Execution";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "Sight";
autoObject.paymentTerms.arabicdescription = "Sight";
autoObject.paymentTerms.englishdescription = "Sight";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.3c9ffa86-4000-468f-9be7-0f80cda238fc">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.a2500e27-4bdb-46aa-b903-94761e5b9e29">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = [];
autoObject.settlementAccounts[0] = {};
autoObject.settlementAccounts[0].debitedAccount = {};
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = {};
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAmount = {};
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = [];
autoObject.settlementAccounts[0].accountNumberList[0] = {};
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = {};
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = [];
autoObject.party[0] = {};
autoObject.party[0].partyType = {};
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = {};
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = {};
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new Date();
autoObject.liquidationSummary.creditValueDate = new Date();
autoObject.IDCProduct = {};
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new Date();
autoObject.transactionMaturityDate = new Date();
autoObject.commissionsAndCharges = [];
autoObject.commissionsAndCharges[0] = {};
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = {};
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = {};
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = {};
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = {};
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = [];
autoObject.commissionsAndCharges[0].commAccountList[0] = {};
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new Date();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = {};
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = {};
autoObject.swiftMessageData.intermediary = {};
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = {};
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = {};
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = {};
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = {};
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = {};
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = {};
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = {};
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = {};
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = {};
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.advices = [];
autoObject.advices[0] = {};
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = {};
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = [];
autoObject.cashCollateralAccounts[0] = {};
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = {};
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new Date();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = [];
autoObject.contractLimitsTracking[0] = {};
autoObject.contractLimitsTracking[0].partyType = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new Date();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = [];
autoObject.accountNumberList[0] = {};
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.c7e9180b-5e37-4c18-bbb3-9f8419d72148">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="complianceComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.e658d584-01ca-42ba-b607-b7567c0c5d22">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.0f187b12-d50e-45b1-b17b-a087a0f53a27">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.2597cac2-7b7a-4e32-9c9f-eecbfcfec168">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.fullPath = "";
autoObject.cmisQuery = "";
autoObject.defaultProperties = [];
autoObject.defaultProperties[0] = {};
autoObject.defaultProperties[0].name = "";
autoObject.defaultProperties[0].value = null;
autoObject.defaultProperties[0].editable = false;
autoObject.defaultProperties[0].hidden = false;
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.ca1aa0ae-1095-4164-8bd2-94949d0619fe" />
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.29a18c6d-9022-434f-9a70-a464b8e167bd" />
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.4b1e7b1e-d8f1-4ca1-b2c2-578cb7d0e60f" />
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.cb3993e8-3e3e-44ea-87f7-46257e0c43af" />
                        <ns16:inputSet />
                        <ns16:outputSet />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e8526ffc-f300-4d39-ac34-4cadbf0ee648</processLinkId>
            <processId>1.e5318bc6-7232-4e45-a258-184ebb476098</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.14ba508a-9fa4-4051-91ca-5240db85c961</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.004a7da5-eded-490a-b4b8-126453446bb6</toProcessItemId>
            <guid>71e168b7-7ad7-4077-8bcf-b04f51a1774a</guid>
            <versionId>5cbca700-189b-4409-bff0-dde4a090b7a1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.14ba508a-9fa4-4051-91ca-5240db85c961</fromProcessItemId>
            <toProcessItemId>2025.004a7da5-eded-490a-b4b8-126453446bb6</toProcessItemId>
        </link>
    </process>
</teamworks>

