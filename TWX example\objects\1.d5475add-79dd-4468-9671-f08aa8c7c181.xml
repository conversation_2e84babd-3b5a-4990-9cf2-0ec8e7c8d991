<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.d5475add-79dd-4468-9671-f08aa8c7c181" name="Review Request by Treasury Maker">
        <lastModified>1692698374667</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.6d50ff7a-acfa-4537-ba34-0bdfb29aa04d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>9ab5422e-95b1-456a-a70c-a631e810cf3c</guid>
        <versionId>dd4b2646-7c46-40a3-85f2-19ad98279d06</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.77ad1cc5-faed-4354-8455-2efd1606864a"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":84,"y":187,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"7f1e25f0-133d-4157-bfd1-c50187cee53e"},{"incoming":["2027.cdf41144-06d1-4001-9e4a-068d3abf8b19"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":946,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"49decf54-20a8-4b6e-8886-36842a3c76e3"},{"targetRef":"2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Start To Coach","declaredType":"sequenceFlow","id":"2027.77ad1cc5-faed-4354-8455-2efd1606864a","sourceRef":"7f1e25f0-133d-4157-bfd1-c50187cee53e"},{"startQuantity":1,"outgoing":["2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce"],"incoming":["2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f"],"default":"2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce","extensionElements":{"nodeVisualInfo":[{"width":95,"x":640,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.selectedAction == tw.epv.Action.submitRequest) {\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTreasuryCheckerApproval;\r\n} else if (tw.local.selectedAction == tw.epv.Action.sendCBEPending) {\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCBE;\r\n} else {\r\n\tif(tw.local.intiator == \"fo\"){\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;\r\n\t}else if (tw.local.intiator == \"Initiation\") {\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;\r\n\t}else{\r\n\t\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n\t\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;\r\n\t}\r\n}\r\n\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce","sourceRef":"2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161"},{"startQuantity":1,"outgoing":["2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f"],"incoming":["2027.b5d56c3a-caaf-40e6-a781-6d54ad228530"],"default":"2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f","extensionElements":{"nodeVisualInfo":[{"width":95,"x":258,"y":164,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a6025006-3452-4608-a935-9732389342a6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\ntw.local.errorVIS = \"NONE\";\r\n\r\nif (tw.local.treasuryComments == null) {\r\n\ttw.local.treasuryComments = tw.local.idcRequest.appLog;\r\n}\r\ntw.local.action = [];\r\ntw.local.action[0] = tw.epv.Action.returnToInitiator;\r\ntw.local.action[1] = tw.epv.Action.submitRequest;\r\ntw.local.action[2] = tw.epv.Action.sendCBEPending;"]}},{"targetRef":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Review Request by Treasury Maker","declaredType":"sequenceFlow","id":"2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f","sourceRef":"2025.a6025006-3452-4608-a935-9732389342a6"},{"outgoing":["2027.27ba55f7-257b-43dd-a041-3857fb606991","2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f"],"incoming":["2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f","2027.897d9dcb-f960-4d5e-9960-69a591c4b36a","2027.29354488-1f8a-4585-ad1d-ac8be98e9699"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":422,"y":164,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"85d002f6-cf80-423c-8012-e934182fea0f","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9e5d90ac-4ea3-4d91-8389-c926f72aaff4","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1b3c975f-03d4-43df-852f-4f1b7c2833d5","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d42dd014-3c51-493c-854d-32da0810dd62","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e5bf4942-3787-4f95-8e29-2b181c6cf2eb","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2b6ba676-fa72-434b-8964-d2708c82213f","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"dc125744-c18b-4123-8abd-3d6e545b8a7e","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8bf3f3ef-1fbd-4a99-84f9-432126b5ee7b","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5b0cc6f2-25a6-4eb0-8f16-c70327be41fa","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"93175e97-fd45-4893-8965-7f0e0d307c32","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a9f3f63-fc45-4d8a-8b52-021b84aa6387","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3ffef35b-37e5-43d1-87db-425f972eeb0e","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b1858223-e0a7-4736-8370-6c84d82eb657","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3bd74e47-48f2-43dc-84c5-15afd8ad4469","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f58cd41-79b7-4fda-8295-9908cf73fca8","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d5f0dbf0-9ea8-4508-82cd-7e93e63daaf4","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cee0c054-7642-40bd-81ea-6ff27a72469b","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2d62b565-d3c5-4a11-8658-2e711a1e441f","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2df5e5b8-ec5c-4a78-8123-f7a1128023df","optionName":"addBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"866be30b-ece8-40f0-85e4-8a1e5d22d5a8","optionName":"deleteBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7f44ae8f-f2ca-4ba1-8915-9329fd3a562d","optionName":"addInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"458abd43-99c9-4842-8798-1497d9b18faf","optionName":"deleteInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"121390b8-8a12-4083-8ba6-5ea724326ecb","optionName":"hasWithdraw","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65fd3314-9dca-47b8-813a-b6521fd50ca6","optionName":"havePaymentTerm","value":"no"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf6eb3fd-fae5-4c71-8721-1685de722ca7","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"99479ef8-40ab-4c83-8dd6-5b6814cfb673","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7da5d45c-bb9c-40f2-842a-67e48bb22a08","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a73b7647-bc21-456f-8448-eade609c7951","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9532cf03-4f99-4974-8c78-c94d517ef2c5","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c3103ddc-7a34-4dd0-879e-143d8df512a0","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f2e4ae75-e073-4d98-8ca4-fb1c9bf419ce","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4603b864-97f3-4bf5-8f4e-9a1d05025c9b","optionName":"advancePaymentsUsedOption","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f3e2fa96-a1c7-4c1e-82dd-fce184612e0e","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9609fc61-e7f3-49eb-8df2-4eb8aa4fdcc0","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97d3a033-fb21-48f7-8c47-1ee16e00eb12","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e87f3d5b-dbdf-48a5-848d-e08e26232312","optionName":"accountsList","value":"tw.local.accountsList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"809c0a3d-8234-4e16-836f-c4430812c352","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59d762ed-ca54-4aa1-8d23-ab9dbbbf8859","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c662e041-e7ef-4934-8733-450d41737803","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"947978e8-00b7-4415-8c8e-923081c00c1b","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpUsedAdvancePayment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5efe8bbc-24ce-4d62-8fca-4534f997e0cf","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9b128bf-6ce9-4977-86e2-c2a74cf5194d","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d33b4375-f834-4032-877f-51187440d856","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b98610a5-3635-4431-8e28-e22333044736","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6370f37c-0663-4efc-886b-5a9aac9a5b05","version":"8550"},{"layoutItemId":"attach1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6b1f2029-2d84-4f68-852d-12c7ad7ee74b","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cfb5ae8f-5939-4877-86dd-cf40eb2ed909","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d4a18a12-7172-4c9d-8812-7dbbc69d519f","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ced0e5a9-81b0-46e4-85b8-e7b5044d2020","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ca502351-e225-4941-8c86-b9291111329f","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"448d0fd2-bff5-4dd4-87db-619978bac5e1","optionName":"canCreate","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"*************-48f6-84d6-ac4b35d4ba8e","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e7b12440-6807-4bb1-817a-10bd1f04514c","optionName":"canDelete","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"067119a9-46f5-4ee0-8ef4-009c1ae28ca5","optionName":"visiable","value":"true"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"304ff02d-fd9d-41ed-8d27-b481f296ed04","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b8163995-3ca6-4407-8764-5807098f0b64","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1a829d28-6f6d-4cf8-8882-89ba38a791fe","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"490a61b2-6650-402f-87fb-973150260d03","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65f00479-20d3-4b3c-859e-7f410c451d92","optionName":"historyVisFlag","value":"None"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.treasuryComments[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c64d2bd8-5020-40e9-85f4-a4c787777445","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"d653e464-4646-493a-8255-42a40d062312"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f220bcad-f67d-474d-8bcc-577f86e74f96","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b310ef1c-5b32-40dd-84bf-6399072240f1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8b07f751-fd78-42e2-835e-98aa246ef7f4","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"896bf24c-6f28-4783-8a78-ba78f84f1c9a","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"871f64d6-6f34-4933-8638-d1dca2eacf97","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"bf301570-4328-47b0-81ac-143b86e15c48"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3e2f419e-5a98-46ec-883a-e1f9ed647a5f","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"67cbca25-b226-4671-85b5-978044472dc7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"48260233-a490-4351-89d1-576a055959f0","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bfe9e6b6-0c9f-4c9e-8f1a-e0781c3af53f","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cecd8a34-31f6-4343-8eda-48a317a10b11","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5e290dce-d3b0-4d34-89de-ace8d66661f8","optionName":"hasApprovals","value":"tw.local.hasApprovals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8234d75b-6a04-447c-87b2-639ffa611d6f","optionName":"hasReturnReason","value":"tw.local.hasreturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9dcc2280-f080-412e-8988-3b891ad8d087","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6c3bc46-4e99-433a-8c3f-3a68bfe5567a","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5ef21c5a-0570-41c8-809d-4f684afd2c74","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f8f74af1-a8eb-458f-87fe-2c2f6ad5f50b","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b196487d-862c-467c-834c-d129886df75a","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"52eed5ef-4535-4903-83ed-bbe812e47d68","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review Request by Treasury Maker","isForCompensation":false,"completionQuantity":1,"id":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6"},{"outgoing":["2027.897d9dcb-f960-4d5e-9960-69a591c4b36a"],"incoming":["2027.27ba55f7-257b-43dd-a041-3857fb606991"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.897d9dcb-f960-4d5e-9960-69a591c4b36a"],"nodeVisualInfo":[{"width":24,"x":435,"y":39,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.35aa8594-8aac-40c7-8979-fca78ea0d077"},{"targetRef":"2025.35aa8594-8aac-40c7-8979-fca78ea0d077","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"4c091836-5b8d-4037-b8c9-9efb16d634f4","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.27ba55f7-257b-43dd-a041-3857fb606991","sourceRef":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6"},{"targetRef":"2025.8e7a637f-d976-47de-be6c-7edeb213b6c5","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"fef1ed12-147f-471e-ac8f-c8fb1aa31ef7","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Service","declaredType":"sequenceFlow","id":"2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f","sourceRef":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6"},{"targetRef":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review Request by Treasury Maker","declaredType":"sequenceFlow","id":"2027.897d9dcb-f960-4d5e-9960-69a591c4b36a","sourceRef":"2025.35aa8594-8aac-40c7-8979-fca78ea0d077"},{"outgoing":["2027.cdf41144-06d1-4001-9e4a-068d3abf8b19"],"incoming":["2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":780,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.cdf41144-06d1-4001-9e4a-068d3abf8b19","name":"Update History","dataInputAssociation":[{"targetRef":"2055.648598d0-2039-40d4-b60b-3753a273a378","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}]},{"targetRef":"2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}]},{"targetRef":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Treasury Maker\""]}}]},{"targetRef":"2055.322bdb97-0698-43d7-8172-71cbc933103d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}],"sourceRef":["2055.65675974-9215-43be-8dce-3b75511a591d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}],"sourceRef":["2055.8fcdef92-a110-407f-aff8-5693f497f953"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}],"sourceRef":["2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}],"sourceRef":["2055.fce152d9-1c42-43bc-8bff-44f6b45aba67"]}],"calledElement":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59"},{"targetRef":"49decf54-20a8-4b6e-8886-36842a3c76e3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.cdf41144-06d1-4001-9e4a-068d3abf8b19","sourceRef":"2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b"},{"startQuantity":1,"outgoing":["2027.b5d56c3a-caaf-40e6-a781-6d54ad228530"],"incoming":["2027.77ad1cc5-faed-4354-8455-2efd1606864a"],"default":"2027.b5d56c3a-caaf-40e6-a781-6d54ad228530","extensionElements":{"nodeVisualInfo":[{"width":95,"x":121,"y":164,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\n\/\/tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\/\/\r\n"]}},{"targetRef":"2025.a6025006-3452-4608-a935-9732389342a6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Initialization Script","declaredType":"sequenceFlow","id":"2027.b5d56c3a-caaf-40e6-a781-6d54ad228530","sourceRef":"2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.73e15258-cf57-4e66-89ad-3aad4698cfd4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.41216b2f-a8c9-48fa-947c-0e5dd8ea1c78"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasApprovals","isCollection":false,"declaredType":"dataObject","id":"2056.2d4a323a-288b-4785-9ed0-05da7d91e21e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasreturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.21f08cf7-0936-4628-8b17-90c6e4aa7577"},{"startQuantity":1,"outgoing":["2027.46f53795-f02c-40d2-8611-900d1a50e4ad"],"incoming":["2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e"],"default":"2027.46f53795-f02c-40d2-8611-900d1a50e4ad","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":422,"y":368,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.543153b5-4193-4f2e-b425-6e28ca1b6c31","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/tw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\/\/\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\n\/\/mandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\/\/\/\/------------------------------------financial Details fo -------------------------------------------------------------------\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,\"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.name\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank\" );\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.account\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,\"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber\" );\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.submitRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.executionHub.code,\"tw.local.idcRequest.financialDetails.executionHub.code\");\r\n\/\/}\r\n\/\/var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;\r\n\/\/if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.cashAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\r\n\/\/}\r\n\/\/\r\n\/\/var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;\r\n\/\/if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtSight\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/}\r\n\/\/for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {\r\n\/\/\t\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentDate\");\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentAmount\");\r\n\/\/}\r\n\/\/-------------------------------action-----------------------------------------------------------------------\r\nif (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {\r\n\tmandatory(tw.local.idcRequest.stepLog.returnReason,\"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n\/\/}\r\n\r\n\/\/if ((tw.local.idcRequest.approvals.CAD==true|| \r\n\/\/    tw.local.idcRequest.approvals.compliance==true ||\r\n\/\/    tw.local.idcRequest.approvals.treasury==true))\r\n\/\/{   \r\n\/\/    if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n\/\/       addError(\"tw.local.selectedAction\", \"Please uncheck Approvals\");\r\n\/\/    }\r\n\/\/}\r\n\/\/else if (tw.local.selectedAction == \"Obtain Approvals\")\r\n\/\/{\r\n\/\/    addError(\"tw.local.selectedAction\", \"Please check Approvals\");\r\n\/\/}\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");"]}},{"targetRef":"2025.a407d111-1421-452e-94c2-50f99c269354","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.46f53795-f02c-40d2-8611-900d1a50e4ad","sourceRef":"2025.543153b5-4193-4f2e-b425-6e28ca1b6c31"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.4a54f005-9975-49a8-9daf-b13206e1976b"},{"outgoing":["2027.29354488-1f8a-4585-ad1d-ac8be98e9699","2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f"],"incoming":["2027.46f53795-f02c-40d2-8611-900d1a50e4ad"],"default":"2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":547,"y":387,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Copy of Have Errors","declaredType":"exclusiveGateway","id":"2025.a407d111-1421-452e-94c2-50f99c269354"},{"targetRef":"2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of yes","declaredType":"sequenceFlow","id":"2027.29354488-1f8a-4585-ad1d-ac8be98e9699","sourceRef":"2025.a407d111-1421-452e-94c2-50f99c269354"},{"targetRef":"2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of no","declaredType":"sequenceFlow","id":"2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f","sourceRef":"2025.a407d111-1421-452e-94c2-50f99c269354"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.6351791a-991e-4e38-820f-0e9b7ac48bd2"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.4ec53c8c-**************-e32059c73b69"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.64ec5f4b-25e8-4c04-8dd0-e37e19107d66"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.50bb137f-f2bf-45fb-97e7-6b0f02babcb1"},{"outgoing":["2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e"],"incoming":["2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f"],"extensionElements":{"postAssignmentScript":["if (tw.local.errorExist) {\r\n\talert(tw.local.errorMessage);\r\n}"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":422,"y":266,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;"]},"declaredType":"callActivity","startQuantity":1,"default":"2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e","name":"Get Required Documents","dataInputAssociation":[{"targetRef":"2055.aae56053-3bba-40b1-abc9-6a441a93f307","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.IDCRequestType.englishdescription"]}}]},{"targetRef":"2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","declaredType":"TFormalExpression","content":["tw.local.documentsTypesSelected"]}}]},{"targetRef":"2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.documentsSource.englishdescription"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.8e7a637f-d976-47de-be6c-7edeb213b6c5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.errorExist"]}}],"sourceRef":["2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.74cb9e94-45c3-4ba9-8862-d45286d425b6"]}],"calledElement":"1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2"},{"targetRef":"2025.543153b5-4193-4f2e-b425-6e28ca1b6c31","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e","sourceRef":"2025.8e7a637f-d976-47de-be6c-7edeb213b6c5"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.d01b1aa7-d404-478d-88de-fcd9642d63b1"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpUsedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.75c687ec-0b7e-4a2f-8cca-c36d1158a985"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.6f5bee7f-f153-4d3a-8808-8bac0dc2c769"},{"startQuantity":1,"outgoing":["2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98"],"incoming":["2027.8d33f704-4b50-411a-85ee-c1a79d42dd87","2027.d95ec4b1-f687-4369-8380-2018ba3c7cde"],"default":"2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":714,"y":383,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.41430c10-4f07-4a49-8fb5-c2239d716d9b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.8d33f704-4b50-411a-85ee-c1a79d42dd87"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.f8f33e06-5406-4113-8dce-54822e1bc52f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b","extensionElements":{"default":["2027.8d33f704-4b50-411a-85ee-c1a79d42dd87"],"nodeVisualInfo":[{"width":24,"x":815,"y":223,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.210fa6cf-4030-4648-8ee7-277f25404977","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.d95ec4b1-f687-4369-8380-2018ba3c7cde"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.c4e0c349-4d50-4fd6-822b-55d7d65bc3f2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.8e7a637f-d976-47de-be6c-7edeb213b6c5","extensionElements":{"default":["2027.d95ec4b1-f687-4369-8380-2018ba3c7cde"],"nodeVisualInfo":[{"width":24,"x":410,"y":289,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 1","declaredType":"boundaryEvent","id":"2025.585935af-98f9-4134-82ec-3b8462c87ff1","outputSet":{}},{"targetRef":"2025.41430c10-4f07-4a49-8fb5-c2239d716d9b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.8d33f704-4b50-411a-85ee-c1a79d42dd87","sourceRef":"2025.210fa6cf-4030-4648-8ee7-277f25404977"},{"targetRef":"2025.41430c10-4f07-4a49-8fb5-c2239d716d9b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false,"customBendPoint":[{"x":611,"y":481}]}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.d95ec4b1-f687-4369-8380-2018ba3c7cde","sourceRef":"2025.585935af-98f9-4134-82ec-3b8462c87ff1"},{"incoming":["2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":715,"y":310,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.940ccf41-3173-4a25-80ab-46eb7738772d"},{"targetRef":"2025.940ccf41-3173-4a25-80ab-46eb7738772d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 1","declaredType":"sequenceFlow","id":"2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98","sourceRef":"2025.41430c10-4f07-4a49-8fb5-c2239d716d9b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.2c412dce-11d3-4ca3-8f66-16e57e00cc41"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.41d70fa5-1f8e-4f01-85a6-************"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.17938ca5-f8a4-4176-8fc3-9521b8ba0c7f"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.9abc7109-b3c5-4cd5-8e79-ae2e2382b35c"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.d617e20a-afa7-429f-8a93-4b26d495bd23"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"06db35dd-1d46-42b6-85e6-2a0283b56d2a","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"9592408a-d5df-4a95-aec7-d7c14e456f10","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Review Request by Treasury Maker","declaredType":"globalUserTask","id":"1.d5475add-79dd-4468-9671-f08aa8c7c181","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.b9c72c1d-3df4-40b0-8b75-60489434ecdc"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.b6e4bcff-d80c-4147-8eed-a3240ddcd486"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.41a92624-2a12-41f4-bc3c-e22a6fee34e0"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"b6f65630-8ae7-4964-8c3e-97a05ca4a274","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"687a3d30-ba1c-4aeb-86d3-f829e2c8953c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"8444f6e0-b1ba-42e2-8f51-e2f58969fcef","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.8b091ffe-9246-4a69-bde2-94a65cfe8f09"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.f99743ef-4742-46f8-b9d7-80a95385ca25"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.635673ed-7d0d-4457-8c49-9ab426653044"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"intiator","isCollection":false,"id":"2055.c0f0cdee-abca-419e-85ab-c8d79a5209f3"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.fullPath = \"\";\nautoObject.cmisQuery = \"\";\nautoObject.defaultProperties = [];\nautoObject.defaultProperties[0] = {};\nautoObject.defaultProperties[0].name = \"\";\nautoObject.defaultProperties[0].value = null;\nautoObject.defaultProperties[0].editable = false;\nautoObject.defaultProperties[0].hidden = false;\nautoObject"}]},"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.73b6ef38-8a52-4e41-aec3-9813ec6c931b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"\""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.a80db957-70a0-4d66-8021-4079b2a01892"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.9ce1116e-0a35-4ead-9f88-d93ac616bc4e"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8b091ffe-9246-4a69-bde2-94a65cfe8f09</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fdcd0b08-9d5d-453f-9686-a11dc93072cc</guid>
            <versionId>4d839083-4ba9-4d67-be79-5fe356080f23</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f99743ef-4742-46f8-b9d7-80a95385ca25</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a78c7509-98a0-4a82-9bcb-c070f13a04ae</guid>
            <versionId>62421cf9-aff4-4d5b-aed9-c53fc42dc619</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.635673ed-7d0d-4457-8c49-9ab426653044</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>686292ab-6799-4095-ac61-902cd1a22986</guid>
            <versionId>20ee5b09-64d6-4f4e-82bf-dc26c4190e2c</versionId>
        </processParameter>
        <processParameter name="intiator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c0f0cdee-abca-419e-85ab-c8d79a5209f3</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7ef1c4f7-bda9-44a2-9cdc-a8594426783f</guid>
            <versionId>a33d5c08-a01e-4095-b5b1-d58014caa788</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.73b6ef38-8a52-4e41-aec3-9813ec6c931b</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c5166fda-8d0f-481f-964d-6ea527fcc95f</guid>
            <versionId>fcc6dbca-8e3a-4834-8964-ecea62681052</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a80db957-70a0-4d66-8021-4079b2a01892</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>78bc0946-bda6-48be-b96c-3e048bc9ad61</guid>
            <versionId>b4bf986d-5b78-49ae-b830-4f3c99e6fa78</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b9c72c1d-3df4-40b0-8b75-60489434ecdc</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7ede5b9f-476d-4883-a8a1-e62e809ffa4d</guid>
            <versionId>30910668-e16e-4cf4-a437-13c37799eac5</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b6e4bcff-d80c-4147-8eed-a3240ddcd486</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>17b1dc93-8749-4cf0-9384-0afd5b51bb88</guid>
            <versionId>7b853bd4-3915-434b-8958-5134c4bde37f</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.41a92624-2a12-41f4-bc3c-e22a6fee34e0</processParameterId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>55ae0619-1a1a-48c7-ba59-351de43fb809</guid>
            <versionId>727db498-8560-4c5d-b64a-fc4137d13ad6</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.73e15258-cf57-4e66-89ad-3aad4698cfd4</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c5fa69c8-8227-414a-a3f7-e946bb68c704</guid>
            <versionId>67437d4f-cf30-4706-a6a8-cf2ed646fb77</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.41216b2f-a8c9-48fa-947c-0e5dd8ea1c78</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b4d89a02-10b1-4c51-a0e1-9c67a4215085</guid>
            <versionId>01b51403-c0a1-47cc-9e35-3cfee6883124</versionId>
        </processVariable>
        <processVariable name="hasApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2d4a323a-288b-4785-9ed0-05da7d91e21e</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>40cbe186-f73f-4ead-a4c0-91e058feb5c5</guid>
            <versionId>9be30666-7050-4064-9b72-c5ff7536a116</versionId>
        </processVariable>
        <processVariable name="hasreturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.21f08cf7-0936-4628-8b17-90c6e4aa7577</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8adf1b1e-dba2-49b3-9263-68cdfa2787fa</guid>
            <versionId>72d5e880-28da-4a85-a165-577bba757b19</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4a54f005-9975-49a8-9daf-b13206e1976b</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bdd51fa1-2b99-44a7-8d3d-7b202b84f075</guid>
            <versionId>713499b4-c9f3-4cd2-9e46-84d9c01ddf5e</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6351791a-991e-4e38-820f-0e9b7ac48bd2</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>02b636bf-22f1-4f78-85f0-bb75bb288f2d</guid>
            <versionId>38b70807-a33d-4b8a-ad96-259525e62eb9</versionId>
        </processVariable>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4ec53c8c-**************-e32059c73b69</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>49d7d93c-96d4-4390-9a45-f3b9c5de571d</guid>
            <versionId>ecb13d42-6522-4ea9-bd00-a31a59260264</versionId>
        </processVariable>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.64ec5f4b-25e8-4c04-8dd0-e37e19107d66</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dd1b764b-c71d-41c2-a7c9-24df825d10c1</guid>
            <versionId>d616d08f-916f-482c-b37b-b2fe72f01a1c</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.50bb137f-f2bf-45fb-97e7-6b0f02babcb1</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8a0d5ef-a541-44b2-9f67-d9047298118e</guid>
            <versionId>3f4a471c-a701-4f20-a867-8d8444c20993</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d01b1aa7-d404-478d-88de-fcd9642d63b1</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4508e126-90b6-44d8-bdf3-ae76bc23b9d8</guid>
            <versionId>c6abd95e-9d40-44ee-a021-074c6cac14c2</versionId>
        </processVariable>
        <processVariable name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.75c687ec-0b7e-4a2f-8cca-c36d1158a985</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2430fdf5-7f37-4ff4-900c-560446802a9c</guid>
            <versionId>e7f41a34-7c27-4b5a-ae7b-36248d50e328</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6f5bee7f-f153-4d3a-8808-8bac0dc2c769</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>52218e4d-a319-4435-9dac-f81c9aa7eebb</guid>
            <versionId>4ee0efd0-dacb-40ca-8bf2-7606ba0ceb88</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2c412dce-11d3-4ca3-8f66-16e57e00cc41</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9f88d63e-808d-49f8-8b55-41b9e90dcdf0</guid>
            <versionId>777b845e-fd11-47d2-a035-342fae46b5cb</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.41d70fa5-1f8e-4f01-85a6-************</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>947b993d-e7ec-4243-9006-650885ddd289</guid>
            <versionId>9b53a6d1-b8f4-42b4-b9fc-8b96783844fe</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.17938ca5-f8a4-4176-8fc3-9521b8ba0c7f</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3b6c7893-e2a9-4145-9fe3-7c284bbdb3c5</guid>
            <versionId>948f0ea0-142a-473c-9254-e74fe11d67ae</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9abc7109-b3c5-4cd5-8e79-ae2e2382b35c</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>df69365e-de6a-4bab-a303-1b6221fd4272</guid>
            <versionId>5cb5f77d-c60a-4773-8500-6bb54ff39c7d</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d617e20a-afa7-429f-8a93-4b26d495bd23</processVariableId>
            <description isNull="true" />
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b96c35a9-53f0-401c-a5c6-1eab4abfc8e4</guid>
            <versionId>74aea0f3-639b-478e-97de-776337867ffd</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e7a637f-d976-47de-be6c-7edeb213b6c5</processItemId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <name>Get Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e6f73d33-0312-4fd5-bcc9-da0943921072</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60d4</guid>
            <versionId>5ca4906d-61d8-46e7-b8c7-d4ec67a87cd3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e6f73d33-0312-4fd5-bcc9-da0943921072</subProcessId>
                <attachedProcessRef>/1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</attachedProcessRef>
                <guid>643c8f3b-4e4a-4dc9-99e0-39614ffbfd22</guid>
                <versionId>31ddc9ed-c367-4bd9-8ef9-9f438bc97536</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b</processItemId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <name>Update History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c70fd339-1391-40fe-a568-4313bedf81c2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60d5</guid>
            <versionId>8f409122-2fb8-4765-985b-9afad9de8f40</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c70fd339-1391-40fe-a568-4313bedf81c2</subProcessId>
                <attachedProcessRef>/1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</attachedProcessRef>
                <guid>db9d66f8-fa10-4800-85dd-fafd1a781bfd</guid>
                <versionId>8c2d892a-929a-4574-89c3-5918776a1b47</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6d50ff7a-acfa-4537-ba34-0bdfb29aa04d</processItemId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.71b54301-70cd-4f6c-9b80-cd4b7b5625d9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60d6</guid>
            <versionId>b1011547-7089-479b-9c6c-4ca1b4990675</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.139595b5-7e2f-4720-863d-2a257e27eeda</processItemId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.8762332c-dd28-42a9-88cf-26a7a22a3eca</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60d7</guid>
            <versionId>e4730e46-b067-4971-881b-0a311d65e615</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.8762332c-dd28-42a9-88cf-26a7a22a3eca</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9d934399-187c-4533-8ebc-4fcfa0ed809e</guid>
                <versionId>09fa8917-8e77-4347-bff9-9176e7f3566a</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f12d0552-5420-4730-a2e5-72d8e6a1355e</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <guid>bb2d8ab0-ea77-49d0-a27f-a44bca55cbeb</guid>
            <versionId>2d959ff5-b0e2-4969-9318-c69b4df80551</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.2c6dc248-8d8c-4051-985c-ea742222720d</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <guid>60868fb4-f3fc-4e67-a706-74b86d5252f4</guid>
            <versionId>*************-43ec-b91f-a9bbd8a94ea5</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f2e6a5f7-ecab-46cc-aeec-0caa17aa61c4</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <guid>93e8089f-5c6a-4e8c-adff-f7e66e40c514</guid>
            <versionId>d504248b-66ab-40dd-a56e-76e068dededf</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.6d50ff7a-acfa-4537-ba34-0bdfb29aa04d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.9ce1116e-0a35-4ead-9f88-d93ac616bc4e" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Review Request by Treasury Maker" id="1.d5475add-79dd-4468-9671-f08aa8c7c181">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="9592408a-d5df-4a95-aec7-d7c14e456f10">
                            
                            
                            <ns16:startEvent name="Start" id="7f1e25f0-133d-4157-bfd1-c50187cee53e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="84" y="187" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.77ad1cc5-faed-4354-8455-2efd1606864a</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="49decf54-20a8-4b6e-8886-36842a3c76e3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="946" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.cdf41144-06d1-4001-9e4a-068d3abf8b19</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="7f1e25f0-133d-4157-bfd1-c50187cee53e" targetRef="2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3" name="Start To Coach" id="2027.77ad1cc5-faed-4354-8455-2efd1606864a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce" name="Set Status" id="2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="640" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.selectedAction == tw.epv.Action.submitRequest) {&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTreasuryCheckerApproval;&#xD;
} else if (tw.local.selectedAction == tw.epv.Action.sendCBEPending) {&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCBE;&#xD;
} else {&#xD;
	if(tw.local.intiator == "fo"){&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;&#xD;
	}else if (tw.local.intiator == "Initiation") {&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;&#xD;
	}else{&#xD;
		tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
		tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161" targetRef="2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b" name="OK To End" id="2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f" name="Initialization Script" id="2025.a6025006-3452-4608-a935-9732389342a6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="258" y="164" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b5d56c3a-caaf-40e6-a781-6d54ad228530</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
tw.local.errorVIS = "NONE";&#xD;
&#xD;
if (tw.local.treasuryComments == null) {&#xD;
	tw.local.treasuryComments = tw.local.idcRequest.appLog;&#xD;
}&#xD;
tw.local.action = [];&#xD;
tw.local.action[0] = tw.epv.Action.returnToInitiator;&#xD;
tw.local.action[1] = tw.epv.Action.submitRequest;&#xD;
tw.local.action[2] = tw.epv.Action.sendCBEPending;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a6025006-3452-4608-a935-9732389342a6" targetRef="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6" name="To Review Request by Treasury Maker" id="2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Review Request by Treasury Maker" id="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="422" y="164" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.3e81eaaa-6c0a-4863-aac8-dfb08007478f</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.897d9dcb-f960-4d5e-9960-69a591c4b36a</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.29354488-1f8a-4585-ad1d-ac8be98e9699</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.27ba55f7-257b-43dd-a041-3857fb606991</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>dc125744-c18b-4123-8abd-3d6e545b8a7e</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>85d002f6-cf80-423c-8012-e934182fea0f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9e5d90ac-4ea3-4d91-8389-c926f72aaff4</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1b3c975f-03d4-43df-852f-4f1b7c2833d5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d42dd014-3c51-493c-854d-32da0810dd62</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e5bf4942-3787-4f95-8e29-2b181c6cf2eb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2b6ba676-fa72-434b-8964-d2708c82213f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>52eed5ef-4535-4903-83ed-bbe812e47d68</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3e2f419e-5a98-46ec-883a-e1f9ed647a5f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>67cbca25-b226-4671-85b5-978044472dc7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>48260233-a490-4351-89d1-576a055959f0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bfe9e6b6-0c9f-4c9e-8f1a-e0781c3af53f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>cecd8a34-31f6-4343-8eda-48a317a10b11</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5e290dce-d3b0-4d34-89de-ace8d66661f8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasApprovals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8234d75b-6a04-447c-87b2-639ffa611d6f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasreturnReason</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9dcc2280-f080-412e-8988-3b891ad8d087</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b6c3bc46-4e99-433a-8c3f-3a68bfe5567a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5ef21c5a-0570-41c8-809d-4f684afd2c74</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f8f74af1-a8eb-458f-87fe-2c2f6ad5f50b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b196487d-862c-467c-834c-d129886df75a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>bf301570-4328-47b0-81ac-143b86e15c48</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>871f64d6-6f34-4933-8638-d1dca2eacf97</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f220bcad-f67d-474d-8bcc-577f86e74f96</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b310ef1c-5b32-40dd-84bf-6399072240f1</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>8b07f751-fd78-42e2-835e-98aa246ef7f4</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>896bf24c-6f28-4783-8a78-ba78f84f1c9a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>d653e464-4646-493a-8255-42a40d062312</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>3bd74e47-48f2-43dc-84c5-15afd8ad4469</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8bf3f3ef-1fbd-4a99-84f9-432126b5ee7b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5b0cc6f2-25a6-4eb0-8f16-c70327be41fa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>93175e97-fd45-4893-8965-7f0e0d307c32</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4a9f3f63-fc45-4d8a-8b52-021b84aa6387</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3ffef35b-37e5-43d1-87db-425f972eeb0e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b1858223-e0a7-4736-8370-6c84d82eb657</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>7da5d45c-bb9c-40f2-842a-67e48bb22a08</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1f58cd41-79b7-4fda-8295-9908cf73fca8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d5f0dbf0-9ea8-4508-82cd-7e93e63daaf4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cee0c054-7642-40bd-81ea-6ff27a72469b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2d62b565-d3c5-4a11-8658-2e711a1e441f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2df5e5b8-ec5c-4a78-8123-f7a1128023df</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>866be30b-ece8-40f0-85e4-8a1e5d22d5a8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7f44ae8f-f2ca-4ba1-8915-9329fd3a562d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>458abd43-99c9-4842-8798-1497d9b18faf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>121390b8-8a12-4083-8ba6-5ea724326ecb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>65fd3314-9dca-47b8-813a-b6521fd50ca6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>no</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cf6eb3fd-fae5-4c71-8721-1685de722ca7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>99479ef8-40ab-4c83-8dd6-5b6814cfb673</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6370f37c-0663-4efc-886b-5a9aac9a5b05</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a73b7647-bc21-456f-8448-eade609c7951</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9532cf03-4f99-4974-8c78-c94d517ef2c5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c3103ddc-7a34-4dd0-879e-143d8df512a0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f2e4ae75-e073-4d98-8ca4-fb1c9bf419ce</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4603b864-97f3-4bf5-8f4e-9a1d05025c9b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f3e2fa96-a1c7-4c1e-82dd-fce184612e0e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9609fc61-e7f3-49eb-8df2-4eb8aa4fdcc0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>97d3a033-fb21-48f7-8c47-1ee16e00eb12</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e87f3d5b-dbdf-48a5-848d-e08e26232312</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>809c0a3d-8234-4e16-836f-c4430812c352</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>59d762ed-ca54-4aa1-8d23-ab9dbbbf8859</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c662e041-e7ef-4934-8733-450d41737803</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>947978e8-00b7-4415-8c8e-923081c00c1b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.tmpUsedAdvancePayment</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5efe8bbc-24ce-4d62-8fca-4534f997e0cf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b9b128bf-6ce9-4977-86e2-c2a74cf5194d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d33b4375-f834-4032-877f-51187440d856</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b98610a5-3635-4431-8e28-e22333044736</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>304ff02d-fd9d-41ed-8d27-b481f296ed04</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>attach1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6b1f2029-2d84-4f68-852d-12c7ad7ee74b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cfb5ae8f-5939-4877-86dd-cf40eb2ed909</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d4a18a12-7172-4c9d-8812-7dbbc69d519f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ced0e5a9-81b0-46e4-85b8-e7b5044d2020</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ca502351-e225-4941-8c86-b9291111329f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>448d0fd2-bff5-4dd4-87db-619978bac5e1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>*************-48f6-84d6-ac4b35d4ba8e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e7b12440-6807-4bb1-817a-10bd1f04514c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>067119a9-46f5-4ee0-8ef4-009c1ae28ca5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>c64d2bd8-5020-40e9-85f4-a4c787777445</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b8163995-3ca6-4407-8764-5807098f0b64</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1a829d28-6f6d-4cf8-8882-89ba38a791fe</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>490a61b2-6650-402f-87fb-973150260d03</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>65f00479-20d3-4b3c-859e-7f410c451d92</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.treasuryComments[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.35aa8594-8aac-40c7-8979-fca78ea0d077">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="435" y="39" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.897d9dcb-f960-4d5e-9960-69a591c4b36a</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.27ba55f7-257b-43dd-a041-3857fb606991</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.897d9dcb-f960-4d5e-9960-69a591c4b36a</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6" targetRef="2025.35aa8594-8aac-40c7-8979-fca78ea0d077" name="To Stay on page" id="2027.27ba55f7-257b-43dd-a041-3857fb606991">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="4c091836-5b8d-4037-b8c9-9efb16d634f4">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6" targetRef="2025.8e7a637f-d976-47de-be6c-7edeb213b6c5" name="To Service" id="2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="fef1ed12-147f-471e-ac8f-c8fb1aa31ef7">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.35aa8594-8aac-40c7-8979-fca78ea0d077" targetRef="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6" name="To Review Request by Treasury Maker" id="2027.897d9dcb-f960-4d5e-9960-69a591c4b36a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.cdf41144-06d1-4001-9e4a-068d3abf8b19" name="Update History" id="2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="780" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b23e4f1c-bd0f-4c66-bf73-bf02dbaf50ce</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.cdf41144-06d1-4001-9e4a-068d3abf8b19</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.648598d0-2039-40d4-b60b-3753a273a378</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Treasury Maker"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.65675974-9215-43be-8dce-3b75511a591d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8fcdef92-a110-407f-aff8-5693f497f953</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fce152d9-1c42-43bc-8bff-44f6b45aba67</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b" targetRef="49decf54-20a8-4b6e-8886-36842a3c76e3" name="To End" id="2027.cdf41144-06d1-4001-9e4a-068d3abf8b19">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b5d56c3a-caaf-40e6-a781-6d54ad228530" name="Set Step Name" id="2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="121" y="164" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.77ad1cc5-faed-4354-8455-2efd1606864a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b5d56c3a-caaf-40e6-a781-6d54ad228530</ns16:outgoing>
                                
                                
                                <ns16:script>//tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
//tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
//&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f234a3d6-d7c8-461e-8f47-fed2d51595c3" targetRef="2025.a6025006-3452-4608-a935-9732389342a6" name="To Initialization Script" id="2027.b5d56c3a-caaf-40e6-a781-6d54ad228530">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.73e15258-cf57-4e66-89ad-3aad4698cfd4" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.41216b2f-a8c9-48fa-947c-0e5dd8ea1c78" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasApprovals" id="2056.2d4a323a-288b-4785-9ed0-05da7d91e21e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasreturnReason" id="2056.21f08cf7-0936-4628-8b17-90c6e4aa7577">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">true</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.46f53795-f02c-40d2-8611-900d1a50e4ad" name="validation" id="2025.543153b5-4193-4f2e-b425-6e28ca1b6c31">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="422" y="368" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.46f53795-f02c-40d2-8611-900d1a50e4ad</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
//		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
////validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
////----------------------------------basic details------------------------------------------------------------------------------&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
////----------------------------------------app info------------------------------------------------------------------------------------&#xD;
//mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
////------------------------------------financial Details fo -------------------------------------------------------------------	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,"tw.local.idcRequest.financialDetails.beneficiaryDetails.name");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank" );	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,"tw.local.idcRequest.financialDetails.beneficiaryDetails.account" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber" );&#xD;
//if (tw.local.selectedAction == tw.epv.Action.submitRequest) {&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.executionHub.code,"tw.local.idcRequest.financialDetails.executionHub.code");&#xD;
//}&#xD;
//var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;&#xD;
//if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
//	addError("tw.local.idcRequest.financialDetails.cashAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//&#xD;
//}&#xD;
//&#xD;
//var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;&#xD;
//if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtSight" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//}&#xD;
//for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {&#xD;
//	&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentDate");&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentAmount");&#xD;
//}&#xD;
//-------------------------------action-----------------------------------------------------------------------&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToInitiator || tw.local.selectedAction == tw.epv.Action.returnToMaker) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.returnReason,"tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
//if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
//	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
//}&#xD;
&#xD;
//if ((tw.local.idcRequest.approvals.CAD==true|| &#xD;
//    tw.local.idcRequest.approvals.compliance==true ||&#xD;
//    tw.local.idcRequest.approvals.treasury==true))&#xD;
//{   &#xD;
//    if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
//       addError("tw.local.selectedAction", "Please uncheck Approvals");&#xD;
//    }&#xD;
//}&#xD;
//else if (tw.local.selectedAction == "Obtain Approvals")&#xD;
//{&#xD;
//    addError("tw.local.selectedAction", "Please check Approvals");&#xD;
//}&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.543153b5-4193-4f2e-b425-6e28ca1b6c31" targetRef="2025.a407d111-1421-452e-94c2-50f99c269354" name="To Have Errors" id="2027.46f53795-f02c-40d2-8611-900d1a50e4ad">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.4a54f005-9975-49a8-9daf-b13206e1976b" />
                            
                            
                            <ns16:exclusiveGateway default="2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f" gatewayDirection="Unspecified" name="Copy of Have Errors" id="2025.a407d111-1421-452e-94c2-50f99c269354">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="547" y="387" width="32" height="32" />
                                    
                                    
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.46f53795-f02c-40d2-8611-900d1a50e4ad</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.29354488-1f8a-4585-ad1d-ac8be98e9699</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a407d111-1421-452e-94c2-50f99c269354" targetRef="2025.63a8c88f-a86a-4f2d-b90c-2760c01d3ec6" name="Copy of yes" id="2027.29354488-1f8a-4585-ad1d-ac8be98e9699">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a407d111-1421-452e-94c2-50f99c269354" targetRef="2025.f6fc0fcd-c134-44c6-acd5-c35e05d02161" name="Copy of no" id="2027.124ed5ae-6489-444b-8cc9-3d0dc0ebf97f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.6351791a-991e-4e38-820f-0e9b7ac48bd2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="documentsTypesSelected" id="2056.4ec53c8c-**************-e32059c73b69" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.64ec5f4b-25e8-4c04-8dd0-e37e19107d66" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.50bb137f-f2bf-45fb-97e7-6b0f02babcb1" />
                            
                            
                            <ns16:callActivity calledElement="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e" name="Get Required Documents" id="2025.8e7a637f-d976-47de-be6c-7edeb213b6c5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="422" y="266" width="95" height="70" color="#95D087" />
                                    
                                    
                                    <ns3:postAssignmentScript>if (tw.local.errorExist) {&#xD;
	alert(tw.local.errorMessage);&#xD;
}</ns3:postAssignmentScript>
                                    
                                    
                                    <ns3:preAssignmentScript>tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.7d25ce28-c86f-42e3-abfc-feb8b501e41f</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.aae56053-3bba-40b1-abc9-6a441a93f307</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.IDCRequestType.englishdescription</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f">tw.local.documentsTypesSelected</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.documentsSource.englishdescription</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.errorExist</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8e7a637f-d976-47de-be6c-7edeb213b6c5" targetRef="2025.543153b5-4193-4f2e-b425-6e28ca1b6c31" name="To End" id="2027.0bfefd16-9f72-47e7-bd26-1c013e0c716e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.d01b1aa7-d404-478d-88de-fcd9642d63b1" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpUsedAdvancePayment" id="2056.75c687ec-0b7e-4a2f-8cca-c36d1158a985" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.6f5bee7f-f153-4d3a-8808-8bac0dc2c769">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98" name="Handling Error" id="2025.41430c10-4f07-4a49-8fb5-c2239d716d9b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="714" y="383" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8d33f704-4b50-411a-85ee-c1a79d42dd87</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.d95ec4b1-f687-4369-8380-2018ba3c7cde</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.e8f0fb46-a294-4556-849a-b4b0f4574b8b" parallelMultiple="false" name="Error" id="2025.210fa6cf-4030-4648-8ee7-277f25404977">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="815" y="223" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.8d33f704-4b50-411a-85ee-c1a79d42dd87</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.8d33f704-4b50-411a-85ee-c1a79d42dd87</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.f8f33e06-5406-4113-8dce-54822e1bc52f" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.8e7a637f-d976-47de-be6c-7edeb213b6c5" parallelMultiple="false" name="Error 1" id="2025.585935af-98f9-4134-82ec-3b8462c87ff1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="410" y="289" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.d95ec4b1-f687-4369-8380-2018ba3c7cde</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.d95ec4b1-f687-4369-8380-2018ba3c7cde</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.c4e0c349-4d50-4fd6-822b-55d7d65bc3f2" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.210fa6cf-4030-4648-8ee7-277f25404977" targetRef="2025.41430c10-4f07-4a49-8fb5-c2239d716d9b" name="To Handling Error" id="2027.8d33f704-4b50-411a-85ee-c1a79d42dd87">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.585935af-98f9-4134-82ec-3b8462c87ff1" targetRef="2025.41430c10-4f07-4a49-8fb5-c2239d716d9b" name="To Handling Error" id="2027.d95ec4b1-f687-4369-8380-2018ba3c7cde">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:customBendPoint x="611" y="481" />
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.940ccf41-3173-4a25-80ab-46eb7738772d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="715" y="310" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.41430c10-4f07-4a49-8fb5-c2239d716d9b" targetRef="2025.940ccf41-3173-4a25-80ab-46eb7738772d" name="To Stay on page 1" id="2027.07a9f0fc-8faa-4e3d-8a23-52e439e61e98">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.2c412dce-11d3-4ca3-8f66-16e57e00cc41" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.41d70fa5-1f8e-4f01-85a6-************" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.17938ca5-f8a4-4176-8fc3-9521b8ba0c7f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.9abc7109-b3c5-4cd5-8e79-ae2e2382b35c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.d617e20a-afa7-429f-8a93-4b26d495bd23" />
                            
                            
                            <ns3:htmlHeaderTag id="06db35dd-1d46-42b6-85e6-2a0283b56d2a">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="b6f65630-8ae7-4964-8c3e-97a05ca4a274" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="687a3d30-ba1c-4aeb-86d3-f829e2c8953c" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="8444f6e0-b1ba-42e2-8f51-e2f58969fcef" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.8b091ffe-9246-4a69-bde2-94a65cfe8f09">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.f99743ef-4742-46f8-b9d7-80a95385ca25">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.635673ed-7d0d-4457-8c49-9ab426653044">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="intiator" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c0f0cdee-abca-419e-85ab-c8d79a5209f3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">""</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.73b6ef38-8a52-4e41-aec3-9813ec6c931b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.fullPath = "";
autoObject.cmisQuery = "";
autoObject.defaultProperties = [];
autoObject.defaultProperties[0] = {};
autoObject.defaultProperties[0].name = "";
autoObject.defaultProperties[0].value = null;
autoObject.defaultProperties[0].editable = false;
autoObject.defaultProperties[0].hidden = false;
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.a80db957-70a0-4d66-8021-4079b2a01892">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">""</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.b9c72c1d-3df4-40b0-8b75-60489434ecdc" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.b6e4bcff-d80c-4147-8eed-a3240ddcd486" />
                        
                        
                        <ns16:dataOutput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.41a92624-2a12-41f4-bc3c-e22a6fee34e0" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.94a97d7a-6ac6-4a24-9599-5c7b58e65f6c</processLinkId>
            <processId>1.d5475add-79dd-4468-9671-f08aa8c7c181</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6d50ff7a-acfa-4537-ba34-0bdfb29aa04d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.139595b5-7e2f-4720-863d-2a257e27eeda</toProcessItemId>
            <guid>e61a30d2-720b-4d12-80c2-5b25c651a7fc</guid>
            <versionId>1f51ee1d-2522-4c2e-a75a-00fb889b65c7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.6d50ff7a-acfa-4537-ba34-0bdfb29aa04d</fromProcessItemId>
            <toProcessItemId>2025.139595b5-7e2f-4720-863d-2a257e27eeda</toProcessItemId>
        </link>
    </process>
</teamworks>

