<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.848ab487-8214-4d8b-88fd-a9cac5257791" name="Financial Details Trade FO">
        <lastModified>1692189561816</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;3554cac7-3f4e-4ae0-8c3e-afececd66a5f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cf02bbbf-e862-4197-80d8-0c884fa50932&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Financial Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fcf89807-7229-4b82-8467-6ad5aeb6f871&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd223d0f-7c4d-4a3f-89ad-1eb11b5f731d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;990387ff-c09e-4926-8542-66d9920e86cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ac6d485-8901-4ace-89d2-7d342567132b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;160126d5-0821-4322-8065-d63cd0edf3b1&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0eb45012-7d98-4a3a-80cd-b847a84a210a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee0af62d-c114-430e-82f9-fdfb97184d09&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cf63f715-f9af-4343-8725-b6a3483ebfad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e4697196-81eb-465b-8819-87bb47e27cfc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;d0229005-8155-49c2-853c-81e8485680d8&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;419d8c88-d782-43b8-8f1f-3af277d9d29e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51729393-ab6d-431b-8d07-046e2d9bd097&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8b93cb0-7ca7-4bd3-8453-83809c5145c9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8a55de24-e084-4ded-828f-0810b008ee64&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;a3c3f339-6aa6-47a6-8dfe-f6093938993c&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4fa6368a-d9db-48ec-8766-78022209ced9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CashAmountDocumentCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd7bc79b-1be7-439d-bd1c-391ecb7233ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ffdf9895-da21-400f-8afa-a8fe0fd5151e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CashAmountDocumentCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a74e77e4-c96d-42c3-8507-e5b6de34bd44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;450bee5c-6132-41f4-846a-8cc014a33263&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be1b3e6a-9b0b-4e37-876a-e504b7585916&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
	
}else{
	me.setValid(true);
}
view.isEqualPAybaleByNBE();


&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ddafc8ee-f34f-4b13-8046-8d7dfccb993f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.cashAmtInDocCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;54cf992f-3c06-41de-8b5f-bf27cc7a1a16&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;FacilityAmountDocumentCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01356eae-152c-4a05-a8c0-617d911d84a8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab3ccd38-74e3-4027-897f-26ad0f7a05e5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.FacilityAmountDocumentCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2178ab4e-4f1b-42c2-8785-b73310c3fd50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc420a36-7fd9-4126-8c0e-679f78533e97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e9a6295e-9660-4182-8515-3ed5776531e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;numericFormatting&lt;/ns2:optionName&gt;&lt;ns2:value&gt;auto&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87736616-16ce-4265-8dd3-c8f8d2bd5453&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.isEqualPAybaleByNBE();

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d05aefd5-c6f4-4554-8506-73a70cebd41e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.facilityAmtInDocCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a0c6610a-cf01-4a9e-861e-8ad396b83ebf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountPaidbyOtherBanks&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2abc2165-0a1c-4ce7-8eb0-342f1c4b6a9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;460131ad-6660-416d-8a17-b9341ccb8d81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountPaidbyOtherBanks&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0a382359-2b0f-467b-83ea-600e3e9269e0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0139cb18-710b-489d-89ba-c153c76f72df&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6ad7c76-082f-4495-8d5f-9947dc1d1132&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce7d0562-8e9a-4132-8e18-387d9d87cada&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.calculateAmountPayablebyNBE();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amtPaidbyOtherBanks&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3eab2586-26ae-455a-8fad-a5cfe222e476&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountDeferredAvalization&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;145882db-2d6f-43d4-b5b7-67d312bf7ff7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9c87a302-7b64-480b-8bf7-e41887988c2e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountDeferredAvalization&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8589308-1d4c-40e5-87d8-8845d113e237&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be3df371-35a8-42b5-8eba-d88a6ca2852f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d093dfc1-2f16-4ef4-8028-aabfff8804e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.LimitsVisible();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54e48e16-2b52-4300-8bec-d288f3cf99c6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.avalization();
view.checkPartiAlavalization();
view.LimitsVisible();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amtDeferredAvalized&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d27fe6ba-955d-4b1d-8d69-7bb41713443e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountPayablebyNBE&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86e1b810-291e-4647-9cab-2d85bab24137&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;523f7bb0-24d2-40c3-837f-95eb5bf1dbf8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountPayablebyNBE&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68e84160-71cf-4685-841f-13ba0d4ae971&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17a3e5bd-8da2-4f2e-84b2-2b3d2f48214c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a53b2a7e-2851-4a82-8ea7-ce874ff82352&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hideThousandsSeparator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e923f8b6-4a2d-4ff5-8ee8-9d5babed5fde&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb062533-cd15-441e-8319-feaa5aee87e9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.avalization();
view.isEqualPAybaleByNBE();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amtPayableByNBE&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c57824db-31d8-48d9-8507-bdc36d370493&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5bdf95b-1a54-4f45-8a65-3dcb588148d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a267491-4927-4e57-8c4b-d23ee7eae785&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;946a403f-303f-4887-8690-7ea5e059da4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;50b7ee8f-8d6a-4a78-8f86-7d8409f0ff36&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e2f15355-de80-4bae-8291-cebd899cd66b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CashAmountNoCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ffb5b9f-06b1-4e92-abc1-584051eb7c0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5172e1c-fa13-472d-880f-654ea04ee5c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CashAmountNoCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebecd5e7-015b-4295-8ff6-840aa7b0fc92&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;26edccdd-a2c9-4e47-8a99-f1bd4535337a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0a2a9b79-cd91-4526-89b2-515c9a862c7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}

view.isEqualPAybaleByNBE();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6652f2a4-332e-46d2-824f-720e4a2b5656&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.CashAmtWithNoCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1c7330e1-82f6-47d2-8a76-a0a9fc4f3d69&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;FacilityAmountNoCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40ac6105-e271-474d-951f-fb54ab201f50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a86c3f3c-982f-4398-8fd2-723f451eade5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.FacilityAmountNoCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd5a854f-daa5-4aa5-8dad-c5aefa942aa7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;13e7d629-db4f-4e64-80df-bef06ecd4a0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f93763dc-4662-4c12-8f22-bca028108702&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.isEqualPAybaleByNBE();

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d24082e1-7d78-4d81-8167-2b9cf8b2e1c9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.facilityAmtWithNoCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6bba93c7-1dd8-4bec-8637-eca051ac6cd4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountSight&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;656ee5c8-9371-4034-a287-79c6f27b2916&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3295448a-ba50-4bb2-8db9-3e6a286a165f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountSight&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0cec0ca-e38c-4e76-8032-9f06ef070539&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb365708-1f91-4817-8e95-7a14f103bc00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ed02697e-e1c5-47cc-8b77-fdef96d2a118&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c53d3d51-2b47-40f1-81d9-715f7adff820&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.avalization();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amtSight&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ad9e55c7-ae4a-4135-82b2-f7421217063d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;MaturityDateoffirstinstallement&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;90c4fb5b-9a5f-4917-a87e-efd26f293d1e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;584f57d1-eb1f-4342-815e-baf9b2326e03&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.MaturityDateoffirstinstallement&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f18c1ee5-7c2a-407f-8738-9715367cde9a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dfc8f9dd-a02e-4a93-8543-df55e67dff26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;19bdc0b3-81de-42dc-8f60-ef6e6e629781&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d3c48bcf-f90d-4e6d-81b1-e396134c2017&lt;/ns2:id&gt;&lt;ns2:optionName&gt;highlightToday&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9fe0f2da-a7a5-4c56-8423-a0187528bbcd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;16d949ce-28ca-4965-81a6-fa3fda03dba6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.firstInstallementMaturityDate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b13b77df-7f0b-45d8-8f8f-f783732e7407&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;90265dd9-c862-4256-8c58-7408b0ee2a49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;41c2b15a-3ea0-4f9f-8f10-a3206a320977&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47293184-d2bf-47ff-8a48-efd766068e87&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;eca3dbf9-22f2-4200-8a0c-f6e8af38866f&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1f68ca25-803f-49cb-82ca-591bb7e59e4a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Discount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4cf25d02-471e-43ec-af81-f6452a0388bd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9c6f0e72-4049-4ed7-8f43-b0663641f932&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Discount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c36e2234-c6d2-4a67-828b-7ceed26970cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b2061b7-b1ce-4831-83ba-1c92dace8dd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59eb8e5b-c80a-4874-8625-660f1b14ee8a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;837b921c-2fb6-416f-8ab0-334cce6b29d6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
	
	
}else{
	me.setValid(true);
}
view.calculateAmountPayablebyNBE();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.discountAmt&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;13fff1d2-d1d6-4901-80c8-7805c2cb9fa7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountAdvanced&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;98ba506d-ce8e-46cf-88b2-424b2fc133b3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21ca7647-c7e8-4a49-8ea7-0b31e8803aa3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountAdvanced&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bed33051-e7f9-4763-8826-ae82ccf0e266&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;98d85498-d9a9-431b-8341-73e4ef1a890b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d37b673a-c93f-48e8-8c96-7e0dab68cafd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;39f2b51c-b4ea-444c-80fe-39db4336dd2e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.calculateAmountPayablebyNBE();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b810687-d963-420a-8ad1-cf50dfadb8f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.haveAmountAdvanced&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amountAdvanced&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;94d03ee8-4d56-4ee7-8fd7-a9accdec9d1e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountDeferredNoAvalization&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2532154c-d816-4c23-a837-53ba4be98e6e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a3e4f70-5263-47cb-88d8-d8812286d030&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountDeferredNoAvalization&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4438841c-1c8c-44f4-89f7-c9427225dd2e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50d18bb6-ad6f-431f-81fd-1bb81c404d21&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50238fb3-cc63-45e4-80b9-7186f113d04b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92166f27-8f33-4dd8-85ae-1d71d958f7e7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0.0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}
view.avalization();
view.checkPartiAlavalization();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.amtDeferredNoAvalized&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;409b701c-afdb-4bbb-874c-b1b0dbd98a86&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;NoofDaystillMaturity&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;10e92ff8-3ef3-43eb-8742-2cb29c9d2daa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.NoofDaystillMaturity&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d4294ed-70bc-4ee2-8808-034bbca22108&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;674d4d27-24f7-4dfe-8e0e-cefbe76589fb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d5fc698-31be-4e9b-8822-72a9263426df&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()&amp;lt;0 || me.getData()==null || me.getData() == undefined){
	me.setData(0);
	me.setValid(false , "Manadatory");
}else{
	me.setValid(true);
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.a6946c4c-f73d-4ced-9216-90018985ca96&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.daysTillMaturity&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;43ab6e51-e66c-4df9-83aa-a96c9727b2c5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;paymentTermsTable&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5897377b-94c5-4925-87ce-2f97dc95209f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Multi-Tenor Dates&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ffbc5a49-357e-482c-8b23-63c3eec0ede1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8834969b-e514-4107-8fae-c705c4866d9c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8bb4b541-30b4-44ff-8366-93d0423dd443&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bcbf735b-4888-456c-822b-ec6cf1ce04f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;397e6c15-0ef6-498c-8d5f-7c9576ba300a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aaee6d42-cb5d-442e-8413-c98b3365feb8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01edd1bb-335f-4e69-8e13-9159674ad194&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.havePaymentTerms&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e2327751-b327-4ec3-8b75-36542a0903c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getRecordIndex(item) ==0){
alert("You can not delete this row");
return false;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.paymentTerms[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;56ede9a4-cb73-4e15-8a8d-9a6e158cdae8&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;47f389f1-6481-4e6d-85fe-924dd2c2e967&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Date_Time_Picker1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;de476c12-e3cc-47de-8e4a-0bc7570b82d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InstallmentDate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e76cfadc-dcd2-48d9-823d-d213afff2e82&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ea33fa6-9fe5-4efd-8b0e-39543d731db3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea34fc73-3c7c-4f79-8a0a-7d384c428c74&lt;/ns2:id&gt;&lt;ns2:optionName&gt;format&lt;/ns2:optionName&gt;&lt;ns2:value&gt;dd/MM/yyyy&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ec3a8e2-855f-4560-8327-8fad48d9765e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81a3ce1a-67b6-482e-8ada-5879ae8e5427&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fec8761-7526-4c8b-8b3c-c8a3ba1dc908&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//var date = new Date();
//me.setStart(date);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.paymentTerms.currentItem.installmentDate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c4b92905-5fbb-434c-8a39-11a21822681b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Decimal1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5f6374f-cb31-484d-809a-15bcf7ba69b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;543b0486-ab77-4411-8658-c292af8e9b65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InstallmentAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aafda566-9b00-4f3c-8d35-6fc09c569533&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e93bf1f-5ee1-4cb8-8bbd-a6f75eca2d77&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1a49184c-6a17-454f-88b6-cf31b411146d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.paymentTerms.currentItem.installmentAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;16c1899a-518f-490d-8572-ca2474471ee1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e0394182-82e2-4e2d-8b1c-8d33e6ce513e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 7&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e45f287-5ce1-45c8-819c-cc66843d71d2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa87ac9e-c980-496e-84dd-158f59ef7ec9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;1ae1bdd4-dac8-43e3-8357-3646fc24478b&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8c56e2f5-**************-466c9075b054&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d355bfa0-d079-4d26-8c81-b2dfcea2c2e4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ae99db1-f244-428c-8a4b-a1a5bb615026&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9803f8f2-1d8c-487d-8311-bc6d0fb25742&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;15549291-d2e3-4f4f-89a3-c4fc2b427080&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;faea1483-f0b0-4cbf-85a2-66ffecd582d3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b7b6204-57a6-4e40-81a5-81fa94272fe4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34143f23-06a7-434d-8637-d689f4497309&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b7fc772-3ba2-4de9-8de7-8a7a607f54cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;*************-458d-8295-7b428bad06da&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f90778fa-52b4-487b-8b04-d2a15a50ade7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BeneficiaryName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47fc92de-7f7a-42bc-9b4a-f51b0b538d58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dcd6d0fb-cdce-4296-8cf3-ebdb1212ca46&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b6dbf6b-ef64-454c-875e-c7e6d087b4f1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86a1b822-5ff9-4a13-8f4f-3c6c1b5aa157&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;942e98eb-c648-4e77-8893-f69585edeab6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 80)
{
	me.setValid(false , "max lenght is 80 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a16e8083-a032-402a-8b44-57541426703e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setbeneficiaryDetails();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.beneficiaryDetails.name&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;177a1522-a993-40a3-80bf-763c95e74139&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BeneficiaryCountry&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97ef6945-72fd-44e1-87f0-8a8570e17f4b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryCountry&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49b0693d-3160-4810-8b48-f9a76769b7fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c8c92409-50cd-43fe-8cb0-7e69a8f58ea9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;166a792c-c223-4ce1-86ff-aa656d34a893&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1081b05e-6ff3-4b2b-81fb-877d594747ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;65c2005e-b190-41c1-83e0-aa4fd17efd26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9dff6165-cb17-47b4-8bb8-913a5b0b9eb7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.b24f815d-f98b-4f2e-9dbe-f7601985749f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0776a770-98e6-46bd-8bee-f1a831155dc4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setbeneficiaryDetails();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;afe2014b-ec13-4ca7-815b-c34564f778d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.beneficiaryDetails.country&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;30c618af-ac01-47a1-8368-423f6ed9d483&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;TradeFOReferenceNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aead734d-b07e-4a44-9fce-2e6078c6e584&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8d3450c1-c422-4c4f-8da0-67e9ba857f86&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.TradeFOReferenceNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f68077e3-2e22-42c4-8b2e-0ae71fe4b735&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;07abf366-095b-4222-85e5-3f39f3f4dc25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5f4a8fc-68a4-445f-83c8-5a9d66a6effb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;181d8f3b-4255-45b1-8d98-422f8f895900&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 16)
{
	me.setValid(false , "max lenght 16 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;060563bd-63a0-422f-8856-dabb8a7439a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.haveTradeFOReferenceNumber();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a3525867-41fa-4c0b-80b8-0fc4696ac050&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//if(isNaN(Number(me.getData()))){
//	me.setValid(false , "must be digits");
//	me.setData("");
//	return false;
//}else
//{
////	me.setValid(true);
//	return true;
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.tradeFOReferenceNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6c5e3a77-544a-4a86-8ad9-40c59d276927&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1315a1a9-ee9e-40c1-81ba-d8f85c1f2e66&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c182de3-f0e7-4901-8ffc-5ee6c9ad952d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f602c720-0d36-4e65-8887-ba470a5537bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;2553d9a4-190a-4d55-89d5-ddeb51e61d02&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;316169ca-e18e-4a79-898b-221236b2de1d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BeneficiaryBank&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;850d7910-f8d0-4fc7-b464-9cc08ead2b4a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce13b442-eb32-4a27-81a7-af1a2a22c66f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryBank&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b216821-9ca5-4b5b-852c-cf25caada486&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58beaf0e-6d5f-4dfe-8a0c-2d5f65f6ff25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4551678-3bac-4677-8ec2-5a6277cfa7c5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 80)
{
	me.setValid(false , "max lenght is 80 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c79fd8ad-5485-4321-864f-da86779073ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setbeneficiaryDetails();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.beneficiaryDetails.bank&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;********-879d-45d2-8b60-363392eef61c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;NumberOfFundUnit&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1061e04d-e3f3-42a4-87a2-928aae16ecd0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-f0ae-4c8e-8b89-e87cfb9f9dc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.NumberOfFundUnit&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f999a3d3-2af3-4a51-8a73-0f128d8af3fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6251c56b-dca1-4c66-8c3b-fc25b1b9b982&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;680d834f-e49f-4502-8308-629f0bafe458&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 16)
{
	me.setValid(false , "max lenght 16 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d7068df9-38ce-4de4-896e-58cdf658b48f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//if(isNaN(Number(me.getData()))){
//	me.setValid(false , "must be digits");
//	me.setData("");
//	return false;
//}else
//{
////	me.setValid(true);
//	return true;
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.tradeFinanceApprovalNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6504d95d-f365-462f-8a70-1e783858c8c2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ExecutionHubCode&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb9958b3-475e-4169-807d-1e59e5cda405&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ExecutionHubCode&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;57550a25-**************-1d8d16a3210e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;24513664-f421-498d-8e9c-17bd77a25d1a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;90ea279d-8281-468a-89f5-488acbf534a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2cfbe195-6b20-41c0-81f6-16ac70cdac14&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;030862c4-fe9b-491a-881a-60eba030b1ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"arabicdescription","optionDisplayProperty":"code"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92471b74-209a-4c25-881e-29b5093cfe83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"arabicdescription","optionDisplayProperty":"code"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea055a3c-5c7b-4bd3-8099-924dc231941d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Execution_Hubs&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;faa57492-9a6a-4673-8d7f-5bdb61205aec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.executionHub&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;59f4759a-8426-41e5-8d30-efe16ac92787&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d28d872a-32ae-4315-809c-b1c15a5844d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0402355-69a7-4f87-8bf5-4f245239d860&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb24ebc5-b783-42bd-83b2-013ecb4b85de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;afe8668b-738b-4458-811c-9eda1b16a7b3&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;409c37a1-aba1-458c-8bd6-bdc1643d2b88&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BeneficiaryAccountIBAN&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;376cac1e-feaf-4850-bc9d-aee3ca71fbd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-eb59-47d6-81c6-223d085212e5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryAccountIBAN&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9a20660-114d-4161-81df-48b2971c26cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f279db09-dd60-4f70-82a9-b29bcc3990e7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05aee0bb-465f-4ff6-84e2-6f49947f2011&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 40)
{
	me.setValid(false , "max lenght is 40 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a23523c3-260c-47d9-8e52-ac943a2e47a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//
//if(isNaN(Number(me.getData()))){
//	me.setValid(false , "must be digits");
//	me.setData("");
//	return false;
//}else
//{
////	me.setValid(true);
//	return true;
//}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.beneficiaryDetails.account&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;906a4360-a466-4b65-81e3-e82bd5f5b2a0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CorrespondentRefNum&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;02a70243-3351-417a-b9b8-40bfb3d9903b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc239290-2b7e-4a85-8c5e-d2f69cabc8b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CorrespondentRefNum&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;74b22e08-2d9c-438b-8e92-93af8198f045&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;76730af2-bb6b-4899-88d3-dcebe0689fc7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83ceec14-31fb-4ee1-87cc-78ab48f0913d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2695f123-3102-49f1-8a30-199b7a1e3122&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 30)
{
	me.setValid(false , "max lenght is 30 character");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce0b5b16-5b33-4c01-8d38-9150ca3e5f58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//if(isNaN(Number(me.getData()))){
//	me.setValid(false , "must be digits");
//	me.setData("");
//	return false;
//}else
//{
////	me.setValid(true);
//	view.setbeneficiaryDetails();
//	return true;
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.beneficiaryDetails.correspondentRefNum&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;43bb8f23-bee0-40e5-8b12-572c1855ef9b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ExecutionHubName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2e2fccba-9b81-4b5b-8d43-0ab1f923b31f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ExecutionHubName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f3c033cf-aea0-4d48-8627-5e16f0f00010&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd713837-9bd9-41ac-8ea7-f05474dcd08c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;079b8ade-c584-4466-8ff4-21fb0659cff8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.financialDetails.executionHub.arabicdescription&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>efd6e062-da5f-4024-9088-ca2c0d3254b4</guid>
        <versionId>5059dce7-b1d2-4b7f-b88b-c9a36bf1972e</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="financialDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.5a887a97-63e6-45f1-9918-88734838fd84</coachViewBindingTypeId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <classId>/12.47b1b163-7266-46de-9171-529c0b65a377</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>394599b1-fbf7-4318-85b9-e33f19930788</guid>
            <versionId>4d437776-e4b6-465f-8952-d2271dfd8369</versionId>
        </bindingType>
        <configOption name="havePaymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9485ae52-cd84-4280-9648-e7f408158896</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>6bb81cbe-c25d-43c6-a3a0-ae1ea8c9c8f2</guid>
            <versionId>bbcfe51c-59df-4e65-96a0-af93f80259ce</versionId>
        </configOption>
        <configOption name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b1184f7b-9953-4e60-af9f-f641aa36540d</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>132a6e44-787b-4882-a277-8a72a0bfac81</guid>
            <versionId>f95e7ecb-ea52-4fc2-aeb0-dd01238e340d</versionId>
        </configOption>
        <configOption name="haveTradeFOReferenceNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c95f75de-9e95-4dfb-9870-541b8f38e80a</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>abd2e8c8-f986-4393-8cc5-c0f8d988fbcd</guid>
            <versionId>62b5a33b-f522-4666-9c46-c024e9ba0cb9</versionId>
        </configOption>
        <configOption name="beneficiaryDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7afc063b-a4de-46a3-8c95-ba4d4b14754b</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.a025468c-38bc-4809-b337-57da9e95dacb</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>70b063f5-5770-4195-a7be-e69b2206f268</guid>
            <versionId>bdf7a0f0-d27f-4ad2-bf7a-80ca2a497e44</versionId>
        </configOption>
        <configOption name="haveAmountAdvanced">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.14e95755-214b-4f9c-8747-7623048df01a</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>d2a67a74-5c13-4534-879d-1d9e932d797a</guid>
            <versionId>5da1f0c4-adb5-40e1-afd6-b90bef10e6c8</versionId>
        </configOption>
        <configOption name="ischecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.1a98e1a3-3f1f-4f0d-b3d0-d763cb7575bf</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>6be6ea0b-3d8f-4a18-8feb-68acc16a6ee7</guid>
            <versionId>11433312-8024-4fe2-8823-54165737e3ca</versionId>
        </configOption>
        <configOption name="LimitsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.eba57729-496e-4db6-86e7-9341709364d7</coachViewConfigOptionId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>7982276b-72f3-4342-95c0-ccda6e6fb3e5</guid>
            <versionId>2edbafcd-b812-45ed-928a-e7715326029c</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.7c174793-160c-4fda-b81b-0080d3571dc0</coachViewInlineScriptId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.LimitsVisible = function  () {&#xD;
	if ((this.context.options.requestType.get("value") == "IDC Execution" || this.context.options.requestType.get("value") == "IDC Completion" || this.context.options.requestType.get("value") == "IDC Amendment") &amp;&amp; this.context.binding.get("value").get("amtDeferredAvalized") &gt; 0) {&#xD;
		this.context.options.LimitsVis.set("value", "DEFAULT");&#xD;
	}&#xD;
	else{&#xD;
		this.context.options.LimitsVis.set("value", "READONLY");&#xD;
	}&#xD;
}&#xD;
&#xD;
this.isEqualPAybaleByNBE = function  () {&#xD;
	var payable = this.context.binding.get("value").get("amtPayableByNBE");&#xD;
	var sum =  this.context.binding.get("value").get("facilityAmtWithNoCurrency")+this.context.binding.get("value").get("cashAmtInDocCurrency")+this.context.binding.get("value").get("CashAmtWithNoCurrency")+this.context.binding.get("value").get("facilityAmtInDocCurrency");&#xD;
&#xD;
&#xD;
&#xD;
	if (sum!=payable) {&#xD;
		this.ui.get("FacilityAmountNoCurrency").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Facility Amount No Currency,Cash Amount Document Currency,Cash Amount No Currency,Facility Amount Document Currency");&#xD;
		this.ui.get("CashAmountDocumentCurrency").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Facility Amount No Currency,Cash Amount Document Currency,Cash Amount No Currency,Facility Amount Document Currency");&#xD;
		this.ui.get("CashAmountNoCurrency").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Facility Amount No Currency,Cash Amount Document Currency,Cash Amount No Currency,Facility Amount Document Currency");&#xD;
		this.ui.get("FacilityAmountDocumentCurrency").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Facility Amount No Currency,Cash Amount Document Currency,Cash Amount No Currency,Facility Amount Document Currency");&#xD;
	}else{&#xD;
		&#xD;
		this.ui.get("FacilityAmountNoCurrency").setValid(true);&#xD;
		this.ui.get("CashAmountDocumentCurrency").setValid(true);&#xD;
		this.ui.get("CashAmountNoCurrency").setValid(true);&#xD;
		this.ui.get("FacilityAmountDocumentCurrency").setValid(true);&#xD;
&#xD;
	}&#xD;
}&#xD;
var avalizationFlag = true;&#xD;
this.avalization = function  () {&#xD;
	this.context.binding.get("value").get("amtDeferredNoAvalized")&#xD;
	var payable = this.context.binding.get("value").get("amtPayableByNBE");&#xD;
	var sum =  this.context.binding.get("value").get("amtSight")+this.context.binding.get("value").get("amtDeferredNoAvalized")+this.context.binding.get("value").get("amtDeferredAvalized");&#xD;
	if (sum!=payable) {&#xD;
		avalizationFlag = false;&#xD;
		this.ui.get("AmountSight").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
		this.ui.get("AmountDeferredNoAvalization").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
		this.ui.get("AmountDeferredAvalization").setValid(false,"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
		&#xD;
	}else{&#xD;
		&#xD;
		this.ui.get("AmountSight").setValid(true);&#xD;
		this.ui.get("AmountDeferredNoAvalization").setValid(true);&#xD;
		this.ui.get("AmountDeferredAvalization").setValid(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.checkPartiAlavalization = function  () {&#xD;
	var noAvalization = this.ui.get("AmountDeferredNoAvalization").getData();&#xD;
	var avalization = this.ui.get("AmountDeferredAvalization").getData();&#xD;
	if (avalization &gt; 0 &amp;&amp; noAvalization &gt; 0) {&#xD;
		this.ui.get("AmountDeferredNoAvalization").setValid(false,"partial avalization not allowed");&#xD;
		this.ui.get("AmountDeferredAvalization").setValid(false,"partial avalization not allowed");&#xD;
	}else{&#xD;
		if(avalizationFlag){&#xD;
		this.ui.get("AmountDeferredNoAvalization").setValid(true);&#xD;
		this.ui.get("AmountDeferredAvalization").setValid(true);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.calculateAmountPayablebyNBE = function  () {&#xD;
	var payable = this.context.binding.get("value").get("documentAmount") - this.context.binding.get("value").get("amtPaidbyOtherBanks") -  this.context.binding.get("value").get("discountAmt") - this.context.binding.get("value").get("amountAdvanced");&#xD;
	this.context.binding.get("value").set("amtPayableByNBE", payable);&#xD;
	if (this.context.options.requestType == "ICAP") {&#xD;
		if(payable &gt; 0){&#xD;
			document.getElementById("text-input-Financial_Details_Trade_FO1:AmountPayablebyNBE").style.backgroundColor = "#eb8724";&#xD;
			this.ui.get("AmountPayablebyNBE").setValid(false,"the amount is more than 0 and request type is ICAP");&#xD;
		}else{&#xD;
			document.getElementById("text-input-Financial_Details_Trade_FO1:AmountPayablebyNBE").style.backgroundColor = "#ffffff";&#xD;
			this.ui.get("AmountPayablebyNBE").setValid(true);&#xD;
		}&#xD;
	}else if(payable &lt; 0){&#xD;
		this.ui.get("AmountPayablebyNBE").setValid(false,"this field must be more than 0");&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
this.haveTradeFOReferenceNumber = function  () {&#xD;
&#xD;
	if (this.context.options.haveTradeFOReferenceNumber.get("value")==false) {&#xD;
&#xD;
		this.ui.get("TradeFOReferenceNumber").setEnabled(false);&#xD;
		this.ui.get("ExecutionHubCode").setEnabled(false);&#xD;
	}else{&#xD;
&#xD;
		this.ui.get("TradeFOReferenceNumber").setEnabled(true);&#xD;
		this.ui.get("ExecutionHubCode").setEnabled(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setbeneficiaryDetails = function  () {&#xD;
	this.context.options.beneficiaryDetails.set("value", this.context.binding.get("value").get("beneficiaryDetails"));&#xD;
	&#xD;
}&#xD;
this.checkIsChecker = function  () {&#xD;
	if (this.context.options.ischecker == true) {&#xD;
		this.ui.get("CorrespondentRefNum").setEnabled(false);&#xD;
		this.ui.get("AmountAdvanced").setEnabled(false);&#xD;
		&#xD;
	}&#xD;
	else{&#xD;
		this.ui.get("CorrespondentRefNum").setEnabled(true);&#xD;
		this.ui.get("AmountAdvanced").setEnabled(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>9170af28-71f4-4422-a922-f398913bd5bc</guid>
            <versionId>03f27fd8-06ad-4a74-8d52-706a0007cdd9</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.f81fd84f-9e2a-4976-b2a1-e5b596fdb31f</coachViewLocalResId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>fe1c1a0e-acbb-4332-98dc-2ade0d9d4147</guid>
            <versionId>8ed37bad-9cc9-4541-995a-561b393cac54</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.0ca19ce8-8cc9-45c6-b6e1-7c9adc24d164</coachViewLocalResId>
            <coachViewId>64.848ab487-8214-4d8b-88fd-a9cac5257791</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>8f11ad20-3d38-4fb5-aed6-5766eb81e262</guid>
            <versionId>06a4a271-0bdb-49aa-b3be-8f79f8e552f7</versionId>
        </localization>
    </coachView>
</teamworks>

