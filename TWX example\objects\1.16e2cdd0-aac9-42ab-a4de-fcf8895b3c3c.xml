<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c" name="Generate BPM Request Number">
        <lastModified>1692505515630</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>e7d87efa-af11-492f-a7a2-56561564e7eb</guid>
        <versionId>78c0d0be-a20e-445e-95d5-f717564a75f6</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dd5" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"78c201b9-b24c-413b-93c2-37acbf34d2c2"},{"incoming":["54b7374e-6b16-426c-a1d3-97bdacc77e41","702e6d96-ebfb-4255-83d0-f3b0026ce354"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f"},{"targetRef":"4f185411-6ebe-46f3-8f0b-09bef5afa739","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Query","declaredType":"sequenceFlow","id":"2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289","sourceRef":"78c201b9-b24c-413b-93c2-37acbf34d2c2"},{"startQuantity":1,"outgoing":["3ad0758e-82c7-4538-944b-433685e2ddbe"],"incoming":["12dc14d4-0901-435e-b33c-2072e6e8efda"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":238,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Sequence\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0594f4ef-26b2-4970-a45e-52a46a7f3cf0","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["tw.local.sequence"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"08df4287-5d99-48d8-833b-b4e89c3ee68f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set BPM Request Number","declaredType":"sequenceFlow","id":"3ad0758e-82c7-4538-944b-433685e2ddbe","sourceRef":"0594f4ef-26b2-4970-a45e-52a46a7f3cf0"},{"itemSubjectRef":"itm.12.449bd82a-6888-4a7f-ab7d-01ca0e3d9b8e","name":"sequence","isCollection":true,"declaredType":"dataObject","id":"2056.9b23da21-cb0b-4eaf-b2b9-a821b8d9af6a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.99a52cba-daa5-497f-9348-739d51da9faf"},{"startQuantity":1,"outgoing":["12dc14d4-0901-435e-b33c-2072e6e8efda"],"incoming":["2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":94,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Set Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4f185411-6ebe-46f3-8f0b-09bef5afa739","scriptFormat":"text\/plain","script":{"content":["tw.local.sql\nselect next value for BPM.IDC_BPM_Request_Number as Request_Number from ( values 1 );"]}},{"targetRef":"0594f4ef-26b2-4970-a45e-52a46a7f3cf0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"12dc14d4-0901-435e-b33c-2072e6e8efda","sourceRef":"4f185411-6ebe-46f3-8f0b-09bef5afa739"},{"startQuantity":1,"outgoing":["54b7374e-6b16-426c-a1d3-97bdacc77e41"],"incoming":["3ad0758e-82c7-4538-944b-433685e2ddbe"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":406,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set BPM Request Number","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"08df4287-5d99-48d8-833b-b4e89c3ee68f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.tempSewuence = \"\"+tw.local.sequence[0].Request_Number;\r\ntw.local.BPM_Request_Number = \"\";\r\nvar date = new Date().getFullYear()+\"\";\r\ntw.local.BPM_Request_Number += tw.local.branchCode+\"02\"+date.substring(2,4);\r\nfor (var i=0; i&lt;(7-tw.local.tempSewuence.length); i++) {\r\n\ttw.local.BPM_Request_Number += \"0\";\r\n}\r\ntw.local.BPM_Request_Number += tw.local.tempSewuence;"]}},{"targetRef":"6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"54b7374e-6b16-426c-a1d3-97bdacc77e41","sourceRef":"08df4287-5d99-48d8-833b-b4e89c3ee68f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"tempSewuence","isCollection":false,"declaredType":"dataObject","id":"2056.552d2c45-8040-405e-b8b6-b91612b4b762"},{"parallelMultiple":false,"outgoing":["60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a7e23732-ed90-4cb4-8739-72d56ce1bfa3"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"96a14e3c-5c8f-43bc-8bd4-4ae3f11fc02c","otherAttributes":{"eventImplId":"170aaabb-67e1-4503-8fa9-51de1520bfda"}}],"attachedToRef":"4f185411-6ebe-46f3-8f0b-09bef5afa739","extensionElements":{"nodeVisualInfo":[{"width":24,"x":129,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24","outputSet":{}},{"parallelMultiple":false,"outgoing":["98685af4-7628-4ff0-8b9d-76ebfe025e27"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"707f07d0-0c6b-4b62-8f41-4e866f1418b8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4b754ba4-008a-415b-8d90-9f0cc7e2ba15","otherAttributes":{"eventImplId":"e35941d2-27ed-4a06-8d1d-7d73751f365f"}}],"attachedToRef":"0594f4ef-26b2-4970-a45e-52a46a7f3cf0","extensionElements":{"nodeVisualInfo":[{"width":24,"x":273,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"b48b5d79-c9d3-4e10-8532-ec50cd0feca3","outputSet":{}},{"parallelMultiple":false,"outgoing":["8b9f5bdc-b14d-4189-8a13-d661b1145e14"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"97febb5e-1be7-48bc-8bce-b2cf01f32be3"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e6ca7a83-d685-4448-8c3c-d39852b130f0","otherAttributes":{"eventImplId":"6d949b86-f4f2-4597-8ea7-ef0f1753e3fa"}}],"attachedToRef":"08df4287-5d99-48d8-833b-b4e89c3ee68f","extensionElements":{"nodeVisualInfo":[{"width":24,"x":441,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"9732b73d-7e78-45fc-8af5-3c07d2110e8d","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.cd337be5-74d2-4f19-8390-6dd4487118de"},{"startQuantity":1,"outgoing":["702e6d96-ebfb-4255-83d0-f3b0026ce354"],"incoming":["60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a","98685af4-7628-4ff0-8b9d-76ebfe025e27","8b9f5bdc-b14d-4189-8a13-d661b1145e14"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":305,"y":181,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"cdadbebd-64ea-4873-8a16-7d267b34024b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"cdadbebd-64ea-4873-8a16-7d267b34024b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a","sourceRef":"ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24"},{"targetRef":"cdadbebd-64ea-4873-8a16-7d267b34024b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"98685af4-7628-4ff0-8b9d-76ebfe025e27","sourceRef":"b48b5d79-c9d3-4e10-8532-ec50cd0feca3"},{"targetRef":"cdadbebd-64ea-4873-8a16-7d267b34024b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"8b9f5bdc-b14d-4189-8a13-d661b1145e14","sourceRef":"9732b73d-7e78-45fc-8af5-3c07d2110e8d"},{"targetRef":"6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"702e6d96-ebfb-4255-83d0-f3b0026ce354","sourceRef":"cdadbebd-64ea-4873-8a16-7d267b34024b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.ca1ed641-397e-4e14-8b2c-56704029b364"}],"laneSet":[{"id":"26147c03-96cd-4c87-8425-f98a7e5d705b","lane":[{"flowNodeRef":["78c201b9-b24c-413b-93c2-37acbf34d2c2","6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f","0594f4ef-26b2-4970-a45e-52a46a7f3cf0","4f185411-6ebe-46f3-8f0b-09bef5afa739","08df4287-5d99-48d8-833b-b4e89c3ee68f","ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24","b48b5d79-c9d3-4e10-8532-ec50cd0feca3","9732b73d-7e78-45fc-8af5-3c07d2110e8d","cdadbebd-64ea-4873-8a16-7d267b34024b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"d21e32c4-8f58-472d-8eb6-a52d3c841082","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Generate BPM Request Number","declaredType":"process","id":"1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"BPM_Request_Number","isCollection":false,"id":"2055.268b31c4-3c29-4092-8b24-cae3868d3d09"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.07e6c003-f08c-491a-8499-6d67cbb376ff"}],"inputSet":[{"dataInputRefs":["2055.37b01447-781c-4780-8505-1b49814ab727"]}],"outputSet":[{"dataOutputRefs":["2055.268b31c4-3c29-4092-8b24-cae3868d3d09","2055.07e6c003-f08c-491a-8499-6d67cbb376ff"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"077\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.37b01447-781c-4780-8505-1b49814ab727"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.37b01447-781c-4780-8505-1b49814ab727</processParameterId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"077"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0cc513b5-ad77-4397-ac89-6ab035abee2c</guid>
            <versionId>47331281-ec4f-4e33-b573-0fa8202f909a</versionId>
        </processParameter>
        <processParameter name="BPM_Request_Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.268b31c4-3c29-4092-8b24-cae3868d3d09</processParameterId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>74fab6d6-fffe-42c3-ab1a-cb70020ffd9a</guid>
            <versionId>e424a065-3afa-4a01-8e5a-72789618be06</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.07e6c003-f08c-491a-8499-6d67cbb376ff</processParameterId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>73f5ec8f-9f07-4967-ac69-0240ceda232d</guid>
            <versionId>9f5c4fd1-3359-4173-bb26-0aefd5a74598</versionId>
        </processParameter>
        <processVariable name="sequence">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b23da21-cb0b-4eaf-b2b9-a821b8d9af6a</processVariableId>
            <description isNull="true" />
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.449bd82a-6888-4a7f-ab7d-01ca0e3d9b8e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ec2a674-bac8-413b-86c0-7957f700f778</guid>
            <versionId>063b0775-ca92-479b-b2c6-f2c50410fdcf</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.99a52cba-daa5-497f-9348-739d51da9faf</processVariableId>
            <description isNull="true" />
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8c42d4e6-cc39-415a-9393-582f811ac07b</guid>
            <versionId>2028f96c-4c13-4eaa-9f5e-a9fe3cfb5d8f</versionId>
        </processVariable>
        <processVariable name="tempSewuence">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.552d2c45-8040-405e-b8b6-b91612b4b762</processVariableId>
            <description isNull="true" />
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>441bf0bc-85c0-48ae-b47e-3a8aab177411</guid>
            <versionId>82d55399-1294-41ac-ad12-25b9721b8b83</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cd337be5-74d2-4f19-8390-6dd4487118de</processVariableId>
            <description isNull="true" />
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>38dad0cd-3b56-49f7-8513-717528fa6691</guid>
            <versionId>27abe565-8746-41a8-a947-6eed206416f0</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ca1ed641-397e-4e14-8b2c-56704029b364</processVariableId>
            <description isNull="true" />
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6424d786-e4da-41f1-a899-b352fec777bd</guid>
            <versionId>40731a05-7b72-4f94-a035-b9975562f773</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0594f4ef-26b2-4970-a45e-52a46a7f3cf0</processItemId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b5</guid>
            <versionId>020caf67-44eb-4e5f-85a2-fbb22b4657d9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="238" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dd1</errorHandlerItem>
                <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>8fd1e927-95b0-4850-98b3-708edd5d0a22</guid>
                <versionId>48c0aa10-b1cd-4704-9a11-7ec5af279fdc</versionId>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.801af664-23b7-448b-900b-bc7ef50975b5</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5a186361-2756-43a0-a930-a75f5648a223</guid>
                    <versionId>1052da7a-16fe-4963-8c0d-136387e2bfc6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dea25f69-b72d-48b7-8942-417591d5b4cf</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sequence</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a0face48-6cac-4a5c-9413-2eda14501cf6</guid>
                    <versionId>a4238773-40ed-45f0-bbbc-acc46b287c7c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e96186ae-d15e-445f-8888-46d3c6593ec3</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Sequence"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>92ca14bd-4103-4ec3-8402-9033d603a0bd</guid>
                    <versionId>ad9123a2-0ad7-481d-9b5e-dd31484cecf5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e76921a6-b22d-4d8f-ac6e-d11cabcdc272</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9384c899-e434-423a-ac11-a8158a426b34</guid>
                    <versionId>b28cc3c0-ff28-45b8-8fb5-21410ccc0161</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.627f88f0-30ef-410d-ad24-f03fd040f6c0</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>c483b939-0a77-41e4-ba07-124992bcadc2</guid>
                    <versionId>df8b4a78-e9e9-4b3f-a994-1ac80355b739</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.180e42a5-8377-499c-a531-58177a80ad40</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.1aef43d4-49ca-44ac-bf99-c787b83bb6e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2ee8c46a-8676-4daf-accf-293d07566ae1</guid>
                    <versionId>ed519b64-3a01-4ff2-8872-4d69654f7ebf</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</processItemId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0dd42fd7-0338-4fd6-8084-e87fba971c3a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4</guid>
            <versionId>07969bd9-ab67-4731-96e4-8265d011c7c5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0dd42fd7-0338-4fd6-8084-e87fba971c3a</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a19f508f-28ff-4225-b92d-44d3334fb7de</guid>
                <versionId>f6dc2bbd-a360-4b0b-8c8c-1ed6807f24e0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</processItemId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2556be97-1602-40fb-b969-c9b725ace090</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dd1</guid>
            <versionId>3851f558-85f8-4374-b6f2-3708282d11b0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="305" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2556be97-1602-40fb-b969-c9b725ace090</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>c8603561-237b-4ad4-9d27-bb81b72fe4e3</guid>
                <versionId>5dc42f67-1087-430b-9dc0-59433e3453a4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</processItemId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <name>Set Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.eb225109-9342-4703-993d-a7916f463331</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b7</guid>
            <versionId>41b754ae-a8fa-438d-9627-b3764b66da60</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ccd2a893-01f1-43b3-ac19-6c5f032a8939</processItemPrePostId>
                <processItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>48a5c18d-a1bf-446b-89d5-f11f19524c6a</guid>
                <versionId>9aa3d85f-ecd6-49ef-884f-9bef32890590</versionId>
            </processPrePosts>
            <layoutData x="94" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dd1</errorHandlerItem>
                <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.eb225109-9342-4703-993d-a7916f463331</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql
select next value for BPM.IDC_BPM_Request_Number as Request_Number from ( values 1 );</script>
                <isRule>false</isRule>
                <guid>d4e2e01a-b02f-47c2-b060-435c635e8888</guid>
                <versionId>cb68ab8e-8769-4e1a-b452-21abb1252093</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.08df4287-5d99-48d8-833b-b4e89c3ee68f</processItemId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <name>Set BPM Request Number</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.3235ba61-1f95-4929-b9d3-68a94ce983ad</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b6</guid>
            <versionId>fe1a059f-02b2-49fd-9c08-b77394ef0b1a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="406" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3dd1</errorHandlerItem>
                <errorHandlerItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.3235ba61-1f95-4929-b9d3-68a94ce983ad</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.tempSewuence = ""+tw.local.sequence[0].Request_Number;&#xD;
tw.local.BPM_Request_Number = "";&#xD;
var date = new Date().getFullYear()+"";&#xD;
tw.local.BPM_Request_Number += tw.local.branchCode+"02"+date.substring(2,4);&#xD;
for (var i=0; i&lt;(7-tw.local.tempSewuence.length); i++) {&#xD;
	tw.local.BPM_Request_Number += "0";&#xD;
}&#xD;
tw.local.BPM_Request_Number += tw.local.tempSewuence;</script>
                <isRule>false</isRule>
                <guid>22d7c687-714e-4e53-92d3-6650ab8cabd0</guid>
                <versionId>53e7aaa1-2608-4661-8aaf-e2df82262fc3</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Generate BPM Request Number" id="1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.37b01447-781c-4780-8505-1b49814ab727">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"077"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="BPM_Request_Number" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.268b31c4-3c29-4092-8b24-cae3868d3d09" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.07e6c003-f08c-491a-8499-6d67cbb376ff" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.37b01447-781c-4780-8505-1b49814ab727</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.268b31c4-3c29-4092-8b24-cae3868d3d09</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.07e6c003-f08c-491a-8499-6d67cbb376ff</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="26147c03-96cd-4c87-8425-f98a7e5d705b">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="d21e32c4-8f58-472d-8eb6-a52d3c841082" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>78c201b9-b24c-413b-93c2-37acbf34d2c2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0594f4ef-26b2-4970-a45e-52a46a7f3cf0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4f185411-6ebe-46f3-8f0b-09bef5afa739</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>08df4287-5d99-48d8-833b-b4e89c3ee68f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b48b5d79-c9d3-4e10-8532-ec50cd0feca3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9732b73d-7e78-45fc-8af5-3c07d2110e8d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cdadbebd-64ea-4873-8a16-7d267b34024b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="78c201b9-b24c-413b-93c2-37acbf34d2c2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>54b7374e-6b16-426c-a1d3-97bdacc77e41</ns16:incoming>
                        
                        
                        <ns16:incoming>702e6d96-ebfb-4255-83d0-f3b0026ce354</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="78c201b9-b24c-413b-93c2-37acbf34d2c2" targetRef="4f185411-6ebe-46f3-8f0b-09bef5afa739" name="To Set Query" id="2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="0594f4ef-26b2-4970-a45e-52a46a7f3cf0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="238" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>12dc14d4-0901-435e-b33c-2072e6e8efda</ns16:incoming>
                        
                        
                        <ns16:outgoing>3ad0758e-82c7-4538-944b-433685e2ddbe</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Sequence"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">tw.local.sequence</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="0594f4ef-26b2-4970-a45e-52a46a7f3cf0" targetRef="08df4287-5d99-48d8-833b-b4e89c3ee68f" name="To Set BPM Request Number" id="3ad0758e-82c7-4538-944b-433685e2ddbe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.449bd82a-6888-4a7f-ab7d-01ca0e3d9b8e" isCollection="true" name="sequence" id="2056.9b23da21-cb0b-4eaf-b2b9-a821b8d9af6a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.99a52cba-daa5-497f-9348-739d51da9faf" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" name="Set Query" id="4f185411-6ebe-46f3-8f0b-09bef5afa739">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="94" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.cfdab3be-dc44-4e8f-853d-64c2eb9d1289</ns16:incoming>
                        
                        
                        <ns16:outgoing>12dc14d4-0901-435e-b33c-2072e6e8efda</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql
select next value for BPM.IDC_BPM_Request_Number as Request_Number from ( values 1 );</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4f185411-6ebe-46f3-8f0b-09bef5afa739" targetRef="0594f4ef-26b2-4970-a45e-52a46a7f3cf0" name="To SQL Execute Statement" id="12dc14d4-0901-435e-b33c-2072e6e8efda">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set BPM Request Number" id="08df4287-5d99-48d8-833b-b4e89c3ee68f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="406" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3ad0758e-82c7-4538-944b-433685e2ddbe</ns16:incoming>
                        
                        
                        <ns16:outgoing>54b7374e-6b16-426c-a1d3-97bdacc77e41</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.tempSewuence = ""+tw.local.sequence[0].Request_Number;&#xD;
tw.local.BPM_Request_Number = "";&#xD;
var date = new Date().getFullYear()+"";&#xD;
tw.local.BPM_Request_Number += tw.local.branchCode+"02"+date.substring(2,4);&#xD;
for (var i=0; i&lt;(7-tw.local.tempSewuence.length); i++) {&#xD;
	tw.local.BPM_Request_Number += "0";&#xD;
}&#xD;
tw.local.BPM_Request_Number += tw.local.tempSewuence;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="08df4287-5d99-48d8-833b-b4e89c3ee68f" targetRef="6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f" name="To End" id="54b7374e-6b16-426c-a1d3-97bdacc77e41">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tempSewuence" id="2056.552d2c45-8040-405e-b8b6-b91612b4b762" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="4f185411-6ebe-46f3-8f0b-09bef5afa739" parallelMultiple="false" name="Error" id="ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="129" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a7e23732-ed90-4cb4-8739-72d56ce1bfa3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="96a14e3c-5c8f-43bc-8bd4-4ae3f11fc02c" eventImplId="170aaabb-67e1-4503-8fa9-51de1520bfda">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0594f4ef-26b2-4970-a45e-52a46a7f3cf0" parallelMultiple="false" name="Error1" id="b48b5d79-c9d3-4e10-8532-ec50cd0feca3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="273" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>98685af4-7628-4ff0-8b9d-76ebfe025e27</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="707f07d0-0c6b-4b62-8f41-4e866f1418b8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4b754ba4-008a-415b-8d90-9f0cc7e2ba15" eventImplId="e35941d2-27ed-4a06-8d1d-7d73751f365f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="08df4287-5d99-48d8-833b-b4e89c3ee68f" parallelMultiple="false" name="Error2" id="9732b73d-7e78-45fc-8af5-3c07d2110e8d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="441" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8b9f5bdc-b14d-4189-8a13-d661b1145e14</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="97febb5e-1be7-48bc-8bce-b2cf01f32be3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e6ca7a83-d685-4448-8c3c-d39852b130f0" eventImplId="6d949b86-f4f2-4597-8ea7-ef0f1753e3fa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.cd337be5-74d2-4f19-8390-6dd4487118de" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="cdadbebd-64ea-4873-8a16-7d267b34024b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="305" y="181" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a</ns16:incoming>
                        
                        
                        <ns16:incoming>98685af4-7628-4ff0-8b9d-76ebfe025e27</ns16:incoming>
                        
                        
                        <ns16:incoming>8b9f5bdc-b14d-4189-8a13-d661b1145e14</ns16:incoming>
                        
                        
                        <ns16:outgoing>702e6d96-ebfb-4255-83d0-f3b0026ce354</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ebaafcc2-ac98-4aa4-8ec1-1cd3104e3f24" targetRef="cdadbebd-64ea-4873-8a16-7d267b34024b" name="To Catch Errors" id="60b6c2f9-ef9f-4bfb-89af-6bad9b696a5a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b48b5d79-c9d3-4e10-8532-ec50cd0feca3" targetRef="cdadbebd-64ea-4873-8a16-7d267b34024b" name="To Catch Errors" id="98685af4-7628-4ff0-8b9d-76ebfe025e27">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9732b73d-7e78-45fc-8af5-3c07d2110e8d" targetRef="cdadbebd-64ea-4873-8a16-7d267b34024b" name="To Catch Errors" id="8b9f5bdc-b14d-4189-8a13-d661b1145e14">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="cdadbebd-64ea-4873-8a16-7d267b34024b" targetRef="6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f" name="To End" id="702e6d96-ebfb-4255-83d0-f3b0026ce354">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.ca1ed641-397e-4e14-8b2c-56704029b364" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set BPM Request Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3ad0758e-82c7-4538-944b-433685e2ddbe</processLinkId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0594f4ef-26b2-4970-a45e-52a46a7f3cf0</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.08df4287-5d99-48d8-833b-b4e89c3ee68f</toProcessItemId>
            <guid>7ae689c3-d4fe-4ed2-9234-cd8c9a611f67</guid>
            <versionId>258b4966-4a57-4cee-b4a1-eaed92f65ff1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0594f4ef-26b2-4970-a45e-52a46a7f3cf0</fromProcessItemId>
            <toProcessItemId>2025.08df4287-5d99-48d8-833b-b4e89c3ee68f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.54b7374e-6b16-426c-a1d3-97bdacc77e41</processLinkId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.08df4287-5d99-48d8-833b-b4e89c3ee68f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</toProcessItemId>
            <guid>d220c79f-3738-4076-98aa-b33170b34823</guid>
            <versionId>76843e9e-fdcd-465a-95e7-b0bf7fbffb8f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.08df4287-5d99-48d8-833b-b4e89c3ee68f</fromProcessItemId>
            <toProcessItemId>2025.6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.12dc14d4-0901-435e-b33c-2072e6e8efda</processLinkId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.0594f4ef-26b2-4970-a45e-52a46a7f3cf0</toProcessItemId>
            <guid>aa9510d9-74bd-4d63-89fe-dd1c6d69bf80</guid>
            <versionId>9b2cdeb0-2a73-4bcb-b3eb-2cdfb4ea8da3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4f185411-6ebe-46f3-8f0b-09bef5afa739</fromProcessItemId>
            <toProcessItemId>2025.0594f4ef-26b2-4970-a45e-52a46a7f3cf0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.702e6d96-ebfb-4255-83d0-f3b0026ce354</processLinkId>
            <processId>1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</toProcessItemId>
            <guid>f3f87d6a-a832-4838-8cf4-a3e651dfc713</guid>
            <versionId>b38464ce-e412-4b7a-a4df-db58836b38a4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.cdadbebd-64ea-4873-8a16-7d267b34024b</fromProcessItemId>
            <toProcessItemId>2025.6f07eedf-a5e3-4f9b-8ebd-34dcfd844b5f</toProcessItemId>
        </link>
    </process>
</teamworks>

