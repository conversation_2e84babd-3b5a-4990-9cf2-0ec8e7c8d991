<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.d4263736-0fea-47c5-90ea-cc6b49adfcec" name="Test Swift">
        <lastModified>1688833187021</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.989e8254-6920-47b4-ab30-995976e93a24</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>fcb200c0-130b-40e2-b813-aed2e0b11f84</guid>
        <versionId>3af2140b-3fba-4022-a668-ec75b0cd31b4</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.d7404766-321f-4d44-bd09-1ad3558c0043"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c588ef7e-4290-4bd1-8220-5760b9a46cdb"},{"outgoing":["2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45"],"incoming":["2027.d7404766-321f-4d44-bd09-1ad3558c0043","2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":150,"y":177,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Swift_Message_Data1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7c012e69-d2a6-4e68-8d61-b987f6282a93","optionName":"@label","value":"Swift Message Data"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e0c474f7-e849-49fd-8c1c-2149ca5d771c","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9dee3ff7-1e54-4693-88bf-ead66c950f34","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.122789dd-9d59-4a0d-b507-23e1fe2141c4","binding":"tw.local.SwiftMessageData","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"87b663ba-a2bd-4246-826e-0bee9a9e8dd4","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"37c16247-1c98-49ac-b39d-4080d2e4192c","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0c017316-a3a0-4fb0-9b3f-2a3ababb6fc8"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach","isForCompensation":false,"completionQuantity":1,"id":"2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c"},{"incoming":["2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":874,"y":160,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"6bbb4bbb-2c08-4022-9d2d-1fbf8476ce5d"},{"targetRef":"2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.d7404766-321f-4d44-bd09-1ad3558c0043","sourceRef":"c588ef7e-4290-4bd1-8220-5760b9a46cdb"},{"startQuantity":1,"outgoing":["2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218"],"incoming":["2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45"],"default":"2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":401,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/tw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\/\/--------------------------------------------------------------------------------------------------\r\nfunction swiftValidation (field,fieldName) {\r\n\tregexString = \/^(?![\\s])[a-zA-Z0-9.,()\\\/='+:?!\\\"%&amp;*&lt;&gt;;{@#_ \\r\\n-]*$\/;\r\n\tregex = new RegExp(regexString);\r\n\t\r\n\tif (field != \"\") {\r\n\t\tif (!regex.test(field)){\r\n\t\t\taddError(fieldName , \"Not Valid Swift Format\",\"Not Valid Swift Format\" );\r\n\t\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\/\/--------------------------------------------------------------------------------------------------------\r\nswiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line1,\"tw.local.SwiftMessageData.detailsOfPayment.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line2,\"tw.local.SwiftMessageData.detailsOfPayment.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line3,\"tw.local.SwiftMessageData.detailsOfPayment.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line4,\"tw.local.SwiftMessageData.detailsOfPayment.line4\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line1,\"tw.local.SwiftMessageData.senderToReciever.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line2,\"tw.local.SwiftMessageData.senderToReciever.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line3,\"tw.local.SwiftMessageData.senderToReciever.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line4,\"tw.local.SwiftMessageData.senderToReciever.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line5,\"tw.local.SwiftMessageData.senderToReciever.line5\");\r\nswiftValidation (tw.local.SwiftMessageData.senderToReciever.line6,\"tw.local.SwiftMessageData.senderToReciever.line6\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.intermediary.line1,\"tw.local.SwiftMessageData.intermediary.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.intermediary.line2,\"tw.local.SwiftMessageData.intermediary.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.intermediary.line3,\"tw.local.SwiftMessageData.intermediary.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.intermediary.line4,\"tw.local.SwiftMessageData.intermediary.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.intermediary.line5,\"tw.local.SwiftMessageData.intermediary.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line1,\"tw.local.SwiftMessageData.receiverCorrespondent.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line2,\"tw.local.SwiftMessageData.receiverCorrespondent.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line3,\"tw.local.SwiftMessageData.receiverCorrespondent.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line4,\"tw.local.SwiftMessageData.receiverCorrespondent.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line5,\"tw.local.SwiftMessageData.receiverCorrespondent.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.receiverOfCover,\"tw.local.SwiftMessageData.receiverOfCover\");\r\nswiftValidation (tw.local.SwiftMessageData.receiver,\"tw.local.SwiftMessageData.receiver\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line1,\"tw.local.SwiftMessageData.accountWithInstitution.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line2,\"tw.local.SwiftMessageData.accountWithInstitution.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line3,\"tw.local.SwiftMessageData.accountWithInstitution.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line4,\"tw.local.SwiftMessageData.accountWithInstitution.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line5,\"tw.local.SwiftMessageData.accountWithInstitution.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.orderingInstitution.line1,\"tw.local.SwiftMessageData.orderingInstitution.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingInstitution.line2,\"tw.local.SwiftMessageData.orderingInstitution.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingInstitution.line3,\"tw.local.SwiftMessageData.orderingInstitution.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingInstitution.line4,\"tw.local.SwiftMessageData.orderingInstitution.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingInstitution.line5,\"tw.local.SwiftMessageData.orderingInstitution.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line1,\"tw.local.SwiftMessageData.beneficiaryInstitution.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line2,\"tw.local.SwiftMessageData.beneficiaryInstitution.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line3,\"tw.local.SwiftMessageData.beneficiaryInstitution.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line4,\"tw.local.SwiftMessageData.beneficiaryInstitution.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line5,\"tw.local.SwiftMessageData.beneficiaryInstitution.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.orderingCustomer.line1,\"tw.local.SwiftMessageData.orderingCustomer.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingCustomer.line2,\"tw.local.SwiftMessageData.orderingCustomer.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingCustomer.line3,\"tw.local.SwiftMessageData.orderingCustomer.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingCustomer.line4,\"tw.local.SwiftMessageData.orderingCustomer.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.orderingCustomer.line5,\"tw.local.SwiftMessageData.orderingCustomer.line5\");\r\n\r\nswiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line1,\"tw.local.SwiftMessageData.ultimateBeneficiary.line1\");\r\nswiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line2,\"tw.local.SwiftMessageData.ultimateBeneficiary.line2\");\r\nswiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line3,\"tw.local.SwiftMessageData.ultimateBeneficiary.line3\");\r\nswiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line4,\"tw.local.SwiftMessageData.ultimateBeneficiary.line4\");\r\nswiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line5,\"tw.local.SwiftMessageData.ultimateBeneficiary.line5\");\r\n\r\nmandatory(tw.local.SwiftMessageData.swiftMessageOption,\"tw.local.SwiftMessageData.swiftMessageOption\");\r\ntw.local.needBicVal = false;\r\ntw.local.bicFields = [];\r\nif (tw.local.SwiftMessageData.swiftMessageOption == \"Option 1: Serial Payment \u2013 Send MT 103\") {\r\n\tmandatory(tw.local.SwiftMessageData.detailsOfCharge,\"tw.local.SwiftMessageData.detailsOfCharge\");\r\n\t\r\n\tmandatory(tw.local.SwiftMessageData.detailsOfPayment.line1,\"tw.local.SwiftMessageData.detailsOfPayment.line1\");\r\n\t\r\n\tmandatory(tw.local.SwiftMessageData.orderingCustomer.line1,\"tw.local.SwiftMessageData.orderingCustomer.line1\");\r\n\tmandatory(tw.local.SwiftMessageData.orderingCustomer.line2,\"tw.local.SwiftMessageData.orderingCustomer.line2\");\r\n\t\r\n\tmandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line1,\"tw.local.SwiftMessageData.ultimateBeneficiary.line1\");\r\n\tmandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line2,\"tw.local.SwiftMessageData.ultimateBeneficiary.line2\");\r\n\t\r\n\t\r\n} else if (tw.local.SwiftMessageData.swiftMessageOption == \"Option 2: Cover Payment \u2013 Send MT 103 and MT 202 COV\") {\r\n\ttw.local.needBicVal = true;\r\n\ttw.local.bicFields[0] = {};\r\n\ttw.local.bicFields[0].name = \"tw.local.SwiftMessageData.receiverOfCover\";\r\n\ttw.local.bicFields[0].value = tw.local.SwiftMessageData.receiverOfCover;\r\n\ttw.local.bicFields[1] = {};\r\n\ttw.local.bicFields[1].name = \"tw.local.SwiftMessageData.receiver\";\r\n\ttw.local.bicFields[1].value = tw.local.SwiftMessageData.receiver;\r\n\tmandatory(tw.local.SwiftMessageData.detailsOfCharge,\"tw.local.SwiftMessageData.detailsOfCharge\");\r\n\tmandatory(tw.local.SwiftMessageData.detailsOfPayment.line1,\"tw.local.SwiftMessageData.detailsOfPayment.line1\");\r\n\t\r\n\tmandatory(tw.local.SwiftMessageData.orderingCustomer.line1,\"tw.local.SwiftMessageData.orderingCustomer.line1\");\r\n\tmandatory(tw.local.SwiftMessageData.orderingCustomer.line2,\"tw.local.SwiftMessageData.orderingCustomer.line2\");\r\n\t\r\n\tmandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line1,\"tw.local.SwiftMessageData.ultimateBeneficiary.line1\");\r\n\tmandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line2,\"tw.local.SwiftMessageData.ultimateBeneficiary.line2\");\r\n\t\r\n} else if (tw.local.SwiftMessageData.swiftMessageOption == \"Option 3: Bank to Bank Payment (1) \u2013 Send MT 202 and MT 400\") {\r\n\t\r\n\tif (tw.local.SwiftMessageData.beneficiaryInstitution.line1 == \"\" || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == null) {\r\n\t\tmandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line1,\"tw.local.SwiftMessageData.beneficiaryInstitution.line1\");\r\n\t\tmandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line2,\"tw.local.SwiftMessageData.beneficiaryInstitution.line2\");\r\n\t}else{\r\n\t\ttw.local.needBicVal = true;\r\n\t\ttw.local.bicFields[0] = {};\r\n\t\ttw.local.bicFields[0].name = \"tw.local.SwiftMessageData.beneficiaryInstitution.line1\";\r\n\t\ttw.local.bicFields[0].value = tw.local.SwiftMessageData.beneficiaryInstitution.line1;\r\n\t}\r\n\t\r\n\t\r\n} else if (tw.local.SwiftMessageData.swiftMessageOption == \"Option 4: Bank to Bank Payment (2) \u2013 Send MT 400\") {\r\n\t\r\n} else if (tw.local.SwiftMessageData.swiftMessageOption == \"Option 5: Bank to Bank (Local) \u2013 Send MT 202\"){\r\n\r\n\t\r\n\tif (tw.local.SwiftMessageData.beneficiaryInstitution.line1 == \"\" || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == null) {\r\n\t\tmandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line1,\"tw.local.SwiftMessageData.beneficiaryInstitution.line1\");\r\n\t\tmandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line2,\"tw.local.SwiftMessageData.beneficiaryInstitution.line2\");\r\n\t}else{\r\n\t\ttw.local.needBicVal = true;\r\n\t\ttw.local.bicFields[0] = {};\r\n\t\ttw.local.bicFields[0].name = \"tw.local.SwiftMessageData.beneficiaryInstitution.line1\";\r\n\t\ttw.local.bicFields[0].value = tw.local.SwiftMessageData.beneficiaryInstitution.line1;\r\n\t}\r\n}\r\n\t\r\n\r\n\t\r\n"]}},{"outgoing":["2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064","2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6"],"incoming":["2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182","2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2"],"default":"2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":816,"y":111,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.f229bd37-1e77-4809-8ed3-63cccea08564"},{"targetRef":"2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of To Create IDC Request","declaredType":"sequenceFlow","id":"2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064","sourceRef":"2025.f229bd37-1e77-4809-8ed3-63cccea08564"},{"targetRef":"6bbb4bbb-2c08-4022-9d2d-1fbf8476ce5d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Request Number ,CBE And ECM Folder","declaredType":"sequenceFlow","id":"2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6","sourceRef":"2025.f229bd37-1e77-4809-8ed3-63cccea08564"},{"targetRef":"2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Exclusive Gateway 1","declaredType":"sequenceFlow","id":"2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218","sourceRef":"2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f"},{"itemSubjectRef":"itm.12.770d3eea-9807-42ad-9ce2-c280a5145765","name":"SwiftMessageData","isCollection":false,"declaredType":"dataObject","id":"2056.f81d58e0-a120-4d07-95f2-01d8f270b37b"},{"targetRef":"2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"cf2ccaf4-df6c-41fd-8fd0-7c8181a65732","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation","declaredType":"sequenceFlow","id":"2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45","sourceRef":"2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"needBicVal","isCollection":false,"declaredType":"dataObject","id":"2056.3d6ae48b-8024-4908-ab7b-d104b7c48a48"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"bicFields","isCollection":true,"declaredType":"dataObject","id":"2056.d0e0cabf-e8fe-45cb-b608-fb44bc0221a1"},{"outgoing":["2027.e758d99f-3bb5-4c32-83b1-14bed5161ada","2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2"],"incoming":["2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218"],"default":"2027.e758d99f-3bb5-4c32-83b1-14bed5161ada","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":545,"y":176,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway 1","declaredType":"exclusiveGateway","id":"2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7"},{"targetRef":"2025.43557850-602e-49fd-8fd8-cdf919f8d94c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Validate BIC","declaredType":"sequenceFlow","id":"2027.e758d99f-3bb5-4c32-83b1-14bed5161ada","sourceRef":"2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7"},{"outgoing":["2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182"],"incoming":["2027.e758d99f-3bb5-4c32-83b1-14bed5161ada"],"extensionElements":{"postAssignmentScript":["for (var i=0; i&lt;tw.local.result.length; i++) {\r\n\tif (!tw.local.result[i]) {\r\n\t\r\n\t\ttw.system.coachValidation.addValidationError(tw.local.bicFields[i].name, \"not valid BIC code\");\r\n\t}\r\n}"],"nodeVisualInfo":[{"width":95,"x":658,"y":144,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182","name":"Validate BIC","dataInputAssociation":[{"targetRef":"2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.bicFields"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.43557850-602e-49fd-8fd8-cdf919f8d94c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.result"]}}],"sourceRef":["2055.876be99d-0b55-4982-8dae-2a839dc36cb6"]}],"calledElement":"1.72840b39-03cc-42d8-a24b-01650b923101"},{"targetRef":"2025.f229bd37-1e77-4809-8ed3-63cccea08564","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validate BIC","declaredType":"sequenceFlow","id":"2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182","sourceRef":"2025.43557850-602e-49fd-8fd8-cdf919f8d94c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"result","isCollection":true,"declaredType":"dataObject","id":"2056.348855d1-cffd-4a93-8aa8-f583917d94c8"},{"targetRef":"2025.f229bd37-1e77-4809-8ed3-63cccea08564","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.needBicVal\t  !=\t  true"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2","sourceRef":"2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"594d8836-e34f-4193-8713-c744caae0897","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"9e307b12-67d2-4bc8-a19a-ca77eeaccd18","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Test Swift","declaredType":"globalUserTask","id":"1.d4263736-0fea-47c5-90ea-cc6b49adfcec","ioSpecification":{"inputSet":[{"id":"ccd5e484-bdba-4b62-8aeb-fb34d4a9fc77"}],"outputSet":[{"id":"28fa699d-1e8f-4130-b8a7-783d7b98bf4e"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"506dd745-7488-4345-9377-7ccb7a76c108"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processVariable name="SwiftMessageData">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f81d58e0-a120-4d07-95f2-01d8f270b37b</processVariableId>
            <description isNull="true" />
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.770d3eea-9807-42ad-9ce2-c280a5145765</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eb47863d-5bec-4e4a-a0ce-f706127ea8be</guid>
            <versionId>33aa6857-ae26-4187-abc2-b1fdc4ac57ca</versionId>
        </processVariable>
        <processVariable name="needBicVal">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3d6ae48b-8024-4908-ab7b-d104b7c48a48</processVariableId>
            <description isNull="true" />
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>099098e4-31b1-4435-b1ed-97bb5b70654c</guid>
            <versionId>5bf9cdd8-9a9a-4b64-be05-8d4b894cff53</versionId>
        </processVariable>
        <processVariable name="bicFields">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d0e0cabf-e8fe-45cb-b608-fb44bc0221a1</processVariableId>
            <description isNull="true" />
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a610e30f-920c-4945-bc39-f365e2930ff7</guid>
            <versionId>064a4c12-a53f-4bf6-8261-40c9738a3714</versionId>
        </processVariable>
        <processVariable name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.348855d1-cffd-4a93-8aa8-f583917d94c8</processVariableId>
            <description isNull="true" />
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a9252ae4-b7b2-4a41-880e-bedeb9e2bbbc</guid>
            <versionId>8817613d-ead7-4a8a-b41a-c818ded2dc97</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.989e8254-6920-47b4-ab30-995976e93a24</processItemId>
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.4bd9f9fb-03e8-4387-87c7-68a0b00de029</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6221</guid>
            <versionId>4807e610-ac94-4776-9286-a4100e15771b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.43557850-602e-49fd-8fd8-cdf919f8d94c</processItemId>
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <name>Validate BIC</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.14262405-d91e-4418-bfdc-60537bc88268</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:18936351eeb:-5c75</guid>
            <versionId>693cff89-f6a0-421a-9e89-42ca319ba144</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.14262405-d91e-4418-bfdc-60537bc88268</subProcessId>
                <attachedProcessRef>/1.72840b39-03cc-42d8-a24b-01650b923101</attachedProcessRef>
                <guid>cff0ef4e-d6e4-4490-b2ce-bdfb5e0d126d</guid>
                <versionId>9793c721-101d-4ac6-90e6-6a0933573cd1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c282d6b3-65cf-4bcf-9671-1f72bfad698c</processItemId>
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6db10b90-286e-4114-a9b1-7a9bf4ac6c68</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6220</guid>
            <versionId>c1d6aaa3-2d45-4814-8da3-9c477b604cb6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6db10b90-286e-4114-a9b1-7a9bf4ac6c68</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>dd750bd4-4620-4dfd-887c-803e6005384c</guid>
                <versionId>f1c19eda-087e-4549-a35a-029c66e5cec5</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.989e8254-6920-47b4-ab30-995976e93a24</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="506dd745-7488-4345-9377-7ccb7a76c108" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="Test Swift" id="1.d4263736-0fea-47c5-90ea-cc6b49adfcec">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="9e307b12-67d2-4bc8-a19a-ca77eeaccd18">
                            
                            
                            <ns16:startEvent name="Start" id="c588ef7e-4290-4bd1-8220-5760b9a46cdb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.d7404766-321f-4d44-bd09-1ad3558c0043</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns3:formTask name="Coach" id="2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                    
                                    <ns13:nodeVisualInfo x="150" y="177" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d7404766-321f-4d44-bd09-1ad3558c0043</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>87b663ba-a2bd-4246-826e-0bee9a9e8dd4</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Swift_Message_Data1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7c012e69-d2a6-4e68-8d61-b987f6282a93</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Swift Message Data</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e0c474f7-e849-49fd-8c1c-2149ca5d771c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9dee3ff7-1e54-4693-88bf-ead66c950f34</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.122789dd-9d59-4a0d-b507-23e1fe2141c4</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.SwiftMessageData</ns19:binding>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                
                                                
                                                <ns19:id>0c017316-a3a0-4fb0-9b3f-2a3ababb6fc8</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>37c16247-1c98-49ac-b39d-4080d2e4192c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>OK</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:endEvent name="End" id="6bbb4bbb-2c08-4022-9d2d-1fbf8476ce5d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="874" y="160" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="c588ef7e-4290-4bd1-8220-5760b9a46cdb" targetRef="2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c" name="To Coach" id="2027.d7404766-321f-4d44-bd09-1ad3558c0043">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218" name="validation" id="2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="401" y="176" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
var message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
function swiftValidation (field,fieldName) {&#xD;
	regexString = /^(?![\s])[a-zA-Z0-9.,()\/='+:?!\"%&amp;*&lt;&gt;;{@#_ \r\n-]*$/;&#xD;
	regex = new RegExp(regexString);&#xD;
	&#xD;
	if (field != "") {&#xD;
		if (!regex.test(field)){&#xD;
			addError(fieldName , "Not Valid Swift Format","Not Valid Swift Format" );&#xD;
	&#xD;
		}&#xD;
	}&#xD;
&#xD;
}&#xD;
//--------------------------------------------------------------------------------------------------------&#xD;
swiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line1,"tw.local.SwiftMessageData.detailsOfPayment.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line2,"tw.local.SwiftMessageData.detailsOfPayment.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line3,"tw.local.SwiftMessageData.detailsOfPayment.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.detailsOfPayment.line4,"tw.local.SwiftMessageData.detailsOfPayment.line4");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line1,"tw.local.SwiftMessageData.senderToReciever.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line2,"tw.local.SwiftMessageData.senderToReciever.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line3,"tw.local.SwiftMessageData.senderToReciever.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line4,"tw.local.SwiftMessageData.senderToReciever.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line5,"tw.local.SwiftMessageData.senderToReciever.line5");&#xD;
swiftValidation (tw.local.SwiftMessageData.senderToReciever.line6,"tw.local.SwiftMessageData.senderToReciever.line6");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.intermediary.line1,"tw.local.SwiftMessageData.intermediary.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.intermediary.line2,"tw.local.SwiftMessageData.intermediary.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.intermediary.line3,"tw.local.SwiftMessageData.intermediary.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.intermediary.line4,"tw.local.SwiftMessageData.intermediary.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.intermediary.line5,"tw.local.SwiftMessageData.intermediary.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line1,"tw.local.SwiftMessageData.receiverCorrespondent.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line2,"tw.local.SwiftMessageData.receiverCorrespondent.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line3,"tw.local.SwiftMessageData.receiverCorrespondent.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line4,"tw.local.SwiftMessageData.receiverCorrespondent.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverCorrespondent.line5,"tw.local.SwiftMessageData.receiverCorrespondent.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.receiverOfCover,"tw.local.SwiftMessageData.receiverOfCover");&#xD;
swiftValidation (tw.local.SwiftMessageData.receiver,"tw.local.SwiftMessageData.receiver");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line1,"tw.local.SwiftMessageData.accountWithInstitution.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line2,"tw.local.SwiftMessageData.accountWithInstitution.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line3,"tw.local.SwiftMessageData.accountWithInstitution.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line4,"tw.local.SwiftMessageData.accountWithInstitution.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.accountWithInstitution.line5,"tw.local.SwiftMessageData.accountWithInstitution.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingInstitution.line1,"tw.local.SwiftMessageData.orderingInstitution.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingInstitution.line2,"tw.local.SwiftMessageData.orderingInstitution.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingInstitution.line3,"tw.local.SwiftMessageData.orderingInstitution.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingInstitution.line4,"tw.local.SwiftMessageData.orderingInstitution.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingInstitution.line5,"tw.local.SwiftMessageData.orderingInstitution.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line1,"tw.local.SwiftMessageData.beneficiaryInstitution.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line2,"tw.local.SwiftMessageData.beneficiaryInstitution.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line3,"tw.local.SwiftMessageData.beneficiaryInstitution.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line4,"tw.local.SwiftMessageData.beneficiaryInstitution.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.beneficiaryInstitution.line5,"tw.local.SwiftMessageData.beneficiaryInstitution.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingCustomer.line1,"tw.local.SwiftMessageData.orderingCustomer.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingCustomer.line2,"tw.local.SwiftMessageData.orderingCustomer.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingCustomer.line3,"tw.local.SwiftMessageData.orderingCustomer.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingCustomer.line4,"tw.local.SwiftMessageData.orderingCustomer.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.orderingCustomer.line5,"tw.local.SwiftMessageData.orderingCustomer.line5");&#xD;
&#xD;
swiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line1,"tw.local.SwiftMessageData.ultimateBeneficiary.line1");&#xD;
swiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line2,"tw.local.SwiftMessageData.ultimateBeneficiary.line2");&#xD;
swiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line3,"tw.local.SwiftMessageData.ultimateBeneficiary.line3");&#xD;
swiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line4,"tw.local.SwiftMessageData.ultimateBeneficiary.line4");&#xD;
swiftValidation (tw.local.SwiftMessageData.ultimateBeneficiary.line5,"tw.local.SwiftMessageData.ultimateBeneficiary.line5");&#xD;
&#xD;
mandatory(tw.local.SwiftMessageData.swiftMessageOption,"tw.local.SwiftMessageData.swiftMessageOption");&#xD;
tw.local.needBicVal = false;&#xD;
tw.local.bicFields = [];&#xD;
if (tw.local.SwiftMessageData.swiftMessageOption == "Option 1: Serial Payment – Send MT 103") {&#xD;
	mandatory(tw.local.SwiftMessageData.detailsOfCharge,"tw.local.SwiftMessageData.detailsOfCharge");&#xD;
	&#xD;
	mandatory(tw.local.SwiftMessageData.detailsOfPayment.line1,"tw.local.SwiftMessageData.detailsOfPayment.line1");&#xD;
	&#xD;
	mandatory(tw.local.SwiftMessageData.orderingCustomer.line1,"tw.local.SwiftMessageData.orderingCustomer.line1");&#xD;
	mandatory(tw.local.SwiftMessageData.orderingCustomer.line2,"tw.local.SwiftMessageData.orderingCustomer.line2");&#xD;
	&#xD;
	mandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line1,"tw.local.SwiftMessageData.ultimateBeneficiary.line1");&#xD;
	mandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line2,"tw.local.SwiftMessageData.ultimateBeneficiary.line2");&#xD;
	&#xD;
	&#xD;
} else if (tw.local.SwiftMessageData.swiftMessageOption == "Option 2: Cover Payment – Send MT 103 and MT 202 COV") {&#xD;
	tw.local.needBicVal = true;&#xD;
	tw.local.bicFields[0] = {};&#xD;
	tw.local.bicFields[0].name = "tw.local.SwiftMessageData.receiverOfCover";&#xD;
	tw.local.bicFields[0].value = tw.local.SwiftMessageData.receiverOfCover;&#xD;
	tw.local.bicFields[1] = {};&#xD;
	tw.local.bicFields[1].name = "tw.local.SwiftMessageData.receiver";&#xD;
	tw.local.bicFields[1].value = tw.local.SwiftMessageData.receiver;&#xD;
	mandatory(tw.local.SwiftMessageData.detailsOfCharge,"tw.local.SwiftMessageData.detailsOfCharge");&#xD;
	mandatory(tw.local.SwiftMessageData.detailsOfPayment.line1,"tw.local.SwiftMessageData.detailsOfPayment.line1");&#xD;
	&#xD;
	mandatory(tw.local.SwiftMessageData.orderingCustomer.line1,"tw.local.SwiftMessageData.orderingCustomer.line1");&#xD;
	mandatory(tw.local.SwiftMessageData.orderingCustomer.line2,"tw.local.SwiftMessageData.orderingCustomer.line2");&#xD;
	&#xD;
	mandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line1,"tw.local.SwiftMessageData.ultimateBeneficiary.line1");&#xD;
	mandatory(tw.local.SwiftMessageData.ultimateBeneficiary.line2,"tw.local.SwiftMessageData.ultimateBeneficiary.line2");&#xD;
	&#xD;
} else if (tw.local.SwiftMessageData.swiftMessageOption == "Option 3: Bank to Bank Payment (1) – Send MT 202 and MT 400") {&#xD;
	&#xD;
	if (tw.local.SwiftMessageData.beneficiaryInstitution.line1 == "" || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == null) {&#xD;
		mandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line1,"tw.local.SwiftMessageData.beneficiaryInstitution.line1");&#xD;
		mandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line2,"tw.local.SwiftMessageData.beneficiaryInstitution.line2");&#xD;
	}else{&#xD;
		tw.local.needBicVal = true;&#xD;
		tw.local.bicFields[0] = {};&#xD;
		tw.local.bicFields[0].name = "tw.local.SwiftMessageData.beneficiaryInstitution.line1";&#xD;
		tw.local.bicFields[0].value = tw.local.SwiftMessageData.beneficiaryInstitution.line1;&#xD;
	}&#xD;
	&#xD;
	&#xD;
} else if (tw.local.SwiftMessageData.swiftMessageOption == "Option 4: Bank to Bank Payment (2) – Send MT 400") {&#xD;
	&#xD;
} else if (tw.local.SwiftMessageData.swiftMessageOption == "Option 5: Bank to Bank (Local) – Send MT 202"){&#xD;
&#xD;
	&#xD;
	if (tw.local.SwiftMessageData.beneficiaryInstitution.line1 == "" || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == undefined || tw.local.SwiftMessageData.beneficiaryInstitution.line1 == null) {&#xD;
		mandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line1,"tw.local.SwiftMessageData.beneficiaryInstitution.line1");&#xD;
		mandatory(tw.local.SwiftMessageData.beneficiaryInstitution.line2,"tw.local.SwiftMessageData.beneficiaryInstitution.line2");&#xD;
	}else{&#xD;
		tw.local.needBicVal = true;&#xD;
		tw.local.bicFields[0] = {};&#xD;
		tw.local.bicFields[0].name = "tw.local.SwiftMessageData.beneficiaryInstitution.line1";&#xD;
		tw.local.bicFields[0].value = tw.local.SwiftMessageData.beneficiaryInstitution.line1;&#xD;
	}&#xD;
}&#xD;
	&#xD;
&#xD;
	&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6" gatewayDirection="Unspecified" name="Exclusive Gateway" id="2025.f229bd37-1e77-4809-8ed3-63cccea08564">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="816" y="111" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f229bd37-1e77-4809-8ed3-63cccea08564" targetRef="2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c" name="Copy of To Create IDC Request" id="2027.5a057fc6-ef7b-47bc-8d17-7aa2507b7064">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f229bd37-1e77-4809-8ed3-63cccea08564" targetRef="6bbb4bbb-2c08-4022-9d2d-1fbf8476ce5d" name="To Get Request Number ,CBE And ECM Folder" id="2027.87bed5af-0ff3-4b80-80b7-3791979ac7b6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f" targetRef="2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7" name="To Exclusive Gateway 1" id="2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.770d3eea-9807-42ad-9ce2-c280a5145765" isCollection="false" name="SwiftMessageData" id="2056.f81d58e0-a120-4d07-95f2-01d8f270b37b" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4a9f455a-3d9a-4031-9252-e61fd7ade98c" targetRef="2025.d5f762d8-6e1b-4eca-a824-65c86b8b210f" name="To validation" id="2027.c8a47283-46cf-4180-aadb-a7eaf7b71b45">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="cf2ccaf4-df6c-41fd-8fd0-7c8181a65732">
                                        
                                        
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="needBicVal" id="2056.3d6ae48b-8024-4908-ab7b-d104b7c48a48" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="bicFields" id="2056.d0e0cabf-e8fe-45cb-b608-fb44bc0221a1" />
                            
                            
                            <ns16:exclusiveGateway default="2027.e758d99f-3bb5-4c32-83b1-14bed5161ada" name="Exclusive Gateway 1" id="2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="545" y="176" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b9b505ad-8db1-4ec9-bbe5-e1cd7b4a8218</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e758d99f-3bb5-4c32-83b1-14bed5161ada</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7" targetRef="2025.43557850-602e-49fd-8fd8-cdf919f8d94c" name="To Validate BIC" id="2027.e758d99f-3bb5-4c32-83b1-14bed5161ada">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.72840b39-03cc-42d8-a24b-01650b923101" default="2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182" name="Validate BIC" id="2025.43557850-602e-49fd-8fd8-cdf919f8d94c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="658" y="144" width="95" height="70" />
                                    
                                    
                                    <ns3:postAssignmentScript>for (var i=0; i&lt;tw.local.result.length; i++) {&#xD;
	if (!tw.local.result[i]) {&#xD;
	&#xD;
		tw.system.coachValidation.addValidationError(tw.local.bicFields[i].name, "not valid BIC code");&#xD;
	}&#xD;
}</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.e758d99f-3bb5-4c32-83b1-14bed5161ada</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.4c3f2342-57fd-4c9b-8569-9c7d53182e43</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.bicFields</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.876be99d-0b55-4982-8dae-2a839dc36cb6</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.result</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.43557850-602e-49fd-8fd8-cdf919f8d94c" targetRef="2025.f229bd37-1e77-4809-8ed3-63cccea08564" name="To Validate BIC" id="2027.5a21ab38-ff2f-4b79-85ec-65a14bf3e182">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" name="result" id="2056.348855d1-cffd-4a93-8aa8-f583917d94c8" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fed8ff93-436f-4942-8d45-fea1c5be5cd7" targetRef="2025.f229bd37-1e77-4809-8ed3-63cccea08564" name="To Exclusive Gateway" id="2027.0e88bccf-35f8-4006-8b91-ad01e77d0ed2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.needBicVal	  !=	  true</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="594d8836-e34f-4193-8713-c744caae0897">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                        
                        <ns3:mobileReady>true</ns3:mobileReady>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:inputSet id="ccd5e484-bdba-4b62-8aeb-fb34d4a9fc77" />
                        
                        
                        <ns16:outputSet id="28fa699d-1e8f-4130-b8a7-783d7b98bf4e" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f56249ef-649e-4216-9de3-419d674c8985</processLinkId>
            <processId>1.d4263736-0fea-47c5-90ea-cc6b49adfcec</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.989e8254-6920-47b4-ab30-995976e93a24</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c282d6b3-65cf-4bcf-9671-1f72bfad698c</toProcessItemId>
            <guid>800e53eb-6cfe-43f3-beb4-7814713c19b6</guid>
            <versionId>fda228ae-7703-4613-a367-d682c435b8d7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.989e8254-6920-47b4-ab30-995976e93a24</fromProcessItemId>
            <toProcessItemId>2025.c282d6b3-65cf-4bcf-9671-1f72bfad698c</toProcessItemId>
        </link>
    </process>
</teamworks>

