<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1" name="Client-Side Human Service">
        <lastModified>1692094045509</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f8e395a5-6a6a-4899-9837-907d2e696cb4</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>1eb7d3ac-0c72-47c9-93bd-1a7dab8b1a2c</guid>
        <versionId>3b6bd824-0af0-4285-a55d-5235f1cbefd2</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3"],"isInterrupting":true,"extensionElements":{"default":["2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"7c745920-7a5b-430f-badf-9ad5b0c27b3d"},{"incoming":["2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":898,"y":199,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"31a84e55-83d1-4b02-bc29-e3e224a2f14a"},{"itemSubjectRef":"itm.12.8dacb128-2b07-4eaf-a2c1-3a0cb480a228","name":"productDetails","isCollection":false,"declaredType":"dataObject","id":"2056.74497a6a-389a-4eaa-8304-e9a045588566"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\n\/\/autoObject.financialDetails.usedAdvancePayment[0] = {};\n\/\/autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\n\/\/autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\n\/\/autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\n\/\/autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\n\/\/autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = true;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idc","isCollection":false,"declaredType":"dataObject","id":"2056.ebbc7128-faa8-4835-b684-c020ad512f23"},{"targetRef":"2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3","sourceRef":"7c745920-7a5b-430f-badf-9ad5b0c27b3d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].accountCurrency = \"\";\nautoObject[0].accountClass = \"\";\nautoObject[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject[0].GLAccountNumber = \"\";\nautoObject[0].accountBranchCode = \"\";\nautoObject[0].accountNumber = \"\";\r\nautoObject[1] = {};\r\nautoObject[1].accountCurrency = \"\";\r\nautoObject[1].accountClass = \"\";\r\nautoObject[1].debitedAmtinCollateralCurrency = 0.0;\r\nautoObject[1].GLAccountNumber = \"\";\r\nautoObject[1].accountBranchCode = \"\";\r\nautoObject[1].accountNumber = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5a746545-c6b4-49a5-85fe-aaccc003f1ef","name":"cash","isCollection":true,"declaredType":"dataObject","id":"2056.ad5ad28b-d89f-4c93-991d-3d790b4b30f9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.373a1fe9-e80a-40c0-890a-e617d6b4c3b6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatString","isCollection":false,"declaredType":"dataObject","id":"2056.********-a20a-4619-afa4-ca2267399027"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = [];\nautoObject.settlementAccounts[0] = {};\nautoObject.settlementAccounts[0].debitedAccount = {};\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = {};\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\nautoObject.settlementAccounts[0].debitedAmount = {};\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = [];\nautoObject.settlementAccounts[0].accountNumberList[0] = {};\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = {};\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = [];\nautoObject.party[0] = {};\nautoObject.party[0].partyType = {};\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = {};\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = {};\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new Date();\nautoObject.liquidationSummary.creditValueDate = new Date();\nautoObject.IDCProduct = {};\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new Date();\nautoObject.transactionMaturityDate = new Date();\nautoObject.commissionsAndCharges = [];\nautoObject.commissionsAndCharges[0] = {};\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = {};\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = {};\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = {};\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = [];\nautoObject.commissionsAndCharges[0].commAccountList[0] = {};\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new Date();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = {};\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = {};\nautoObject.swiftMessageData.intermediary = {};\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = {};\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = {};\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = {};\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = {};\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = {};\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = {};\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = {};\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = {};\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = {};\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = [];\nautoObject.advices[0] = {};\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = {};\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = [];\nautoObject.cashCollateralAccounts[0] = {};\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = {};\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new Date();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = [];\nautoObject.contractLimitsTracking[0] = {};\nautoObject.contractLimitsTracking[0].partyType = {};\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new Date();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = [];\nautoObject.accountNumberList[0] = {};\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject.facilities = [];\nautoObject.facilities[0] = {};\nautoObject.facilities[0].facilityCode = \"\";\nautoObject.facilities[0].overallLimit = 0.0;\nautoObject.facilities[0].limitAmount = 0.0;\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\nautoObject.facilities[0].availableAmount = 0.0;\nautoObject.facilities[0].expiryDate = new Date();\nautoObject.facilities[0].availableFlag = false;\nautoObject.facilities[0].authorizedFlag = false;\nautoObject.facilities[0].Utilization = 0.0;\nautoObject.facilities[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines = [];\nautoObject.facilities[0].facilityLines[0] = {};\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].expiryDate = new Date();\nautoObject.facilities[0].facilityLines[0].facilityBranch = {};\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines[0].LGCommission = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches = [];\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts = [];\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = [];\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = [];\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\nautoObject.facilities[0].facilityLines[0].partyType = {};\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\nautoObject.facilities[0].status = \"\";\nautoObject.facilities[0].facilityCurrency = {};\nautoObject.facilities[0].facilityCurrency.name = \"\";\nautoObject.facilities[0].facilityCurrency.value = \"\";\nautoObject.facilities[0].facilityID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"declaredType":"dataObject","id":"2056.d07cfd78-2b2c-4353-97ec-0236ba650ff8"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountList2","isCollection":true,"declaredType":"dataObject","id":"2056.d70b135c-127f-43c4-a10b-e3fb8c4d4044"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].partyType = {};\nautoObject[0].partyType.name = \"\";\nautoObject[0].partyType.value = \"\";\nautoObject[0].partyId = \"\";\nautoObject[0].name = \"\";\nautoObject[0].country = \"\";\nautoObject[0].reference = \"\";\nautoObject[0].address1 = \"\";\nautoObject[0].address2 = \"\";\nautoObject[0].address3 = \"\";\nautoObject[0].address4 = \"\";\nautoObject[0].media = \"\";\nautoObject[0].address = [];\nautoObject[0].address[0] = \"\";\nautoObject[0].phone = \"\";\nautoObject[0].fax = \"\";\nautoObject[0].email = \"\";\nautoObject[0].contactPersonName = \"\";\nautoObject[0].mobile = \"\";\nautoObject[0].branch = {};\nautoObject[0].branch.name = \"\";\nautoObject[0].branch.value = \"\";\nautoObject[0].language = \"\";\nautoObject[0].partyCIF = \"\";\nautoObject[0].isNbeCustomer = false;\nautoObject"}]},"itemSubjectRef":"itm.12.af731d60-bee2-4d24-bfed-30192291dbd7","name":"parties","isCollection":true,"declaredType":"dataObject","id":"2056.711615ea-56cd-4408-8367-39f520515f39"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"caseCIF","isCollection":false,"declaredType":"dataObject","id":"2056.f9809b03-35ca-42bb-a645-07b3f4157503"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"accounteeCIF","isCollection":false,"declaredType":"dataObject","id":"2056.6b11e36f-9b51-4318-8b02-fdb6633ff106"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"Drawer\";\nautoObject[0].value = \"Drawer\";\r\nautoObject[1].name = \"Drawer\";\r\nautoObject[1].value = \"Drawer\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"partyTypeList","isCollection":true,"declaredType":"dataObject","id":"2056.8d2988eb-78b0-4a14-b5bc-21a0ee27735e"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":202,"y":59,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Objects Init","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.79445ec1-0e43-4217-8cf9-49d77b01afd3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcContract = {};\r\ntw.local.idcContract.contractLimitsTracking = [];\r\ntw.local.idcContract.contractLimitsTracking[0] = {};\r\ntw.local.idcContract.contractLimitsTracking[0].partyType = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].type = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].jointVentureParent = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].customerNo = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].linkageRefNum = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].amountTag = \"\";\r\ntw.local.idcContract.contractLimitsTracking[0].contributionPercentage = 0.0;\r\ntw.local.idcContract.settlementAccounts = [];\r\ntw.local.idcContract.settlementAccounts[0] = {};\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount = {};\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.balanceSign = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = {};\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.currency = {};\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.currency.name = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.currency.value = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.accountNumber = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = \"\";\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\r\ntw.local.idcContract.settlementAccounts[0].debitedAmount = {};\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\r\n\/\/tw.local.idcContract.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\r\n\r\n\r\n\r\ntw.local.idcContract.billAmount = 0.0;\r\ntw.local.idcContract.billCurrency = {};\r\n\r\ntw.local.idcContract.party = [];\r\ntw.local.idcContract.party[0] = {};\r\ntw.local.idcContract.party[0].partyType = {};\r\ntw.local.idcContract.party[0].partyType.name = \"Drawee\";\r\ntw.local.idcContract.party[0].partyType.value = \"Drawee\";\r\ntw.local.idcContract.party[0].partyId = \"\";\r\ntw.local.idcContract.party[0].name = \"\";\r\ntw.local.idcContract.party[0].country = \"\";\r\ntw.local.idcContract.party[0].reference = \"\";\r\ntw.local.idcContract.party[0].address1 = \"\";\r\ntw.local.idcContract.party[0].address2 = \"\";\r\ntw.local.idcContract.party[0].address3 = \"\";\r\ntw.local.idcContract.party[0].address4 = \"\";\r\ntw.local.idcContract.party[0].media = \"\";\r\ntw.local.idcContract.party[0].address = [];\r\ntw.local.idcContract.party[0].address[0] = \"\";\r\ntw.local.idcContract.party[0].phone = \"\";\r\ntw.local.idcContract.party[0].fax = \"\";\r\ntw.local.idcContract.party[0].email = \"\";\r\ntw.local.idcContract.party[0].contactPersonName = \"\";\r\ntw.local.idcContract.party[0].mobile = \"\";\r\ntw.local.idcContract.party[0].branch = {}\r\ntw.local.idcContract.party[0].branch.name = \"\";\r\ntw.local.idcContract.party[0].branch.value = \"\";\r\ntw.local.idcContract.party[0].language = \"\";\r\ntw.local.idcContract.party[0].partyCIF = \"\";\r\ntw.local.idcContract.party[0].isNbeCustomer = false;\r\n\r\ntw.local.idcContract.party[1] = {};\r\ntw.local.idcContract.party[1].partyType = {};\r\ntw.local.idcContract.party[1].partyType.name = \"Drawer\";\r\ntw.local.idcContract.party[1].partyType.value = \"Drawer\";\r\ntw.local.idcContract.party[1].partyId = \"\";\r\ntw.local.idcContract.party[1].name = \"\";\r\ntw.local.idcContract.party[1].country = \"\";\r\ntw.local.idcContract.party[1].reference = \"\";\r\ntw.local.idcContract.party[1].address1 = \"\";\r\ntw.local.idcContract.party[1].address2 = \"\";\r\ntw.local.idcContract.party[1].address3 = \"\";\r\ntw.local.idcContract.party[1].address4 = \"\";\r\ntw.local.idcContract.party[1].media = \"\";\r\ntw.local.idcContract.party[1].address = [];\r\ntw.local.idcContract.party[1].address[0] = \"\";\r\ntw.local.idcContract.party[1].phone = \"\";\r\ntw.local.idcContract.party[1].fax = \"\";\r\ntw.local.idcContract.party[1].email = \"\";\r\ntw.local.idcContract.party[1].contactPersonName = \"\";\r\ntw.local.idcContract.party[1].mobile = \"\";\r\ntw.local.idcContract.party[1].branch = {}\r\ntw.local.idcContract.party[1].branch.name = \"\";\r\ntw.local.idcContract.party[1].branch.value = \"\";\r\ntw.local.idcContract.party[1].language = \"\";\r\ntw.local.idcContract.party[1].partyCIF = \"\";\r\ntw.local.idcContract.party[1].isNbeCustomer = false;\r\n\r\ntw.local.idcContract.liquidationSummary = {};\r\ntw.local.idcContract.IDCProduct = {};\r\ntw.local.idcContract.commissionsAndCharges = [];\r\n\r\ntw.local.idcContract.collateralCurrency = {};\r\ntw.local.idcContract.swiftMessageData = {};\r\ntw.local.idcContract.swiftMessageData.intermediary = {};\r\ntw.local.idcContract.swiftMessageData.accountWithInstitution = {};\r\ntw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};\r\ntw.local.idcContract.swiftMessageData.receiverCorrespondent = {};\r\ntw.local.idcContract.swiftMessageData.detailsOfPayment = {};\r\n\r\ntw.local.idcContract.swiftMessageData.orderingInstitution = {};\r\n\r\ntw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};\r\ntw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};\r\ntw.local.idcContract.swiftMessageData.orderingCustomer = {};\r\ntw.local.idcContract.swiftMessageData.senderToReciever = {};\r\n\r\ntw.local.idcContract.advices = [];\r\n\r\ntw.local.idcContract.cashCollateralAccounts = [];\r\n\r\n"]}},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"variable1","isCollection":false,"declaredType":"dataObject","id":"2056.75f8a48c-4111-4a26-825b-7f6d05bc558d"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpUsedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.3140c45f-a269-4e05-88af-72f77a5aba61"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"actions","isCollection":true,"declaredType":"dataObject","id":"2056.82334f18-e19a-4a8a-89e6-7074b9c22a03"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.314af613-**************-592d9ea16a3b"},{"outgoing":["2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85"],"incoming":["2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":552,"y":176,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"new_view1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"94fd09a7-9477-466b-89eb-7012b812284b","optionName":"@label","value":"new view"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bbaf0814-9c87-433e-84a8-fe877ad82705","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ec0efbe4-2205-4f69-85fd-21f70f614031","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.7a7ff012-3e34-4c17-949a-ed83917c49bf","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"105d3d73-d9de-4928-8219-4577355cd33d","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1cffe75c-9ed3-40fc-8aed-4d11172adf06","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"982c8b9d-042c-4ced-8518-45d2c35c6b64"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach 1","isForCompensation":false,"completionQuantity":1,"id":"2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8"},{"targetRef":"31a84e55-83d1-4b02-bc29-e3e224a2f14a","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"6f046b3f-1d34-4bd9-8013-8e244e4de266","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85","sourceRef":"2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"5103acd6-89c5-4191-8217-0e2df53fd663","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"3771155a-75a1-45b6-b515-15c62be36c77","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Client-Side Human Service","declaredType":"globalUserTask","id":"1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1","ioSpecification":{"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.3cdc3d62-8358-459b-8c20-2ae7bb7e6fe8"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}]},"inputSet":[{"id":"6e4e357c-9663-4608-aa73-2399da446131"}],"outputSet":[{"id":"053c4d0e-14d8-4a33-888f-412a1a079ed8"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"c459a972-a166-4d87-80ed-6ba720a4bae2"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processVariable name="productDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.74497a6a-389a-4eaa-8304-e9a045588566</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.8dacb128-2b07-4eaf-a2c1-3a0cb480a228</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ca608347-72e5-4539-ade5-0fce6745f706</guid>
            <versionId>6fb085cf-4854-47ce-9b76-f20ed10a4be0</versionId>
        </processVariable>
        <processVariable name="idc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ebbc7128-faa8-4835-b684-c020ad512f23</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b54a06f2-2832-4fea-8485-50a4bccbab80</guid>
            <versionId>bc9cb2a8-f58c-45f3-9acb-3ef8c38b8896</versionId>
        </processVariable>
        <processVariable name="cash">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ad5ad28b-d89f-4c93-991d-3d790b4b30f9</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.5a746545-c6b4-49a5-85fe-aaccc003f1ef</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ede9239-98f0-4834-91be-baaafe035237</guid>
            <versionId>d8822781-c684-42dc-bb76-3d65e2e9aa1d</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.373a1fe9-e80a-40c0-890a-e617d6b4c3b6</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9d812412-6049-4b34-b069-477168cf99a7</guid>
            <versionId>9ea7b878-4f83-477a-a469-b5d219287e7c</versionId>
        </processVariable>
        <processVariable name="concatString">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-a20a-4619-afa4-ca2267399027</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>821ce9c7-ec1c-4aea-b329-8aefd41cb824</guid>
            <versionId>609f873d-b56a-4d55-8021-bdb68d576217</versionId>
        </processVariable>
        <processVariable name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d07cfd78-2b2c-4353-97ec-0236ba650ff8</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>014f51e9-96b4-4f73-ba7e-249d50c9a953</guid>
            <versionId>f367daa3-6293-48bf-8a73-7978292a47b5</versionId>
        </processVariable>
        <processVariable name="accountList2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d70b135c-127f-43c4-a10b-e3fb8c4d4044</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>********-487d-4721-96dd-63058b5c6fe5</guid>
            <versionId>0bbf0867-3a72-4260-ab3a-9e361581be5a</versionId>
        </processVariable>
        <processVariable name="parties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.711615ea-56cd-4408-8367-39f520515f39</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.af731d60-bee2-4d24-bfed-30192291dbd7</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4e1db277-d916-410a-a750-5c04f6509d16</guid>
            <versionId>6a090ce4-8b07-472f-b28a-1a2809239b2e</versionId>
        </processVariable>
        <processVariable name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f9809b03-35ca-42bb-a645-07b3f4157503</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fc7e7f85-38c7-49f1-84b8-6aa274c6a4f0</guid>
            <versionId>8b8b5899-8c31-4522-8fb2-27eb64bb149d</versionId>
        </processVariable>
        <processVariable name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6b11e36f-9b51-4318-8b02-fdb6633ff106</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fd9f9874-1fc2-49bc-bbbb-8ed72d94a54e</guid>
            <versionId>ca590d97-8708-4a3c-b474-428b6fc6af82</versionId>
        </processVariable>
        <processVariable name="partyTypeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8d2988eb-78b0-4a14-b5bc-21a0ee27735e</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>67048d6e-b0be-4e14-8b65-8384e15f4755</guid>
            <versionId>4706ef99-85cb-4073-9044-4079af1773bf</versionId>
        </processVariable>
        <processVariable name="variable1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.75f8a48c-4111-4a26-825b-7f6d05bc558d</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>32a28783-cb84-47f7-97bc-7379b1b7dc7e</guid>
            <versionId>ba379187-01ff-4f9a-972b-4bdeab165afc</versionId>
        </processVariable>
        <processVariable name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3140c45f-a269-4e05-88af-72f77a5aba61</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d0c2a65b-28aa-4a40-881f-3b937052d21b</guid>
            <versionId>bcd76251-9ef4-4b1f-976b-6b1f4ce4eefc</versionId>
        </processVariable>
        <processVariable name="actions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.82334f18-e19a-4a8a-89e6-7074b9c22a03</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a7552218-798f-4ad0-b77d-1a31bf0c8e44</guid>
            <versionId>20551fb6-f691-4011-ab16-658ce4f364ab</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.314af613-**************-592d9ea16a3b</processVariableId>
            <description isNull="true" />
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8540fbe6-df94-4029-8d17-6611de1e7e33</guid>
            <versionId>6deef308-514f-4f36-a4aa-34ad0de5b8a5</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.040a8d6e-c255-4ad0-b553-0c840e0503e4</processItemId>
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.afc4df7b-7c65-499b-95d8-36945b0c1747</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-623e</guid>
            <versionId>9b67ba9f-d3d8-45e5-b519-47d02dd6df5b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.afc4df7b-7c65-499b-95d8-36945b0c1747</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>120ed4d1-1778-400b-910c-abe675ee6150</guid>
                <versionId>74d02fdc-22ca-4d4e-bca7-bc0ce528adf0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f8e395a5-6a6a-4899-9837-907d2e696cb4</processItemId>
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.64809b23-2c4f-43c5-ac79-a0530f495b55</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-623f</guid>
            <versionId>9d9a19ac-8f41-4fe5-92c2-7ccdcb379f55</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.86f2577d-6e5a-4b43-b768-b7d0bb7c9106</resourceProcessLinkId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <guid>96f292a5-ca51-48dc-8b1d-849d8b757a2d</guid>
            <versionId>336d5270-c51e-48ab-9a1d-7301f094289f</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.f8e395a5-6a6a-4899-9837-907d2e696cb4</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="c459a972-a166-4d87-80ed-6ba720a4bae2" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="Client-Side Human Service" id="1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="3771155a-75a1-45b6-b515-15c62be36c77">
                            
                            
                            <ns16:startEvent name="Start" id="7c745920-7a5b-430f-badf-9ad5b0c27b3d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:default>2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="31a84e55-83d1-4b02-bc29-e3e224a2f14a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="898" y="199" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.8dacb128-2b07-4eaf-a2c1-3a0cb480a228" isCollection="false" name="productDetails" id="2056.74497a6a-389a-4eaa-8304-e9a045588566" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" name="idc" id="2056.ebbc7128-faa8-4835-b684-c020ad512f23">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
//autoObject.financialDetails.usedAdvancePayment[0] = {};
//autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
//autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
//autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
//autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
//autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
//autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
//autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
//autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
//autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
//autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
//autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
//autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
//autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = true;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:sequenceFlow sourceRef="7c745920-7a5b-430f-badf-9ad5b0c27b3d" targetRef="2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8" name="To Client-Side Script" id="2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5a746545-c6b4-49a5-85fe-aaccc003f1ef" isCollection="true" name="cash" id="2056.ad5ad28b-d89f-4c93-991d-3d790b4b30f9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].accountCurrency = "";
autoObject[0].accountClass = "";
autoObject[0].debitedAmtinCollateralCurrency = 0.0;
autoObject[0].GLAccountNumber = "";
autoObject[0].accountBranchCode = "";
autoObject[0].accountNumber = "";&#xD;
autoObject[1] = {};&#xD;
autoObject[1].accountCurrency = "";&#xD;
autoObject[1].accountClass = "";&#xD;
autoObject[1].debitedAmtinCollateralCurrency = 0.0;&#xD;
autoObject[1].GLAccountNumber = "";&#xD;
autoObject[1].accountBranchCode = "";&#xD;
autoObject[1].accountNumber = "";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.373a1fe9-e80a-40c0-890a-e617d6b4c3b6" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatString" id="2056.********-a20a-4619-afa4-ca2267399027" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" name="idcContract" id="2056.d07cfd78-2b2c-4353-97ec-0236ba650ff8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = [];
autoObject.settlementAccounts[0] = {};
autoObject.settlementAccounts[0].debitedAccount = {};
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = {};
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = {};
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = [];
autoObject.settlementAccounts[0].accountNumberList[0] = {};
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = {};
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = [];
autoObject.party[0] = {};
autoObject.party[0].partyType = {};
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = {};
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = {};
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new Date();
autoObject.liquidationSummary.creditValueDate = new Date();
autoObject.IDCProduct = {};
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new Date();
autoObject.transactionMaturityDate = new Date();
autoObject.commissionsAndCharges = [];
autoObject.commissionsAndCharges[0] = {};
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = {};
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = {};
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = {};
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = {};
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = [];
autoObject.commissionsAndCharges[0].commAccountList[0] = {};
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new Date();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = {};
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = {};
autoObject.swiftMessageData.intermediary = {};
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = {};
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = {};
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = {};
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = {};
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = {};
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = {};
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = {};
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = {};
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = {};
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = [];
autoObject.advices[0] = {};
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = {};
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = [];
autoObject.cashCollateralAccounts[0] = {};
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = {};
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new Date();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = [];
autoObject.contractLimitsTracking[0] = {};
autoObject.contractLimitsTracking[0].partyType = {};
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new Date();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = [];
autoObject.accountNumberList[0] = {};
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = [];
autoObject.facilities[0] = {};
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new Date();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = [];
autoObject.facilities[0].facilityLines[0] = {};
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new Date();
autoObject.facilities[0].facilityLines[0].facilityBranch = {};
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = [];
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = [];
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = [];
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = [];
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = {};
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = {};
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountList2" id="2056.d70b135c-127f-43c4-a10b-e3fb8c4d4044" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.af731d60-bee2-4d24-bfed-30192291dbd7" isCollection="true" name="parties" id="2056.711615ea-56cd-4408-8367-39f520515f39">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].partyType = {};
autoObject[0].partyType.name = "";
autoObject[0].partyType.value = "";
autoObject[0].partyId = "";
autoObject[0].name = "";
autoObject[0].country = "";
autoObject[0].reference = "";
autoObject[0].address1 = "";
autoObject[0].address2 = "";
autoObject[0].address3 = "";
autoObject[0].address4 = "";
autoObject[0].media = "";
autoObject[0].address = [];
autoObject[0].address[0] = "";
autoObject[0].phone = "";
autoObject[0].fax = "";
autoObject[0].email = "";
autoObject[0].contactPersonName = "";
autoObject[0].mobile = "";
autoObject[0].branch = {};
autoObject[0].branch.name = "";
autoObject[0].branch.value = "";
autoObject[0].language = "";
autoObject[0].partyCIF = "";
autoObject[0].isNbeCustomer = false;
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="caseCIF" id="2056.f9809b03-35ca-42bb-a645-07b3f4157503" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="accounteeCIF" id="2056.6b11e36f-9b51-4318-8b02-fdb6633ff106" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="partyTypeList" id="2056.8d2988eb-78b0-4a14-b5bc-21a0ee27735e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "Drawer";
autoObject[0].value = "Drawer";&#xD;
autoObject[1].name = "Drawer";&#xD;
autoObject[1].value = "Drawer";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Objects Init" id="2025.79445ec1-0e43-4217-8cf9-49d77b01afd3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="202" y="59" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:script>tw.local.idcContract = {};&#xD;
tw.local.idcContract.contractLimitsTracking = [];&#xD;
tw.local.idcContract.contractLimitsTracking[0] = {};&#xD;
tw.local.idcContract.contractLimitsTracking[0].partyType = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].type = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].jointVentureParent = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].customerNo = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].linkageRefNum = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].amountTag = "";&#xD;
tw.local.idcContract.contractLimitsTracking[0].contributionPercentage = 0.0;&#xD;
tw.local.idcContract.settlementAccounts = [];&#xD;
tw.local.idcContract.settlementAccounts[0] = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount = {};&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.balanceSign = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.GLAccountNumber = "";&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = {};&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.currency = {};&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.currency.name = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.currency.value = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.accountBranchCode = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.accountBalance = 0.0;&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.accountNumber = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "";&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAccount.ownerAccounts = "";&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAmount = {};&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;&#xD;
//tw.local.idcContract.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;&#xD;
&#xD;
&#xD;
&#xD;
tw.local.idcContract.billAmount = 0.0;&#xD;
tw.local.idcContract.billCurrency = {};&#xD;
&#xD;
tw.local.idcContract.party = [];&#xD;
tw.local.idcContract.party[0] = {};&#xD;
tw.local.idcContract.party[0].partyType = {};&#xD;
tw.local.idcContract.party[0].partyType.name = "Drawee";&#xD;
tw.local.idcContract.party[0].partyType.value = "Drawee";&#xD;
tw.local.idcContract.party[0].partyId = "";&#xD;
tw.local.idcContract.party[0].name = "";&#xD;
tw.local.idcContract.party[0].country = "";&#xD;
tw.local.idcContract.party[0].reference = "";&#xD;
tw.local.idcContract.party[0].address1 = "";&#xD;
tw.local.idcContract.party[0].address2 = "";&#xD;
tw.local.idcContract.party[0].address3 = "";&#xD;
tw.local.idcContract.party[0].address4 = "";&#xD;
tw.local.idcContract.party[0].media = "";&#xD;
tw.local.idcContract.party[0].address = [];&#xD;
tw.local.idcContract.party[0].address[0] = "";&#xD;
tw.local.idcContract.party[0].phone = "";&#xD;
tw.local.idcContract.party[0].fax = "";&#xD;
tw.local.idcContract.party[0].email = "";&#xD;
tw.local.idcContract.party[0].contactPersonName = "";&#xD;
tw.local.idcContract.party[0].mobile = "";&#xD;
tw.local.idcContract.party[0].branch = {}&#xD;
tw.local.idcContract.party[0].branch.name = "";&#xD;
tw.local.idcContract.party[0].branch.value = "";&#xD;
tw.local.idcContract.party[0].language = "";&#xD;
tw.local.idcContract.party[0].partyCIF = "";&#xD;
tw.local.idcContract.party[0].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.party[1] = {};&#xD;
tw.local.idcContract.party[1].partyType = {};&#xD;
tw.local.idcContract.party[1].partyType.name = "Drawer";&#xD;
tw.local.idcContract.party[1].partyType.value = "Drawer";&#xD;
tw.local.idcContract.party[1].partyId = "";&#xD;
tw.local.idcContract.party[1].name = "";&#xD;
tw.local.idcContract.party[1].country = "";&#xD;
tw.local.idcContract.party[1].reference = "";&#xD;
tw.local.idcContract.party[1].address1 = "";&#xD;
tw.local.idcContract.party[1].address2 = "";&#xD;
tw.local.idcContract.party[1].address3 = "";&#xD;
tw.local.idcContract.party[1].address4 = "";&#xD;
tw.local.idcContract.party[1].media = "";&#xD;
tw.local.idcContract.party[1].address = [];&#xD;
tw.local.idcContract.party[1].address[0] = "";&#xD;
tw.local.idcContract.party[1].phone = "";&#xD;
tw.local.idcContract.party[1].fax = "";&#xD;
tw.local.idcContract.party[1].email = "";&#xD;
tw.local.idcContract.party[1].contactPersonName = "";&#xD;
tw.local.idcContract.party[1].mobile = "";&#xD;
tw.local.idcContract.party[1].branch = {}&#xD;
tw.local.idcContract.party[1].branch.name = "";&#xD;
tw.local.idcContract.party[1].branch.value = "";&#xD;
tw.local.idcContract.party[1].language = "";&#xD;
tw.local.idcContract.party[1].partyCIF = "";&#xD;
tw.local.idcContract.party[1].isNbeCustomer = false;&#xD;
&#xD;
tw.local.idcContract.liquidationSummary = {};&#xD;
tw.local.idcContract.IDCProduct = {};&#xD;
tw.local.idcContract.commissionsAndCharges = [];&#xD;
&#xD;
tw.local.idcContract.collateralCurrency = {};&#xD;
tw.local.idcContract.swiftMessageData = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediary = {};&#xD;
tw.local.idcContract.swiftMessageData.accountWithInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.receiverCorrespondent = {};&#xD;
tw.local.idcContract.swiftMessageData.detailsOfPayment = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.orderingInstitution = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};&#xD;
tw.local.idcContract.swiftMessageData.orderingCustomer = {};&#xD;
tw.local.idcContract.swiftMessageData.senderToReciever = {};&#xD;
&#xD;
tw.local.idcContract.advices = [];&#xD;
&#xD;
tw.local.idcContract.cashCollateralAccounts = [];&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="variable1" id="2056.75f8a48c-4111-4a26-825b-7f6d05bc558d" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpUsedAdvancePayment" id="2056.3140c45f-a269-4e05-88af-72f77a5aba61" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="actions" id="2056.82334f18-e19a-4a8a-89e6-7074b9c22a03">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = "";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.314af613-**************-592d9ea16a3b" />
                            
                            
                            <ns3:formTask name="Coach 1" id="2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="552" y="176" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a707e911-f0be-4387-90a3-f9ac3d3b0ff3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>105d3d73-d9de-4928-8219-4577355cd33d</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>new_view1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>94fd09a7-9477-466b-89eb-7012b812284b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>new view</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bbaf0814-9c87-433e-84a8-fe877ad82705</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ec0efbe4-2205-4f69-85fd-21f70f614031</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.7a7ff012-3e34-4c17-949a-ed83917c49bf</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                
                                                
                                                <ns19:id>982c8b9d-042c-4ced-8518-45d2c35c6b64</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1cffe75c-9ed3-40fc-8aed-4d11172adf06</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>OK</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4bea93b4-3aab-4d26-8c14-c19aea923ed8" targetRef="31a84e55-83d1-4b02-bc29-e3e224a2f14a" name="To Coach" id="2027.1bdb080c-b216-417d-8e68-6f8f9ed89a85">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="6f046b3f-1d34-4bd9-8013-8e244e4de266">
                                        
                                        
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="5103acd6-89c5-4191-8217-0e2df53fd663">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                        
                        <ns3:mobileReady>true</ns3:mobileReady>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.3cdc3d62-8358-459b-8c20-2ae7bb7e6fe8</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:inputSet id="6e4e357c-9663-4608-aa73-2399da446131" />
                        
                        
                        <ns16:outputSet id="053c4d0e-14d8-4a33-888f-412a1a079ed8" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.17214274-4c65-4600-8896-95730bb10ece</processLinkId>
            <processId>1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f8e395a5-6a6a-4899-9837-907d2e696cb4</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.040a8d6e-c255-4ad0-b553-0c840e0503e4</toProcessItemId>
            <guid>be0154de-b7fb-4f50-9043-5f731f37144b</guid>
            <versionId>03c40af2-017e-4f22-951f-a1176cc0a51f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.f8e395a5-6a6a-4899-9837-907d2e696cb4</fromProcessItemId>
            <toProcessItemId>2025.040a8d6e-c255-4ad0-b553-0c840e0503e4</toProcessItemId>
        </link>
    </process>
</teamworks>

