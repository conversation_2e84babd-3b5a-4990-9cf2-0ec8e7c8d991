<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.7073c330-e8a8-4bd8-94fb-d764787dffeb" name="Execution HUB Filter Service">
        <lastModified>1688903820062</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.e9a2fecc-16d9-457d-ac10-460498b47b07</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>b76f2ce0-5d29-4b41-bc7f-daedaddbff02</guid>
        <versionId>bb90ce49-9010-4cee-8d4d-ea8f86ab229d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:246979e51bde3d7d:-3bedc06d:18939e75821:-2805" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.6dbacd30-1b08-419d-89d6-979e55c112a8"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"446c2e0c-7e3a-4775-b215-cb34b03ae375"},{"incoming":["c1b9014b-c931-4e1e-8509-a1f770bc08e6"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60a8"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793"},{"targetRef":"e9a2fecc-16d9-457d-ac10-460498b47b07","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get filtered team","declaredType":"sequenceFlow","id":"2027.6dbacd30-1b08-419d-89d6-979e55c112a8","sourceRef":"446c2e0c-7e3a-4775-b215-cb34b03ae375"},{"startQuantity":1,"outgoing":["c1b9014b-c931-4e1e-8509-a1f770bc08e6"],"incoming":["2027.6dbacd30-1b08-419d-89d6-979e55c112a8"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":299,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get filtered team","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e9a2fecc-16d9-457d-ac10-460498b47b07","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.originalTeam = new tw.object.Team();\r\ntw.local.originalTeam.members = new tw.object.listOf.String();\r\ntw.local.originalTeam.members[0] = \"idchubexemkr01\";\r\ntw.local.originalTeam.members[1] = \"idchubexemkr02\";\r\ntw.local.originalTeam.members[2] = \"idchubexemkr59\";\r\ntw.local.originalTeam.managerTeam = \"Managers\";\r\ntw.local.originalTeam.name = \"IDC EXE HUB\";\r\n\r\ntw.local.filteredTeam = new tw.object.Team();\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\n\r\nvar users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;\r\n\t\r\nlog.info(\"users::: \"+ users);\r\n\tfor (var i = 0; i &lt; users.listLength ; i++)\r\n\t{\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\n\t}"]}},{"targetRef":"05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c1b9014b-c931-4e1e-8509-a1f770bc08e6","sourceRef":"e9a2fecc-16d9-457d-ac10-460498b47b07"}],"laneSet":[{"id":"c360c33d-9334-47a7-9d92-665d96128f73","lane":[{"flowNodeRef":["446c2e0c-7e3a-4775-b215-cb34b03ae375","05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793","e9a2fecc-16d9-457d-ac10-460498b47b07"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"54e15675-cbec-4d53-95a8-5861124179b3","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Execution HUB Filter Service","declaredType":"process","id":"1.7073c330-e8a8-4bd8-94fb-d764787dffeb","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"filteredTeam","isCollection":false,"id":"2055.e9a62d27-f6b1-47db-b793-fa16e59969ff","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{"dataInputRefs":["2055.9cbe7cec-5419-4783-a668-d8175ceef614","2055.13fb133c-a966-4e5d-9268-0c0a158492c5"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"originalTeam","isCollection":false,"id":"2055.9cbe7cec-5419-4783-a668-d8175ceef614","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"BPM_IDC_HUB_0599_EXE_MKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"groupName","isCollection":false,"id":"2055.13fb133c-a966-4e5d-9268-0c0a158492c5"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9cbe7cec-5419-4783-a668-d8175ceef614</processParameterId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e2101777-29bb-49ec-a5ea-dc6647964911</guid>
            <versionId>ccd938a2-85f0-4fc9-9360-8d3b62c08500</versionId>
        </processParameter>
        <processParameter name="groupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.13fb133c-a966-4e5d-9268-0c0a158492c5</processParameterId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"BPM_IDC_HUB_0599_EXE_MKR"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>77ea8158-e4c6-4f0e-9a12-a4bf442500ca</guid>
            <versionId>ed8e5ee9-7c0f-4cad-8d1b-9c06f4877d88</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e9a62d27-f6b1-47db-b793-fa16e59969ff</processParameterId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>93454d05-7c82-4379-a626-0057d7c45bfe</guid>
            <versionId>04e458ba-fe61-4b10-93b9-8e27336d7c04</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793</processItemId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.89f8f74f-6bc2-401b-be65-b4a36ec41a2e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60a8</guid>
            <versionId>950dab87-d087-4525-bd51-0a54e516ef42</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.89f8f74f-6bc2-401b-be65-b4a36ec41a2e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>e7b2be12-676c-4727-b1ff-c14102068cda</guid>
                <versionId>0d831c79-730f-410c-90ce-2d4ab266bd7e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e9a2fecc-16d9-457d-ac10-460498b47b07</processItemId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <name>Get filtered team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.468c1c4e-f783-4c3f-ac89-78751f48ef10</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60a7</guid>
            <versionId>cb2659c8-cbb3-480e-83c8-6d73bc136d68</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="299" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.468c1c4e-f783-4c3f-ac89-78751f48ef10</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.originalTeam = new tw.object.Team();&#xD;
tw.local.originalTeam.members = new tw.object.listOf.String();&#xD;
tw.local.originalTeam.members[0] = "idchubexemkr01";&#xD;
tw.local.originalTeam.members[1] = "idchubexemkr02";&#xD;
tw.local.originalTeam.members[2] = "idchubexemkr59";&#xD;
tw.local.originalTeam.managerTeam = "Managers";&#xD;
tw.local.originalTeam.name = "IDC EXE HUB";&#xD;
&#xD;
tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</script>
                <isRule>false</isRule>
                <guid>f15576fb-e932-4498-abf2-447b604eddf8</guid>
                <versionId>8d10464f-520a-4fbb-a045-0a37ed093582</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.e9a2fecc-16d9-457d-ac10-460498b47b07</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Execution HUB Filter Service" id="1.7073c330-e8a8-4bd8-94fb-d764787dffeb" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.9cbe7cec-5419-4783-a668-d8175ceef614" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="groupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.13fb133c-a966-4e5d-9268-0c0a158492c5">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"BPM_IDC_HUB_0599_EXE_MKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.e9a62d27-f6b1-47db-b793-fa16e59969ff" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9cbe7cec-5419-4783-a668-d8175ceef614</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.13fb133c-a966-4e5d-9268-0c0a158492c5</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c360c33d-9334-47a7-9d92-665d96128f73">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="54e15675-cbec-4d53-95a8-5861124179b3" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>446c2e0c-7e3a-4775-b215-cb34b03ae375</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e9a2fecc-16d9-457d-ac10-460498b47b07</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="446c2e0c-7e3a-4775-b215-cb34b03ae375">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.6dbacd30-1b08-419d-89d6-979e55c112a8</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60a8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c1b9014b-c931-4e1e-8509-a1f770bc08e6</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="446c2e0c-7e3a-4775-b215-cb34b03ae375" targetRef="e9a2fecc-16d9-457d-ac10-460498b47b07" name="To Get filtered team" id="2027.6dbacd30-1b08-419d-89d6-979e55c112a8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get filtered team" id="e9a2fecc-16d9-457d-ac10-460498b47b07">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="299" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.6dbacd30-1b08-419d-89d6-979e55c112a8</ns16:incoming>
                        
                        
                        <ns16:outgoing>c1b9014b-c931-4e1e-8509-a1f770bc08e6</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.originalTeam = new tw.object.Team();&#xD;
tw.local.originalTeam.members = new tw.object.listOf.String();&#xD;
tw.local.originalTeam.members[0] = "idchubexemkr01";&#xD;
tw.local.originalTeam.members[1] = "idchubexemkr02";&#xD;
tw.local.originalTeam.members[2] = "idchubexemkr59";&#xD;
tw.local.originalTeam.managerTeam = "Managers";&#xD;
tw.local.originalTeam.name = "IDC EXE HUB";&#xD;
&#xD;
tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e9a2fecc-16d9-457d-ac10-460498b47b07" targetRef="05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793" name="To End" id="c1b9014b-c931-4e1e-8509-a1f770bc08e6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c1b9014b-c931-4e1e-8509-a1f770bc08e6</processLinkId>
            <processId>1.7073c330-e8a8-4bd8-94fb-d764787dffeb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e9a2fecc-16d9-457d-ac10-460498b47b07</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793</toProcessItemId>
            <guid>fe803398-9155-4b89-9b9d-a85acb388c08</guid>
            <versionId>46ae4ef0-40a9-48aa-9155-cf5cac9c111b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e9a2fecc-16d9-457d-ac10-460498b47b07</fromProcessItemId>
            <toProcessItemId>2025.05dc3bd9-ee3b-42a6-b47a-c5c9a29a8793</toProcessItemId>
        </link>
    </process>
</teamworks>

