<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.7eee0980-1508-423c-95a0-c08797e92721" name="Create IDC Reversal Request">
        <lastModified>1690187462086</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.15b09e6e-487b-4f1d-9cdd-97975e166c03</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>d163cd3a-1d9c-4115-9408-8a4f1a21ca54</guid>
        <versionId>24201065-1d0c-49f7-b4a6-2361ce7b2758</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.eee8288b-13e1-4063-8622-9d163e4a3610"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":188,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"4487d88d-cc5f-4b99-8acb-1b8cae344cb4"},{"incoming":["2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f","2027.f0d8a82b-b75a-4185-8242-347d5b4f015a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":700,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"f51dd531-9b5e-44f5-8301-3134b8d6d465"},{"targetRef":"2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Start To Coach","declaredType":"sequenceFlow","id":"2027.eee8288b-13e1-4063-8622-9d163e4a3610","sourceRef":"4487d88d-cc5f-4b99-8acb-1b8cae344cb4"},{"startQuantity":1,"outgoing":["2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f"],"default":"2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f","extensionElements":{"nodeVisualInfo":[{"width":95,"x":519,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.ea18e6d5-0396-4295-b365-a0837c9305db","scriptFormat":"text\/x-javascript","script":{"content":["\r\n"]}},{"targetRef":"f51dd531-9b5e-44f5-8301-3134b8d6d465","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f","sourceRef":"2025.ea18e6d5-0396-4295-b365-a0837c9305db"},{"startQuantity":1,"outgoing":["2027.fc4035d4-a592-4eb0-beb5-7301eba30343"],"incoming":["2027.eee8288b-13e1-4063-8622-9d163e4a3610"],"default":"2027.fc4035d4-a592-4eb0-beb5-7301eba30343","extensionElements":{"nodeVisualInfo":[{"width":95,"x":159,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.idcReversalRequest == null) {\r\n\ttw.local.idcReversalRequest = {};\r\n\ttw.local.idcReversalRequest.subStatus = \"\";\r\n} "]}},{"targetRef":"2025.32acfbc4-dc58-4ef1-af33-17413cad7da2","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Create IDC Reversal Request","declaredType":"sequenceFlow","id":"2027.fc4035d4-a592-4eb0-beb5-7301eba30343","sourceRef":"2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee"},{"outgoing":["2027.f0d8a82b-b75a-4185-8242-347d5b4f015a","2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877"],"incoming":["2027.fc4035d4-a592-4eb0-beb5-7301eba30343","2027.4494cbb0-2d86-4189-8276-e46545004c1c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":358,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5f9816c1-531c-4eba-87f6-7bf60ea2f354","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"78b51903-b98b-4b9f-85cd-e5e96869dfc8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cabdf52e-db27-4cf3-8a5f-13b7712272c0","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"b9b346eb-71ba-4650-8ea1-b14d51e2609d","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Create IDC Reversal Request","isForCompensation":false,"completionQuantity":1,"id":"2025.32acfbc4-dc58-4ef1-af33-17413cad7da2"},{"targetRef":"f51dd531-9b5e-44f5-8301-3134b8d6d465","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"1727c539-1029-4a59-bdf9-e0bfd757241c","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.f0d8a82b-b75a-4185-8242-347d5b4f015a","sourceRef":"2025.32acfbc4-dc58-4ef1-af33-17413cad7da2"},{"outgoing":["2027.4494cbb0-2d86-4189-8276-e46545004c1c"],"incoming":["2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.4494cbb0-2d86-4189-8276-e46545004c1c"],"nodeVisualInfo":[{"width":24,"x":379,"y":62,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4"},{"targetRef":"2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"6bca3dd5-0b31-4b5c-8133-8aa3ada3212f","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877","sourceRef":"2025.32acfbc4-dc58-4ef1-af33-17413cad7da2"},{"targetRef":"2025.32acfbc4-dc58-4ef1-af33-17413cad7da2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create IDC Reversal Request","declaredType":"sequenceFlow","id":"2027.4494cbb0-2d86-4189-8276-e46545004c1c","sourceRef":"2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"ae6d2965-641a-4a70-a8ee-4178c95dd91a","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"99f97061-9e3d-4d56-ae2f-f3febdc265c1","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Create IDC Reversal Request","declaredType":"globalUserTask","id":"1.7eee0980-1508-423c-95a0-c08797e92721","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474","name":"idcReversalRequest","isCollection":false,"id":"2055.20977cc3-c153-4a4d-ab6d-c1f2c594370b"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.14b71d5c-752c-49c4-afba-cff5d026e9cd","epvProcessLinkId":"c1040398-0cd6-4e4a-8a38-8ecfdb1e64fb","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474","name":"idcReversalRequest","isCollection":false,"id":"2055.08987844-87e6-4b47-bfda-027fbffde9b4"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.6c1efd84-4b0f-4954-baf0-289da686c083"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcReversalRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.08987844-87e6-4b47-bfda-027fbffde9b4</processParameterId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>da8e6e8b-e498-426f-b573-416b3bc18b16</guid>
            <versionId>bfbd7578-c55f-4d3e-9635-9883dcaadf7b</versionId>
        </processParameter>
        <processParameter name="idcReversalRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.20977cc3-c153-4a4d-ab6d-c1f2c594370b</processParameterId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>95076423-07ff-40c2-8992-1dd03ca6094e</guid>
            <versionId>509996a6-1612-43fe-bd24-d88bc13f572b</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.15b09e6e-487b-4f1d-9cdd-97975e166c03</processItemId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.0dc97308-147b-4d9f-b520-d5b0db6ec538</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6211</guid>
            <versionId>68aa1e12-55e2-4d8e-8cb5-fbe6fe8af01e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9643dcbc-9de0-4b8f-9467-3b03c907d62c</processItemId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5f5607db-d76f-4375-b6c1-ebc1f5a1a1a0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6210</guid>
            <versionId>f53b11cb-0501-45a9-8f60-ef9fb44cb75b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5f5607db-d76f-4375-b6c1-ebc1f5a1a1a0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ccf828be-df3d-4356-a75f-f12f243ae44c</guid>
                <versionId>5cfbd494-1e7b-49e2-9e51-c5afbcd1fb56</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b0b2b682-bfe9-4921-99b2-8e1318cab875</epvProcessLinkId>
            <epvId>/21.14b71d5c-752c-49c4-afba-cff5d026e9cd</epvId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <guid>e5eff46e-5c38-478d-9f50-db9fb8bb10db</guid>
            <versionId>41c40a99-a75e-465f-b52f-20b10da697af</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.15b09e6e-487b-4f1d-9cdd-97975e166c03</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.6c1efd84-4b0f-4954-baf0-289da686c083" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Create IDC Reversal Request" id="1.7eee0980-1508-423c-95a0-c08797e92721">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="99f97061-9e3d-4d56-ae2f-f3febdc265c1">
                            
                            
                            <ns16:startEvent name="Start" id="4487d88d-cc5f-4b99-8acb-1b8cae344cb4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.eee8288b-13e1-4063-8622-9d163e4a3610</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="f51dd531-9b5e-44f5-8301-3134b8d6d465">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="700" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.f0d8a82b-b75a-4185-8242-347d5b4f015a</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="4487d88d-cc5f-4b99-8acb-1b8cae344cb4" targetRef="2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee" name="Start To Coach" id="2027.eee8288b-13e1-4063-8622-9d163e4a3610">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f" name="Set Status" id="2025.ea18e6d5-0396-4295-b365-a0837c9305db">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="519" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ea18e6d5-0396-4295-b365-a0837c9305db" targetRef="f51dd531-9b5e-44f5-8301-3134b8d6d465" name="OK To End" id="2027.d324fd98-d1cb-494d-b652-d1eb2fd2022f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.fc4035d4-a592-4eb0-beb5-7301eba30343" name="Initialization Script" id="2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="159" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.eee8288b-13e1-4063-8622-9d163e4a3610</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fc4035d4-a592-4eb0-beb5-7301eba30343</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.idcReversalRequest == null) {&#xD;
	tw.local.idcReversalRequest = {};&#xD;
	tw.local.idcReversalRequest.subStatus = "";&#xD;
} </ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.95d38f70-ced3-4aa7-bcb0-43b1e26ac3ee" targetRef="2025.32acfbc4-dc58-4ef1-af33-17413cad7da2" name="To Create IDC Reversal Request" id="2027.fc4035d4-a592-4eb0-beb5-7301eba30343">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Create IDC Reversal Request" id="2025.32acfbc4-dc58-4ef1-af33-17413cad7da2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="358" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fc4035d4-a592-4eb0-beb5-7301eba30343</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.4494cbb0-2d86-4189-8276-e46545004c1c</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f0d8a82b-b75a-4185-8242-347d5b4f015a</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>b9b346eb-71ba-4650-8ea1-b14d51e2609d</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5f9816c1-531c-4eba-87f6-7bf60ea2f354</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>78b51903-b98b-4b9f-85cd-e5e96869dfc8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>cabdf52e-db27-4cf3-8a5f-13b7712272c0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.32acfbc4-dc58-4ef1-af33-17413cad7da2" targetRef="f51dd531-9b5e-44f5-8301-3134b8d6d465" name="To End" id="2027.f0d8a82b-b75a-4185-8242-347d5b4f015a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="1727c539-1029-4a59-bdf9-e0bfd757241c">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="379" y="62" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.4494cbb0-2d86-4189-8276-e46545004c1c</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4494cbb0-2d86-4189-8276-e46545004c1c</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.32acfbc4-dc58-4ef1-af33-17413cad7da2" targetRef="2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4" name="To Postpone" id="2027.05b4a181-baed-4d6d-8fe3-0de1ceb1b877">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="6bca3dd5-0b31-4b5c-8133-8aa3ada3212f">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.790ab82e-cc8c-4b3a-8400-2e83e294d7b4" targetRef="2025.32acfbc4-dc58-4ef1-af33-17413cad7da2" name="To Create IDC Reversal Request" id="2027.4494cbb0-2d86-4189-8276-e46545004c1c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="ae6d2965-641a-4a70-a8ee-4178c95dd91a">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.14b71d5c-752c-49c4-afba-cff5d026e9cd" epvProcessLinkId="c1040398-0cd6-4e4a-8a38-8ecfdb1e64fb" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcReversalRequest" itemSubjectRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474" isCollection="false" id="2055.08987844-87e6-4b47-bfda-027fbffde9b4" />
                        
                        
                        <ns16:dataOutput name="idcReversalRequest" itemSubjectRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474" isCollection="false" id="2055.20977cc3-c153-4a4d-ab6d-c1f2c594370b" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.850f0371-8ab7-4e2e-b9fe-1066f030d934</processLinkId>
            <processId>1.7eee0980-1508-423c-95a0-c08797e92721</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.15b09e6e-487b-4f1d-9cdd-97975e166c03</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9643dcbc-9de0-4b8f-9467-3b03c907d62c</toProcessItemId>
            <guid>a4ec11f4-e36a-4ce1-8d12-3f222c3c0f43</guid>
            <versionId>037c185c-47c3-44f9-9cbc-a49d09c00c62</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.15b09e6e-487b-4f1d-9cdd-97975e166c03</fromProcessItemId>
            <toProcessItemId>2025.9643dcbc-9de0-4b8f-9467-3b03c907d62c</toProcessItemId>
        </link>
    </process>
</teamworks>

