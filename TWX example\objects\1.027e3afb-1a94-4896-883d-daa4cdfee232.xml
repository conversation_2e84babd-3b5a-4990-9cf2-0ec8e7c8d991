<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.027e3afb-1a94-4896-883d-daa4cdfee232" name="Get HUB name by code">
        <lastModified>1692506748460</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:7331</guid>
        <versionId>cf9b85d7-e4d2-4dc2-9e41-5fd3490c6154</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e85" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":65,"y":140,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"43d1e187-f428-414c-8109-430d4a09971e"},{"incoming":["141c9f16-421d-4451-84a5-2605889a553c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":820,"y":140,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:7333"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"00aab4f7-16a0-44b8-86e7-3141b11e8622"},{"targetRef":"a592e56d-217f-4427-8bb3-c03e788efb84","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Query","declaredType":"sequenceFlow","id":"2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd","sourceRef":"43d1e187-f428-414c-8109-430d4a09971e"},{"startQuantity":1,"outgoing":["fa0f9a32-3165-4367-80fd-46176d123f39"],"incoming":["2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":144,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["\r\nif(tw.local.data == \"0599\")\r\n{\r\n tw.local.data = \"599\";\r\n}\r\n"]},"name":"Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"a592e56d-217f-4427-8bb3-c03e788efb84","scriptFormat":"text\/plain","script":{"content":["tw.local.sqlQuery\nselect ARABIC_DESCRIPTION from &lt;#= tw.resource.Lookups.IDC_Execution_Hubs#&gt; where code = '&lt;#= tw.local.data #&gt;'"]}},{"targetRef":"98c078fa-fcb4-4281-822c-8f0c8fc9d522","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Execute","declaredType":"sequenceFlow","id":"fa0f9a32-3165-4367-80fd-46176d123f39","sourceRef":"a592e56d-217f-4427-8bb3-c03e788efb84"},{"startQuantity":1,"outgoing":["fe7b7927-fac4-4116-81fc-ec6a176ffdb8"],"incoming":["fa0f9a32-3165-4367-80fd-46176d123f39"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":376,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute","dataInputAssociation":[{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_APP"]}}]},{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sqlQuery"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"98c078fa-fcb4-4281-822c-8f0c8fc9d522","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.sqlResults"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"4d85e177-654d-40eb-81d4-4b927e92d7f4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Output","declaredType":"sequenceFlow","id":"fe7b7927-fac4-4116-81fc-ec6a176ffdb8","sourceRef":"98c078fa-fcb4-4281-822c-8f0c8fc9d522"},{"startQuantity":1,"outgoing":["141c9f16-421d-4451-84a5-2605889a553c"],"incoming":["fe7b7927-fac4-4116-81fc-ec6a176ffdb8"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":602,"y":117,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4d85e177-654d-40eb-81d4-4b927e92d7f4","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.sqlResults != null &amp;&amp; tw.local.sqlResults.listLength != 0)\r\n{\r\n\ttw.local.hubName = tw.local.sqlResults[0].rows[0].data[0];\r\n}"]}},{"targetRef":"00aab4f7-16a0-44b8-86e7-3141b11e8622","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"141c9f16-421d-4451-84a5-2605889a553c","sourceRef":"4d85e177-654d-40eb-81d4-4b927e92d7f4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sqlQuery","isCollection":false,"declaredType":"dataObject","id":"2056.cb03dbc7-5df7-4fe0-8db2-29563dff5abb"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"sqlResults","isCollection":true,"declaredType":"dataObject","id":"2056.a9b52009-70b3-491f-81cc-c13136d7aa0f"},{"parallelMultiple":false,"outgoing":["b9ef5116-ce14-4449-834c-0649005e613a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"202ef9e4-6707-43b6-83c8-2a49cd980b92"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"f2b00179-b6ed-49f8-8aeb-75fdaa32f54c","otherAttributes":{"eventImplId":"dba53b96-29e9-44b0-8eb1-21f92df19e15"}}],"attachedToRef":"a592e56d-217f-4427-8bb3-c03e788efb84","extensionElements":{"nodeVisualInfo":[{"width":24,"x":179,"y":175,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"91ad8365-87c7-4877-8e40-06b09c0de2ab","outputSet":{}},{"parallelMultiple":false,"outgoing":["daa3b913-ed0f-406f-88fd-241edc1df813"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"22a21da3-a03f-4c76-82fb-1a004e2630d9"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1f97bc34-c5d5-4349-8379-f8520d41fa77","otherAttributes":{"eventImplId":"8089bcfd-56ee-4fb6-8406-52e49f4f58db"}}],"attachedToRef":"98c078fa-fcb4-4281-822c-8f0c8fc9d522","extensionElements":{"nodeVisualInfo":[{"width":24,"x":411,"y":175,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56","outputSet":{}},{"parallelMultiple":false,"outgoing":["081733d9-20a8-4fe9-89d8-c9e2444c9d7a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"dcd24e4b-0fa8-47ad-88e5-63ea19f54473"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6d2868ac-ce66-4e81-822c-678295812673","otherAttributes":{"eventImplId":"7f3f5711-ea05-4201-810d-61285764e978"}}],"attachedToRef":"4d85e177-654d-40eb-81d4-4b927e92d7f4","extensionElements":{"nodeVisualInfo":[{"width":24,"x":637,"y":175,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"ebd6e37b-5b83-4fce-8c9a-a89905cb7f45","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.e4069bdf-2ecf-4fa6-8ab0-2680565b91e4"},{"startQuantity":1,"incoming":["b9ef5116-ce14-4449-834c-0649005e613a","daa3b913-ed0f-406f-88fd-241edc1df813","081733d9-20a8-4fe9-89d8-c9e2444c9d7a"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":388,"y":260,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"be835cd4-e5a2-4149-8e03-958a1a8ef8c2","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"be835cd4-e5a2-4149-8e03-958a1a8ef8c2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"b9ef5116-ce14-4449-834c-0649005e613a","sourceRef":"91ad8365-87c7-4877-8e40-06b09c0de2ab"},{"targetRef":"be835cd4-e5a2-4149-8e03-958a1a8ef8c2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"daa3b913-ed0f-406f-88fd-241edc1df813","sourceRef":"dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56"},{"targetRef":"be835cd4-e5a2-4149-8e03-958a1a8ef8c2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"081733d9-20a8-4fe9-89d8-c9e2444c9d7a","sourceRef":"ebd6e37b-5b83-4fce-8c9a-a89905cb7f45"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.74507b39-d17f-45cb-84f3-61438f89dab4"}],"laneSet":[{"id":"6dcbd30b-bfb5-45e7-8d07-6c3e8ace2ff0","lane":[{"flowNodeRef":["43d1e187-f428-414c-8109-430d4a09971e","00aab4f7-16a0-44b8-86e7-3141b11e8622","a592e56d-217f-4427-8bb3-c03e788efb84","98c078fa-fcb4-4281-822c-8f0c8fc9d522","4d85e177-654d-40eb-81d4-4b927e92d7f4","91ad8365-87c7-4877-8e40-06b09c0de2ab","dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56","ebd6e37b-5b83-4fce-8c9a-a89905cb7f45","be835cd4-e5a2-4149-8e03-958a1a8ef8c2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"fd1ccb75-193b-46dd-81b5-46eb11e53932","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get HUB name by code","declaredType":"process","id":"1.027e3afb-1a94-4896-883d-daa4cdfee232","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubName","isCollection":false,"id":"2055.c7d0cfc7-082a-485e-8888-d60e6af40354"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.90c24b32-faa1-4055-8e19-b7c8ce78b175"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.d37ebe05-41d3-47ac-9237-53de467d6a4a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.08c96fba-aa12-4078-8185-db260fc1a928"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}]},"inputSet":[{"dataInputRefs":["2055.21f4753b-2351-494a-849c-bc4c055e9605"]}],"outputSet":[{"dataOutputRefs":["2055.c7d0cfc7-082a-485e-8888-d60e6af40354","2055.90c24b32-faa1-4055-8e19-b7c8ce78b175"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"0599\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.21f4753b-2351-494a-849c-bc4c055e9605"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.21f4753b-2351-494a-849c-bc4c055e9605</processParameterId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>becc748a-af11-4b70-99b0-055c3ffa8d11</guid>
            <versionId>069b9845-67c9-4c4a-a839-0a700f1b7b4b</versionId>
        </processParameter>
        <processParameter name="hubName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c7d0cfc7-082a-485e-8888-d60e6af40354</processParameterId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>db2c28f7-7e12-4924-91aa-fc7bdfbaa5ca</guid>
            <versionId>98b1a6c4-c8d3-4d96-b359-65eaa3ecfed2</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.90c24b32-faa1-4055-8e19-b7c8ce78b175</processParameterId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d8bf22d5-abc2-4950-8bf7-27f489e44868</guid>
            <versionId>52f2e2b5-2ef7-4ace-a37f-8c06e3fdaa5e</versionId>
        </processParameter>
        <processVariable name="sqlQuery">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb03dbc7-5df7-4fe0-8db2-29563dff5abb</processVariableId>
            <description isNull="true" />
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f7a2cfd4-b22b-4fa1-bf6b-193098e153e6</guid>
            <versionId>32c0478d-e9da-4f50-a538-a3edc2e31381</versionId>
        </processVariable>
        <processVariable name="sqlResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a9b52009-70b3-491f-81cc-c13136d7aa0f</processVariableId>
            <description isNull="true" />
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>53ecbbb3-6c69-4992-99c8-133db5b62b2c</guid>
            <versionId>dd16dd0e-a4bb-4e96-a66f-f6192de6911e</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e4069bdf-2ecf-4fa6-8ab0-2680565b91e4</processVariableId>
            <description isNull="true" />
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>46f8d3da-4810-4083-a822-3baaa16b7056</guid>
            <versionId>28e1cf18-e1b2-4c9e-a341-1caa95ce4ea6</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.74507b39-d17f-45cb-84f3-61438f89dab4</processVariableId>
            <description isNull="true" />
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>664ba5ba-4429-446c-8e21-56c322762843</guid>
            <versionId>40005b5a-711e-4647-9057-a6ff77a0a839</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</processItemId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0719854f-8efc-47d7-90f2-a7e33ae521dd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e84</guid>
            <versionId>0623a3ff-8cfa-4953-b490-55526e1aaad3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="388" y="260">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0719854f-8efc-47d7-90f2-a7e33ae521dd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>8de0899d-f33b-4c69-bbbf-ab4c99a83af3</guid>
                <versionId>bac81030-65e5-4fed-9159-2d0aef3ee9ed</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.00aab4f7-16a0-44b8-86e7-3141b11e8622</processItemId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d443597d-039f-4d3a-a88a-016dde1e0cdc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:7333</guid>
            <versionId>207244b3-96a6-4d73-9790-bbf5bad7fe03</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="820" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d443597d-039f-4d3a-a88a-016dde1e0cdc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9ca106b5-e090-41c9-b845-ca2f4c9f5b62</guid>
                <versionId>3497ef4e-90e5-4d74-bc0e-eb3b83dfb0ea</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4d85e177-654d-40eb-81d4-4b927e92d7f4</processItemId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <name>Output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c3ab424f-d8b6-4a77-867f-c1ac423e088b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:733c</guid>
            <versionId>41143b50-0536-4c47-a8b0-8082d68782d3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="602" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e84</errorHandlerItem>
                <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c3ab424f-d8b6-4a77-867f-c1ac423e088b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.sqlResults != null &amp;&amp; tw.local.sqlResults.listLength != 0)&#xD;
{&#xD;
	tw.local.hubName = tw.local.sqlResults[0].rows[0].data[0];&#xD;
}</script>
                <isRule>false</isRule>
                <guid>06135258-3288-4208-b498-bb8b836ed1e3</guid>
                <versionId>a94c0688-69d1-4548-8fed-f661ba901963</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.98c078fa-fcb4-4281-822c-8f0c8fc9d522</processItemId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <name>Execute</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:733b</guid>
            <versionId>42ebaf5d-ff99-4147-803d-047f53fa6695</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="376" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e84</errorHandlerItem>
                <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>2491ff8f-9606-43a2-a350-83931c6989e2</guid>
                <versionId>163d195e-c7d8-4559-936b-09eceb815fb1</versionId>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9d230387-5774-4cc1-a688-660462879ef5</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>eb25137c-3f6c-4525-a275-1e5b4f2c5646</guid>
                    <versionId>273b58e6-7820-46ef-97a4-4097d16b44b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.33d90bf3-dd61-42c5-947c-c36a7a40b7ad</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_APP</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2773d905-5576-4a79-ad57-bd6288b03a2c</guid>
                    <versionId>5faff04d-ef4c-4c9f-a01c-96c1a2b8e1ce</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.67c6b1fc-9f94-4255-bb06-ceb79d88de78</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlResults</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f548ae0a-35f6-4e08-897f-94ed94163554</guid>
                    <versionId>a1a0df89-0afe-400b-8321-1fe0c3c89990</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8214b04a-375d-4670-b94d-cf82c3b9b2a9</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlQuery</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8e6e14a8-23fe-44a0-8fbd-16d4841f44ed</guid>
                    <versionId>a41ab8fd-3f62-4032-b784-585b3bdeb38d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cc551b69-184b-4256-90de-4e581608ced9</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7ddf93ab-ddfc-48d1-993f-f3dfdaab85c8</guid>
                    <versionId>bd4a0c15-63e2-483d-beb0-a0a86dc2dbb6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9bcdb43d-0086-48cc-bde6-d6e3c6d15b5e</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.2624a785-aa60-41e2-aa0b-66b62f6141cb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f39d839c-f4e3-4ed4-aaaa-04b39b365dcf</guid>
                    <versionId>f2300596-c766-48a2-ad6e-07dcdb1c0c97</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</processItemId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <name>Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.59de8262-a6a5-45cf-b50b-3cd664ed138e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:733a</guid>
            <versionId>6d5652dc-2fe0-4ed8-9bb5-e9e4542d04c3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b319293e-f4b7-48f0-93d6-e58538f61cfd</processItemPrePostId>
                <processItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</processItemId>
                <location>1</location>
                <script>&#xD;
if(tw.local.data == "0599")&#xD;
{&#xD;
 tw.local.data = "599";&#xD;
}&#xD;
</script>
                <guid>9f733bb2-e1b0-4719-b9bc-************</guid>
                <versionId>8859a258-e355-4aab-a93e-deb5ea34823e</versionId>
            </processPrePosts>
            <layoutData x="144" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e84</errorHandlerItem>
                <errorHandlerItemId>2025.be835cd4-e5a2-4149-8e03-958a1a8ef8c2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.59de8262-a6a5-45cf-b50b-3cd664ed138e</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlQuery
select ARABIC_DESCRIPTION from &lt;#= tw.resource.Lookups.IDC_Execution_Hubs#&gt; where code = '&lt;#= tw.local.data #&gt;'</script>
                <isRule>false</isRule>
                <guid>858198a6-c9d1-4486-aa65-c1dbcde13a3f</guid>
                <versionId>dbb3b693-cd43-4b05-87d7-2f71564aadba</versionId>
            </TWComponent>
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.51c3f68b-f815-4416-9b53-66deda4c69c4</resourceProcessLinkId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <guid>4aa1bb6c-41ec-4828-bf45-f7192b4a67ff</guid>
            <versionId>3baf5257-93e5-4092-ad73-e18341047c78</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="65" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get HUB name by code" id="1.027e3afb-1a94-4896-883d-daa4cdfee232" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.d37ebe05-41d3-47ac-9237-53de467d6a4a</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.08c96fba-aa12-4078-8185-db260fc1a928</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.21f4753b-2351-494a-849c-bc4c055e9605">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"0599"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="hubName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c7d0cfc7-082a-485e-8888-d60e6af40354" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.90c24b32-faa1-4055-8e19-b7c8ce78b175" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.21f4753b-2351-494a-849c-bc4c055e9605</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.c7d0cfc7-082a-485e-8888-d60e6af40354</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.90c24b32-faa1-4055-8e19-b7c8ce78b175</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="6dcbd30b-bfb5-45e7-8d07-6c3e8ace2ff0">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="fd1ccb75-193b-46dd-81b5-46eb11e53932" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>43d1e187-f428-414c-8109-430d4a09971e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>00aab4f7-16a0-44b8-86e7-3141b11e8622</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a592e56d-217f-4427-8bb3-c03e788efb84</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>98c078fa-fcb4-4281-822c-8f0c8fc9d522</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4d85e177-654d-40eb-81d4-4b927e92d7f4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>91ad8365-87c7-4877-8e40-06b09c0de2ab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ebd6e37b-5b83-4fce-8c9a-a89905cb7f45</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>be835cd4-e5a2-4149-8e03-958a1a8ef8c2</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="43d1e187-f428-414c-8109-430d4a09971e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="65" y="140" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="00aab4f7-16a0-44b8-86e7-3141b11e8622">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="820" y="140" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:7333</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>141c9f16-421d-4451-84a5-2605889a553c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="43d1e187-f428-414c-8109-430d4a09971e" targetRef="a592e56d-217f-4427-8bb3-c03e788efb84" name="To Query" id="2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" name="Query" id="a592e56d-217f-4427-8bb3-c03e788efb84">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="144" y="117" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>&#xD;
if(tw.local.data == "0599")&#xD;
{&#xD;
 tw.local.data = "599";&#xD;
}&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.af01a151-d9ce-4f8f-8dfe-0db60ac684fd</ns16:incoming>
                        
                        
                        <ns16:outgoing>fa0f9a32-3165-4367-80fd-46176d123f39</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlQuery
select ARABIC_DESCRIPTION from &lt;#= tw.resource.Lookups.IDC_Execution_Hubs#&gt; where code = '&lt;#= tw.local.data #&gt;'</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="a592e56d-217f-4427-8bb3-c03e788efb84" targetRef="98c078fa-fcb4-4281-822c-8f0c8fc9d522" name="To Execute" id="fa0f9a32-3165-4367-80fd-46176d123f39">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Execute" id="98c078fa-fcb4-4281-822c-8f0c8fc9d522">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="376" y="117" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fa0f9a32-3165-4367-80fd-46176d123f39</ns16:incoming>
                        
                        
                        <ns16:outgoing>fe7b7927-fac4-4116-81fc-ec6a176ffdb8</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_APP</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sqlQuery</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.sqlResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="98c078fa-fcb4-4281-822c-8f0c8fc9d522" targetRef="4d85e177-654d-40eb-81d4-4b927e92d7f4" name="To Output" id="fe7b7927-fac4-4116-81fc-ec6a176ffdb8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Output" id="4d85e177-654d-40eb-81d4-4b927e92d7f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="602" y="117" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fe7b7927-fac4-4116-81fc-ec6a176ffdb8</ns16:incoming>
                        
                        
                        <ns16:outgoing>141c9f16-421d-4451-84a5-2605889a553c</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.sqlResults != null &amp;&amp; tw.local.sqlResults.listLength != 0)&#xD;
{&#xD;
	tw.local.hubName = tw.local.sqlResults[0].rows[0].data[0];&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4d85e177-654d-40eb-81d4-4b927e92d7f4" targetRef="00aab4f7-16a0-44b8-86e7-3141b11e8622" name="To End" id="141c9f16-421d-4451-84a5-2605889a553c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sqlQuery" id="2056.cb03dbc7-5df7-4fe0-8db2-29563dff5abb" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="sqlResults" id="2056.a9b52009-70b3-491f-81cc-c13136d7aa0f" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a592e56d-217f-4427-8bb3-c03e788efb84" parallelMultiple="false" name="Error" id="91ad8365-87c7-4877-8e40-06b09c0de2ab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="179" y="175" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b9ef5116-ce14-4449-834c-0649005e613a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="202ef9e4-6707-43b6-83c8-2a49cd980b92" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="f2b00179-b6ed-49f8-8aeb-75fdaa32f54c" eventImplId="dba53b96-29e9-44b0-8eb1-21f92df19e15">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="98c078fa-fcb4-4281-822c-8f0c8fc9d522" parallelMultiple="false" name="Error1" id="dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="411" y="175" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>daa3b913-ed0f-406f-88fd-241edc1df813</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="22a21da3-a03f-4c76-82fb-1a004e2630d9" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="1f97bc34-c5d5-4349-8379-f8520d41fa77" eventImplId="8089bcfd-56ee-4fb6-8406-52e49f4f58db">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="4d85e177-654d-40eb-81d4-4b927e92d7f4" parallelMultiple="false" name="Error2" id="ebd6e37b-5b83-4fce-8c9a-a89905cb7f45">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="637" y="175" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>081733d9-20a8-4fe9-89d8-c9e2444c9d7a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="dcd24e4b-0fa8-47ad-88e5-63ea19f54473" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="6d2868ac-ce66-4e81-822c-678295812673" eventImplId="7f3f5711-ea05-4201-810d-61285764e978">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.e4069bdf-2ecf-4fa6-8ab0-2680565b91e4" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="be835cd4-e5a2-4149-8e03-958a1a8ef8c2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="388" y="260" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b9ef5116-ce14-4449-834c-0649005e613a</ns16:incoming>
                        
                        
                        <ns16:incoming>daa3b913-ed0f-406f-88fd-241edc1df813</ns16:incoming>
                        
                        
                        <ns16:incoming>081733d9-20a8-4fe9-89d8-c9e2444c9d7a</ns16:incoming>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="91ad8365-87c7-4877-8e40-06b09c0de2ab" targetRef="be835cd4-e5a2-4149-8e03-958a1a8ef8c2" name="To Catch Errors" id="b9ef5116-ce14-4449-834c-0649005e613a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="dc9fe5cf-586a-4c37-8aba-a99cb9d7bf56" targetRef="be835cd4-e5a2-4149-8e03-958a1a8ef8c2" name="To Catch Errors" id="daa3b913-ed0f-406f-88fd-241edc1df813">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ebd6e37b-5b83-4fce-8c9a-a89905cb7f45" targetRef="be835cd4-e5a2-4149-8e03-958a1a8ef8c2" name="To Catch Errors" id="081733d9-20a8-4fe9-89d8-c9e2444c9d7a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.74507b39-d17f-45cb-84f3-61438f89dab4" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fe7b7927-fac4-4116-81fc-ec6a176ffdb8</processLinkId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.98c078fa-fcb4-4281-822c-8f0c8fc9d522</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.4d85e177-654d-40eb-81d4-4b927e92d7f4</toProcessItemId>
            <guid>761f9a89-a3fe-448d-ac2b-29f1306b3e5b</guid>
            <versionId>50813da4-a66c-47a9-a834-2736591410bf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.98c078fa-fcb4-4281-822c-8f0c8fc9d522</fromProcessItemId>
            <toProcessItemId>2025.4d85e177-654d-40eb-81d4-4b927e92d7f4</toProcessItemId>
        </link>
        <link name="To Execute">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fa0f9a32-3165-4367-80fd-46176d123f39</processLinkId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.98c078fa-fcb4-4281-822c-8f0c8fc9d522</toProcessItemId>
            <guid>0cde5301-d764-4782-8c1a-096570fe8a53</guid>
            <versionId>6e6182ea-ff08-400e-8295-b18541c2a706</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a592e56d-217f-4427-8bb3-c03e788efb84</fromProcessItemId>
            <toProcessItemId>2025.98c078fa-fcb4-4281-822c-8f0c8fc9d522</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.141c9f16-421d-4451-84a5-2605889a553c</processLinkId>
            <processId>1.027e3afb-1a94-4896-883d-daa4cdfee232</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4d85e177-654d-40eb-81d4-4b927e92d7f4</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.00aab4f7-16a0-44b8-86e7-3141b11e8622</toProcessItemId>
            <guid>3e7b452f-5e36-498b-b898-d4c6094533b3</guid>
            <versionId>dcb18914-915d-4fec-9766-1c525f03c21d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4d85e177-654d-40eb-81d4-4b927e92d7f4</fromProcessItemId>
            <toProcessItemId>2025.00aab4f7-16a0-44b8-86e7-3141b11e8622</toProcessItemId>
        </link>
    </process>
</teamworks>

