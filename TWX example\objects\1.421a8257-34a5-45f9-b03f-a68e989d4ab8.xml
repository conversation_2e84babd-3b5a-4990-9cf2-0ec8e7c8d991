<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.421a8257-34a5-45f9-b03f-a68e989d4ab8" name="Approvals Comments">
        <lastModified>1691921882441</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>40dec8cc-02ca-4af9-b951-c47b789898a0</guid>
        <versionId>8bbff875-a233-445f-9a27-b8bfa2be6cad</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:6ad7eb4224455a46:11f23e39:189eae0bd83:49eb" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.48664197-e262-434a-afa6-6e2523b6a453"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4"},{"incoming":["9662b217-7681-438a-abe0-7f4e039b1c0a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-614a"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"34d0aea0-8635-4686-a941-9d3b02c3d329"},{"targetRef":"86c4f0fd-1d85-45a6-af83-e58e07317e9c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.48664197-e262-434a-afa6-6e2523b6a453","sourceRef":"89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4"},{"startQuantity":1,"outgoing":["9662b217-7681-438a-abe0-7f4e039b1c0a"],"incoming":["2027.48664197-e262-434a-afa6-6e2523b6a453"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":275,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"86c4f0fd-1d85-45a6-af83-e58e07317e9c","scriptFormat":"text\/x-javascript","script":{"content":["var len = tw.local.appLog.listLength;\r\n\r\nif (tw.local.treasuryComments != null) {\r\n\tfor (var i=len; i&lt;tw.local.treasuryComments.listLength; i++) {\r\n\t\ttw.local.appLog.insertIntoList(i, tw.local.treasuryComments[i]);\r\n\t}\r\n}\r\nif (tw.local.CADcomments != null) {\r\n\tfor (var i=len; i&lt;tw.local.CADcomments.listLength; i++) {\r\n\t\ttw.local.appLog.insertIntoList(i, tw.local.CADcomments[i]);\r\n\t}\r\n}\r\nif (tw.local.complianceComments != null) {\r\n\tfor (var i=len; i&lt;tw.local.complianceComments.listLength; i++) {\r\n\t\ttw.local.appLog.insertIntoList(i, tw.local.complianceComments[i]);\r\n\t}\r\n}\r\n"]}},{"targetRef":"34d0aea0-8635-4686-a941-9d3b02c3d329","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"9662b217-7681-438a-abe0-7f4e039b1c0a","sourceRef":"86c4f0fd-1d85-45a6-af83-e58e07317e9c"},{"parallelMultiple":false,"outgoing":["4a065e67-3143-40a3-8791-dd79f821381d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f0aac7f9-cf2c-40d5-8fab-d040e528fa6b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3f61d90f-6b3e-4198-8fc4-3e374ef0a8d4","otherAttributes":{"eventImplId":"23dad045-7159-4438-84e3-cd8c55750b61"}}],"attachedToRef":"86c4f0fd-1d85-45a6-af83-e58e07317e9c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":310,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"44cb0b0a-dbd2-4d94-8932-f78c5573b1af","outputSet":{}},{"incoming":["4a065e67-3143-40a3-8791-dd79f821381d"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"21fc6f86-c020-4929-8767-128da7972b81","otherAttributes":{"eventImplId":"1a5d0c35-5c9d-4bc2-8f7f-e433b47b556f"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":344,"y":174,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Approvals Comments -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Approvals Comments -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"479ee959-459f-4b6c-81ec-20475f6c5676"},{"targetRef":"479ee959-459f-4b6c-81ec-20475f6c5676","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"4a065e67-3143-40a3-8791-dd79f821381d","sourceRef":"44cb0b0a-dbd2-4d94-8932-f78c5573b1af"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.890a7ca1-7731-4d84-8302-ce5a5b2d27fe"}],"laneSet":[{"id":"f44ccac5-2f7d-449f-813f-ff5d6eb3e1c9","lane":[{"flowNodeRef":["89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4","34d0aea0-8635-4686-a941-9d3b02c3d329","86c4f0fd-1d85-45a6-af83-e58e07317e9c","44cb0b0a-dbd2-4d94-8932-f78c5573b1af","479ee959-459f-4b6c-81ec-20475f6c5676"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c7958f6e-dcda-4466-ac19-f421ddef0fdf","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Approvals Comments","declaredType":"process","id":"1.421a8257-34a5-45f9-b03f-a68e989d4ab8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"appLog","isCollection":true,"id":"2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.2eefde76-5898-4e8d-87b1-0f434cabef87"}],"inputSet":[{"dataInputRefs":["2055.792bf3d4-e1dc-4b70-afbc-5214492c915c","2055.59289b25-4be8-4329-b825-d40969b0afee","2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6","2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031"]}],"outputSet":[{"dataOutputRefs":["2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c","2055.2eefde76-5898-4e8d-87b1-0f434cabef87"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject[0].startTime = new TWDate();\nautoObject[0].endTime = new TWDate();\nautoObject[0].userName = \"appLog\";\nautoObject[0].role = \"appLog\";\nautoObject[0].step = \"appLog\";\nautoObject[0].action = \"appLog\";\nautoObject[0].comment = \"appLog\";\nautoObject[0].terminateReason = \"appLog\";\nautoObject[0].returnReason = \"appLog\";\r\n\r\nautoObject[1] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject[1].startTime = new TWDate();\r\nautoObject[1].endTime = new TWDate();\r\nautoObject[1].userName = \"appLog\";\r\nautoObject[1].role = \"appLog\";\r\nautoObject[1].step = \"appLog\";\r\nautoObject[1].action = \"appLog\";\r\nautoObject[1].comment = \"appLog\";\r\nautoObject[1].terminateReason = \"appLog\";\r\nautoObject[1].returnReason = \"appLog\";\r\n\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"appLog","isCollection":true,"id":"2055.792bf3d4-e1dc-4b70-afbc-5214492c915c"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject[0].startTime = new TWDate();\nautoObject[0].endTime = new TWDate();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\r\n\r\nautoObject[1] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject[1].startTime = new TWDate();\r\nautoObject[1].endTime = new TWDate();\r\nautoObject[1].userName = \"complianceComments\";\r\nautoObject[1].role = \"complianceComments\";\r\nautoObject[1].step = \"complianceComments\";\r\nautoObject[1].action = \"complianceComments\";\r\nautoObject[1].comment = \"complianceComments\";\r\nautoObject[1].terminateReason = \"complianceComments\";\r\nautoObject[1].returnReason = \"complianceComments\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"complianceComments","isCollection":true,"id":"2055.59289b25-4be8-4329-b825-d40969b0afee"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject[0].startTime = new TWDate();\nautoObject[0].endTime = new TWDate();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\n\r\nautoObject[1] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject[1].startTime = new TWDate();\r\nautoObject[1].endTime = new TWDate();\r\nautoObject[1].userName = \"CADcomments\";\r\nautoObject[1].role = \"CADcomments\";\r\nautoObject[1].step = \"CADcomments\";\r\nautoObject[1].action = \"CADcomments\";\r\nautoObject[1].comment = \"CADcomments\";\r\nautoObject[1].terminateReason = \"CADcomments\";\r\nautoObject[1].returnReason = \"CADcomments\";\r\n\r\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject[0].startTime = new TWDate();\nautoObject[0].endTime = new TWDate();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\r\n\r\nautoObject[1] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject[1].startTime = new TWDate();\r\nautoObject[1].endTime = new TWDate();\r\nautoObject[1].userName = \"treasuryComments\";\r\nautoObject[1].role = \"treasuryComments\";\r\nautoObject[1].step = \"treasuryComments\";\r\nautoObject[1].action = \"treasuryComments\";\r\nautoObject[1].comment = \"treasuryComments\";\r\nautoObject[1].terminateReason = \"treasuryComments\";\r\nautoObject[1].returnReason = \"treasuryComments\";\r\n\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="appLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.792bf3d4-e1dc-4b70-afbc-5214492c915c</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "appLog";
autoObject[0].role = "appLog";
autoObject[0].step = "appLog";
autoObject[0].action = "appLog";
autoObject[0].comment = "appLog";
autoObject[0].terminateReason = "appLog";
autoObject[0].returnReason = "appLog";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "appLog";&#xD;
autoObject[1].role = "appLog";&#xD;
autoObject[1].step = "appLog";&#xD;
autoObject[1].action = "appLog";&#xD;
autoObject[1].comment = "appLog";&#xD;
autoObject[1].terminateReason = "appLog";&#xD;
autoObject[1].returnReason = "appLog";&#xD;

autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>45f77b27-463f-4e1a-a1df-ebe404727dad</guid>
            <versionId>8244f89b-eb5a-4f53-9af7-3b65894df78b</versionId>
        </processParameter>
        <processParameter name="complianceComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.59289b25-4be8-4329-b825-d40969b0afee</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "complianceComments";&#xD;
autoObject[1].role = "complianceComments";&#xD;
autoObject[1].step = "complianceComments";&#xD;
autoObject[1].action = "complianceComments";&#xD;
autoObject[1].comment = "complianceComments";&#xD;
autoObject[1].terminateReason = "complianceComments";&#xD;
autoObject[1].returnReason = "complianceComments";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>813a7dca-8e46-4f68-bd5b-989fe62289a0</guid>
            <versionId>a7f10685-e112-410d-8aea-ac3e5906f406</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "CADcomments";&#xD;
autoObject[1].role = "CADcomments";&#xD;
autoObject[1].step = "CADcomments";&#xD;
autoObject[1].action = "CADcomments";&#xD;
autoObject[1].comment = "CADcomments";&#xD;
autoObject[1].terminateReason = "CADcomments";&#xD;
autoObject[1].returnReason = "CADcomments";&#xD;
&#xD;
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>311b39de-3f3f-425a-9779-9a83b35c7531</guid>
            <versionId>ee2cb6a3-c84f-4b56-aea3-603cb2a5e955</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>4</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "treasuryComments";&#xD;
autoObject[1].role = "treasuryComments";&#xD;
autoObject[1].step = "treasuryComments";&#xD;
autoObject[1].action = "treasuryComments";&#xD;
autoObject[1].comment = "treasuryComments";&#xD;
autoObject[1].terminateReason = "treasuryComments";&#xD;
autoObject[1].returnReason = "treasuryComments";&#xD;

autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9eed5d57-2421-4fa8-beec-7c89ea60f8d8</guid>
            <versionId>66e4829c-2179-4bc5-8c07-5de8445c79b1</versionId>
        </processParameter>
        <processParameter name="appLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dffeee39-c7e6-4201-b67b-8a991a35d99d</guid>
            <versionId>b069767c-d6bf-4574-b38b-2cd71b51dcaa</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1fb1e2ae-4f95-443e-861b-b26713501f83</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d825dc5b-7960-456b-8590-60b5d1484017</guid>
            <versionId>6a1ac9ab-b980-4cdb-9fee-a41b9aa80760</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2eefde76-5898-4e8d-87b1-0f434cabef87</processParameterId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a443f7c3-63c2-4ece-b778-1155ff9d6f5d</guid>
            <versionId>90d90e12-fd25-4278-a58e-591ee4462190</versionId>
        </processParameter>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.890a7ca1-7731-4d84-8302-ce5a5b2d27fe</processVariableId>
            <description isNull="true" />
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>39760f76-efb8-46fa-96ad-981ea7fe61f0</guid>
            <versionId>e88bd744-ed96-4dbe-9329-0de90672eade</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.34d0aea0-8635-4686-a941-9d3b02c3d329</processItemId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5c37bb63-f5c7-4421-8ee2-3c548ba65175</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-614a</guid>
            <versionId>66b60852-22be-4605-9665-8aaeb2de3e5f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5c37bb63-f5c7-4421-8ee2-3c548ba65175</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>17a6107a-d901-4fce-b2d6-0e376347e664</guid>
                <versionId>6506feaf-42b5-48c0-9090-954186c1dcc5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</processItemId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.d475a4b4-d366-4993-bf63-cc66790d4287</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.479ee959-459f-4b6c-81ec-20475f6c5676</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-614b</guid>
            <versionId>936a8713-0ecb-40b4-97f6-90fd6ea17074</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4db4bc01-ac4e-47e6-bcf6-990f2cd876c7</processItemPrePostId>
                <processItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>50e0d462-081d-41d7-b231-f8026fb9443a</guid>
                <versionId>7bad513b-d2f6-4e59-9a3e-d81d2330bd76</versionId>
            </processPrePosts>
            <layoutData x="275" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d575e46d:-7c34</errorHandlerItem>
                <errorHandlerItemId>2025.479ee959-459f-4b6c-81ec-20475f6c5676</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.d475a4b4-d366-4993-bf63-cc66790d4287</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var len = tw.local.appLog.listLength;&#xD;
&#xD;
if (tw.local.treasuryComments != null) {&#xD;
	for (var i=len; i&lt;tw.local.treasuryComments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.treasuryComments[i]);&#xD;
	}&#xD;
}&#xD;
if (tw.local.CADcomments != null) {&#xD;
	for (var i=len; i&lt;tw.local.CADcomments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.CADcomments[i]);&#xD;
	}&#xD;
}&#xD;
if (tw.local.complianceComments != null) {&#xD;
	for (var i=len; i&lt;tw.local.complianceComments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.complianceComments[i]);&#xD;
	}&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>8c51f660-1810-4409-acd1-7c21c784cd37</guid>
                <versionId>5912e172-48ca-41ae-b3ca-92a187e91063</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.479ee959-459f-4b6c-81ec-20475f6c5676</processItemId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.d7ef43b1-51aa-4068-a1b8-f86ea39dba97</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:42738960b3d81f85:8740bc4:189d575e46d:-7c34</guid>
            <versionId>c4fb172b-e6e4-4ccc-847f-c194250371be</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e43bf0b4-0501-45b8-a5ba-6aa05dfdecaf</processItemPrePostId>
                <processItemId>2025.479ee959-459f-4b6c-81ec-20475f6c5676</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Approvals Comments -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Approvals Comments -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>d176cd42-ac23-4753-9e5d-352bf36ddc16</guid>
                <versionId>44bfeb23-1bb9-45b3-bb99-2a83ae8636d0</versionId>
            </processPrePosts>
            <layoutData x="344" y="174">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.d7ef43b1-51aa-4068-a1b8-f86ea39dba97</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>d921257b-cc10-42b5-9412-742230e6decb</guid>
                <versionId>e1cb3d65-d879-4fdf-b631-05e05e526bf9</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.76b8034f-f4b3-4209-ab40-b7138acdd99a</parameterMappingId>
                    <processParameterId>2055.1fb1e2ae-4f95-443e-861b-b26713501f83</processParameterId>
                    <parameterMappingParentId>3007.d7ef43b1-51aa-4068-a1b8-f86ea39dba97</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ddcc3730-c418-4618-a549-30a1c9971677</guid>
                    <versionId>7727db92-1294-4eb6-970a-adfdada833a9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Approvals Comments" id="1.421a8257-34a5-45f9-b03f-a68e989d4ab8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="appLog" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.792bf3d4-e1dc-4b70-afbc-5214492c915c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "appLog";
autoObject[0].role = "appLog";
autoObject[0].step = "appLog";
autoObject[0].action = "appLog";
autoObject[0].comment = "appLog";
autoObject[0].terminateReason = "appLog";
autoObject[0].returnReason = "appLog";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "appLog";&#xD;
autoObject[1].role = "appLog";&#xD;
autoObject[1].step = "appLog";&#xD;
autoObject[1].action = "appLog";&#xD;
autoObject[1].comment = "appLog";&#xD;
autoObject[1].terminateReason = "appLog";&#xD;
autoObject[1].returnReason = "appLog";&#xD;

autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="complianceComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.59289b25-4be8-4329-b825-d40969b0afee">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "complianceComments";&#xD;
autoObject[1].role = "complianceComments";&#xD;
autoObject[1].step = "complianceComments";&#xD;
autoObject[1].action = "complianceComments";&#xD;
autoObject[1].comment = "complianceComments";&#xD;
autoObject[1].terminateReason = "complianceComments";&#xD;
autoObject[1].returnReason = "complianceComments";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "CADcomments";&#xD;
autoObject[1].role = "CADcomments";&#xD;
autoObject[1].step = "CADcomments";&#xD;
autoObject[1].action = "CADcomments";&#xD;
autoObject[1].comment = "CADcomments";&#xD;
autoObject[1].terminateReason = "CADcomments";&#xD;
autoObject[1].returnReason = "CADcomments";&#xD;
&#xD;
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject[0].startTime = new TWDate();
autoObject[0].endTime = new TWDate();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";&#xD;
&#xD;
autoObject[1] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject[1].startTime = new TWDate();&#xD;
autoObject[1].endTime = new TWDate();&#xD;
autoObject[1].userName = "treasuryComments";&#xD;
autoObject[1].role = "treasuryComments";&#xD;
autoObject[1].step = "treasuryComments";&#xD;
autoObject[1].action = "treasuryComments";&#xD;
autoObject[1].comment = "treasuryComments";&#xD;
autoObject[1].terminateReason = "treasuryComments";&#xD;
autoObject[1].returnReason = "treasuryComments";&#xD;

autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="appLog" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.2eefde76-5898-4e8d-87b1-0f434cabef87" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.792bf3d4-e1dc-4b70-afbc-5214492c915c</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.59289b25-4be8-4329-b825-d40969b0afee</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.4573cdb1-aafb-4a82-9e6e-c1d5972414c6</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.cb12b0cf-49f7-4a48-9255-e4950bb2b031</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.5d4f7109-3f4a-4b64-ba7c-093ed52c115c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.2eefde76-5898-4e8d-87b1-0f434cabef87</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="f44ccac5-2f7d-449f-813f-ff5d6eb3e1c9">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c7958f6e-dcda-4466-ac19-f421ddef0fdf" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>34d0aea0-8635-4686-a941-9d3b02c3d329</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>86c4f0fd-1d85-45a6-af83-e58e07317e9c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>44cb0b0a-dbd2-4d94-8932-f78c5573b1af</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>479ee959-459f-4b6c-81ec-20475f6c5676</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.48664197-e262-434a-afa6-6e2523b6a453</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="34d0aea0-8635-4686-a941-9d3b02c3d329">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-614a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9662b217-7681-438a-abe0-7f4e039b1c0a</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="89ba5ee3-53d1-4a6f-9ba9-c2ff6df217e4" targetRef="86c4f0fd-1d85-45a6-af83-e58e07317e9c" name="To Script Task" id="2027.48664197-e262-434a-afa6-6e2523b6a453">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="86c4f0fd-1d85-45a6-af83-e58e07317e9c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="275" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.48664197-e262-434a-afa6-6e2523b6a453</ns16:incoming>
                        
                        
                        <ns16:outgoing>9662b217-7681-438a-abe0-7f4e039b1c0a</ns16:outgoing>
                        
                        
                        <ns16:script>var len = tw.local.appLog.listLength;&#xD;
&#xD;
if (tw.local.treasuryComments != null) {&#xD;
	for (var i=len; i&lt;tw.local.treasuryComments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.treasuryComments[i]);&#xD;
	}&#xD;
}&#xD;
if (tw.local.CADcomments != null) {&#xD;
	for (var i=len; i&lt;tw.local.CADcomments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.CADcomments[i]);&#xD;
	}&#xD;
}&#xD;
if (tw.local.complianceComments != null) {&#xD;
	for (var i=len; i&lt;tw.local.complianceComments.listLength; i++) {&#xD;
		tw.local.appLog.insertIntoList(i, tw.local.complianceComments[i]);&#xD;
	}&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="86c4f0fd-1d85-45a6-af83-e58e07317e9c" targetRef="34d0aea0-8635-4686-a941-9d3b02c3d329" name="To End" id="9662b217-7681-438a-abe0-7f4e039b1c0a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="86c4f0fd-1d85-45a6-af83-e58e07317e9c" parallelMultiple="false" name="Error" id="44cb0b0a-dbd2-4d94-8932-f78c5573b1af">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="310" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4a065e67-3143-40a3-8791-dd79f821381d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f0aac7f9-cf2c-40d5-8fab-d040e528fa6b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3f61d90f-6b3e-4198-8fc4-3e374ef0a8d4" eventImplId="23dad045-7159-4438-84e3-cd8c55750b61">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="479ee959-459f-4b6c-81ec-20475f6c5676">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="344" y="174" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Approvals Comments -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Approvals Comments -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4a065e67-3143-40a3-8791-dd79f821381d</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="21fc6f86-c020-4929-8767-128da7972b81" eventImplId="1a5d0c35-5c9d-4bc2-8f7f-e433b47b556f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="44cb0b0a-dbd2-4d94-8932-f78c5573b1af" targetRef="479ee959-459f-4b6c-81ec-20475f6c5676" name="To End Event" id="4a065e67-3143-40a3-8791-dd79f821381d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.890a7ca1-7731-4d84-8302-ce5a5b2d27fe" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9662b217-7681-438a-abe0-7f4e039b1c0a</processLinkId>
            <processId>1.421a8257-34a5-45f9-b03f-a68e989d4ab8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.34d0aea0-8635-4686-a941-9d3b02c3d329</toProcessItemId>
            <guid>e33fc2b2-f288-458c-99c1-a3d37822a90d</guid>
            <versionId>b514255e-cd46-41b8-856d-f95a821260ca</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.86c4f0fd-1d85-45a6-af83-e58e07317e9c</fromProcessItemId>
            <toProcessItemId>2025.34d0aea0-8635-4686-a941-9d3b02c3d329</toProcessItemId>
        </link>
    </process>
</teamworks>

