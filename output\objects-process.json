{"typeName": "Process", "count": 111, "objects": [{"id": "1.b7869400-09ed-4f15-972f-39c44a324089", "versionId": "3d49ff53-f14e-445c-826a-2e1e534e63f3", "name": "1.b7869400-09ed-4f15-972f-39c44a324089", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.c042b0b3-9130-4489-8642-a4a851c1b331", "versionId": "bdbad7cb-8c38-4cd3-9fa4-2f0110d5e0d2", "name": "Add Advance Payment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.421a8257-34a5-45f9-b03f-a68e989d4ab8", "versionId": "8bbff875-a233-445f-9a27-b8bfa2be6cad", "name": "Approvals Comments", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.43e432f4-0618-40df-9601-41aef33a2c23", "versionId": "f7acd706-a1f7-47c0-9bd1-7c96fd6e4a14", "name": "Approve Request by Credit Admin Execution Checker", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.07f8c418-7e5f-4b13-90a2-1e2660fc7597", "versionId": "dc656848-3a27-4cf3-bc0f-be7095712a50", "name": "Attachment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.ecd7d5e6-9ab6-4f46-b38c-5afbc2dc21a3", "versionId": "f3ffbae2-1002-46d8-aae2-2da8c742857c", "name": "Authorize FileNet", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.a195e5d7-a7f4-4419-84bc-2bcff38a6561", "versionId": "65a7057c-2e92-4adb-b5f0-39e53b996520", "name": "Authorize ICAP", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.8f81ee30-b515-498c-b076-7fcf05c5013d", "versionId": "3314b751-9904-485b-ab67-4e3bf2f6d912", "name": "Branch Hub filter service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2", "versionId": "8e12ecd0-e7fc-4851-bcd1-6e3046f5bb9b", "name": "CAD filter service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.6d2930cb-0b25-4940-913e-e6643366bf6a", "versionId": "f3308354-323b-4503-8927-8816e96a8247", "name": "Cancel IDC", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.96dc5449-b281-4b46-8b84-bf73531c54ff", "versionId": "98330fbf-f4e8-4b51-a613-c860095f6cd3", "name": "cancel request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.37eab8a6-3b8b-4e52-8824-851827e9889b", "versionId": "8037e590-cde5-4c5f-bb6f-9d59bac51cdf", "name": "Check Assign Large Corprate", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488", "versionId": "585872b8-a86e-40e7-8ebc-b9330273605d", "name": "Check Bill", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.0d6d9e00-0481-4275-82a8-9bfdb143a2f8", "versionId": "e7d1f10e-fd04-4367-ba26-aeab4c3edb93", "name": "Check Customer Accounts", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.6081d75e-a1c6-4d00-ade0-18cd3cfaaffa", "versionId": "4875899d-0a9a-4397-8eca-291e30427256", "name": "Check Existing CIF", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.417ae6ff-837a-426f-b651-d8e3e8810d90", "versionId": "64cf0631-8b5b-4884-8a3a-ab390d205a43", "name": "Check Existing GL Account", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.5b59000d-e60c-4dee-87e1-34bfc64da51b", "versionId": "5e845de4-135d-454c-bd98-e073e2c3c138", "name": "Check Invoice", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258", "versionId": "da92b884-e937-45c3-b77b-13a00d6fd441", "name": "Check Large corporate in DB", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.51cfa3b6-ac55-4472-8d92-9c7aac143ff1", "versionId": "3b6bd824-0af0-4285-a55d-5235f1cbefd2", "name": "Client-Side Human Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.ac6ff0af-98bb-4d5c-b7fe-2ec84aebba72", "versionId": "********-6bc8-4040-a332-b55749401b80", "name": "Client-Side Human Service_1", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.4e624381-ed2d-4716-836c-494f4210ce96", "versionId": "bef73ddf-aa6e-45da-b496-cb2893a566d9", "name": "convert currency", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058", "versionId": "44205fb7-bc66-494c-aa1f-cfd44732b820", "name": "Create CIF Folder", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.45e2a149-1d4a-4946-ad59-f81f1424f100", "versionId": "d8df3b6e-051c-495b-8085-957e0914d5e6", "name": "Create Folder FileNet", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.76e93686-1290-441b-87b6-9e3624b6ce34", "versionId": "9891c5eb-aed4-470e-8b57-637f29eb7d1f", "name": "Create Folder Structure", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25", "versionId": "203cbf5e-3e8a-44a9-bc4c-1d2fe5420e28", "name": "Create IDC Customs Release Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.19d05118-4f9d-482a-afd5-662663bc3612", "versionId": "c6684745-aea7-48d3-b17a-570cd239aa61", "name": "Create IDC Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.7eee0980-1508-423c-95a0-c08797e92721", "versionId": "24201065-1d0c-49f7-b4a6-2361ce7b2758", "name": "Create IDC Reversal Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e2bdefe5-4826-4c86-8897-1bfda517d27d", "versionId": "b18cbb03-cd9f-4625-b5ab-6154de5e0b4b", "name": "Create IDC Withdrawal Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.92ae0e54-fe7e-4f8f-a009-7220bb980c9b", "versionId": "14b4332a-9733-4a90-b9e2-81cbbcbcddc9", "name": "Create Liquidation", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.97607171-1a4e-436c-8b92-7a6920219722", "versionId": "8ebcbfcb-ab1c-4985-b703-1a40cd13a88e", "name": "Customs Release Approval Mail Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.9f0a859b-5010-4ab6-947a-81ad99803cf1", "versionId": "7d0eadd2-805f-4406-bcc8-2ad6b25b4340", "name": "Database Integration", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.2d69e423-2547-4495-845d-7bccbf743136", "versionId": "4e69ec98-b083-4250-b444-8312c68bf7aa", "name": "DB BPM Audit user tasks", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.68698fcd-008a-4312-8428-1b0aee0e67c8", "versionId": "1513ec0e-fca2-4de3-8dcd-a3cb5b9a2f69", "name": "Delete Advance Payment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.7dcf1057-2d3e-4d5f-bea7-4f12cca3d3c4", "versionId": "3edca62b-a2ba-49cb-9a95-8581fdf9bd17", "name": "Deployment Service Flow", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b", "versionId": "dde5b93d-5f58-413a-9c4e-335d330420ad", "name": "Escalation Mail Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.7073c330-e8a8-4bd8-94fb-d764787dffeb", "versionId": "bb90ce49-9010-4cee-8d4d-ea8f86ab229d", "name": "Execution HUB Filter Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.4c90d45f-cb2b-4afd-bc7a-b0f825c061a3", "versionId": "8a086af6-181f-4fe2-b687-29cf973a43d4", "name": "Execution Hub Processing Withdrawal Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.041e4df2-7de8-4ce4-85a2-24f4482c8a7a", "versionId": "d6c4dd61-a0ce-410e-a5e1-aefc566f9b0e", "name": "Execution Hub Processing Withdrawal Request Review", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.0f065bb2-7741-40cd-bc78-8e7d29bcd8e5", "versionId": "1396a188-ab01-4a12-8965-b83193544c74", "name": "FC_Get Account Number", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.b066b0aa-3b41-4663-a2f1-ca880f07a183", "versionId": "39ccb957-5a9e-4cc1-af21-148756d18c2e", "name": "Filter CA Team", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c", "versionId": "78c0d0be-a20e-445e-95d5-f717564a75f6", "name": "Generate BPM Request Number", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.1d264c59-eafd-4f05-a029-411f466237ce", "versionId": "e79ea78c-c820-4f80-8789-75c76644ffc9", "name": "get advance payment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.********-457c-489f-9972-9349737b8a6e", "versionId": "d76e2b87-c084-41a4-aa81-1336fec3d7f3", "name": "Get Advices Code", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e3028b61-b01a-4a56-a086-244083a8d445", "versionId": "a9cd3b56-b156-47f9-a16d-22b9ac5b4562", "name": "Get Applicant and Accountee Facility Codes", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.71c9670f-4700-465f-a303-3053d933a16e", "versionId": "61c8ad15-3252-4361-8e7e-221b85be1614", "name": "Get Applied Charges", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf", "versionId": "1e6e6ff5-ed7e-4182-adc9-529fba296f72", "name": "Get BIC Codes", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.8804be93-df98-44b0-bf01-1f35970dfee0", "versionId": "14d6ae4e-b614-4869-aed1-77097c4f9d22", "name": "get booked facilities", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.ca00ae5d-7e75-4b8a-bc01-7a95d477c541", "versionId": "e65c6a95-f84a-4e05-8ebb-09058fcf8781", "name": "Get Booked Facilities", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.cc39766e-740a-4aae-91d4-983d3f72c00c", "versionId": "56dde49f-61bb-48fe-b400-384a4961055d", "name": "Get CBE Sanctions", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165", "versionId": "3d6f0975-26c1-481d-8b98-ef96abe9d20f", "name": "Get Center by branch code", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.69de9393-41a9-476a-9c6c-10054e92ebb8", "versionId": "c0102c8d-302a-4a3b-8d2c-d4e7365549f9", "name": "Get Charge Details", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.b24f815d-f98b-4f2e-9dbe-f7601985749f", "versionId": "49fe4e23-b3ad-4893-9ab8-00a157ea3a7e", "name": "Get Country of Origin", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.753ea3e8-d234-409e-9fb2-015400e4c9fb", "versionId": "b3bd252b-5841-4c98-beb8-464d9f17622b", "name": "Get Customer Accounts", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.473ca24e-c03e-4a25-b37a-58cd047b0fff", "versionId": "106b6b3d-c66b-4e95-b1f1-a138f6a138c3", "name": "Get Customer Information", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.55393dcd-3352-41df-9690-75cb207d48b8", "versionId": "44eda8be-e2fb-4ce2-ad48-42ed1f19b2af", "name": "Get Exchange Rate", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.6cb94b35-ca38-4aee-8b8e-7ca2e59e39df", "versionId": "ec61280d-a56e-4d5b-ab3c-a1f96ccb1fed", "name": "Get Facility Codes", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.027e3afb-1a94-4896-883d-daa4cdfee232", "versionId": "cf9b85d7-e4d2-4dc2-9e41-5fd3490c6154", "name": "Get HUB name by code", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.97d49092-c827-40be-bf11-7146f12c0134", "versionId": "f530bd28-2c3c-41f6-b43c-502d7363fd16", "name": "Get IDC initiator", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.0d77a33d-fe75-4164-87ca-b9d0a0b91640", "versionId": "e1b8f376-4c18-4c89-9511-78d482f982ae", "name": "Get Interest Amount", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.c3124e3b-0cdd-4710-a44b-6071a04e76dc", "versionId": "6a1c2fa3-a60c-4e20-bb2d-08875bb8983e", "name": "Get Party Details", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1", "versionId": "011905f4-bb8b-46d2-bfb6-2eef2c<PERSON><PERSON><PERSON>", "name": "get party type", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.b16e9737-5d24-4229-83f7-2e9dc0637416", "versionId": "94cb3c17-ad1a-444c-9443-0a79e03f9a2e", "name": "Get Product Codes", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13", "versionId": "154abbc4-880d-4892-bb07-4e009700c72b", "name": "Get Request Number And CBE", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2", "versionId": "0cd7c585-f3c1-4732-8390-ee6b46efcaf3", "name": "Get Required Documents", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.9545825f-4a3f-43f2-82b0-a6b75c82df6f", "versionId": "393eb98b-f609-4418-a6e4-4b41c166b773", "name": "getLookups", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.01554586-6b73-40b1-957d-c2491f071bbb", "versionId": "efbec366-e797-45d2-9a92-b625a913f937", "name": "getRequestType", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e93f9906-c4e8-472c-be9e-35e8202551c9", "versionId": "a6922de6-9abf-4932-adeb-91bc37d9a2ba", "name": "Hub Initiation Trade Compliance", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.b8deeb2e-af87-4d78-a0c3-a27d16bb22f8", "versionId": "c05f5cd6-2cbd-4661-9b0e-cfac593be59e", "name": "Hub Liquidation Trade Compliance", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.5cb2beea-428b-42ba-b1c1-8b6c92bc89a0", "versionId": "5b8d1ff7-42b7-4543-b983-c04f8bab91c2", "name": "IDC Execution Hub Initiation", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.9e0de1a3-7709-49c8-9b29-963ea4784c51", "versionId": "85b06b71-d037-473a-ad80-0b3621c3ab1c", "name": "IDC Execution Hub Initiation 2", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.480ae8a4-054d-4c43-9b3a-7c7c03194306", "versionId": "7e04ed62-6c92-45e9-8f7b-ba07ba12821a", "name": "IDC Execution Hub Initiation Review", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.6ed24840-941d-48c8-9769-8bbc53ced721", "versionId": "1787bd0b-57eb-40ed-bcd5-f68fa6250b8b", "name": "IDC Execution Hub Liquidation", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.8e17d2f7-29ca-4085-ba10-ed50e8088ff3", "versionId": "21b131c1-4106-447a-80fa-8885da8b2e9b", "name": "IDC Execution Hub Liquidation Review", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.bd478cbf-52d1-4705-9fe4-6868408a5256", "versionId": "b85f4944-ce3a-4078-a025-170cba6c5726", "name": "IDC Initialization Process Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.30211575-677e-4082-b83a-a9d7592dfc68", "versionId": "b415f71e-aee9-47ae-8bd8-e10e6b8b2f70", "name": "IDC Request Details UI", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.8ef1af07-5249-42da-b1a2-40f2e2ea490f", "versionId": "161d88a3-89d1-4f29-8cc4-84d5dfcca131", "name": "IDC Request Details UI 2", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.80b52f2e-cf28-4943-b3bd-447b9230d1db", "versionId": "0d14b68b-3083-4c3b-bd61-c787c3aecd10", "name": "IDC Request Details UI 3", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.89b422a8-bd14-41af-8dbc-4155dc2154f8", "versionId": "633735f9-ea5d-4aa6-8174-333f080f3db3", "name": "IDC Request Details UI_1", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e5318bc6-7232-4e45-a258-184ebb476098", "versionId": "6532a569-a7f8-46fc-9b2b-4e37e36ae8f5", "name": "IDC test", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.06eaabde-33db-480a-9d65-982fa27c2eac", "versionId": "45d0fd4f-b287-4f32-a11d-c0369b35bc14", "name": "Insert IDC Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.05e3bb1d-d52a-4ff9-9141-8307f650a0c9", "versionId": "503010f5-4243-4cbd-8d62-134c3d03e82e", "name": "Print Documents for Customer", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.31a44e81-e812-4dc3-b80d-e2bd710a4bad", "versionId": "07c9f141-bdc5-4c5a-80e4-4d509a68ca82", "name": "Print Withdrawal Documents", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f", "versionId": "01e1a627-a82b-427d-a98b-b906a323ebd0", "name": "Retrieve Customer Facilities", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.f19131b2-2481-4c62-a40d-8f829cdeb66a", "versionId": "5e659ced-4faf-498d-a457-4cbae46aa683", "name": "Retrieve Request Data", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.1f68d702-993e-4d40-b851-571e35cda577", "versionId": "60f2e782-0763-4672-822d-fcfe07eed11b", "name": "Reversal Approval Mail Service", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.16931d1e-3b6a-4725-9805-a04464e7991e", "versionId": "8f5fff8e-eede-42ee-9e4c-eb63136b1769", "name": "Review IDC Customs Release Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.0903f902-3a18-4b6d-9e59-1d4ad6437937", "versionId": "3be3f9f3-da8c-48b4-96a7-f9f34867f5c7", "name": "Review IDC Customs Release Request by Trade FO", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.fcc27b9d-793f-4d79-b8ed-d17e5877201f", "versionId": "5ebd982d-c295-488a-bbb1-ee9c2b94f322", "name": "Review IDC Request by Branch Compliance Rep", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.81cc6ee4-96cd-4a6a-97cf-31c4094876fd", "versionId": "c2c0b863-5b00-40de-8288-46bdf43cc88c", "name": "Review IDC Request by Trade Front Office", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.98cf1a97-9e24-44c6-9e06-44065464f45b", "versionId": "ad47be90-8369-4426-9c29-87af02e7a6f7", "name": "Review IDC Reversal Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.3beb46eb-40aa-4939-bf56-225a33b731fd", "versionId": "0c9d8b08-496a-46a7-ac85-8e75fcece42a", "name": "Review IDC Withdrawal Request by Compliance Rep", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.53b740e1-0338-4573-9e9b-780485ae5319", "versionId": "84e6726d-00a9-4c2d-b9b1-b70a6adca153", "name": "Review IDC Withdrawal Request by Trade FO", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300", "versionId": "ee5d4846-722c-40a1-a087-2e737861204c", "name": "Review Pending Queue by Credit Admin Execution Maker", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.15b5fc3d-bb22-4981-b2bc-bafefbe8f721", "versionId": "f2021bfe-5e00-413e-a463-631eaeb79b7a", "name": "Review Request by Credit Admin Execution Maker", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.f964f62b-c99c-41db-81e6-d7c13f5ef504", "versionId": "e8837f42-8ac9-4aab-8dd8-8209314b2499", "name": "Review Request by Treasury Checker", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.d5475add-79dd-4468-9671-f08aa8c7c181", "versionId": "dd4b2646-7c46-40a3-85f2-19ad98279d06", "name": "Review Request by Treasury Maker", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.c0682bdf-40ae-48f0-92cc-390e1358e90a", "versionId": "bf0d74be-3729-4165-b924-2c4e3faa3bce", "name": "test", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.7cd9c833-9de0-441c-987b-2cea94c2b257", "versionId": "7065b81a-a1fb-4044-a3d4-9c906e371585", "name": "test json", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.d4263736-0fea-47c5-90ea-cc6b49adfcec", "versionId": "3af2140b-3fba-4022-a668-ec75b0cd31b4", "name": "Test Swift", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.b0d126fc-c26e-4e48-b3c8-bf0af0485476", "versionId": "005a140d-13e1-414c-9ef4-f919a087df20", "name": "<PERSON><PERSON><PERSON>", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.1fe1e7f7-ce9c-401f-991c-b6eebe370d51", "versionId": "5a364f9c-7622-48fb-8573-30f0a640056e", "name": "Trade Compliance", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.33e9489c-6751-46e8-bc5c-74b573bdd856", "versionId": "8373c3f5-cf50-4f0d-9cb4-d5aed9bc8bc1", "name": "update advance payment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.604d7736-0fa5-4f03-af3c-19556056fdbf", "versionId": "0fb863f6-fe34-4648-9789-a8058a27dc3e", "name": "update facility", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59", "versionId": "cbf38ad1-7216-4342-a2f8-852a4004eda1", "name": "Update History", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.e75c339e-472e-439b-878f-bfafd0c4b968", "versionId": "6719e525-cf7b-433e-bdeb-8fac927dd57f", "name": "Update IDC Request", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.02d07ad8-f212-4ea3-8b5e-e8457d984314", "versionId": "e7d97031-56df-4548-a0ab-0e5948847fd0", "name": "validate accounts and attachment", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.72840b39-03cc-42d8-a24b-01650b923101", "versionId": "acfb07f7-5517-43f9-a493-0bdd78068752", "name": "Validate BIC", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.65481b15-1f15-43d3-b21f-fd25532bd3a4", "versionId": "ce761e63-874e-4d57-8f2d-e48808e443de", "name": "Validate Collateral Amount", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.c04ab227-0184-4fcd-99e2-b165319d2807", "versionId": "db717c91-795e-42b4-8aa4-ab91b87ea3f7", "name": "Validate Required Documents", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.f2c8f018-67e0-416a-acb3-8af30664cba5", "versionId": "634e32ae-4dfc-4123-bd61-53bd0ef048b8", "name": "<PERSON> <PERSON> App<PERSON>al", "type": "process", "typeName": "Process", "details": {}}, {"id": "1.700681c9-fc9c-4767-b15d-063fdcbc57ed", "versionId": "c93560c8-10b6-4d12-a981-279cc878a423", "name": "Withdrawal Approval Mail Service", "type": "process", "typeName": "Process", "details": {}}]}