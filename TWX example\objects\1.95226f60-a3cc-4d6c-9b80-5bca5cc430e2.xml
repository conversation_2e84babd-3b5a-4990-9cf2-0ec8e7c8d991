<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" name="Get Required Documents">
        <lastModified>1722429902951</lastModified>
        <lastModifiedBy>eslam</lastModifiedBy>
        <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>cf210144-8bf3-4a9d-9e16-ef4d94e5713a</guid>
        <versionId>0cd7c585-f3c1-4732-8390-ee6b46efcaf3</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:d227ae9e677c32d5:-661aced:190e953c57b:2add" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"cace737e-f023-4235-a294-eedb70d8852a"},{"incoming":["bcbbc3cf-20fc-4754-9bda-a77f59d53f92","60319491-91ea-4de2-bb72-28619b23dd1f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"191c4fb9-e70a-4225-9c78-dc88865ab6d4"},{"targetRef":"0875087d-623f-44f0-96fd-bc591d7e87d6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Is IDC Amendment","declaredType":"sequenceFlow","id":"2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27","sourceRef":"cace737e-f023-4235-a294-eedb70d8852a"},{"startQuantity":1,"outgoing":["a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5"],"incoming":["3ee3e353-d98d-48f0-8781-fb89790ccb0d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":190,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Validate Required Documents","dataInputAssociation":[{"targetRef":"2055.ca1eead8-e152-4ac6-8389-4431f391cc84","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestType"]}}]},{"targetRef":"2055.1388ff03-e35d-4090-b3b9-0281f961e86e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.documentSource"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"5885e4fb-2826-4f20-ac59-da2beded9cd8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requiredDocuments"]}}],"sourceRef":["2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6"]}],"calledElement":"1.c04ab227-0184-4fcd-99e2-b165319d2807"},{"targetRef":"fea09597-38f0-414f-ab48-f30d6e9f04d6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Documents In Folder","declaredType":"sequenceFlow","id":"a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5","sourceRef":"5885e4fb-2826-4f20-ac59-da2beded9cd8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requiredDocuments","isCollection":true,"declaredType":"dataObject","id":"2056.130284bf-e9df-4d06-9e30-5e1d7e283410"},{"outgoing":["3ee3e353-d98d-48f0-8781-fb89790ccb0d","bcbbc3cf-20fc-4754-9bda-a77f59d53f92"],"incoming":["2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27"],"default":"3ee3e353-d98d-48f0-8781-fb89790ccb0d","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":95,"y":76,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"Is IDC Amendment","declaredType":"exclusiveGateway","id":"0875087d-623f-44f0-96fd-bc591d7e87d6"},{"targetRef":"5885e4fb-2826-4f20-ac59-da2beded9cd8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validate Required Documents","declaredType":"sequenceFlow","id":"3ee3e353-d98d-48f0-8781-fb89790ccb0d","sourceRef":"0875087d-623f-44f0-96fd-bc591d7e87d6"},{"targetRef":"191c4fb9-e70a-4225-9c78-dc88865ab6d4","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  \"IDC Amendment\""]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":396,"y":16}]}]},"name":"To End","declaredType":"sequenceFlow","id":"bcbbc3cf-20fc-4754-9bda-a77f59d53f92","sourceRef":"0875087d-623f-44f0-96fd-bc591d7e87d6"},{"outgoing":["453bda9b-6980-427d-8009-03c479397846"],"incoming":["a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":310,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["ContentTask"]},"operationRef":"DOC_OP_MOVE_DOCUMENT","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Documents In Folder","dataInputAssociation":[{"targetRef":"FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FileNet"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"fea09597-38f0-414f-ab48-f30d6e9f04d6","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","declaredType":"TFormalExpression","content":["tw.local.documents"]}}],"sourceRef":["DOCUMENTS"]}],"orderOverride":false},{"targetRef":"9ec36c88-4983-4a13-807c-18837f6fb353","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Missing Documents","declaredType":"sequenceFlow","id":"453bda9b-6980-427d-8009-03c479397846","sourceRef":"fea09597-38f0-414f-ab48-f30d6e9f04d6"},{"itemSubjectRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","name":"documents","isCollection":true,"declaredType":"dataObject","id":"2056.e3d8f359-d676-4164-a9f3-4ed42dc39bcd"},{"startQuantity":1,"outgoing":["60319491-91ea-4de2-bb72-28619b23dd1f"],"incoming":["453bda9b-6980-427d-8009-03c479397846"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":457,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Missing Documents","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"9ec36c88-4983-4a13-807c-18837f6fb353","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMessage = \"The following documents are requierd:\\n\";\r\ntw.local.errorExist = false;\r\nvar allDocumentTypes= [];\r\nvar haveIDC = false;\r\nfor (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) {\r\n\ttw.local.debug = \"documentsTypesSelected\";\r\n\tallDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;\r\n}\r\n\r\nfor (var i=0; i&lt;tw.local.documents.listLength; i++) {\r\n\ttw.local.debug = \"documents\";\r\n\tif (tw.local.documents[i].objectTypeId==\"IDCDocument\") {\r\n\t\tvar haveIDC = true;\r\n\t}\r\n}\r\nvar j=0;\r\nif (haveIDC) {\r\n\tfor (var i=0; i&lt;tw.local.requiredDocuments.listLength; i++) {\r\n\t\ttw.local.debug = \"requiredDocuments\";\r\n\t\tif (allDocumentTypes.indexOf(tw.local.requiredDocuments[i]) == -1) {\r\n\t\t\ttw.local.errorMessage+=tw.local.requiredDocuments[i]+\"\\n\";\r\n\t\t\ttw.local.errorExist=true;\r\n\t\t}\t\r\n\t}\r\n}else{\r\n\ttw.local.errorMessage = \"must upload document of type IDC Document\";\r\n\ttw.local.errorExist=true;\r\n}\r\n"]}},{"targetRef":"191c4fb9-e70a-4225-9c78-dc88865ab6d4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Validate Required Documents","declaredType":"sequenceFlow","id":"60319491-91ea-4de2-bb72-28619b23dd1f","sourceRef":"9ec36c88-4983-4a13-807c-18837f6fb353"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"debug","isCollection":false,"declaredType":"dataObject","id":"2056.a1da7b12-216b-4b22-9aa9-0e7532d345e7"},{"parallelMultiple":false,"outgoing":["220f5d82-0eae-448a-8929-012b7d644595"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a15aeb56-c45d-4d71-8603-7246e07d78c4"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"12b8208d-bad3-40d0-8419-881875db64d6","otherAttributes":{"eventImplId":"5d8ebb18-3ba3-4c27-8345-948a53634497"}}],"attachedToRef":"5885e4fb-2826-4f20-ac59-da2beded9cd8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":225,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"0c9746f9-41ef-4e3c-87ba-5823dda3345e","outputSet":{}},{"parallelMultiple":false,"outgoing":["e9d4da5b-f888-42d8-880a-af32575aca4f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"1ad1d590-f8d9-4dca-8819-255470d66580"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"979981e1-6b84-4438-8028-bd384be01a80","otherAttributes":{"eventImplId":"332bc231-23bd-4cb6-85c6-4e6a676d92c2"}}],"attachedToRef":"fea09597-38f0-414f-ab48-f30d6e9f04d6","extensionElements":{"nodeVisualInfo":[{"width":24,"x":345,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"cba2ad12-fd37-4ea3-85f8-c5c60711005f","outputSet":{}},{"parallelMultiple":false,"outgoing":["9c720847-7372-4cdd-8829-130eb1f88555"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"45899147-d106-44cc-810f-00aba8addb71"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9d2021dc-4e15-4a4e-8e2e-89dad7c1d74f","otherAttributes":{"eventImplId":"c634a25f-d714-4b8d-8155-c3a041c5e0af"}}],"attachedToRef":"9ec36c88-4983-4a13-807c-18837f6fb353","extensionElements":{"nodeVisualInfo":[{"width":24,"x":492,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"7734cbef-ad36-4b37-84a2-d79df2d06296","outputSet":{}},{"incoming":["9c720847-7372-4cdd-8829-130eb1f88555","e9d4da5b-f888-42d8-880a-af32575aca4f","220f5d82-0eae-448a-8929-012b7d644595"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"eb95e855-42ba-40a2-8810-9a027c0db92d","otherAttributes":{"eventImplId":"4b2b813e-cb8e-49f9-8a44-0a35ef5f817d"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":412,"y":182,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Get Required Documents -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Get Required Documents -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"31219688-92d8-4699-8285-5035e5471d3d"},{"targetRef":"31219688-92d8-4699-8285-5035e5471d3d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"220f5d82-0eae-448a-8929-012b7d644595","sourceRef":"0c9746f9-41ef-4e3c-87ba-5823dda3345e"},{"targetRef":"31219688-92d8-4699-8285-5035e5471d3d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"e9d4da5b-f888-42d8-880a-af32575aca4f","sourceRef":"cba2ad12-fd37-4ea3-85f8-c5c60711005f"},{"targetRef":"31219688-92d8-4699-8285-5035e5471d3d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"9c720847-7372-4cdd-8829-130eb1f88555","sourceRef":"7734cbef-ad36-4b37-84a2-d79df2d06296"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.8280301c-20c8-48a4-848e-800dcfff4897"}],"laneSet":[{"id":"8db68be9-5c59-4cb2-b98b-1b4ef2004283","lane":[{"flowNodeRef":["cace737e-f023-4235-a294-eedb70d8852a","191c4fb9-e70a-4225-9c78-dc88865ab6d4","5885e4fb-2826-4f20-ac59-da2beded9cd8","0875087d-623f-44f0-96fd-bc591d7e87d6","fea09597-38f0-414f-ab48-f30d6e9f04d6","9ec36c88-4983-4a13-807c-18837f6fb353","0c9746f9-41ef-4e3c-87ba-5823dda3345e","cba2ad12-fd37-4ea3-85f8-c5c60711005f","7734cbef-ad36-4b37-84a2-d79df2d06296","31219688-92d8-4699-8285-5035e5471d3d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"72a9853a-3081-43fc-a8bf-65cb77d9ac85","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Required Documents","declaredType":"process","id":"1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"id":"2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.74cb9e94-45c3-4ba9-8862-d45286d425b6"}],"inputSet":[{"dataInputRefs":["2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c","2055.aae56053-3bba-40b1-abc9-6a441a93f307","2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef","2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8"]}],"outputSet":[{"dataOutputRefs":["2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749","2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6","2055.74cb9e94-45c3-4ba9-8862-d45286d425b6"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"Correspondent\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"documentSource","isCollection":false,"id":"2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"Advance Payment\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.aae56053-3bba-40b1-abc9-6a441a93f307"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"{00B3EA88-0000-C735-A621-11E22CFFF8BE}\""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.Attachment();\nautoObject[0] = new tw.object.Attachment();\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"id":"2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="documentSource">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f1b09701-73e2-4b04-8c07-378fdbb1f148</guid>
            <versionId>9297c484-3f62-4ed2-9aed-31f7cfc94c0f</versionId>
        </processParameter>
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.aae56053-3bba-40b1-abc9-6a441a93f307</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cd51ecf7-ce41-41e1-90bb-f190099dd4f8</guid>
            <versionId>7fd6d212-8e31-4ec3-bd05-0e77bc06e8ed</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f3d93717-b3d8-4efd-a2f7-0a42678f8a9d</guid>
            <versionId>c8305255-b78b-49f1-94e1-99b0103bcf79</versionId>
        </processParameter>
        <processParameter name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>4</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.Attachment();
autoObject[0] = new tw.object.Attachment();
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bb5a904c-56c9-4b1d-bb3a-038825a8f4bc</guid>
            <versionId>d1123792-4684-436f-ae71-24129f54c865</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8182b777-99e5-445a-bcea-93dd4b202fc1</guid>
            <versionId>603da40a-facc-46ce-af07-1ec81dc103eb</versionId>
        </processParameter>
        <processParameter name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2eacffef-279c-4562-b369-339864b83f47</guid>
            <versionId>c2042efd-78fc-40a8-a21a-4107fde39aeb</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3eae3a8e-48c7-4dad-bc29-0784e0b41fa0</guid>
            <versionId>56fcfd6a-f03d-4a26-95b0-e5f265db9eef</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.60108cef-f7d8-4f16-ab1f-616102bfb3d0</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>66</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d08e1a2e-d1b1-4d8a-b556-3b311ad0abd8</guid>
            <versionId>51e5157e-bca5-4829-8b43-9a7199ca78c2</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c4ccbd5f-ca68-40fa-b21c-333ec916cede</processParameterId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>78</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>40c5c04e-feb7-4820-b66d-47012a9119ac</guid>
            <versionId>14e8f629-6af4-4357-80e1-bf61e5e90983</versionId>
        </processParameter>
        <processVariable name="requiredDocuments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.130284bf-e9df-4d06-9e30-5e1d7e283410</processVariableId>
            <description isNull="true" />
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f9234d90-4061-493b-b107-81421e01eefa</guid>
            <versionId>f3b814f0-a873-4267-b6ac-6708b0d3abb0</versionId>
        </processVariable>
        <processVariable name="documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e3d8f359-d676-4164-a9f3-4ed42dc39bcd</processVariableId>
            <description isNull="true" />
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.490f939c-3c6d-4ef7-9707-33b5b618877a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>94e1be32-842c-421d-b3a6-c5e187cc024e</guid>
            <versionId>832fafda-59d2-447d-a015-26033ff9c0da</versionId>
        </processVariable>
        <processVariable name="debug">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a1da7b12-216b-4b22-9aa9-0e7532d345e7</processVariableId>
            <description isNull="true" />
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b5ae7c2f-dbfb-4b5c-a0ea-dd3d0c81baca</guid>
            <versionId>379c10e4-bdd9-43a4-a076-5b91d51de663</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8280301c-20c8-48a4-848e-800dcfff4897</processVariableId>
            <description isNull="true" />
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5373a793-1dcc-412c-a99c-128924ac2f27</guid>
            <versionId>e2bf1617-5922-48c3-ad5a-a72dd0590a32</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>Is IDC Amendment</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.ab79ead9-f893-4d01-8ec6-c6bc5c24cb63</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61de</guid>
            <versionId>07dede84-60ef-45b1-a704-3808fa4cc3ee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.256ade93-28ad-4166-ad87-c8cf7f91c0c7</processItemPrePostId>
                <processItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>f28bf300-7b99-4d77-81f4-3b1a8aa006bb</guid>
                <versionId>6309e8af-c756-4b61-bb1c-8dabcf8a2ec8</versionId>
            </processPrePosts>
            <layoutData x="95" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.ab79ead9-f893-4d01-8ec6-c6bc5c24cb63</switchId>
                <guid>4f346b12-2842-4f53-af1f-7402fc63e2e0</guid>
                <versionId>216eced5-935d-4c58-b069-52c64fa6a3bc</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.d6d1772e-4fe5-48f1-b0f3-50f3aecb5b9a</switchConditionId>
                    <switchId>3013.ab79ead9-f893-4d01-8ec6-c6bc5c24cb63</switchId>
                    <seq>1</seq>
                    <endStateId>guid:d227ae9e677c32d5:-661aced:190e953c57b:2adc</endStateId>
                    <condition>tw.local.requestType	  ==	  "IDC Amendment"</condition>
                    <guid>10cebdfd-55d4-41e2-930c-51c798da6b9b</guid>
                    <versionId>8b375845-bfbb-4ade-aa1f-f7e914818a35</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.1b3dae76-406c-42f7-99a4-5eca7fce22bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:42738960b3d81f85:8740bc4:189d9444efc:-1dfa</guid>
            <versionId>17c972e3-c4dd-4e43-ab84-f0da014e9703</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.fd116918-f7b2-4f13-a717-7934d11629c0</processItemPrePostId>
                <processItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Get Required Documents -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Required Documents -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>b0b4750d-e2d8-40fa-aebd-81bb8b7c2785</guid>
                <versionId>bce05182-09d9-49e9-9954-92827926070e</versionId>
            </processPrePosts>
            <layoutData x="412" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.1b3dae76-406c-42f7-99a4-5eca7fce22bb</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>bbc2a9d3-8017-4745-baca-a356426bb466</guid>
                <versionId>daba8847-c72f-47f7-bdbb-99123f848f2e</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.383468ca-1074-425f-acf5-0a509102a8c5</parameterMappingId>
                    <processParameterId>2055.60108cef-f7d8-4f16-ab1f-616102bfb3d0</processParameterId>
                    <parameterMappingParentId>3007.1b3dae76-406c-42f7-99a4-5eca7fce22bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8bf8caa9-310b-447c-a002-320d7b439c15</guid>
                    <versionId>e977533a-9b24-457e-9eb0-eba6ea4396ec</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5885e4fb-2826-4f20-ac59-da2beded9cd8</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>Validate Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e012160d-038d-4d1b-b4bd-b988ff03b538</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e2</guid>
            <versionId>5cc0c75e-9a98-4f7f-93d8-7b549a3c762d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="190" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d9444efc:-1dfa</errorHandlerItem>
                <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e012160d-038d-4d1b-b4bd-b988ff03b538</subProcessId>
                <attachedProcessRef>/1.c04ab227-0184-4fcd-99e2-b165319d2807</attachedProcessRef>
                <guid>587e3a33-343d-48d3-a0af-a6d69be0c8a5</guid>
                <versionId>e0906d50-a905-4fb8-987b-5f2acaa9f71c</versionId>
                <parameterMapping name="requiredDocuments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3903b036-bcd1-468e-8599-65c965ca4988</parameterMappingId>
                    <processParameterId>2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6</processParameterId>
                    <parameterMappingParentId>3012.e012160d-038d-4d1b-b4bd-b988ff03b538</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requiredDocuments</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>f03a539a-f4c3-4f25-a5ab-7b8e5442cd3f</guid>
                    <versionId>2620a95b-e2bb-43a2-a0b1-667b536a45ef</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="documentSource">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b6ad28f2-0fd6-4f14-8d26-abc67db8bede</parameterMappingId>
                    <processParameterId>2055.1388ff03-e35d-4090-b3b9-0281f961e86e</processParameterId>
                    <parameterMappingParentId>3012.e012160d-038d-4d1b-b4bd-b988ff03b538</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.documentSource</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>88ec012a-3b0c-463b-a414-2fdf79cd8099</guid>
                    <versionId>50794e52-8e5e-4b7b-868a-b9504a45b3f7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0d76f4cb-f297-49ca-ba9f-e8c9e2de4274</parameterMappingId>
                    <processParameterId>2055.ca1eead8-e152-4ac6-8389-4431f391cc84</processParameterId>
                    <parameterMappingParentId>3012.e012160d-038d-4d1b-b4bd-b988ff03b538</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestType</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>75f639c6-996f-440d-b8df-bc03795270b8</guid>
                    <versionId>8f1459cc-04bb-49ae-b3ab-d8f296a4700e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.191c4fb9-e70a-4225-9c78-dc88865ab6d4</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.fc4ef784-fcfa-45ba-a284-72aa056a0126</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1</guid>
            <versionId>c1af63a5-329b-4520-9a68-c30d4c7b3ed1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.fc4ef784-fcfa-45ba-a284-72aa056a0126</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0076865b-2dd1-45ae-84c7-1b6f80dd04ec</guid>
                <versionId>4770c3f8-344d-4037-b384-dd5266e049c1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9ec36c88-4983-4a13-807c-18837f6fb353</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>Set Missing Documents</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9e86754e-aa7c-4013-a6c1-7151387d0d29</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61df</guid>
            <versionId>e6d4cdee-c7ca-4dd6-86cc-2b32f04ede94</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="457" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d9444efc:-1dfa</errorHandlerItem>
                <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9e86754e-aa7c-4013-a6c1-7151387d0d29</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.errorMessage = "The following documents are requierd:\n";&#xD;
tw.local.errorExist = false;&#xD;
var allDocumentTypes= [];&#xD;
var haveIDC = false;&#xD;
for (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) {&#xD;
	tw.local.debug = "documentsTypesSelected";&#xD;
	allDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;&#xD;
}&#xD;
&#xD;
for (var i=0; i&lt;tw.local.documents.listLength; i++) {&#xD;
	tw.local.debug = "documents";&#xD;
	if (tw.local.documents[i].objectTypeId=="IDCDocument") {&#xD;
		var haveIDC = true;&#xD;
	}&#xD;
}&#xD;
var j=0;&#xD;
if (haveIDC) {&#xD;
	for (var i=0; i&lt;tw.local.requiredDocuments.listLength; i++) {&#xD;
		tw.local.debug = "requiredDocuments";&#xD;
		if (allDocumentTypes.indexOf(tw.local.requiredDocuments[i]) == -1) {&#xD;
			tw.local.errorMessage+=tw.local.requiredDocuments[i]+"\n";&#xD;
			tw.local.errorExist=true;&#xD;
		}	&#xD;
	}&#xD;
}else{&#xD;
	tw.local.errorMessage = "must upload document of type IDC Document";&#xD;
	tw.local.errorExist=true;&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>adceb061-d1e4-4a61-a3d8-3fed6b51a841</guid>
                <versionId>2136b1ab-3809-4061-a0a9-2bb8e207f027</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fea09597-38f0-414f-ab48-f30d6e9f04d6</processItemId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <name>Get Documents In Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.abf11249-515f-46cd-a42b-b09bbf0f4220</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e0</guid>
            <versionId>fdeec1ad-051a-484f-b68a-085ee894838b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="310" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:42738960b3d81f85:8740bc4:189d9444efc:-1dfa</errorHandlerItem>
                <errorHandlerItemId>2025.31219688-92d8-4699-8285-5035e5471d3d</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.abf11249-515f-46cd-a42b-b09bbf0f4220</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;documentId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description /&gt;&#xD;
      &lt;defaultValue /&gt;&#xD;
      &lt;isArray /&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;targetFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description /&gt;&#xD;
      &lt;defaultValue /&gt;&#xD;
      &lt;isArray /&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;sourceFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description /&gt;&#xD;
      &lt;defaultValue /&gt;&#xD;
      &lt;isArray /&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description /&gt;&#xD;
      &lt;defaultValue /&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FileNet&lt;/argumentVariable&gt;&#xD;
      &lt;isArray /&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters /&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue /&gt;&#xD;
  &lt;operationType&gt;DOC_OP_MOVE_DOCUMENT&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>9d809eee-d048-4287-a413-13ff27d0b5cb</guid>
                <versionId>05dbaa08-4abb-4f13-b8b3-e8980faa21e4</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Get Required Documents" id="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="documentSource" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">"Correspondent"</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.aae56053-3bba-40b1-abc9-6a441a93f307">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">"Advance Payment"</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">"{00B3EA88-0000-C735-A621-11E22CFFF8BE}"</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="documentsTypesSelected" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.Attachment();
autoObject[0] = new tw.object.Attachment();
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749" />
                        <ns16:dataOutput name="errorExist" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6" />
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.74cb9e94-45c3-4ba9-8862-d45286d425b6" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</ns16:dataInputRefs>
                            <ns16:dataInputRefs>2055.aae56053-3bba-40b1-abc9-6a441a93f307</ns16:dataInputRefs>
                            <ns16:dataInputRefs>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</ns16:dataInputRefs>
                            <ns16:dataInputRefs>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="8db68be9-5c59-4cb2-b98b-1b4ef2004283">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="72a9853a-3081-43fc-a8bf-65cb77d9ac85" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>cace737e-f023-4235-a294-eedb70d8852a</ns16:flowNodeRef>
                            <ns16:flowNodeRef>191c4fb9-e70a-4225-9c78-dc88865ab6d4</ns16:flowNodeRef>
                            <ns16:flowNodeRef>5885e4fb-2826-4f20-ac59-da2beded9cd8</ns16:flowNodeRef>
                            <ns16:flowNodeRef>0875087d-623f-44f0-96fd-bc591d7e87d6</ns16:flowNodeRef>
                            <ns16:flowNodeRef>fea09597-38f0-414f-ab48-f30d6e9f04d6</ns16:flowNodeRef>
                            <ns16:flowNodeRef>9ec36c88-4983-4a13-807c-18837f6fb353</ns16:flowNodeRef>
                            <ns16:flowNodeRef>0c9746f9-41ef-4e3c-87ba-5823dda3345e</ns16:flowNodeRef>
                            <ns16:flowNodeRef>cba2ad12-fd37-4ea3-85f8-c5c60711005f</ns16:flowNodeRef>
                            <ns16:flowNodeRef>7734cbef-ad36-4b37-84a2-d79df2d06296</ns16:flowNodeRef>
                            <ns16:flowNodeRef>31219688-92d8-4699-8285-5035e5471d3d</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="cace737e-f023-4235-a294-eedb70d8852a">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="191c4fb9-e70a-4225-9c78-dc88865ab6d4">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e1</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>bcbbc3cf-20fc-4754-9bda-a77f59d53f92</ns16:incoming>
                        <ns16:incoming>60319491-91ea-4de2-bb72-28619b23dd1f</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="cace737e-f023-4235-a294-eedb70d8852a" targetRef="0875087d-623f-44f0-96fd-bc591d7e87d6" name="To Is IDC Amendment" id="2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:callActivity calledElement="1.c04ab227-0184-4fcd-99e2-b165319d2807" name="Validate Required Documents" id="5885e4fb-2826-4f20-ac59-da2beded9cd8">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="190" y="57" width="95" height="70" />
                            <ns4:activityType>CalledProcess</ns4:activityType>
                        </ns16:extensionElements>
                        <ns16:incoming>3ee3e353-d98d-48f0-8781-fb89790ccb0d</ns16:incoming>
                        <ns16:outgoing>a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.ca1eead8-e152-4ac6-8389-4431f391cc84</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestType</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.1388ff03-e35d-4090-b3b9-0281f961e86e</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.documentSource</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataOutputAssociation>
                            <ns16:sourceRef>2055.2bf6de84-f26a-42f8-87bc-3edd91aba2c6</ns16:sourceRef>
                            <ns16:assignment>
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requiredDocuments</ns16:to>
                            </ns16:assignment>
                        </ns16:dataOutputAssociation>
                    </ns16:callActivity>
                    <ns16:sequenceFlow sourceRef="5885e4fb-2826-4f20-ac59-da2beded9cd8" targetRef="fea09597-38f0-414f-ab48-f30d6e9f04d6" name="To Get Documents In Folder" id="a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9</ns3:endStateId>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="requiredDocuments" id="2056.130284bf-e9df-4d06-9e30-5e1d7e283410" />
                    <ns16:exclusiveGateway default="3ee3e353-d98d-48f0-8781-fb89790ccb0d" name="Is IDC Amendment" id="0875087d-623f-44f0-96fd-bc591d7e87d6">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="95" y="76" width="32" height="32" />
                            <ns3:preAssignmentScript />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.7a47e857-3b19-4bb3-a00a-d36f922cfd27</ns16:incoming>
                        <ns16:outgoing>3ee3e353-d98d-48f0-8781-fb89790ccb0d</ns16:outgoing>
                        <ns16:outgoing>bcbbc3cf-20fc-4754-9bda-a77f59d53f92</ns16:outgoing>
                    </ns16:exclusiveGateway>
                    <ns16:sequenceFlow sourceRef="0875087d-623f-44f0-96fd-bc591d7e87d6" targetRef="5885e4fb-2826-4f20-ac59-da2beded9cd8" name="To Validate Required Documents" id="3ee3e353-d98d-48f0-8781-fb89790ccb0d">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="0875087d-623f-44f0-96fd-bc591d7e87d6" targetRef="191c4fb9-e70a-4225-9c78-dc88865ab6d4" name="To End" id="bcbbc3cf-20fc-4754-9bda-a77f59d53f92">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                <ns13:showLabel>true</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:customBendPoint x="396" y="16" />
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  "IDC Amendment"</ns16:conditionExpression>
                    </ns16:sequenceFlow>
                    <ns4:contentTask serverName="FileNet" operationRef="DOC_OP_MOVE_DOCUMENT" name="Get Documents In Folder" id="fea09597-38f0-414f-ab48-f30d6e9f04d6">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="310" y="57" width="95" height="70" />
                            <ns4:activityType>ContentTask</ns4:activityType>
                        </ns16:extensionElements>
                        <ns16:incoming>a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5</ns16:incoming>
                        <ns16:outgoing>453bda9b-6980-427d-8009-03c479397846</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>FOLDER_ID</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FileNet</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataOutputAssociation>
                            <ns16:sourceRef>DOCUMENTS</ns16:sourceRef>
                            <ns16:assignment>
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a">tw.local.documents</ns16:to>
                            </ns16:assignment>
                        </ns16:dataOutputAssociation>
                    </ns4:contentTask>
                    <ns16:sequenceFlow sourceRef="fea09597-38f0-414f-ab48-f30d6e9f04d6" targetRef="9ec36c88-4983-4a13-807c-18837f6fb353" name="To Set Missing Documents" id="453bda9b-6980-427d-8009-03c479397846">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a" isCollection="true" name="documents" id="2056.e3d8f359-d676-4164-a9f3-4ed42dc39bcd" />
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Missing Documents" id="9ec36c88-4983-4a13-807c-18837f6fb353">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="457" y="57" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>453bda9b-6980-427d-8009-03c479397846</ns16:incoming>
                        <ns16:outgoing>60319491-91ea-4de2-bb72-28619b23dd1f</ns16:outgoing>
                        <ns16:script>tw.local.errorMessage = "The following documents are requierd:\n";&#xD;
tw.local.errorExist = false;&#xD;
var allDocumentTypes= [];&#xD;
var haveIDC = false;&#xD;
for (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) {&#xD;
	tw.local.debug = "documentsTypesSelected";&#xD;
	allDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;&#xD;
}&#xD;
&#xD;
for (var i=0; i&lt;tw.local.documents.listLength; i++) {&#xD;
	tw.local.debug = "documents";&#xD;
	if (tw.local.documents[i].objectTypeId=="IDCDocument") {&#xD;
		var haveIDC = true;&#xD;
	}&#xD;
}&#xD;
var j=0;&#xD;
if (haveIDC) {&#xD;
	for (var i=0; i&lt;tw.local.requiredDocuments.listLength; i++) {&#xD;
		tw.local.debug = "requiredDocuments";&#xD;
		if (allDocumentTypes.indexOf(tw.local.requiredDocuments[i]) == -1) {&#xD;
			tw.local.errorMessage+=tw.local.requiredDocuments[i]+"\n";&#xD;
			tw.local.errorExist=true;&#xD;
		}	&#xD;
	}&#xD;
}else{&#xD;
	tw.local.errorMessage = "must upload document of type IDC Document";&#xD;
	tw.local.errorExist=true;&#xD;
}&#xD;
</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="9ec36c88-4983-4a13-807c-18837f6fb353" targetRef="191c4fb9-e70a-4225-9c78-dc88865ab6d4" name="To Validate Required Documents" id="60319491-91ea-4de2-bb72-28619b23dd1f">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="debug" id="2056.a1da7b12-216b-4b22-9aa9-0e7532d345e7" />
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="5885e4fb-2826-4f20-ac59-da2beded9cd8" parallelMultiple="false" name="Error" id="0c9746f9-41ef-4e3c-87ba-5823dda3345e">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="225" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>220f5d82-0eae-448a-8929-012b7d644595</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a15aeb56-c45d-4d71-8603-7246e07d78c4" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="12b8208d-bad3-40d0-8419-881875db64d6" eventImplId="5d8ebb18-3ba3-4c27-8345-948a53634497">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="fea09597-38f0-414f-ab48-f30d6e9f04d6" parallelMultiple="false" name="Error1" id="cba2ad12-fd37-4ea3-85f8-c5c60711005f">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="345" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>e9d4da5b-f888-42d8-880a-af32575aca4f</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="1ad1d590-f8d9-4dca-8819-255470d66580" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="979981e1-6b84-4438-8028-bd384be01a80" eventImplId="332bc231-23bd-4cb6-85c6-4e6a676d92c2">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9ec36c88-4983-4a13-807c-18837f6fb353" parallelMultiple="false" name="Error2" id="7734cbef-ad36-4b37-84a2-d79df2d06296">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="492" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>9c720847-7372-4cdd-8829-130eb1f88555</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="45899147-d106-44cc-810f-00aba8addb71" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="9d2021dc-4e15-4a4e-8e2e-89dad7c1d74f" eventImplId="c634a25f-d714-4b8d-8155-c3a041c5e0af">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:endEvent name="End Event" id="31219688-92d8-4699-8285-5035e5471d3d">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="412" y="182" width="24" height="24" />
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Get Required Documents -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Get Required Documents -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                        </ns16:extensionElements>
                        <ns16:incoming>9c720847-7372-4cdd-8829-130eb1f88555</ns16:incoming>
                        <ns16:incoming>e9d4da5b-f888-42d8-880a-af32575aca4f</ns16:incoming>
                        <ns16:incoming>220f5d82-0eae-448a-8929-012b7d644595</ns16:incoming>
                        <ns16:dataInputAssociation>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:errorEventDefinition id="eb95e855-42ba-40a2-8810-9a027c0db92d" eventImplId="4b2b813e-cb8e-49f9-8a44-0a35ef5f817d">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                    <ns4:errorCode />
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="0c9746f9-41ef-4e3c-87ba-5823dda3345e" targetRef="31219688-92d8-4699-8285-5035e5471d3d" name="To End Event" id="220f5d82-0eae-448a-8929-012b7d644595">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="cba2ad12-fd37-4ea3-85f8-c5c60711005f" targetRef="31219688-92d8-4699-8285-5035e5471d3d" name="To End Event" id="e9d4da5b-f888-42d8-880a-af32575aca4f">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="7734cbef-ad36-4b37-84a2-d79df2d06296" targetRef="31219688-92d8-4699-8285-5035e5471d3d" name="To End Event" id="9c720847-7372-4cdd-8829-130eb1f88555">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.8280301c-20c8-48a4-848e-800dcfff4897" />
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Missing Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.453bda9b-6980-427d-8009-03c479397846</processLinkId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fea09597-38f0-414f-ab48-f30d6e9f04d6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9ec36c88-4983-4a13-807c-18837f6fb353</toProcessItemId>
            <guid>efd3eba8-0eb4-4692-a412-f4f6c809d524</guid>
            <versionId>12cd1fe3-f94d-4450-85da-5b55e4c8bf48</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fea09597-38f0-414f-ab48-f30d6e9f04d6</fromProcessItemId>
            <toProcessItemId>2025.9ec36c88-4983-4a13-807c-18837f6fb353</toProcessItemId>
        </link>
        <link name="To Get Documents In Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a786ffd5-7f1a-4fac-a9ac-93c29b11fbe5</processLinkId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5885e4fb-2826-4f20-ac59-da2beded9cd8</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61d9</endStateId>
            <toProcessItemId>2025.fea09597-38f0-414f-ab48-f30d6e9f04d6</toProcessItemId>
            <guid>f73abc50-461e-4617-bf3e-c3485ee8f71a</guid>
            <versionId>1e7b52a4-10a9-4fbb-842a-17c60efd7401</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5885e4fb-2826-4f20-ac59-da2beded9cd8</fromProcessItemId>
            <toProcessItemId>2025.fea09597-38f0-414f-ab48-f30d6e9f04d6</toProcessItemId>
        </link>
        <link name="To Validate Required Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3ee3e353-d98d-48f0-8781-fb89790ccb0d</processLinkId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.5885e4fb-2826-4f20-ac59-da2beded9cd8</toProcessItemId>
            <guid>e1a64e7a-bb79-4999-8096-90141b76b1d4</guid>
            <versionId>46ab7372-5814-4e8b-ac2f-54faa8663c4d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</fromProcessItemId>
            <toProcessItemId>2025.5885e4fb-2826-4f20-ac59-da2beded9cd8</toProcessItemId>
        </link>
        <link name="To Validate Required Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.60319491-91ea-4de2-bb72-28619b23dd1f</processLinkId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9ec36c88-4983-4a13-807c-18837f6fb353</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.191c4fb9-e70a-4225-9c78-dc88865ab6d4</toProcessItemId>
            <guid>0c8153f5-ac11-47d2-a68d-233e45901977</guid>
            <versionId>c0818979-70c3-4e99-9dc8-83512c2a1cf6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9ec36c88-4983-4a13-807c-18837f6fb353</fromProcessItemId>
            <toProcessItemId>2025.191c4fb9-e70a-4225-9c78-dc88865ab6d4</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bcbbc3cf-20fc-4754-9bda-a77f59d53f92</processLinkId>
            <processId>1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</fromProcessItemId>
            <endStateId>guid:d227ae9e677c32d5:-661aced:190e953c57b:2adc</endStateId>
            <toProcessItemId>2025.191c4fb9-e70a-4225-9c78-dc88865ab6d4</toProcessItemId>
            <guid>b79450e2-43a9-40a5-b899-61abd0b564ea</guid>
            <versionId>cfa38f43-dbe9-4c7c-bd15-141e289bd3c5</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="396" y="16" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.0875087d-623f-44f0-96fd-bc591d7e87d6</fromProcessItemId>
            <toProcessItemId>2025.191c4fb9-e70a-4225-9c78-dc88865ab6d4</toProcessItemId>
        </link>
    </process>
</teamworks>

