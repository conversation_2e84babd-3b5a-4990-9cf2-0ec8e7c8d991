<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a195e5d7-a7f4-4419-84bc-2bcff38a6561" name="Authorize ICAP">
        <lastModified>1691064530985</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.1492d3c1-933c-4640-8c1e-dbb73e3189e2</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:3444</guid>
        <versionId>65a7057c-2e92-4adb-b5f0-39e53b996520</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:d7a662a99ec79f4c:-60dc945f:189bab22bee:36e5" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7c169a57-5908-4fdc-829f-742f4272c253"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"809b1561-63ca-4fda-8f11-e118fc8617e0"},{"incoming":["def53f79-6a5d-4d15-87be-96e6b75b2635"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:3446"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"50bccb86-3c5b-4320-8cb5-91e0cb4392d7"},{"targetRef":"1492d3c1-933c-4640-8c1e-dbb73e3189e2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.7c169a57-5908-4fdc-829f-742f4272c253","sourceRef":"809b1561-63ca-4fda-8f11-e118fc8617e0"},{"startQuantity":1,"outgoing":["def53f79-6a5d-4d15-87be-96e6b75b2635"],"incoming":["2027.7c169a57-5908-4fdc-829f-742f4272c253"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1492d3c1-933c-4640-8c1e-dbb73e3189e2","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.data &gt; 0) {\r\n\ttw.local.results = true;\r\n}else{\r\n\ttw.local.results = false;\r\n}"]}},{"targetRef":"50bccb86-3c5b-4320-8cb5-91e0cb4392d7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"def53f79-6a5d-4d15-87be-96e6b75b2635","sourceRef":"1492d3c1-933c-4640-8c1e-dbb73e3189e2"}],"laneSet":[{"id":"d00c7ab8-81ce-4608-8686-368eac307a76","lane":[{"flowNodeRef":["809b1561-63ca-4fda-8f11-e118fc8617e0","50bccb86-3c5b-4320-8cb5-91e0cb4392d7","1492d3c1-933c-4640-8c1e-dbb73e3189e2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"01099f77-e113-4898-8e1a-4dbc0e3819a2","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Authorize ICAP","declaredType":"process","id":"1.a195e5d7-a7f4-4419-84bc-2bcff38a6561","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.81b6b3a7-fd10-44e6-81e0-897afaa5838c"}],"inputSet":[{"dataInputRefs":["2055.3e5e6cd1-8bbc-425f-880d-a7db72a1bb42"]}],"outputSet":[{"dataOutputRefs":["2055.81b6b3a7-fd10-44e6-81e0-897afaa5838c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"2"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.3e5e6cd1-8bbc-425f-880d-a7db72a1bb42"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3e5e6cd1-8bbc-425f-880d-a7db72a1bb42</processParameterId>
            <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>2</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d30b149b-2e0f-4e11-8046-09e4de1ec4bf</guid>
            <versionId>89cdb773-7c4c-49ca-ac81-c758beae273a</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.81b6b3a7-fd10-44e6-81e0-897afaa5838c</processParameterId>
            <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0e05437c-e5bd-4497-adb1-7ca53970bcce</guid>
            <versionId>92ac90a6-79fe-4747-9c44-80f4b4f646a0</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.50bccb86-3c5b-4320-8cb5-91e0cb4392d7</processItemId>
            <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c20e7b28-38bc-4c22-b403-7df6329a09ce</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:3446</guid>
            <versionId>65d92bb0-893f-4bb2-bbcc-8ecfde042b9b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c20e7b28-38bc-4c22-b403-7df6329a09ce</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>f31ec14f-a88b-4720-b272-caef304e582e</guid>
                <versionId>5e338bcf-7e63-4d31-a5e4-af98f694dcfd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1492d3c1-933c-4640-8c1e-dbb73e3189e2</processItemId>
            <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.86c054ae-628e-4993-951b-9fc786b8389c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:3510</guid>
            <versionId>b3c98cdc-c5b0-403d-b747-b55e02160b54</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.86c054ae-628e-4993-951b-9fc786b8389c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.data &gt; 0) {&#xD;
	tw.local.results = true;&#xD;
}else{&#xD;
	tw.local.results = false;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>35e2d357-2d2f-4a35-971e-981c66499fcb</guid>
                <versionId>499eb745-d1e8-4837-b21d-1035e87a2e43</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.1492d3c1-933c-4640-8c1e-dbb73e3189e2</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Authorize ICAP" id="1.a195e5d7-a7f4-4419-84bc-2bcff38a6561" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.3e5e6cd1-8bbc-425f-880d-a7db72a1bb42">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">2</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.81b6b3a7-fd10-44e6-81e0-897afaa5838c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.3e5e6cd1-8bbc-425f-880d-a7db72a1bb42</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.81b6b3a7-fd10-44e6-81e0-897afaa5838c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="d00c7ab8-81ce-4608-8686-368eac307a76">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="01099f77-e113-4898-8e1a-4dbc0e3819a2" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>809b1561-63ca-4fda-8f11-e118fc8617e0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>50bccb86-3c5b-4320-8cb5-91e0cb4392d7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1492d3c1-933c-4640-8c1e-dbb73e3189e2</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="809b1561-63ca-4fda-8f11-e118fc8617e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.7c169a57-5908-4fdc-829f-742f4272c253</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="50bccb86-3c5b-4320-8cb5-91e0cb4392d7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:3446</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>def53f79-6a5d-4d15-87be-96e6b75b2635</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="809b1561-63ca-4fda-8f11-e118fc8617e0" targetRef="1492d3c1-933c-4640-8c1e-dbb73e3189e2" name="To Script Task" id="2027.7c169a57-5908-4fdc-829f-742f4272c253">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="1492d3c1-933c-4640-8c1e-dbb73e3189e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.7c169a57-5908-4fdc-829f-742f4272c253</ns16:incoming>
                        
                        
                        <ns16:outgoing>def53f79-6a5d-4d15-87be-96e6b75b2635</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.data &gt; 0) {&#xD;
	tw.local.results = true;&#xD;
}else{&#xD;
	tw.local.results = false;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1492d3c1-933c-4640-8c1e-dbb73e3189e2" targetRef="50bccb86-3c5b-4320-8cb5-91e0cb4392d7" name="To End" id="def53f79-6a5d-4d15-87be-96e6b75b2635">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.def53f79-6a5d-4d15-87be-96e6b75b2635</processLinkId>
            <processId>1.a195e5d7-a7f4-4419-84bc-2bcff38a6561</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1492d3c1-933c-4640-8c1e-dbb73e3189e2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.50bccb86-3c5b-4320-8cb5-91e0cb4392d7</toProcessItemId>
            <guid>8893aad6-2f6d-4831-8951-381ef480daa3</guid>
            <versionId>e2358a42-ce6e-4ba6-8145-44056445d7e7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1492d3c1-933c-4640-8c1e-dbb73e3189e2</fromProcessItemId>
            <toProcessItemId>2025.50bccb86-3c5b-4320-8cb5-91e0cb4392d7</toProcessItemId>
        </link>
    </process>
</teamworks>

