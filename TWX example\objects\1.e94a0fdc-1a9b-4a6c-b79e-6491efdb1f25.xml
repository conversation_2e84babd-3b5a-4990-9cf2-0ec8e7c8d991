<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25" name="Create IDC Customs Release Request">
        <lastModified>1688661261151</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.76f0803f-99ad-4a19-b148-0aa7ef07c94a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>60dcfc06-6e9f-4685-918b-af5b1702b0d4</guid>
        <versionId>203cbf5e-3e8a-44a9-bc4c-1d2fe5420e28</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcCustomsReleaseRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.787cd339-1200-4284-9c39-59ac7434b298</processParameterId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>19f22f61-2079-48f8-a0c3-4e43e71dc988</guid>
            <versionId>fac5adbc-4c3f-40c4-a0a9-f2def3db6d88</versionId>
        </processParameter>
        <processParameter name="idcCustomsReleaseRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.aaa930c1-4bcd-4305-865c-9ac6b03399d1</processParameterId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>36750a3d-8e58-463c-803e-92adb8c60c91</guid>
            <versionId>fb6dbd8f-b020-4cb6-88b9-704dac1d1db2</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.76f0803f-99ad-4a19-b148-0aa7ef07c94a</processItemId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.a60edf5a-3a9a-41e5-b851-d419f8e40de7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-621c</guid>
            <versionId>0f7c31d2-5f29-4573-a0a3-a52eaa205d6a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.773eb8c1-d3eb-4b59-bd8f-bf80d27ae1e8</processItemId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ace487d2-d150-4f11-81e1-a5e7f87e4c84</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-621d</guid>
            <versionId>e8c1a279-4aff-445f-9cd5-1a4a8aff9832</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ace487d2-d150-4f11-81e1-a5e7f87e4c84</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>cb5bff3e-45f2-473a-babf-8a76b7a8fd2e</guid>
                <versionId>67237b87-ec3d-4349-9897-8a20be3fa0e5</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.d136d773-36d0-4ca5-9b57-b691d4fd3b11</epvProcessLinkId>
            <epvId>/21.2dce7af7-b766-40ab-acc0-0e77449191aa</epvId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <guid>fc50de3e-78e2-4110-a381-1b64da8d09e8</guid>
            <versionId>12e25c78-3928-4a8a-8485-6fa9b70a720c</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.76f0803f-99ad-4a19-b148-0aa7ef07c94a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.8a1a93ec-ad29-4d28-b18f-43c25ae90b83" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Create IDC Customs Release Request" id="1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="5dae69b7-6aec-4501-9fd2-3d7f8f4b4ecd">
                            
                            
                            <ns16:startEvent name="Start" id="4579ff33-8d5f-409b-9539-e51565aac6de">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.5c7d2d79-7760-4a29-a07d-f44e048e8879</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="3ab46ab8-e3ef-40cd-a9e0-9423728d5960">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="700" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8cf4077e-c8b9-4fac-bca1-06cca3177890</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.aed9ac81-f69d-4d5a-9f74-4dcf45209e63</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="4579ff33-8d5f-409b-9539-e51565aac6de" targetRef="2025.a0efd1d4-6613-4c3b-879d-3b833fafe228" name="Start To Coach" id="2027.5c7d2d79-7760-4a29-a07d-f44e048e8879">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.8cf4077e-c8b9-4fac-bca1-06cca3177890" name="Set Status" id="2025.d5dd4ed5-4379-4939-a48e-256d74d2a014">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="552" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.8cf4077e-c8b9-4fac-bca1-06cca3177890</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d5dd4ed5-4379-4939-a48e-256d74d2a014" targetRef="3ab46ab8-e3ef-40cd-a9e0-9423728d5960" name="OK To End" id="2027.8cf4077e-c8b9-4fac-bca1-06cca3177890">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.6968302c-de59-4a10-9118-e154965ebc24" name="Initialization Script" id="2025.a0efd1d4-6613-4c3b-879d-3b833fafe228">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="163" y="164" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5c7d2d79-7760-4a29-a07d-f44e048e8879</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.6968302c-de59-4a10-9118-e154965ebc24</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.idcCustomsReleaseRequest == null) {&#xD;
	tw.local.idcCustomsReleaseRequest = {};&#xD;
	tw.local.idcCustomsReleaseRequest.subStatus = "";&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.6c7aad1b-76f8-4ebc-b394-ff8c582887cf">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="357" y="70" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.dca83472-4b0f-490b-bddc-78f3aac55c83</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b45ab04a-3a0c-440e-a34a-c413e48c7169</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.dca83472-4b0f-490b-bddc-78f3aac55c83</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns3:formTask name="Create IDC Customs Release Request" id="2025.4bc05c0f-78c8-457d-ba5e-57718e9eb56e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="348" y="164" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6968302c-de59-4a10-9118-e154965ebc24</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.dca83472-4b0f-490b-bddc-78f3aac55c83</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.aed9ac81-f69d-4d5a-9f74-4dcf45209e63</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.b45ab04a-3a0c-440e-a34a-c413e48c7169</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>bed169fe-e219-4d91-84e4-6b30c4a04dc4</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c0290f80-5b38-4cbf-856c-14bc06bab531</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>440db70f-b7ba-4dea-89ef-5bb44f48bba2</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9c586bf5-be46-40da-8c28-ee7cff8fdabb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3b77eb3c-bd67-4384-86f5-93e12f133947</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a0efd1d4-6613-4c3b-879d-3b833fafe228" targetRef="2025.4bc05c0f-78c8-457d-ba5e-57718e9eb56e" name="To Create IDC Customs Release Request" id="2027.6968302c-de59-4a10-9118-e154965ebc24">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4bc05c0f-78c8-457d-ba5e-57718e9eb56e" targetRef="3ab46ab8-e3ef-40cd-a9e0-9423728d5960" name="To End" id="2027.aed9ac81-f69d-4d5a-9f74-4dcf45209e63">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="e5d6538e-c1b4-4863-afd6-f97a0e87c880">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4bc05c0f-78c8-457d-ba5e-57718e9eb56e" targetRef="2025.6c7aad1b-76f8-4ebc-b394-ff8c582887cf" name="To Stay on page" id="2027.b45ab04a-3a0c-440e-a34a-c413e48c7169">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="5056ddd5-d74b-4c68-846a-36f8f5a05bd5">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6c7aad1b-76f8-4ebc-b394-ff8c582887cf" targetRef="2025.4bc05c0f-78c8-457d-ba5e-57718e9eb56e" name="To Create IDC Customs Release Request" id="2027.dca83472-4b0f-490b-bddc-78f3aac55c83">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="e0b1b294-dbc1-4c2a-9d4c-ecc16d267491">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.2dce7af7-b766-40ab-acc0-0e77449191aa" epvProcessLinkId="4b779aaa-2e59-4f54-858a-7141408d3f56" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcCustomsReleaseRequest" itemSubjectRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1" isCollection="false" id="2055.787cd339-1200-4284-9c39-59ac7434b298" />
                        
                        
                        <ns16:dataOutput name="idcCustomsReleaseRequest" itemSubjectRef="itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1" isCollection="false" id="2055.aaa930c1-4bcd-4305-865c-9ac6b03399d1" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.52c53276-89f8-46b9-9c52-410f234df2fc</processLinkId>
            <processId>1.e94a0fdc-1a9b-4a6c-b79e-6491efdb1f25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.76f0803f-99ad-4a19-b148-0aa7ef07c94a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.773eb8c1-d3eb-4b59-bd8f-bf80d27ae1e8</toProcessItemId>
            <guid>4970f307-66cc-455f-8da6-a41575bb52fa</guid>
            <versionId>e6e18f03-58c4-4bd4-8481-59435a4c2ff5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.76f0803f-99ad-4a19-b148-0aa7ef07c94a</fromProcessItemId>
            <toProcessItemId>2025.773eb8c1-d3eb-4b59-bd8f-bf80d27ae1e8</toProcessItemId>
        </link>
    </process>
</teamworks>

