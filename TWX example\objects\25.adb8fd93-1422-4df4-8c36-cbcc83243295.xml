<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.adb8fd93-1422-4df4-8c36-cbcc83243295" name="Test Nested Process">
        <lastModified>1732629943087</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <bpdId>25.adb8fd93-1422-4df4-8c36-cbcc83243295</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="4ae44902-a05d-4b15-b34c-edc77ae0afef" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="Test Nested Process" id="25.adb8fd93-1422-4df4-8c36-cbcc83243295" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot;Test Nested Process:&amp;quot; + tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="true" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1732629390899"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="33f67998-9913-4607-82f6-d2b64ba0e03b" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="_bf828780-458b-49e9-b8b3-17058d20b4ba" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="_14286da8-9411-4b47-b656-f79e3f574a01" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="bf5bd5aa-fce8-4fa5-81e3-974b136805a0"&gt;&lt;ns15:lane name="Team" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="1d39daf0-ff15-4abb-8854-89678c24c3c1" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;020ef269-93e1-4ec9-a845-25808fd32841&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5906e50e-a329-4193-9baa-b5cf3143727c&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Team1" partitionElementRef="24.ca7e0934-a001-4a68-8626-6a038afdc7c6" id="71a5549d-73b2-4999-8082-b4850d2dbc7c" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;fb739613-cf31-46c8-8bcc-f09ff43c4081&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="46340585-98b9-4b9c-8472-c99017078640" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="020ef269-93e1-4ec9-a845-25808fd32841"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;0d5243e2-29d5-488d-817d-24be3454e1a4&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;0d5243e2-29d5-488d-817d-24be3454e1a4&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="End" id="5906e50e-a329-4193-9baa-b5cf3143727c"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;88c8999f-bb5f-4921-8753-1289c3ce561f&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:callActivity calledElement="1.b7869400-09ed-4f15-972f-39c44a324089" default="bbf36031-28cc-48d2-bcae-b27e5fc0e3e3" name="Inline User Task" id="c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="296" y="57" width="95" height="70" /&gt;&lt;ns4:activityType&gt;InlineUserTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false" transactionalBehavior="NotSet"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;0d5243e2-29d5-488d-817d-24be3454e1a4&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;bbf36031-28cc-48d2-bcae-b27e5fc0e3e3&lt;/ns15:outgoing&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="020ef269-93e1-4ec9-a845-25808fd32841" targetRef="c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae" name="To Inline User Task" id="0d5243e2-29d5-488d-817d-24be3454e1a4"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae" targetRef="fb739613-cf31-46c8-8bcc-f09ff43c4081" name="To End" id="bbf36031-28cc-48d2-bcae-b27e5fc0e3e3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" /&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="25.adb8fd93-1422-4df4-8c36-cbcc83243295" default="88c8999f-bb5f-4921-8753-1289c3ce561f" name="Linked Process" id="fb739613-cf31-46c8-8bcc-f09ff43c4081"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="463" y="50" width="95" height="70" /&gt;&lt;ns4:activityType&gt;CalledProcess&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;bbf36031-28cc-48d2-bcae-b27e5fc0e3e3&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;88c8999f-bb5f-4921-8753-1289c3ce561f&lt;/ns15:outgoing&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="fb739613-cf31-46c8-8bcc-f09ff43c4081" targetRef="5906e50e-a329-4193-9baa-b5cf3143727c" name="To End" id="88c8999f-bb5f-4921-8753-1289c3ce561f"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef" /&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef" /&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="Test Nested ProcessInterface" id="_c8648c79-65f9-4838-8b83-dcd8923b9935" /&gt;&lt;ns15:globalUserTask name="1.b7869400-09ed-4f15-972f-39c44a324089" id="1.b7869400-09ed-4f15-972f-39c44a324089"&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:inputSet id="8909727b-d49b-4e9c-a1f2-0573d81ffac7" /&gt;&lt;ns15:outputSet id="0452a7b0-7116-4af7-9f56-5b7f5bd45f02" /&gt;&lt;/ns15:ioSpecification&gt;&lt;/ns15:globalUserTask&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary>&lt;dependencySummary id="bpdid:2340c4cc899e0880:-********:***********:381e"&gt;&#xD;
  &lt;artifactReference id="bpdid:2340c4cc899e0880:-********:***********:381f"&gt;&#xD;
    &lt;refId&gt;/1.b7869400-09ed-4f15-972f-39c44a324089&lt;/refId&gt;&#xD;
    &lt;refType&gt;1&lt;/refType&gt;&#xD;
    &lt;nameValuePair id="bpdid:2340c4cc899e0880:-********:***********:3820"&gt;&#xD;
      &lt;name&gt;mimeType&lt;/name&gt;&#xD;
      &lt;value&gt;inlineUserTask&lt;/value&gt;&#xD;
    &lt;/nameValuePair&gt;&#xD;
  &lt;/artifactReference&gt;&#xD;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["0d5243e2-29d5-488d-817d-24be3454e1a4"],"isInterrupting":true,"extensionElements":{"default":["0d5243e2-29d5-488d-817d-24be3454e1a4"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"020ef269-93e1-4ec9-a845-25808fd32841"},{"incoming":["88c8999f-bb5f-4921-8753-1289c3ce561f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End","declaredType":"endEvent","id":"5906e50e-a329-4193-9baa-b5cf3143727c"},{"startQuantity":1,"outgoing":["bbf36031-28cc-48d2-bcae-b27e5fc0e3e3"],"incoming":["0d5243e2-29d5-488d-817d-24be3454e1a4"],"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"bbf36031-28cc-48d2-bcae-b27e5fc0e3e3","extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":296,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["InlineUserTask"]},"name":"Inline User Task","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae","calledElement":"1.b7869400-09ed-4f15-972f-39c44a324089"},{"targetRef":"c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Inline User Task","declaredType":"sequenceFlow","id":"0d5243e2-29d5-488d-817d-24be3454e1a4","sourceRef":"020ef269-93e1-4ec9-a845-25808fd32841"},{"targetRef":"fb739613-cf31-46c8-8bcc-f09ff43c4081","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End","declaredType":"sequenceFlow","id":"bbf36031-28cc-48d2-bcae-b27e5fc0e3e3","sourceRef":"c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae"},{"startQuantity":1,"outgoing":["88c8999f-bb5f-4921-8753-1289c3ce561f"],"incoming":["bbf36031-28cc-48d2-bcae-b27e5fc0e3e3"],"default":"88c8999f-bb5f-4921-8753-1289c3ce561f","extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":463,"y":50,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Linked Process","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"fb739613-cf31-46c8-8bcc-f09ff43c4081","calledElement":"25.adb8fd93-1422-4df4-8c36-cbcc83243295"},{"targetRef":"5906e50e-a329-4193-9baa-b5cf3143727c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End","declaredType":"sequenceFlow","id":"88c8999f-bb5f-4921-8753-1289c3ce561f","sourceRef":"fb739613-cf31-46c8-8bcc-f09ff43c4081"}],"laneSet":[{"id":"bf5bd5aa-fce8-4fa5-81e3-974b136805a0","lane":[{"flowNodeRef":["020ef269-93e1-4ec9-a845-25808fd32841","5906e50e-a329-4193-9baa-b5cf3143727c","c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Team","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"1d39daf0-ff15-4abb-8854-89678c24c3c1","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["fb739613-cf31-46c8-8bcc-f09ff43c4081"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Team1","partitionElementRef":"24.ca7e0934-a001-4a68-8626-6a038afdc7c6","declaredType":"lane","id":"71a5549d-73b2-4999-8082-b4850d2dbc7c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":200}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"46340585-98b9-4b9c-8472-c99017078640","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":true,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"instanceName":"\"Test Nested Process:\" + tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1732629390899","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":true,"atRiskCalcEnabled":true,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"33f67998-9913-4607-82f6-d2b64ba0e03b","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Test Nested Process","declaredType":"process","id":"25.adb8fd93-1422-4df4-8c36-cbcc83243295","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"inputSet":[{},{"id":"_bf828780-458b-49e9-b8b3-17058d20b4ba"}],"outputSet":[{},{"id":"_14286da8-9411-4b47-b656-f79e3f574a01"}]}},{"name":"Test Nested ProcessInterface","declaredType":"interface","id":"_c8648c79-65f9-4838-8b83-dcd8923b9935"},{"implementation":"##unspecified","name":"1.b7869400-09ed-4f15-972f-39c44a324089","declaredType":"globalUserTask","id":"1.b7869400-09ed-4f15-972f-39c44a324089","ioSpecification":{"inputSet":[{"id":"8909727b-d49b-4e9c-a1f2-0573d81ffac7"}],"outputSet":[{"id":"0452a7b0-7116-4af7-9f56-5b7f5bd45f02"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"4ae44902-a05d-4b15-b34c-edc77ae0afef"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:2340c4cc899e0880:-********:***********:37c3</guid>
        <versionId>a6db8ada-5bdc-482c-9c72-525ca73c4881</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:2340c4cc899e0880:-********:***********:37c2">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>Test Nested Process</name>
            <documentation></documentation>
            <name>Test Nested Process</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>mohamed.reda</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1732629390911</creationDate>
            <modificationDate>1732629943087</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"Test Nested Process:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="bf5bd5aa-fce8-4fa5-81e3-974b136805a0" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:2340c4cc899e0880:-********:***********:3822" />
            <ownerTeamInstanceUI id="bpdid:2340c4cc899e0880:-********:***********:3821" />
            <simulationScenario id="bpdid:2340c4cc899e0880:-********:***********:37d6">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1732629391381</startTime>
            </simulationScenario>
            <flow id="bbf36031-28cc-48d2-bcae-b27e5fc0e3e3" connectionType="SequenceFlow">
                <name>To End</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:2340c4cc899e0880:-********:***********:382e" />
                </connection>
            </flow>
            <flow id="88c8999f-bb5f-4921-8753-1289c3ce561f" connectionType="SequenceFlow">
                <name>To End</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:2340c4cc899e0880:-********:***********:382f" />
                </connection>
            </flow>
            <flow id="0d5243e2-29d5-488d-817d-24be3454e1a4" connectionType="SequenceFlow">
                <name>To Inline User Task</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:2340c4cc899e0880:-********:***********:3830" />
                </connection>
            </flow>
            <pool id="bf5bd5aa-fce8-4fa5-81e3-974b136805a0">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at1732629390899</restrictedName>
                <dimension>
                    <size w="3000" h="400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="1d39daf0-ff15-4abb-8854-89678c24c3c1">
                    <name>Team</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="c7fef59a-1f23-4a02-9d0c-55cd8b8c87ae" componentType="Activity">
                        <name>Inline User Task</name>
                        <documentation></documentation>
                        <position>
                            <location x="296" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.b7869400-09ed-4f15-972f-39c44a324089</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:2340c4cc899e0880:-********:***********:3824">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:2340c4cc899e0880:-********:***********:3825">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:2340c4cc899e0880:-********:***********:3828">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="0d5243e2-29d5-488d-817d-24be3454e1a4" />
                        </inputPort>
                        <outputPort id="bpdid:2340c4cc899e0880:-********:***********:3829">
                            <positionId>rightCenter</positionId>
                            <flow ref="bbf36031-28cc-48d2-bcae-b27e5fc0e3e3" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="020ef269-93e1-4ec9-a845-25808fd32841" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:2340c4cc899e0880:-********:***********:3827">
                            <positionId>rightCenter</positionId>
                            <flow ref="0d5243e2-29d5-488d-817d-24be3454e1a4" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="5906e50e-a329-4193-9baa-b5cf3143727c" componentType="Event">
                        <name>End</name>
                        <documentation></documentation>
                        <position>
                            <location x="650" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:2340c4cc899e0880:-********:***********:382c">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="88c8999f-bb5f-4921-8753-1289c3ce561f" />
                        </inputPort>
                    </flowObject>
                </lane>
                <lane id="71a5549d-73b2-4999-8082-b4850d2dbc7c">
                    <name>Team1</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c319d130-0736-4bb2-8843-c5f9e688799e/24.ca7e0934-a001-4a68-8626-6a038afdc7c6</attachedParticipant>
                    <flowObject id="fb739613-cf31-46c8-8bcc-f09ff43c4081" componentType="Activity">
                        <name>Linked Process</name>
                        <documentation></documentation>
                        <position>
                            <location x="463" y="50" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>2</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>5</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedProcessId>/25.adb8fd93-1422-4df4-8c36-cbcc83243295</attachedProcessId>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:2340c4cc899e0880:-********:***********:382a">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="bbf36031-28cc-48d2-bcae-b27e5fc0e3e3" />
                        </inputPort>
                        <outputPort id="bpdid:2340c4cc899e0880:-********:***********:382b">
                            <positionId>rightCenter</positionId>
                            <flow ref="88c8999f-bb5f-4921-8753-1289c3ce561f" />
                        </outputPort>
                    </flowObject>
                </lane>
            </pool>
            <extension id="bpdid:2340c4cc899e0880:-********:***********:382d" type="CASE">
                <caseFolder id="33f67998-9913-4607-82f6-d2b64ba0e03b">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

