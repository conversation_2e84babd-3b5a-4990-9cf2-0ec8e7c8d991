<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.9e0de1a3-7709-49c8-9b29-963ea4784c51" name="IDC Execution Hub Initiation 2">
        <lastModified>1714998772457</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.df9bbc7e-d1fe-4121-bd0f-cacf84294522</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>6811ba57-8d30-45f6-8598-85dfd45af416</guid>
        <versionId>85b06b71-d037-473a-ad80-0b3621c3ab1c</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.*************-4646-8ccc-7c17c4e5c1dc"],"isInterrupting":true,"extensionElements":{"default":["2027.*************-4646-8ccc-7c17c4e5c1dc"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":47,"y":58,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"22be4bfc-9649-4377-aebf-9c66f55c5f3e"},{"startQuantity":1,"outgoing":["2027.afbaa6a5-e738-4c50-84c2-6638602bfba7"],"incoming":["2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0"],"default":"2027.afbaa6a5-e738-4c50-84c2-6638602bfba7","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":426,"y":44,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\ntw.local.validationMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\ntw.local.invalidTabs = [];\r\n\r\nfunction validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n   regexString = `^\\\\d{1,10}(\\\\.\\\\d{1,6})?$`;\r\n   regex = new RegExp(regexString);\r\n\r\n  if (!regex.test(field))\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction alphanumeric (str) {\r\n  \/\/ Define a regular expression pattern to match 16 alphanumeric characters\r\n  const pattern = \/^[A-Za-z0-9]{1,16}$\/;\r\n  \r\n  \/\/ Test the input string against the pattern and return the result\r\n  return pattern.test(str);\r\n}\r\n\r\nfunction validateDecimal2(field, fieldName, controlMessage , validationMessage) {\r\n   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n   regex = new RegExp(regexString);\r\n\r\n  if (!regex.test(field) &amp;&amp; field &gt;= 0)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction isNumber(field , fieldName) {\r\n  if (!isNaN(parseFloat(field)) &amp;&amp; isFinite(field)){\r\n\treturn true;\r\n  }\r\n  addError(fieldName , \"This Field Must Be Number\" , \"validationMessage\");\r\n  return false;\r\n}\r\n\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tvar today =  new Date();\r\n\t\ttoday.setHours(0,0,0,0);\r\n\/\/\t\talert(\"today = \"+today);\r\n\/\/\t\talert(\"date = \"+ date);\r\n\t\tif(date != null &amp;&amp; date &lt; today)\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index , tabName)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\tif (tw.local.validationMessage.length == 0) {\r\n\t\t\ttw.local.validationMessage += \"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.validationMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\/\/ ====================================================================================================== \/\/\r\n\/\/--------------------------------------------------------------------------------------------------\r\nfunction swiftValidation (field,fieldName) {\r\n\tregexString = \/^(?![\\s])[a-zA-Z0-9.,()\\\/='+:?!\\\"%&amp;*&lt;&gt;;{@#_ \\r\\n-]*$\/;\r\n\tregex = new RegExp(regexString);\r\n\t\r\n\tif (field != \"\") {\r\n\t\tif (!regex.test(field)){\r\n\t\t\taddError(fieldName , \"Not Valid Swift Format\",\"Not Valid Swift Format\" );\r\n\t\t}\r\n\t}\r\n}\r\n\/\/--------------------------------------------------------------------------------------------------------\r\n\/*\r\n* =====================\r\n* |\tVALIDATE HERE   |\r\n* =====================\r\n*\/\r\n\r\n\r\n\/\/ \/\/----------------------------------customer info[0] ---------------------------------------------------------------\r\nmandatory(tw.local.idcRequest.customerInformation.facilityType,\"tw.local.idcRequest.customerInformation.facilityType\");\r\nmandatory(tw.local.idcRequest.customerInformation.importCardNumber,\"tw.local.idcRequest.customerInformation.importCardNumber\");\r\n\r\n\/\/ validateTab(0,\"Customer Information\");\r\n\/\/ \/\/ \/\/---------------------------------basic details[1]-----------------------------------------------------------------\r\nmandatory(tw.local.idcContract.IDCProduct.englishdescription,\"tw.local.idcContract.IDCProduct.englishdescription\");\r\nmandatory(tw.local.idcRequest.importPurpose.englishdescription,\"tw.local.idcRequest.importPurpose.englishdescription\");\r\nmandatory(tw.local.idcRequest.paymentTerms.englishdescription,\"tw.local.idcRequest.paymentTerms.englishdescription\");\r\nmandatory(tw.local.idcRequest.documentsSource.englishdescription,\"tw.local.idcRequest.documentsSource.englishdescription\");\r\nmandatory(tw.local.idcRequest.productCategory.englishdescription,\"tw.local.idcRequest.productCategory.englishdescription\");\r\nmandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\nif (tw.local.idcRequest.invoices.length&gt;0){\r\n\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\t}\r\n}\r\nif(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\t}\r\n}\r\nmandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\r\nif (tw.local.idcRequest.IDCRequestNature.englishdescription == \"New Request\" &amp;&amp; tw.local.selectedAction == \"Create Contract\") {\r\n\t\r\n\tmandatory(tw.local.idcRequest.FCContractNumber,\"tw.local.idcRequest.FCContractNumber\");\r\n}\r\n\r\nvalidateTab(1,\"Basic Details\");\r\n\/\/-----------------------------------financial Details[2]---------------------------------------------------------\r\nmandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\nminNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\r\nmandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\nmandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\nmandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\nmandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.englishdescription,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.englishdescription\");\r\nmandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.englishdescription,\"tw.local.idcRequest.financialDetails.sourceOfFunds.englishdescription\");\r\n\r\n\/\/ validateTab(2,\"Financial Details Branch\");\r\n\/\/ \/\/ \/\/----------------------------------financial Details fo[3]--------------------------------------------------------\r\nmandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,\"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate\" );\r\nmandatory(tw.local.idcRequest.financialDetails.daysTillMaturity,\"tw.local.idcRequest.financialDetails.daysTillMaturity\" );\r\nmandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.name\");\r\nmandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank\" );\t\r\nmandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.account\" );\r\nmandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code\");\r\nmandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,\"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber\" );\r\nif (tw.local.selectedAction == tw.epv.Action.approveRequest) {\r\n\tmandatory(tw.local.idcRequest.financialDetails.executionHub.code,\"tw.local.idcRequest.financialDetails.executionHub.code\");\r\n}\r\nif (tw.local.idcRequest.documentsSource.englishdescription == \"Correspondent\") {\r\n\tmandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.correspondentRefNum,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.correspondentRefNum\");\r\n}\r\nvar sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;\r\nif(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\taddError(\"tw.local.idcRequest.financialDetails.cashAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\taddError(\"tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\r\n}\r\n\r\nvar sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;\r\nif (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtSight\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n}\r\nif (tw.local.idcRequest.financialDetails.amtDeferredNoAvalized &gt;0 &amp;&amp; tw.local.idcRequest.financialDetails.amtDeferredAvalized &gt; 0) {\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"\");\t\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"\");\t\r\n}\r\nif (tw.local.idcRequest.financialDetails.amtDeferredNoAvalized ==0 &amp;&amp; tw.local.idcRequest.financialDetails.amtDeferredAvalized == 0) {\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"you must fill at least on of these fields\");\t\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"you must fill at least on of these fields\");\t\r\n\r\n}\r\nvar totalInstallments = 0;\r\nif (tw.local.idcRequest.paymentTerms.englishdescription != \"Sight\") {\r\n\tfor (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {\r\n\t\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentDate\");\r\n\t\tnotPastDate(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate , 'tw.local.idcRequest.financialDetails.paymentTerms['+i+'].installmentDate' , 'cannot select date in the past' , 'cannot select date in the past' , false);\r\n\t\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentAmount\");\r\n\t\ttotalInstallments +=  tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount;\r\n\t}\r\n\tif (totalInstallments &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\t\taddError(\"tw.local.idcRequest.financialDetails.paymentTerms[\"+0+\"].installmentAmount\",\"Sum of all Installment Amounts in a request must be &lt;= Amount Payable by NBE\");\r\n\t}\r\n}\r\nif (tw.local.idcRequest.financialDetails.amtPayableByNBE &lt; 0) {\r\n\taddError(\"tw.local.idcRequest.financialDetails.amtPayableByNBE\" , \"must be more than 0\");\r\n}\r\n\r\n\/\/ tw.local.validationMessage += \"Financial Details Trade FO\";\r\nvalidateTab(3,\"Financial Details Trade FO\");\r\n\/\/------------------------------product details[4]--------------------------------------------------------------\r\n\r\nmandatory(tw.local.idcRequest.productsDetails.shippingDate,\"tw.local.idcRequest.productsDetails.shippingDate\");\r\nmandatory(tw.local.idcRequest.productsDetails.incoterms.englishdescription,\"tw.local.idcRequest.productsDetails.incoterms.englishdescription\");\r\nmandatory(tw.local.idcRequest.productsDetails.shipmentMethod.arabicdescription,\"tw.local.idcRequest.productsDetails.shipmentMethod.arabicdescription\");\r\nmandatory(tw.local.idcRequest.productsDetails.destinationPort,\"tw.local.idcRequest.productsDetails.destinationPort\");\r\nmandatory(tw.local.idcRequest.productsDetails.CBECommodityClassification.arabicdescription,\"tw.local.idcRequest.productsDetails.CBECommodityClassification.arabicdescription\");\r\nmandatory(tw.local.idcRequest.productsDetails.HSProduct.code,\"tw.local.idcRequest.productsDetails.HSProduct.code\");\r\nmandatory(tw.local.idcRequest.productsDetails.ACID,\"tw.local.idcRequest.productsDetails.ACID\");\r\n\r\n\r\n\/\/ validateTab(4,\"Product Details\");\r\n\/\/ \/\/ \/\/---------------------------------Create Contract[5]--------------------------------------\r\n\/\/Basic Details\r\n\r\n mandatory(tw.local.idcContract.userReference,\"tw.local.idcContract.userReference\");\r\nif(alphanumeric(tw.local.idcContract.userReference) == false){\r\n\taddError(\"tw.local.idcContract.userReference\",\"Please enter upto 16 alphanumeric chars\");\r\n}\r\n\r\n\/\/Contract Transaction Details\t\r\nif (tw.local.contractTransactionDetailsVis == true) {\r\n\tmandatory(tw.local.idcContract.transactionTenorDays,\"tw.local.idcContract.transactionTenorDays\");\r\n\tmandatory(tw.local.idcContract.transactionTransitDays,\"tw.local.idcContract.transactionTransitDays\");\r\n}\t\t\t\t\t\t\t\t\r\n\/\/Interest Details\r\nif (tw.local.idcContract.haveInterest == true) {\r\n\tif (tw.local.idcContract.interestFromDate &lt; tw.local.idcContract.transactionBaseDate){\r\n\t\taddError(\"tw.local.idcContract.interestFromDate\",\"Interest From Date must be &gt;= Base Date.\");\r\n\t}\r\n\tif (tw.local.idcContract.interestToDate &gt; tw.local.idcContract.transactionMaturityDate){\r\n\t\taddError(\"tw.local.idcContract.interestToDate\",\"Interest To Date must be &lt;= Maturity Date.\");\r\n\t}\r\n\r\n\tif (tw.local.idcContract.interestToDate &lt;= tw.local.idcContract.interestFromDate) {\r\n\t\taddError(\"tw.local.idcContract.interestToDate\",\"Interest To Date must be &gt; Interest From Date.\");\r\n\t}\r\n\tif (tw.local.idcContract.interestRate &lt; 0) {\r\n\t\taddError(\"tw.local.idcContract.interestRate\",\"Rate must be &gt;= 0\")\r\n\t}\r\n\tif (tw.local.idcContract.interestAmount &lt; 0){\r\n\t\taddError(\"tw.local.idcContract.interestAmount\",\"Amount must be &gt;= 0\")\r\n\t}\r\n}\r\n\t\t\t\t\t\t\r\n\/\/Cash Collaterals\r\nif (tw.local.contractCashCollateralsVis == true) {\r\n\tif (tw.local.idcContract.collateralAmount &lt; 0){\r\n\t\taddError(\"tw.local.idcContract.collateralAmount\",\"Collateral Amount must be &gt;= 0\");\r\n\t}\r\n\tif (tw.local.idcContract.collateralAmount &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\t\taddError(\"tw.local.idcContract.collateralAmount\",\"Collateral Amount must be &lt;= Total Amount Payable By NBE\");\r\n\t}\r\n\tif (tw.local.idcContract.collateralAmount &gt; 0) {\r\n\t\tmandatory(tw.local.idcContract.collateralCurrency.code,\"tw.local.idcContract.collateralCurrency.code\")\r\n\t}\r\n\tif (tw.local.collateralError == true) {\r\n\t\taddError(\"tw.local.idcContract.collateralAmount\",\"Collateral Amount must be &lt;= Total Amount Payable By NBE Please consider the currency exchange\");\r\n\t}\r\n\r\n\tif (tw.local.idcContract.collateralAmount &gt; 0) {\r\n\t\tfor (var i=0; i&lt;tw.local.idcContract.cashCollateralAccounts.length; i++){\r\n\t\t\tmandatory(tw.local.idcContract.cashCollateralAccounts[i].debitedAmtinCollateralCurrency,\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].debitedAmtinCollateralCurrency\");\r\n\t\t\tif (tw.local.idcContract.cashCollateralAccounts[i].accountClass == \"Customer Account\") {\r\n\t\t\t\tmandatory(tw.local.idcContract.cashCollateralAccounts[i].accountNumber.name,\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].accountNumber.name\");\r\n\t\t\t}else if(tw.local.idcContract.cashCollateralAccounts[i].accountClass == \"GL Account\"){\r\n\t\t\t\t\r\n\t\t\t\tmandatory(tw.local.idcContract.cashCollateralAccounts[i].GLAccountNumber,\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].GLAccountNumber\");\r\n\t\t\t\tmandatory(tw.local.idcContract.cashCollateralAccounts[i].accountBranchCode,\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].accountBranchCode\");\r\n\t\t\t\tmandatory(tw.local.idcContract.cashCollateralAccounts[i].accountCurrency,\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].accountCurrency\");\r\n\t\t\t\t\r\n\t\t\t\tif (tw.local.idcContract.cashCollateralAccounts[i].isGLFound == false) {\r\n\t\t\t\t\taddError(\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].GLAccountNumber\",\"GL Account Not Found\");\r\n\t\t\t\t}\r\n\t\t            if (tw.local.idcContract.cashCollateralAccounts[i].isGLVerified == false) {\r\n\t\t                addError(\"tw.local.idcContract.cashCollateralAccounts[\"+i+\"].GLAccountNumber\",\"Please Click the Verify Button\");\r\n\t\t            }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (tw.local.idcRequest.financialDetails.documentCurrency.code != tw.local.idcContract.collateralCurrency.code){\r\n\t\tvar amount = tw.local.idcContract.collateralAmount * tw.local.exRate;\r\n\t\tif (amount &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\t\t\taddError(\"tw.local.idcContract.collateralAmount\",\"Collateral Amount must be &lt;= Total Amount Payable By NBE\");\r\n\t\t}\r\n\t}\r\n}\r\n\/\/Advices\r\nif (tw.local.contractAdvicesVis == true) {\r\n    for (var i=0; i&lt;tw.local.idcContract.advices.length; i++) {\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line1,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line1\");\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line2,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line2\");\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line3,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line3\");\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line4,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line4\");\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line5,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line5\");\r\n        swiftValidation (tw.local.idcContract.advices[i].advicelines.line6,\"tw.local.idcContract.advices[\"+i+\"].advicelines.line6\");\r\n    }\r\n}\r\n\/\/ validateTab(5,\"Create Contract\");\r\n\/\/ \/\/-----------------------------------Commissions and Charges[6]---------------------------------------------- \r\nfor (var i=0; i&lt;tw.local.idcContract.commissionsAndCharges.length; i++) {\r\n\tif (tw.local.idcContract.commissionsAndCharges[i].chargeAmount &lt; 0){\r\n\t\taddError(\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].chargeAmount\",\"Must be &gt;= 0\");\r\n\t}\r\n\tif (tw.local.idcContract.commissionsAndCharges[i].waiver == false &amp;&amp; tw.local.idcContract.commissionsAndCharges[i].chargeAmount &gt; 0) {\r\n\t\tmandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.accountClass\");\r\n\t\r\n\t\tif (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == \"Customer Account\"){\r\n\t      \t mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountNumber,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.accountNumber\");\r\n\t\t\tmandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts,\"tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts\")\r\n\t       \r\n\t   \t}else if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == \"GL Account\"){\r\n\t   \t\tif(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLVerifiedC == false){\r\n\t   \t\t\taddError(\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.GLAccountNumber\",\"Please Click the Verify Button\");\r\n\t   \t\t}\r\n\t\t\tmandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.GLAccountNumber\");\r\n\t\t\tmandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountBranchCode,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.accountBranchCode\");\r\n\t\t\tmandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountCurrency,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAccount.accountCurrency\");\r\n\t\t}\r\n\t\tif (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLFoundC == false ){\r\n\t\t\taddError(\"tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber\",\"Account Not Found\");\r\n\t\t}\r\n\t\tif (tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != undefined){\r\n\t\t\tvalidateDecimal(tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate,\"tw.local.idcContract.commissionsAndCharges[\"+i+\"].debitedAmount.negotiatedExchangeRate\",\"Must be Decimal (10,6)\");\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\/\/ validateTab(6,\"Commissions and Charges\");\r\n\/\/ \/\/-------------------------------------------------------Party[7]-------------------------------------------------------------------------\r\n var accounteeFound = false;\r\n var RemittingFound = false;\r\n var caseFound = false;\r\n var drawerFound = false;\r\n var draweeFound = false;\r\n for (var i=0; i&lt;tw.local.idcContract.party.length; i++) {\r\n \tmandatory(tw.local.idcContract.party[i].partyId,\"tw.local.idcContract.party[\"+i+\"].partyId\");\r\n \tif (tw.local.idcContract.party[i].partyType.name == \"Remitting Bank\") {\r\n \t\tmandatory(tw.local.idcContract.party[i].media,\"tw.local.idcContract.party[\"+i+\"].media\");\r\n \t\tmandatory(tw.local.idcContract.party[i].address,\"tw.local.idcContract.party[\"+i+\"].address\");\r\n \t}\r\n \tif ((accounteeFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == \"Accountee\") || (RemittingFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == \"Remitting Bank\") || (caseFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == \"Case in Need\" ) || (drawerFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == \"Drawer\") || (draweeFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == \"Drawee\")) {\r\n \t\taddError(\"tw.local.idcContract.party[\"+i+\"].partyType.name\",\"only one is allowed from this type\");\r\n \t}else{\r\n \t\tif (tw.local.idcContract.party[i].partyType.name == \"Accountee\") {\r\n \t\t\taccounteeFound = true;\r\n \t\t}\r\n \t\tif (tw.local.idcContract.party[i].partyType.name == \"Drawee\") {\r\n \t\t\tdraweeFound = true;\r\n \t\t}\r\n \t\tif (tw.local.idcContract.party[i].partyType.name == \"Drawer\") {\r\n \t\t\tdrawerFound = true;\r\n \t\t}\r\n \t\tif (tw.local.idcContract.party[i].partyType.name == \"Remitting Bank\") {\r\n \t\t\tRemittingFound = true;\r\n \t\t}\r\n \t\tif (tw.local.idcContract.party[i].partyType.name == \"Case in Need\") {\r\n \t\t\tcaseFound = true;\r\n \t\t}\r\n \t}\r\n\t\r\n \tmandatory(tw.local.idcContract.party[i].address1,\"tw.local.idcContract.party[\"+i+\"].address1\");\r\n \tmandatory(tw.local.idcContract.party[i].reference,\"tw.local.idcContract.party[\"+i+\"].reference\");\r\n \tmandatory(tw.local.idcContract.party[i].name,\"tw.local.idcContract.party[\"+i+\"].name\");\r\n \tmandatory(tw.local.idcContract.party[i].country,\"tw.local.idcContract.party[\"+i+\"].country\");\r\n \tmandatory(tw.local.idcContract.party[i].language,\"tw.local.idcContract.party[\"+i+\"].language\");\r\n }\r\n validateTab(7,\"Parties\");\r\n\r\n\/\/ \/\/ \/\/-------------------------------------limit[8]-----------------------------------------------------------------\r\ntw.local.limitMessage = \"\";\r\nif (tw.local.idcContract.isLimitsTrackingRequired == true) {\r\n\tvar ciflist = [];\r\n\tfor (var i=0; i&lt;tw.local.idcContract.party.length; i++) {\r\n\t\tif (tw.local.idcContract.party[i].partyType.name == \"Drawee\" || tw.local.idcContract.party[i].partyType.name == \"Accountee\") {\r\n\t\t\tciflist.push(tw.local.idcContract.party[i].partyId);\r\n\t\t}\r\n\t}\r\n\tif (tw.local.idcContract.facilities.length == 0) {\r\n\t\taddError(\"tw.local.facility.facilityCode.value\",\"you must add facility\",\"you must add facility\");\r\n\t\t\r\n\t}else{\r\n\t\tvar sumOfPresentage = 0;\r\n\t\tfor (var i=0; i&lt;tw.local.idcContract.facilities.length; i++) {\r\n\t\t\tfor (var j=0; j&lt;tw.local.idcContract.facilities[i].facilityLines.length; j++) {\r\n\t\t\t\tsumOfPresentage = sumOfPresentage + tw.local.idcContract.facilities[i].facilityLines[j].facilityPercentageToBook;\r\n\t\t\t\tif (tw.local.idcContract.facilities[i].facilityLines[j].CIF != ciflist[0] &amp;&amp; tw.local.idcContract.facilities[i].facilityLines[j].CIF != ciflist[1]) {\r\n\t\t\t\t\ttw.local.limitMessage += \"the facility \"+tw.local.idcContract.facilities[i].facilityCode+\" is not related to drawee or accountee \\n\";\r\n\t\t\t\t\taddError(\"tw.local.limitMessage\",\"Error\",\"Error\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\tif (sumOfPresentage != 100) {\r\n\t\t\ttw.local.limitMessage += \"The Sum Of Facility Percentage To Book Must Be 100% \\n\";\r\n\t\t\taddError(\"tw.local.limitMessage\",\"Error\",\"Error\");\t\t\t\r\n\t\t}\r\n\t}\r\n}\r\n\/\/ validateTab(8,\"Limits Tracking\");\r\n\/\/----------------------------------------app info--------------------------------------------------------------\r\n\/\/------------------------------------Actions Validation---------------------------------------------------------\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\r\nif (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n}\r\n\r\nif (tw.local.selectedAction == tw.epv.Action.returnToTradeFO){\r\n\tmandatory(tw.local.idcRequest.stepLog.returnReason,\"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\nif (tw.local.selectedAction == tw.epv.Action.createContract) {\r\n    mandatory(tw.local.idcRequest.FCContractNumber,\"tw.local.idcRequest.FCContractNumber\");\r\n}\r\n\r\nif (tw.local.idcRequest.approvals.CAD==true &amp;&amp; tw.local.selectedAction == \"Obtain Approvals\" &amp;&amp; tw.local.idcContract.facilities.length == 0) {\r\n\taddError(\"tw.local.selectedAction\", \"This requset does not has facility to acquire CAD\");\r\n}\r\n\r\nif ((tw.local.idcRequest.approvals.CAD==true|| \r\n    tw.local.idcRequest.approvals.compliance==true ||\r\n    tw.local.idcRequest.approvals.treasury==true))\r\n{   \r\n    if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n       addError(\"tw.local.selectedAction\", \"Please uncheck Approvals\");\r\n    }\r\n}else if (tw.local.selectedAction == \"Obtain Approvals\")\r\n{\r\n    addError(\"tw.local.selectedAction\", \"Please check Approvals\");\r\n}\r\n\r\n"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.ebf2625e-51cd-4f8d-bdfd-ed7bae08b22b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","documentation":[{"textFormat":"text\/plain"}],"name":"contractInterestDetailsVis","isCollection":false,"declaredType":"dataObject","id":"2056.3ddbd5c6-f9a5-47d1-98c8-faf818bc4ab9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"contractCashCollateralsVis","isCollection":false,"declaredType":"dataObject","id":"2056.8b2f5baa-46c9-4a7c-83d6-c472846350e1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"contractLimitsTrackingVis","isCollection":false,"declaredType":"dataObject","id":"2056.eee297e2-e33d-46a6-a23a-6e6882c58b51"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"contractAdvicesVis","isCollection":false,"declaredType":"dataObject","id":"2056.9f9d5d04-364b-42e4-9edc-edc5c92ab772"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"contractTransactionDetailsVis","isCollection":false,"declaredType":"dataObject","id":"2056.9ff7ba4a-6b6a-49cf-b6bb-ad0e9701ef38"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"advancePaymentsUsedVis","isCollection":false,"declaredType":"dataObject","id":"2056.e02bf9fd-e2e4-4827-8661-0ca1511b2105"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.841104aa-2225-4d72-8e37-f015d834ffbb"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.8b9b8ce3-9933-4b67-a696-88f14c673b7a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"partyTypeList","isCollection":true,"declaredType":"dataObject","id":"2056.a5652337-27bc-4562-a4c3-aa60223ddabd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatString","isCollection":false,"declaredType":"dataObject","id":"2056.9644ec97-63e3-43c4-b7aa-3b784db3c80f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.account = \"\";\nautoObject.bank = \"\";\nautoObject.correspondentRefNum = \"\";\nautoObject.name = \"\";\nautoObject.country = {};\nautoObject.country.id = 0;\nautoObject.country.code = \"\";\nautoObject.country.arabicdescription = \"\";\nautoObject.country.englishdescription = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a025468c-38bc-4809-b337-57da9e95dacb","name":"BeneficiaryDetailsOption","isCollection":false,"declaredType":"dataObject","id":"2056.c2f8a6cd-ff7c-4b3c-8926-1221b9631245"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"partyIndex","isCollection":false,"declaredType":"dataObject","id":"2056.65a726ef-6236-4b6b-8da8-4cbb496c841e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"1"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"exRate","isCollection":false,"declaredType":"dataObject","id":"2056.8a7ace65-b33e-4420-93f3-1bf3437bba7b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].accountNO = \"\";\nautoObject[0].currencyCode = \"\";\nautoObject[0].branchCode = \"\";\nautoObject[0].balance = 0.0;\nautoObject[0].typeCode = \"\";\nautoObject[0].customerName = \"\";\nautoObject[0].customerNo = \"\";\nautoObject[0].frozen = false;\nautoObject[0].dormant = false;\nautoObject[0].noDebit = false;\nautoObject[0].noCredit = false;\nautoObject[0].postingAllowed = false;\nautoObject[0].ibanAccountNumber = \"\";\nautoObject[0].accountClassCode = \"\";\nautoObject[0].balanceType = \"\";\nautoObject[0].accountStatus = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","documentation":[{"content":["&lt;div&gt;&lt;br \/&gt;&lt;\/div&gt;"],"textFormat":"text\/plain"}],"name":"accountList","isCollection":true,"declaredType":"dataObject","id":"2056.1ed5bdf9-280d-4ee3-bd6f-f7f5af23f215"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"accountNumberList","isCollection":true,"declaredType":"dataObject","id":"2056.9a923395-c931-45eb-93c3-d42bcc156340"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.f687ac51-6bf9-4bf0-8a14-73f61f74ba28"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.ff0287cf-0ce4-44f4-85ba-d4b6a2a2947d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.bfafc277-f866-4dcd-96ce-8138f68ca691"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.b2b75484-ee9d-4c50-93ee-93a2552988b5"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"exist","isCollection":true,"declaredType":"dataObject","id":"2056.34ab7fee-8079-4d23-93fa-8efcacf2b686"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.customerNo = \"\";\nautoObject.customerType = \"\";\nautoObject.customerName = \"\";\nautoObject.customerCategory = \"\";\nautoObject.customerBranch = \"\";\nautoObject.customerStatus = {};\nautoObject.customerStatus.name = \"\";\nautoObject.customerStatus.value = \"\";\nautoObject.privateCustomer = \"\";\nautoObject.faciliyBranchCode = \"\";\nautoObject.frozen = false;\nautoObject.deceased = false;\nautoObject.sName = \"\";\nautoObject.customerFullName = \"\";\nautoObject.nationality = \"\";\nautoObject.country = {};\nautoObject.country.name = \"\";\nautoObject.country.value = \"\";\nautoObject.customerAddress = {};\nautoObject.customerAddress.addressLine1 = \"\";\nautoObject.customerAddress.addressLine2 = \"\";\nautoObject.customerAddress.addressLine3 = \"\";\nautoObject.customerAddress.addressLine4 = \"\";\nautoObject.customerAddress.customerNO = \"\";\nautoObject.fullName = \"\";\nautoObject.customerArabicName = \"\";\nautoObject.customerEnglishName = \"\";\nautoObject.customerNationalID = \"\";\nautoObject.tradingTitle = \"\";\nautoObject.commercialRegisterNo = \"\";\nautoObject.cardTaxNo = \"\";\nautoObject.businessActivity = \"\";\nautoObject.arabicName = \"\";\nautoObject.EnglishName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.16768f40-baac-4f67-b4c8-1515e214acf9"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isGLFound","isCollection":false,"declaredType":"dataObject","id":"2056.0a9ecfc1-5bfa-43cd-8a58-5da87970d8b0"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.946b98e1-c28f-48ee-a16a-7df32b75a87f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.dc0493e1-914d-4c9b-bc17-df6382964e73"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.d4fafa4b-5e4e-4046-ae4e-e267a1b27c43"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.fcb2c3c9-109f-42f6-9486-16cb1c790266"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.665d3d2d-0218-4c74-a461-ba2515fb91c6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.b8c585b2-9c44-4d3b-87fb-235a191a2988"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.2573c8bf-c93e-473b-b648-e550a03b8d2e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"havePaymentTerms","isCollection":false,"declaredType":"dataObject","id":"2056.4f15f032-dad4-49b8-8698-f77827a03ff3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedBIC","isCollection":false,"declaredType":"dataObject","id":"2056.0d2edf67-9e17-4c95-b706-c3df33c9e8d8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"chargesAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.66da0014-9bfa-4595-9f60-db019e021a8e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"adviceCodeList","isCollection":true,"declaredType":"dataObject","id":"2056.e3ed63c0-8121-4455-b3d2-5f1f21ce4f4f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.name = \"\";\nautoObject.value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"tempObj","isCollection":false,"declaredType":"dataObject","id":"2056.5bc4fd52-e3b2-4170-a76e-187aa172e20b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject"}]},"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"partyAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.7137ff16-5afa-4666-ad46-4b64043754ec"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sumAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.338ceda7-f7d7-4489-9518-d6660e4ebb53"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"accounteeCIF","isCollection":false,"declaredType":"dataObject","id":"2056.bafa5084-67d2-44a1-b111-0ef024850793"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerCIF","isCollection":false,"declaredType":"dataObject","id":"2056.369c4936-0e4f-4815-bddb-3710f9d5f6c9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"caseCIF","isCollection":false,"declaredType":"dataObject","id":"2056.171a12f7-e907-4353-be80-637dc742f7e9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"draweeCIF","isCollection":false,"declaredType":"dataObject","id":"2056.79af2607-11e0-4a15-83f1-492ffcdbc611"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject"}]},"itemSubjectRef":"itm.12.89a53d06-50b8-41df-a5cc-5e4f61147b6d","name":"tempCommissions","isCollection":true,"declaredType":"dataObject","id":"2056.6e172f54-cb2e-4136-82a8-723f0d2fb01a"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"partiesVis","isCollection":false,"declaredType":"dataObject","id":"2056.4b9a2570-dfb1-4b0c-ab1c-74be76bfda38"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isChecker","isCollection":false,"declaredType":"dataObject","id":"2056.b8ce5d34-3ecd-4e3d-b20f-f7819f84383e"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"liqDone","isCollection":false,"declaredType":"dataObject","id":"2056.4cfb1ce5-6911-46f8-b306-f58edc11c771"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"amtPayableByNBE","isCollection":false,"declaredType":"dataObject","id":"2056.06a0d82c-0930-4399-bd5f-bfa6be68158e"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"intDefaultAmount","isCollection":false,"declaredType":"dataObject","id":"2056.47f2a416-723e-4fa5-add1-38f6b36f9bb9"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"minAmount","isCollection":false,"declaredType":"dataObject","id":"2056.db14b40c-f2fe-4564-baaa-989a20160b7e"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"chargesIndex","isCollection":false,"declaredType":"dataObject","id":"2056.*************-4eda-94e5-c5a8eed9086b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatExCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.8f6675f6-457c-4249-a083-e230ba345552"},{"itemSubjectRef":"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e","name":"InterestsAndChargesList","isCollection":true,"declaredType":"dataObject","id":"2056.e5559355-c9c7-48e3-8e03-32b760d966c5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatInterest","isCollection":false,"declaredType":"dataObject","id":"2056.d874db2e-9bed-4502-b05f-cbe98eb0fc2d"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"contractAmount","isCollection":false,"declaredType":"dataObject","id":"2056.4aa8924c-0139-402f-800c-6bb769875d65"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"accountIndex","isCollection":false,"declaredType":"dataObject","id":"2056.04dcc23c-338b-4a97-ac8f-310da2497297"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"adviceData","isCollection":false,"declaredType":"dataObject","id":"2056.c209b161-1758-4b88-b3b1-bc27cee41cb0"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"accountIndexC","isCollection":false,"declaredType":"dataObject","id":"2056.d75ba1ad-3a9c-46e0-b31a-ae6faf51dd05"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"concatComm","isCollection":false,"declaredType":"dataObject","id":"2056.7665f354-07f6-4023-8000-f5e37be7bd51"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityCIF","isCollection":false,"declaredType":"dataObject","id":"2056.02d34944-4b00-4b46-b810-2eb8378660df"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"commCIF","isCollection":false,"declaredType":"dataObject","id":"2056.f9f4bb28-7e6c-442d-bd1a-38836b4d1f72"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isGLFoundC","isCollection":false,"declaredType":"dataObject","id":"2056.cef9cfd2-acbe-4852-b387-3c78c1d3f7b8"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"collateralError","isCollection":false,"declaredType":"dataObject","id":"2056.b6a933b2-0bdc-472c-a826-34e6b922da4a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.RateType = \"\";\nautoObject.Rate = 0.0;\nautoObject"}]},"itemSubjectRef":"itm.12.8c1c1f36-4744-499a-841e-72be2892c861","name":"currencyExchangeRate","isCollection":false,"declaredType":"dataObject","id":"2056.420d4c63-3aef-4e06-98f0-557abd97e4a4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedCIF","isCollection":false,"declaredType":"dataObject","id":"2056.7c43bcc7-6ef6-4154-9502-f7e8f65edb18"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.beedde8a-f965-4cc5-8571-3832147d40af"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Can not retrive customer accounts please try again\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorAccount","isCollection":false,"declaredType":"dataObject","id":"2056.bce77f1e-c7d5-41e9-881c-a9e5683d38f6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].accountNO = \"\";\nautoObject[0].currencyCode = \"\";\nautoObject[0].branchCode = \"\";\nautoObject[0].balance = 0.0;\nautoObject[0].typeCode = \"\";\nautoObject[0].customerName = \"\";\nautoObject[0].customerNo = \"\";\nautoObject[0].frozen = false;\nautoObject[0].dormant = false;\nautoObject[0].noDebit = false;\nautoObject[0].noCredit = false;\nautoObject[0].postingAllowed = false;\nautoObject[0].ibanAccountNumber = \"\";\nautoObject[0].accountClassCode = \"\";\nautoObject[0].balanceType = \"\";\nautoObject[0].accountStatus = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"commAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.b863cf54-3cc9-4fbb-88b0-020a7b1472c6"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"limitedPartyTypeList","isCollection":true,"declaredType":"dataObject","id":"2056.a75e6a7c-a291-4beb-ad40-c2e281b4ce0e"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"interestIsVisible","isCollection":false,"declaredType":"dataObject","id":"2056.4dbcc7e9-e171-41df-a681-09c5d93a48d9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"exConcatedString","isCollection":false,"declaredType":"dataObject","id":"2056.0d1a0d47-ba00-474a-a6c0-b478ed72de43"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpusdAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.5c7e0942-10b6-4439-8032-f12287bd32fd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"haveAmountAdvanced","isCollection":false,"declaredType":"dataObject","id":"2056.3f3d8347-32d2-4f64-a09b-786bb8508689"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.a58732f2-7f59-478e-8d50-246c2eeef485"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.7f36e9c4-bdbf-4b4b-999b-3943b87022de"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.6a665115-5753-44af-8b94-7f89ceca781a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.3b4761b5-0c53-410d-8068-2c25229c6ce5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.6da8d426-d51d-47f8-9c83-9fe0e3850bf2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"commClassCode","isCollection":false,"declaredType":"dataObject","id":"2056.fa56f8ce-2f5c-40d6-ba79-288fca33a1f4"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.066bb830-4c84-4459-83f9-127fa46894e2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"interestVis","isCollection":false,"declaredType":"dataObject","id":"2056.f6707f4e-6945-41e8-94d0-7459cebf8ff2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"addressBICList","isCollection":true,"declaredType":"dataObject","id":"2056.83afc80b-c4cb-4a1b-9ed9-95bc7334e428"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityVis","isCollection":false,"declaredType":"dataObject","id":"2056.a8ac79ef-2a39-4df0-9cbb-0608f54f05f6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityPercentageToBookVis","isCollection":false,"declaredType":"dataObject","id":"2056.398aab6e-2c4e-47ba-a548-fa28a6eca929"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cifs","isCollection":true,"declaredType":"dataObject","id":"2056.34ea356f-15b9-4eb9-867e-102bcda94ac5"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"partyType","isCollection":true,"declaredType":"dataObject","id":"2056.ad600c7c-e329-4f8a-936c-39fe6f9c9040"},{"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"customerFacilities","isCollection":true,"declaredType":"dataObject","id":"2056.7376cf1f-abbe-4876-b923-09810a16a772"},{"itemSubjectRef":"itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08","name":"facility","isCollection":false,"declaredType":"dataObject","id":"2056.ed3d268d-3894-41a4-ba93-6d680b29da6d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"LimitsVis","isCollection":false,"declaredType":"dataObject","id":"2056.ce179ffd-a4f6-4b37-88fc-9f654575b083"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"limitMessage","isCollection":false,"declaredType":"dataObject","id":"2056.33e9a24c-ab08-42fe-b001-ccef14d06702"},{"targetRef":"2025.7138dc8f-1707-4eb8-8721-15624a4ff914","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Execution Hub Initiation","declaredType":"sequenceFlow","id":"2027.*************-4646-8ccc-7c17c4e5c1dc","sourceRef":"22be4bfc-9649-4377-aebf-9c66f55c5f3e"},{"outgoing":["2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0"],"incoming":["2027.*************-4646-8ccc-7c17c4e5c1dc","2027.afbaa6a5-e738-4c50-84c2-6638602bfba7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":199,"y":38,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["DC_Templete1\/submit"],"preAssignmentScript":["\r\ntw.local.interestVis = \"NONE\";\r\ntw.local.errorVIS = \"NONE\";\r\ntw.local.idcRequest.financialDetails.amtPayableByNBE = 2000;\r\ntw.local.idcRequest.financialDetails.documentCurrency.code = \"EGP\";\r\ntw.local.BeneficiaryDetailsOption.name = \"Hamada\";\r\ntw.local.BeneficiaryDetailsOption.country.englishdescription = \"Sudan\";\r\ntw.local.BeneficiaryDetailsOption.correspondentRefNum = \"2251122511\";\r\ntw.local.idcRequest.appInfo.instanceID = \"2251122511\"\r\ntw.local.idcRequest.financialDetails.documentAmount = 3000;\r\ntw.local.idcRequest.customerInformation.CIFNumber = \"03024932\";\r\ntw.local.idcRequest.IDCRequestStage = \"Final\";\r\ntw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation;\r\ntw.local.idcRequest.IDCRequestType.englishdescription = \"ICAP\";\r\n\r\ntw.local.cifs = [\"1st\",\"2nd\",\"3rd\"];"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"50578d7f-b539-4de7-8353-336c3e1153b0","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"911dbff8-279d-4111-8945-f0ff7b6b4b2b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a4d5a42d-4c25-4327-8ecc-684ce310e4c3","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c3c6543-77c3-4631-84ce-db5e87a6ed49","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a56c06ec-7a44-48b1-8a3f-ee0adede5afd","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a6bb27d-6872-4119-8cbe-87dd6288696c","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ac00d1ba-d95d-4ba2-8917-3f1816c2c06d","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"0","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a49c69ef-e6c3-479e-8699-50e973e19547","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"971f23a4-fc93-4030-8adf-e902b7454f42","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fc055b43-b11e-43f4-8e4d-fc1edf66c90f","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9d40b31-0908-4cdf-8eef-e5bcc131834d","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2399e918-36a7-45a8-8e13-4c966f2d1c0f","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"abbb2735-4f54-4517-8aff-723c7cc10d92","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d7302254-20af-4759-8e6e-cc538d2730e7","version":"8550"},{"layoutItemId":"1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9a05a08c-344c-441d-8f46-fb02bd60434c","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f12a9cc6-0213-429c-87c4-7089995c3e59","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a616b111-c426-49f6-83e1-c2c351bb372c","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fb1b9b52-b0e8-4634-8aa3-6622672b011a","optionName":"havePaymentTerm","value":"tw.local.havePaymentTerms"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3b586a0d-85c1-47b3-8129-bd9f606dfb29","optionName":"hasWithdraw","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3fa9865d-ca9d-472a-8921-fd38e7c65288","optionName":"addBill","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5cb3a3a4-f514-4a9c-868d-a87ec2d33742","optionName":"deleteBill","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24e1eae1-a4e1-4485-8068-2c40b4fb0d60","optionName":"addInvoice","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"112a7e0b-2c2f-4d13-8676-43b67bc55d63","optionName":"deleteInvoice","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6841e7eb-42de-4aa2-856e-22dfe6bce1c2","optionName":"alertMessage","value":"tw.local.errorMSG"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"250eed0d-bd5a-42fd-8d83-16cbcc6bd519","optionName":"billExist","value":"0"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7c0a309b-b9a6-4a13-8653-985052c5428a","optionName":"invoiceExist","value":"0"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5d779693-5be0-46a8-8013-db4d7138db15","optionName":"errorVis","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a11fb4e0-3625-45cc-801c-350e1728ae62","optionName":"IDCProduct","value":"tw.local.idcContract.IDCProduct"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e0ecf3cf-45c9-4364-8aa0-d07f19a0238d","optionName":"interestVis","value":"tw.local.interestVis"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bd6b067c-ec38-4291-8db7-6ef93cdc5ffa","optionName":"isHub","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e69f4def-acf2-42ed-8941-3626e9a6d838","optionName":"idcContract","value":"tw.local.idcContract"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e27ab2a5-9458-493f-8350-d48a78e849bf","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"90935fb6-7ab9-4d25-83df-e8530133d970","optionName":"InterestAndChargesList","value":"tw.local.InterestsAndChargesList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7cb1917c-95fb-41d0-8a67-f32681c082dc","optionName":"adviceCodeList","value":"tw.local.adviceCodeList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"880c4ee7-e305-4bd5-86a3-b79bffcd6174","optionName":"stage","value":"tw.local.idcRequest.IDCRequestStage"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c7793482-0481-457e-8cbf-9b7ea0be2bb1","version":"8550"},{"layoutItemId":"2","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65e374a6-cbb8-4765-8581-8b97f0553ebc","optionName":"@label","value":"Financial Details - Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20316d6b-bed1-4add-83c9-de5ac9ac1789","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"908b31d5-07da-46bd-879f-1901e98fdbf3","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"738ddf1b-65d7-41ff-8cec-a31581bba0f1","optionName":"advancePaymentsUsedOption","value":"tw.local.advancePaymentsUsedVis"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"67742af4-05b0-4bc6-81df-4e6658987a19","optionName":"actionEPV","value":"[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"57b1c6bd-a69c-4953-855c-19930867ff8c","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b32897dc-8fe2-4c69-8577-4c73caecd880","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"707a3f7f-c62e-46f9-8245-3f289b2126fe","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6fde81ad-7434-4901-8fb1-5b8856ec89fa","optionName":"accountsList","value":"tw.local.accountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"75a5a5f9-2019-4a06-8f8a-7eb471cb9ebc","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ab2fe7aa-ffcc-4e4a-8ce9-4150cef4039b","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpusdAdvancePayment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cd60a9fe-4ad1-4692-8b95-fcf86046ada2","optionName":"haveAmountAdvanced","value":"tw.local.haveAmountAdvanced"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eb667ce1-810b-4e14-89d6-a267325ad9d5","optionName":"isChecker","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"febef72a-fd2d-4a63-8dbe-95bc60d77e80","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8b83e66d-e80d-4a06-82f6-25c2cbf32742","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f364cb74-dee3-49cc-8ac4-feaea93e921a","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f7605249-c0ce-453d-81cb-5bac4f30a2dc","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f6a9375f-c9a4-4f81-8a35-bc2a1cad69fe","version":"8550"},{"layoutItemId":"3","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5440e320-b256-4a5a-8baa-3b7b7f28ab2d","optionName":"@label","value":"Financial Details - Trade FO"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"78495ab9-c1d3-418d-841f-7a9ba14b5329","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e79aaf6-253f-4354-8cd6-9eafd9c95b40","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3f8d3175-518d-456f-89fd-fb1b6885c376","optionName":"beneficiaryDetails","value":"tw.local.BeneficiaryDetailsOption"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1c4544ba-3dc1-4e92-8aa7-2d36060f8f02","optionName":"havePaymentTerms","value":"tw.local.havePaymentTerms"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"251b0d33-4a9b-40d3-8660-acb35236065d","optionName":"haveTradeFOReferenceNumber","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3c34aeb0-0116-40de-8e18-24460da4b225","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ec382bff-936d-4ae5-8c67-07669b41e588","optionName":"haveAmountAdvanced","value":"tw.local.haveAmountAdvanced"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ed007d74-72c8-4609-8cc0-1ae612e45f19","optionName":"alertMessage","value":"tw.local.alertMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0399e9fb-de30-4b9e-814a-1c99ee8b8143","optionName":"LimitsVis","value":"tw.local.idcContract.limitsTrackingVIs"}],"viewUUID":"64.848ab487-8214-4d8b-88fd-a9cac5257791","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bf758623-ce9d-447d-8f4f-00fdcb191c15","version":"8550"},{"layoutItemId":"4","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0c641917-e7bb-48c3-8419-483f83787321","optionName":"@label","value":"Products Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c8933b7f-e887-4d46-887b-42d03f7bad3f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9608efc2-f4ac-4abb-88eb-2194a21fb1fc","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"781cace5-4e7a-4928-8ffd-9653d63bab0f","optionName":"alertMessage","value":"tw.local.alertMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e6e93a5e-9cc8-4158-8ab9-a1e172a258c9","optionName":"testList","value":"tw.local.cifs[]"}],"viewUUID":"64.bd0ada34-acf3-449a-91df-9aa363c2b280","binding":"tw.local.idcRequest.productsDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a0ecb3be-76e9-4a71-8db5-4db78a20e9e2","version":"8550"},{"layoutItemId":"samah","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c11ac0bf-b2ea-4df0-8ccd-a0fed38e7cdb","optionName":"@label","value":"Charges and Commissions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"98b0ca8f-**************-45fb925aedee","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a2633e0e-b18b-4103-8146-23e5dbd20f16","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2f84310-73c3-4835-8241-eba6ea42db17","optionName":"owner","value":"tw.local.accounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65d4fd3b-9616-4833-8b0a-bab193cc8d5d","optionName":"accounteeCIF","value":"tw.local.accounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c10087f3-4fad-403a-84d7-181473b12971","optionName":"customerCIF","value":"tw.local.customerCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7de19115-4f02-4682-8fb4-d7c562ae1b0d","optionName":"accountNumberList","value":"tw.local.accountNumberList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a3a96ce-6f15-406f-85cf-4e0166f00cb8","optionName":"accountList","value":"tw.local.accountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"56ca78cb-cb03-41f6-8dae-7f40828db4e8","optionName":"caseCIF","value":"tw.local.caseCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"178add51-1469-4f02-835c-32e17e707c32","optionName":"mainCIF","value":"tw.local.mainCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"223d425e-2483-40f1-8610-db21f4a3cc8c","optionName":"draweeCIF","value":"tw.local.draweeCIF"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8a72f9d1-8316-41fb-8570-4058542efbe4","optionName":"exCurrency","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5cc2485b-aa0d-4b45-89b2-a01e3ba20cd0","optionName":"tempCommissions","value":"tw.local.tempCommissions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f125e866-7151-4577-8f76-277e75057a76","optionName":"isChecker","value":"tw.local.isChecker"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f9e4a967-5a74-49ad-87a2-b0a8979c1074","optionName":"concatExCurrency","value":"tw.local.concatExCurrency"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2799564-2083-4aca-87e0-4bca4be0aef9","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b8e10fc9-d07b-4e46-8ee8-358c216e4ebf","optionName":"isFound","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"25b98154-2944-485b-8764-e2f7be38c9c0","optionName":"commCIF","value":"tw.local.commCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"addedac9-7134-4cc5-86cf-6df490ece58e","optionName":"accountIndexC","value":"tw.local.accountIndexC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2c1e6c9-c3c9-4d03-85b7-5389326de048","optionName":"isGLFoundC","value":"tw.local.isGLFoundC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"78f6313e-9796-4fba-81c9-5c1dba8e9199","optionName":"CommAccountList","value":"tw.local.commAccountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"60bd9c77-3816-4dc4-859c-511ace239a47","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8006c537-9812-43ac-8aeb-1f13673612a0","optionName":"commClassCode","value":"tw.local.commClassCode"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ffceb684-1360-4f17-80ca-3cb89834e477","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.e45b18e8-a83a-4484-8089-b2ab3c33146a","binding":"tw.local.idcContract.commissionsAndCharges[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7fb63c08-1540-4314-8542-1aff8bc5f117","version":"8550"},{"layoutItemId":"Limits_Tracking1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4f2bc48c-0498-4ec6-8ff2-0408da211d22","optionName":"@label","value":"Limits Tracking"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f3b814cc-97f1-4d7c-8b5c-311ed9408d4b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1b363a52-cd38-481e-8478-5744d808a0c2","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ed64ba82-5045-4cca-8ff6-3765782f0c4f","optionName":"SectionVis","value":"tw.local.facilityVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"156110c5-665c-4a16-8a8d-5533acb3a840","optionName":"facilityPercentageToBookVis","value":"tw.local.facilityPercentageToBookVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b547fde6-1d2f-4809-82d0-4315db8d65c8","optionName":"facilitiesCodesList","value":"tw.local.facilityCodes[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c94cf460-e182-47e6-890b-32639a6204c5","optionName":"facility","value":"tw.local.facility"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"86cea20b-51f9-4a9a-85b4-d8c5394cc7ba","optionName":"LimitsVis","value":"tw.local.idcContract.limitsTrackingVIs"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c43bbe91-dc9b-4843-8594-8e9acd733a88","optionName":"limitMessage","value":"tw.local.limitMessage"}],"viewUUID":"64.9e7e0016-3899-48ff-9db4-bea34bea40f0","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ffea0abc-6037-4d8b-81a0-bb7c28849ad2","version":"8550"},{"layoutItemId":"7","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f9b29a0-c593-4812-8fcf-cceb171144fa","optionName":"@label","value":"Parties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"08879760-2255-4c7b-8f03-366b8d8de561","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2dd4480-0c4e-46a6-83a8-709a8aed182a","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9505b718-1bdd-4c08-8b9f-36aa4d36a71a","optionName":"partyTypeList","value":"tw.local.partyTypeList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dba6049a-8c7c-4435-84cb-1526c0297b11","optionName":"concatString","value":"tw.local.concatString"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5d702eb3-9508-4410-814e-63523033cd88","optionName":"BeneficiaryDetails","value":"tw.local.BeneficiaryDetailsOption"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"382bd8d2-0476-4218-8bc0-202fd2f7bffa","optionName":"partyIndex","value":"tw.local.partyIndex"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8e69a4ee-7da0-4242-8ae5-e872ee1934b2","optionName":"customerFullDetails","value":"tw.local.customerFullDetails"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"96ab7b64-32be-4694-8171-c04a5bf62bd0","optionName":"selectedBIC","value":"tw.local.selectedBIC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a72ab21-08a4-4c32-8b08-f2b3d4859d8e","optionName":"customerCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b21f96fb-ebe2-4b1a-87bf-881c18348948","optionName":"accounteeCIF","value":"tw.local.accounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf9fbf00-d787-4c00-8f81-c4ef47151bfe","optionName":"caseCIF","value":"tw.local.caseCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0b008e47-501c-4438-8f81-f8a997b6f390","optionName":"addressBICList","value":"tw.local.addressBICList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"06ea273d-dbfd-4895-8b62-3b3361a9049d","optionName":"selectedCIF","value":"tw.local.selectedCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8a664dc1-2be4-4980-8feb-571c697434da","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d7fb8922-d23c-40d9-8b9c-7bfc5a7200e7","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f","binding":"tw.local.idcContract.party[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5be280e6-cc8a-441e-8ca3-8517d37cd406","version":"8550"},{"layoutItemId":"5","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18bb9618-6804-470b-8a5f-3935516fffa6","optionName":"@label","value":"Contract Creation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4f3173e8-f96d-49ce-8bd5-3bdb6437238d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"07af5670-9b11-43f1-830a-14e2907c2a19","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6501672c-0306-41ec-8900-791ebd623aae","optionName":"@visibility.script"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20939ee9-c6ca-44b6-8b70-7c3a8207697f","optionName":"contractcashCollateralsVisability","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f2062916-933d-47c6-8765-4bacee118794","optionName":"contractLimitsTrackingVisability","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5ad7b207-0de6-4b72-8edd-ca13c2387722","optionName":"advicesVisability","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b84d6649-a2f5-45b1-81bc-5ff6779d6e69","optionName":"contractInterestDetailsVisability","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02dd1729-ebcc-413d-8bbc-ee453d3ca03a","optionName":"contractTransactionDetailsVisability","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"aa797eb9-fe0c-4cd2-8ccc-5631c3884ae2","optionName":"contractcashCollateralsOption","value":"tw.local.contractCashCollateralsVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"32e3857b-851a-48e8-8146-292dea7e6e9c","optionName":"advicesOption","value":"tw.local.contractAdvicesVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d1eb0835-592c-48c5-8096-ef7085addfd8","optionName":"contractLimitsTrackingOption","value":"tw.local.contractLimitsTrackingVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6c6073b2-f9f4-4620-8fd9-2f14d674c860","optionName":"contractInterestDetailsOption","value":"tw.local.contractInterestDetailsVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"713dfd71-58b8-4fff-8afd-b286da42dfd0","optionName":"contractTransactionDetailsOption","value":"tw.local.contractTransactionDetailsVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8a048e75-9004-4180-8dce-f9ef119c16d8","optionName":"partyTypeList","value":"tw.local.partyTypeList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f4c9be11-52b5-456f-8c17-b1d48015153d","optionName":"concatString","value":"tw.local.concatString"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"426fe51c-115a-47a6-8559-3bac602d7de8","optionName":"BeneficiaryDetails","value":"tw.local.BeneficiaryDetailsOption"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3f4c99b4-1ebe-4683-84ac-556afffc3aeb","optionName":"partyIndex","value":"tw.local.partyIndex"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5e7bae6a-fa3a-4cb7-80c9-4becdd237465","optionName":"accountNumberList","value":"tw.local.accountNumberList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c92c062-d080-4ca2-8d81-9143e6994837","optionName":"accountList","value":"tw.local.accountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64ecd55f-f26e-46e0-8d0c-c5b7da5fceca","optionName":"customerFullDetails","value":"tw.local.customerFullDetails"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e5cb0ee5-a910-452d-8d9d-9e22d7d30c46","optionName":"isFound","value":"tw.local.isFound"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7a6e75e0-59f7-47e8-8e7c-f7bb5c56bbf7","optionName":"isSuccessful","value":"tw.local.isSuccessful"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6c9a159d-d0ec-41c0-8bf2-efefd25c7092","optionName":"customerCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df534c8d-6d5c-44c0-8c97-6cdd0b7b2d75","optionName":"selectedBIC","value":"tw.local.selectedBIC"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a6eff8e2-882a-4e4b-8410-91d552225a3c","optionName":"adviceCodeList","value":"tw.local.adviceCodeList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"14eae105-53b4-402a-85d7-c825140b1a24","optionName":"tempObj","value":"tw.local.tempObj"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1ef370be-7c88-4aec-8220-2b6071ca1dbe","optionName":"partyAccountList","value":"tw.local.partyAccountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a4a9a20e-4474-4522-840a-cc09eadb5095","optionName":"sumAccountList","value":"tw.local.sumAccountList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"********-f308-4cb7-8c14-257a0292abfb","optionName":"accounteeCIF","value":"tw.local.accounteeCIF"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2343705b-8105-4141-8285-61e1107c9387","optionName":"caseCIF","value":"tw.local.caseCIF"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a122ddc7-fa1a-4896-8b72-cd807112e620","optionName":"exCurrency","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2493079d-569a-4c06-8847-89d442b0f918","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1a0c0b81-6a4d-4265-8e8d-a69d73d2844e","optionName":"partiesOption","value":"tw.local.partiesVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"edb35141-ffab-4ec3-89e2-07568ddbcab6","optionName":"requestCurrency","value":"tw.local.idcRequest.financialDetails.documentCurrency.code"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"671e20e6-91b2-46f6-80d1-37ece62ee6ee","optionName":"partiesVis","value":"tw.local.partiesVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c8eada5c-8713-4f53-802d-3b3f493ba43c","optionName":"amtPayableByNBE","value":"tw.local.idcRequest.financialDetails.amtPayableByNBE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4c20f3aa-469e-4287-8ee9-9168295b01a2","optionName":"intDefaultAmount","value":"tw.local.intDefaultAmount"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"91a8b60c-f9b7-4282-82ea-515565847ebd","optionName":"minAmount","value":"tw.local.minAmount"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2072430e-1805-48bb-85cb-763b93be735c","optionName":"chargesIndex","value":"tw.local.chargesIndex"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c4d7fce5-d81c-451e-8b4f-d0afd6fa128b","optionName":"concatExCurrency","value":"tw.local.concatExCurrency"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"779cd5d4-fe37-46be-8dd7-1d28d01a9a67","optionName":"InterestAndChargesList","value":"tw.local.InterestsAndChargesList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4d4caf01-1c36-4702-8f37-5a7c0d6120fc","optionName":"concatInterest","value":"tw.local.concatInterest"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d3251b62-5d3f-4106-821d-b99856a64693","optionName":"contractAmount","value":"tw.local.idcRequest.financialDetails.documentAmount"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"03ab382e-d7e2-4105-8666-03e46d638539","optionName":"isGLFound","value":"tw.local.isGLFound"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df1b2d28-1bae-4353-8628-23855614e297","optionName":"accountIndex","value":"tw.local.accountIndex"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"********-5395-4cba-8c0c-7dee0f15dd41","optionName":"isChecker","value":"tw.local.isChecker"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"299eb5b5-604f-448d-8267-4ef74c66a63c","optionName":"stage","value":"tw.local.idcRequest.IDCRequestStage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c177eb7e-6e8a-41c7-8aaa-40ca0381474f","optionName":"adviceData","value":"tw.local.adviceData"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d9286620-a30b-42f3-89bb-9ee7deee50e8","optionName":"concatComm","value":"tw.local.concatComm"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19ff959d-5f66-4330-85a1-917bdae427ce","optionName":"facilityCIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6f57593-bc90-43a1-8ed2-a5c978fc4655","optionName":"errorMessage","value":"tw.local.collateralError"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f6501ac0-a7bd-4c52-8d21-a5593e7282c9","optionName":"selectedCIF","value":"tw.local.selectedCIF"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e80e0656-7510-4f46-8c61-9242d87ffa22","optionName":"Suppressedvis","value":"DEFAULT"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e86aa518-d554-4dca-81f5-5c596c60c951","optionName":"limitedPartyTypeList","value":"tw.local.limitedPartyTypeList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"649ed432-c019-4791-8d84-fc525847aa2b","optionName":"interestIsVisible","value":"tw.local.interestIsVisible"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b42460c3-abaf-468f-8e8f-c382febc5d17","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8ddda15b-cd8f-4b7d-8e68-c91cbfe99f78","optionName":"errorVis","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5bff2054-55c0-4763-8b7f-312ae094f05c","optionName":"interestVis","value":"tw.local.interestVis"}],"viewUUID":"64.0fa21995-2169-498c-ba2e-ea66c3dc5616","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"8af214e5-2414-49b8-8184-b89ef5072a61","version":"8550"},{"layoutItemId":"8","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"70bd94e2-d55b-4520-8ddc-611014d819d8","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a0a35733-1c07-4350-81b3-1b3a98634d4b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bec6560e-edbb-4b96-89ea-fb57d0e8b4d4","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0498b79b-41c7-432b-8869-1b7acd06eee9","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3303900c-d707-4c52-889c-251950ae03de","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"341d3618-5218-4c6d-80eb-1b0dfec19c98","optionName":"canDelete","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0a6f1428-727f-470d-8fbd-a2cf57f31674","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d3fd88a-c1e0-4336-81c1-bbfa68221530","optionName":"visiable","value":"true"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"291f4763-69e7-451b-8425-32e93586b2d0","version":"8550"},{"layoutItemId":"9","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"68c431e7-3d8b-4511-8373-692c51fc975e","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e3f55ee2-f36c-4682-87bb-b70e215b148b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"05f99566-66b8-4484-8db1-c6bd4ae15a23","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"67cf9621-cd0d-4fc0-811b-67d776f76ff5","optionName":"historyVisFlag","value":"None"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.idcRequest.appLog[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d8b5db02-af64-423f-869c-8062dd9a3c68","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"3f6ef11c-0219-448f-864c-af365b38bbfe"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5a2ba6db-4ab3-4acc-805c-65e18e31ea49","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f9673b2e-307f-428e-87e2-c09d3df53b14","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"56311dd8-9633-47fb-81fc-3ea3e918dbdd","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6066ea31-de2c-48ae-8a9a-1f299820b4d9","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"10d37d44-cd63-4c8b-8eff-a7267cd08bfe","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"74b65e3a-8349-445c-8bb9-1e787ad4da64","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"f2cf4863-c7e9-42ef-8015-4f0ad99d2f5c"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6d6c909-2408-45dc-8156-936f2543098e","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9fef8957-c93e-46e7-8625-1241217e393c","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e7a3f561-8cdd-4494-8fd6-7ca9e5d0f0ad","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7ed95949-6f4c-49e8-8301-8aa5e28d159d","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf478edd-25af-4810-80eb-f234f9afc459","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b3dc2375-ec36-4d4c-82d3-7b5af94635cf","optionName":"selectedAction","value":"tw.local.selectedAction"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c2b22392-0f3d-4e69-8cd7-1802595aa935","optionName":"hasApprovals","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c88bdaba-c851-4ab3-850a-782b81e026b9","optionName":"hasReturnReason","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1263330d-1074-4b07-887a-9b94024763b4","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6d28034e-8ae9-43cb-8bd9-ca18aeaae19f","optionName":"buttonName","value":"Submit"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"abb0f1dd-d3af-45ad-8b37-15866a461e05","optionName":"approvalsReadOnly","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"58828f13-8a3b-4771-8eb5-77d870bcb588","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"704ee87c-5b91-46b6-88ab-85265409cf6e","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2202459d-7158-420e-8ca1-f0bf3a5dac8f","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f258f858-b510-424b-8190-9f8d42fc30ec","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"IDC Execution Hub Initiation","isForCompensation":false,"completionQuantity":1,"id":"2025.7138dc8f-1707-4eb8-8721-15624a4ff914"},{"targetRef":"2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"4aef87be-8491-442e-8991-056f8307fefc","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validation","declaredType":"sequenceFlow","id":"2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0","sourceRef":"2025.7138dc8f-1707-4eb8-8721-15624a4ff914"},{"targetRef":"2025.7138dc8f-1707-4eb8-8721-15624a4ff914","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Execution Hub Initiation","declaredType":"sequenceFlow","id":"2027.afbaa6a5-e738-4c50-84c2-6638602bfba7","sourceRef":"2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"e43aba5d-e2a0-41a7-9bbe-dc9522b67b82","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"2059fd80-789d-48a7-9f0f-93a0f12cb094","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"IDC Execution Hub Initiation 2","declaredType":"globalUserTask","id":"1.9e0de1a3-7709-49c8-9b29-963ea4784c51","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.036382d6-78e6-4da7-9eed-0aa5d5d93300"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.ab4deee4-4ad4-4756-933a-86b2c19fde70"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.26756e9b-2946-4377-9b62-43e6ffbffa25"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"dc15769d-ecc6-4ec1-8bcb-abf64ee084d3","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"9f9b80af-eb57-4778-8882-1ef810d6a5e6","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"b20b243e-e71a-469d-8bf0-295daf432767","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.e5829eee-0ab1-4f47-9191-f0f8705bc33e","epvProcessLinkId":"cef89e5c-a12c-4df5-8b24-1929b0909dd5","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = {};\nautoObject.customerInformation.facilityType.id = 0;\nautoObject.customerInformation.facilityType.code = \"\";\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\nautoObject.customerInformation.facilityType.englishdescription = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new Date();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.0d99a3e7-a8de-4163-b3fb-55786de20aa1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = [];\nautoObject.settlementAccounts[0] = {};\nautoObject.settlementAccounts[0].debitedAccount = {};\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = {};\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\nautoObject.settlementAccounts[0].debitedAmount = {};\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = [];\nautoObject.settlementAccounts[0].accountNumberList[0] = {};\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = {};\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = [];\nautoObject.party[0] = {};\nautoObject.party[0].partyType = {};\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = {};\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = {};\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new Date();\nautoObject.liquidationSummary.creditValueDate = new Date();\nautoObject.IDCProduct = {};\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new Date();\nautoObject.transactionMaturityDate = new Date();\nautoObject.commissionsAndCharges = [];\nautoObject.commissionsAndCharges[0] = {};\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = {};\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = {};\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = {};\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = [];\nautoObject.commissionsAndCharges[0].commAccountList[0] = {};\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new Date();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = {};\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = {};\nautoObject.swiftMessageData.intermediary = {};\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = {};\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = {};\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = {};\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = {};\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = {};\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = {};\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = {};\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = {};\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = {};\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = [];\nautoObject.advices[0] = {};\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = {};\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = [];\nautoObject.cashCollateralAccounts[0] = {};\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = {};\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new Date();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = [];\nautoObject.contractLimitsTracking[0] = {};\nautoObject.contractLimitsTracking[0].partyType = {};\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new Date();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = [];\nautoObject.accountNumberList[0] = {};\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject.facilities = [];\nautoObject.facilities[0] = {};\nautoObject.facilities[0].facilityCode = \"\";\nautoObject.facilities[0].overallLimit = 0.0;\nautoObject.facilities[0].limitAmount = 0.0;\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\nautoObject.facilities[0].availableAmount = 0.0;\nautoObject.facilities[0].expiryDate = new Date();\nautoObject.facilities[0].availableFlag = false;\nautoObject.facilities[0].authorizedFlag = false;\nautoObject.facilities[0].Utilization = 0.0;\nautoObject.facilities[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines = [];\nautoObject.facilities[0].facilityLines[0] = {};\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].expiryDate = new Date();\nautoObject.facilities[0].facilityLines[0].facilityBranch = {};\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines[0].LGCommission = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches = [];\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts = [];\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = [];\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = [];\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\nautoObject.facilities[0].facilityLines[0].partyType = {};\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\nautoObject.facilities[0].status = \"\";\nautoObject.facilities[0].facilityCurrency = {};\nautoObject.facilities[0].facilityCurrency.name = \"\";\nautoObject.facilities[0].facilityCurrency.value = \"\";\nautoObject.facilities[0].facilityID = \"\";\nautoObject.limitsTrackingVIs = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.d52aaa6a-fe03-486b-a2ec-bf603fe7aa57"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.ca1a49de-f726-4195-afa3-86c1831cf286"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.b1ad4f17-5a19-4eec-a57c-188f58f96e72"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"complianceComments","isCollection":true,"id":"2055.646e6c2d-aa08-4fa8-adca-c95fecc02155"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].startTime = new Date();\nautoObject[0].endTime = new Date();\nautoObject[0].userName = \"\";\nautoObject[0].role = \"\";\nautoObject[0].step = \"\";\nautoObject[0].action = \"\";\nautoObject[0].comment = \"\";\nautoObject[0].terminateReason = \"\";\nautoObject[0].returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.30405cca-92a3-4efd-a7aa-5370d77ebb9d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.fullPath = \"\";\nautoObject.cmisQuery = \"\";\nautoObject.defaultProperties = [];\nautoObject.defaultProperties[0] = {};\nautoObject.defaultProperties[0].name = \"\";\nautoObject.defaultProperties[0].value = null;\nautoObject.defaultProperties[0].editable = false;\nautoObject.defaultProperties[0].hidden = false;\nautoObject"}]},"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.d5114bbb-7d27-4d40-a232-641034e87d67"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.bcfcce29-02b7-4e2e-a443-1199515bbf9b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"facilityCodes","isCollection":true,"id":"2055.a9e71516-841c-4e44-a1b9-b68f71618f8f"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.aef0c576-1ef4-4e84-aca3-959c9eab5682"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0d99a3e7-a8de-4163-b3fb-55786de20aa1</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e1caa8d9-5594-418b-a690-081d21f7ffd3</guid>
            <versionId>155e5a8b-9c7a-48f4-8d06-f694bb4091e4</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d52aaa6a-fe03-486b-a2ec-bf603fe7aa57</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b061678a-59c5-4b22-8b6b-542cf89142f1</guid>
            <versionId>98d377d1-fffa-4abe-8637-613a849aae7b</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ca1a49de-f726-4195-afa3-86c1831cf286</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>be49c4b0-45d4-4373-acca-1bd11c2eb0a7</guid>
            <versionId>78c762fb-4680-432b-a959-8554d885871b</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b1ad4f17-5a19-4eec-a57c-188f58f96e72</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8875aca4-3bd9-4f08-b65b-5af9b01e8a48</guid>
            <versionId>b165bab6-d9f4-4084-b4d3-87eb7cd2d076</versionId>
        </processParameter>
        <processParameter name="complianceComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.646e6c2d-aa08-4fa8-adca-c95fecc02155</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>52cb549c-e577-4fef-a232-d90b5154203b</guid>
            <versionId>8cc2825f-153a-462d-b404-b12973c4f82a</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.30405cca-92a3-4efd-a7aa-5370d77ebb9d</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8f3cd5c3-da9b-444f-9330-dec7b80b2396</guid>
            <versionId>084129a5-0524-4787-985e-4d62290e9d02</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d5114bbb-7d27-4d40-a232-641034e87d67</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>584f8699-b267-46ef-80c9-738a7dc4b211</guid>
            <versionId>7f4b07ae-950c-45d4-9026-3820d2e0abcc</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bcfcce29-02b7-4e2e-a443-1199515bbf9b</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4d95bbfb-d8e7-4cb9-8151-65b42beffb12</guid>
            <versionId>8d79c034-366c-4ca1-8f9d-f89c9026c2bc</versionId>
        </processParameter>
        <processParameter name="facilityCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a9e71516-841c-4e44-a1b9-b68f71618f8f</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d134c7f4-7602-4b12-8a4e-43fc7416a8f1</guid>
            <versionId>3ba49a59-6a0e-4bcb-9dc7-b065f3d7ac44</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.036382d6-78e6-4da7-9eed-0aa5d5d93300</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ad7e8643-1df1-4d46-8fbe-1f76c8988f9f</guid>
            <versionId>b2e3a927-62ae-431c-9f04-1bba01541cdf</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ab4deee4-4ad4-4756-933a-86b2c19fde70</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>905eff17-d292-45bc-b688-16a2dd3bff58</guid>
            <versionId>3afbb9d1-f889-446d-a980-b64496196dac</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.26756e9b-2946-4377-9b62-43e6ffbffa25</processParameterId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>12</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d0b7ae8f-8b0a-42fc-9662-efb57e606eef</guid>
            <versionId>9a0a0adb-d5f5-4342-8f0d-49913a2c0b36</versionId>
        </processParameter>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ebf2625e-51cd-4f8d-bdfd-ed7bae08b22b</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a15395ad-1398-4c79-812b-1ddba9ab64ff</guid>
            <versionId>2421e20a-46b8-48fc-a125-857c4d19e20d</versionId>
        </processVariable>
        <processVariable name="contractInterestDetailsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3ddbd5c6-f9a5-47d1-98c8-faf818bc4ab9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>72e6702a-0701-4629-b031-9dff9ec2af8f</guid>
            <versionId>1e30b6a9-8cab-4739-bc60-2b6effb68fa8</versionId>
        </processVariable>
        <processVariable name="contractCashCollateralsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8b2f5baa-46c9-4a7c-83d6-c472846350e1</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ecf8cce2-f4e4-4ef8-bbd6-9aace895ea84</guid>
            <versionId>6fd194c5-d645-4b94-8567-7591feed5e38</versionId>
        </processVariable>
        <processVariable name="contractLimitsTrackingVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eee297e2-e33d-46a6-a23a-6e6882c58b51</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b10d89e2-2500-4141-ad57-7f3d335dbc6d</guid>
            <versionId>6e0b8052-aa33-41c1-bb70-ff8a01234368</versionId>
        </processVariable>
        <processVariable name="contractAdvicesVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9f9d5d04-364b-42e4-9edc-edc5c92ab772</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b37966de-621b-47ab-9b9f-78b71debaf3b</guid>
            <versionId>e78c2955-239c-4d55-aaa7-49c81f04889c</versionId>
        </processVariable>
        <processVariable name="contractTransactionDetailsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9ff7ba4a-6b6a-49cf-b6bb-ad0e9701ef38</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b2232105-a2a3-4b71-9acd-b2bacbf16b67</guid>
            <versionId>840cc29d-f58c-4ad0-be06-3ea8c2a1a00b</versionId>
        </processVariable>
        <processVariable name="advancePaymentsUsedVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e02bf9fd-e2e4-4827-8661-0ca1511b2105</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5102b038-4b6e-4be5-9a10-b853bd456d4c</guid>
            <versionId>239270b8-187f-412c-9310-2ede8e772cf7</versionId>
        </processVariable>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.841104aa-2225-4d72-8e37-f015d834ffbb</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3acbf00b-58f0-46d3-8b70-49cc002cb27d</guid>
            <versionId>449c7d8e-c14c-4107-bdb5-23d954b6f786</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8b9b8ce3-9933-4b67-a696-88f14c673b7a</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8560467f-d967-4eee-9ff9-3d2a09ae5cb6</guid>
            <versionId>0b32f577-3edf-4fef-b716-18f5d64c4045</versionId>
        </processVariable>
        <processVariable name="partyTypeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a5652337-27bc-4562-a4c3-aa60223ddabd</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>995610a7-1559-4528-8d67-e7ad1d3d004b</guid>
            <versionId>596a95d5-6ebb-4767-9ff6-078344574e9d</versionId>
        </processVariable>
        <processVariable name="concatString">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9644ec97-63e3-43c4-b7aa-3b784db3c80f</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c21d40f5-d9e2-4551-bf01-da187a9b8dda</guid>
            <versionId>62d07758-f954-494d-b21d-b95271347f31</versionId>
        </processVariable>
        <processVariable name="BeneficiaryDetailsOption">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c2f8a6cd-ff7c-4b3c-8926-1221b9631245</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a025468c-38bc-4809-b337-57da9e95dacb</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0efb0b68-a5c7-4cc2-b4a1-860bd615ae0c</guid>
            <versionId>0b921a7f-5a48-4d63-9056-dded12d71d5b</versionId>
        </processVariable>
        <processVariable name="partyIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.65a726ef-6236-4b6b-8da8-4cbb496c841e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>488607d3-f888-418a-80b6-1d950098ab5a</guid>
            <versionId>69684254-6ed2-41ed-9f56-c56a7125fd25</versionId>
        </processVariable>
        <processVariable name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8a7ace65-b33e-4420-93f3-1bf3437bba7b</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>07ec9007-6e49-4c73-8a06-3c967df7ca8b</guid>
            <versionId>690b6a32-71f0-46e7-bbfd-0a3d22189ae1</versionId>
        </processVariable>
        <processVariable name="accountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1ed5bdf9-280d-4ee3-bd6f-f7f5af23f215</processVariableId>
            <description>&lt;div&gt;&lt;br /&gt;&lt;/div&gt;</description>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ebf33651-02f4-4c50-9011-654733e6ece3</guid>
            <versionId>7558dc62-af24-47f3-8079-c96dc2d9c531</versionId>
        </processVariable>
        <processVariable name="accountNumberList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a923395-c931-45eb-93c3-d42bcc156340</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6ccf973e-56c0-402b-8ba7-3536d6bee6c9</guid>
            <versionId>76fdabc4-16fe-4388-bd4f-f921994eaaa7</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f687ac51-6bf9-4bf0-8a14-73f61f74ba28</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b1f3f859-dff1-49e0-b446-b946e2036c4c</guid>
            <versionId>be48a7c1-4782-46b9-bf83-93bb94607e99</versionId>
        </processVariable>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ff0287cf-0ce4-44f4-85ba-d4b6a2a2947d</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2dc25b79-9fc8-4e60-935d-36052e2b3545</guid>
            <versionId>5a04a176-bb70-4e0f-b100-e9f476d684b2</versionId>
        </processVariable>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bfafc277-f866-4dcd-96ce-8138f68ca691</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8f1833a3-16b0-483a-a0f3-af764726dfc2</guid>
            <versionId>7ab41f19-1934-49c3-9a84-2585326d5ed9</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b2b75484-ee9d-4c50-93ee-93a2552988b5</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c6ef5a81-6ba9-4a0f-ad43-faf0444b5bfa</guid>
            <versionId>4d7ac53a-59c3-473b-93b3-1bd00ef7fdfb</versionId>
        </processVariable>
        <processVariable name="exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.34ab7fee-8079-4d23-93fa-8efcacf2b686</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>01bc20ec-f225-428a-96b4-81180fd03b0e</guid>
            <versionId>44bb61dd-7740-4cb0-afba-00291a13a92e</versionId>
        </processVariable>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.16768f40-baac-4f67-b4c8-1515e214acf9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8f51b31-42c9-437b-ba7f-1573d2be20a3</guid>
            <versionId>64666e95-e06c-48a0-9ffe-228a9400b337</versionId>
        </processVariable>
        <processVariable name="isGLFound">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0a9ecfc1-5bfa-43cd-8a58-5da87970d8b0</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7022da1f-a180-4d28-a6f8-30a4edbc27d7</guid>
            <versionId>798fe7dc-04cb-40af-90f2-52eaaf03f967</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.946b98e1-c28f-48ee-a16a-7df32b75a87f</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>913690f1-9b3d-4aea-8701-dbfd943d72dc</guid>
            <versionId>1f6bef52-d070-4497-af7d-182a1e6acc19</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dc0493e1-914d-4c9b-bc17-df6382964e73</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2c4a6d03-0b4f-475f-980b-1643481c823a</guid>
            <versionId>b5c27181-a77c-47a2-9a08-03ff036fb0d0</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d4fafa4b-5e4e-4046-ae4e-e267a1b27c43</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8f8bafa7-bba4-4770-b50f-a3234f8a6801</guid>
            <versionId>0ed5162a-4c36-4679-953a-cc9e2e4c0f39</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fcb2c3c9-109f-42f6-9486-16cb1c790266</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7cd1127f-66f3-450e-bef9-caae7d51b5ef</guid>
            <versionId>0847023a-5c9e-48d3-a941-37dee2e4af54</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.665d3d2d-0218-4c74-a461-ba2515fb91c6</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9b4e075c-a44a-4705-a397-2b4eb5be865d</guid>
            <versionId>4d87ca34-5f82-4932-b831-93a690efc6ed</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b8c585b2-9c44-4d3b-87fb-235a191a2988</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>10b42b79-97bd-4a2e-9881-d24a5635fbfe</guid>
            <versionId>31995a44-afe1-46ce-81aa-6116d3792578</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2573c8bf-c93e-473b-b648-e550a03b8d2e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>154ebad9-f379-4079-8d15-ab7932bc25dc</guid>
            <versionId>2cff6060-02c7-4478-8468-350ba12a3206</versionId>
        </processVariable>
        <processVariable name="havePaymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4f15f032-dad4-49b8-8698-f77827a03ff3</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>31</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1b5eb025-a4f8-46ae-b0fc-fb4d610929aa</guid>
            <versionId>7d8651ec-5596-4e41-9901-220178280c5c</versionId>
        </processVariable>
        <processVariable name="selectedBIC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d2edf67-9e17-4c95-b706-c3df33c9e8d8</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>32</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eab73588-effb-4d95-97f2-abae238a686f</guid>
            <versionId>8b4a323b-0cd5-4037-9492-c1d9bf04dc0a</versionId>
        </processVariable>
        <processVariable name="chargesAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.66da0014-9bfa-4595-9f60-db019e021a8e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>33</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>25d2ad03-f51d-4446-81b8-f39937255060</guid>
            <versionId>b660188f-40c6-4d0e-bec5-012c3cecde36</versionId>
        </processVariable>
        <processVariable name="adviceCodeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e3ed63c0-8121-4455-b3d2-5f1f21ce4f4f</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>34</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2f7d3b74-5535-40dd-8f4f-97c75fa18696</guid>
            <versionId>efe3c7e1-de2c-430c-a216-dc42fd540df0</versionId>
        </processVariable>
        <processVariable name="tempObj">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5bc4fd52-e3b2-4170-a76e-187aa172e20b</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>35</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>65306af3-ba0a-4eac-a1fd-4b2ad30ba497</guid>
            <versionId>ec37af8e-e282-4f74-9d4c-858c37190f25</versionId>
        </processVariable>
        <processVariable name="partyAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7137ff16-5afa-4666-ad46-4b64043754ec</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>36</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc010c12-5019-4f35-9b56-63d3923d5b5f</guid>
            <versionId>afc1d48d-ec46-4946-b880-6df2cc7e2252</versionId>
        </processVariable>
        <processVariable name="sumAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.338ceda7-f7d7-4489-9518-d6660e4ebb53</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>37</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>557593db-6099-4562-ac9b-f521e4018147</guid>
            <versionId>4cdd6b5e-13ce-4693-bf8f-44ca2c3ab9dd</versionId>
        </processVariable>
        <processVariable name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bafa5084-67d2-44a1-b111-0ef024850793</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>38</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>98d3f3db-0bdb-443c-bc75-4c9c5db9ded4</guid>
            <versionId>4c63b0ce-9bba-434e-b924-4014d0fcd175</versionId>
        </processVariable>
        <processVariable name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.369c4936-0e4f-4815-bddb-3710f9d5f6c9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>39</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4009c2b0-f985-46f6-b781-30b201699d8d</guid>
            <versionId>b4716ac9-4b9a-4dae-a6c8-1fde726b4a89</versionId>
        </processVariable>
        <processVariable name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.171a12f7-e907-4353-be80-637dc742f7e9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>40</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2c513396-a9e8-4295-b045-e1e6c2d693ed</guid>
            <versionId>f922f7bf-fdba-471a-9abe-9b5b7083714c</versionId>
        </processVariable>
        <processVariable name="draweeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.79af2607-11e0-4a15-83f1-492ffcdbc611</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>41</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2ed873f6-6b3d-4958-a2fd-484a708d6086</guid>
            <versionId>464267c0-5f60-428e-ac11-1412bb85904f</versionId>
        </processVariable>
        <processVariable name="tempCommissions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6e172f54-cb2e-4136-82a8-723f0d2fb01a</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>42</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.89a53d06-50b8-41df-a5cc-5e4f61147b6d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>76216469-4c5a-47db-a6ae-4728ab77f204</guid>
            <versionId>bd70428c-ef50-4fb8-80e1-90c2e806727f</versionId>
        </processVariable>
        <processVariable name="partiesVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4b9a2570-dfb1-4b0c-ab1c-74be76bfda38</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>43</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b79479ef-50ea-45c2-ac38-6f9e362e5dd6</guid>
            <versionId>8004dc58-68fc-4f63-8299-acd405799ced</versionId>
        </processVariable>
        <processVariable name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b8ce5d34-3ecd-4e3d-b20f-f7819f84383e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>44</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e8cf6064-cd33-4c02-a1f5-7f84693cd17e</guid>
            <versionId>29d0d85d-c5c0-49fd-a07c-507875e1b6b4</versionId>
        </processVariable>
        <processVariable name="liqDone">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4cfb1ce5-6911-46f8-b306-f58edc11c771</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>45</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0f660bd-78c2-4a3e-8fae-8dde7fef610d</guid>
            <versionId>fd6a876e-54b8-4609-a8d1-0741e2bb7688</versionId>
        </processVariable>
        <processVariable name="amtPayableByNBE">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.06a0d82c-0930-4399-bd5f-bfa6be68158e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>46</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>08acf541-682d-4708-a3b9-b18544ecddd4</guid>
            <versionId>9422f1ca-9eb6-461a-89fb-************</versionId>
        </processVariable>
        <processVariable name="intDefaultAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.47f2a416-723e-4fa5-add1-38f6b36f9bb9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>47</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>015b5952-e626-4687-af08-b9d3b5cf4595</guid>
            <versionId>1d80ba1b-b35e-4ba3-89a0-fdb15c24e0b9</versionId>
        </processVariable>
        <processVariable name="minAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.db14b40c-f2fe-4564-baaa-989a20160b7e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>48</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4f72aa3e-0f2f-4d52-963d-469fd4a1a09a</guid>
            <versionId>f77e54d5-8651-41f8-9a0f-fe4ff8090517</versionId>
        </processVariable>
        <processVariable name="chargesIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.*************-4eda-94e5-c5a8eed9086b</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>49</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a9737647-2528-4b4f-99ca-d06b069e5f78</guid>
            <versionId>c8547a85-45a7-4705-afc1-aa17a634ea35</versionId>
        </processVariable>
        <processVariable name="concatExCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8f6675f6-457c-4249-a083-e230ba345552</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>50</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ee44d16-28df-4ed4-9e5b-d9c20c54d046</guid>
            <versionId>34b2cc85-6517-4661-aac8-c06ca1983f47</versionId>
        </processVariable>
        <processVariable name="InterestsAndChargesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e5559355-c9c7-48e3-8e03-32b760d966c5</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>51</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2d291efa-ea3e-4b6c-bab5-05149d371c1c</guid>
            <versionId>35aaa8c6-d366-4644-bd2a-74b6a939e5ba</versionId>
        </processVariable>
        <processVariable name="concatInterest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d874db2e-9bed-4502-b05f-cbe98eb0fc2d</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>52</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>423f3a15-6321-4fc4-b30d-c2b51579e135</guid>
            <versionId>b0553a68-5857-42cc-9680-08b2b6a4b560</versionId>
        </processVariable>
        <processVariable name="contractAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4aa8924c-0139-402f-800c-6bb769875d65</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>53</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>775cd220-ede2-404e-ac77-bfaa236c92e6</guid>
            <versionId>59f4dd09-7ac8-4597-bcd6-f4aaba1f6d13</versionId>
        </processVariable>
        <processVariable name="accountIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.04dcc23c-338b-4a97-ac8f-310da2497297</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>54</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>29f123fd-443c-4631-9cf5-2199de98e38c</guid>
            <versionId>8c1908d7-6453-492b-8e32-2c893bb2a6b3</versionId>
        </processVariable>
        <processVariable name="adviceData">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c209b161-1758-4b88-b3b1-bc27cee41cb0</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>55</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>75f4dd34-ea44-4fa5-8698-5b26b5b18fe0</guid>
            <versionId>*************-49bc-b4ac-afaacdf38fca</versionId>
        </processVariable>
        <processVariable name="accountIndexC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d75ba1ad-3a9c-46e0-b31a-ae6faf51dd05</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>56</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f573e6f7-961b-44dc-9c22-6bd6c60d6c6e</guid>
            <versionId>********-ba76-4fac-8e48-deecf2ffcb64</versionId>
        </processVariable>
        <processVariable name="concatComm">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7665f354-07f6-4023-8000-f5e37be7bd51</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>57</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ac329044-c092-42d4-a8fb-83f48dd8a74c</guid>
            <versionId>4bc04f98-9f99-4432-a8d0-d855adb1f4bb</versionId>
        </processVariable>
        <processVariable name="facilityCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.02d34944-4b00-4b46-b810-2eb8378660df</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>58</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ddd3e8f-b706-440a-9a67-b5af5e707605</guid>
            <versionId>ba9a725c-4fe0-4af1-93d5-f4867dc3f288</versionId>
        </processVariable>
        <processVariable name="commCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f9f4bb28-7e6c-442d-bd1a-38836b4d1f72</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>59</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6e9d45a3-002a-4d3b-8606-334fdf81d09e</guid>
            <versionId>2ced80a7-1ffe-4ed2-8c7f-37e2289b2ae3</versionId>
        </processVariable>
        <processVariable name="isGLFoundC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cef9cfd2-acbe-4852-b387-3c78c1d3f7b8</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>60</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>535cb527-9fea-427d-b170-9191d9f0b153</guid>
            <versionId>5642a8da-158d-4781-9776-8be4cccc0cec</versionId>
        </processVariable>
        <processVariable name="collateralError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b6a933b2-0bdc-472c-a826-34e6b922da4a</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>61</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4c8904cb-8932-48b4-90c0-41b9c9225d06</guid>
            <versionId>48d05cce-7256-4073-9a1f-477f16e933fa</versionId>
        </processVariable>
        <processVariable name="currencyExchangeRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.420d4c63-3aef-4e06-98f0-557abd97e4a4</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>62</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.8c1c1f36-4744-499a-841e-72be2892c861</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>52a446e6-29fb-4a17-98cf-27705f99b0d0</guid>
            <versionId>a3cc1d8c-c7a7-4a41-97f5-dbcc32282a2b</versionId>
        </processVariable>
        <processVariable name="selectedCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7c43bcc7-6ef6-4154-9502-f7e8f65edb18</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>63</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>97c74429-96ed-40e1-89a8-b9d552f444a8</guid>
            <versionId>f2468f9e-2f17-4d75-a407-dddc7488602f</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.beedde8a-f965-4cc5-8571-3832147d40af</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>64</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>994db7fd-14ad-411d-a9a2-a2aabf9b3da5</guid>
            <versionId>32505ebd-0b9d-4c64-99cc-5553dc618870</versionId>
        </processVariable>
        <processVariable name="errorAccount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bce77f1e-c7d5-41e9-881c-a9e5683d38f6</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>65</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dbf0af73-01b8-4d0c-9c6d-aa2f2472a1ec</guid>
            <versionId>813ead7f-6259-487f-b01d-6a88b1c3b7f4</versionId>
        </processVariable>
        <processVariable name="commAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b863cf54-3cc9-4fbb-88b0-020a7b1472c6</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>66</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2bf16784-be14-45ae-b539-2dad5a79d5f1</guid>
            <versionId>ace5fc10-d296-4775-b8d0-3010838fdd9e</versionId>
        </processVariable>
        <processVariable name="limitedPartyTypeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a75e6a7c-a291-4beb-ad40-c2e281b4ce0e</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>67</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a35fdfd1-5530-4999-a339-01b2e679295e</guid>
            <versionId>f8c0f6f2-1185-4d67-9225-c402e7eb7f2e</versionId>
        </processVariable>
        <processVariable name="interestIsVisible">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4dbcc7e9-e171-41df-a681-09c5d93a48d9</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>68</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>788b05d7-923b-4e54-a662-a3dd25569497</guid>
            <versionId>1a7dfbd7-6822-4840-a03b-bab56229967d</versionId>
        </processVariable>
        <processVariable name="exConcatedString">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d1a0d47-ba00-474a-a6c0-b478ed72de43</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>69</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>793fc9e4-fb96-4aea-9558-6ab1d22107bf</guid>
            <versionId>bd7b0064-03f1-43d7-9a51-e0f58f43fc27</versionId>
        </processVariable>
        <processVariable name="tmpusdAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5c7e0942-10b6-4439-8032-f12287bd32fd</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>70</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>33a49c44-b4f5-4391-978f-10a281d1039b</guid>
            <versionId>5f295267-34be-4e62-93e6-a0f0f945f276</versionId>
        </processVariable>
        <processVariable name="haveAmountAdvanced">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3f3d8347-32d2-4f64-a09b-786bb8508689</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>71</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>64304d90-586a-44e4-a949-f9458794a5d2</guid>
            <versionId>505ab441-c417-4d66-ac67-17a69346e96c</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a58732f2-7f59-478e-8d50-246c2eeef485</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>72</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7269ea45-eed8-4cdd-af8e-f0b930058388</guid>
            <versionId>216c9510-1bdd-4511-9b44-c517a6268e58</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7f36e9c4-bdbf-4b4b-999b-3943b87022de</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>73</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9a125b8b-10e8-43c3-873b-767bb0e18cd5</guid>
            <versionId>60ecbf16-c0d1-40e3-afb7-cb553a62f70f</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6a665115-5753-44af-8b94-7f89ceca781a</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>74</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>409cc4e6-4861-42a4-b986-6643e5ca2c65</guid>
            <versionId>80aa5866-56e2-4021-b924-b3f6c4835146</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3b4761b5-0c53-410d-8068-2c25229c6ce5</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>75</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f373feaf-4692-4bd4-b0b8-9413c5ed97bd</guid>
            <versionId>6f8a5b9d-**************-63dd438591a9</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6da8d426-d51d-47f8-9c83-9fe0e3850bf2</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>76</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b23a6b9c-5a57-4e6d-b6e9-fddf4c54e14a</guid>
            <versionId>8cdc4c74-3e81-4635-923c-bcaafa1f4d1e</versionId>
        </processVariable>
        <processVariable name="commClassCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fa56f8ce-2f5c-40d6-ba79-288fca33a1f4</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>77</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c2f87e7d-3665-4c1e-b951-12fa59a886dd</guid>
            <versionId>265eeb99-33b5-4cf4-a7e3-f26095fc3267</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.066bb830-4c84-4459-83f9-127fa46894e2</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>78</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0c441c1e-7543-4124-8ceb-2f458afd5c5b</guid>
            <versionId>f72b85da-463e-4b38-9171-1f5d858b4863</versionId>
        </processVariable>
        <processVariable name="interestVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f6707f4e-6945-41e8-94d0-7459cebf8ff2</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>79</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5a9cd1d-c3f4-4674-b4f0-24f6dd75c042</guid>
            <versionId>13b0e97a-bb19-47d7-ab61-bfed1ff5e220</versionId>
        </processVariable>
        <processVariable name="addressBICList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.83afc80b-c4cb-4a1b-9ed9-95bc7334e428</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>80</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>86a46f9a-8ad0-4617-bbd8-87d4a141ed7b</guid>
            <versionId>a02467b5-ce7a-4e6a-83d7-a79387579e6e</versionId>
        </processVariable>
        <processVariable name="facilityVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a8ac79ef-2a39-4df0-9cbb-0608f54f05f6</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>81</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>51dbe07d-e016-4625-897c-7dde422d86ed</guid>
            <versionId>824302c1-608d-4753-86c1-1227b93ed3f6</versionId>
        </processVariable>
        <processVariable name="facilityPercentageToBookVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.398aab6e-2c4e-47ba-a548-fa28a6eca929</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>82</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>72c6c8d7-1bdc-476c-be08-70d2639cea66</guid>
            <versionId>273d060a-3b0f-40f5-a777-05e9bd32788c</versionId>
        </processVariable>
        <processVariable name="cifs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.34ea356f-15b9-4eb9-867e-102bcda94ac5</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>83</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3361abfa-ae3b-447f-9651-b8b4082046a0</guid>
            <versionId>2cbb7c17-efb3-4a90-956f-91376428a8ba</versionId>
        </processVariable>
        <processVariable name="partyType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ad600c7c-e329-4f8a-936c-39fe6f9c9040</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>84</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cabc809a-1bf6-4d1d-8a21-7bb1a0ea7b7a</guid>
            <versionId>c74abae2-604b-4fb6-8723-a2f8d2fddaae</versionId>
        </processVariable>
        <processVariable name="customerFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7376cf1f-abbe-4876-b923-09810a16a772</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>85</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0d679c1-1fa4-446d-8656-75dec772416b</guid>
            <versionId>0fa5c33e-ee19-41d0-92a0-1bf7fdb43ed6</versionId>
        </processVariable>
        <processVariable name="facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ed3d268d-3894-41a4-ba93-6d680b29da6d</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>86</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.b7087c1b-7f18-4032-b88b-ffe584eafd08</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>efc57cef-10a2-4378-8d3b-57854c0c2d7a</guid>
            <versionId>ce063075-0760-43cf-b9ec-60cd57c3f8ca</versionId>
        </processVariable>
        <processVariable name="LimitsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce179ffd-a4f6-4b37-88fc-9f654575b083</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>87</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>05b4b26a-e4cd-48db-a34e-b3c83734ebae</guid>
            <versionId>af3e5452-a6c2-4414-b5c3-cddb84f92e1b</versionId>
        </processVariable>
        <processVariable name="limitMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.33e9a24c-ab08-42fe-b001-ccef14d06702</processVariableId>
            <description isNull="true" />
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <namespace>2</namespace>
            <seq>88</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2e2e1c9e-06bd-4dab-9114-4fd6c28adb41</guid>
            <versionId>7bb58b22-b106-4afb-9906-d442d45ccd73</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df9bbc7e-d1fe-4121-bd0f-cacf84294522</processItemId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.87b464c7-5bb1-4ef6-bbb8-199a7a5350ac</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-406</guid>
            <versionId>6a88ef75-a7db-4975-a8d0-da3d9d569a19</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.81f1324d-ddb1-4355-85b4-5c743fa93da4</processItemId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0b7aed19-3fce-4335-b08f-b744620af5fa</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:412aec78ca1e02a9:-2e093335:18c3033b632:-40a</guid>
            <versionId>82d596b9-7d59-43e2-9ba1-9324a6ad6e7c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0b7aed19-3fce-4335-b08f-b744620af5fa</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b442ebdc-7356-45c4-b452-326ecd5b1e8f</guid>
                <versionId>3ff68cd1-3c53-4704-a9f8-54bd84d26d16</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f63f31a4-f4ed-4ec0-b3ff-3b52b4ebd66d</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <guid>c65114ba-1da9-4a9d-a9c7-e2c76774e9dc</guid>
            <versionId>1f5b7b39-b4c6-4867-8d33-1ab68b6457f0</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.5c69ec68-3a3b-40b9-b812-233c9f10a2df</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <guid>a70a0aa9-afd8-42be-b949-e938f1d74f00</guid>
            <versionId>5b458312-b1aa-43b0-891b-a804bb6a2c75</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.1fc8ff9b-ec4c-42ec-8278-c368a5ab8571</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <guid>563fc55c-b613-47ac-a1f0-e6b5e09e0888</guid>
            <versionId>888c6331-44db-4552-91e0-204514d35f90</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.9702ac6b-1795-47f5-850e-36f79eae3773</epvProcessLinkId>
            <epvId>/21.e5829eee-0ab1-4f47-9191-f0f8705bc33e</epvId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <guid>0aa28a14-2025-4d5a-a9de-953179d7ea08</guid>
            <versionId>ca575955-d5a3-4f16-92cd-cc4f4391633e</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.df9bbc7e-d1fe-4121-bd0f-cacf84294522</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.aef0c576-1ef4-4e84-aca3-959c9eab5682" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:globalUserTask implementation="##unspecified" name="IDC Execution Hub Initiation 2" id="1.9e0de1a3-7709-49c8-9b29-963ea4784c51">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="2059fd80-789d-48a7-9f0f-93a0f12cb094">
                            <ns16:startEvent name="Start" id="22be4bfc-9649-4377-aebf-9c66f55c5f3e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="47" y="58" width="24" height="24" color="#F8F8F8" />
                                    <ns3:default>2027.*************-4646-8ccc-7c17c4e5c1dc</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.*************-4646-8ccc-7c17c4e5c1dc</ns16:outgoing>
                            </ns16:startEvent>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.afbaa6a5-e738-4c50-84c2-6638602bfba7" name="Validation" id="2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="426" y="44" width="95" height="70" color="#95D087" />
                                    <ns3:preAssignmentScript />
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0</ns16:incoming>
                                <ns16:outgoing>2027.afbaa6a5-e738-4c50-84c2-6638602bfba7</ns16:outgoing>
                                <ns16:script>tw.local.message = "";&#xD;
tw.local.validationMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
tw.local.invalidTabs = [];&#xD;
&#xD;
function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
   regexString = `^\\d{1,10}(\\.\\d{1,6})?$`;&#xD;
   regex = new RegExp(regexString);&#xD;
&#xD;
  if (!regex.test(field))&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function alphanumeric (str) {&#xD;
  // Define a regular expression pattern to match 16 alphanumeric characters&#xD;
  const pattern = /^[A-Za-z0-9]{1,16}$/;&#xD;
  &#xD;
  // Test the input string against the pattern and return the result&#xD;
  return pattern.test(str);&#xD;
}&#xD;
&#xD;
function validateDecimal2(field, fieldName, controlMessage , validationMessage) {&#xD;
   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
   regex = new RegExp(regexString);&#xD;
&#xD;
  if (!regex.test(field) &amp;&amp; field &gt;= 0)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function isNumber(field , fieldName) {&#xD;
  if (!isNaN(parseFloat(field)) &amp;&amp; isFinite(field)){&#xD;
	return true;&#xD;
  }&#xD;
  addError(fieldName , "This Field Must Be Number" , "validationMessage");&#xD;
  return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		var today =  new Date();&#xD;
		today.setHours(0,0,0,0);&#xD;
//		alert("today = "+today);&#xD;
//		alert("date = "+ date);&#xD;
		if(date != null &amp;&amp; date &lt; today)&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index , tabName)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		if (tw.local.validationMessage.length == 0) {&#xD;
			tw.local.validationMessage += "&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.validationMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
// ====================================================================================================== //&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
function swiftValidation (field,fieldName) {&#xD;
	regexString = /^(?![\s])[a-zA-Z0-9.,()\/='+:?!\"%&amp;*&lt;&gt;;{@#_ \r\n-]*$/;&#xD;
	regex = new RegExp(regexString);&#xD;
	&#xD;
	if (field != "") {&#xD;
		if (!regex.test(field)){&#xD;
			addError(fieldName , "Not Valid Swift Format","Not Valid Swift Format" );&#xD;
		}&#xD;
	}&#xD;
}&#xD;
//--------------------------------------------------------------------------------------------------------&#xD;
/*&#xD;
* =====================&#xD;
* |	VALIDATE HERE   |&#xD;
* =====================&#xD;
*/&#xD;
&#xD;
&#xD;
// //----------------------------------customer info[0] ---------------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.customerInformation.facilityType,"tw.local.idcRequest.customerInformation.facilityType");&#xD;
mandatory(tw.local.idcRequest.customerInformation.importCardNumber,"tw.local.idcRequest.customerInformation.importCardNumber");&#xD;
&#xD;
// validateTab(0,"Customer Information");&#xD;
// // //---------------------------------basic details[1]-----------------------------------------------------------------&#xD;
mandatory(tw.local.idcContract.IDCProduct.englishdescription,"tw.local.idcContract.IDCProduct.englishdescription");&#xD;
mandatory(tw.local.idcRequest.importPurpose.englishdescription,"tw.local.idcRequest.importPurpose.englishdescription");&#xD;
mandatory(tw.local.idcRequest.paymentTerms.englishdescription,"tw.local.idcRequest.paymentTerms.englishdescription");&#xD;
mandatory(tw.local.idcRequest.documentsSource.englishdescription,"tw.local.idcRequest.documentsSource.englishdescription");&#xD;
mandatory(tw.local.idcRequest.productCategory.englishdescription,"tw.local.idcRequest.productCategory.englishdescription");&#xD;
mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
	}&#xD;
}&#xD;
if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
	}&#xD;
}&#xD;
mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
&#xD;
if (tw.local.idcRequest.IDCRequestNature.englishdescription == "New Request" &amp;&amp; tw.local.selectedAction == "Create Contract") {&#xD;
	&#xD;
	mandatory(tw.local.idcRequest.FCContractNumber,"tw.local.idcRequest.FCContractNumber");&#xD;
}&#xD;
&#xD;
validateTab(1,"Basic Details");&#xD;
//-----------------------------------financial Details[2]---------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
//validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
&#xD;
mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.englishdescription,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.englishdescription");&#xD;
mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.englishdescription,"tw.local.idcRequest.financialDetails.sourceOfFunds.englishdescription");&#xD;
&#xD;
// validateTab(2,"Financial Details Branch");&#xD;
// // //----------------------------------financial Details fo[3]--------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate" );&#xD;
mandatory(tw.local.idcRequest.financialDetails.daysTillMaturity,"tw.local.idcRequest.financialDetails.daysTillMaturity" );&#xD;
mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,"tw.local.idcRequest.financialDetails.beneficiaryDetails.name");&#xD;
mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank" );	&#xD;
mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,"tw.local.idcRequest.financialDetails.beneficiaryDetails.account" );&#xD;
mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code");&#xD;
mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber" );&#xD;
if (tw.local.selectedAction == tw.epv.Action.approveRequest) {&#xD;
	mandatory(tw.local.idcRequest.financialDetails.executionHub.code,"tw.local.idcRequest.financialDetails.executionHub.code");&#xD;
}&#xD;
if (tw.local.idcRequest.documentsSource.englishdescription == "Correspondent") {&#xD;
	mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.correspondentRefNum,"tw.local.idcRequest.financialDetails.beneficiaryDetails.correspondentRefNum");&#xD;
}&#xD;
var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;&#xD;
if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
	addError("tw.local.idcRequest.financialDetails.cashAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
	addError("tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
	addError("tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
	addError("tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
&#xD;
}&#xD;
&#xD;
var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;&#xD;
if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
	addError("tw.local.idcRequest.financialDetails.amtSight" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
}&#xD;
if (tw.local.idcRequest.financialDetails.amtDeferredNoAvalized &gt;0 &amp;&amp; tw.local.idcRequest.financialDetails.amtDeferredAvalized &gt; 0) {&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "");	&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "");	&#xD;
}&#xD;
if (tw.local.idcRequest.financialDetails.amtDeferredNoAvalized ==0 &amp;&amp; tw.local.idcRequest.financialDetails.amtDeferredAvalized == 0) {&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "you must fill at least on of these fields");	&#xD;
	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "you must fill at least on of these fields");	&#xD;
&#xD;
}&#xD;
var totalInstallments = 0;&#xD;
if (tw.local.idcRequest.paymentTerms.englishdescription != "Sight") {&#xD;
	for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {&#xD;
		mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentDate");&#xD;
		notPastDate(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate , 'tw.local.idcRequest.financialDetails.paymentTerms['+i+'].installmentDate' , 'cannot select date in the past' , 'cannot select date in the past' , false);&#xD;
		mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentAmount");&#xD;
		totalInstallments +=  tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount;&#xD;
	}&#xD;
	if (totalInstallments &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
		addError("tw.local.idcRequest.financialDetails.paymentTerms["+0+"].installmentAmount","Sum of all Installment Amounts in a request must be &lt;= Amount Payable by NBE");&#xD;
	}&#xD;
}&#xD;
if (tw.local.idcRequest.financialDetails.amtPayableByNBE &lt; 0) {&#xD;
	addError("tw.local.idcRequest.financialDetails.amtPayableByNBE" , "must be more than 0");&#xD;
}&#xD;
&#xD;
// tw.local.validationMessage += "Financial Details Trade FO";&#xD;
validateTab(3,"Financial Details Trade FO");&#xD;
//------------------------------product details[4]--------------------------------------------------------------&#xD;
&#xD;
mandatory(tw.local.idcRequest.productsDetails.shippingDate,"tw.local.idcRequest.productsDetails.shippingDate");&#xD;
mandatory(tw.local.idcRequest.productsDetails.incoterms.englishdescription,"tw.local.idcRequest.productsDetails.incoterms.englishdescription");&#xD;
mandatory(tw.local.idcRequest.productsDetails.shipmentMethod.arabicdescription,"tw.local.idcRequest.productsDetails.shipmentMethod.arabicdescription");&#xD;
mandatory(tw.local.idcRequest.productsDetails.destinationPort,"tw.local.idcRequest.productsDetails.destinationPort");&#xD;
mandatory(tw.local.idcRequest.productsDetails.CBECommodityClassification.arabicdescription,"tw.local.idcRequest.productsDetails.CBECommodityClassification.arabicdescription");&#xD;
mandatory(tw.local.idcRequest.productsDetails.HSProduct.code,"tw.local.idcRequest.productsDetails.HSProduct.code");&#xD;
mandatory(tw.local.idcRequest.productsDetails.ACID,"tw.local.idcRequest.productsDetails.ACID");&#xD;
&#xD;
&#xD;
// validateTab(4,"Product Details");&#xD;
// // //---------------------------------Create Contract[5]--------------------------------------&#xD;
//Basic Details&#xD;
&#xD;
 mandatory(tw.local.idcContract.userReference,"tw.local.idcContract.userReference");&#xD;
if(alphanumeric(tw.local.idcContract.userReference) == false){&#xD;
	addError("tw.local.idcContract.userReference","Please enter upto 16 alphanumeric chars");&#xD;
}&#xD;
&#xD;
//Contract Transaction Details	&#xD;
if (tw.local.contractTransactionDetailsVis == true) {&#xD;
	mandatory(tw.local.idcContract.transactionTenorDays,"tw.local.idcContract.transactionTenorDays");&#xD;
	mandatory(tw.local.idcContract.transactionTransitDays,"tw.local.idcContract.transactionTransitDays");&#xD;
}								&#xD;
//Interest Details&#xD;
if (tw.local.idcContract.haveInterest == true) {&#xD;
	if (tw.local.idcContract.interestFromDate &lt; tw.local.idcContract.transactionBaseDate){&#xD;
		addError("tw.local.idcContract.interestFromDate","Interest From Date must be &gt;= Base Date.");&#xD;
	}&#xD;
	if (tw.local.idcContract.interestToDate &gt; tw.local.idcContract.transactionMaturityDate){&#xD;
		addError("tw.local.idcContract.interestToDate","Interest To Date must be &lt;= Maturity Date.");&#xD;
	}&#xD;
&#xD;
	if (tw.local.idcContract.interestToDate &lt;= tw.local.idcContract.interestFromDate) {&#xD;
		addError("tw.local.idcContract.interestToDate","Interest To Date must be &gt; Interest From Date.");&#xD;
	}&#xD;
	if (tw.local.idcContract.interestRate &lt; 0) {&#xD;
		addError("tw.local.idcContract.interestRate","Rate must be &gt;= 0")&#xD;
	}&#xD;
	if (tw.local.idcContract.interestAmount &lt; 0){&#xD;
		addError("tw.local.idcContract.interestAmount","Amount must be &gt;= 0")&#xD;
	}&#xD;
}&#xD;
						&#xD;
//Cash Collaterals&#xD;
if (tw.local.contractCashCollateralsVis == true) {&#xD;
	if (tw.local.idcContract.collateralAmount &lt; 0){&#xD;
		addError("tw.local.idcContract.collateralAmount","Collateral Amount must be &gt;= 0");&#xD;
	}&#xD;
	if (tw.local.idcContract.collateralAmount &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
		addError("tw.local.idcContract.collateralAmount","Collateral Amount must be &lt;= Total Amount Payable By NBE");&#xD;
	}&#xD;
	if (tw.local.idcContract.collateralAmount &gt; 0) {&#xD;
		mandatory(tw.local.idcContract.collateralCurrency.code,"tw.local.idcContract.collateralCurrency.code")&#xD;
	}&#xD;
	if (tw.local.collateralError == true) {&#xD;
		addError("tw.local.idcContract.collateralAmount","Collateral Amount must be &lt;= Total Amount Payable By NBE Please consider the currency exchange");&#xD;
	}&#xD;
&#xD;
	if (tw.local.idcContract.collateralAmount &gt; 0) {&#xD;
		for (var i=0; i&lt;tw.local.idcContract.cashCollateralAccounts.length; i++){&#xD;
			mandatory(tw.local.idcContract.cashCollateralAccounts[i].debitedAmtinCollateralCurrency,"tw.local.idcContract.cashCollateralAccounts["+i+"].debitedAmtinCollateralCurrency");&#xD;
			if (tw.local.idcContract.cashCollateralAccounts[i].accountClass == "Customer Account") {&#xD;
				mandatory(tw.local.idcContract.cashCollateralAccounts[i].accountNumber.name,"tw.local.idcContract.cashCollateralAccounts["+i+"].accountNumber.name");&#xD;
			}else if(tw.local.idcContract.cashCollateralAccounts[i].accountClass == "GL Account"){&#xD;
				&#xD;
				mandatory(tw.local.idcContract.cashCollateralAccounts[i].GLAccountNumber,"tw.local.idcContract.cashCollateralAccounts["+i+"].GLAccountNumber");&#xD;
				mandatory(tw.local.idcContract.cashCollateralAccounts[i].accountBranchCode,"tw.local.idcContract.cashCollateralAccounts["+i+"].accountBranchCode");&#xD;
				mandatory(tw.local.idcContract.cashCollateralAccounts[i].accountCurrency,"tw.local.idcContract.cashCollateralAccounts["+i+"].accountCurrency");&#xD;
				&#xD;
				if (tw.local.idcContract.cashCollateralAccounts[i].isGLFound == false) {&#xD;
					addError("tw.local.idcContract.cashCollateralAccounts["+i+"].GLAccountNumber","GL Account Not Found");&#xD;
				}&#xD;
		            if (tw.local.idcContract.cashCollateralAccounts[i].isGLVerified == false) {&#xD;
		                addError("tw.local.idcContract.cashCollateralAccounts["+i+"].GLAccountNumber","Please Click the Verify Button");&#xD;
		            }&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
	if (tw.local.idcRequest.financialDetails.documentCurrency.code != tw.local.idcContract.collateralCurrency.code){&#xD;
		var amount = tw.local.idcContract.collateralAmount * tw.local.exRate;&#xD;
		if (amount &gt; tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
			addError("tw.local.idcContract.collateralAmount","Collateral Amount must be &lt;= Total Amount Payable By NBE");&#xD;
		}&#xD;
	}&#xD;
}&#xD;
//Advices&#xD;
if (tw.local.contractAdvicesVis == true) {&#xD;
    for (var i=0; i&lt;tw.local.idcContract.advices.length; i++) {&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line1,"tw.local.idcContract.advices["+i+"].advicelines.line1");&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line2,"tw.local.idcContract.advices["+i+"].advicelines.line2");&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line3,"tw.local.idcContract.advices["+i+"].advicelines.line3");&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line4,"tw.local.idcContract.advices["+i+"].advicelines.line4");&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line5,"tw.local.idcContract.advices["+i+"].advicelines.line5");&#xD;
        swiftValidation (tw.local.idcContract.advices[i].advicelines.line6,"tw.local.idcContract.advices["+i+"].advicelines.line6");&#xD;
    }&#xD;
}&#xD;
// validateTab(5,"Create Contract");&#xD;
// //-----------------------------------Commissions and Charges[6]---------------------------------------------- &#xD;
for (var i=0; i&lt;tw.local.idcContract.commissionsAndCharges.length; i++) {&#xD;
	if (tw.local.idcContract.commissionsAndCharges[i].chargeAmount &lt; 0){&#xD;
		addError("tw.local.idcContract.commissionsAndCharges["+i+"].chargeAmount","Must be &gt;= 0");&#xD;
	}&#xD;
	if (tw.local.idcContract.commissionsAndCharges[i].waiver == false &amp;&amp; tw.local.idcContract.commissionsAndCharges[i].chargeAmount &gt; 0) {&#xD;
		mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.accountClass");&#xD;
	&#xD;
		if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == "Customer Account"){&#xD;
	      	 mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountNumber,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.accountNumber");&#xD;
			mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts,"tw.local.idcContract.commissionsAndCharges[i].debitedAccount.ownerAccounts")&#xD;
	       &#xD;
	   	}else if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountClass == "GL Account"){&#xD;
	   		if(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLVerifiedC == false){&#xD;
	   			addError("tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.GLAccountNumber","Please Click the Verify Button");&#xD;
	   		}&#xD;
			mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.GLAccountNumber");&#xD;
			mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountBranchCode,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.accountBranchCode");&#xD;
			mandatory(tw.local.idcContract.commissionsAndCharges[i].debitedAccount.accountCurrency,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAccount.accountCurrency");&#xD;
		}&#xD;
		if (tw.local.idcContract.commissionsAndCharges[i].debitedAccount.isGLFoundC == false ){&#xD;
			addError("tw.local.idcContract.commissionsAndCharges[i].debitedAccount.GLAccountNumber","Account Not Found");&#xD;
		}&#xD;
		if (tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != 0 || tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate != undefined){&#xD;
			validateDecimal(tw.local.idcContract.commissionsAndCharges[i].debitedAmount.negotiatedExchangeRate,"tw.local.idcContract.commissionsAndCharges["+i+"].debitedAmount.negotiatedExchangeRate","Must be Decimal (10,6)");&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
// validateTab(6,"Commissions and Charges");&#xD;
// //-------------------------------------------------------Party[7]-------------------------------------------------------------------------&#xD;
 var accounteeFound = false;&#xD;
 var RemittingFound = false;&#xD;
 var caseFound = false;&#xD;
 var drawerFound = false;&#xD;
 var draweeFound = false;&#xD;
 for (var i=0; i&lt;tw.local.idcContract.party.length; i++) {&#xD;
 	mandatory(tw.local.idcContract.party[i].partyId,"tw.local.idcContract.party["+i+"].partyId");&#xD;
 	if (tw.local.idcContract.party[i].partyType.name == "Remitting Bank") {&#xD;
 		mandatory(tw.local.idcContract.party[i].media,"tw.local.idcContract.party["+i+"].media");&#xD;
 		mandatory(tw.local.idcContract.party[i].address,"tw.local.idcContract.party["+i+"].address");&#xD;
 	}&#xD;
 	if ((accounteeFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == "Accountee") || (RemittingFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == "Remitting Bank") || (caseFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == "Case in Need" ) || (drawerFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == "Drawer") || (draweeFound == true &amp;&amp; tw.local.idcContract.party[i].partyType.name == "Drawee")) {&#xD;
 		addError("tw.local.idcContract.party["+i+"].partyType.name","only one is allowed from this type");&#xD;
 	}else{&#xD;
 		if (tw.local.idcContract.party[i].partyType.name == "Accountee") {&#xD;
 			accounteeFound = true;&#xD;
 		}&#xD;
 		if (tw.local.idcContract.party[i].partyType.name == "Drawee") {&#xD;
 			draweeFound = true;&#xD;
 		}&#xD;
 		if (tw.local.idcContract.party[i].partyType.name == "Drawer") {&#xD;
 			drawerFound = true;&#xD;
 		}&#xD;
 		if (tw.local.idcContract.party[i].partyType.name == "Remitting Bank") {&#xD;
 			RemittingFound = true;&#xD;
 		}&#xD;
 		if (tw.local.idcContract.party[i].partyType.name == "Case in Need") {&#xD;
 			caseFound = true;&#xD;
 		}&#xD;
 	}&#xD;
	&#xD;
 	mandatory(tw.local.idcContract.party[i].address1,"tw.local.idcContract.party["+i+"].address1");&#xD;
 	mandatory(tw.local.idcContract.party[i].reference,"tw.local.idcContract.party["+i+"].reference");&#xD;
 	mandatory(tw.local.idcContract.party[i].name,"tw.local.idcContract.party["+i+"].name");&#xD;
 	mandatory(tw.local.idcContract.party[i].country,"tw.local.idcContract.party["+i+"].country");&#xD;
 	mandatory(tw.local.idcContract.party[i].language,"tw.local.idcContract.party["+i+"].language");&#xD;
 }&#xD;
 validateTab(7,"Parties");&#xD;
&#xD;
// // //-------------------------------------limit[8]-----------------------------------------------------------------&#xD;
tw.local.limitMessage = "";&#xD;
if (tw.local.idcContract.isLimitsTrackingRequired == true) {&#xD;
	var ciflist = [];&#xD;
	for (var i=0; i&lt;tw.local.idcContract.party.length; i++) {&#xD;
		if (tw.local.idcContract.party[i].partyType.name == "Drawee" || tw.local.idcContract.party[i].partyType.name == "Accountee") {&#xD;
			ciflist.push(tw.local.idcContract.party[i].partyId);&#xD;
		}&#xD;
	}&#xD;
	if (tw.local.idcContract.facilities.length == 0) {&#xD;
		addError("tw.local.facility.facilityCode.value","you must add facility","you must add facility");&#xD;
		&#xD;
	}else{&#xD;
		var sumOfPresentage = 0;&#xD;
		for (var i=0; i&lt;tw.local.idcContract.facilities.length; i++) {&#xD;
			for (var j=0; j&lt;tw.local.idcContract.facilities[i].facilityLines.length; j++) {&#xD;
				sumOfPresentage = sumOfPresentage + tw.local.idcContract.facilities[i].facilityLines[j].facilityPercentageToBook;&#xD;
				if (tw.local.idcContract.facilities[i].facilityLines[j].CIF != ciflist[0] &amp;&amp; tw.local.idcContract.facilities[i].facilityLines[j].CIF != ciflist[1]) {&#xD;
					tw.local.limitMessage += "the facility "+tw.local.idcContract.facilities[i].facilityCode+" is not related to drawee or accountee \n";&#xD;
					addError("tw.local.limitMessage","Error","Error");&#xD;
				}&#xD;
			}&#xD;
		}&#xD;
		&#xD;
		if (sumOfPresentage != 100) {&#xD;
			tw.local.limitMessage += "The Sum Of Facility Percentage To Book Must Be 100% \n";&#xD;
			addError("tw.local.limitMessage","Error","Error");			&#xD;
		}&#xD;
	}&#xD;
}&#xD;
// validateTab(8,"Limits Tracking");&#xD;
//----------------------------------------app info--------------------------------------------------------------&#xD;
//------------------------------------Actions Validation---------------------------------------------------------&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
&#xD;
if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
}&#xD;
&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToTradeFO){&#xD;
	mandatory(tw.local.idcRequest.stepLog.returnReason,"tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
if (tw.local.selectedAction == tw.epv.Action.createContract) {&#xD;
    mandatory(tw.local.idcRequest.FCContractNumber,"tw.local.idcRequest.FCContractNumber");&#xD;
}&#xD;
&#xD;
if (tw.local.idcRequest.approvals.CAD==true &amp;&amp; tw.local.selectedAction == "Obtain Approvals" &amp;&amp; tw.local.idcContract.facilities.length == 0) {&#xD;
	addError("tw.local.selectedAction", "This requset does not has facility to acquire CAD");&#xD;
}&#xD;
&#xD;
if ((tw.local.idcRequest.approvals.CAD==true|| &#xD;
    tw.local.idcRequest.approvals.compliance==true ||&#xD;
    tw.local.idcRequest.approvals.treasury==true))&#xD;
{   &#xD;
    if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
       addError("tw.local.selectedAction", "Please uncheck Approvals");&#xD;
    }&#xD;
}else if (tw.local.selectedAction == "Obtain Approvals")&#xD;
{&#xD;
    addError("tw.local.selectedAction", "Please check Approvals");&#xD;
}&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.ebf2625e-51cd-4f8d-bdfd-ed7bae08b22b" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="contractInterestDetailsVis" id="2056.3ddbd5c6-f9a5-47d1-98c8-faf818bc4ab9">
                                <ns16:documentation textFormat="text/plain" />
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="contractCashCollateralsVis" id="2056.8b2f5baa-46c9-4a7c-83d6-c472846350e1">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="contractLimitsTrackingVis" id="2056.eee297e2-e33d-46a6-a23a-6e6882c58b51">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="contractAdvicesVis" id="2056.9f9d5d04-364b-42e4-9edc-edc5c92ab772">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="contractTransactionDetailsVis" id="2056.9ff7ba4a-6b6a-49cf-b6bb-ad0e9701ef38">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="advancePaymentsUsedVis" id="2056.e02bf9fd-e2e4-4827-8661-0ca1511b2105">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">true</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.841104aa-2225-4d72-8e37-f015d834ffbb" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.8b9b8ce3-9933-4b67-a696-88f14c673b7a" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="partyTypeList" id="2056.a5652337-27bc-4562-a4c3-aa60223ddabd">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].value = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatString" id="2056.9644ec97-63e3-43c4-b7aa-3b784db3c80f" />
                            <ns16:dataObject itemSubjectRef="itm.12.a025468c-38bc-4809-b337-57da9e95dacb" isCollection="false" name="BeneficiaryDetailsOption" id="2056.c2f8a6cd-ff7c-4b3c-8926-1221b9631245">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.account = "";
autoObject.bank = "";
autoObject.correspondentRefNum = "";
autoObject.name = "";
autoObject.country = {};
autoObject.country.id = 0;
autoObject.country.code = "";
autoObject.country.arabicdescription = "";
autoObject.country.englishdescription = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="partyIndex" id="2056.65a726ef-6236-4b6b-8da8-4cbb496c841e" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="exRate" id="2056.8a7ace65-b33e-4420-93f3-1bf3437bba7b">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">1</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountList" id="2056.1ed5bdf9-280d-4ee3-bd6f-f7f5af23f215">
                                <ns16:documentation textFormat="text/plain">&lt;div&gt;&lt;br /&gt;&lt;/div&gt;</ns16:documentation>
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="accountNumberList" id="2056.9a923395-c931-45eb-93c3-d42bcc156340">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.f687ac51-6bf9-4bf0-8a14-73f61f74ba28" />
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="documentsTypesSelected" id="2056.ff0287cf-0ce4-44f4-85ba-d4b6a2a2947d" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.bfafc277-f866-4dcd-96ce-8138f68ca691" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.b2b75484-ee9d-4c50-93ee-93a2552988b5" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" name="exist" id="2056.34ab7fee-8079-4d23-93fa-8efcacf2b686" />
                            <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.16768f40-baac-4f67-b4c8-1515e214acf9">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.customerNo = "";
autoObject.customerType = "";
autoObject.customerName = "";
autoObject.customerCategory = "";
autoObject.customerBranch = "";
autoObject.customerStatus = {};
autoObject.customerStatus.name = "";
autoObject.customerStatus.value = "";
autoObject.privateCustomer = "";
autoObject.faciliyBranchCode = "";
autoObject.frozen = false;
autoObject.deceased = false;
autoObject.sName = "";
autoObject.customerFullName = "";
autoObject.nationality = "";
autoObject.country = {};
autoObject.country.name = "";
autoObject.country.value = "";
autoObject.customerAddress = {};
autoObject.customerAddress.addressLine1 = "";
autoObject.customerAddress.addressLine2 = "";
autoObject.customerAddress.addressLine3 = "";
autoObject.customerAddress.addressLine4 = "";
autoObject.customerAddress.customerNO = "";
autoObject.fullName = "";
autoObject.customerArabicName = "";
autoObject.customerEnglishName = "";
autoObject.customerNationalID = "";
autoObject.tradingTitle = "";
autoObject.commercialRegisterNo = "";
autoObject.cardTaxNo = "";
autoObject.businessActivity = "";
autoObject.arabicName = "";
autoObject.EnglishName = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isGLFound" id="2056.0a9ecfc1-5bfa-43cd-8a58-5da87970d8b0" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.946b98e1-c28f-48ee-a16a-7df32b75a87f" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.dc0493e1-914d-4c9b-bc17-df6382964e73" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.d4fafa4b-5e4e-4046-ae4e-e267a1b27c43" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.fcb2c3c9-109f-42f6-9486-16cb1c790266" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.665d3d2d-0218-4c74-a461-ba2515fb91c6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.b8c585b2-9c44-4d3b-87fb-235a191a2988" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.2573c8bf-c93e-473b-b648-e550a03b8d2e" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="havePaymentTerms" id="2056.4f15f032-dad4-49b8-8698-f77827a03ff3" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedBIC" id="2056.0d2edf67-9e17-4c95-b706-c3df33c9e8d8" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="chargesAccountList" id="2056.66da0014-9bfa-4595-9f60-db019e021a8e">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="adviceCodeList" id="2056.e3ed63c0-8121-4455-b3d2-5f1f21ce4f4f" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" name="tempObj" id="2056.5bc4fd52-e3b2-4170-a76e-187aa172e20b">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.name = "";
autoObject.value = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="partyAccountList" id="2056.7137ff16-5afa-4666-ad46-4b64043754ec">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="sumAccountList" id="2056.338ceda7-f7d7-4489-9518-d6660e4ebb53">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="accounteeCIF" id="2056.bafa5084-67d2-44a1-b111-0ef024850793">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false" />
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="customerCIF" id="2056.369c4936-0e4f-4815-bddb-3710f9d5f6c9">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false" />
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="caseCIF" id="2056.171a12f7-e907-4353-be80-637dc742f7e9" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="draweeCIF" id="2056.79af2607-11e0-4a15-83f1-492ffcdbc611" />
                            <ns16:dataObject itemSubjectRef="itm.12.89a53d06-50b8-41df-a5cc-5e4f61147b6d" isCollection="true" name="tempCommissions" id="2056.6e172f54-cb2e-4136-82a8-723f0d2fb01a">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="partiesVis" id="2056.4b9a2570-dfb1-4b0c-ab1c-74be76bfda38" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isChecker" id="2056.b8ce5d34-3ecd-4e3d-b20f-f7819f84383e">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="liqDone" id="2056.4cfb1ce5-6911-46f8-b306-f58edc11c771" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="amtPayableByNBE" id="2056.06a0d82c-0930-4399-bd5f-bfa6be68158e" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="intDefaultAmount" id="2056.47f2a416-723e-4fa5-add1-38f6b36f9bb9" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="minAmount" id="2056.db14b40c-f2fe-4564-baaa-989a20160b7e" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="chargesIndex" id="2056.*************-4eda-94e5-c5a8eed9086b" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatExCurrency" id="2056.8f6675f6-457c-4249-a083-e230ba345552" />
                            <ns16:dataObject itemSubjectRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e" isCollection="true" name="InterestsAndChargesList" id="2056.e5559355-c9c7-48e3-8e03-32b760d966c5" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatInterest" id="2056.d874db2e-9bed-4502-b05f-cbe98eb0fc2d" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="contractAmount" id="2056.4aa8924c-0139-402f-800c-6bb769875d65" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="accountIndex" id="2056.04dcc23c-338b-4a97-ac8f-310da2497297" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="adviceData" id="2056.c209b161-1758-4b88-b3b1-bc27cee41cb0" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="accountIndexC" id="2056.d75ba1ad-3a9c-46e0-b31a-ae6faf51dd05" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="concatComm" id="2056.7665f354-07f6-4023-8000-f5e37be7bd51" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityCIF" id="2056.02d34944-4b00-4b46-b810-2eb8378660df" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="commCIF" id="2056.f9f4bb28-7e6c-442d-bd1a-38836b4d1f72" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isGLFoundC" id="2056.cef9cfd2-acbe-4852-b387-3c78c1d3f7b8" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="collateralError" id="2056.b6a933b2-0bdc-472c-a826-34e6b922da4a" />
                            <ns16:dataObject itemSubjectRef="itm.12.8c1c1f36-4744-499a-841e-72be2892c861" isCollection="false" name="currencyExchangeRate" id="2056.420d4c63-3aef-4e06-98f0-557abd97e4a4">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.RateType = "";
autoObject.Rate = 0.0;
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedCIF" id="2056.7c43bcc7-6ef6-4154-9502-f7e8f65edb18" />
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.beedde8a-f965-4cc5-8571-3832147d40af">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorAccount" id="2056.bce77f1e-c7d5-41e9-881c-a9e5683d38f6">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"Can not retrive customer accounts please try again"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="commAccountList" id="2056.b863cf54-3cc9-4fbb-88b0-020a7b1472c6">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].accountNO = "";
autoObject[0].currencyCode = "";
autoObject[0].branchCode = "";
autoObject[0].balance = 0.0;
autoObject[0].typeCode = "";
autoObject[0].customerName = "";
autoObject[0].customerNo = "";
autoObject[0].frozen = false;
autoObject[0].dormant = false;
autoObject[0].noDebit = false;
autoObject[0].noCredit = false;
autoObject[0].postingAllowed = false;
autoObject[0].ibanAccountNumber = "";
autoObject[0].accountClassCode = "";
autoObject[0].balanceType = "";
autoObject[0].accountStatus = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="limitedPartyTypeList" id="2056.a75e6a7c-a291-4beb-ad40-c2e281b4ce0e" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="interestIsVisible" id="2056.4dbcc7e9-e171-41df-a681-09c5d93a48d9" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="exConcatedString" id="2056.0d1a0d47-ba00-474a-a6c0-b478ed72de43" />
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpusdAdvancePayment" id="2056.5c7e0942-10b6-4439-8032-f12287bd32fd" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="haveAmountAdvanced" id="2056.3f3d8347-32d2-4f64-a09b-786bb8508689" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.a58732f2-7f59-478e-8d50-246c2eeef485" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.7f36e9c4-bdbf-4b4b-999b-3943b87022de" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.6a665115-5753-44af-8b94-7f89ceca781a" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.3b4761b5-0c53-410d-8068-2c25229c6ce5" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.6da8d426-d51d-47f8-9c83-9fe0e3850bf2" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="commClassCode" id="2056.fa56f8ce-2f5c-40d6-ba79-288fca33a1f4" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.066bb830-4c84-4459-83f9-127fa46894e2">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="interestVis" id="2056.f6707f4e-6945-41e8-94d0-7459cebf8ff2" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="addressBICList" id="2056.83afc80b-c4cb-4a1b-9ed9-95bc7334e428" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityVis" id="2056.a8ac79ef-2a39-4df0-9cbb-0608f54f05f6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityPercentageToBookVis" id="2056.398aab6e-2c4e-47ba-a548-fa28a6eca929" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="cifs" id="2056.34ea356f-15b9-4eb9-867e-102bcda94ac5" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="partyType" id="2056.ad600c7c-e329-4f8a-936c-39fe6f9c9040" />
                            <ns16:dataObject itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" name="customerFacilities" id="2056.7376cf1f-abbe-4876-b923-09810a16a772" />
                            <ns16:dataObject itemSubjectRef="itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08" isCollection="false" name="facility" id="2056.ed3d268d-3894-41a4-ba93-6d680b29da6d" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="LimitsVis" id="2056.ce179ffd-a4f6-4b37-88fc-9f654575b083" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="limitMessage" id="2056.33e9a24c-ab08-42fe-b001-ccef14d06702" />
                            <ns16:sequenceFlow sourceRef="22be4bfc-9649-4377-aebf-9c66f55c5f3e" targetRef="2025.7138dc8f-1707-4eb8-8721-15624a4ff914" name="To IDC Execution Hub Initiation" id="2027.*************-4646-8ccc-7c17c4e5c1dc">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="IDC Execution Hub Initiation" id="2025.7138dc8f-1707-4eb8-8721-15624a4ff914">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="199" y="38" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>DC_Templete1/submit</ns3:validationStayOnPagePaths>
                                    <ns3:preAssignmentScript>&#xD;
tw.local.interestVis = "NONE";&#xD;
tw.local.errorVIS = "NONE";&#xD;
tw.local.idcRequest.financialDetails.amtPayableByNBE = 2000;&#xD;
tw.local.idcRequest.financialDetails.documentCurrency.code = "EGP";&#xD;
tw.local.BeneficiaryDetailsOption.name = "Hamada";&#xD;
tw.local.BeneficiaryDetailsOption.country.englishdescription = "Sudan";&#xD;
tw.local.BeneficiaryDetailsOption.correspondentRefNum = "2251122511";&#xD;
tw.local.idcRequest.appInfo.instanceID = "2251122511"&#xD;
tw.local.idcRequest.financialDetails.documentAmount = 3000;&#xD;
tw.local.idcRequest.customerInformation.CIFNumber = "03024932";&#xD;
tw.local.idcRequest.IDCRequestStage = "Final";&#xD;
tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation;&#xD;
tw.local.idcRequest.IDCRequestType.englishdescription = "ICAP";&#xD;
&#xD;
tw.local.cifs = ["1st","2nd","3rd"];</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.*************-4646-8ccc-7c17c4e5c1dc</ns16:incoming>
                                <ns16:incoming>2027.afbaa6a5-e738-4c50-84c2-6638602bfba7</ns16:incoming>
                                <ns16:outgoing>2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>ac00d1ba-d95d-4ba2-8917-3f1816c2c06d</ns19:id>
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>50578d7f-b539-4de7-8353-336c3e1153b0</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Error Message</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>911dbff8-279d-4111-8945-f0ff7b6b4b2b</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a4d5a42d-4c25-4327-8ecc-684ce310e4c3</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8c3c6543-77c3-4631-84ce-db5e87a6ed49</ns19:id>
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a56c06ec-7a44-48b1-8a3f-ee0adede5afd</ns19:id>
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>3a6bb27d-6872-4119-8cbe-87dd6288696c</ns19:id>
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>f258f858-b510-424b-8190-9f8d42fc30ec</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>b6d6c909-2408-45dc-8156-936f2543098e</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>9fef8957-c93e-46e7-8625-1241217e393c</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>e7a3f561-8cdd-4494-8fd6-7ca9e5d0f0ad</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>7ed95949-6f4c-49e8-8301-8aa5e28d159d</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>cf478edd-25af-4810-80eb-f234f9afc459</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b3dc2375-ec36-4d4c-82d3-7b5af94635cf</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c2b22392-0f3d-4e69-8cd7-1802595aa935</ns19:id>
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c88bdaba-c851-4ab3-850a-782b81e026b9</ns19:id>
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>1263330d-1074-4b07-887a-9b94024763b4</ns19:id>
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6d28034e-8ae9-43cb-8bd9-ca18aeaae19f</ns19:id>
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    <ns19:value>Submit</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>abb0f1dd-d3af-45ad-8b37-15866a461e05</ns19:id>
                                                    <ns19:optionName>approvalsReadOnly</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>58828f13-8a3b-4771-8eb5-77d870bcb588</ns19:id>
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>704ee87c-5b91-46b6-88ab-85265409cf6e</ns19:id>
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>2202459d-7158-420e-8ca1-f0bf3a5dac8f</ns19:id>
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>f2cf4863-c7e9-42ef-8015-4f0ad99d2f5c</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>74b65e3a-8349-445c-8bb9-1e787ad4da64</ns19:id>
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>5a2ba6db-4ab3-4acc-805c-65e18e31ea49</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tab section</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>f9673b2e-307f-428e-87e2-c09d3df53b14</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>56311dd8-9633-47fb-81fc-3ea3e918dbdd</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>6066ea31-de2c-48ae-8a9a-1f299820b4d9</ns19:id>
                                                            <ns19:optionName>tabsStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>10d37d44-cd63-4c8b-8eff-a7267cd08bfe</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>3f6ef11c-0219-448f-864c-af365b38bbfe</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>d7302254-20af-4759-8e6e-cc538d2730e7</ns19:id>
                                                                <ns19:layoutItemId>0</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>a49c69ef-e6c3-479e-8699-50e973e19547</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>971f23a4-fc93-4030-8adf-e902b7454f42</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>fc055b43-b11e-43f4-8e4d-fc1edf66c90f</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b9d40b31-0908-4cdf-8eef-e5bcc131834d</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2399e918-36a7-45a8-8e13-4c966f2d1c0f</ns19:id>
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>abbb2735-4f54-4517-8aff-723c7cc10d92</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>c7793482-0481-457e-8cbf-9b7ea0be2bb1</ns19:id>
                                                                <ns19:layoutItemId>1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>9a05a08c-344c-441d-8f46-fb02bd60434c</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f12a9cc6-0213-429c-87c4-7089995c3e59</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a616b111-c426-49f6-83e1-c2c351bb372c</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>fb1b9b52-b0e8-4634-8aa3-6622672b011a</ns19:id>
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    <ns19:value>tw.local.havePaymentTerms</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3b586a0d-85c1-47b3-8129-bd9f606dfb29</ns19:id>
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3fa9865d-ca9d-472a-8921-fd38e7c65288</ns19:id>
                                                                    <ns19:optionName>addBill</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5cb3a3a4-f514-4a9c-868d-a87ec2d33742</ns19:id>
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>24e1eae1-a4e1-4485-8068-2c40b4fb0d60</ns19:id>
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>112a7e0b-2c2f-4d13-8676-43b67bc55d63</ns19:id>
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6841e7eb-42de-4aa2-856e-22dfe6bce1c2</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>250eed0d-bd5a-42fd-8d83-16cbcc6bd519</ns19:id>
                                                                    <ns19:optionName>billExist</ns19:optionName>
                                                                    <ns19:value>0</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7c0a309b-b9a6-4a13-8653-985052c5428a</ns19:id>
                                                                    <ns19:optionName>invoiceExist</ns19:optionName>
                                                                    <ns19:value>0</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5d779693-5be0-46a8-8013-db4d7138db15</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a11fb4e0-3625-45cc-801c-350e1728ae62</ns19:id>
                                                                    <ns19:optionName>IDCProduct</ns19:optionName>
                                                                    <ns19:value>tw.local.idcContract.IDCProduct</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e0ecf3cf-45c9-4364-8aa0-d07f19a0238d</ns19:id>
                                                                    <ns19:optionName>interestVis</ns19:optionName>
                                                                    <ns19:value>tw.local.interestVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>bd6b067c-ec38-4291-8db7-6ef93cdc5ffa</ns19:id>
                                                                    <ns19:optionName>isHub</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e69f4def-acf2-42ed-8941-3626e9a6d838</ns19:id>
                                                                    <ns19:optionName>idcContract</ns19:optionName>
                                                                    <ns19:value>tw.local.idcContract</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e27ab2a5-9458-493f-8350-d48a78e849bf</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>90935fb6-7ab9-4d25-83df-e8530133d970</ns19:id>
                                                                    <ns19:optionName>InterestAndChargesList</ns19:optionName>
                                                                    <ns19:value>tw.local.InterestsAndChargesList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7cb1917c-95fb-41d0-8a67-f32681c082dc</ns19:id>
                                                                    <ns19:optionName>adviceCodeList</ns19:optionName>
                                                                    <ns19:value>tw.local.adviceCodeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>880c4ee7-e305-4bd5-86a3-b79bffcd6174</ns19:id>
                                                                    <ns19:optionName>stage</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestStage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>f6a9375f-c9a4-4f81-8a35-bc2a1cad69fe</ns19:id>
                                                                <ns19:layoutItemId>2</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>65e374a6-cbb8-4765-8581-8b97f0553ebc</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details - Branch</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>20316d6b-bed1-4add-83c9-de5ac9ac1789</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>908b31d5-07da-46bd-879f-1901e98fdbf3</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>738ddf1b-65d7-41ff-8cec-a31581bba0f1</ns19:id>
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    <ns19:value>tw.local.advancePaymentsUsedVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>67742af4-05b0-4bc6-81df-4e6658987a19</ns19:id>
                                                                    <ns19:optionName>actionEPV</ns19:optionName>
                                                                    <ns19:value>[]</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>57b1c6bd-a69c-4953-855c-19930867ff8c</ns19:id>
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b32897dc-8fe2-4c69-8577-4c73caecd880</ns19:id>
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>707a3f7f-c62e-46f9-8245-3f289b2126fe</ns19:id>
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6fde81ad-7434-4901-8fb1-5b8856ec89fa</ns19:id>
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>75a5a5f9-2019-4a06-8f8a-7eb471cb9ebc</ns19:id>
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ab2fe7aa-ffcc-4e4a-8ce9-4150cef4039b</ns19:id>
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    <ns19:value>tw.local.tmpusdAdvancePayment</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cd60a9fe-4ad1-4692-8b95-fcf86046ada2</ns19:id>
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    <ns19:value>tw.local.haveAmountAdvanced</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>eb667ce1-810b-4e14-89d6-a267325ad9d5</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>febef72a-fd2d-4a63-8dbe-95bc60d77e80</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8b83e66d-e80d-4a06-82f6-25c2cbf32742</ns19:id>
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f364cb74-dee3-49cc-8ac4-feaea93e921a</ns19:id>
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f7605249-c0ce-453d-81cb-5bac4f30a2dc</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>bf758623-ce9d-447d-8f4f-00fdcb191c15</ns19:id>
                                                                <ns19:layoutItemId>3</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>5440e320-b256-4a5a-8baa-3b7b7f28ab2d</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details - Trade FO</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>78495ab9-c1d3-418d-841f-7a9ba14b5329</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2e79aaf6-253f-4354-8cd6-9eafd9c95b40</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3f8d3175-518d-456f-89fd-fb1b6885c376</ns19:id>
                                                                    <ns19:optionName>beneficiaryDetails</ns19:optionName>
                                                                    <ns19:value>tw.local.BeneficiaryDetailsOption</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1c4544ba-3dc1-4e92-8aa7-2d36060f8f02</ns19:id>
                                                                    <ns19:optionName>havePaymentTerms</ns19:optionName>
                                                                    <ns19:value>tw.local.havePaymentTerms</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>251b0d33-4a9b-40d3-8660-acb35236065d</ns19:id>
                                                                    <ns19:optionName>haveTradeFOReferenceNumber</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3c34aeb0-0116-40de-8e18-24460da4b225</ns19:id>
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ec382bff-936d-4ae5-8c67-07669b41e588</ns19:id>
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    <ns19:value>tw.local.haveAmountAdvanced</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ed007d74-72c8-4609-8cc0-1ae612e45f19</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0399e9fb-de30-4b9e-814a-1c99ee8b8143</ns19:id>
                                                                    <ns19:optionName>LimitsVis</ns19:optionName>
                                                                    <ns19:value>tw.local.idcContract.limitsTrackingVIs</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.848ab487-8214-4d8b-88fd-a9cac5257791</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>a0ecb3be-76e9-4a71-8db5-4db78a20e9e2</ns19:id>
                                                                <ns19:layoutItemId>4</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>0c641917-e7bb-48c3-8419-483f83787321</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Products Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c8933b7f-e887-4d46-887b-42d03f7bad3f</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9608efc2-f4ac-4abb-88eb-2194a21fb1fc</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>781cace5-4e7a-4928-8ffd-9653d63bab0f</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e6e93a5e-9cc8-4158-8ab9-a1e172a258c9</ns19:id>
                                                                    <ns19:optionName>testList</ns19:optionName>
                                                                    <ns19:value>tw.local.cifs[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.bd0ada34-acf3-449a-91df-9aa363c2b280</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.productsDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>7fb63c08-1540-4314-8542-1aff8bc5f117</ns19:id>
                                                                <ns19:layoutItemId>samah</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>c11ac0bf-b2ea-4df0-8ccd-a0fed38e7cdb</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Charges and Commissions</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>98b0ca8f-**************-45fb925aedee</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a2633e0e-b18b-4103-8146-23e5dbd20f16</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b2f84310-73c3-4835-8241-eba6ea42db17</ns19:id>
                                                                    <ns19:optionName>owner</ns19:optionName>
                                                                    <ns19:value>tw.local.accounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>65d4fd3b-9616-4833-8b0a-bab193cc8d5d</ns19:id>
                                                                    <ns19:optionName>accounteeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.accounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c10087f3-4fad-403a-84d7-181473b12971</ns19:id>
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.customerCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7de19115-4f02-4682-8fb4-d7c562ae1b0d</ns19:id>
                                                                    <ns19:optionName>accountNumberList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountNumberList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3a3a96ce-6f15-406f-85cf-4e0166f00cb8</ns19:id>
                                                                    <ns19:optionName>accountList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>56ca78cb-cb03-41f6-8dae-7f40828db4e8</ns19:id>
                                                                    <ns19:optionName>caseCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.caseCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>178add51-1469-4f02-835c-32e17e707c32</ns19:id>
                                                                    <ns19:optionName>mainCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.mainCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>223d425e-2483-40f1-8610-db21f4a3cc8c</ns19:id>
                                                                    <ns19:optionName>draweeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.draweeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8a72f9d1-8316-41fb-8570-4058542efbe4</ns19:id>
                                                                    <ns19:optionName>exCurrency</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5cc2485b-aa0d-4b45-89b2-a01e3ba20cd0</ns19:id>
                                                                    <ns19:optionName>tempCommissions</ns19:optionName>
                                                                    <ns19:value>tw.local.tempCommissions[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f125e866-7151-4577-8f76-277e75057a76</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>tw.local.isChecker</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f9e4a967-5a74-49ad-87a2-b0a8979c1074</ns19:id>
                                                                    <ns19:optionName>concatExCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.concatExCurrency</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d2799564-2083-4aca-87e0-4bca4be0aef9</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b8e10fc9-d07b-4e46-8ee8-358c216e4ebf</ns19:id>
                                                                    <ns19:optionName>isFound</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>25b98154-2944-485b-8764-e2f7be38c9c0</ns19:id>
                                                                    <ns19:optionName>commCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.commCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>addedac9-7134-4cc5-86cf-6df490ece58e</ns19:id>
                                                                    <ns19:optionName>accountIndexC</ns19:optionName>
                                                                    <ns19:value>tw.local.accountIndexC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d2c1e6c9-c3c9-4d03-85b7-5389326de048</ns19:id>
                                                                    <ns19:optionName>isGLFoundC</ns19:optionName>
                                                                    <ns19:value>tw.local.isGLFoundC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>78f6313e-9796-4fba-81c9-5c1dba8e9199</ns19:id>
                                                                    <ns19:optionName>CommAccountList</ns19:optionName>
                                                                    <ns19:value>tw.local.commAccountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>60bd9c77-3816-4dc4-859c-511ace239a47</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8006c537-9812-43ac-8aeb-1f13673612a0</ns19:id>
                                                                    <ns19:optionName>commClassCode</ns19:optionName>
                                                                    <ns19:value>tw.local.commClassCode</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ffceb684-1360-4f17-80ca-3cb89834e477</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract.commissionsAndCharges[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>ffea0abc-6037-4d8b-81a0-bb7c28849ad2</ns19:id>
                                                                <ns19:layoutItemId>Limits_Tracking1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>4f2bc48c-0498-4ec6-8ff2-0408da211d22</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Limits Tracking</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f3b814cc-97f1-4d7c-8b5c-311ed9408d4b</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1b363a52-cd38-481e-8478-5744d808a0c2</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ed64ba82-5045-4cca-8ff6-3765782f0c4f</ns19:id>
                                                                    <ns19:optionName>SectionVis</ns19:optionName>
                                                                    <ns19:value>tw.local.facilityVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>156110c5-665c-4a16-8a8d-5533acb3a840</ns19:id>
                                                                    <ns19:optionName>facilityPercentageToBookVis</ns19:optionName>
                                                                    <ns19:value>tw.local.facilityPercentageToBookVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b547fde6-1d2f-4809-82d0-4315db8d65c8</ns19:id>
                                                                    <ns19:optionName>facilitiesCodesList</ns19:optionName>
                                                                    <ns19:value>tw.local.facilityCodes[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c94cf460-e182-47e6-890b-32639a6204c5</ns19:id>
                                                                    <ns19:optionName>facility</ns19:optionName>
                                                                    <ns19:value>tw.local.facility</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>86cea20b-51f9-4a9a-85b4-d8c5394cc7ba</ns19:id>
                                                                    <ns19:optionName>LimitsVis</ns19:optionName>
                                                                    <ns19:value>tw.local.idcContract.limitsTrackingVIs</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c43bbe91-dc9b-4843-8594-8e9acd733a88</ns19:id>
                                                                    <ns19:optionName>limitMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.limitMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>5be280e6-cc8a-441e-8ca3-8517d37cd406</ns19:id>
                                                                <ns19:layoutItemId>7</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>1f9b29a0-c593-4812-8fcf-cceb171144fa</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Parties</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>08879760-2255-4c7b-8f03-366b8d8de561</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d2dd4480-0c4e-46a6-83a8-709a8aed182a</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9505b718-1bdd-4c08-8b9f-36aa4d36a71a</ns19:id>
                                                                    <ns19:optionName>partyTypeList</ns19:optionName>
                                                                    <ns19:value>tw.local.partyTypeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>dba6049a-8c7c-4435-84cb-1526c0297b11</ns19:id>
                                                                    <ns19:optionName>concatString</ns19:optionName>
                                                                    <ns19:value>tw.local.concatString</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5d702eb3-9508-4410-814e-63523033cd88</ns19:id>
                                                                    <ns19:optionName>BeneficiaryDetails</ns19:optionName>
                                                                    <ns19:value>tw.local.BeneficiaryDetailsOption</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>382bd8d2-0476-4218-8bc0-202fd2f7bffa</ns19:id>
                                                                    <ns19:optionName>partyIndex</ns19:optionName>
                                                                    <ns19:value>tw.local.partyIndex</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8e69a4ee-7da0-4242-8ae5-e872ee1934b2</ns19:id>
                                                                    <ns19:optionName>customerFullDetails</ns19:optionName>
                                                                    <ns19:value>tw.local.customerFullDetails</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>96ab7b64-32be-4694-8171-c04a5bf62bd0</ns19:id>
                                                                    <ns19:optionName>selectedBIC</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedBIC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4a72ab21-08a4-4c32-8b08-f2b3d4859d8e</ns19:id>
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b21f96fb-ebe2-4b1a-87bf-881c18348948</ns19:id>
                                                                    <ns19:optionName>accounteeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.accounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cf9fbf00-d787-4c00-8f81-c4ef47151bfe</ns19:id>
                                                                    <ns19:optionName>caseCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.caseCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0b008e47-501c-4438-8f81-f8a997b6f390</ns19:id>
                                                                    <ns19:optionName>addressBICList</ns19:optionName>
                                                                    <ns19:value>tw.local.addressBICList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>06ea273d-dbfd-4895-8b62-3b3361a9049d</ns19:id>
                                                                    <ns19:optionName>selectedCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8a664dc1-2be4-4980-8feb-571c697434da</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d7fb8922-d23c-40d9-8b9c-7bfc5a7200e7</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract.party[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>8af214e5-2414-49b8-8184-b89ef5072a61</ns19:id>
                                                                <ns19:layoutItemId>5</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>18bb9618-6804-470b-8a5f-3935516fffa6</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Contract Creation</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4f3173e8-f96d-49ce-8bd5-3bdb6437238d</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>07af5670-9b11-43f1-830a-14e2907c2a19</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6501672c-0306-41ec-8900-791ebd623aae</ns19:id>
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>20939ee9-c6ca-44b6-8b70-7c3a8207697f</ns19:id>
                                                                    <ns19:optionName>contractcashCollateralsVisability</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f2062916-933d-47c6-8765-4bacee118794</ns19:id>
                                                                    <ns19:optionName>contractLimitsTrackingVisability</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5ad7b207-0de6-4b72-8edd-ca13c2387722</ns19:id>
                                                                    <ns19:optionName>advicesVisability</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b84d6649-a2f5-45b1-81bc-5ff6779d6e69</ns19:id>
                                                                    <ns19:optionName>contractInterestDetailsVisability</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>02dd1729-ebcc-413d-8bbc-ee453d3ca03a</ns19:id>
                                                                    <ns19:optionName>contractTransactionDetailsVisability</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>aa797eb9-fe0c-4cd2-8ccc-5631c3884ae2</ns19:id>
                                                                    <ns19:optionName>contractcashCollateralsOption</ns19:optionName>
                                                                    <ns19:value>tw.local.contractCashCollateralsVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>32e3857b-851a-48e8-8146-292dea7e6e9c</ns19:id>
                                                                    <ns19:optionName>advicesOption</ns19:optionName>
                                                                    <ns19:value>tw.local.contractAdvicesVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d1eb0835-592c-48c5-8096-ef7085addfd8</ns19:id>
                                                                    <ns19:optionName>contractLimitsTrackingOption</ns19:optionName>
                                                                    <ns19:value>tw.local.contractLimitsTrackingVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6c6073b2-f9f4-4620-8fd9-2f14d674c860</ns19:id>
                                                                    <ns19:optionName>contractInterestDetailsOption</ns19:optionName>
                                                                    <ns19:value>tw.local.contractInterestDetailsVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>713dfd71-58b8-4fff-8afd-b286da42dfd0</ns19:id>
                                                                    <ns19:optionName>contractTransactionDetailsOption</ns19:optionName>
                                                                    <ns19:value>tw.local.contractTransactionDetailsVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8a048e75-9004-4180-8dce-f9ef119c16d8</ns19:id>
                                                                    <ns19:optionName>partyTypeList</ns19:optionName>
                                                                    <ns19:value>tw.local.partyTypeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f4c9be11-52b5-456f-8c17-b1d48015153d</ns19:id>
                                                                    <ns19:optionName>concatString</ns19:optionName>
                                                                    <ns19:value>tw.local.concatString</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>426fe51c-115a-47a6-8559-3bac602d7de8</ns19:id>
                                                                    <ns19:optionName>BeneficiaryDetails</ns19:optionName>
                                                                    <ns19:value>tw.local.BeneficiaryDetailsOption</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3f4c99b4-1ebe-4683-84ac-556afffc3aeb</ns19:id>
                                                                    <ns19:optionName>partyIndex</ns19:optionName>
                                                                    <ns19:value>tw.local.partyIndex</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5e7bae6a-fa3a-4cb7-80c9-4becdd237465</ns19:id>
                                                                    <ns19:optionName>accountNumberList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountNumberList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5c92c062-d080-4ca2-8d81-9143e6994837</ns19:id>
                                                                    <ns19:optionName>accountList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>64ecd55f-f26e-46e0-8d0c-c5b7da5fceca</ns19:id>
                                                                    <ns19:optionName>customerFullDetails</ns19:optionName>
                                                                    <ns19:value>tw.local.customerFullDetails</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e5cb0ee5-a910-452d-8d9d-9e22d7d30c46</ns19:id>
                                                                    <ns19:optionName>isFound</ns19:optionName>
                                                                    <ns19:value>tw.local.isFound</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7a6e75e0-59f7-47e8-8e7c-f7bb5c56bbf7</ns19:id>
                                                                    <ns19:optionName>isSuccessful</ns19:optionName>
                                                                    <ns19:value>tw.local.isSuccessful</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6c9a159d-d0ec-41c0-8bf2-efefd25c7092</ns19:id>
                                                                    <ns19:optionName>customerCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>df534c8d-6d5c-44c0-8c97-6cdd0b7b2d75</ns19:id>
                                                                    <ns19:optionName>selectedBIC</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedBIC</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a6eff8e2-882a-4e4b-8410-91d552225a3c</ns19:id>
                                                                    <ns19:optionName>adviceCodeList</ns19:optionName>
                                                                    <ns19:value>tw.local.adviceCodeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>14eae105-53b4-402a-85d7-c825140b1a24</ns19:id>
                                                                    <ns19:optionName>tempObj</ns19:optionName>
                                                                    <ns19:value>tw.local.tempObj</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1ef370be-7c88-4aec-8220-2b6071ca1dbe</ns19:id>
                                                                    <ns19:optionName>partyAccountList</ns19:optionName>
                                                                    <ns19:value>tw.local.partyAccountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a4a9a20e-4474-4522-840a-cc09eadb5095</ns19:id>
                                                                    <ns19:optionName>sumAccountList</ns19:optionName>
                                                                    <ns19:value>tw.local.sumAccountList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>********-f308-4cb7-8c14-257a0292abfb</ns19:id>
                                                                    <ns19:optionName>accounteeCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.accounteeCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2343705b-8105-4141-8285-61e1107c9387</ns19:id>
                                                                    <ns19:optionName>caseCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.caseCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a122ddc7-fa1a-4896-8b72-cd807112e620</ns19:id>
                                                                    <ns19:optionName>exCurrency</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2493079d-569a-4c06-8847-89d442b0f918</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1a0c0b81-6a4d-4265-8e8d-a69d73d2844e</ns19:id>
                                                                    <ns19:optionName>partiesOption</ns19:optionName>
                                                                    <ns19:value>tw.local.partiesVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>edb35141-ffab-4ec3-89e2-07568ddbcab6</ns19:id>
                                                                    <ns19:optionName>requestCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.financialDetails.documentCurrency.code</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>671e20e6-91b2-46f6-80d1-37ece62ee6ee</ns19:id>
                                                                    <ns19:optionName>partiesVis</ns19:optionName>
                                                                    <ns19:value>tw.local.partiesVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c8eada5c-8713-4f53-802d-3b3f493ba43c</ns19:id>
                                                                    <ns19:optionName>amtPayableByNBE</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.financialDetails.amtPayableByNBE</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4c20f3aa-469e-4287-8ee9-9168295b01a2</ns19:id>
                                                                    <ns19:optionName>intDefaultAmount</ns19:optionName>
                                                                    <ns19:value>tw.local.intDefaultAmount</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>91a8b60c-f9b7-4282-82ea-515565847ebd</ns19:id>
                                                                    <ns19:optionName>minAmount</ns19:optionName>
                                                                    <ns19:value>tw.local.minAmount</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2072430e-1805-48bb-85cb-763b93be735c</ns19:id>
                                                                    <ns19:optionName>chargesIndex</ns19:optionName>
                                                                    <ns19:value>tw.local.chargesIndex</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c4d7fce5-d81c-451e-8b4f-d0afd6fa128b</ns19:id>
                                                                    <ns19:optionName>concatExCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.concatExCurrency</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>779cd5d4-fe37-46be-8dd7-1d28d01a9a67</ns19:id>
                                                                    <ns19:optionName>InterestAndChargesList</ns19:optionName>
                                                                    <ns19:value>tw.local.InterestsAndChargesList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4d4caf01-1c36-4702-8f37-5a7c0d6120fc</ns19:id>
                                                                    <ns19:optionName>concatInterest</ns19:optionName>
                                                                    <ns19:value>tw.local.concatInterest</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d3251b62-5d3f-4106-821d-b99856a64693</ns19:id>
                                                                    <ns19:optionName>contractAmount</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.financialDetails.documentAmount</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>03ab382e-d7e2-4105-8666-03e46d638539</ns19:id>
                                                                    <ns19:optionName>isGLFound</ns19:optionName>
                                                                    <ns19:value>tw.local.isGLFound</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>df1b2d28-1bae-4353-8628-23855614e297</ns19:id>
                                                                    <ns19:optionName>accountIndex</ns19:optionName>
                                                                    <ns19:value>tw.local.accountIndex</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>********-5395-4cba-8c0c-7dee0f15dd41</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>tw.local.isChecker</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>299eb5b5-604f-448d-8267-4ef74c66a63c</ns19:id>
                                                                    <ns19:optionName>stage</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestStage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c177eb7e-6e8a-41c7-8aaa-40ca0381474f</ns19:id>
                                                                    <ns19:optionName>adviceData</ns19:optionName>
                                                                    <ns19:value>tw.local.adviceData</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d9286620-a30b-42f3-89bb-9ee7deee50e8</ns19:id>
                                                                    <ns19:optionName>concatComm</ns19:optionName>
                                                                    <ns19:value>tw.local.concatComm</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>19ff959d-5f66-4330-85a1-917bdae427ce</ns19:id>
                                                                    <ns19:optionName>facilityCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b6f57593-bc90-43a1-8ed2-a5c978fc4655</ns19:id>
                                                                    <ns19:optionName>errorMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.collateralError</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f6501ac0-a7bd-4c52-8d21-a5593e7282c9</ns19:id>
                                                                    <ns19:optionName>selectedCIF</ns19:optionName>
                                                                    <ns19:value>tw.local.selectedCIF</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e80e0656-7510-4f46-8c61-9242d87ffa22</ns19:id>
                                                                    <ns19:optionName>Suppressedvis</ns19:optionName>
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e86aa518-d554-4dca-81f5-5c596c60c951</ns19:id>
                                                                    <ns19:optionName>limitedPartyTypeList</ns19:optionName>
                                                                    <ns19:value>tw.local.limitedPartyTypeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>649ed432-c019-4791-8d84-fc525847aa2b</ns19:id>
                                                                    <ns19:optionName>interestIsVisible</ns19:optionName>
                                                                    <ns19:value>tw.local.interestIsVisible</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b42460c3-abaf-468f-8e8f-c382febc5d17</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8ddda15b-cd8f-4b7d-8e68-c91cbfe99f78</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5bff2054-55c0-4763-8b7f-312ae094f05c</ns19:id>
                                                                    <ns19:optionName>interestVis</ns19:optionName>
                                                                    <ns19:value>tw.local.interestVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.0fa21995-2169-498c-ba2e-ea66c3dc5616</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>291f4763-69e7-451b-8425-32e93586b2d0</ns19:id>
                                                                <ns19:layoutItemId>8</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>70bd94e2-d55b-4520-8ddc-611014d819d8</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Attachment</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a0a35733-1c07-4350-81b3-1b3a98634d4b</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>bec6560e-edbb-4b96-89ea-fb57d0e8b4d4</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0498b79b-41c7-432b-8869-1b7acd06eee9</ns19:id>
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3303900c-d707-4c52-889c-251950ae03de</ns19:id>
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>341d3618-5218-4c6d-80eb-1b0dfec19c98</ns19:id>
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0a6f1428-727f-470d-8fbd-a2cf57f31674</ns19:id>
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0d3fd88a-c1e0-4336-81c1-bbfa68221530</ns19:id>
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>d8b5db02-af64-423f-869c-8062dd9a3c68</ns19:id>
                                                                <ns19:layoutItemId>9</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>68c431e7-3d8b-4511-8373-692c51fc975e</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>History</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e3f55ee2-f36c-4682-87bb-b70e215b148b</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>05f99566-66b8-4484-8db1-c6bd4ae15a23</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>67cf9621-cd0d-4fc0-811b-67d776f76ff5</ns19:id>
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="2025.7138dc8f-1707-4eb8-8721-15624a4ff914" targetRef="2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4" name="To Validation" id="2027.d90e985e-7d94-48b2-8c87-b1d0d946ddd0">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="4aef87be-8491-442e-8991-056f8307fefc">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.5b5b3a73-163c-43d3-b776-cf0497d4bea4" targetRef="2025.7138dc8f-1707-4eb8-8721-15624a4ff914" name="To IDC Execution Hub Initiation" id="2027.afbaa6a5-e738-4c50-84c2-6638602bfba7">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:htmlHeaderTag id="e43aba5d-e2a0-41a7-9bbe-dc9522b67b82">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        <ns3:mobileReady>true</ns3:mobileReady>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="dc15769d-ecc6-4ec1-8bcb-abf64ee084d3" />
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="9f9b80af-eb57-4778-8882-1ef810d6a5e6" />
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="b20b243e-e71a-469d-8bf0-295daf432767" />
                                <ns3:epvProcessLinkRef epvId="21.e5829eee-0ab1-4f47-9191-f0f8705bc33e" epvProcessLinkId="cef89e5c-a12c-4df5-8b24-1929b0909dd5" />
                            </ns3:epvProcessLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.0d99a3e7-a8de-4163-b3fb-55786de20aa1">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = {};
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new Date();
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.d52aaa6a-fe03-486b-a2ec-bf603fe7aa57">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = [];
autoObject.settlementAccounts[0] = {};
autoObject.settlementAccounts[0].debitedAccount = {};
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = {};
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = {};
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = [];
autoObject.settlementAccounts[0].accountNumberList[0] = {};
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = {};
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = [];
autoObject.party[0] = {};
autoObject.party[0].partyType = {};
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = {};
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = {};
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new Date();
autoObject.liquidationSummary.creditValueDate = new Date();
autoObject.IDCProduct = {};
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new Date();
autoObject.transactionMaturityDate = new Date();
autoObject.commissionsAndCharges = [];
autoObject.commissionsAndCharges[0] = {};
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = {};
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = {};
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = {};
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = {};
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = [];
autoObject.commissionsAndCharges[0].commAccountList[0] = {};
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new Date();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = {};
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = {};
autoObject.swiftMessageData.intermediary = {};
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = {};
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = {};
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = {};
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = {};
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = {};
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = {};
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = {};
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = {};
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = {};
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = [];
autoObject.advices[0] = {};
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = {};
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = [];
autoObject.cashCollateralAccounts[0] = {};
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = {};
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new Date();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = [];
autoObject.contractLimitsTracking[0] = {};
autoObject.contractLimitsTracking[0].partyType = {};
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new Date();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = [];
autoObject.accountNumberList[0] = {};
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = [];
autoObject.facilities[0] = {};
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new Date();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = [];
autoObject.facilities[0].facilityLines[0] = {};
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new Date();
autoObject.facilities[0].facilityLines[0].facilityBranch = {};
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = [];
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = [];
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = [];
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = [];
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = {};
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = {};
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject.limitsTrackingVIs = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.ca1a49de-f726-4195-afa3-86c1831cf286">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.b1ad4f17-5a19-4eec-a57c-188f58f96e72">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="complianceComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.646e6c2d-aa08-4fa8-adca-c95fecc02155">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.30405cca-92a3-4efd-a7aa-5370d77ebb9d">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].startTime = new Date();
autoObject[0].endTime = new Date();
autoObject[0].userName = "";
autoObject[0].role = "";
autoObject[0].step = "";
autoObject[0].action = "";
autoObject[0].comment = "";
autoObject[0].terminateReason = "";
autoObject[0].returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.d5114bbb-7d27-4d40-a232-641034e87d67">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.fullPath = "";
autoObject.cmisQuery = "";
autoObject.defaultProperties = [];
autoObject.defaultProperties[0] = {};
autoObject.defaultProperties[0].name = "";
autoObject.defaultProperties[0].value = null;
autoObject.defaultProperties[0].editable = false;
autoObject.defaultProperties[0].hidden = false;
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.bcfcce29-02b7-4e2e-a443-1199515bbf9b">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="facilityCodes" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.a9e71516-841c-4e44-a1b9-b68f71618f8f">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].value = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.036382d6-78e6-4da7-9eed-0aa5d5d93300" />
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.ab4deee4-4ad4-4756-933a-86b2c19fde70" />
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.26756e9b-2946-4377-9b62-43e6ffbffa25" />
                        <ns16:inputSet />
                        <ns16:outputSet />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2049c715-4da5-4dc1-a331-02a2eb21325f</processLinkId>
            <processId>1.9e0de1a3-7709-49c8-9b29-963ea4784c51</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.df9bbc7e-d1fe-4121-bd0f-cacf84294522</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.81f1324d-ddb1-4355-85b4-5c743fa93da4</toProcessItemId>
            <guid>9f966b71-740d-43b9-9591-4222c864ecee</guid>
            <versionId>eb524d24-c624-43e2-91f3-34e710e597e5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.df9bbc7e-d1fe-4121-bd0f-cacf84294522</fromProcessItemId>
            <toProcessItemId>2025.81f1324d-ddb1-4355-85b4-5c743fa93da4</toProcessItemId>
        </link>
    </process>
</teamworks>

