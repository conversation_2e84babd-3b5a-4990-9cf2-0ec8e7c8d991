<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.69de9393-41a9-476a-9c6c-10054e92ebb8" name="Get Charge Details">
        <lastModified>1692506049938</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>267ee147-61be-409f-8cc6-2c669f85400e</guid>
        <versionId>c0102c8d-302a-4a3b-8d2c-d4e7365549f9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e43" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.856eec82-7270-4f3a-abf4-945e093554b7"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"3662d4df-63d0-4380-a77b-1c5f06c2ad63"},{"incoming":["c7a38902-452d-41c6-aaa9-a3018917b180","f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a06"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c95d3f31-da13-4eef-bb58-557ddc25e615"},{"targetRef":"c6abe11f-f8c4-4fac-8950-837bef531a41","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.856eec82-7270-4f3a-abf4-945e093554b7","sourceRef":"3662d4df-63d0-4380-a77b-1c5f06c2ad63"},{"itemSubjectRef":"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e","name":"interestsAndCharges","isCollection":true,"declaredType":"dataObject","id":"2056.194a4070-e353-47f1-8f0a-36a3ae0b5b45"},{"startQuantity":1,"outgoing":["3a30d286-7534-41a1-99ff-6cdb22ec7057"],"incoming":["9fb692ef-3fa3-42e9-a6ae-13d6851a7340"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":330,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Interests and Charges","dataInputAssociation":[{"targetRef":"2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.productCode"]}}]},{"targetRef":"2055.d8783716-2453-46e3-8522-b1b2504092a2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.event"]}}]},{"targetRef":"2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.b6919232-c019-43a5-8742-9783cfd63371","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"d945fcbd-8147-4331-b0a9-7bdfbba623a9","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.dd74dd40-8fa5-4359-8243-08e144b543d2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.eventCode"]}}],"sourceRef":["2055.78c4796d-3236-45f3-883b-556500834b95"]}],"calledElement":"1.e059295b-f72a-4e32-a329-8d32ebe941de"},{"targetRef":"80b33f50-f73b-4b29-a103-865bddeb6206","extensionElements":{"endStateId":["guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"3a30d286-7534-41a1-99ff-6cdb22ec7057","sourceRef":"d945fcbd-8147-4331-b0a9-7bdfbba623a9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"INIT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"event","isCollection":false,"declaredType":"dataObject","id":"2056.0e5e1068-a0ca-4354-bd34-4abc67237379"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.d71f2e20-0991-4ea3-8e3d-c0dad3c4218f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.2a384d72-d350-443d-8e6d-fde19412e259"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.6792e29c-4af0-4e0b-9178-9b7dfa853cf8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.131e61d0-fe94-4499-9d5f-efecc8c031e5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.6a506baa-5c47-498e-bd3b-88ce6467bc80"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.c27b4a94-6459-4e37-bbb9-afcfad797771"},{"startQuantity":1,"outgoing":["9fb692ef-3fa3-42e9-a6ae-13d6851a7340"],"incoming":["2027.856eec82-7270-4f3a-abf4-945e093554b7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":139,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c6abe11f-f8c4-4fac-8950-837bef531a41","scriptFormat":"text\/x-javascript","script":{"content":["try {\r\n\ttw.local.Seperated = new tw.object.listOf.String();\r\n\ttw.local.Seperated = tw.local.data.split(\",\");\r\n\ttw.local.productCode = tw.local.Seperated[0];\r\n\t\r\n\tif (tw.local.Seperated[1]==\"Initial\") {\r\n\t\ttw.local.eventCode = \"BOOK\";\t\r\n\t}\r\n\telse{\r\n\t\ttw.local.eventCode = \"INIT\";\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"targetRef":"d945fcbd-8147-4331-b0a9-7bdfbba623a9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"9fb692ef-3fa3-42e9-a6ae-13d6851a7340","sourceRef":"c6abe11f-f8c4-4fac-8950-837bef531a41"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.72b6c449-d8b5-4913-802a-2405016e4c7a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"declaredType":"dataObject","id":"2056.154af04d-32ef-45ae-ab7d-f21ff4f31ace"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"eventCode","isCollection":false,"declaredType":"dataObject","id":"2056.dba3d85b-dcb2-456c-a0df-77a85e32ae03"},{"parallelMultiple":false,"outgoing":["61626981-2881-42d6-a496-66ec2db50f8a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3fbfecff-3acb-4117-9258-c21e01872a00"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"344e67f7-4977-4aba-9279-0d9d67a6562d","otherAttributes":{"eventImplId":"3c0ca0f0-affe-4b63-81b7-ee4417350812"}}],"attachedToRef":"d945fcbd-8147-4331-b0a9-7bdfbba623a9","extensionElements":{"nodeVisualInfo":[{"width":24,"x":365,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"55d0022f-1bda-45f9-a612-ad59f2f30a61","outputSet":{}},{"targetRef":"c92c30b8-f2b9-423c-8239-943316d32f8c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"61626981-2881-42d6-a496-66ec2db50f8a","sourceRef":"55d0022f-1bda-45f9-a612-ad59f2f30a61"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.e9d15930-66ab-4a7b-b185-60ca57fcd720"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.a71ef842-7d67-4657-bcf7-99eaed8d0169"},{"outgoing":["c7a38902-452d-41c6-aaa9-a3018917b180","0b1cd04a-e96b-4881-bf0d-0f23241f13f3"],"incoming":["3a30d286-7534-41a1-99ff-6cdb22ec7057"],"default":"c7a38902-452d-41c6-aaa9-a3018917b180","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":492,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"80b33f50-f73b-4b29-a103-865bddeb6206"},{"targetRef":"c95d3f31-da13-4eef-bb58-557ddc25e615","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Get Interests and Charges","declaredType":"sequenceFlow","id":"c7a38902-452d-41c6-aaa9-a3018917b180","sourceRef":"80b33f50-f73b-4b29-a103-865bddeb6206"},{"targetRef":"c92c30b8-f2b9-423c-8239-943316d32f8c","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0b1cd04a-e96b-4881-bf0d-0f23241f13f3","sourceRef":"80b33f50-f73b-4b29-a103-865bddeb6206"},{"parallelMultiple":false,"outgoing":["5a97e9a5-d705-469f-9211-b35b42f75bb7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"654b9dfa-c78b-42d2-bb1c-962c8a0ef4a0"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"25370995-a335-459e-bb9c-da7c73a219f4","otherAttributes":{"eventImplId":"5c263038-31d5-4c38-886f-a06bbf2c67ad"}}],"attachedToRef":"c6abe11f-f8c4-4fac-8950-837bef531a41","extensionElements":{"nodeVisualInfo":[{"width":24,"x":174,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"9a94c568-93b1-46c0-a0b0-1f4342561ac7","outputSet":{}},{"targetRef":"c92c30b8-f2b9-423c-8239-943316d32f8c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"5a97e9a5-d705-469f-9211-b35b42f75bb7","sourceRef":"9a94c568-93b1-46c0-a0b0-1f4342561ac7"},{"startQuantity":1,"outgoing":["f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df"],"incoming":["0b1cd04a-e96b-4881-bf0d-0f23241f13f3","61626981-2881-42d6-a496-66ec2db50f8a","5a97e9a5-d705-469f-9211-b35b42f75bb7"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":404,"y":205,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c92c30b8-f2b9-423c-8239-943316d32f8c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"c95d3f31-da13-4eef-bb58-557ddc25e615","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df","sourceRef":"c92c30b8-f2b9-423c-8239-943316d32f8c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.050f8b3f-f741-48da-88dc-32c0d5352114"}],"laneSet":[{"id":"b579d2ab-957f-470b-9033-edd134553d27","lane":[{"flowNodeRef":["3662d4df-63d0-4380-a77b-1c5f06c2ad63","c95d3f31-da13-4eef-bb58-557ddc25e615","d945fcbd-8147-4331-b0a9-7bdfbba623a9","c6abe11f-f8c4-4fac-8950-837bef531a41","9a94c568-93b1-46c0-a0b0-1f4342561ac7","55d0022f-1bda-45f9-a612-ad59f2f30a61","80b33f50-f73b-4b29-a103-865bddeb6206","c92c30b8-f2b9-423c-8239-943316d32f8c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c6203cc5-00f2-4287-8d6b-17e4b20c7961","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Charge Details","declaredType":"process","id":"1.69de9393-41a9-476a-9c6c-10054e92ebb8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.ce68d874-ac9b-49a1-93ea-920ce054ffd0"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.bc4d0a35-b2f5-451f-b1ee-bb2f443747d4"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"ICAP\"\r\n\/\/\"ASAT\"\r\n\/\/\"IAVC\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.173f8cc0-9c85-4034-94e7-b193eff9bd2a"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.173f8cc0-9c85-4034-94e7-b193eff9bd2a</processParameterId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"ICAP"&#xD;
//"ASAT"&#xD;
//"IAVC"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c1970cd9-518b-4c3f-b2da-0800fa8fac50</guid>
            <versionId>da43db03-afa9-4bd7-bb3b-769a3acdd912</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ce68d874-ac9b-49a1-93ea-920ce054ffd0</processParameterId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a386f89e-e15a-4d7f-bfe6-aec3cfd8b13d</guid>
            <versionId>60e31c20-306b-4509-bc5d-ae8262fc7479</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bc4d0a35-b2f5-451f-b1ee-bb2f443747d4</processParameterId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>772f50c0-07bc-451b-bab0-ca68f7ba0859</guid>
            <versionId>4e0741e6-8812-4733-a8d5-ac502793ef0f</versionId>
        </processParameter>
        <processVariable name="interestsAndCharges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.194a4070-e353-47f1-8f0a-36a3ae0b5b45</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c42295d1-ea18-4d29-9e66-9405a99fca51</guid>
            <versionId>01862f01-534a-4d17-adbd-a19a8c07a9de</versionId>
        </processVariable>
        <processVariable name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0e5e1068-a0ca-4354-bd34-4abc67237379</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"INIT"</defaultValue>
            <guid>50e6c323-2741-4aaf-bda1-0080fbc1d27f</guid>
            <versionId>8aab9f70-9a47-46fc-a2ec-fe6c95efa429</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d71f2e20-0991-4ea3-8e3d-c0dad3c4218f</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b1fdd9c2-370c-42be-a2fc-7397f0d9b771</guid>
            <versionId>18f7c729-1a98-4186-a3df-3f667ea98974</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2a384d72-d350-443d-8e6d-fde19412e259</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e71dbc73-c706-4f44-b63a-2c9114720800</guid>
            <versionId>d16f2019-479a-4ec6-8bb9-86ffdcac921d</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6792e29c-4af0-4e0b-9178-9b7dfa853cf8</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>20b01068-1aed-4e25-8164-9c641cf6f097</guid>
            <versionId>a0448e93-4c1d-4adf-8e90-5f2861632722</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.131e61d0-fe94-4499-9d5f-efecc8c031e5</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>abcf29e8-7cb4-4578-8853-107351dcdbda</guid>
            <versionId>f6d40ab3-353c-4619-b792-4867ab61ed24</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6a506baa-5c47-498e-bd3b-88ce6467bc80</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ab470cc-a806-4ecd-a65e-7a9c8e9a7ccf</guid>
            <versionId>343227b5-fc7d-4730-9609-7c07132e6992</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c27b4a94-6459-4e37-bbb9-afcfad797771</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>766a8327-64f0-4ecc-8568-94e7373e3767</guid>
            <versionId>04e93976-8376-48b9-97db-b3864d2ed635</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.72b6c449-d8b5-4913-802a-2405016e4c7a</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>edc7ee2a-ed13-4872-b88d-238273bfb278</guid>
            <versionId>4110fcfb-e275-4d47-a401-5495bc30917e</versionId>
        </processVariable>
        <processVariable name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.154af04d-32ef-45ae-ab7d-f21ff4f31ace</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1b4c318f-4155-403e-a4a6-1936413098c9</guid>
            <versionId>35835788-605f-48d6-ac2b-1ef9e063cb8a</versionId>
        </processVariable>
        <processVariable name="eventCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dba3d85b-dcb2-456c-a0df-77a85e32ae03</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1aab850d-07d7-4ee8-970b-b3e21de3bfd7</guid>
            <versionId>0a5cd71d-0b72-431c-a0f6-d954fe6dc3aa</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e9d15930-66ab-4a7b-b185-60ca57fcd720</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>154de639-ff7e-4023-ab7a-6984085b94ee</guid>
            <versionId>0ea47f02-fcde-493a-828c-561a4f71755c</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a71ef842-7d67-4657-bcf7-99eaed8d0169</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5f68cb6a-b2fc-495b-8c97-3dafdba3d2f2</guid>
            <versionId>ef5d46d9-8a6a-4180-9283-adc28db6142b</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.050f8b3f-f741-48da-88dc-32c0d5352114</processVariableId>
            <description isNull="true" />
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a6944b44-3ac2-483c-9c69-f23110011870</guid>
            <versionId>e1d9e7a5-5072-4912-a447-f5b164f9dcb1</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</processItemId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ff0ce615-d76c-409d-aab1-d32ab4c4c480</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</errorHandlerItemId>
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a02</guid>
            <versionId>0d609246-a3e4-4036-af96-f7cf5973d507</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2a8a995c-b552-480a-a158-ebcd2d3f4c06</processItemPrePostId>
                <processItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>4925d584-634a-4999-9cab-8a1026906a39</guid>
                <versionId>5667a33a-fb48-497f-a373-2fd756c3218c</versionId>
            </processPrePosts>
            <layoutData x="139" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e3b</errorHandlerItem>
                <errorHandlerItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ff0ce615-d76c-409d-aab1-d32ab4c4c480</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try {&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split(",");&#xD;
	tw.local.productCode = tw.local.Seperated[0];&#xD;
	&#xD;
	if (tw.local.Seperated[1]=="Initial") {&#xD;
		tw.local.eventCode = "BOOK";	&#xD;
	}&#xD;
	else{&#xD;
		tw.local.eventCode = "INIT";&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>*************-4daf-a6a7-88c8efe85712</guid>
                <versionId>07be825f-1601-432f-a169-eb8599a3d3d6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</processItemId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.76305ae5-2b12-4cbb-a6fd-2282e8f66c95</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a04</guid>
            <versionId>8377851c-285d-4f04-8f2e-f38e0b1826bc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="492" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.76305ae5-2b12-4cbb-a6fd-2282e8f66c95</switchId>
                <guid>3ae26d40-3d34-4b67-bc25-31622d86a5e5</guid>
                <versionId>8564651a-5920-4dd2-97a9-19820540d91f</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.a799783d-1a93-4187-a1b8-c4dfe05e0353</switchConditionId>
                    <switchId>3013.76305ae5-2b12-4cbb-a6fd-2282e8f66c95</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e42</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>b8afa311-2f1b-4716-ab32-382e0fb04fc6</guid>
                    <versionId>267f775f-00b5-45ff-8d65-3920268a6643</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c95d3f31-da13-4eef-bb58-557ddc25e615</processItemId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.10bed889-dd8e-4507-a409-a34c4d632091</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a06</guid>
            <versionId>8dad1088-5aad-40d4-a2be-f7ee1e708be0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.10bed889-dd8e-4507-a409-a34c4d632091</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>22944f04-59b7-4d38-9640-c6afbd49a99e</guid>
                <versionId>77e2b3e9-2fbc-4793-81cb-c428536db01d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d945fcbd-8147-4331-b0a9-7bdfbba623a9</processItemId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <name>Get Interests and Charges</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</errorHandlerItemId>
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a03</guid>
            <versionId>ece55cda-55ee-46c4-a589-51243829a613</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="330" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e3b</errorHandlerItem>
                <errorHandlerItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.e059295b-f72a-4e32-a329-8d32ebe941de</attachedProcessRef>
                <guid>497b0240-f381-4af8-ac27-bcf33ad2555d</guid>
                <versionId>*************-483a-9197-c2fe620fb60f</versionId>
                <parameterMapping name="chargesAndInterest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8c3a8f4d-0137-488c-86e4-e9363a2db365</parameterMappingId>
                    <processParameterId>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>f2b21d2c-47c4-4baf-bd68-e2cdd176592c</guid>
                    <versionId>037827b9-0b08-42ef-a967-96097c39f6db</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0f5139bd-7b6e-4f31-9e2f-487ea990ba67</parameterMappingId>
                    <processParameterId>2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>29b83822-c033-4972-9930-bcae75859a98</guid>
                    <versionId>0ba391b0-62d9-448f-9a69-c2cf7d613693</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="event">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.086f9960-2822-460b-9218-39574dbeb107</parameterMappingId>
                    <processParameterId>2055.d8783716-2453-46e3-8522-b1b2504092a2</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.event</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4dc80402-c8ed-48bc-94a8-091489286c75</guid>
                    <versionId>1ba85c3a-246c-4158-a1ab-b027e1555c47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.27b3247e-f391-445a-87ee-78446985cd28</parameterMappingId>
                    <processParameterId>2055.b6919232-c019-43a5-8742-9783cfd63371</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d4dc3adc-e056-4bda-84dc-ea62aa80855e</guid>
                    <versionId>281ac6db-e630-4de8-867b-91500d6f3242</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4d0f36f6-95d8-45ce-b522-253f6dda2e35</parameterMappingId>
                    <processParameterId>2055.78c4796d-3236-45f3-883b-556500834b95</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.eventCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>de71d98b-d2d6-45b5-ae4c-6736cc2ec20c</guid>
                    <versionId>46315d2f-42e8-4252-9c9c-2a2a05673f52</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.007a038b-2a50-40ba-b4ce-898559be16d5</parameterMappingId>
                    <processParameterId>2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>18a8a0a6-425e-449a-a411-815cc8b94a7b</guid>
                    <versionId>49133724-112f-4c6e-acdc-c4f2c2e5413a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.790c96f4-c07b-4b3b-b805-244e48f729bf</parameterMappingId>
                    <processParameterId>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>489e2408-d022-4e08-837f-b0a37c6faff6</guid>
                    <versionId>4e1fac93-e8ee-4f29-acb6-913673ec3a2a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.86b6cfb5-e07f-4f7d-bb27-e354e5d55753</parameterMappingId>
                    <processParameterId>2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>45f59dc8-8698-4217-bde2-8baad1505c26</guid>
                    <versionId>5fed5573-3def-44eb-a2fa-baabd2571030</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bad7527c-acbe-4c78-b866-580724ce338d</parameterMappingId>
                    <processParameterId>2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>9f171bd6-07ee-429b-8bc3-0091195d26f0</guid>
                    <versionId>63fb9067-abf7-4d3a-b414-1ba19c160fac</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bdb791ac-c54c-486c-8bd0-dc7eb6e45f30</parameterMappingId>
                    <processParameterId>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8306cb56-ff4e-4f3c-8558-4a908cd33264</guid>
                    <versionId>954b3a4a-1df9-4421-a65d-25bf97ea06b6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bb1f42d4-d4ac-4fa7-8f27-9676c89bc5dc</parameterMappingId>
                    <processParameterId>2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>63520a4a-7ae7-41f3-80c0-f8fcf60057f0</guid>
                    <versionId>9c8af4cd-5a28-42b2-a92c-9f579fa5077f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.26be3ceb-4dcb-4abd-8cc0-89c24ec2df3f</parameterMappingId>
                    <processParameterId>2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6</processParameterId>
                    <parameterMappingParentId>3012.6afc5a2f-9659-4172-98aa-4fbd8660f36a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ef0d086d-0835-4431-b7ee-5546f112f348</guid>
                    <versionId>a49a3d21-80ee-414d-adb6-ced76a6ddfd2</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</processItemId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ba1fff66-e140-4f42-9e16-8f25d426e107</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e3b</guid>
            <versionId>ed19a286-b740-4ad5-84d5-47f2ec7ed453</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="404" y="205">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ba1fff66-e140-4f42-9e16-8f25d426e107</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>6eeef6e5-1a9e-40fc-9e77-29c956718562</guid>
                <versionId>8e2abbf0-60dd-4112-b446-dd1964df5404</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Charge Details" id="1.69de9393-41a9-476a-9c6c-10054e92ebb8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.173f8cc0-9c85-4034-94e7-b193eff9bd2a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"ICAP"&#xD;
//"ASAT"&#xD;
//"IAVC"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.ce68d874-ac9b-49a1-93ea-920ce054ffd0" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.bc4d0a35-b2f5-451f-b1ee-bb2f443747d4" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b579d2ab-957f-470b-9033-edd134553d27">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c6203cc5-00f2-4287-8d6b-17e4b20c7961" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>3662d4df-63d0-4380-a77b-1c5f06c2ad63</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c95d3f31-da13-4eef-bb58-557ddc25e615</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d945fcbd-8147-4331-b0a9-7bdfbba623a9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c6abe11f-f8c4-4fac-8950-837bef531a41</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9a94c568-93b1-46c0-a0b0-1f4342561ac7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>55d0022f-1bda-45f9-a612-ad59f2f30a61</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>80b33f50-f73b-4b29-a103-865bddeb6206</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c92c30b8-f2b9-423c-8239-943316d32f8c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="3662d4df-63d0-4380-a77b-1c5f06c2ad63">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.856eec82-7270-4f3a-abf4-945e093554b7</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c95d3f31-da13-4eef-bb58-557ddc25e615">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:-7a06</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c7a38902-452d-41c6-aaa9-a3018917b180</ns16:incoming>
                        
                        
                        <ns16:incoming>f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="3662d4df-63d0-4380-a77b-1c5f06c2ad63" targetRef="c6abe11f-f8c4-4fac-8950-837bef531a41" name="To Script Task" id="2027.856eec82-7270-4f3a-abf4-945e093554b7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e" isCollection="true" name="interestsAndCharges" id="2056.194a4070-e353-47f1-8f0a-36a3ae0b5b45" />
                    
                    
                    <ns16:callActivity calledElement="1.e059295b-f72a-4e32-a329-8d32ebe941de" name="Get Interests and Charges" id="d945fcbd-8147-4331-b0a9-7bdfbba623a9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="330" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9fb692ef-3fa3-42e9-a6ae-13d6851a7340</ns16:incoming>
                        
                        
                        <ns16:outgoing>3a30d286-7534-41a1-99ff-6cdb22ec7057</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d8783716-2453-46e3-8522-b1b2504092a2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.event</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b6919232-c019-43a5-8742-9783cfd63371</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.78c4796d-3236-45f3-883b-556500834b95</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.eventCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="d945fcbd-8147-4331-b0a9-7bdfbba623a9" targetRef="80b33f50-f73b-4b29-a103-865bddeb6206" name="To is Successful" id="3a30d286-7534-41a1-99ff-6cdb22ec7057">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="event" id="2056.0e5e1068-a0ca-4354-bd34-4abc67237379">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"INIT"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.d71f2e20-0991-4ea3-8e3d-c0dad3c4218f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.2a384d72-d350-443d-8e6d-fde19412e259" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.6792e29c-4af0-4e0b-9178-9b7dfa853cf8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.131e61d0-fe94-4499-9d5f-efecc8c031e5" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.6a506baa-5c47-498e-bd3b-88ce6467bc80" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.c27b4a94-6459-4e37-bbb9-afcfad797771" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="c6abe11f-f8c4-4fac-8950-837bef531a41">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="139" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.856eec82-7270-4f3a-abf4-945e093554b7</ns16:incoming>
                        
                        
                        <ns16:outgoing>9fb692ef-3fa3-42e9-a6ae-13d6851a7340</ns16:outgoing>
                        
                        
                        <ns16:script>try {&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split(",");&#xD;
	tw.local.productCode = tw.local.Seperated[0];&#xD;
	&#xD;
	if (tw.local.Seperated[1]=="Initial") {&#xD;
		tw.local.eventCode = "BOOK";	&#xD;
	}&#xD;
	else{&#xD;
		tw.local.eventCode = "INIT";&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c6abe11f-f8c4-4fac-8950-837bef531a41" targetRef="d945fcbd-8147-4331-b0a9-7bdfbba623a9" name="To Exclusive Gateway" id="9fb692ef-3fa3-42e9-a6ae-13d6851a7340">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.72b6c449-d8b5-4913-802a-2405016e4c7a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="productCode" id="2056.154af04d-32ef-45ae-ab7d-f21ff4f31ace" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="eventCode" id="2056.dba3d85b-dcb2-456c-a0df-77a85e32ae03" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="d945fcbd-8147-4331-b0a9-7bdfbba623a9" parallelMultiple="false" name="Error1" id="55d0022f-1bda-45f9-a612-ad59f2f30a61">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="365" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>61626981-2881-42d6-a496-66ec2db50f8a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3fbfecff-3acb-4117-9258-c21e01872a00" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="344e67f7-4977-4aba-9279-0d9d67a6562d" eventImplId="3c0ca0f0-affe-4b63-81b7-ee4417350812">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="55d0022f-1bda-45f9-a612-ad59f2f30a61" targetRef="c92c30b8-f2b9-423c-8239-943316d32f8c" name="To End Event" id="61626981-2881-42d6-a496-66ec2db50f8a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.e9d15930-66ab-4a7b-b185-60ca57fcd720" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.a71ef842-7d67-4657-bcf7-99eaed8d0169" />
                    
                    
                    <ns16:exclusiveGateway default="c7a38902-452d-41c6-aaa9-a3018917b180" name="is Successful" id="80b33f50-f73b-4b29-a103-865bddeb6206">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="492" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3a30d286-7534-41a1-99ff-6cdb22ec7057</ns16:incoming>
                        
                        
                        <ns16:outgoing>c7a38902-452d-41c6-aaa9-a3018917b180</ns16:outgoing>
                        
                        
                        <ns16:outgoing>0b1cd04a-e96b-4881-bf0d-0f23241f13f3</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="80b33f50-f73b-4b29-a103-865bddeb6206" targetRef="c95d3f31-da13-4eef-bb58-557ddc25e615" name="To Get Interests and Charges" id="c7a38902-452d-41c6-aaa9-a3018917b180">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="80b33f50-f73b-4b29-a103-865bddeb6206" targetRef="c92c30b8-f2b9-423c-8239-943316d32f8c" name="To End Event" id="0b1cd04a-e96b-4881-bf0d-0f23241f13f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c6abe11f-f8c4-4fac-8950-837bef531a41" parallelMultiple="false" name="Error" id="9a94c568-93b1-46c0-a0b0-1f4342561ac7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="174" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>5a97e9a5-d705-469f-9211-b35b42f75bb7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="654b9dfa-c78b-42d2-bb1c-962c8a0ef4a0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="25370995-a335-459e-bb9c-da7c73a219f4" eventImplId="5c263038-31d5-4c38-886f-a06bbf2c67ad">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9a94c568-93b1-46c0-a0b0-1f4342561ac7" targetRef="c92c30b8-f2b9-423c-8239-943316d32f8c" name="To End Event" id="5a97e9a5-d705-469f-9211-b35b42f75bb7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="c92c30b8-f2b9-423c-8239-943316d32f8c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="404" y="205" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0b1cd04a-e96b-4881-bf0d-0f23241f13f3</ns16:incoming>
                        
                        
                        <ns16:incoming>61626981-2881-42d6-a496-66ec2db50f8a</ns16:incoming>
                        
                        
                        <ns16:incoming>5a97e9a5-d705-469f-9211-b35b42f75bb7</ns16:incoming>
                        
                        
                        <ns16:outgoing>f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c92c30b8-f2b9-423c-8239-943316d32f8c" targetRef="c95d3f31-da13-4eef-bb58-557ddc25e615" name="To End" id="f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.050f8b3f-f741-48da-88dc-32c0d5352114" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f13ad0f6-8379-4207-8e2f-2bd8f2a2b5df</processLinkId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c95d3f31-da13-4eef-bb58-557ddc25e615</toProcessItemId>
            <guid>d0755cc2-e820-43e0-b8e7-a1bade6a0be6</guid>
            <versionId>4418692c-52f7-4c46-9108-46f073fa031c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</fromProcessItemId>
            <toProcessItemId>2025.c95d3f31-da13-4eef-bb58-557ddc25e615</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3a30d286-7534-41a1-99ff-6cdb22ec7057</processLinkId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d945fcbd-8147-4331-b0a9-7bdfbba623a9</fromProcessItemId>
            <endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</endStateId>
            <toProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</toProcessItemId>
            <guid>d1c754dd-0098-4700-a1fe-8ac815e43418</guid>
            <versionId>54e1ec7f-ed0e-448a-9368-6da87b767736</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d945fcbd-8147-4331-b0a9-7bdfbba623a9</fromProcessItemId>
            <toProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0b1cd04a-e96b-4881-bf0d-0f23241f13f3</processLinkId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e42</endStateId>
            <toProcessItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</toProcessItemId>
            <guid>b56ad3fa-d235-4d7c-9835-eaa65532e75a</guid>
            <versionId>9d4e68db-f46d-41b6-83c2-b0635423f70f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</fromProcessItemId>
            <toProcessItemId>2025.c92c30b8-f2b9-423c-8239-943316d32f8c</toProcessItemId>
        </link>
        <link name="To Get Interests and Charges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c7a38902-452d-41c6-aaa9-a3018917b180</processLinkId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c95d3f31-da13-4eef-bb58-557ddc25e615</toProcessItemId>
            <guid>83a4d14b-b06c-41d2-8359-d0b4037583fa</guid>
            <versionId>fb7fc8a6-c5b7-498f-9923-7d0e9091250e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.80b33f50-f73b-4b29-a103-865bddeb6206</fromProcessItemId>
            <toProcessItemId>2025.c95d3f31-da13-4eef-bb58-557ddc25e615</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9fb692ef-3fa3-42e9-a6ae-13d6851a7340</processLinkId>
            <processId>1.69de9393-41a9-476a-9c6c-10054e92ebb8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d945fcbd-8147-4331-b0a9-7bdfbba623a9</toProcessItemId>
            <guid>301f19b1-2293-4ebf-ad2e-7eed1ebf8a7e</guid>
            <versionId>fccb2d25-ab1b-4d69-9dec-69ed0b3bec98</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c6abe11f-f8c4-4fac-8950-837bef531a41</fromProcessItemId>
            <toProcessItemId>2025.d945fcbd-8147-4331-b0a9-7bdfbba623a9</toProcessItemId>
        </link>
    </process>
</teamworks>

