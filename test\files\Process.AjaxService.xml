<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1" name="AJAX1">
        <lastModified>1569970535675</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId isNull="true" />
        <isRootProcess>false</isRootProcess>
        <processType>2</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType isNull="true" />
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>43200</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe5</guid>
        <versionId>04e9ee0e-3851-43c1-86c9-9e128c3fc245</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <processParameter name="Untitled1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.22b92088-07e4-452d-a105-4d4ae34e259b</processParameterId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b69ef2d5-f112-4601-a193-c3d5f35db482</guid>
            <versionId>a5dbc651-8318-4aef-b57d-15dd357455bc</versionId>
        </processParameter>
        <processParameter name="Untitled2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.963890b2-7cb2-47fb-a9e0-72c2515add86</processParameterId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a5565e78-9e81-4539-b364-ad220d7fd57b</guid>
            <versionId>7ea6df35-caf4-42fd-b54c-f6c1329a1425</versionId>
        </processParameter>
        <processVariable name="Untitled3">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2064c680-d049-4add-a08d-310a258987c5</processVariableId>
            <description isNull="true" />
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a43dfa72-b8a5-4ee2-8ce2-5f5574e8169b</guid>
            <versionId>6f50e3f7-4202-4eb7-a15f-d8d0028cc09d</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0de856ec-cba5-4a12-a330-3460a5b86753</processItemId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <name>UCA1</name>
            <tWComponentName>InvokeUca</tWComponentName>
            <tWComponentId>3009.601c968d-056f-4d49-b86a-8b336db4ba23</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe1</guid>
            <versionId>0d53e2a4-c701-4e44-a40b-87e3386927e0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="382" y="261">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <invokeUcaId>3009.601c968d-056f-4d49-b86a-8b336db4ba23</invokeUcaId>
                <isUcaActive>false</isUcaActive>
                <ucaRef>/4.f32f2065-49b8-4e77-8c58-90d96ffce088</ucaRef>
                <triggerTargetInSnapshotCtx>false</triggerTargetInSnapshotCtx>
                <guid>2ba5b742-7630-47b7-be25-33eaae4f3e77</guid>
                <versionId>e555c761-61df-4b6e-9980-9f5fa3f8b4a6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0303b3a6-6d51-4c93-8381-d3d00f3fe0ea</processItemId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <name>Service1</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c9c08e6e-4b56-4afc-8544-c4e6e7f39103</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe3</guid>
            <versionId>6f9d17cd-3b37-4e31-b326-3dad7b19c714</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="92">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c9c08e6e-4b56-4afc-8544-c4e6e7f39103</subProcessId>
                <attachedProcessRef>/1.143ff27f-5e08-478c-af65-06723fa26d26</attachedProcessRef>
                <guid>fc523462-94d1-424d-a4cd-774833fa2946</guid>
                <versionId>7828c4c7-db27-4abb-965c-30fc03c0ecbf</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4cbe235f-443f-4090-9913-901202c57270</processItemId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <name>Integration2</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c1c02c5d-67a8-428a-973f-2a5f030922d6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe2</guid>
            <versionId>8e018fce-62b0-450a-9f38-73cb8d1b7d92</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="515" y="92">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c1c02c5d-67a8-428a-973f-2a5f030922d6</subProcessId>
                <attachedProcessRef>/1.0295217c-7383-4a27-b311-333ea889f2bb</attachedProcessRef>
                <guid>06e7fcdc-c5f3-442b-9a42-a9106eaf4be7</guid>
                <versionId>b5a2a6e4-8ad7-4459-9e45-69be2208da63</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ef834233-8139-4cf5-b06e-8d1c7ca6b2cb</processItemId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <name>Terminar</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.151543aa-d42a-40a8-a3f5-48db5ecc918f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7fe4</guid>
            <versionId>d9ac822c-8315-4432-84f8-571e80e43778</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="700" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.151543aa-d42a-40a8-a3f5-48db5ecc918f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8c0c986b-e956-4790-9f96-a2d0a571e6b9</guid>
                <versionId>10376cc1-31f5-4989-8c1d-4d05747d8b62</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.10db64be-8cc3-4f4b-bbb0-30592a78c12b</epvProcessLinkId>
            <epvId>/21.ed99f470-25b4-4a03-b89d-888bc265e2aa</epvId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <guid>1b7c2034-0b7e-4001-8b1a-54fb49ccd4ad</guid>
            <versionId>f409bf13-01de-486a-a7b1-5bc52eb8c763</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.06fd96b4-7e77-4b2a-913a-801f5ba02f57</resourceProcessLinkId>
            <resourceBundleGroupId>/50.2641e279-160b-4d0d-bc96-528b36793ecf</resourceBundleGroupId>
            <processId>1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1</processId>
            <guid>42baff15-8114-42d2-8954-64c94b4903a8</guid>
            <versionId>e17ac9b3-35a8-4d7f-aab8-3791d00b57aa</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId isNull="true" />
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="20" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
    </process>
</teamworks>

