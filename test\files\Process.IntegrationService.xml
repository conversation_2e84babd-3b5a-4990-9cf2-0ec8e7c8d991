<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.0295217c-7383-4a27-b311-333ea889f2bb" name="Integration2">
        <lastModified>1569970222051</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId isNull="true" />
        <isRootProcess>false</isRootProcess>
        <processType>4</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType isNull="true" />
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>43200</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>72a78512-44e2-4740-b689-14f9d4172e7e</guid>
        <versionId>b65eb81f-6a68-4d7c-b0e1-e0c5c3ee8fe1</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.19f8ef72-a419-4b94-930b-e6fb93864e79</processParameterId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>true</isLocked>
            <description isNull="true" />
            <guid>42b3a11e-0080-4d4e-95e6-a6ff0d85b24b</guid>
            <versionId>992d0a72-2fc9-4aa2-ba2c-74badaf7fd10</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bbf9e34b-d53c-4206-be79-59788df2439d</processParameterId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>true</isLocked>
            <description isNull="true" />
            <guid>44cea2e6-dc0c-45ad-8305-d1767f21885f</guid>
            <versionId>7c06d5d3-684c-4fd3-bb9e-50e55f799367</versionId>
        </processParameter>
        <processParameter name="Untitled2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5c844b9f-794c-411e-acd8-ae53143bcc55</processParameterId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0ad3d228-44d4-425c-a15e-67f4d8dc860e</guid>
            <versionId>cbe322bb-9133-4655-8bf9-d35a0e7e177a</versionId>
        </processParameter>
        <processVariable name="Untitled1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c43fb7bf-21e3-45b3-94eb-9c5fce585494</processVariableId>
            <description isNull="true" />
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <classId>/12.60da4770-d3a3-4937-840f-8fd74f8c33ce</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>81574919-072d-4fed-accd-6d565fc1f1d3</guid>
            <versionId>89dc3ba0-ed2b-40e0-8761-15f35ebe80cd</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.eb1afd00-c81a-47e4-9cb5-1c5f4f923868</processItemId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2d3998b5-5912-411d-8437-3a8f946c6e0c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-14c9</guid>
            <versionId>5e214bfd-9075-49ef-8a9c-6a7f0d061cb2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="700" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2d3998b5-5912-411d-8437-3a8f946c6e0c</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>39fa22fd-0aff-4db8-91c7-aca16110faa2</guid>
                <versionId>c8d16d9d-4d35-41a7-a16e-ded39c5681c4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.30343e51-5bd6-4b25-9646-1573a757758a</processItemId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <name>Integration1</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3191c8f2-9b99-495f-8753-e59690216b0a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7ffd</guid>
            <versionId>8296ebcf-75b4-405e-b5c0-d93faa60918b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="327" y="82">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3191c8f2-9b99-495f-8753-e59690216b0a</subProcessId>
                <attachedProcessRef>/1.61a0bf71-b88b-4fc3-b79b-f023825ed63e</attachedProcessRef>
                <guid>e90a6ef8-b1ab-4fe3-85b9-fdc746cf071c</guid>
                <versionId>52368c20-974b-41be-8666-0e0562e467e7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.85a2af25-9d3e-4a79-a298-5eeb3c7d72ae</processItemId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <name>Service1</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c9618643-dcdf-4f8d-9a51-b8744f29fbe9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5fa14a851dbbd551:-30ba6dbf:16d898202ee:-7ffa</guid>
            <versionId>8cb5a432-aab1-4c31-8107-7334fefcbcab</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="469" y="91">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c9618643-dcdf-4f8d-9a51-b8744f29fbe9</subProcessId>
                <attachedProcessRef>/1.143ff27f-5e08-478c-af65-06723fa26d26</attachedProcessRef>
                <guid>f083f14e-2d73-404a-8b7a-65676bfb40cb</guid>
                <versionId>47e34ee1-f895-417f-9f93-edf09de36dd7</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.bd058fd6-7a5a-4a8d-abfe-************</epvProcessLinkId>
            <epvId>/21.ed99f470-25b4-4a03-b89d-888bc265e2aa</epvId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <guid>d0948b70-7f11-44e6-8b07-78acd3865956</guid>
            <versionId>e8900859-342b-4c7a-976f-dbd0b721250b</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.c33eafe8-4684-4dcf-814e-e012e67fc155</resourceProcessLinkId>
            <resourceBundleGroupId>/50.2641e279-160b-4d0d-bc96-528b36793ecf</resourceBundleGroupId>
            <processId>1.0295217c-7383-4a27-b311-333ea889f2bb</processId>
            <guid>e8a578df-4199-44d3-ab63-cdc07b3e68da</guid>
            <versionId>fbd18b41-c8c4-4efc-98c6-3eaacf6f5fb5</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId isNull="true" />
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="20" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
    </process>
</teamworks>

