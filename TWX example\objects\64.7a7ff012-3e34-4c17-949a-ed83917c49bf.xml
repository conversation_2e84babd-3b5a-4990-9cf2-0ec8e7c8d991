<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.7a7ff012-3e34-4c17-949a-ed83917c49bf" name="new view">
        <lastModified>1692094075263</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.7a7ff012-3e34-4c17-949a-ed83917c49bf</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;6350b9cf-f527-42f6-8b20-107a93d5c39b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Single_Select1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;002f8671-e4e8-4eee-84bc-5b6042bbd9e5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Single select&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;73e3e2cb-8985-4771-8b44-df22095b8bd7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8a56cfd0-b2b6-4c63-8bc3-8246d2f478f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ebcc170-2dd2-4381-8adb-17732f097350&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe4600f7-ba29-46d6-854d-7102d2e3fb5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"10","value":"10"},{"name":"20","value":"20"},{"name":"30","value":"30"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c90add14-98da-48e4-8f2f-431d28e5dede&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.test1();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.collateralAmount&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;1d2963c9-eee4-49d7-8e84-db5aa9bce633&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Single_Select2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;174889c0-35ce-4116-8a53-8511c14a8ebd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Single select 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a04f222-7c81-4f8f-89ac-db9e14aee343&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a33f54fc-96f4-4e42-857f-baa1465dcd6b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46100637-fae7-402c-8a9d-f40cc7c0a70f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e860812a-fe09-4da3-820b-88683beae9cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"1","value":"1"},{"name":"2","value":"2"},{"name":"3","value":"3"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;14a93899-c85d-4afe-8ca7-c28048885838&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.test2();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.IDCContract.billAmount&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>c35e84ae-aff4-4230-8033-b56d9e3f883e</guid>
        <versionId>5c3c22fd-c39b-407c-b2ac-a7ba610ef812</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="IDCContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.92274ae4-4e94-4dd3-ac3a-0855a54fe249</coachViewBindingTypeId>
            <coachViewId>64.7a7ff012-3e34-4c17-949a-ed83917c49bf</coachViewId>
            <isList>false</isList>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>2ca2a5c9-b7dd-4c86-a16d-062632762df4</guid>
            <versionId>9ee0f891-4c13-42a0-90f2-3f248a3881da</versionId>
        </bindingType>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.685d9c0a-662c-4405-b8bb-742617390d8c</coachViewInlineScriptId>
            <coachViewId>64.7a7ff012-3e34-4c17-949a-ed83917c49bf</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>var value1 = 0;&#xD;
var value2 = 0;&#xD;
this.test1 = function (){&#xD;
	value1 = 1010&#xD;
	alert(value1);&#xD;
}&#xD;
&#xD;
this.test2 = function(){&#xD;
//	value1 = 11;&#xD;
	alert(value1);&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>f4fe0d40-f5ec-4c5e-b01e-62618a8be721</guid>
            <versionId>d3237f7f-672f-4642-8172-d7adbf6c671f</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.c2c50d40-ef88-4919-982e-dac79abb7e05</coachViewLocalResId>
            <coachViewId>64.7a7ff012-3e34-4c17-949a-ed83917c49bf</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>b2c74fb3-72ae-42e4-b4d9-974087d393a9</guid>
            <versionId>28638f5e-87c3-4167-97c8-d8d92596ba85</versionId>
        </localization>
    </coachView>
</teamworks>

