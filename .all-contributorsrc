{"projectName": "twx-parse", "projectOwner": "tigermarques", "repoType": "github", "repoHost": "https://github.com", "files": ["README.md"], "imageSize": 100, "commit": false, "commitConvention": "angular", "contributors": [{"login": "tigermarques", "name": "<PERSON>", "avatar_url": "https://avatars0.githubusercontent.com/u/15315098?v=4", "profile": "https://github.com/tigermarques", "contributions": ["code", "doc", "example", "projectManagement", "test"]}], "contributorsPerLine": 7}