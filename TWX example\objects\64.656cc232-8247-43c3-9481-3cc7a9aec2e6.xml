<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.656cc232-8247-43c3-9481-3cc7a9aec2e6" name="Customer Information">
        <lastModified>1702294078051</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;fd5bd947-7178-4b62-86f3-5a35f3190e7e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35f59c40-362e-4de1-8b75-6bf1c37c2a1c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerInformation&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce833fed-9631-4337-831d-a17734a86683&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;050be7a5-9a2f-4d57-8e0b-4c6228c07c39&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34a2180c-2fa7-4bad-8643-9cda71a46650&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ffb3771-a351-4c15-8a2c-a9cdeae88e67&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e33a15a-1507-4dcb-82e6-74b365cf4264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;e9d48a07-a49b-496b-86b7-bab79ac6b651&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;b1ebc70e-da5f-4465-8b12-db61cde13799&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b81915e6-a358-4c0b-82fa-6c13f8e5fc92&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;76c961f5-3d02-45a2-888e-fd66ffa73274&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c66b781e-114b-420c-8b94-31b95c50ae8d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;0f265010-7329-4d07-8434-5230767450c2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46171196-24b3-49dd-8853-08f5d12011db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;b1684746-09c5-4d9d-83ee-7fd63017e2d4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;11ee38e4-16c5-466e-8d85-0ff5bbb582a1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;89a0e17a-66d9-4fa3-89be-464db0a5bc3f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CIFNumber1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1bcf38c5-9615-487a-8b15-27ad5699a537&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;851f5f11-d4bc-4e0e-8b89-0c547c960ba7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f98994dc-9f03-46b0-81d7-134ee0d03b7c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3fbca844-308b-45bd-8ad1-f46b7d5b1aee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.CIFNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;bd119992-e50b-415a-8f34-e4cee6278078&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomerSector1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7685e15f-975c-4e0f-8865-f213b9c4398d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerSector&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a796abec-6a31-42e8-8347-53fd3a03bcc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e7fddd67-bb46-4808-8806-c62914742bce&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.customerSector&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1cebb6c2-c99e-467c-83aa-a80d7cd3c8cd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CBE1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d2b4f24-9a5b-4bb7-8c47-79d644eeeb25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerNumberatCBE&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fd17ace-44a6-4edf-8e13-3c6accb2b52c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.CBENumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cd01ef15-b78d-4597-83bc-022eee6b574b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CommercialRegistrationNumber1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b4cb6cd-83d4-442d-8248-fd55f0d52a42&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CommercialRegistrationNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;900db265-7d85-4a3a-8813-82ab7aa1b97e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.commercialRegistrationNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cdd703e8-16a3-4635-81a3-eb554e517852&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;TaxCardNumber1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb69fd09-526c-4d4e-897b-b840a4573c7e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.TaxCardNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e055cd1a-3385-46ef-85c2-b6acc337fcea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06c2b8bf-6860-4503-852f-9ff754333d52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.taxCardNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;39cb8aff-6f6d-49da-8b13-a43b63fd248f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc0191a9-1add-4f0a-8123-78bb8c181496&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":6},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9a1955c1-670a-4acf-8a24-f84e35073d10&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomerName1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7bc42d29-8c20-4d85-8999-f2bc94642363&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a8b1ad2a-9361-4b5f-8155-cff5e9210b76&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;081ba453-a46f-48ff-8159-505af55719a0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;labelPlacement&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"T"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;87ff09b8-28a7-44f5-8f85-dc8187a2ea81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.customerName&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b151d713-2d69-4fdf-8ecd-d9657a816cf2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomerType1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2f7adcd-635d-4356-8373-a785b07fdf80&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CustomerType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;261e1b0a-7502-41c6-8afa-12535f5cf4a1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c20426c-**************-425c4a284c50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.customerType&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;741f3e94-6417-4738-8fca-99df7f13f1c9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;FacilityType1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f53459f-78a2-4291-8761-25a17e7b61c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.FacilityType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5472898-e985-4c85-8581-6e72c85ca048&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d821644d-9c07-4e7b-885a-8958b3fde58f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6a176c36-b841-4150-8ed6-99994e3727d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aefa0bb4-ddfe-4a84-89ab-75f5fb45595e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"لا يتمتع بتسهيلات","value":"لا يتمتع بتسهيلات"},{"name":"تسهيلات ائتمانية بضمانات نقدية","value":"تسهيلات ائتمانية بضمانات نقدية"},{"name":"تسهيلات ائتمانية بدون ضمانات","value":"تسهيلات ائتمانية بدون ضمانات"},{"name":"قروض مشتركة","value":"قروض مشتركة"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e5b6c2c-bccd-421a-8058-3605e84411e9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;203da750-d55d-42c3-8c85-7cd3946da1bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Facility_Type&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e7c27bde-af84-47b8-8e22-17ece9a08bf0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"arabicdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd26162e-f03b-49de-8719-d5f43a48a47a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"arabicdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a62ea72-01e8-4368-8490-471eb9753618&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ef3f0df-8bd4-4ef0-8896-1b1b516ca8a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;console.log("--------VIEW----------");
console.log(me.ui.getAbsoluteName());
console.log("--------VIEW----------");&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.facilityType&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92d88819-85d7-4dab-863c-75429e09035b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CommercialRegistrationOffice1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03b31257-7dc4-4bc0-8480-3775facc6a87&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.CommercialRegistrationOffice&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d77ee46-f09f-4a2e-83ee-9e7c1af0f073&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.commercialRegistrationOffice&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a172f3b0-0a26-4bcc-8e80-fb8beaf2071c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ImportCardNumber1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8594c803-c413-4156-89ad-9d0a835a0629&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ImportCardNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2ee2cf0-29d5-4010-8b55-fe000b39b153&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7533175b-1037-4fd9-8612-48e6b89e497d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d3e0399-2515-43fd-81ce-ef33918b0a4a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.visControler();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47f10fb1-2a8e-483c-810d-9bf8846f367f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 20)
{
	me.setValid(false , "max lenght is 20");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47338fac-a0a4-46ca-854b-0d25c059c22b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(isNaN(Number(me.getData()))){
	me.setValid(false , "must be number");
	me.setData("");
	return false;
}else
{
//	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.customer.importCardNumber&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>6913c69f-ff02-4fb4-b120-10a7ec609a04</guid>
        <versionId>d6db6ece-5692-4e79-b517-1fb798ac4e53</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="customer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.a5e65395-27f9-4b6d-9087-7b24db354c49</coachViewBindingTypeId>
            <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
            <isList>false</isList>
            <classId>/12.cc907e5e-4284-4dc5-8dea-8792e0a471c1</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>4d45f6a7-35bf-49ed-89f7-500cf419133e</guid>
            <versionId>4b89493f-bcbd-44ff-904f-d982d256c6c3</versionId>
        </bindingType>
        <configOption name="instanceview">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a37e1d2e-92d7-47b1-8b24-b6a685238e09</coachViewConfigOptionId>
            <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>a23941ae-79f1-4852-8dd0-715b5dfa2090</guid>
            <versionId>ca40efb2-**************-26535df46e37</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.1f11831d-b821-445f-9384-2a5045b1bf8c</coachViewInlineScriptId>
            <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.visControler = function  () {&#xD;
	if (this.context.options.instanceview.get("value") == true) {&#xD;
 		this.ui.get("FacilityType1").setEnabled(false);&#xD;
 		this.ui.get("ImportCardNumber1").setEnabled(false);&#xD;
 	}&#xD;
}&#xD;
&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.ui.get("Error_Message").setVisible(true);&#xD;
	this.ui.get("Error_Message/Alert_Modal").setVisible(true);&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>d09313fe-57d2-4b16-a6fa-95e26261587d</guid>
            <versionId>a207c4f6-22af-4d74-8499-d470da0147f5</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.5e9062f3-daa4-4606-a382-a445013f7048</coachViewLocalResId>
            <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>09e84055-c2ee-4857-ac6e-eef066f6599a</guid>
            <versionId>53a85148-7285-4de7-81f5-3dd1601b34f1</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.835e9104-d380-4b12-b144-a2527ec38f46</coachViewLocalResId>
            <coachViewId>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>57310c74-d138-414f-9e25-eff5629b40ae</guid>
            <versionId>0ae194f5-c88f-4141-8d8c-ee4e29162d00</versionId>
        </localization>
    </coachView>
</teamworks>

