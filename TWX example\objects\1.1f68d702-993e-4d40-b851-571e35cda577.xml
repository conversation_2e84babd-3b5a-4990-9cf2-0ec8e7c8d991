<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.1f68d702-993e-4d40-b851-571e35cda577" name="Reversal Approval Mail Service">
        <lastModified>1691935408605</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>40fd082c-01cc-4777-b6d6-3d86a01d87d7</guid>
        <versionId>60f2e782-0763-4672-822d-fcfe07eed11b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:6ad7eb4224455a46:11f23e39:189eef37169:-4b78" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.ba86f398-522f-4971-816e-9d431493705b"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"cc8267e1-44b2-4c97-adde-3348faaca5fd"},{"incoming":["427534a2-ebf0-46bf-ad3f-a808c9700566"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":670,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-622b"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c79656de-a151-4146-a51d-ae9213503a84"},{"targetRef":"c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.ba86f398-522f-4971-816e-9d431493705b","sourceRef":"cc8267e1-44b2-4c97-adde-3348faaca5fd"},{"startQuantity":1,"outgoing":["427534a2-ebf0-46bf-ad3f-a808c9700566"],"incoming":["fde213cf-3a12-4196-a984-372349c69291"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":364,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Send Email","dataInputAssociation":[{"targetRef":"2055.a7c74b41-811f-4581-94ef-69a84c74eb84","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.ae81d59d-2fde-4526-9476-d0598d6e8472","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.subject"]}}]},{"targetRef":"2055.6d9bd911-88b8-4ea3-8823-421a4f690290","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.msgBody"]}}]},{"targetRef":"2055.268afc2e-a651-49ef-8704-9a6ff22065c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.20348cf5-023e-4d3a-826e-5b92143ec224","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.1da05789-2131-46bc-aacf-34d84ca37def","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailDebugMode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"db5e752f-19e7-43a7-bcc7-1a3ff0d77575","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.ec26c46c-d70b-4881-98d8-40e694dd7362"]}],"calledElement":"1.338e9f4d-8538-4ceb-a155-c288604435d4"},{"targetRef":"c79656de-a151-4146-a51d-ae9213503a84","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2970"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"427534a2-ebf0-46bf-ad3f-a808c9700566","sourceRef":"db5e752f-19e7-43a7-bcc7-1a3ff0d77575"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"2056.d6d8bf98-c953-4f5c-b29f-3f2659600b3b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"msgBody","isCollection":false,"declaredType":"dataObject","id":"2056.19e55879-40e0-4802-b6d9-f81da09cc2aa"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"subject","isCollection":false,"declaredType":"dataObject","id":"2056.2192cfe1-e1eb-48bd-810e-f2d0f49576ad"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.a9e5c791-79a1-4122-99f9-f0d5c36396e7"},{"startQuantity":1,"outgoing":["fde213cf-3a12-4196-a984-372349c69291"],"incoming":["2027.ba86f398-522f-4971-816e-9d431493705b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":218,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tif (tw.local.idcRequest == null) {\r\n\t\ttw.local.idcRequest = new tw.object.IDCRequest();\r\n\t\t\r\n\t\ttw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.appInfo = new tw.object.AppInfo();\r\n\t\ttw.local.idcRequest.appInfo.instanceID = \"\";\r\n\t\t\r\n\t\ttw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();\r\n\t\r\n\t\ttw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.importPurpose = new tw.object.DBLookup();\r\n\t\ttw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();\r\n\t\ttw.local.idcRequest.customerInformation.customerName = \"\";\r\n\t\t\r\n\t\ttw.local.idcRequest.invoices = new tw.object.listOf.Invoice();\r\n\t\t\r\n\t\t\r\n\t\ttw.local.idcRequest.productCategory = new tw.object.DBLookup();\r\n\t\ttw.local.idcRequest.documentsSource = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.paymentTerms = new tw.object.DBLookup();\r\n\t\t\r\n\t\ttw.local.idcRequest.approvals = new tw.object.Approvals();\r\n\t\t\r\n\t\ttw.local.idcRequest.appLog = new tw.object.listOf.AppLog();\r\n\t}\r\n\t\r\n\tvar receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;\r\n\tvar dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;\r\n\t\/\/var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;\r\n\t\r\n\tvar taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;\r\n\t\r\n\t\r\n\tvar owner = tw.system.findTaskByID(tw.local.taskid).owner;\r\n\t\r\n\tif (owner == null){owner = \"\"}\r\n\t\r\n\ttw.local.subject = \"IDC Request No. \"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\" for Customer \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\" is approved for reversal \"\r\n\t\t\t\t+\"\u0637\u0644\u0628 \u0627\u0644\u062a\u062d\u0635\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u0646\u062f\u0649 \u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0631\u0642\u0645 \"+tw.local.idcRequest.appInfo.instanceID+\r\n\t\t\t\t\" \u0644\u0644\u0639\u0645\u064a\u0644 \"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\" \u062a\u0645\u062a \u0627\u0644\u0645\u0648\u0627\u0641\u0642\u0629 \u0639\u0644\u0649 \u0625\u0639\u0627\u062f\u0629 \u0642\u064a\u062f\u0647\";\r\n\t\r\n\ttw.local.msgBody = '&lt;html dir=\"ltl\" lang=\"en\"&gt;'\r\n\t\t\t\t+\"&lt;p&gt;Dear Sir \/ Madam,&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;Kindly be informed that the Request \"+\r\n\t\t\t\t+\"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; has been approved for reversal&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;br&gt;&lt;\/br&gt;&lt;p&gt;\u0627\u0644\u0633\u064a\u062f \/ \u0627\u0644\u0633\u064a\u062f\u0629&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;\u0628\u0631\u062c\u0627\u0621 \u0627\u0644\u0639\u0644\u0645 \u0623\u0646 \u0637\u0644\u0628 \"+\r\n\t\t\t\t+\"\u0631\u0642\u0645 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.appInfo.instanceID\r\n\t\t\t\t+\"&amp;gt;&lt;\/em&gt;&lt;\/strong&gt; \u0644\u0644\u0639\u0645\u064a\u0644 &lt;strong&gt;&lt;em&gt;&amp;lt;\"+tw.local.idcRequest.customerInformation.customerName\r\n\t\t\t\t+\"&lt;p&gt;\u062a\u0645\u062a \u0627\u0644\u0645\u0648\u0627\u0641\u0642\u0629 \u0639\u0644\u0649 \u0625\u0639\u0627\u062f\u0629 \u0642\u064a\u062f\u0647&lt;\/p&gt;\"\r\n\t\t\t\t+\"&lt;p&gt;\u0627\u0644\u0631\u062c\u0627\u0621 \u0639\u062f\u0645 \u0627\u0644\u0631\u062f \u0639\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0631\u0633\u0627\u0644\u0629. \u0647\u0630\u0627 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627.&lt;\/p&gt;\"\r\n\t\t\t+\"&lt;\/html&gt;\"\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\t\t\t\t"]}},{"targetRef":"db5e752f-19e7-43a7-bcc7-1a3ff0d77575","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Send Email","declaredType":"sequenceFlow","id":"fde213cf-3a12-4196-a984-372349c69291","sourceRef":"c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd"},{"parallelMultiple":false,"outgoing":["0d50dcb3-5107-44cf-8343-3dbe72e70941"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5081b98b-c98c-4395-8d21-41a8154b0923"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"15cec5f8-60c9-4d25-84c7-a523ba939e7d","otherAttributes":{"eventImplId":"4600d230-73f4-4f1e-89b9-4378f3741812"}}],"attachedToRef":"c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd","extensionElements":{"nodeVisualInfo":[{"width":24,"x":253,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1318c4d3-7427-4328-86d1-5343538a03c2","outputSet":{}},{"parallelMultiple":false,"outgoing":["caaad323-8482-4934-8c63-a944e2e5b714"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f3c3b5c7-1c55-42db-8d58-42452ce981fb"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8a7ebd10-0276-466e-87d5-bc9f70319f6b","otherAttributes":{"eventImplId":"561909c3-1c32-4874-81db-bcf7709e3eba"}}],"attachedToRef":"db5e752f-19e7-43a7-bcc7-1a3ff0d77575","extensionElements":{"nodeVisualInfo":[{"width":24,"x":399,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"e59edd49-141d-4302-8235-bcc78230e608","outputSet":{}},{"incoming":["0d50dcb3-5107-44cf-8343-3dbe72e70941","caaad323-8482-4934-8c63-a944e2e5b714"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"effca520-b979-49e5-81a1-882ed1db4252","otherAttributes":{"eventImplId":"119cc703-8945-4a92-8f3c-bbcf5024274c"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":440,"y":166,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Reversal Approval Mail Service -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\n\/\/var attribute = String(tw.system.error.getAttribute(\"type\"));\r\n\/\/var element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n\/\/tw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Reversal Approval Mail Service -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"208e9e1f-d39f-4946-806e-b9c6d1e3d624"},{"targetRef":"208e9e1f-d39f-4946-806e-b9c6d1e3d624","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0d50dcb3-5107-44cf-8343-3dbe72e70941","sourceRef":"1318c4d3-7427-4328-86d1-5343538a03c2"},{"targetRef":"208e9e1f-d39f-4946-806e-b9c6d1e3d624","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"caaad323-8482-4934-8c63-a944e2e5b714","sourceRef":"e59edd49-141d-4302-8235-bcc78230e608"}],"laneSet":[{"id":"4d25e2b8-ef0b-49e5-a161-f339636f618d","lane":[{"flowNodeRef":["cc8267e1-44b2-4c97-adde-3348faaca5fd","c79656de-a151-4146-a51d-ae9213503a84","db5e752f-19e7-43a7-bcc7-1a3ff0d77575","c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd","1318c4d3-7427-4328-86d1-5343538a03c2","e59edd49-141d-4302-8235-bcc78230e608","208e9e1f-d39f-4946-806e-b9c6d1e3d624"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5fce546a-3b27-4ad9-8b48-b66a1a826205","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Reversal Approval Mail Service","declaredType":"process","id":"1.1f68d702-993e-4d40-b851-571e35cda577","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.028e365e-a546-40c9-8f19-9631b46eb15c"}],"inputSet":[{"dataInputRefs":["2055.b62126b5-3a24-45d7-b250-cb32658e18f9","2055.7a270092-a920-486e-b4ff-d9e45deb1047","2055.903a964c-9862-4a42-97f3-f1434ddfbf7a"]}],"outputSet":[{"dataOutputRefs":["2055.028e365e-a546-40c9-8f19-9631b46eb15c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Y\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailDebugMode","isCollection":false,"id":"2055.b62126b5-3a24-45d7-b250-cb32658e18f9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"2078.70913\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskid","isCollection":false,"id":"2055.7a270092-a920-486e-b4ff-d9e45deb1047"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"12345678909876\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"test\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.903a964c-9862-4a42-97f3-f1434ddfbf7a"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="mailDebugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b62126b5-3a24-45d7-b250-cb32658e18f9</processParameterId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Y"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c9737706-fc6c-4d56-b108-da7b5519f956</guid>
            <versionId>41711ba6-976a-4267-a1ea-8b4aed71e213</versionId>
        </processParameter>
        <processParameter name="taskid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7a270092-a920-486e-b4ff-d9e45deb1047</processParameterId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"2078.70913"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cd44b1ee-375e-4e4d-92cf-c370f4b80ecd</guid>
            <versionId>da36bb73-c4d3-4099-8608-6813bcc40014</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.903a964c-9862-4a42-97f3-f1434ddfbf7a</processParameterId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fffaa2cf-9a38-48aa-8b76-59919f71e7ed</guid>
            <versionId>dadd5d74-1486-46c9-86c2-1dd9f7a75215</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.028e365e-a546-40c9-8f19-9631b46eb15c</processParameterId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1ef269d1-33e9-404e-b5b3-b2af1541b307</guid>
            <versionId>55a66553-a7bf-4a3e-9d55-d1c0acb90e4b</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8feefe56-ac4c-4f88-8e9d-d0d47fa0635f</processParameterId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a91eddbd-fbb2-477d-8483-6407e1a31206</guid>
            <versionId>e567e41d-6168-43de-863e-af3d7cfe91e7</versionId>
        </processParameter>
        <processVariable name="mailTo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d6d8bf98-c953-4f5c-b29f-3f2659600b3b</processVariableId>
            <description isNull="true" />
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>6d439f7f-12d8-4123-a9ed-bbd26197c248</guid>
            <versionId>675a0402-6090-43a4-a849-c26b7f0386d3</versionId>
        </processVariable>
        <processVariable name="msgBody">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.19e55879-40e0-4802-b6d9-f81da09cc2aa</processVariableId>
            <description isNull="true" />
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>ef1cd02a-b4b5-4734-a865-b44e24e78339</guid>
            <versionId>68019657-af8d-411f-89a1-d66f7f7275a2</versionId>
        </processVariable>
        <processVariable name="subject">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2192cfe1-e1eb-48bd-810e-f2d0f49576ad</processVariableId>
            <description isNull="true" />
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>247648e6-f4aa-4899-8678-6a704260b56f</guid>
            <versionId>998782a2-d09c-4859-93e3-999f890fb4ff</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a9e5c791-79a1-4122-99f9-f0d5c36396e7</processVariableId>
            <description isNull="true" />
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>baca38b5-5dce-4687-9b3d-ce5c4ef70b43</guid>
            <versionId>4b272189-39e4-4990-a1ce-f293a04ff409</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.db5e752f-19e7-43a7-bcc7-1a3ff0d77575</processItemId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <name>Send Email</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.062d01e7-ed96-4117-923f-4309813e6845</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-622a</guid>
            <versionId>3c1c0fa8-cb63-412f-b549-869d1e6c620f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-72b0</errorHandlerItem>
                <errorHandlerItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.062d01e7-ed96-4117-923f-4309813e6845</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.338e9f4d-8538-4ceb-a155-c288604435d4</attachedProcessRef>
                <guid>db7cf552-d752-418f-bdb3-e6faf4922417</guid>
                <versionId>a88a5c63-e821-4395-9e76-55f9bd9c27fc</versionId>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f7cbaa0e-2f0a-4ca6-a85d-7b3b9854fde0</parameterMappingId>
                    <processParameterId>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4dba017f-8006-4fc4-b466-c988e7342e1e</guid>
                    <versionId>0d908048-e183-42be-b558-181175bb0660</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="subject">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bf6a8ba0-2122-40ec-b4ac-e4bfc4883bb2</parameterMappingId>
                    <processParameterId>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subject</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d22be1b8-a694-4463-90a4-d0926b77a8ee</guid>
                    <versionId>3019cea5-1110-4ccb-939f-b126053a4fbd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="attachments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.30a56b62-8b56-4e27-a976-64125c1acc2c</parameterMappingId>
                    <processParameterId>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.8a11240b-682f-4caf-9f03-5ce6a64d720b</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>bcb1e293-f123-44ce-9522-777098595e66</guid>
                    <versionId>369ed299-73a0-4031-8ca9-60bba34c1023</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="msgBody">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.39dac6c1-6c30-440d-85a4-e76d2849cf01</parameterMappingId>
                    <processParameterId>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.msgBody</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f8ff5b8f-3d93-4eba-a58e-f243bcbdd98b</guid>
                    <versionId>53d98643-b680-4c5a-b6e9-498bc7886f52</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f9876a84-ca70-4b30-958b-124cc20d1fef</parameterMappingId>
                    <processParameterId>2055.20348cf5-023e-4d3a-826e-5b92143ec224</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>65f8fa7d-d944-4c8e-b20c-12e1acc62ccc</guid>
                    <versionId>6334716a-3cc1-46e1-a577-1a0c0525420f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailDebugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5ec797f3-480f-4037-9f22-c6094c939be0</parameterMappingId>
                    <processParameterId>2055.1da05789-2131-46bc-aacf-34d84ca37def</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailDebugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>37fa829e-13e6-4936-a101-aa250b559087</guid>
                    <versionId>69d809d3-e80d-4e0c-b7f4-a212935b290d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailTo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3c89506d-296d-46fc-9e42-e801c4bdaee8</parameterMappingId>
                    <processParameterId>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</processParameterId>
                    <parameterMappingParentId>3012.062d01e7-ed96-4117-923f-4309813e6845</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailTo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e5b7c3a7-d2a0-484c-8fc3-4628cf7dab59</guid>
                    <versionId>fc194fbe-2bc9-47a1-8b8d-bdf0f075329e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</processItemId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.2a69b819-ea8e-4334-891e-a94031fab255</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-72b0</guid>
            <versionId>53527f5e-c3e4-418a-889d-78516d1f0be2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.fb3bac1b-3fde-4c2f-b9ef-1cf1e809ee32</processItemPrePostId>
                <processItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Reversal Approval Mail Service -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Reversal Approval Mail Service -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>a789f6ff-91b3-408a-82f6-e0da2ce86b0a</guid>
                <versionId>c70fdbeb-e80d-41cf-8ed2-034ee8dcd31d</versionId>
            </processPrePosts>
            <layoutData x="440" y="166">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.2a69b819-ea8e-4334-891e-a94031fab255</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>e95f04c7-43df-40e6-8175-7b26485d6739</guid>
                <versionId>594dbbc1-8e5d-42b2-abad-291fe43d55bb</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e553483f-f0b8-42e3-b262-81acbbb98b02</parameterMappingId>
                    <processParameterId>2055.8feefe56-ac4c-4f88-8e9d-d0d47fa0635f</processParameterId>
                    <parameterMappingParentId>3007.2a69b819-ea8e-4334-891e-a94031fab255</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>89cd0793-642e-495e-bd51-b1784d37bdae</guid>
                    <versionId>4a1d0f60-5b12-4481-b772-1e63d1379d9c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</processItemId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.84637bf6-c484-4dff-8655-7c910568ce10</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-622c</guid>
            <versionId>83bbd7e1-dee4-4c8c-92d6-6e5155de80b9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="218" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-72b0</errorHandlerItem>
                <errorHandlerItemId>2025.208e9e1f-d39f-4946-806e-b9c6d1e3d624</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.84637bf6-c484-4dff-8655-7c910568ce10</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	if (tw.local.idcRequest == null) {&#xD;
		tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
		&#xD;
		tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
		tw.local.idcRequest.appInfo.instanceID = "";&#xD;
		&#xD;
		tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
	&#xD;
		tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
		tw.local.idcRequest.customerInformation.customerName = "";&#xD;
		&#xD;
		tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
		&#xD;
		tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	}&#xD;
	&#xD;
	var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
	var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
	//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
	&#xD;
	var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
	&#xD;
	&#xD;
	var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
	&#xD;
	if (owner == null){owner = ""}&#xD;
	&#xD;
	tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
				+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" is approved for reversal "&#xD;
				+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
				" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" تمت الموافقة على إعادة قيده";&#xD;
	&#xD;
	tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
				+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Kindly be informed that the Request "+&#xD;
				+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved for reversal&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
				+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
				+"&lt;p&gt;برجاء العلم أن طلب "+&#xD;
				+"رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&lt;p&gt;تمت الموافقة على إعادة قيده&lt;/p&gt;"&#xD;
				+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
			+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}				</script>
                <isRule>false</isRule>
                <guid>d6d23cb9-84b1-4bad-974a-fc837b14298f</guid>
                <versionId>2a1e0eae-8cb4-43f8-93b8-f3d4d6789c78</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c79656de-a151-4146-a51d-ae9213503a84</processItemId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3ec86f66-6536-42b7-9697-f3f70cff6725</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-622b</guid>
            <versionId>f4370ccd-27bb-4cc8-bcf5-37f23fefc472</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="670" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3ec86f66-6536-42b7-9697-f3f70cff6725</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a60f7650-dc96-4e21-a845-cd0fd44633f0</guid>
                <versionId>bd03827e-b782-4984-b5ea-a83133df29f8</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Reversal Approval Mail Service" id="1.1f68d702-993e-4d40-b851-571e35cda577" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="mailDebugMode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b62126b5-3a24-45d7-b250-cb32658e18f9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Y"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="taskid" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7a270092-a920-486e-b4ff-d9e45deb1047">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"2078.70913"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.903a964c-9862-4a42-97f3-f1434ddfbf7a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "12345678909876";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "test";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.028e365e-a546-40c9-8f19-9631b46eb15c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.b62126b5-3a24-45d7-b250-cb32658e18f9</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.7a270092-a920-486e-b4ff-d9e45deb1047</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.903a964c-9862-4a42-97f3-f1434ddfbf7a</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.028e365e-a546-40c9-8f19-9631b46eb15c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="4d25e2b8-ef0b-49e5-a161-f339636f618d">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5fce546a-3b27-4ad9-8b48-b66a1a826205" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>cc8267e1-44b2-4c97-adde-3348faaca5fd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c79656de-a151-4146-a51d-ae9213503a84</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>db5e752f-19e7-43a7-bcc7-1a3ff0d77575</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1318c4d3-7427-4328-86d1-5343538a03c2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e59edd49-141d-4302-8235-bcc78230e608</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>208e9e1f-d39f-4946-806e-b9c6d1e3d624</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="cc8267e1-44b2-4c97-adde-3348faaca5fd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.ba86f398-522f-4971-816e-9d431493705b</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c79656de-a151-4146-a51d-ae9213503a84">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="670" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-622b</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>427534a2-ebf0-46bf-ad3f-a808c9700566</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="cc8267e1-44b2-4c97-adde-3348faaca5fd" targetRef="c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd" name="To Exclusive Gateway" id="2027.ba86f398-522f-4971-816e-9d431493705b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.338e9f4d-8538-4ceb-a155-c288604435d4" name="Send Email" id="db5e752f-19e7-43a7-bcc7-1a3ff0d77575">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fde213cf-3a12-4196-a984-372349c69291</ns16:incoming>
                        
                        
                        <ns16:outgoing>427534a2-ebf0-46bf-ad3f-a808c9700566</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailTo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.subject</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.msgBody</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.20348cf5-023e-4d3a-826e-5b92143ec224</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1da05789-2131-46bc-aacf-34d84ca37def</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailDebugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="db5e752f-19e7-43a7-bcc7-1a3ff0d77575" targetRef="c79656de-a151-4146-a51d-ae9213503a84" name="To End" id="427534a2-ebf0-46bf-ad3f-a808c9700566">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="2056.d6d8bf98-c953-4f5c-b29f-3f2659600b3b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="msgBody" id="2056.19e55879-40e0-4802-b6d9-f81da09cc2aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="subject" id="2056.2192cfe1-e1eb-48bd-810e-f2d0f49576ad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.a9e5c791-79a1-4122-99f9-f0d5c36396e7" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="218" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.ba86f398-522f-4971-816e-9d431493705b</ns16:incoming>
                        
                        
                        <ns16:outgoing>fde213cf-3a12-4196-a984-372349c69291</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	if (tw.local.idcRequest == null) {&#xD;
		tw.local.idcRequest = new tw.object.IDCRequest();&#xD;
		&#xD;
		tw.local.idcRequest.countryOfOrigin = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.appInfo = new tw.object.AppInfo();&#xD;
		tw.local.idcRequest.appInfo.instanceID = "";&#xD;
		&#xD;
		tw.local.idcRequest.appInfo.branch = new tw.object.NameValuePair();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails = new tw.object.ProductsDetails();&#xD;
	&#xD;
		tw.local.idcRequest.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails = new tw.object.FinancialDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.IDCRequestType = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.billOfLading = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.importPurpose = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.IDCRequestNature = new tw.object.DBLookup();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.customerInformation = new tw.object.CustomerInformation();&#xD;
		tw.local.idcRequest.customerInformation.customerName = "";&#xD;
		&#xD;
		tw.local.idcRequest.invoices = new tw.object.listOf.Invoice();&#xD;
		&#xD;
		&#xD;
		tw.local.idcRequest.productCategory = new tw.object.DBLookup();&#xD;
		tw.local.idcRequest.documentsSource = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.paymentTerms = new tw.object.DBLookup();&#xD;
		&#xD;
		tw.local.idcRequest.approvals = new tw.object.Approvals();&#xD;
		&#xD;
		tw.local.idcRequest.appLog = new tw.object.listOf.AppLog();&#xD;
	}&#xD;
	&#xD;
	var receivedDate = tw.system.findTaskByID(tw.local.taskid).activationTime;&#xD;
	var dueDate=tw.system.findTaskByID(tw.local.taskid).dueDate;&#xD;
	//var activityName = tw.system.findTaskByID(tw.local.taskid).processActivityName;&#xD;
	&#xD;
	var taskSubject = tw.system.findTaskByID(tw.local.taskid).subject;&#xD;
	&#xD;
	&#xD;
	var owner = tw.system.findTaskByID(tw.local.taskid).owner;&#xD;
	&#xD;
	if (owner == null){owner = ""}&#xD;
	&#xD;
	tw.local.subject = "IDC Request No. "+tw.local.idcRequest.appInfo.instanceID&#xD;
				+" for Customer "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" is approved for reversal "&#xD;
				+"طلب التحصيل المستندى استيراد رقم "+tw.local.idcRequest.appInfo.instanceID+&#xD;
				" للعميل "+tw.local.idcRequest.customerInformation.customerName&#xD;
				+" تمت الموافقة على إعادة قيده";&#xD;
	&#xD;
	tw.local.msgBody = '&lt;html dir="ltl" lang="en"&gt;'&#xD;
				+"&lt;p&gt;Dear Sir / Madam,&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Kindly be informed that the Request "+&#xD;
				+"with request number &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; related to the customer &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; has been approved for reversal&lt;/p&gt;"&#xD;
				+"&lt;p&gt;Please do not reply to this message. This is an automatically generated email.&lt;/p&gt;"&#xD;
				+"&lt;br&gt;&lt;/br&gt;&lt;p&gt;السيد / السيدة&lt;/p&gt;"&#xD;
				+"&lt;p&gt;برجاء العلم أن طلب "+&#xD;
				+"رقم &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.appInfo.instanceID&#xD;
				+"&amp;gt;&lt;/em&gt;&lt;/strong&gt; للعميل &lt;strong&gt;&lt;em&gt;&amp;lt;"+tw.local.idcRequest.customerInformation.customerName&#xD;
				+"&lt;p&gt;تمت الموافقة على إعادة قيده&lt;/p&gt;"&#xD;
				+"&lt;p&gt;الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.&lt;/p&gt;"&#xD;
			+"&lt;/html&gt;"&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}				</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd" targetRef="db5e752f-19e7-43a7-bcc7-1a3ff0d77575" name="To Send Email" id="fde213cf-3a12-4196-a984-372349c69291">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd" parallelMultiple="false" name="Error" id="1318c4d3-7427-4328-86d1-5343538a03c2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="253" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0d50dcb3-5107-44cf-8343-3dbe72e70941</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5081b98b-c98c-4395-8d21-41a8154b0923" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="15cec5f8-60c9-4d25-84c7-a523ba939e7d" eventImplId="4600d230-73f4-4f1e-89b9-4378f3741812">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="db5e752f-19e7-43a7-bcc7-1a3ff0d77575" parallelMultiple="false" name="Error1" id="e59edd49-141d-4302-8235-bcc78230e608">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="399" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>caaad323-8482-4934-8c63-a944e2e5b714</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f3c3b5c7-1c55-42db-8d58-42452ce981fb" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8a7ebd10-0276-466e-87d5-bc9f70319f6b" eventImplId="561909c3-1c32-4874-81db-bcf7709e3eba">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="208e9e1f-d39f-4946-806e-b9c6d1e3d624">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="440" y="166" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Reversal Approval Mail Service -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
//var attribute = String(tw.system.error.getAttribute("type"));&#xD;
//var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
//tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Reversal Approval Mail Service -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0d50dcb3-5107-44cf-8343-3dbe72e70941</ns16:incoming>
                        
                        
                        <ns16:incoming>caaad323-8482-4934-8c63-a944e2e5b714</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="effca520-b979-49e5-81a1-882ed1db4252" eventImplId="119cc703-8945-4a92-8f3c-bbcf5024274c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1318c4d3-7427-4328-86d1-5343538a03c2" targetRef="208e9e1f-d39f-4946-806e-b9c6d1e3d624" name="To End Event" id="0d50dcb3-5107-44cf-8343-3dbe72e70941">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e59edd49-141d-4302-8235-bcc78230e608" targetRef="208e9e1f-d39f-4946-806e-b9c6d1e3d624" name="To End Event" id="caaad323-8482-4934-8c63-a944e2e5b714">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.427534a2-ebf0-46bf-ad3f-a808c9700566</processLinkId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.db5e752f-19e7-43a7-bcc7-1a3ff0d77575</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</endStateId>
            <toProcessItemId>2025.c79656de-a151-4146-a51d-ae9213503a84</toProcessItemId>
            <guid>8c6be3e3-1e5e-492a-89f6-bc2b073427b1</guid>
            <versionId>091e152b-0ced-4457-8dd2-fcbef29abbdc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.db5e752f-19e7-43a7-bcc7-1a3ff0d77575</fromProcessItemId>
            <toProcessItemId>2025.c79656de-a151-4146-a51d-ae9213503a84</toProcessItemId>
        </link>
        <link name="To Send Email">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fde213cf-3a12-4196-a984-372349c69291</processLinkId>
            <processId>1.1f68d702-993e-4d40-b851-571e35cda577</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.db5e752f-19e7-43a7-bcc7-1a3ff0d77575</toProcessItemId>
            <guid>d318a9db-f02a-4ebb-aec8-c57cd76d1e99</guid>
            <versionId>0ca0fc21-dfbb-4d29-8273-5b87963ed617</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c823b2d8-bc78-4cdf-8bb0-3b7e57be5ccd</fromProcessItemId>
            <toProcessItemId>2025.db5e752f-19e7-43a7-bcc7-1a3ff0d77575</toProcessItemId>
        </link>
    </process>
</teamworks>

