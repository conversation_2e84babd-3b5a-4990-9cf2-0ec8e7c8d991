<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.236d78a5-bcbd-4c3e-a421-cfb181f40791" name="Serviço manual de lado-cliente">
        <lastModified>1569971628262</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.26d2fb40-de68-4dba-92ea-3f50485c7c47</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>5</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</description>
        <guid>ced16901-f1b5-4cc3-b150-e76b8aec039e</guid>
        <versionId>a9457050-5a86-413e-b5ea-76f6f198cb1c</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.41cb3ad8-dd34-4f60-b919-34abed2fef06"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"787275bb-2992-4595-a9d6-a94de1a2988e"},{"startQuantity":1,"outgoing":["2027.20bd83f9-8381-4891-bc6b-fb468c15036a","2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4","2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9"],"incoming":["2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8","2027.3be85c06-e292-42a0-bc27-d88501fcf12f"],"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"input11","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"199ad100-60a7-4e85-8d9a-0fa9186d3faa","optionName":"@label","value":"Input 1"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b4b64602-4c25-49c1-8978-f203b7376c04","optionName":"@helpText","value":""}],"viewUUID":"64.d109fc11-3729-396e-bf94-d748d1967596","binding":"tw.local.input1","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"994f3270-5cbb-4295-8266-8ecf3bcadd8a","version":"8570"},{"layoutItemId":"output11","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"66708c93-4bc4-4102-8eca-14860f2bb454","optionName":"@label","value":"Output 1"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b571255-5dbd-4c33-8b9c-afdabe38e630","optionName":"@helpText","value":""}],"viewUUID":"64.d109fc11-3729-396e-bf94-d748d1967596","binding":"tw.local.output1","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ccc75b05-5ffd-4c48-8349-7020aca280e0","version":"8570"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Untitled11","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"52c92b67-e294-486a-85ce-9a0bce288b32","optionName":"@label","value":"Untitled 1"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7e6b500b-d715-46ce-8bfc-9c653cefaae0","optionName":"@helpText","value":""}],"viewUUID":"64.d109fc11-3729-396e-bf94-d748d1967596","binding":"tw.local.variable1.Untitled1","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a241730f-ec99-4183-8456-bde28505b01b","version":"8570"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Untitled12","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"03203288-c17b-4b21-8517-7f4e4d4a69d2","optionName":"@label","value":"Untitled 1 2"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f1478fb2-7441-4d90-8340-7aec8934a41c","optionName":"@helpText","value":""}],"viewUUID":"64.d109fc11-3729-396e-bf94-d748d1967596","binding":"tw.local.variable1.Untitled2.currentItem.Untitled1","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"dc984dc3-3323-4c46-8595-65f4529ff6e8","version":"8570"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"92bc67d1-a4f3-4d7e-8f46-711200d22a36"}],"layoutItemId":"Untitled21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"389da10e-a1c8-4933-854c-b5b842f0c14c","optionName":"@label","value":"Untitled 2"}],"viewUUID":"64.327b1224-e7d9-3d9c-ad44-392f5702827c","binding":"tw.local.variable1.Untitled2[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"aafd04fb-6f07-4838-8052-61cbb9bd0fed","version":"8570"}],"contentBoxId":"LAYOUT_ITEM_ID_1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"8c59ff17-e61e-4a51-8143-cd762e4dd432"}],"layoutItemId":"LayoutItem2","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1bf22bc9-85dd-4d60-8fb6-0683daccb901","optionName":"@label","value":"Variable 1"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5daa18b1-b4c2-4e67-8f0f-27ac796419e5","optionName":"showBorder","value":"true"}],"viewUUID":"64.e44c5617-c1ca-31f8-a810-ce1138fb1c99","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"de20f28f-80f2-47ce-8e8a-51c0573bc6d2","version":"8570"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"322236e8-1bf9-4aa1-82b0-82d6509c2e13"}],"layoutItemId":"CustomDataSection","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4e2a3c9c-c1b4-46c1-8702-a759801b9a98","optionName":"@label","value":"Data Section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dfa8c6bf-96af-4357-884c-feccd236438e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b1b4c0df-7ee7-4d78-80b3-b304b125bc71","optionName":"@labelVisibility","value":"Show"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fe1d0946-027e-4431-8e4e-cf61959e7246","optionName":"instanceId","value":"tw.local.selectedInstanceId"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"812694fc-ec5b-4b28-8cc3-30b5f79a6df8","optionName":"refreshTrigger","value":"tw.local.automaticRefreshTrigger"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4e9831a9-2a2c-42ad-8496-64f8a2986996","optionName":"failedUpdateMessage","value":"tw.local.failedSaveMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ba75e726-3020-4deb-801d-97c773689bc1","optionName":"failedSaveMessage","value":"tw.local.failedSaveMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5ffdf52f-a81e-4102-8021-e42182614f3b","optionName":"collapsible","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"71c3f7d5-5923-43a1-8afb-08d66ef58935","optionName":"instanceStatus","value":"tw.local.instanceSummary.status"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c56113bb-d385-4851-8f6d-845a7e4735cf","optionName":"boundaryEventType","value":"tw.local.dataSectionBoundaryEventType"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2671d70f-2db5-481a-8f4d-4bfab3eb4c78","optionName":"incomingChanges","value":"tw.local.incomingChanges"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"72d5852e-9d8a-4e7d-874e-4c65a27b99e8","optionName":"localChanges","value":"tw.local.localChanges"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ea375ed0-449e-4fc0-832c-1b3b8aceeb78","optionName":"incomingChangesMerged","value":"tw.local.incomingChangesMerged"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64dfbd54-2f9b-47e8-82db-68f5ff41050d","optionName":"unsavedLocalChanges","value":"tw.local.unsavedLocalChanges"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"977f2648-ba2a-456b-8a27-8e7b2daff4bc","optionName":"originalInput","value":"tw.local.originalInput"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3b13163-61bc-4d25-8ed1-6999d99fc36d","optionName":"editMode","value":"true"}],"viewUUID":"64.0678c7bb-7028-4bca-8111-e0e9977f294d","binding":"","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bf0bed38-b0b3-4061-86b8-ed9a128a2f74","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"7b9be36f-a072-4a9f-8e28-5b82b638e3a5"}],"layoutItemId":"Default_Instance_Details_Template1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f63e14ca-c966-45b2-882e-81bc2b9c8187","optionName":"@label","value":"Default Instance Details Template"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2bcc1159-507c-4aac-8738-9e6fba11a6e1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d179dba8-0882-4a25-8d61-a0fb9c613201","optionName":"@labelVisibility","value":"Show"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b690cd77-16bd-4efd-8563-59acf3690f2e","optionName":"activitiesTextFilter","value":"tw.local.activitiesTextFilter"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"039d54a8-4561-4592-86b1-80d78fb8c9af","optionName":"addButtonDropdownSelectedItem","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18d42900-50f6-42ca-8402-4e73c7058a9a","optionName":"breadcrumbs","value":"tw.local.breadcrumbs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e753769b-2683-4abf-8949-c0ee9983d269","optionName":"breadcrumbText","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dd8e737f-97d3-4649-8cce-85cd250b15f7","optionName":"buttonClicked_GanttView","value":"tw.local.buttonClicked_GanttView"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"90082a09-f8d0-4d81-8f4f-d0978b834eeb","optionName":"currentPage","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"75848139-b40b-4f67-83a2-5357fa191e10","optionName":"documentAddButtonDropdownList"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d78f38bf-38b3-4b96-81af-5cd2965170b3","optionName":"follow","value":"tw.local.follow"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"adf58ddf-5fba-4503-8024-a73d35ab180c","optionName":"instanceName","value":"tw.local.instanceName"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d03a7a7-7ae9-4769-8ca9-19f74bf3af1f","optionName":"instanceSummary","value":"tw.local.instanceSummary"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"75345881-e617-4621-8d2a-23fa52df5af3","optionName":"activitiesCategoriesSelectionList","value":"tw.local.activitiesCategoriesSelectionList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ffcd419-851b-484b-82e6-21633b183a06","optionName":"selectedActivitiesCategory","value":"tw.local.selectedActivitiesCategory"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c5da619-d4d9-4239-8986-3a9ad08ba623","optionName":"selectedInstanceId","value":"tw.local.selectedInstanceId"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a8a63797-2fbe-4edb-8a03-d1bfd0d586a5","optionName":"selectedTasksCategory","value":"tw.local.selectedTasksCategory"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fad4e682-3e04-4ca1-8f63-816879e3b34d","optionName":"tasksCategoriesSelectionList","value":"tw.local.tasksCategoriesSelectionList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"efb4cd08-f86e-40d7-80e9-8cc94e746452","optionName":"currentDate","value":"tw.local.currentDate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"db8724e7-9529-45a3-89a0-056aa3f5311e","optionName":"hideDocumentSection","value":"tw.local.hideDocumentSection"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f8422e0b-3194-4e87-854e-97532e1f6f24","optionName":"refreshTrigger","value":"tw.local.automaticRefreshTrigger"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d509199f-5840-416e-8ba6-e492ec8116d7","optionName":"manualRefreshTrigger","value":"tw.local.manualRefreshTrigger"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c8dc1403-728a-42dc-8a46-e61f16c73af3","optionName":"canViewDiagram","value":"tw.local.canViewDiagram"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2b0fcd30-bde7-4bb8-8188-e237917bd138","optionName":"navigationURL","value":"tw.local.navigationURL"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3fc5cd8c-d88c-443f-82d3-51cac3534953","optionName":"unsavedLocalChanges","value":"tw.local.unsavedLocalChanges"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7e161c1f-1c84-4d83-8d2c-da98b99f38fd","optionName":"preventBoundaryEvent","value":"tw.local.unsavedLocalChanges"}],"viewUUID":"64.5f0e6839-f7f9-41a3-b633-541ba91f9b31","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"312c9e8b-f749-4fc7-89d7-fdf42381a821","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"extensionElements":{"nodeVisualInfo":[{"width":95,"x":675,"y":103,"declaredType":"TNodeVisualInfo","height":70}]},"name":"View Instance Details","isForCompensation":false,"completionQuantity":1,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"id":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099"},{"outgoing":["2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8","2027.c461f373-e3eb-4bf6-b13f-4384f716944f"],"incoming":["2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d","2027.20bd83f9-8381-4891-bc6b-fb468c15036a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":372,"y":103,"declaredType":"TNodeVisualInfo","height":70}]},"documentation":[{"content":["&lt;br _moz_editor_bogus_node=\"TRUE\" \/&gt;"],"textFormat":"text\/plain"}],"declaredType":"callActivity","startQuantity":1,"default":"2027.c461f373-e3eb-4bf6-b13f-4384f716944f","name":"Server Side Init Data","dataInputAssociation":[{"targetRef":"2055.79a0c5f3-08ff-45b1-a99d-3b828a60339d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.selectedInstanceId"]}}]},{"targetRef":"2055.0344478b-933e-4e7e-8122-349aaeb03f2d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.selectedInstanceId"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.d959c04a-f61a-4508-b985-436bbc646544","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceName"]}}],"sourceRef":["2055.9918e74b-8220-44bd-a6df-213ae1f96c8f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935","declaredType":"TFormalExpression","content":["tw.local.instanceSummary"]}}],"sourceRef":["2055.99c78e1f-41aa-4f5b-89e4-9f0449dbb478"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","declaredType":"TFormalExpression","content":["tw.local.currentDate"]}}],"sourceRef":["2055.8e52cfcb-db25-4bc4-a016-c8103a1cae4b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.canViewDiagram"]}}],"sourceRef":["2055.83bae48e-f188-476a-bd4f-eee8360c042d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.helperScriptURL"]}}],"sourceRef":["2055.b2e6d33b-136e-4c8e-b88f-abe718a89062"]}],"calledElement":"1.554dd7d5-ceb8-4548-91e5-788940b70e0d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"activitiesTextFilter","isCollection":false,"declaredType":"dataObject","id":"2056.57f86fa0-67b6-4726-ad97-162161f2e6fe"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"breadcrumbs","isCollection":true,"declaredType":"dataObject","id":"2056.e4585b41-c23e-48cb-b206-da672aee5880"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"buttonClicked_GanttView","isCollection":false,"declaredType":"dataObject","id":"2056.aaa77807-46d9-4f14-ae1e-c593aa435a1f"},{"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"currentDate","isCollection":false,"declaredType":"dataObject","id":"2056.ff69c386-cd9e-4193-9d5f-213d1932b6cc"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"follow","isCollection":false,"declaredType":"dataObject","id":"2056.cf7a7d8f-977e-4312-b046-8903b4e823c4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceName","isCollection":false,"declaredType":"dataObject","id":"2056.9b359655-1b37-4b87-bf8d-19e8df143c01"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hideDocumentSection","isCollection":false,"declaredType":"dataObject","id":"2056.21baa20b-36e6-420a-99b6-dabc2d9c6f0e"},{"itemSubjectRef":"itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935","name":"instanceSummary","isCollection":false,"declaredType":"dataObject","id":"2056.bd6236b0-e04b-4067-8af1-40116ac81c7a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedActivitiesCategory","isCollection":false,"declaredType":"dataObject","id":"2056.8ae618a2-e789-4937-a1da-c9875bbf9215"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"activitiesCategoriesSelectionList","isCollection":true,"declaredType":"dataObject","id":"2056.ef26f502-5ace-4704-969c-e4764e83804f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedTasksCategory","isCollection":false,"declaredType":"dataObject","id":"2056.9cfca0eb-67e6-48b6-a590-181fd11286bd"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"tasksCategoriesSelectionList","isCollection":true,"declaredType":"dataObject","id":"2056.cddcdbfa-d2f5-4de9-a134-d089b25dbd5f"},{"startQuantity":1,"outgoing":["2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d"],"incoming":["2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6"],"default":"2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d","extensionElements":{"nodeVisualInfo":[{"width":95,"x":227,"y":103,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client Side Init Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.1a666951-4594-47bb-9ad9-b801c8a71ac4","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.selectedInstanceId = tw.system.processInstance.id;\r\n\r\nvar breadcrumbText = null;\r\nif (window.location.href.indexOf('breadcrumbText=') != -1)  {\r\n\tbreadcrumbText = window.location.href.substring(window.location.href.indexOf('breadcrumbText=')+15);\r\n\tif (breadcrumbText.indexOf('&amp;') != -1) {\r\n\t\tbreadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('&amp;'));\r\n\t}\r\n\tif (breadcrumbText.indexOf('#') != -1) {\r\n\t\tbreadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('#'));\r\n\t}\r\n}\n                                \r\nvar _debug = function(){};\r\n\/\/ enable following line to log into browser console\r\n\/\/var _debug = console.log;\r\n\r\ntry {\r\n\t\/\/ check for breadcrumb\r\n\ttw.local.breadcrumbs = [];\r\n\tif (breadcrumbText) {\r\n\t\ttw.local.breadcrumbs.push({\r\n\t\t\tname: decodeURI(breadcrumbText),\r\n\t\t\tvalue: '{\"value\":\"back\",\"navigationDestination\":\"BACK\"}'\r\n\t\t});\r\n\t}\r\n    \r\n} catch (err) {\r\n\tconsole.log(\"Error within Instance Details initialization: \"+err);\r\n}\r\n\r\n\r\n"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedInstanceId","isCollection":false,"declaredType":"dataObject","id":"2056.934f9158-6cd8-4223-bff8-3a92815540c7"},{"outgoing":["2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6","2027.4d377842-7a0e-4967-948d-c0a66856f673"],"incoming":["2027.41cb3ad8-dd34-4f60-b919-34abed2fef06"],"default":"2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":131,"y":196,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Instance present?","declaredType":"exclusiveGateway","id":"2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0"},{"startQuantity":1,"outgoing":["2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420"],"incoming":["2027.4d377842-7a0e-4967-948d-c0a66856f673"],"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Output_Text1","configData":[{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1fa476df-8872-4212-8eb6-78a4f4ee0c5f","optionName":"@label","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3b1ca332-b7d0-4ac8-81e7-589d693e342f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ebb4f8b6-cc75-4aaa-830c-7cf84c9e566e","optionName":"@labelVisibility","value":"Show"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6c1c2e85-08d7-47d3-89a6-94fe6b5d5c3d","optionName":"@htmlOverrides","value":""}],"viewUUID":"64.fc2d6e5b-da91-4e0a-b874-3ec8ace34c82","binding":"tw.resource.Dashboards.defaultInstanceDetails.NoInstance","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"78b2ab0b-7b94-46db-8737-8ce857c45be3","version":"8550"},{"layoutItemId":"okButton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f6970509-b656-457c-86e2-91e5101ed142","optionName":"@label","value":"OK"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e311d86e-e717-4ad1-8042-9806d00d5420","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"649a0446-7ab5-4bef-850a-d509921c432e","optionName":"@labelVisibility","value":"Show"}],"viewUUID":"64.36f46ec6-616b-4e38-86aa-fba20ec6f9b4","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"272ece89-7eb6-4a83-8e48-e6e22e132ae9","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"extensionElements":{"nodeVisualInfo":[{"width":95,"x":224,"y":292,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Show Error","isForCompensation":false,"completionQuantity":1,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"id":"2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a"},{"targetRef":"2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Instance present?","declaredType":"sequenceFlow","id":"2027.41cb3ad8-dd34-4f60-b919-34abed2fef06","sourceRef":"787275bb-2992-4595-a9d6-a94de1a2988e"},{"incoming":["2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":403,"y":316,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"2025.d86aee95-db42-478c-b40f-04d67e91cde7"},{"targetRef":"2025.d86aee95-db42-478c-b40f-04d67e91cde7","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"400bdb3c-89a6-4eb2-91d7-a2196b92fd64","coachEventPath":"okButton"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420","sourceRef":"2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a"},{"targetRef":"2025.d959c04a-f61a-4508-b985-436bbc646544","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Server Side Init Data","declaredType":"sequenceFlow","id":"2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d","sourceRef":"2025.1a666951-4594-47bb-9ad9-b801c8a71ac4"},{"targetRef":"2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.processInstance == null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.4d377842-7a0e-4967-948d-c0a66856f673","sourceRef":"2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0"},{"startQuantity":1,"outgoing":["2027.60a32949-9558-42a0-8769-85badc83d43b"],"incoming":["2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e"],"default":"2027.60a32949-9558-42a0-8769-85badc83d43b","extensionElements":{"mode":["RefreshVariables"],"nodeVisualInfo":[{"width":95,"x":555,"y":293,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Process Variables","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.0189770c-7f8d-445b-ac63-eaa0944191b8"},{"startQuantity":1,"outgoing":["2027.850927ae-1953-4e74-a251-28ee606d108b"],"incoming":["2027.94a513af-a416-4e2c-888b-adf35e8a500b"],"default":"2027.850927ae-1953-4e74-a251-28ee606d108b","extensionElements":{"mode":["SaveBPDVariables"],"nodeVisualInfo":[{"width":95,"x":956,"y":406,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Update Process Variables","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c"},{"incoming":["2027.08c48f64-a737-415c-8dad-2db02f48d88c","2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db","2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454","2027.7ffead20-3b6e-4c13-b056-11c095659e79"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1077,"y":321,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.2ad584dc-fda8-4b3d-869a-03785828d7e0"},{"incoming":["2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1012,"y":127,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"ToOtherDashboard","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions","targetURL":"tw.local.navigationURL"}]},"name":"End","declaredType":"endEvent","id":"2025.961fc12e-4a5f-4257-90ba-899103878414"},{"parallelMultiple":false,"outgoing":["2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.5b01c2be-0aca-4074-8fee-c3a60184530b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1039,"y":411,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.e4f7632c-ff14-4873-bf10-415a01430c30","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.failedSaveMessage"]}}]}]},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"automaticRefreshTrigger","isCollection":false,"declaredType":"dataObject","id":"2056.ac0ba0af-9035-4a59-a657-f5eb4cbc9947"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"manualRefreshTrigger","isCollection":false,"declaredType":"dataObject","id":"2056.8c1dc92f-367e-4314-8c3b-89252c6b6805"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"failedSaveMessage","isCollection":false,"declaredType":"dataObject","id":"2056.3d456ca3-87ce-4bc3-a503-a86a19aa63d9"},{"startQuantity":1,"outgoing":["2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813"],"incoming":["2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e"],"default":"2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813","extensionElements":{"nodeVisualInfo":[{"width":95,"x":815,"y":298,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5aba8790-6f16-4c8d-9815-3b107b802637","scriptFormat":"text\/x-javascript","script":{"content":["\/\/  Check for validation errors in the instance data. For examle:\r\n\/\/ if (tw.local.name == \"\" ) {\r\n\/\/    tw.system.coachValidation.addValidationError(\"tw.local.name\", \"Name must be specified\");\r\n\/\/ }"]}},{"outgoing":["2027.08c48f64-a737-415c-8dad-2db02f48d88c","2027.94a513af-a416-4e2c-888b-adf35e8a500b"],"incoming":["2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813"],"default":"2027.94a513af-a416-4e2c-888b-adf35e8a500b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":955,"y":317,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Validation Error?","declaredType":"exclusiveGateway","id":"2025.2fec7021-4671-4209-b11a-50d140e6c717"},{"targetRef":"2025.2fec7021-4671-4209-b11a-50d140e6c717","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validation Error","declaredType":"sequenceFlow","id":"2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813","sourceRef":"2025.5aba8790-6f16-4c8d-9815-3b107b802637"},{"targetRef":"2025.2ad584dc-fda8-4b3d-869a-03785828d7e0","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length != 0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.08c48f64-a737-415c-8dad-2db02f48d88c","sourceRef":"2025.2fec7021-4671-4209-b11a-50d140e6c717"},{"incoming":["2027.954fbeb4-0a94-4ead-b909-798d9236b711"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":564,"y":519,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.248669b3-62a4-428d-972b-049cb489bfb1"},{"targetRef":"2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.60a32949-9558-42a0-8769-85badc83d43b","sourceRef":"2025.0189770c-7f8d-445b-ac63-eaa0944191b8"},{"itemSubjectRef":"itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54","name":"boSaveFailedError","isCollection":false,"declaredType":"dataObject","id":"2056.d51f9fdb-a15a-4ec3-afb6-b8a4c56e89db"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"canViewDiagram","isCollection":false,"declaredType":"dataObject","id":"2056.cb5bdf2f-b405-4d10-bf5b-a100445b9ac6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"navigationURL","isCollection":false,"declaredType":"dataObject","id":"2056.3155a4c4-7a66-473d-91c0-d0f0e258f73c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","documentation":[{"content":["This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected."],"textFormat":"text\/plain"}],"name":"unsavedLocalChanges","isCollection":false,"declaredType":"dataObject","id":"2056.48e50ca4-be3a-4ba4-bf00-a241ea55c8f3"},{"startQuantity":1,"outgoing":["2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db"],"incoming":["2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc"],"default":"2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1065,"y":213,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Merge Changes","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.e2dd5067-751f-4c56-aad9-e0e060b076fc","scriptFormat":"text\/x-javascript","script":{"content":["require([tw.local.helperScriptURL], function() { \/\/ Load the helper functions\r\n\r\n\t\/\/ The merge the server changes into the local variables based on the merge policy picked by the user.\r\n\r\n\t\/\/ Determine what type of merge the user requested\r\n\tvar keepConflictingLocalChanges = false;\r\n\tif(tw.local.dataSectionBoundaryEventType === \"KEEP_LOCAL\")\r\n\t\tkeepConflictingLocalChanges = true;\r\n\t\t\r\n\tmerge(tw.local, keepConflictingLocalChanges); \/\/ Reset variables\r\n\t          \r\n}); "]}},{"targetRef":"2025.d959c04a-f61a-4508-b985-436bbc646544","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"4da2ca11-edbf-4796-a022-be245707e685","coachEventPath":"Default_Instance_Details_Template1\/Service_Controller1"}],"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Automatic Refresh","declaredType":"sequenceFlow","id":"2027.20bd83f9-8381-4891-bc6b-fb468c15036a","sourceRef":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client."],"textFormat":"text\/plain"}],"name":"originalInput","isCollection":false,"declaredType":"dataObject","id":"2056.c8f3c91a-33c2-49e6-b12d-9e985c38b289"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server."],"textFormat":"text\/plain"}],"name":"incomingChanges","isCollection":false,"declaredType":"dataObject","id":"2056.63bdc052-e874-4983-90ef-a3786f8baa54"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["This variable is bound to the Data Section coach view. It contains the updated variable values from the coach."],"textFormat":"text\/plain"}],"name":"localChanges","isCollection":false,"declaredType":"dataObject","id":"2056.08c90b93-1a62-4953-9126-8abef8a1aed8"},{"startQuantity":1,"outgoing":["2027.954fbeb4-0a94-4ead-b909-798d9236b711"],"incoming":["2027.60a32949-9558-42a0-8769-85badc83d43b","2027.850927ae-1953-4e74-a251-28ee606d108b"],"default":"2027.954fbeb4-0a94-4ead-b909-798d9236b711","extensionElements":{"nodeVisualInfo":[{"width":95,"x":555,"y":406,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Reset Pending Change Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7","scriptFormat":"text\/x-javascript","script":{"content":["require([tw.local.helperScriptURL], function() { \/\/ Load the helper functions\r\n\r\n\t\/\/ The local variables have been refreshed with the latest changes from the server,\r\n\t\/\/ or the instance UI has been saved.  There are no pending changes.  Reset merge data.\r\n\t\t\r\n\tresetDataSyncronizationVariables(tw.local); \/\/ Reset variables\r\n\t          \r\n}); \r\n\r\n\r\n"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","documentation":[{"content":["This variable indicates the type of boundary event triggered by the Data Section coach view."],"textFormat":"text\/plain"}],"name":"dataSectionBoundaryEventType","isCollection":false,"declaredType":"dataObject","id":"2056.035f5618-b706-4a78-9494-f3d0561685b8"},{"outgoing":["2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e","2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e","2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc"],"incoming":["2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9"],"default":"2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":709,"y":235,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Data Section Action?","declaredType":"exclusiveGateway","id":"2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d"},{"targetRef":"2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"f9dd1e11-cef4-448c-ac9d-9a168c357ea2","coachEventPath":"CustomDataSection"}],"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9","sourceRef":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099"},{"targetRef":"2025.0189770c-7f8d-445b-ac63-eaa0944191b8","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.dataSectionBoundaryEventType == \"DISCARD\""]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Discard Local Changes","declaredType":"sequenceFlow","id":"2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e","sourceRef":"2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d"},{"targetRef":"2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Reset Pending Change Data","declaredType":"sequenceFlow","id":"2027.850927ae-1953-4e74-a251-28ee606d108b","sourceRef":"2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c"},{"targetRef":"2025.248669b3-62a4-428d-972b-049cb489bfb1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.954fbeb4-0a94-4ead-b909-798d9236b711","sourceRef":"2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7"},{"targetRef":"2025.5aba8790-6f16-4c8d-9815-3b107b802637","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.dataSectionBoundaryEventType == \"SAVE\""]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Save Data","declaredType":"sequenceFlow","id":"2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e","sourceRef":"2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d"},{"targetRef":"2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c","conditionExpression":{"declaredType":"TFormalExpression","content":["  "]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.94a513af-a416-4e2c-888b-adf35e8a500b","sourceRef":"2025.2fec7021-4671-4209-b11a-50d140e6c717"},{"targetRef":"2025.e2dd5067-751f-4c56-aad9-e0e060b076fc","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Merge Changes","declaredType":"sequenceFlow","id":"2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc","sourceRef":"2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d"},{"targetRef":"2025.2ad584dc-fda8-4b3d-869a-03785828d7e0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db","sourceRef":"2025.e2dd5067-751f-4c56-aad9-e0e060b076fc"},{"targetRef":"2025.2ad584dc-fda8-4b3d-869a-03785828d7e0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454","sourceRef":"2025.e4f7632c-ff14-4873-bf10-415a01430c30"},{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8"],"isInterrupting":true,"eventDefinition":[{"extensionElements":{"coachEventTriggers":[{"coachEventTriggerBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventTriggerBinding","id":"75038bdc-ca3b-4728-b3a0-8e033b025835","coachId":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099","coachEventPath":"Default_Instance_Details_Template1\/Service_Controller2"}],"enable":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventTriggers"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDataNotificationEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"2025.684a651a-f1a0-4c85-bbf6-ab061b9147e0"},{"incoming":["2027.a14189d3-4045-4db1-b48b-d476449e23f5"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":700,"y":200,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.4e4757e2-60c3-491c-b599-f636ba69864a"},{"startQuantity":1,"outgoing":["2027.a14189d3-4045-4db1-b48b-d476449e23f5"],"incoming":["2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8"],"default":"2027.a14189d3-4045-4db1-b48b-d476449e23f5","extensionElements":{"nodeVisualInfo":[{"width":95,"x":345,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Collect change information","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.2bc185fb-7595-45b4-8791-6e67e12ab207","scriptFormat":"text\/x-javascript","script":{"content":["require([tw.local.helperScriptURL], function() { \/\/ Load the helper functions\r\n\t\r\n\t\/\/ Calculate if there are any relevant incoming changes\r\n\r\n\t\/\/ There are unsaved changes .  This can be detected by calling tw.system.coachUtils.hasLocallyChangedInputVars().  Here, however, this variable is\r\n\t\/\/ set by the Data Section coach view when it detects that a child view's data binding has changed.\r\n\tif(tw.local.unsavedLocalChanges ===  true || tw.system.coachUtils.hasLocallyChangedInputVars()){\r\n\t\t\r\n\t\tif(!tw.local.unsavedLocalChanges)\r\n\t\t\ttw.local.unsavedLocalChanges = true;\r\n\t\t\t\r\n\t\t\/\/ Call the helper function to update the incoming and local changes.\r\n\t\tcheckIncomingChanges(tw.local, tw.system.coachUtils.getLocallyChangedVars(),  tw.system.dataChangeUtils.getIncomingVars())\t\t          \r\n\t\t\r\n\t} else if(tw.system.coachUtils.getLocallyChangedVars().length &gt; 0) { \/\/ No pending changes to be merged, or local changes\r\n\t\r\n\t\t\/\/ Apply incoming changes to the local variables.\r\n\t\ttw.system.dataChangeUtils.applyAllIncomingVars();\r\n\t\ttw.local.incomingChangesMerged = true;  \/\/ Signal that changes were applied automatically.  The Data Section can listen and pop up a notification.\r\n\t \r\n\t}\r\n\r\n}); "]}},{"targetRef":"2025.2bc185fb-7595-45b4-8791-6e67e12ab207","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Collect change information","declaredType":"sequenceFlow","id":"2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8","sourceRef":"2025.684a651a-f1a0-4c85-bbf6-ab061b9147e0"},{"targetRef":"2025.4e4757e2-60c3-491c-b599-f636ba69864a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.a14189d3-4045-4db1-b48b-d476449e23f5","sourceRef":"2025.2bc185fb-7595-45b4-8791-6e67e12ab207"}],"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":51,"y":463,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Data Change","triggeredByEvent":true,"isForCompensation":false,"completionQuantity":1,"declaredType":"subProcess","id":"2025.4577a5b8-213f-4ae9-9bf0-ad2d5fd57fd5"},{"targetRef":"2025.1a666951-4594-47bb-9ad9-b801c8a71ac4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6","sourceRef":"2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0"},{"startQuantity":1,"outgoing":["2027.3be85c06-e292-42a0-bc27-d88501fcf12f"],"incoming":["2027.c461f373-e3eb-4bf6-b13f-4384f716944f"],"default":"2027.3be85c06-e292-42a0-bc27-d88501fcf12f","extensionElements":{"nodeVisualInfo":[{"width":95,"x":523,"y":103,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init Data Change Support","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575","scriptFormat":"text\/x-javascript","script":{"content":["require([tw.local.helperScriptURL], function() { \/\/ Load the helper functions\r\n\r\n\t\/\/ Initialize variables that support data change syncrhonization.  \r\n\t\r\n\t\/\/ This can only be done once the URL for the helper functions is returned by the \r\n\t\/\/ Server Sice Init Data.  Since this is can also be invoked as part of auto refresh the initialization\r\n\t\/\/ should only happen if the variables are undefined.\r\n\t\r\n\tif(tw.local.originalInput == undefined &amp;&amp; tw.local.localChanges == undefined &amp;&amp; tw.local.incomingChanges == undefined){\r\n\t\tinitializeDataSyncronizationVariables(tw.local); \/\/ Intialize variables\r\n\t}\r\n\t\r\n}); \r\n"]}},{"targetRef":"2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init Data Change Support","declaredType":"sequenceFlow","id":"2027.c461f373-e3eb-4bf6-b13f-4384f716944f","sourceRef":"2025.d959c04a-f61a-4508-b985-436bbc646544"},{"targetRef":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To View Instance Details","declaredType":"sequenceFlow","id":"2027.3be85c06-e292-42a0-bc27-d88501fcf12f","sourceRef":"2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","documentation":[{"content":["This variable contains the URL or the managed asset that provides data synchronization\u00a0functions used in this service."],"textFormat":"text\/plain"}],"name":"helperScriptURL","isCollection":false,"declaredType":"dataObject","id":"2056.877ac262-2997-47b7-9d97-fff1da1e3729"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","documentation":[{"content":["This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data."],"textFormat":"text\/plain"}],"name":"incomingChangesMerged","isCollection":false,"declaredType":"dataObject","id":"2056.4bfc2751-322d-4afc-bfa6-16c509c086df"},{"targetRef":"2025.961fc12e-4a5f-4257-90ba-899103878414","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"4a4baad0-69c3-4fba-815d-e5c0af8d6e55","coachEventPath":"Default_Instance_Details_Template1\/Navigation_Controller1"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4","sourceRef":"2025.1f3d7135-8232-4eed-957e-acf8a8e96099"},{"parallelMultiple":false,"outgoing":["2027.3e5701d4-bd39-4fb8-8645-975d20fd4320"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.00562359-2779-4a98-8611-3a6410430e0b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1039,"y":447,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.ff54e731-6efb-4a6e-ab4c-0b98d145d174","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54","declaredType":"TFormalExpression","content":["tw.local.boSaveFailedError"]}}]}]},{"startQuantity":1,"outgoing":["2027.7ffead20-3b6e-4c13-b056-11c095659e79"],"incoming":["2027.3e5701d4-bd39-4fb8-8645-975d20fd4320"],"default":"2027.7ffead20-3b6e-4c13-b056-11c095659e79","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1162,"y":424,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Populate BO Save Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a832a826-91d4-4444-b41b-82371cfcac29","scriptFormat":"text\/x-javascript","script":{"content":["tw.system.coachValidation.populateFromBOSaveFailedError( tw.local.boSaveFailedError );"]}},{"targetRef":"2025.a832a826-91d4-4444-b41b-82371cfcac29","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.3e5701d4-bd39-4fb8-8645-975d20fd4320","sourceRef":"2025.ff54e731-6efb-4a6e-ab4c-0b98d145d174"},{"targetRef":"2025.2ad584dc-fda8-4b3d-869a-03785828d7e0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.7ffead20-3b6e-4c13-b056-11c095659e79","sourceRef":"2025.a832a826-91d4-4444-b41b-82371cfcac29"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"6397c037-71ff-4f71-9ce6-819ba6eaaa37","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"78ca7e11-8c77-4609-9cee-9e55d2c3b925","processType":"None"}],"exposedAs":["InstanceDetailsUI"]},"implementation":"##unspecified","documentation":[{"content":["&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;\/p&gt;&lt;p&gt;The service template includes:&lt;\/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;\/b&gt; coach, which has these coach controls:&lt;\/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;\/b&gt; - displays the instance details in Process Portal&lt;\/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;\/b&gt; - displays the values of the variables that are passed into the human service&lt;\/li&gt;&lt;\/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;\/b&gt; coach - returns an error if the instance is not found.&lt;\/li&gt;&lt;\/ul&gt;"],"textFormat":"text\/plain"}],"name":"Servi\u00e7o manual de lado-cliente","declaredType":"globalUserTask","id":"1.236d78a5-bcbd-4c3e-a421-cfb181f40791","ioSpecification":{"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.4b698a84-427b-4801-9c1d-18ddcc561bc6","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.46923c36-435d-480a-8627-cca9772b6a02"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}]},"inputSet":[{"dataInputRefs":["2055.f8b1349a-fd25-4d36-852b-6ae1e00920d0","2055.88aa503b-eaa6-4b04-8ea6-f0b9c0345628","2055.f393f41a-02b2-4862-8e4b-67be8f1c1f47"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true","{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyInputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"input1","isCollection":false,"id":"2055.f8b1349a-fd25-4d36-852b-6ae1e00920d0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"output1","isCollection":false,"id":"2055.88aa503b-eaa6-4b04-8ea6-f0b9c0345628"},{"itemSubjectRef":"itm.12.60da4770-d3a3-4937-840f-8fd74f8c33ce","name":"variable1","isCollection":false,"id":"2055.f393f41a-02b2-4862-8e4b-67be8f1c1f47"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"d43a601e-be28-4aff-9759-7e726f09c6f1"}</jsonData>
        <processParameter name="input1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f8b1349a-fd25-4d36-852b-6ae1e00920d0</processParameterId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ad5b7496-de1d-4585-9855-4f115b4ecdca</guid>
            <versionId>c391a9ed-ab5f-43c4-8df5-f5acfca63d7e</versionId>
        </processParameter>
        <processParameter name="output1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.88aa503b-eaa6-4b04-8ea6-f0b9c0345628</processParameterId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>559d2253-8aad-4b9b-a822-d8a27e83bb3e</guid>
            <versionId>d8912490-aa79-42dd-92c1-bb015e92fe63</versionId>
        </processParameter>
        <processParameter name="variable1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f393f41a-02b2-4862-8e4b-67be8f1c1f47</processParameterId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.60da4770-d3a3-4937-840f-8fd74f8c33ce</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5b4469ec-ac64-465d-bc95-7a082d3f0f19</guid>
            <versionId>48746169-05d0-423a-86e2-0b08140972cd</versionId>
        </processParameter>
        <processVariable name="activitiesTextFilter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.57f86fa0-67b6-4726-ad97-162161f2e6fe</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>15feb84b-6366-4c97-a7aa-88c3adea4acf</guid>
            <versionId>44799042-5c08-4855-ac72-3dd4bf67b758</versionId>
        </processVariable>
        <processVariable name="breadcrumbs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e4585b41-c23e-48cb-b206-da672aee5880</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ddcacf84-d02f-40ec-92dc-481faede2194</guid>
            <versionId>6b0a7d48-36da-48ba-86ec-334ca9e2ca03</versionId>
        </processVariable>
        <processVariable name="buttonClicked_GanttView">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.aaa77807-46d9-4f14-ae1e-c593aa435a1f</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ce33bace-312b-4a11-bc43-9ed94bc564fd</guid>
            <versionId>498244b5-383c-4516-8641-229e8dd39dd3</versionId>
        </processVariable>
        <processVariable name="currentDate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ff69c386-cd9e-4193-9d5f-213d1932b6cc</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a193aabe-f704-43cf-a195-5ef44e3c8be8</guid>
            <versionId>f45b6418-9ead-4e4a-a32d-168224dd9d64</versionId>
        </processVariable>
        <processVariable name="follow">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cf7a7d8f-977e-4312-b046-8903b4e823c4</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb909f7a-7e0a-4636-80ab-0f70de1412cb</guid>
            <versionId>84b528e1-d1fc-45ef-94e6-a327dc1054bf</versionId>
        </processVariable>
        <processVariable name="instanceName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b359655-1b37-4b87-bf8d-19e8df143c01</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>203a3fd2-879a-4d8a-a2a1-2e94dacfef48</guid>
            <versionId>c465f80b-cdbd-4734-8ab5-9facbe507e0a</versionId>
        </processVariable>
        <processVariable name="hideDocumentSection">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.21baa20b-36e6-420a-99b6-dabc2d9c6f0e</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>50f83cc6-3dc1-44e6-8287-0ff7cde83655</guid>
            <versionId>f9b3dae1-a977-4e1f-9cd5-dc26c296c8d2</versionId>
        </processVariable>
        <processVariable name="instanceSummary">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bd6236b0-e04b-4067-8af1-40116ac81c7a</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <classId>af325104-4d5d-4315-88c6-6c1de40811b9/12.9a9d479f-686b-484b-80a6-30b52aa4e935</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>25e76bcb-047f-413d-8022-097a4de29351</guid>
            <versionId>e91695f1-211b-4c17-b7d7-a263729b1287</versionId>
        </processVariable>
        <processVariable name="selectedActivitiesCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8ae618a2-e789-4937-a1da-c9875bbf9215</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>57fa4b9b-24fc-43e7-be13-e8b66a151e3f</guid>
            <versionId>3c11cc73-2283-48f8-bba3-27570b938f98</versionId>
        </processVariable>
        <processVariable name="activitiesCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ef26f502-5ace-4704-969c-e4764e83804f</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>38954c7b-5f2e-437b-b27e-24abd93351c0</guid>
            <versionId>5d3b2309-308c-4682-a8b7-a717cd8fd594</versionId>
        </processVariable>
        <processVariable name="selectedTasksCategory">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9cfca0eb-67e6-48b6-a590-181fd11286bd</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cfe4926d-0440-4e8d-adb4-87782a2d1551</guid>
            <versionId>2bd6ff9d-444e-43ac-b05a-b899568f47ee</versionId>
        </processVariable>
        <processVariable name="tasksCategoriesSelectionList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cddcdbfa-d2f5-4de9-a134-d089b25dbd5f</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>23c4aa96-86d9-4113-8fbe-244c36e3ae7e</guid>
            <versionId>c6288dbb-dd2f-4e1d-97c4-a8fafaded6d3</versionId>
        </processVariable>
        <processVariable name="selectedInstanceId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.934f9158-6cd8-4223-bff8-3a92815540c7</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f14d8be9-db20-4b1d-9fad-31de8a2a6e3e</guid>
            <versionId>034718ba-8587-4c1a-9b58-************</versionId>
        </processVariable>
        <processVariable name="automaticRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ac0ba0af-9035-4a59-a657-f5eb4cbc9947</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1b117bb6-b86f-4da2-a6ed-33c80d16d1da</guid>
            <versionId>15a18c48-03ff-4bbd-8e53-4047440b71ce</versionId>
        </processVariable>
        <processVariable name="manualRefreshTrigger">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8c1dc92f-367e-4314-8c3b-89252c6b6805</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a41056b0-1509-4c65-b9bb-5dd5aae384ce</guid>
            <versionId>a34b393f-5924-4ffa-8cea-6628347050de</versionId>
        </processVariable>
        <processVariable name="failedSaveMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3d456ca3-87ce-4bc3-a503-a86a19aa63d9</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bee97224-2baa-4bf1-9f33-79e2a12b64ac</guid>
            <versionId>9815ef5a-1203-47cd-bd04-9d7b35f5d6ae</versionId>
        </processVariable>
        <processVariable name="boSaveFailedError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d51f9fdb-a15a-4ec3-afb6-b8a4c56e89db</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.d8fa7561-8636-40a9-bd70-f45128bb7e54</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>11c5c128-e109-4438-bffe-86e8e36f32e5</guid>
            <versionId>8c2e868c-a017-4ba5-8fe5-fcbf4c15449c</versionId>
        </processVariable>
        <processVariable name="canViewDiagram">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb5bdf2f-b405-4d10-bf5b-a100445b9ac6</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1ee54c1f-e17a-4f17-874b-8f6e02b890be</guid>
            <versionId>5ebaf601-0fef-4401-8dea-6ba12946c679</versionId>
        </processVariable>
        <processVariable name="navigationURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3155a4c4-7a66-473d-91c0-d0f0e258f73c</processVariableId>
            <description isNull="true" />
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c6f7af95-0f9c-424e-ac1c-f7910af8e065</guid>
            <versionId>708696eb-d187-4dfc-9972-7b7f37e575e7</versionId>
        </processVariable>
        <processVariable name="unsavedLocalChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.48e50ca4-be3a-4ba4-bf00-a241ea55c8f3</processVariableId>
            <description>This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>06962b30-7932-4ce3-ac07-5dc1f76c58ea</guid>
            <versionId>8011cd56-5d5a-48e8-a884-cfc56c88f00a</versionId>
        </processVariable>
        <processVariable name="originalInput">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c8f3c91a-33c2-49e6-b12d-9e985c38b289</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ef7f2b58-82d1-4755-b75a-827b6a9238ef</guid>
            <versionId>9c3057d8-70c3-4f2d-be1f-62eadd032576</versionId>
        </processVariable>
        <processVariable name="incomingChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63bdc052-e874-4983-90ef-a3786f8baa54</processVariableId>
            <description>This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e212ef0c-84c4-4787-9050-4320dcfb4d27</guid>
            <versionId>a49a0a1c-32ec-48bc-a2e1-277d31daf763</versionId>
        </processVariable>
        <processVariable name="localChanges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.08c90b93-1a62-4953-9126-8abef8a1aed8</processVariableId>
            <description>This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>946cc1a0-d875-496f-a9d6-05f62d3c4d70</guid>
            <versionId>6c37ef3a-c7dc-4e18-8bfd-d65dbd99567f</versionId>
        </processVariable>
        <processVariable name="dataSectionBoundaryEventType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.035f5618-b706-4a78-9494-f3d0561685b8</processVariableId>
            <description>This variable indicates the type of boundary event triggered by the Data Section coach view.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bdfaf9cd-4ca7-42cd-9034-3e36fb91b644</guid>
            <versionId>e55ef6f6-0717-4a40-a942-5d101edee242</versionId>
        </processVariable>
        <processVariable name="helperScriptURL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.877ac262-2997-47b7-9d97-fff1da1e3729</processVariableId>
            <description>This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bf711948-a74a-4565-ac2e-10475d2119b9</guid>
            <versionId>e8eb1a55-61c5-4d78-b283-ed3ea703a370</versionId>
        </processVariable>
        <processVariable name="incomingChangesMerged">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4bfc2751-322d-4afc-bfa6-16c509c086df</processVariableId>
            <description>This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</description>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <classId>2c7ae840-cf8c-4998-839b-2cf42b6b7656/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9dffac7a-59b0-45c5-828e-48837b622f93</guid>
            <versionId>5343f1f0-58c9-45fa-8582-127bb8a41c7e</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c</processItemId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <name>Update Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.efed6b97-18af-4f2e-b975-af9ba469a047</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-1424</guid>
            <versionId>08198c86-ac26-4c41-a07b-81003fcc7d27</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.efed6b97-18af-4f2e-b975-af9ba469a047</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>34ed4710-08f2-4577-abec-cc7e30c078b7</guid>
                <versionId>02da9eba-bfa8-44c1-b000-aedd4fbfe370</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.26d2fb40-de68-4dba-92ea-3f50485c7c47</processItemId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.4804cfcf-a1b2-4c8c-9f7b-63ada6fb18dc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-1422</guid>
            <versionId>8ed8a8c4-c1d1-4435-a24f-f09ab4a37935</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d959c04a-f61a-4508-b985-436bbc646544</processItemId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <name>Server Side Init Data</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0d570583-3218-4920-a36a-f60942ff327a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-1425</guid>
            <versionId>93da50f5-8460-4449-baf8-aafe1402a23c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0d570583-3218-4920-a36a-f60942ff327a</subProcessId>
                <attachedProcessRef>af325104-4d5d-4315-88c6-6c1de40811b9/1.554dd7d5-ceb8-4548-91e5-788940b70e0d</attachedProcessRef>
                <guid>d63b2eeb-a716-4d35-ba06-0d5c8e8f1f6e</guid>
                <versionId>4094a2df-57b7-4041-bd4b-062d2d17bbbd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0189770c-7f8d-445b-ac63-eaa0944191b8</processItemId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <name>Get Process Variables</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.36cd6a8a-55c4-4f35-80ba-3f3c3f22a79a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-1423</guid>
            <versionId>9ee28f5f-677f-46eb-ad3b-01c682f86efb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.36cd6a8a-55c4-4f35-80ba-3f3c3f22a79a</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>f56063aa-c966-42b5-9f9f-42d9ec9c14f3</guid>
                <versionId>efc05eb8-af36-436f-975c-02b3b8c73fe7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bd67c259-d07c-406e-9853-2bb6d00634c9</processItemId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.bc3813fd-23af-49e1-9d83-8ba82a02996e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-1421</guid>
            <versionId>f3f4334b-ba4c-432f-a64c-0145f954da76</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.bc3813fd-23af-49e1-9d83-8ba82a02996e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>21dafaa4-a767-4368-b13c-479747b06c24</guid>
                <versionId>6174b425-46e9-4541-b5b0-9578fd046e02</versionId>
            </TWComponent>
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.cea8b8f5-546b-4e7d-a55d-96562cb9bd64</resourceProcessLinkId>
            <resourceBundleGroupId>af325104-4d5d-4315-88c6-6c1de40811b9/50.4b698a84-427b-4801-9c1d-18ddcc561bc6</resourceBundleGroupId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <guid>8c0b6dff-1b8c-4c11-a51a-6d55402ebe61</guid>
            <versionId>6edd5299-bf98-4e89-b191-ec26e90742d7</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.26d2fb40-de68-4dba-92ea-3f50485c7c47</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns17:definitions xmlns:ns17="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/bpm/Extensions" xmlns:ns6="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/xmlns/links" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/tagging" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" id="d43a601e-be28-4aff-9759-7e726f09c6f1" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns17:globalUserTask name="Serviço manual de lado-cliente" id="1.236d78a5-bcbd-4c3e-a421-cfb181f40791">
                    <ns17:documentation>&lt;p&gt;When you create a new details UI, the generated human service uses a copy of this template. You can further customize the human service to create your details user interface.&lt;/p&gt;&lt;p&gt;The service template includes:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A &lt;b&gt;View instance details&lt;/b&gt; coach, which has these coach controls:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;&lt;b&gt;Default Instance Details Template&lt;/b&gt; - displays the instance details in Process Portal&lt;/li&gt;&lt;li&gt;&lt;b&gt;Data section view&lt;/b&gt; - displays the values of the variables that are passed into the human service&lt;/li&gt;&lt;/ul&gt;&lt;li&gt;A &lt;b&gt;Show error&lt;/b&gt; coach - returns an error if the instance is not found.&lt;/li&gt;&lt;/ul&gt;</ns17:documentation>
                    <ns17:extensionElements>
                        <ns3:userTaskImplementation id="78ca7e11-8c77-4609-9cee-9e55d2c3b925">
                            <ns17:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="787275bb-2992-4595-a9d6-a94de1a2988e">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                </ns17:extensionElements>
                                <ns17:outgoing>2027.41cb3ad8-dd34-4f60-b919-34abed2fef06</ns17:outgoing>
                            </ns17:startEvent>
                            <ns3:formTask isHeritageCoach="false" isForCompensation="false" startQuantity="1" completionQuantity="1" name="View Instance Details" id="2025.1f3d7135-8232-4eed-957e-acf8a8e96099">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="675" y="103" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns17:incoming>
                                <ns17:incoming>2027.3be85c06-e292-42a0-bc27-d88501fcf12f</ns17:incoming>
                                <ns17:outgoing>2027.20bd83f9-8381-4891-bc6b-fb468c15036a</ns17:outgoing>
                                <ns17:outgoing>2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4</ns17:outgoing>
                                <ns17:outgoing>2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9</ns17:outgoing>
                                <ns3:formDefinition>
                                    <ns18:coachDefinition>
                                        <ns18:layout>
                                            <ns18:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns18:ViewRef" version="8550">
                                                <ns18:id>312c9e8b-f749-4fc7-89d7-fdf42381a821</ns18:id>
                                                <ns18:layoutItemId>Default_Instance_Details_Template1</ns18:layoutItemId>
                                                <ns18:configData>
                                                    <ns18:id>f63e14ca-c966-45b2-882e-81bc2b9c8187</ns18:id>
                                                    <ns18:optionName>@label</ns18:optionName>
                                                    <ns18:value>Default Instance Details Template</ns18:value>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>2bcc1159-507c-4aac-8738-9e6fba11a6e1</ns18:id>
                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                    <ns18:value />
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>d179dba8-0882-4a25-8d61-a0fb9c613201</ns18:id>
                                                    <ns18:optionName>@labelVisibility</ns18:optionName>
                                                    <ns18:value>Show</ns18:value>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>b690cd77-16bd-4efd-8563-59acf3690f2e</ns18:id>
                                                    <ns18:optionName>activitiesTextFilter</ns18:optionName>
                                                    <ns18:value>tw.local.activitiesTextFilter</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>039d54a8-4561-4592-86b1-80d78fb8c9af</ns18:id>
                                                    <ns18:optionName>addButtonDropdownSelectedItem</ns18:optionName>
                                                    <ns18:value />
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>18d42900-50f6-42ca-8402-4e73c7058a9a</ns18:id>
                                                    <ns18:optionName>breadcrumbs</ns18:optionName>
                                                    <ns18:value>tw.local.breadcrumbs[]</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>e753769b-2683-4abf-8949-c0ee9983d269</ns18:id>
                                                    <ns18:optionName>breadcrumbText</ns18:optionName>
                                                    <ns18:value />
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>dd8e737f-97d3-4649-8cce-85cd250b15f7</ns18:id>
                                                    <ns18:optionName>buttonClicked_GanttView</ns18:optionName>
                                                    <ns18:value>tw.local.buttonClicked_GanttView</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>90082a09-f8d0-4d81-8f4f-d0978b834eeb</ns18:id>
                                                    <ns18:optionName>currentPage</ns18:optionName>
                                                    <ns18:value />
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>75848139-b40b-4f67-83a2-5357fa191e10</ns18:id>
                                                    <ns18:optionName>documentAddButtonDropdownList</ns18:optionName>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>d78f38bf-38b3-4b96-81af-5cd2965170b3</ns18:id>
                                                    <ns18:optionName>follow</ns18:optionName>
                                                    <ns18:value>tw.local.follow</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>adf58ddf-5fba-4503-8024-a73d35ab180c</ns18:id>
                                                    <ns18:optionName>instanceName</ns18:optionName>
                                                    <ns18:value>tw.local.instanceName</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>3d03a7a7-7ae9-4769-8ca9-19f74bf3af1f</ns18:id>
                                                    <ns18:optionName>instanceSummary</ns18:optionName>
                                                    <ns18:value>tw.local.instanceSummary</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>75345881-e617-4621-8d2a-23fa52df5af3</ns18:id>
                                                    <ns18:optionName>activitiesCategoriesSelectionList</ns18:optionName>
                                                    <ns18:value>tw.local.activitiesCategoriesSelectionList[]</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>2ffcd419-851b-484b-82e6-21633b183a06</ns18:id>
                                                    <ns18:optionName>selectedActivitiesCategory</ns18:optionName>
                                                    <ns18:value>tw.local.selectedActivitiesCategory</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>8c5da619-d4d9-4239-8986-3a9ad08ba623</ns18:id>
                                                    <ns18:optionName>selectedInstanceId</ns18:optionName>
                                                    <ns18:value>tw.local.selectedInstanceId</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>a8a63797-2fbe-4edb-8a03-d1bfd0d586a5</ns18:id>
                                                    <ns18:optionName>selectedTasksCategory</ns18:optionName>
                                                    <ns18:value>tw.local.selectedTasksCategory</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>fad4e682-3e04-4ca1-8f63-816879e3b34d</ns18:id>
                                                    <ns18:optionName>tasksCategoriesSelectionList</ns18:optionName>
                                                    <ns18:value>tw.local.tasksCategoriesSelectionList[]</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>efb4cd08-f86e-40d7-80e9-8cc94e746452</ns18:id>
                                                    <ns18:optionName>currentDate</ns18:optionName>
                                                    <ns18:value>tw.local.currentDate</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>db8724e7-9529-45a3-89a0-056aa3f5311e</ns18:id>
                                                    <ns18:optionName>hideDocumentSection</ns18:optionName>
                                                    <ns18:value>tw.local.hideDocumentSection</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>f8422e0b-3194-4e87-854e-97532e1f6f24</ns18:id>
                                                    <ns18:optionName>refreshTrigger</ns18:optionName>
                                                    <ns18:value>tw.local.automaticRefreshTrigger</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>d509199f-5840-416e-8ba6-e492ec8116d7</ns18:id>
                                                    <ns18:optionName>manualRefreshTrigger</ns18:optionName>
                                                    <ns18:value>tw.local.manualRefreshTrigger</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>c8dc1403-728a-42dc-8a46-e61f16c73af3</ns18:id>
                                                    <ns18:optionName>canViewDiagram</ns18:optionName>
                                                    <ns18:value>tw.local.canViewDiagram</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>2b0fcd30-bde7-4bb8-8188-e237917bd138</ns18:id>
                                                    <ns18:optionName>navigationURL</ns18:optionName>
                                                    <ns18:value>tw.local.navigationURL</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>3fc5cd8c-d88c-443f-82d3-51cac3534953</ns18:id>
                                                    <ns18:optionName>unsavedLocalChanges</ns18:optionName>
                                                    <ns18:value>tw.local.unsavedLocalChanges</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>7e161c1f-1c84-4d83-8d2c-da98b99f38fd</ns18:id>
                                                    <ns18:optionName>preventBoundaryEvent</ns18:optionName>
                                                    <ns18:value>tw.local.unsavedLocalChanges</ns18:value>
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:viewUUID>64.5f0e6839-f7f9-41a3-b633-541ba91f9b31</ns18:viewUUID>
                                                <ns18:contentBoxContrib>
                                                    <ns18:id>7b9be36f-a072-4a9f-8e28-5b82b638e3a5</ns18:id>
                                                    <ns18:contentBoxId>ContentBox1</ns18:contentBoxId>
                                                    <ns18:contributions xsi:type="ns18:ViewRef" version="8550">
                                                        <ns18:id>bf0bed38-b0b3-4061-86b8-ed9a128a2f74</ns18:id>
                                                        <ns18:layoutItemId>CustomDataSection</ns18:layoutItemId>
                                                        <ns18:configData>
                                                            <ns18:id>4e2a3c9c-c1b4-46c1-8702-a759801b9a98</ns18:id>
                                                            <ns18:optionName>@label</ns18:optionName>
                                                            <ns18:value>Data Section</ns18:value>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>dfa8c6bf-96af-4357-884c-feccd236438e</ns18:id>
                                                            <ns18:optionName>@helpText</ns18:optionName>
                                                            <ns18:value />
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>b1b4c0df-7ee7-4d78-80b3-b304b125bc71</ns18:id>
                                                            <ns18:optionName>@labelVisibility</ns18:optionName>
                                                            <ns18:value>Show</ns18:value>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>fe1d0946-027e-4431-8e4e-cf61959e7246</ns18:id>
                                                            <ns18:optionName>instanceId</ns18:optionName>
                                                            <ns18:value>tw.local.selectedInstanceId</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>812694fc-ec5b-4b28-8cc3-30b5f79a6df8</ns18:id>
                                                            <ns18:optionName>refreshTrigger</ns18:optionName>
                                                            <ns18:value>tw.local.automaticRefreshTrigger</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>4e9831a9-2a2c-42ad-8496-64f8a2986996</ns18:id>
                                                            <ns18:optionName>failedUpdateMessage</ns18:optionName>
                                                            <ns18:value>tw.local.failedSaveMessage</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>ba75e726-3020-4deb-801d-97c773689bc1</ns18:id>
                                                            <ns18:optionName>failedSaveMessage</ns18:optionName>
                                                            <ns18:value>tw.local.failedSaveMessage</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>5ffdf52f-a81e-4102-8021-e42182614f3b</ns18:id>
                                                            <ns18:optionName>collapsible</ns18:optionName>
                                                            <ns18:value>true</ns18:value>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>71c3f7d5-5923-43a1-8afb-08d66ef58935</ns18:id>
                                                            <ns18:optionName>instanceStatus</ns18:optionName>
                                                            <ns18:value>tw.local.instanceSummary.status</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>c56113bb-d385-4851-8f6d-845a7e4735cf</ns18:id>
                                                            <ns18:optionName>boundaryEventType</ns18:optionName>
                                                            <ns18:value>tw.local.dataSectionBoundaryEventType</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>2671d70f-2db5-481a-8f4d-4bfab3eb4c78</ns18:id>
                                                            <ns18:optionName>incomingChanges</ns18:optionName>
                                                            <ns18:value>tw.local.incomingChanges</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>72d5852e-9d8a-4e7d-874e-4c65a27b99e8</ns18:id>
                                                            <ns18:optionName>localChanges</ns18:optionName>
                                                            <ns18:value>tw.local.localChanges</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>ea375ed0-449e-4fc0-832c-1b3b8aceeb78</ns18:id>
                                                            <ns18:optionName>incomingChangesMerged</ns18:optionName>
                                                            <ns18:value>tw.local.incomingChangesMerged</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>64dfbd54-2f9b-47e8-82db-68f5ff41050d</ns18:id>
                                                            <ns18:optionName>unsavedLocalChanges</ns18:optionName>
                                                            <ns18:value>tw.local.unsavedLocalChanges</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>977f2648-ba2a-456b-8a27-8e7b2daff4bc</ns18:id>
                                                            <ns18:optionName>originalInput</ns18:optionName>
                                                            <ns18:value>tw.local.originalInput</ns18:value>
                                                            <ns18:valueType>dynamic</ns18:valueType>
                                                        </ns18:configData>
                                                        <ns18:configData>
                                                            <ns18:id>a3b13163-61bc-4d25-8ed1-6999d99fc36d</ns18:id>
                                                            <ns18:optionName>editMode</ns18:optionName>
                                                            <ns18:value>true</ns18:value>
                                                        </ns18:configData>
                                                        <ns18:viewUUID>64.0678c7bb-7028-4bca-8111-e0e9977f294d</ns18:viewUUID>
                                                        <ns18:binding />
                                                        <ns18:contentBoxContrib>
                                                            <ns18:id>322236e8-1bf9-4aa1-82b0-82d6509c2e13</ns18:id>
                                                            <ns18:contentBoxId>ContentBox1</ns18:contentBoxId>
                                                            <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                <ns18:id>994f3270-5cbb-4295-8266-8ecf3bcadd8a</ns18:id>
                                                                <ns18:layoutItemId>input11</ns18:layoutItemId>
                                                                <ns18:configData>
                                                                    <ns18:id>199ad100-60a7-4e85-8d9a-0fa9186d3faa</ns18:id>
                                                                    <ns18:optionName>@label</ns18:optionName>
                                                                    <ns18:value>Input 1</ns18:value>
                                                                </ns18:configData>
                                                                <ns18:configData>
                                                                    <ns18:id>b4b64602-4c25-49c1-8978-f203b7376c04</ns18:id>
                                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                                    <ns18:value />
                                                                </ns18:configData>
                                                                <ns18:viewUUID>64.d109fc11-3729-396e-bf94-d748d1967596</ns18:viewUUID>
                                                                <ns18:binding>tw.local.input1</ns18:binding>
                                                            </ns18:contributions>
                                                            <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                <ns18:id>ccc75b05-5ffd-4c48-8349-7020aca280e0</ns18:id>
                                                                <ns18:layoutItemId>output11</ns18:layoutItemId>
                                                                <ns18:configData>
                                                                    <ns18:id>66708c93-4bc4-4102-8eca-14860f2bb454</ns18:id>
                                                                    <ns18:optionName>@label</ns18:optionName>
                                                                    <ns18:value>Output 1</ns18:value>
                                                                </ns18:configData>
                                                                <ns18:configData>
                                                                    <ns18:id>4b571255-5dbd-4c33-8b9c-afdabe38e630</ns18:id>
                                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                                    <ns18:value />
                                                                </ns18:configData>
                                                                <ns18:viewUUID>64.d109fc11-3729-396e-bf94-d748d1967596</ns18:viewUUID>
                                                                <ns18:binding>tw.local.output1</ns18:binding>
                                                            </ns18:contributions>
                                                            <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                <ns18:id>de20f28f-80f2-47ce-8e8a-51c0573bc6d2</ns18:id>
                                                                <ns18:layoutItemId>LayoutItem2</ns18:layoutItemId>
                                                                <ns18:configData>
                                                                    <ns18:id>1bf22bc9-85dd-4d60-8fb6-0683daccb901</ns18:id>
                                                                    <ns18:optionName>@label</ns18:optionName>
                                                                    <ns18:value>Variable 1</ns18:value>
                                                                </ns18:configData>
                                                                <ns18:configData>
                                                                    <ns18:id>5daa18b1-b4c2-4e67-8f0f-27ac796419e5</ns18:id>
                                                                    <ns18:optionName>showBorder</ns18:optionName>
                                                                    <ns18:value>true</ns18:value>
                                                                </ns18:configData>
                                                                <ns18:viewUUID>64.e44c5617-c1ca-31f8-a810-ce1138fb1c99</ns18:viewUUID>
                                                                <ns18:contentBoxContrib>
                                                                    <ns18:id>8c59ff17-e61e-4a51-8143-cd762e4dd432</ns18:id>
                                                                    <ns18:contentBoxId>LAYOUT_ITEM_ID_1</ns18:contentBoxId>
                                                                    <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                        <ns18:id>a241730f-ec99-4183-8456-bde28505b01b</ns18:id>
                                                                        <ns18:layoutItemId>Untitled11</ns18:layoutItemId>
                                                                        <ns18:configData>
                                                                            <ns18:id>52c92b67-e294-486a-85ce-9a0bce288b32</ns18:id>
                                                                            <ns18:optionName>@label</ns18:optionName>
                                                                            <ns18:value>Untitled 1</ns18:value>
                                                                        </ns18:configData>
                                                                        <ns18:configData>
                                                                            <ns18:id>7e6b500b-d715-46ce-8bfc-9c653cefaae0</ns18:id>
                                                                            <ns18:optionName>@helpText</ns18:optionName>
                                                                            <ns18:value />
                                                                        </ns18:configData>
                                                                        <ns18:viewUUID>64.d109fc11-3729-396e-bf94-d748d1967596</ns18:viewUUID>
                                                                        <ns18:binding>tw.local.variable1.Untitled1</ns18:binding>
                                                                    </ns18:contributions>
                                                                    <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                        <ns18:id>aafd04fb-6f07-4838-8052-61cbb9bd0fed</ns18:id>
                                                                        <ns18:layoutItemId>Untitled21</ns18:layoutItemId>
                                                                        <ns18:configData>
                                                                            <ns18:id>389da10e-a1c8-4933-854c-b5b842f0c14c</ns18:id>
                                                                            <ns18:optionName>@label</ns18:optionName>
                                                                            <ns18:value>Untitled 2</ns18:value>
                                                                        </ns18:configData>
                                                                        <ns18:viewUUID>64.327b1224-e7d9-3d9c-ad44-392f5702827c</ns18:viewUUID>
                                                                        <ns18:binding>tw.local.variable1.Untitled2[]</ns18:binding>
                                                                        <ns18:contentBoxContrib>
                                                                            <ns18:id>92bc67d1-a4f3-4d7e-8f46-711200d22a36</ns18:id>
                                                                            <ns18:contentBoxId>ContentBox1</ns18:contentBoxId>
                                                                            <ns18:contributions xsi:type="ns18:ViewRef" version="8570">
                                                                                <ns18:id>dc984dc3-3323-4c46-8595-65f4529ff6e8</ns18:id>
                                                                                <ns18:layoutItemId>Untitled12</ns18:layoutItemId>
                                                                                <ns18:configData>
                                                                                    <ns18:id>03203288-c17b-4b21-8517-7f4e4d4a69d2</ns18:id>
                                                                                    <ns18:optionName>@label</ns18:optionName>
                                                                                    <ns18:value>Untitled 1 2</ns18:value>
                                                                                </ns18:configData>
                                                                                <ns18:configData>
                                                                                    <ns18:id>f1478fb2-7441-4d90-8340-7aec8934a41c</ns18:id>
                                                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                                                    <ns18:value />
                                                                                </ns18:configData>
                                                                                <ns18:viewUUID>64.d109fc11-3729-396e-bf94-d748d1967596</ns18:viewUUID>
                                                                                <ns18:binding>tw.local.variable1.Untitled2.currentItem.Untitled1</ns18:binding>
                                                                            </ns18:contributions>
                                                                        </ns18:contentBoxContrib>
                                                                    </ns18:contributions>
                                                                </ns18:contentBoxContrib>
                                                            </ns18:contributions>
                                                        </ns18:contentBoxContrib>
                                                    </ns18:contributions>
                                                </ns18:contentBoxContrib>
                                            </ns18:layoutItem>
                                        </ns18:layout>
                                    </ns18:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns17:callActivity calledElement="1.554dd7d5-ceb8-4548-91e5-788940b70e0d" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.c461f373-e3eb-4bf6-b13f-4384f716944f" name="Server Side Init Data" id="2025.d959c04a-f61a-4508-b985-436bbc646544">
                                <ns17:documentation textFormat="text/plain">&lt;br _moz_editor_bogus_node="TRUE" /&gt;</ns17:documentation>
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="372" y="103" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d</ns17:incoming>
                                <ns17:incoming>2027.20bd83f9-8381-4891-bc6b-fb468c15036a</ns17:incoming>
                                <ns17:outgoing>2027.7bde0859-557f-4d5a-8d2e-7e66d8f2d0b8</ns17:outgoing>
                                <ns17:outgoing>2027.c461f373-e3eb-4bf6-b13f-4384f716944f</ns17:outgoing>
                                <ns17:dataInputAssociation>
                                    <ns17:targetRef>2055.79a0c5f3-08ff-45b1-a99d-3b828a60339d</ns17:targetRef>
                                    <ns17:assignment>
                                        <ns17:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns17:from>
                                    </ns17:assignment>
                                </ns17:dataInputAssociation>
                                <ns17:dataInputAssociation>
                                    <ns17:targetRef>2055.0344478b-933e-4e7e-8122-349aaeb03f2d</ns17:targetRef>
                                    <ns17:assignment>
                                        <ns17:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedInstanceId</ns17:from>
                                    </ns17:assignment>
                                </ns17:dataInputAssociation>
                                <ns17:dataOutputAssociation>
                                    <ns17:sourceRef>2055.9918e74b-8220-44bd-a6df-213ae1f96c8f</ns17:sourceRef>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceName</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:dataOutputAssociation>
                                    <ns17:sourceRef>2055.99c78e1f-41aa-4f5b-89e4-9f0449dbb478</ns17:sourceRef>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935">tw.local.instanceSummary</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:dataOutputAssociation>
                                    <ns17:sourceRef>2055.8e52cfcb-db25-4bc4-a016-c8103a1cae4b</ns17:sourceRef>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.currentDate</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:dataOutputAssociation>
                                    <ns17:sourceRef>2055.83bae48e-f188-476a-bd4f-eee8360c042d</ns17:sourceRef>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.canViewDiagram</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:dataOutputAssociation>
                                    <ns17:sourceRef>2055.b2e6d33b-136e-4c8e-b88f-abe718a89062</ns17:sourceRef>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.helperScriptURL</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                            </ns17:callActivity>
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="activitiesTextFilter" id="2056.57f86fa0-67b6-4726-ad97-162161f2e6fe" />
                            <ns17:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="breadcrumbs" id="2056.e4585b41-c23e-48cb-b206-da672aee5880" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="buttonClicked_GanttView" id="2056.aaa77807-46d9-4f14-ae1e-c593aa435a1f" />
                            <ns17:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="currentDate" id="2056.ff69c386-cd9e-4193-9d5f-213d1932b6cc" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="follow" id="2056.cf7a7d8f-977e-4312-b046-8903b4e823c4" />
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceName" id="2056.9b359655-1b37-4b87-bf8d-19e8df143c01" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hideDocumentSection" id="2056.21baa20b-36e6-420a-99b6-dabc2d9c6f0e" />
                            <ns17:dataObject itemSubjectRef="itm.12.9a9d479f-686b-484b-80a6-30b52aa4e935" isCollection="false" name="instanceSummary" id="2056.bd6236b0-e04b-4067-8af1-40116ac81c7a" />
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedActivitiesCategory" id="2056.8ae618a2-e789-4937-a1da-c9875bbf9215" />
                            <ns17:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="activitiesCategoriesSelectionList" id="2056.ef26f502-5ace-4704-969c-e4764e83804f" />
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedTasksCategory" id="2056.9cfca0eb-67e6-48b6-a590-181fd11286bd" />
                            <ns17:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="tasksCategoriesSelectionList" id="2056.cddcdbfa-d2f5-4de9-a134-d089b25dbd5f" />
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d" name="Client Side Init Data" id="2025.1a666951-4594-47bb-9ad9-b801c8a71ac4">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="227" y="103" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6</ns17:incoming>
                                <ns17:outgoing>2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d</ns17:outgoing>
                                <ns17:script>tw.local.selectedInstanceId = tw.system.processInstance.id;&#xD;
&#xD;
var breadcrumbText = null;&#xD;
if (window.location.href.indexOf('breadcrumbText=') != -1)  {&#xD;
	breadcrumbText = window.location.href.substring(window.location.href.indexOf('breadcrumbText=')+15);&#xD;
	if (breadcrumbText.indexOf('&amp;') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('&amp;'));&#xD;
	}&#xD;
	if (breadcrumbText.indexOf('#') != -1) {&#xD;
		breadcrumbText = breadcrumbText.substring(0,breadcrumbText.indexOf('#'));&#xD;
	}&#xD;
}
                                &#xD;
var _debug = function(){};&#xD;
// enable following line to log into browser console&#xD;
//var _debug = console.log;&#xD;
&#xD;
try {&#xD;
	// check for breadcrumb&#xD;
	tw.local.breadcrumbs = [];&#xD;
	if (breadcrumbText) {&#xD;
		tw.local.breadcrumbs.push({&#xD;
			name: decodeURI(breadcrumbText),&#xD;
			value: '{"value":"back","navigationDestination":"BACK"}'&#xD;
		});&#xD;
	}&#xD;
    &#xD;
} catch (err) {&#xD;
	console.log("Error within Instance Details initialization: "+err);&#xD;
}&#xD;
&#xD;
&#xD;
</ns17:script>
                            </ns17:scriptTask>
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedInstanceId" id="2056.934f9158-6cd8-4223-bff8-3a92815540c7" />
                            <ns17:exclusiveGateway default="2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6" gatewayDirection="Unspecified" name="Instance present?" id="2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="131" y="196" width="32" height="32" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.41cb3ad8-dd34-4f60-b919-34abed2fef06</ns17:incoming>
                                <ns17:outgoing>2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6</ns17:outgoing>
                                <ns17:outgoing>2027.4d377842-7a0e-4967-948d-c0a66856f673</ns17:outgoing>
                            </ns17:exclusiveGateway>
                            <ns3:formTask isHeritageCoach="false" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Show Error" id="2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="224" y="292" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.4d377842-7a0e-4967-948d-c0a66856f673</ns17:incoming>
                                <ns17:outgoing>2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420</ns17:outgoing>
                                <ns3:formDefinition>
                                    <ns18:coachDefinition>
                                        <ns18:layout>
                                            <ns18:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns18:ViewRef" version="8550">
                                                <ns18:id>78b2ab0b-7b94-46db-8737-8ce857c45be3</ns18:id>
                                                <ns18:layoutItemId>Output_Text1</ns18:layoutItemId>
                                                <ns18:configData>
                                                    <ns18:id>1fa476df-8872-4212-8eb6-78a4f4ee0c5f</ns18:id>
                                                    <ns18:optionName>@label</ns18:optionName>
                                                    <ns18:value />
                                                    <ns18:valueType>dynamic</ns18:valueType>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>3b1ca332-b7d0-4ac8-81e7-589d693e342f</ns18:id>
                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                    <ns18:value />
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>ebb4f8b6-cc75-4aaa-830c-7cf84c9e566e</ns18:id>
                                                    <ns18:optionName>@labelVisibility</ns18:optionName>
                                                    <ns18:value>Show</ns18:value>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>6c1c2e85-08d7-47d3-89a6-94fe6b5d5c3d</ns18:id>
                                                    <ns18:optionName>@htmlOverrides</ns18:optionName>
                                                    <ns18:value />
                                                </ns18:configData>
                                                <ns18:viewUUID>64.fc2d6e5b-da91-4e0a-b874-3ec8ace34c82</ns18:viewUUID>
                                                <ns18:binding>tw.resource.Dashboards.defaultInstanceDetails.NoInstance</ns18:binding>
                                            </ns18:layoutItem>
                                            <ns18:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns18:ViewRef" version="8550">
                                                <ns18:id>272ece89-7eb6-4a83-8e48-e6e22e132ae9</ns18:id>
                                                <ns18:layoutItemId>okButton</ns18:layoutItemId>
                                                <ns18:configData>
                                                    <ns18:id>f6970509-b656-457c-86e2-91e5101ed142</ns18:id>
                                                    <ns18:optionName>@label</ns18:optionName>
                                                    <ns18:value>OK</ns18:value>
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>e311d86e-e717-4ad1-8042-9806d00d5420</ns18:id>
                                                    <ns18:optionName>@helpText</ns18:optionName>
                                                    <ns18:value />
                                                </ns18:configData>
                                                <ns18:configData>
                                                    <ns18:id>649a0446-7ab5-4bef-850a-d509921c432e</ns18:id>
                                                    <ns18:optionName>@labelVisibility</ns18:optionName>
                                                    <ns18:value>Show</ns18:value>
                                                </ns18:configData>
                                                <ns18:viewUUID>64.36f46ec6-616b-4e38-86aa-fba20ec6f9b4</ns18:viewUUID>
                                            </ns18:layoutItem>
                                        </ns18:layout>
                                    </ns18:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns17:sequenceFlow sourceRef="787275bb-2992-4595-a9d6-a94de1a2988e" targetRef="2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0" name="To Instance present?" id="2027.41cb3ad8-dd34-4f60-b919-34abed2fef06">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:endEvent name="End" id="2025.d86aee95-db42-478c-b40f-04d67e91cde7">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="403" y="316" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns17:extensionElements>
                                <ns17:incoming>2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420</ns17:incoming>
                            </ns17:endEvent>
                            <ns17:sequenceFlow sourceRef="2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a" targetRef="2025.d86aee95-db42-478c-b40f-04d67e91cde7" name="To End" id="2027.30c65b14-7fcf-48eb-9b4e-ad2633e2d420">
                                <ns17:extensionElements>
                                    <ns3:coachEventBinding id="400bdb3c-89a6-4eb2-91d7-a2196b92fd64">
                                        <ns3:coachEventPath>okButton</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.1a666951-4594-47bb-9ad9-b801c8a71ac4" targetRef="2025.d959c04a-f61a-4508-b985-436bbc646544" name="To Server Side Init Data" id="2027.0fb3ab3a-5d7a-4099-89ce-983c20ac3c0d">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0" targetRef="2025.d3b5a0ae-8411-417b-a2fc-00eaf879d36a" name="No" id="2027.4d377842-7a0e-4967-948d-c0a66856f673">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                                <ns17:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression">tw.system.processInstance == null</ns17:conditionExpression>
                            </ns17:sequenceFlow>
                            <ns17:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.60a32949-9558-42a0-8769-85badc83d43b" name="Get Process Variables" id="2025.0189770c-7f8d-445b-ac63-eaa0944191b8">
                                <ns17:extensionElements>
                                    <ns3:mode>RefreshVariables</ns3:mode>
                                    <ns13:nodeVisualInfo x="555" y="293" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e</ns17:incoming>
                                <ns17:outgoing>2027.60a32949-9558-42a0-8769-85badc83d43b</ns17:outgoing>
                            </ns17:callActivity>
                            <ns17:callActivity isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.850927ae-1953-4e74-a251-28ee606d108b" name="Update Process Variables" id="2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c">
                                <ns17:extensionElements>
                                    <ns3:mode>SaveBPDVariables</ns3:mode>
                                    <ns13:nodeVisualInfo x="956" y="406" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.94a513af-a416-4e2c-888b-adf35e8a500b</ns17:incoming>
                                <ns17:outgoing>2027.850927ae-1953-4e74-a251-28ee606d108b</ns17:outgoing>
                            </ns17:callActivity>
                            <ns17:intermediateThrowEvent name="Stay on page" id="2025.2ad584dc-fda8-4b3d-869a-03785828d7e0">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1077" y="321" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns17:extensionElements>
                                <ns17:incoming>2027.08c48f64-a737-415c-8dad-2db02f48d88c</ns17:incoming>
                                <ns17:incoming>2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db</ns17:incoming>
                                <ns17:incoming>2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454</ns17:incoming>
                                <ns17:incoming>2027.7ffead20-3b6e-4c13-b056-11c095659e79</ns17:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns17:intermediateThrowEvent>
                            <ns17:endEvent name="End" id="2025.961fc12e-4a5f-4257-90ba-899103878414">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1012" y="127" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>ToOtherDashboard</ns3:targetType>
                                        <ns3:targetURL>tw.local.navigationURL</ns3:targetURL>
                                    </ns3:navigationInstructions>
                                </ns17:extensionElements>
                                <ns17:incoming>2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4</ns17:incoming>
                            </ns17:endEvent>
                            <ns17:boundaryEvent cancelActivity="true" attachedToRef="2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c" parallelMultiple="false" name="Error" id="2025.e4f7632c-ff14-4873-bf10-415a01430c30">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1039" y="411" width="24" height="24" />
                                </ns17:extensionElements>
                                <ns17:outgoing>2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454</ns17:outgoing>
                                <ns17:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.5b01c2be-0aca-4074-8fee-c3a60184530b" />
                                <ns17:dataOutputAssociation>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.failedSaveMessage</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:errorEventDefinition>
                                    <ns17:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>false</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns17:extensionElements>
                                </ns17:errorEventDefinition>
                            </ns17:boundaryEvent>
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="automaticRefreshTrigger" id="2056.ac0ba0af-9035-4a59-a657-f5eb4cbc9947" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="manualRefreshTrigger" id="2056.8c1dc92f-367e-4314-8c3b-89252c6b6805" />
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="failedSaveMessage" id="2056.3d456ca3-87ce-4bc3-a503-a86a19aa63d9" />
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813" name="Validation" id="2025.5aba8790-6f16-4c8d-9815-3b107b802637">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="815" y="298" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e</ns17:incoming>
                                <ns17:outgoing>2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813</ns17:outgoing>
                                <ns17:script>//  Check for validation errors in the instance data. For examle:&#xD;
// if (tw.local.name == "" ) {&#xD;
//    tw.system.coachValidation.addValidationError("tw.local.name", "Name must be specified");&#xD;
// }</ns17:script>
                            </ns17:scriptTask>
                            <ns17:exclusiveGateway default="2027.94a513af-a416-4e2c-888b-adf35e8a500b" gatewayDirection="Unspecified" name="Validation Error?" id="2025.2fec7021-4671-4209-b11a-50d140e6c717">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="955" y="317" width="32" height="32" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813</ns17:incoming>
                                <ns17:outgoing>2027.08c48f64-a737-415c-8dad-2db02f48d88c</ns17:outgoing>
                                <ns17:outgoing>2027.94a513af-a416-4e2c-888b-adf35e8a500b</ns17:outgoing>
                            </ns17:exclusiveGateway>
                            <ns17:sequenceFlow sourceRef="2025.5aba8790-6f16-4c8d-9815-3b107b802637" targetRef="2025.2fec7021-4671-4209-b11a-50d140e6c717" name="To Validation Error" id="2027.76bd8dd5-0eba-49a1-80c8-22dd35fb8813">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.2fec7021-4671-4209-b11a-50d140e6c717" targetRef="2025.2ad584dc-fda8-4b3d-869a-03785828d7e0" name="Yes" id="2027.08c48f64-a737-415c-8dad-2db02f48d88c">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                                <ns17:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression">tw.system.coachValidation.validationErrors.length != 0</ns17:conditionExpression>
                            </ns17:sequenceFlow>
                            <ns17:intermediateThrowEvent name="Stay on page" id="2025.248669b3-62a4-428d-972b-049cb489bfb1">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="564" y="519" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns17:extensionElements>
                                <ns17:incoming>2027.954fbeb4-0a94-4ead-b909-798d9236b711</ns17:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns17:intermediateThrowEvent>
                            <ns17:sequenceFlow sourceRef="2025.0189770c-7f8d-445b-ac63-eaa0944191b8" targetRef="2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7" name="To Stay on page" id="2027.60a32949-9558-42a0-8769-85badc83d43b">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:dataObject itemSubjectRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54" isCollection="false" name="boSaveFailedError" id="2056.d51f9fdb-a15a-4ec3-afb6-b8a4c56e89db" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="canViewDiagram" id="2056.cb5bdf2f-b405-4d10-bf5b-a100445b9ac6" />
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="navigationURL" id="2056.3155a4c4-7a66-473d-91c0-d0f0e258f73c" />
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="unsavedLocalChanges" id="2056.48e50ca4-be3a-4ba4-bf00-a241ea55c8f3">
                                <ns17:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. The variable value is set to true when local changes are detected.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db" name="Merge Changes" id="2025.e2dd5067-751f-4c56-aad9-e0e060b076fc">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1065" y="213" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc</ns17:incoming>
                                <ns17:outgoing>2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db</ns17:outgoing>
                                <ns17:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The merge the server changes into the local variables based on the merge policy picked by the user.&#xD;
&#xD;
	// Determine what type of merge the user requested&#xD;
	var keepConflictingLocalChanges = false;&#xD;
	if(tw.local.dataSectionBoundaryEventType === "KEEP_LOCAL")&#xD;
		keepConflictingLocalChanges = true;&#xD;
		&#xD;
	merge(tw.local, keepConflictingLocalChanges); // Reset variables&#xD;
	          &#xD;
}); </ns17:script>
                            </ns17:scriptTask>
                            <ns17:sequenceFlow sourceRef="2025.1f3d7135-8232-4eed-957e-acf8a8e96099" targetRef="2025.d959c04a-f61a-4508-b985-436bbc646544" name="Automatic Refresh" id="2027.20bd83f9-8381-4891-bc6b-fb468c15036a">
                                <ns17:extensionElements>
                                    <ns3:coachEventBinding id="4da2ca11-edbf-4796-a022-be245707e685">
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller1</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="originalInput" id="2056.c8f3c91a-33c2-49e6-b12d-9e985c38b289">
                                <ns17:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable saves (stores) the original values of the variables on the client. The original values are used to determine how local or remote changes affected the variables on the client.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="incomingChanges" id="2056.63bdc052-e874-4983-90ef-a3786f8baa54">
                                <ns17:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. This variable contains the updated variable values from the server.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="localChanges" id="2056.08c90b93-1a62-4953-9126-8abef8a1aed8">
                                <ns17:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. It contains the updated variable values from the coach.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.954fbeb4-0a94-4ead-b909-798d9236b711" name="Reset Pending Change Data" id="2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="555" y="406" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.60a32949-9558-42a0-8769-85badc83d43b</ns17:incoming>
                                <ns17:incoming>2027.850927ae-1953-4e74-a251-28ee606d108b</ns17:incoming>
                                <ns17:outgoing>2027.954fbeb4-0a94-4ead-b909-798d9236b711</ns17:outgoing>
                                <ns17:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// The local variables have been refreshed with the latest changes from the server,&#xD;
	// or the instance UI has been saved.  There are no pending changes.  Reset merge data.&#xD;
		&#xD;
	resetDataSyncronizationVariables(tw.local); // Reset variables&#xD;
	          &#xD;
}); &#xD;
&#xD;
&#xD;
</ns17:script>
                            </ns17:scriptTask>
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="dataSectionBoundaryEventType" id="2056.035f5618-b706-4a78-9494-f3d0561685b8">
                                <ns17:documentation textFormat="text/plain">This variable indicates the type of boundary event triggered by the Data Section coach view.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:exclusiveGateway default="2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc" gatewayDirection="Unspecified" name="Data Section Action?" id="2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="709" y="235" width="32" height="32" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9</ns17:incoming>
                                <ns17:outgoing>2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e</ns17:outgoing>
                                <ns17:outgoing>2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e</ns17:outgoing>
                                <ns17:outgoing>2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc</ns17:outgoing>
                            </ns17:exclusiveGateway>
                            <ns17:sequenceFlow sourceRef="2025.1f3d7135-8232-4eed-957e-acf8a8e96099" targetRef="2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d" name="To Exclusive Gateway" id="2027.fe2d477d-6e0b-43e2-b393-3319f82e52d9">
                                <ns17:extensionElements>
                                    <ns3:coachEventBinding id="f9dd1e11-cef4-448c-ac9d-9a168c357ea2">
                                        <ns3:coachEventPath>CustomDataSection</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d" targetRef="2025.0189770c-7f8d-445b-ac63-eaa0944191b8" name="Discard Local Changes" id="2027.ca01b9e2-c80b-47c2-85cf-35eb1b988c5e">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                                <ns17:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression">tw.local.dataSectionBoundaryEventType == "DISCARD"</ns17:conditionExpression>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c" targetRef="2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7" name="To Reset Pending Change Data" id="2027.850927ae-1953-4e74-a251-28ee606d108b">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.95b5452b-5a49-4840-8ab7-40086cd2ccc7" targetRef="2025.248669b3-62a4-428d-972b-049cb489bfb1" name="To Stay on page" id="2027.954fbeb4-0a94-4ead-b909-798d9236b711">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d" targetRef="2025.5aba8790-6f16-4c8d-9815-3b107b802637" name="Save Data" id="2027.3754b20d-7a05-4845-b1ba-f4e7f3145e0e">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                                <ns17:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression">tw.local.dataSectionBoundaryEventType == "SAVE"</ns17:conditionExpression>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.2fec7021-4671-4209-b11a-50d140e6c717" targetRef="2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c" name="No" id="2027.94a513af-a416-4e2c-888b-adf35e8a500b">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                                <ns17:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression">  </ns17:conditionExpression>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.3f357ffd-abe3-43e7-88ef-934b2ab9db1d" targetRef="2025.e2dd5067-751f-4c56-aad9-e0e060b076fc" name="Merge Changes" id="2027.5e75c882-5b38-41f7-9c4f-d911f91c9acc">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.e2dd5067-751f-4c56-aad9-e0e060b076fc" targetRef="2025.2ad584dc-fda8-4b3d-869a-03785828d7e0" name="To Stay on page" id="2027.7fd7abfc-a0c5-4e59-9d9f-99541fa1a8db">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.e4f7632c-ff14-4873-bf10-415a01430c30" targetRef="2025.2ad584dc-fda8-4b3d-869a-03785828d7e0" name="To Stay on page" id="2027.3b34a1d4-4cea-4292-a0c6-3ec5485c9454">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:subProcess triggeredByEvent="true" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Data Change" id="2025.4577a5b8-213f-4ae9-9bf0-ad2d5fd57fd5">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="51" y="463" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="2025.684a651a-f1a0-4c85-bbf6-ab061b9147e0">
                                    <ns17:extensionElements>
                                        <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    </ns17:extensionElements>
                                    <ns17:outgoing>2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8</ns17:outgoing>
                                    <ns3:dataNotificationEventDefinition>
                                        <ns17:extensionElements>
                                            <ns3:coachEventTriggers enable="true">
                                                <ns3:coachEventTriggerBinding id="75038bdc-ca3b-4728-b3a0-8e033b025835">
                                                    <ns3:coachEventPath>Default_Instance_Details_Template1/Service_Controller2</ns3:coachEventPath>
                                                    <ns3:coachId>2025.1f3d7135-8232-4eed-957e-acf8a8e96099</ns3:coachId>
                                                </ns3:coachEventTriggerBinding>
                                            </ns3:coachEventTriggers>
                                        </ns17:extensionElements>
                                    </ns3:dataNotificationEventDefinition>
                                </ns17:startEvent>
                                <ns17:intermediateThrowEvent name="Stay on page" id="2025.4e4757e2-60c3-491c-b599-f636ba69864a">
                                    <ns17:extensionElements>
                                        <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" />
                                        <ns3:navigationInstructions>
                                            <ns3:targetType>Default</ns3:targetType>
                                        </ns3:navigationInstructions>
                                    </ns17:extensionElements>
                                    <ns17:incoming>2027.a14189d3-4045-4db1-b48b-d476449e23f5</ns17:incoming>
                                    <ns3:stayOnPageEventDefinition />
                                </ns17:intermediateThrowEvent>
                                <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.a14189d3-4045-4db1-b48b-d476449e23f5" name="Collect change information" id="2025.2bc185fb-7595-45b4-8791-6e67e12ab207">
                                    <ns17:extensionElements>
                                        <ns13:nodeVisualInfo x="345" y="176" width="95" height="70" />
                                    </ns17:extensionElements>
                                    <ns17:incoming>2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8</ns17:incoming>
                                    <ns17:outgoing>2027.a14189d3-4045-4db1-b48b-d476449e23f5</ns17:outgoing>
                                    <ns17:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
	&#xD;
	// Calculate if there are any relevant incoming changes&#xD;
&#xD;
	// There are unsaved changes .  This can be detected by calling tw.system.coachUtils.hasLocallyChangedInputVars().  Here, however, this variable is&#xD;
	// set by the Data Section coach view when it detects that a child view's data binding has changed.&#xD;
	if(tw.local.unsavedLocalChanges ===  true || tw.system.coachUtils.hasLocallyChangedInputVars()){&#xD;
		&#xD;
		if(!tw.local.unsavedLocalChanges)&#xD;
			tw.local.unsavedLocalChanges = true;&#xD;
			&#xD;
		// Call the helper function to update the incoming and local changes.&#xD;
		checkIncomingChanges(tw.local, tw.system.coachUtils.getLocallyChangedVars(),  tw.system.dataChangeUtils.getIncomingVars())		          &#xD;
		&#xD;
	} else if(tw.system.coachUtils.getLocallyChangedVars().length &gt; 0) { // No pending changes to be merged, or local changes&#xD;
	&#xD;
		// Apply incoming changes to the local variables.&#xD;
		tw.system.dataChangeUtils.applyAllIncomingVars();&#xD;
		tw.local.incomingChangesMerged = true;  // Signal that changes were applied automatically.  The Data Section can listen and pop up a notification.&#xD;
	 &#xD;
	}&#xD;
&#xD;
}); </ns17:script>
                                </ns17:scriptTask>
                                <ns17:sequenceFlow sourceRef="2025.684a651a-f1a0-4c85-bbf6-ab061b9147e0" targetRef="2025.2bc185fb-7595-45b4-8791-6e67e12ab207" name="To Collect change information" id="2027.0ff6a6c0-fa52-4fd0-af0e-0c3384da69e8">
                                    <ns17:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>false</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns17:extensionElements>
                                </ns17:sequenceFlow>
                                <ns17:sequenceFlow sourceRef="2025.2bc185fb-7595-45b4-8791-6e67e12ab207" targetRef="2025.4e4757e2-60c3-491c-b599-f636ba69864a" name="To Stay on page" id="2027.a14189d3-4045-4db1-b48b-d476449e23f5">
                                    <ns17:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>false</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns17:extensionElements>
                                </ns17:sequenceFlow>
                            </ns17:subProcess>
                            <ns17:sequenceFlow sourceRef="2025.8bb0423c-8a36-4f7c-8c57-b4a0c61218e0" targetRef="2025.1a666951-4594-47bb-9ad9-b801c8a71ac4" name="Yes" id="2027.fb44339a-0c2c-4d70-9a0e-a57cd210c0d6">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.3be85c06-e292-42a0-bc27-d88501fcf12f" name="Init Data Change Support" id="2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="523" y="103" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.c461f373-e3eb-4bf6-b13f-4384f716944f</ns17:incoming>
                                <ns17:outgoing>2027.3be85c06-e292-42a0-bc27-d88501fcf12f</ns17:outgoing>
                                <ns17:script>require([tw.local.helperScriptURL], function() { // Load the helper functions&#xD;
&#xD;
	// Initialize variables that support data change syncrhonization.  &#xD;
	&#xD;
	// This can only be done once the URL for the helper functions is returned by the &#xD;
	// Server Sice Init Data.  Since this is can also be invoked as part of auto refresh the initialization&#xD;
	// should only happen if the variables are undefined.&#xD;
	&#xD;
	if(tw.local.originalInput == undefined &amp;&amp; tw.local.localChanges == undefined &amp;&amp; tw.local.incomingChanges == undefined){&#xD;
		initializeDataSyncronizationVariables(tw.local); // Intialize variables&#xD;
	}&#xD;
	&#xD;
}); &#xD;
</ns17:script>
                            </ns17:scriptTask>
                            <ns17:sequenceFlow sourceRef="2025.d959c04a-f61a-4508-b985-436bbc646544" targetRef="2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575" name="To Init Data Change Support" id="2027.c461f373-e3eb-4bf6-b13f-4384f716944f">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.8f0a5a3f-9720-4b16-9bdd-95feb0a95575" targetRef="2025.1f3d7135-8232-4eed-957e-acf8a8e96099" name="To View Instance Details" id="2027.3be85c06-e292-42a0-bc27-d88501fcf12f">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="helperScriptURL" id="2056.877ac262-2997-47b7-9d97-fff1da1e3729">
                                <ns17:documentation textFormat="text/plain">This variable contains the URL or the managed asset that provides data synchronization functions used in this service.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="incomingChangesMerged" id="2056.4bfc2751-322d-4afc-bfa6-16c509c086df">
                                <ns17:documentation textFormat="text/plain">This variable is bound to the Data Section coach view. Indicates that incoming changes were automatically merged into the local data.</ns17:documentation>
                            </ns17:dataObject>
                            <ns17:sequenceFlow sourceRef="2025.1f3d7135-8232-4eed-957e-acf8a8e96099" targetRef="2025.961fc12e-4a5f-4257-90ba-899103878414" name="To End" id="2027.dae0a1ea-6893-4718-bf2d-f5bcd2be37a4">
                                <ns17:extensionElements>
                                    <ns3:coachEventBinding id="4a4baad0-69c3-4fba-815d-e5c0af8d6e55">
                                        <ns3:coachEventPath>Default_Instance_Details_Template1/Navigation_Controller1</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:boundaryEvent cancelActivity="true" attachedToRef="2025.8fea279e-84f0-4cbd-8d6e-b03d491aa27c" parallelMultiple="false" name="Error" id="2025.ff54e731-6efb-4a6e-ab4c-0b98d145d174">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1039" y="447" width="24" height="24" />
                                </ns17:extensionElements>
                                <ns17:outgoing>2027.3e5701d4-bd39-4fb8-8645-975d20fd4320</ns17:outgoing>
                                <ns17:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.00562359-2779-4a98-8611-3a6410430e0b" />
                                <ns17:dataOutputAssociation>
                                    <ns17:assignment>
                                        <ns17:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns17:tFormalExpression" evaluatesToTypeRef="itm.12.d8fa7561-8636-40a9-bd70-f45128bb7e54">tw.local.boSaveFailedError</ns17:to>
                                    </ns17:assignment>
                                </ns17:dataOutputAssociation>
                                <ns17:errorEventDefinition>
                                    <ns17:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>false</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns17:extensionElements>
                                </ns17:errorEventDefinition>
                            </ns17:boundaryEvent>
                            <ns17:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.7ffead20-3b6e-4c13-b056-11c095659e79" name="Populate BO Save Error" id="2025.a832a826-91d4-4444-b41b-82371cfcac29">
                                <ns17:extensionElements>
                                    <ns13:nodeVisualInfo x="1162" y="424" width="95" height="70" />
                                </ns17:extensionElements>
                                <ns17:incoming>2027.3e5701d4-bd39-4fb8-8645-975d20fd4320</ns17:incoming>
                                <ns17:outgoing>2027.7ffead20-3b6e-4c13-b056-11c095659e79</ns17:outgoing>
                                <ns17:script>tw.system.coachValidation.populateFromBOSaveFailedError( tw.local.boSaveFailedError );</ns17:script>
                            </ns17:scriptTask>
                            <ns17:sequenceFlow sourceRef="2025.ff54e731-6efb-4a6e-ab4c-0b98d145d174" targetRef="2025.a832a826-91d4-4444-b41b-82371cfcac29" name="To Client-Side Script" id="2027.3e5701d4-bd39-4fb8-8645-975d20fd4320">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns17:sequenceFlow sourceRef="2025.a832a826-91d4-4444-b41b-82371cfcac29" targetRef="2025.2ad584dc-fda8-4b3d-869a-03785828d7e0" name="To Stay on page" id="2027.7ffead20-3b6e-4c13-b056-11c095659e79">
                                <ns17:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns17:extensionElements>
                            </ns17:sequenceFlow>
                            <ns3:htmlHeaderTag id="6397c037-71ff-4f71-9ce6-819ba6eaaa37">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>InstanceDetailsUI</ns3:exposedAs>
                    </ns17:extensionElements>
                    <ns17:ioSpecification ns3:readOnlyOutputs="true" ns3:readOnlyInputs="true">
                        <ns17:extensionElements>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.4b698a84-427b-4801-9c1d-18ddcc561bc6</ns3:resourceBundleGroupID>
                                    <ns3:id>69.46923c36-435d-480a-8627-cca9772b6a02</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                        </ns17:extensionElements>
                        <ns17:dataInput name="input1" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f8b1349a-fd25-4d36-852b-6ae1e00920d0" />
                        <ns17:dataInput name="output1" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.88aa503b-eaa6-4b04-8ea6-f0b9c0345628" />
                        <ns17:dataInput name="variable1" itemSubjectRef="itm.12.60da4770-d3a3-4937-840f-8fd74f8c33ce" isCollection="false" id="2055.f393f41a-02b2-4862-8e4b-67be8f1c1f47" />
                        <ns17:inputSet>
                            <ns17:dataInputRefs>2055.f8b1349a-fd25-4d36-852b-6ae1e00920d0</ns17:dataInputRefs>
                            <ns17:dataInputRefs>2055.88aa503b-eaa6-4b04-8ea6-f0b9c0345628</ns17:dataInputRefs>
                            <ns17:dataInputRefs>2055.f393f41a-02b2-4862-8e4b-67be8f1c1f47</ns17:dataInputRefs>
                        </ns17:inputSet>
                        <ns17:outputSet />
                    </ns17:ioSpecification>
                </ns17:globalUserTask>
            </ns17:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b5b8a7f4-93a0-455e-9462-309e33436318</processLinkId>
            <processId>1.236d78a5-bcbd-4c3e-a421-cfb181f40791</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.26d2fb40-de68-4dba-92ea-3f50485c7c47</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bd67c259-d07c-406e-9853-2bb6d00634c9</toProcessItemId>
            <guid>ca28ac7c-3b6c-4e02-9d75-31c006c58a95</guid>
            <versionId>71b3a96b-c26e-4140-ac80-99ca98ce26b7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.26d2fb40-de68-4dba-92ea-3f50485c7c47</fromProcessItemId>
            <toProcessItemId>2025.bd67c259-d07c-406e-9853-2bb6d00634c9</toProcessItemId>
        </link>
    </process>
</teamworks>

