<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b24f815d-f98b-4f2e-9dbe-f7601985749f" name="Get Country of Origin">
        <lastModified>1692506083938</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>true</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>43200</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>b9202e54-9de4-4751-983c-a6c9eacd13db</guid>
        <versionId>49fe4e23-b3ad-4893-9ab8-00a157ea3a7e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e48" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":33,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9b620f9a-c48f-451a-90b6-8cec11a246b1"},{"incoming":["0631acf0-c65e-470c-aa22-788e524e9ca6","bd43c23a-7c57-46f3-8bbd-5b5525ac6664"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":654,"y":33,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6150"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"25b560a3-dc3d-4946-aa57-23af1cf86af0"},{"targetRef":"2cd3dd1f-2eed-4e93-b922-59bd19e553fe","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc","sourceRef":"9b620f9a-c48f-451a-90b6-8cec11a246b1"},{"startQuantity":1,"outgoing":["c1d190f8-a27e-44d0-a033-e92af3790d62"],"incoming":["2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":10,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"MW_FC get CountryList","dataInputAssociation":[{"targetRef":"2055.955ddf56-89f5-4138-8181-a73a5a9b30a5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.b52c707c-669c-4baa-8b6b-ec91855226f1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.e4a7e1f1-3bdc-4ff5-802d-6efe08683b27","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.bbf19f93-c4ee-4e3d-8b08-4888a30e74b9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.bbd3b876-5728-4ead-8604-58f5ec5d525a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.09b34574-0109-4b1a-84d9-10d8798d8638","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2cd3dd1f-2eed-4e93-b922-59bd19e553fe","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.countryList"]}}],"sourceRef":["2055.f1dc090a-a85a-45af-8ef7-9b6143edcc4b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.2a94d974-b607-4899-8250-505833f23fe6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.37de8ad4-b945-40e8-8fca-cd428f242033"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.17614b87-c6ee-4824-852d-45896284f362"]}],"calledElement":"1.86349d0b-ac48-4269-80f1-dbd8b0f9a153"},{"targetRef":"*************-4bc2-8735-69ec5ed1afdf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6b3a"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"c1d190f8-a27e-44d0-a033-e92af3790d62","sourceRef":"2cd3dd1f-2eed-4e93-b922-59bd19e553fe"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"countryList","isCollection":true,"declaredType":"dataObject","id":"2056.289b2b2d-8a1c-40c1-8e77-6ac4ea847d8b"},{"startQuantity":1,"outgoing":["0631acf0-c65e-470c-aa22-788e524e9ca6"],"incoming":["5ac7a166-bc75-4770-8273-abc93a216d08"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":484,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Result","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3a2ee2c6-0764-4aad-82cc-0552bc8bb26d","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.listOf.DBLookup();\r\n\tfor (var i=0; i&lt;tw.local.countryList.listLength; i++) {\r\n\t\ttw.local.results[i] = new tw.object.DBLookup();\r\n\t\ttw.local.results[i].id = i;\r\n\t\ttw.local.results[i].code = tw.local.countryList[i].value+\"\";\r\n\t\ttw.local.results[i].arabicdescription = tw.local.countryList[i].name;\r\n\t\ttw.local.results[i].englishdescription = tw.local.countryList[i].name;\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"25b560a3-dc3d-4946-aa57-23af1cf86af0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"0631acf0-c65e-470c-aa22-788e524e9ca6","sourceRef":"3a2ee2c6-0764-4aad-82cc-0552bc8bb26d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.62f868c0-2cf6-46e6-aef4-fc9aee335e4f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.8eeb8e22-60a2-47ed-9238-3a4b0f686805"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.106ace05-cfe3-4937-9431-d15310be5938"},{"parallelMultiple":false,"outgoing":["6cbb6f4b-75ad-4b51-8575-6276994bfdd5"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"117b3fda-c4dd-492d-8246-6b4f231a612b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1aa0752f-0e36-4e24-8eeb-3d14aa33fe75","otherAttributes":{"eventImplId":"15672643-14f9-4f57-860d-9a0f531b8c83"}}],"attachedToRef":"2cd3dd1f-2eed-4e93-b922-59bd19e553fe","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"482d2a3a-7aad-44c3-8f3c-851b432ab10d","outputSet":{}},{"parallelMultiple":false,"outgoing":["9bd60f2f-3726-446b-82a4-acbc6d3ab351"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2fbf6dfb-e8d9-44c9-8a9b-a57b5cd2379d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"76e2f2e1-8ee7-44f0-8bcd-1447e652f766","otherAttributes":{"eventImplId":"c3d0c5fc-9205-476f-8ed3-1afe06998b74"}}],"attachedToRef":"3a2ee2c6-0764-4aad-82cc-0552bc8bb26d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":519,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"9590277b-cc08-40be-81b9-9650a3a6a83c","outputSet":{}},{"targetRef":"bd67478e-1352-4f4e-88ed-e1cd17ab5e98","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"6cbb6f4b-75ad-4b51-8575-6276994bfdd5","sourceRef":"482d2a3a-7aad-44c3-8f3c-851b432ab10d"},{"targetRef":"bd67478e-1352-4f4e-88ed-e1cd17ab5e98","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"9bd60f2f-3726-446b-82a4-acbc6d3ab351","sourceRef":"9590277b-cc08-40be-81b9-9650a3a6a83c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.7780fa34-b42f-4643-8b76-42d175191ed4"},{"outgoing":["5ac7a166-bc75-4770-8273-abc93a216d08","0d7bacc0-1ff2-48fc-8240-e34188cbc04a"],"incoming":["c1d190f8-a27e-44d0-a033-e92af3790d62"],"default":"5ac7a166-bc75-4770-8273-abc93a216d08","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":323,"y":29,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"*************-4bc2-8735-69ec5ed1afdf"},{"targetRef":"3a2ee2c6-0764-4aad-82cc-0552bc8bb26d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Set Result","declaredType":"sequenceFlow","id":"5ac7a166-bc75-4770-8273-abc93a216d08","sourceRef":"*************-4bc2-8735-69ec5ed1afdf"},{"targetRef":"bd67478e-1352-4f4e-88ed-e1cd17ab5e98","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0d7bacc0-1ff2-48fc-8240-e34188cbc04a","sourceRef":"*************-4bc2-8735-69ec5ed1afdf"},{"startQuantity":1,"outgoing":["bd43c23a-7c57-46f3-8bbd-5b5525ac6664"],"incoming":["0d7bacc0-1ff2-48fc-8240-e34188cbc04a","9bd60f2f-3726-446b-82a4-acbc6d3ab351","6cbb6f4b-75ad-4b51-8575-6276994bfdd5"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":315,"y":130,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"bd67478e-1352-4f4e-88ed-e1cd17ab5e98","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"25b560a3-dc3d-4946-aa57-23af1cf86af0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"bd43c23a-7c57-46f3-8bbd-5b5525ac6664","sourceRef":"bd67478e-1352-4f4e-88ed-e1cd17ab5e98"}],"laneSet":[{"id":"4b7fc777-5746-4fcb-9648-d231e92be152","lane":[{"flowNodeRef":["9b620f9a-c48f-451a-90b6-8cec11a246b1","25b560a3-dc3d-4946-aa57-23af1cf86af0","2cd3dd1f-2eed-4e93-b922-59bd19e553fe","3a2ee2c6-0764-4aad-82cc-0552bc8bb26d","482d2a3a-7aad-44c3-8f3c-851b432ab10d","9590277b-cc08-40be-81b9-9650a3a6a83c","*************-4bc2-8735-69ec5ed1afdf","bd67478e-1352-4f4e-88ed-e1cd17ab5e98"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":272}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"4a044a9d-2c5e-4e23-ae62-94a93cd688e4","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"cachingType":[true],"cacheLength":[43200],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Country of Origin","declaredType":"process","id":"1.b24f815d-f98b-4f2e-9dbe-f7601985749f","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.3bb64ea6-a147-4cef-8f1c-5ce8d48c655f"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.ab114e02-8d10-438d-87f4-c363a9df3b66"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.3bb64ea6-a147-4cef-8f1c-5ce8d48c655f","2055.ab114e02-8d10-438d-87f4-c363a9df3b66"]}],"dataInput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.5aa08e55-3012-45c1-b6d1-f39f47aee7ac"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5aa08e55-3012-45c1-b6d1-f39f47aee7ac</processParameterId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a178d0ca-bd7d-40de-addf-dccda837a55f</guid>
            <versionId>0e1d41eb-887d-43f0-a120-dcac5b4558d1</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3bb64ea6-a147-4cef-8f1c-5ce8d48c655f</processParameterId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a3ac5a7f-b944-4609-a8a0-b3cc0bcfd581</guid>
            <versionId>2c60159c-54b9-4e64-b544-3401936ca852</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ab114e02-8d10-438d-87f4-c363a9df3b66</processParameterId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e3fac97e-3473-49a5-8e89-7098b5578c68</guid>
            <versionId>e0d8b39d-c142-49b2-a19a-7e22e00c51ba</versionId>
        </processParameter>
        <processVariable name="countryList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.289b2b2d-8a1c-40c1-8e77-6ac4ea847d8b</processVariableId>
            <description isNull="true" />
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>371a32c3-430f-47ab-9854-86312c1d3eea</guid>
            <versionId>f9578ad1-d2ef-45ab-935b-7e9cc0723b2a</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.62f868c0-2cf6-46e6-aef4-fc9aee335e4f</processVariableId>
            <description isNull="true" />
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c1418275-804e-4c57-a5d6-983955ec8de0</guid>
            <versionId>f19acec4-1ca0-4906-9cc3-051faaf2eec8</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8eeb8e22-60a2-47ed-9238-3a4b0f686805</processVariableId>
            <description isNull="true" />
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>25b97ff6-d503-4d18-9e40-f25fcb2c7882</guid>
            <versionId>997b4782-89d3-4ab4-85bc-45f39f32b343</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.106ace05-cfe3-4937-9431-d15310be5938</processVariableId>
            <description isNull="true" />
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0e956c8c-736f-46d1-993c-64c2a86e1bfa</guid>
            <versionId>fa38e059-5bed-447f-b29a-dac03243179e</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7780fa34-b42f-4643-8b76-42d175191ed4</processVariableId>
            <description isNull="true" />
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2cb8ccac-bac8-462f-a406-7e20c12880ef</guid>
            <versionId>f37d2c76-cf81-4589-aa96-4c026ece1e40</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</processItemId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <name>Set Result</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8304b7c3-688e-4b7c-bcbc-670cb02f9aa0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6151</guid>
            <versionId>171db4a9-83ea-442e-8a08-cac487ed4554</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="484" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e46</errorHandlerItem>
                <errorHandlerItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8304b7c3-688e-4b7c-bcbc-670cb02f9aa0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.listOf.DBLookup();&#xD;
	for (var i=0; i&lt;tw.local.countryList.listLength; i++) {&#xD;
		tw.local.results[i] = new tw.object.DBLookup();&#xD;
		tw.local.results[i].id = i;&#xD;
		tw.local.results[i].code = tw.local.countryList[i].value+"";&#xD;
		tw.local.results[i].arabicdescription = tw.local.countryList[i].name;&#xD;
		tw.local.results[i].englishdescription = tw.local.countryList[i].name;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>14a5fb72-007b-416b-8f3d-060d3d3b6d93</guid>
                <versionId>deceaa90-b8c2-40c2-ab4c-70f7fd0f5fd9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.*************-4bc2-8735-69ec5ed1afdf</processItemId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.72541c69-7fd0-4854-9bbb-1ebb015058b2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f36</guid>
            <versionId>41e56070-f80b-4528-8d1a-852b2342e6f5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="323" y="29">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.72541c69-7fd0-4854-9bbb-1ebb015058b2</switchId>
                <guid>cbf89a16-891a-4e65-b903-c4b38b5fd92e</guid>
                <versionId>ebf74e03-1e5e-4799-8820-51278f26bf1b</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.9c41a55d-d399-4c77-b5fe-0c9385b6dc36</switchConditionId>
                    <switchId>3013.72541c69-7fd0-4854-9bbb-1ebb015058b2</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e47</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>1fb12964-0f9d-4bdd-804e-d6c5666caca9</guid>
                    <versionId>96248b8c-3aa0-4d41-a420-9a5ea67492d6</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</processItemId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.811c707a-24b5-4b5d-be8a-e0b21c3c7bba</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e46</guid>
            <versionId>5b3cb5ad-3e5f-4fe0-a76b-1aee00a544ee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="315" y="130">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.811c707a-24b5-4b5d-be8a-e0b21c3c7bba</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>ada31ed0-dd72-4cd6-ba14-4935691f8d57</guid>
                <versionId>f3b69320-4fbb-445e-b856-67fc66d5597b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</processItemId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <name>MW_FC get CountryList</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-614e</guid>
            <versionId>a53163f3-2e6f-40fb-b9f0-a9024e4fe02d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.7f92b67a-becf-4abe-bb77-ba33be9beee8</processItemPrePostId>
                <processItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>44034f39-1176-4878-95d2-77572daa4f24</guid>
                <versionId>70d64f72-da22-4e93-8dee-df01a28d5b52</versionId>
            </processPrePosts>
            <layoutData x="177" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e46</errorHandlerItem>
                <errorHandlerItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.86349d0b-ac48-4269-80f1-dbd8b0f9a153</attachedProcessRef>
                <guid>38eee858-d0f4-4b09-ba15-5c0ebe1a6ae2</guid>
                <versionId>86d10cd0-bfc6-43d1-ab67-4e3bab607b6b</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.971f4a96-b6d7-4343-812d-24d2bda35991</parameterMappingId>
                    <processParameterId>2055.09b34574-0109-4b1a-84d9-10d8798d8638</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9d846c82-fb52-4212-bf5f-26a742a350dd</guid>
                    <versionId>0f859c9a-a6fc-4fa7-8796-************</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c1779503-6e2f-4a2b-9f8a-069ad0859653</parameterMappingId>
                    <processParameterId>2055.b52c707c-669c-4baa-8b6b-ec91855226f1</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ef7d1010-2379-482b-9cec-0b72bd18ac0d</guid>
                    <versionId>25217d5d-b802-49ef-8313-70d910e73996</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="countryList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6b7c0406-dc6e-4e44-bf7f-8a17e78f0450</parameterMappingId>
                    <processParameterId>2055.f1dc090a-a85a-45af-8ef7-9b6143edcc4b</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.countryList</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>e6ec825f-c0b2-4cab-8582-287b3c12464c</guid>
                    <versionId>4c188586-76b5-4eb9-a0c1-1f5c041c41ef</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.50d9fb34-dbb1-4629-ba65-3561d98fbe5f</parameterMappingId>
                    <processParameterId>2055.17614b87-c6ee-4824-852d-45896284f362</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>dcf72f4e-fe6e-4e55-8a9e-b9e59d829e7e</guid>
                    <versionId>596013e2-0776-4316-b96c-e16fe44916e9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.49d373e6-e6d8-44b7-a68e-57603286a7cc</parameterMappingId>
                    <processParameterId>2055.bbd3b876-5728-4ead-8604-58f5ec5d525a</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a44f11d4-2797-40c3-9415-cda88f63fe26</guid>
                    <versionId>64bcef41-fe0b-4b89-b9e7-8c2c8ba0cb29</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a40a2827-25db-416a-a4c5-1be62ef3621d</parameterMappingId>
                    <processParameterId>2055.e4a7e1f1-3bdc-4ff5-802d-6efe08683b27</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>66c997f0-e4e8-4a7c-9744-e6c7f6bac464</guid>
                    <versionId>790a6324-c138-47d9-ba87-2f9bdf59378e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fc5f9a71-d2b6-426a-ad60-f4473d9323c6</parameterMappingId>
                    <processParameterId>2055.bbf19f93-c4ee-4e3d-8b08-4888a30e74b9</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f9d82164-a381-4600-a6d7-5f93911aceef</guid>
                    <versionId>85bffd56-bef7-452a-819a-5200f18149ca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="get_countryList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f08cd91c-d774-44cb-a26e-dbc7852aa90f</parameterMappingId>
                    <processParameterId>2055.487c401d-9aa5-4137-8f5e-d95bf0ed702a</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3d8e97fc-dfb6-4469-9fa1-909c5b3951e5</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>23d12d04-3c2f-4f65-9559-5750b5cdc356</guid>
                    <versionId>b413cec4-301b-4628-9803-d3d02a606692</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c5c5bd64-a7d0-4e29-af1e-d08603beaedd</parameterMappingId>
                    <processParameterId>2055.955ddf56-89f5-4138-8181-a73a5a9b30a5</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4468f476-2c44-4847-94d7-e59ff9b91d0f</guid>
                    <versionId>c9086184-d7f0-4b33-80ae-04479458c7d2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1760f211-747c-4b67-a495-ce5175829b71</parameterMappingId>
                    <processParameterId>2055.2a94d974-b607-4899-8250-505833f23fe6</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c4ade015-9266-42ab-9ee0-4f4cd9f63815</guid>
                    <versionId>cd0f4dfe-a13c-4876-a46a-e3faf8139716</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.34a7b305-8c29-4b3e-a2d1-c07834ffb732</parameterMappingId>
                    <processParameterId>2055.37de8ad4-b945-40e8-8fca-cd428f242033</processParameterId>
                    <parameterMappingParentId>3012.314d5cd5-458b-45d7-83e6-91ff50aa0966</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>9ef9464b-34a2-42b4-8b00-e06410fae845</guid>
                    <versionId>e4a0926f-c7ec-4406-8359-d011ec84ba95</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.25b560a3-dc3d-4946-aa57-23af1cf86af0</processItemId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.523922d1-c2b0-46be-8d14-d929cee679ed</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6150</guid>
            <versionId>b1730f11-d29c-4e74-bff7-a9fafda2e0b3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="654" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.523922d1-c2b0-46be-8d14-d929cee679ed</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>50a4791a-67ac-4d29-b860-fe3f2e0f3e57</guid>
                <versionId>7d7f283a-458f-4257-afff-3c714a6e2ee9</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Country of Origin" id="1.b24f815d-f98b-4f2e-9dbe-f7601985749f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:cacheLength>43200</ns3:cacheLength>
                        
                        
                        <ns3:cachingType>true</ns3:cachingType>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.5aa08e55-3012-45c1-b6d1-f39f47aee7ac" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.3bb64ea6-a147-4cef-8f1c-5ce8d48c655f" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.ab114e02-8d10-438d-87f4-c363a9df3b66" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.3bb64ea6-a147-4cef-8f1c-5ce8d48c655f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.ab114e02-8d10-438d-87f4-c363a9df3b66</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="4b7fc777-5746-4fcb-9648-d231e92be152">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="4a044a9d-2c5e-4e23-ae62-94a93cd688e4" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="272" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9b620f9a-c48f-451a-90b6-8cec11a246b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>25b560a3-dc3d-4946-aa57-23af1cf86af0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2cd3dd1f-2eed-4e93-b922-59bd19e553fe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>482d2a3a-7aad-44c3-8f3c-851b432ab10d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9590277b-cc08-40be-81b9-9650a3a6a83c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>*************-4bc2-8735-69ec5ed1afdf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bd67478e-1352-4f4e-88ed-e1cd17ab5e98</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9b620f9a-c48f-451a-90b6-8cec11a246b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="33" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="25b560a3-dc3d-4946-aa57-23af1cf86af0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="654" y="33" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6150</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0631acf0-c65e-470c-aa22-788e524e9ca6</ns16:incoming>
                        
                        
                        <ns16:incoming>bd43c23a-7c57-46f3-8bbd-5b5525ac6664</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9b620f9a-c48f-451a-90b6-8cec11a246b1" targetRef="2cd3dd1f-2eed-4e93-b922-59bd19e553fe" name="To Script Task" id="2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.86349d0b-ac48-4269-80f1-dbd8b0f9a153" name="MW_FC get CountryList" id="2cd3dd1f-2eed-4e93-b922-59bd19e553fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="10" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.279bd9b6-fa5a-4f03-b6b4-de137b567adc</ns16:incoming>
                        
                        
                        <ns16:outgoing>c1d190f8-a27e-44d0-a033-e92af3790d62</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.955ddf56-89f5-4138-8181-a73a5a9b30a5</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b52c707c-669c-4baa-8b6b-ec91855226f1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e4a7e1f1-3bdc-4ff5-802d-6efe08683b27</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.bbf19f93-c4ee-4e3d-8b08-4888a30e74b9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.bbd3b876-5728-4ead-8604-58f5ec5d525a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.09b34574-0109-4b1a-84d9-10d8798d8638</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f1dc090a-a85a-45af-8ef7-9b6143edcc4b</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.countryList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2a94d974-b607-4899-8250-505833f23fe6</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.37de8ad4-b945-40e8-8fca-cd428f242033</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.17614b87-c6ee-4824-852d-45896284f362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="2cd3dd1f-2eed-4e93-b922-59bd19e553fe" targetRef="*************-4bc2-8735-69ec5ed1afdf" name="To is Successful" id="c1d190f8-a27e-44d0-a033-e92af3790d62">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6b3a</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="countryList" id="2056.289b2b2d-8a1c-40c1-8e77-6ac4ea847d8b" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Result" id="3a2ee2c6-0764-4aad-82cc-0552bc8bb26d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="484" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5ac7a166-bc75-4770-8273-abc93a216d08</ns16:incoming>
                        
                        
                        <ns16:outgoing>0631acf0-c65e-470c-aa22-788e524e9ca6</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.listOf.DBLookup();&#xD;
	for (var i=0; i&lt;tw.local.countryList.listLength; i++) {&#xD;
		tw.local.results[i] = new tw.object.DBLookup();&#xD;
		tw.local.results[i].id = i;&#xD;
		tw.local.results[i].code = tw.local.countryList[i].value+"";&#xD;
		tw.local.results[i].arabicdescription = tw.local.countryList[i].name;&#xD;
		tw.local.results[i].englishdescription = tw.local.countryList[i].name;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3a2ee2c6-0764-4aad-82cc-0552bc8bb26d" targetRef="25b560a3-dc3d-4946-aa57-23af1cf86af0" name="To End" id="0631acf0-c65e-470c-aa22-788e524e9ca6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.62f868c0-2cf6-46e6-aef4-fc9aee335e4f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.8eeb8e22-60a2-47ed-9238-3a4b0f686805" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.106ace05-cfe3-4937-9431-d15310be5938" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="2cd3dd1f-2eed-4e93-b922-59bd19e553fe" parallelMultiple="false" name="Error" id="482d2a3a-7aad-44c3-8f3c-851b432ab10d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6cbb6f4b-75ad-4b51-8575-6276994bfdd5</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="117b3fda-c4dd-492d-8246-6b4f231a612b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="1aa0752f-0e36-4e24-8eeb-3d14aa33fe75" eventImplId="15672643-14f9-4f57-860d-9a0f531b8c83">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3a2ee2c6-0764-4aad-82cc-0552bc8bb26d" parallelMultiple="false" name="Error1" id="9590277b-cc08-40be-81b9-9650a3a6a83c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>9bd60f2f-3726-446b-82a4-acbc6d3ab351</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2fbf6dfb-e8d9-44c9-8a9b-a57b5cd2379d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="76e2f2e1-8ee7-44f0-8bcd-1447e652f766" eventImplId="c3d0c5fc-9205-476f-8ed3-1afe06998b74">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="482d2a3a-7aad-44c3-8f3c-851b432ab10d" targetRef="bd67478e-1352-4f4e-88ed-e1cd17ab5e98" name="To End Event" id="6cbb6f4b-75ad-4b51-8575-6276994bfdd5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9590277b-cc08-40be-81b9-9650a3a6a83c" targetRef="bd67478e-1352-4f4e-88ed-e1cd17ab5e98" name="To End Event" id="9bd60f2f-3726-446b-82a4-acbc6d3ab351">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.7780fa34-b42f-4643-8b76-42d175191ed4" />
                    
                    
                    <ns16:exclusiveGateway default="5ac7a166-bc75-4770-8273-abc93a216d08" name="is Successful" id="*************-4bc2-8735-69ec5ed1afdf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="323" y="29" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c1d190f8-a27e-44d0-a033-e92af3790d62</ns16:incoming>
                        
                        
                        <ns16:outgoing>5ac7a166-bc75-4770-8273-abc93a216d08</ns16:outgoing>
                        
                        
                        <ns16:outgoing>0d7bacc0-1ff2-48fc-8240-e34188cbc04a</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-4bc2-8735-69ec5ed1afdf" targetRef="3a2ee2c6-0764-4aad-82cc-0552bc8bb26d" name="To Set Result" id="5ac7a166-bc75-4770-8273-abc93a216d08">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-4bc2-8735-69ec5ed1afdf" targetRef="bd67478e-1352-4f4e-88ed-e1cd17ab5e98" name="To End Event" id="0d7bacc0-1ff2-48fc-8240-e34188cbc04a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="bd67478e-1352-4f4e-88ed-e1cd17ab5e98">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="315" y="130" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0d7bacc0-1ff2-48fc-8240-e34188cbc04a</ns16:incoming>
                        
                        
                        <ns16:incoming>9bd60f2f-3726-446b-82a4-acbc6d3ab351</ns16:incoming>
                        
                        
                        <ns16:incoming>6cbb6f4b-75ad-4b51-8575-6276994bfdd5</ns16:incoming>
                        
                        
                        <ns16:outgoing>bd43c23a-7c57-46f3-8bbd-5b5525ac6664</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="bd67478e-1352-4f4e-88ed-e1cd17ab5e98" targetRef="25b560a3-dc3d-4946-aa57-23af1cf86af0" name="To End" id="bd43c23a-7c57-46f3-8bbd-5b5525ac6664">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5ac7a166-bc75-4770-8273-abc93a216d08</processLinkId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</toProcessItemId>
            <guid>dde55dc9-f51c-4a62-ba8f-43fe2a45d360</guid>
            <versionId>063e0020-4bf1-41dc-9e2d-801f7269f053</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</fromProcessItemId>
            <toProcessItemId>2025.3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0d7bacc0-1ff2-48fc-8240-e34188cbc04a</processLinkId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e47</endStateId>
            <toProcessItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</toProcessItemId>
            <guid>d06bf0fe-e1a5-4574-b2bd-eadf29cd81ce</guid>
            <versionId>406dc006-ec20-4ffc-92c4-bf63b59163bd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</fromProcessItemId>
            <toProcessItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c1d190f8-a27e-44d0-a033-e92af3790d62</processLinkId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</fromProcessItemId>
            <endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6b3a</endStateId>
            <toProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</toProcessItemId>
            <guid>d1371993-40a9-4293-a13e-d7dec1e95835</guid>
            <versionId>82355f08-ac57-448a-9575-a27ef02491ac</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2cd3dd1f-2eed-4e93-b922-59bd19e553fe</fromProcessItemId>
            <toProcessItemId>2025.*************-4bc2-8735-69ec5ed1afdf</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bd43c23a-7c57-46f3-8bbd-5b5525ac6664</processLinkId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.25b560a3-dc3d-4946-aa57-23af1cf86af0</toProcessItemId>
            <guid>bf146acf-a2e9-4af3-8812-aad37d7337a2</guid>
            <versionId>c07e1d4e-62f0-4f90-8c00-9e0fc95a6d84</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.bd67478e-1352-4f4e-88ed-e1cd17ab5e98</fromProcessItemId>
            <toProcessItemId>2025.25b560a3-dc3d-4946-aa57-23af1cf86af0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0631acf0-c65e-470c-aa22-788e524e9ca6</processLinkId>
            <processId>1.b24f815d-f98b-4f2e-9dbe-f7601985749f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.25b560a3-dc3d-4946-aa57-23af1cf86af0</toProcessItemId>
            <guid>b19f45c3-d730-4aa3-8d8d-035c7c18f709</guid>
            <versionId>cbaabbfe-ff81-4934-969d-87f1268a3ff8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3a2ee2c6-0764-4aad-82cc-0552bc8bb26d</fromProcessItemId>
            <toProcessItemId>2025.25b560a3-dc3d-4946-aa57-23af1cf86af0</toProcessItemId>
        </link>
    </process>
</teamworks>

