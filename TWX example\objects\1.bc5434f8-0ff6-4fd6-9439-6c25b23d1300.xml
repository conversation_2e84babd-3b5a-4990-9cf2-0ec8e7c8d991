<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300" name="Review Pending Queue by Credit Admin Execution Maker">
        <lastModified>1692795608148</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.a26010b6-678b-4d4b-b4c4-9b68ac0cf410</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>98a68f24-20bf-4a7a-b0d1-e903e174e1c6</guid>
        <versionId>ee5d4846-722c-40a1-a087-2e737861204c</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.cdeb3082-9890-412e-80ad-e05b78f031e5"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":188,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"59ad6fd1-5327-4c9e-8cd4-b44780d46b82"},{"incoming":["2027.f9781665-6304-4040-b52e-ceecbcbc734b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":898,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"45db3106-c746-43dd-94ce-4e01afedf792"},{"targetRef":"2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review Pending Queue by Credit Admin Execution Maker","declaredType":"sequenceFlow","id":"2027.cdeb3082-9890-412e-80ad-e05b78f031e5","sourceRef":"59ad6fd1-5327-4c9e-8cd4-b44780d46b82"},{"startQuantity":1,"outgoing":["2027.00097f86-e94f-4e48-9ad9-2c65080b3666"],"incoming":["2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471"],"default":"2027.00097f86-e94f-4e48-9ad9-2c65080b3666","extensionElements":{"nodeVisualInfo":[{"width":95,"x":546,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog.action = tw.local.selectedAction;\r\ntw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\ntw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCADExecutionMakerUpdate;"]}},{"targetRef":"2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.00097f86-e94f-4e48-9ad9-2c65080b3666","sourceRef":"2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8"},{"outgoing":["2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5","2027.9502e10b-e2f5-4872-a344-8d50c7126269"],"incoming":["2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c","2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9","2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe","2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":287,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6ac08bf8-bfa8-4ba2-889e-231be1c1640d","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7749a19c-73ce-4dca-82f7-56cc11083200","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"86058feb-7d20-47d4-89e8-7485279e19c6","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7cca0806-dd0b-436b-822a-1b476930056c","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5749336-2c98-42fe-804a-41f30e0b62b8","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"866b331a-87e7-405c-821c-32ca84feb7f8","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f087d562-c52f-41b5-82ee-854e046a58a7","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cb1563e5-6482-4deb-804f-147f527ff14d","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"94804aad-6c7e-4355-8623-099db2032bd0","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2a43295c-0c7f-4728-8dad-5157baa2c476","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0203cfd9-f3dd-4ec4-8902-29b1294487e7","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f18925fd-ffb1-43c1-8934-ea0e4e285b71","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97b27f96-0f09-4f1a-8dcd-d7ea550a6e6f","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"47f4e602-7392-4788-83ec-58af5a351e94","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6950bbc8-feb3-41f1-824d-8f8b12cc5d7c","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"28596ed1-8c56-416d-8603-3464bf3e194a","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d98575e1-4cc8-4c54-829b-6d6f00fcf0dd","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"43235844-9d6c-4142-854c-c4d6dc7a6fe8","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fb789b9f-c517-487d-8575-d5d6fa6e3bd4","optionName":"addBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c76ea5d0-9a97-4b4f-8623-afd77f1918a7","optionName":"deleteBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f7b09ac-cd9f-4f5b-8e63-81f9aa592a93","optionName":"addInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f2b060ed-bf5a-4bcf-85e7-0ef0b3849472","optionName":"deleteInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"69497bde-363a-4fd9-8631-4df20efd993d","optionName":"hasWithdraw","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8157dc53-972d-4031-89bb-619f6a589a49","optionName":"havePaymentTerm","value":"no"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64836706-2ab6-4924-85e3-889727f78611","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7abe27e0-ba25-4728-8921-5173bdaec6fd","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5d2570df-547e-4b87-8257-e7d66f3037b2","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"568a6495-18d9-4cd7-8d8e-91872b84237d","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fd1ef2b6-60b1-4d6c-8c70-052e057c57ba","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"74e7ac3b-b099-4982-8069-80c801b61a22","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df114436-6972-4066-873d-c32282b93d99","optionName":"advancePaymentsUsedOption","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9cd5aa9e-780e-4f91-82f5-466c3d5ec32f","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d1a6fd54-5e46-4dee-85d7-e874e3ae0210","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"62d41e0c-2d22-4088-8f3f-c2617cef7a5d","optionName":"accountsList","value":"tw.local.accountsList[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"34e30f88-0705-4a60-8cc3-b3fe248b31de","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"780fd120-0f14-46a7-8311-9b8869735faa","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"35d2d949-5f13-4e75-81d4-9d368558dc57","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9beb0508-54eb-483e-86b0-d473bdb8823f","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bc232bee-fa86-47f6-82bc-749304d707a3","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3f136395-66e2-4c8f-827c-6d7774d0e7e0","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpUsedAdvancePayment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c38a3afa-bbe5-4f70-855c-133a73129a5b","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4287d7e2-33af-4062-8eac-3f7f10f691db","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cd58107e-d3fc-4d43-88ab-69cc08ed4c47","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9ee2b47a-1492-4e36-8d38-620e69d8b5e0","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5a95d75f-d61f-465f-8984-036a25edff94","version":"8550"},{"layoutItemId":"Limits_Tracking1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e8a21326-50a9-4f0d-8bf3-28e7699b7fe5","optionName":"@label","value":"Limits Tracking"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6e5e9f98-bf72-4d71-87cb-828d682e8c81","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a341b6b9-bca1-40ef-8b30-a3e901c8a4ac","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"027ce8ac-1f5c-4dbb-84ba-fe9f499e6070","optionName":"SectionVis","value":"tw.local.facilityVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eeb76662-2dd8-4bae-8a86-e2735f862711","optionName":"facilityPercentageToBookVis","value":"tw.local.facilityPercentageToBookVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7d309b7d-32c6-414b-8004-d05f85b6bbae","optionName":"facilitiesCodesList","value":"tw.local.facilityCodes[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"913ea293-4830-45d6-8b33-ba8452e0ad03","optionName":"facility","value":"tw.local.facility"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ff58d99c-44e5-4ce0-81e4-b2b43c49b532","optionName":"LimitsVis","value":"tw.local.LimitsVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f93cf22-7b05-4891-86a1-d444aff76e24","optionName":"limitMessage","value":"tw.local.limitMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18a69afc-d740-42a6-8fce-0c20241b3c59","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}],"viewUUID":"64.9e7e0016-3899-48ff-9db4-bea34bea40f0","binding":"tw.local.idcContract","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"022400de-7e97-43a4-8570-0e733eb1320a","version":"8550"},{"layoutItemId":"attach1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4bb539a4-2f2c-48a3-80fc-c2489a9671ea","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5570311e-a289-46da-8e10-e293c89613a8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b86f4d9d-5c8b-4858-8cde-4750b2abdd45","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"32ed307a-db8a-4588-8c87-f42b64d1ae87","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"92895645-cab8-4eb2-840b-54f8323e25b9","optionName":"canDelete","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"227aa18a-d3ec-486a-8ba0-9a3443865cf7","optionName":"canCreate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c24ab0d4-17be-472c-8222-29e2ce13039e","optionName":"canUpdate","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c269a1dc-0ea8-4281-8133-827b6c24fd27","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a95e171c-2140-4ecb-8587-ed1d872ae85c","optionName":"visiable","value":"false"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"26e7d331-1335-4992-8cd0-27323559ff15","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b3fb583-3145-48ee-8b8a-02e3c02567f5","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eaf1e242-99e5-4766-8657-a56a4b01de29","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c28b129f-b2c5-4676-813f-4bc28f3e3759","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02719d48-698b-473b-85eb-579c95bee4b3","optionName":"historyVisFlag","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9258f5fe-ce39-4e15-8b7d-130e4b18d8bc","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.CADcomments[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"1ff19c20-8938-4f88-8789-dc220d250607","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"5f054ad0-6d18-4b11-86c8-8ba9a81cca47"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97f16088-9e76-482d-88a1-dca2cbd1a21a","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fbc9aad1-d494-43c9-898f-50cd8921ac28","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ae1992dd-0592-4045-8c19-ed2f9fc9134c","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65baf878-d7be-45bf-8231-f9c000d2430a","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6ac86cd8-b723-4484-8ccb-86221f49dec9","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"8e536e5b-a0b4-4562-8b0a-eeaaa887f587"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8ed05abc-5746-4d55-8db2-03c8cc8ecd4c","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"244b6baa-3b7c-47a7-86bc-87f3f6143c57","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c7cbf1ae-0ab9-4276-8d1c-fce0d7b07ec0","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ead9dde3-1c02-4816-8520-d5c788197902","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"21109e91-dd0c-4f08-8b51-680e1802227c","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7f289938-0cc2-4cd7-8805-5537cffdd256","optionName":"hasApprovals","value":"tw.local.hasApprovals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f6cc85a2-9b35-4744-87c5-c2d4014dd33b","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16c4ac20-c95a-41bd-8601-1bb3aefe4ac3","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5a6e881f-505b-4330-8f28-65aa724903fa","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"456cbe7c-eb53-4ef7-8eb9-87f4f4878a58","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1523f2aa-4c51-40ce-8555-b3b8497a1300","optionName":"invalidTabs","value":"tw.local.invalidTabs"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3ea24fef-4c2c-4c7a-83d4-72c24b669829","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"522f4861-587d-4e92-83b2-7b53733bfd11","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review Pending Queue by Credit Admin Execution Maker","isForCompensation":false,"completionQuantity":1,"id":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f"},{"outgoing":["2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c"],"incoming":["2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c"],"nodeVisualInfo":[{"width":24,"x":299,"y":37,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb"},{"targetRef":"2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"1c890b22-fd9e-47fb-9075-1a128b92f9e5","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5","sourceRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f"},{"targetRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review Pending Queue by Credit Admin Execution Maker","declaredType":"sequenceFlow","id":"2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c","sourceRef":"2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb"},{"outgoing":["2027.f9781665-6304-4040-b52e-ceecbcbc734b"],"incoming":["2027.00097f86-e94f-4e48-9ad9-2c65080b3666"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":716,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f9781665-6304-4040-b52e-ceecbcbc734b","name":"Update History","dataInputAssociation":[{"targetRef":"2055.648598d0-2039-40d4-b60b-3753a273a378","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}]},{"targetRef":"2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}]},{"targetRef":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Credit Admin Execution Maker\""]}}]},{"targetRef":"2055.322bdb97-0698-43d7-8172-71cbc933103d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}],"sourceRef":["2055.65675974-9215-43be-8dce-3b75511a591d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}],"sourceRef":["2055.8fcdef92-a110-407f-aff8-5693f497f953"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.CADcomments"]}}],"sourceRef":["2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}],"sourceRef":["2055.fce152d9-1c42-43bc-8bff-44f6b45aba67"]}],"calledElement":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59"},{"targetRef":"45db3106-c746-43dd-94ce-4e01afedf792","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.f9781665-6304-4040-b52e-ceecbcbc734b","sourceRef":"2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292"},{"startQuantity":1,"outgoing":["2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9"],"incoming":["2027.e64f7456-e137-4747-a620-a40cf84518fe"],"default":"2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9","extensionElements":{"nodeVisualInfo":[{"width":95,"x":145,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.149ddfee-98be-42cc-80d0-73d84660d3ef","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\ntw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();"]}},{"targetRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review Pending Queue by Credit Admin Execution Maker","declaredType":"sequenceFlow","id":"2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9","sourceRef":"2025.149ddfee-98be-42cc-80d0-73d84660d3ef"},{"targetRef":"2025.893bca87-4fbf-41f7-a793-8cd2a20570c1","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"5b3f4092-ca4b-471e-a7c1-ea31862e983a","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Status","declaredType":"sequenceFlow","id":"2027.9502e10b-e2f5-4872-a344-8d50c7126269","sourceRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f"},{"startQuantity":1,"outgoing":["2027.e64f7456-e137-4747-a620-a40cf84518fe"],"incoming":["2027.cdeb3082-9890-412e-80ad-e05b78f031e5"],"default":"2027.e64f7456-e137-4747-a620-a40cf84518fe","extensionElements":{"nodeVisualInfo":[{"width":95,"x":145,"y":68,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a","scriptFormat":"text\/x-javascript","script":{"content":["\/\/Dummy\r\n\/\/tw.local.idcRequest.appInfo.subStatus = tw.epv.Action.obtainApprovals;\r\n\r\ntw.local.facilityVis = \"NONE\";\r\ntw.local.LimitsVis = \"READONLY\";\r\ntw.local.errorVIS = \"NONE\";\r\ntw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\ntw.local.action = [];\r\n\/\/tw.local.action[0] = tw.epv.Action.se\r\ntw.local.action[0] = tw.epv.Action.sendBackToCADExecutionMakerPool;"]}},{"targetRef":"2025.149ddfee-98be-42cc-80d0-73d84660d3ef","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Step Name","declaredType":"sequenceFlow","id":"2027.e64f7456-e137-4747-a620-a40cf84518fe","sourceRef":"2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.89b3a89d-67ef-4332-b3dd-cc4b9c8fc5d5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.89b8fd58-b0bf-49ab-b78a-45bc3e7c74a2"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasApprovals","isCollection":false,"declaredType":"dataObject","id":"2056.a65a9147-00cd-4063-bb1f-fc5af97bb180"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasReturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.6116ddee-44fa-418d-ad0c-7872e50dd137"},{"startQuantity":1,"outgoing":["2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7"],"incoming":["2027.9502e10b-e2f5-4872-a344-8d50c7126269"],"default":"2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":287,"y":295,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.893bca87-4fbf-41f7-a793-8cd2a20570c1","scriptFormat":"text\/x-javascript","script":{"content":["var message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/var invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\/\/\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/validateTab(2);\r\n\/\/\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/validateTab(1);\r\n\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\r\n"]}},{"outgoing":["2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471","2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe"],"incoming":["2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7"],"default":"2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":408,"y":314,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.d5ee71e3-3aef-40ed-971c-da38288b770e"},{"targetRef":"2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of Copy of no","declaredType":"sequenceFlow","id":"2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471","sourceRef":"2025.d5ee71e3-3aef-40ed-971c-da38288b770e"},{"targetRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of Copy of yes","declaredType":"sequenceFlow","id":"2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe","sourceRef":"2025.d5ee71e3-3aef-40ed-971c-da38288b770e"},{"targetRef":"2025.d5ee71e3-3aef-40ed-971c-da38288b770e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7","sourceRef":"2025.893bca87-4fbf-41f7-a793-8cd2a20570c1"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.95994aed-76e1-4f5b-9d80-abb58863e9b7"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.6269e493-b7cd-4c41-bfe0-8caf48467691"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.50641fea-1d3b-48c4-99e2-0e8ab78cb0ba"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpUsedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.10361c85-a71c-4a4b-a391-2040b891b812"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.********-cad6-4c7c-a236-bd2083490c6f"},{"startQuantity":1,"outgoing":["2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1"],"incoming":["2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a"],"default":"2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":708,"y":37,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.40297c5a-93fa-4ef2-b438-a0365998ec8e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.4fdaa373-f098-4481-8161-d69988ff39c8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292","extensionElements":{"default":["2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a"],"nodeVisualInfo":[{"width":24,"x":751,"y":153,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.61e12a4c-d8cb-4401-ae4f-11cf0318e094","outputSet":{}},{"targetRef":"2025.40297c5a-93fa-4ef2-b438-a0365998ec8e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a","sourceRef":"2025.61e12a4c-d8cb-4401-ae4f-11cf0318e094"},{"targetRef":"2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review Pending Queue by Credit Admin Execution Maker","declaredType":"sequenceFlow","id":"2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1","sourceRef":"2025.40297c5a-93fa-4ef2-b438-a0365998ec8e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c13cc58e-1638-448c-85e6-b9019d5abfb8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.eeb08958-0058-4f96-8e48-8e2f965a9c2b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.571cdb64-0b07-4976-9860-23fb1658a821"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.4f7abd54-56b9-4d73-af8c-13c549216d62"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"NONE\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityVis","isCollection":false,"declaredType":"dataObject","id":"2056.e743f3e9-2d24-44d6-abca-c191c349f731"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"READONLY\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"facilityPercentageToBookVis","isCollection":false,"declaredType":"dataObject","id":"2056.ba6e8c52-8eea-4f04-89d0-1f493a1a88da"},{"itemSubjectRef":"itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08","name":"facility","isCollection":false,"declaredType":"dataObject","id":"2056.0b4db21e-2a2f-4489-972a-abe4264fe931"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"LimitsVis","isCollection":false,"declaredType":"dataObject","id":"2056.a561ec4c-6a12-4a2e-b679-7a36eedc5bee"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"limitMessage","isCollection":false,"declaredType":"dataObject","id":"2056.36c3e17e-7e3f-49b8-8785-7377d9497339"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.f9fecd90-aca2-4e68-8ce7-b4ce4a4f1b88"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"b805f7b8-d49e-4258-8286-37d78420ae95","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"7ed06b16-0a8b-4809-b5aa-c9b09700b7ee","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Review Pending Queue by Credit Admin Execution Maker","declaredType":"globalUserTask","id":"1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.32624665-7ad7-4374-a7af-0d9148296f16"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.2447ba5e-b42a-4ac1-90b0-f779e556c6cb"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.40117b77-6f7c-4d0f-b9bc-a6c669896cc4"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.3a4d2e0c-8f6d-4df3-9840-7761abedc431"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"8a9fffca-5a05-4de6-8bc0-d5d6c1a3c340","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"f7d140cf-ed3a-408c-8ba9-1802596f715f","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"8a1ac5df-ab8e-4ec1-8a2c-6dd75babd274","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.DBID = 0;\nautoObject.requestDate = new Date();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.b852c5bc-5a36-4a0a-80db-01de82e01b52"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.c2951d68-3b7f-4f8f-bda5-97f3c7738987"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"CADcomments","isCollection":true,"id":"2055.81f6f136-9d8b-427a-af9a-b4bd1f8033fa"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.fb144590-b7a2-4e56-b886-7a45923c2de6"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.7337ff82-8393-455a-8f61-436070f73e0a"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"facilityCodes","isCollection":true,"id":"2055.6dd64106-556d-4bb1-9488-10694af93fc0"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.4874070b-2182-40a2-9dcf-8bb4b65a8e94"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b852c5bc-5a36-4a0a-80db-01de82e01b52</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7d2b79d8-c9c3-410a-bbb5-6f4fb61bd99c</guid>
            <versionId>c910257a-dbf6-4bc5-8f1b-a9aff938e48f</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c2951d68-3b7f-4f8f-bda5-97f3c7738987</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>abb529e2-de0d-4d04-9de0-96d6b6689cbb</guid>
            <versionId>f88fd0e1-6ef8-4a98-95fb-563efa9c0a96</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.81f6f136-9d8b-427a-af9a-b4bd1f8033fa</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>54e2023c-9404-4438-b6cc-135b2bfb056a</guid>
            <versionId>410d26d2-5bc0-4b96-8048-274dbfa99905</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fb144590-b7a2-4e56-b886-7a45923c2de6</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>17010c53-85f2-4741-a994-59515162b688</guid>
            <versionId>5d0bc59a-37a7-48f8-b338-11e9a8e65eb3</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7337ff82-8393-455a-8f61-436070f73e0a</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7d374209-0ec3-4295-8d7c-b5978f37c375</guid>
            <versionId>a822a44b-d1f2-4d85-a823-e57a7c2518d5</versionId>
        </processParameter>
        <processParameter name="facilityCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6dd64106-556d-4bb1-9488-10694af93fc0</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>40243241-51e7-4278-9215-0736e2575476</guid>
            <versionId>7e3d05f8-35e6-49a9-b609-55bfdfb8c8a4</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.32624665-7ad7-4374-a7af-0d9148296f16</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1dd10738-a7f4-4566-aa7f-3d6be4e772d0</guid>
            <versionId>edda1f3a-fd5c-464a-809b-eaa5a3acd909</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2447ba5e-b42a-4ac1-90b0-f779e556c6cb</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6b6c8351-725c-4b44-9b71-9130a2174772</guid>
            <versionId>443d09cf-7b45-424b-aa3a-e598945f09d2</versionId>
        </processParameter>
        <processParameter name="CADcomments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.40117b77-6f7c-4d0f-b9bc-a6c669896cc4</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>13734a13-**************-a0becbd9c674</guid>
            <versionId>12204f9c-91cd-44ae-8998-************</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3a4d2e0c-8f6d-4df3-9840-7761abedc431</processParameterId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e00d2242-eee5-4d64-ae2e-02275260a562</guid>
            <versionId>3ec92e35-9eee-4cdf-8e94-4eb7be04dca5</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.89b3a89d-67ef-4332-b3dd-cc4b9c8fc5d5</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d1bb05c8-7c35-404d-9afd-ed2357e50822</guid>
            <versionId>3e6e7d5e-2e87-4d68-a2cf-c2ae5c780882</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.89b8fd58-b0bf-49ab-b78a-45bc3e7c74a2</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>459b990c-6585-4835-bed0-73e6dcb2fac5</guid>
            <versionId>d2d59360-10b5-4bfb-a4e2-c2dc59eb49a0</versionId>
        </processVariable>
        <processVariable name="hasApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a65a9147-00cd-4063-bb1f-fc5af97bb180</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>56b4035f-8156-4a57-b0b9-dcb74bb6cb6a</guid>
            <versionId>9d351404-6220-4523-a216-742078081a53</versionId>
        </processVariable>
        <processVariable name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6116ddee-44fa-418d-ad0c-7872e50dd137</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>********-12e2-4e11-bc70-9cf9dc390fb8</guid>
            <versionId>850b39a8-5af5-494d-aa82-973f83795db3</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.95994aed-76e1-4f5b-9d80-abb58863e9b7</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc2193bb-b898-4e81-af08-fc5292ae080d</guid>
            <versionId>705c2000-d915-48c6-9d5a-75f80ddd6c40</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6269e493-b7cd-4c41-bfe0-8caf48467691</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9a78a67a-98be-434c-b6ee-bcbdb42e8892</guid>
            <versionId>e6b499c0-14f4-4279-b710-93cf073cd23f</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.50641fea-1d3b-48c4-99e2-0e8ab78cb0ba</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1cb9ac20-dbc0-40b7-a390-c3043ef78982</guid>
            <versionId>bfa6dba1-c36e-410c-969a-c413e0cfa18b</versionId>
        </processVariable>
        <processVariable name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.10361c85-a71c-4a4b-a391-2040b891b812</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2469f9e5-e986-4a1a-9cc1-892d4c542467</guid>
            <versionId>7809f330-5907-40f6-9d61-044aaaf6a56b</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-cad6-4c7c-a236-bd2083490c6f</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>76c34928-1718-42e5-b94b-033f760b0301</guid>
            <versionId>fd344152-c623-4e64-bd2e-7c656deb97f7</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c13cc58e-1638-448c-85e6-b9019d5abfb8</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1d092ea3-2514-417f-b077-c07482a8df82</guid>
            <versionId>1518b31f-e0de-411e-a9d7-3383eb4a4cd6</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eeb08958-0058-4f96-8e48-8e2f965a9c2b</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9dcbfab6-842b-4989-80d0-81ca1a2db588</guid>
            <versionId>9873f953-bb1f-4819-b953-3643675c0a1a</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.571cdb64-0b07-4976-9860-23fb1658a821</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>50c93f45-afa4-4c93-b3f6-0d209935ee67</guid>
            <versionId>4695d916-9532-450a-94c5-58fa9396d966</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4f7abd54-56b9-4d73-af8c-13c549216d62</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>610558fb-4ec8-45a1-ac15-490b3d32cd03</guid>
            <versionId>6b54ed82-7c23-4f24-9589-c00ee8212086</versionId>
        </processVariable>
        <processVariable name="facilityVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e743f3e9-2d24-44d6-abca-c191c349f731</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>05ccaba5-1ded-4b3c-8bb7-f121a7dc0e96</guid>
            <versionId>14de2c42-4b41-4767-a878-c1b15ade6b92</versionId>
        </processVariable>
        <processVariable name="facilityPercentageToBookVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ba6e8c52-8eea-4f04-89d0-1f493a1a88da</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>637aecc9-5a07-4b1f-9c72-9804b1fb2405</guid>
            <versionId>723cfc63-a1a7-4ae4-95ea-a4e96d2926eb</versionId>
        </processVariable>
        <processVariable name="facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0b4db21e-2a2f-4489-972a-abe4264fe931</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.b7087c1b-7f18-4032-b88b-ffe584eafd08</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4ca10da1-44d2-4e06-810a-68032e143058</guid>
            <versionId>a3f58e83-71fb-46cc-b5f0-c9caf1203d58</versionId>
        </processVariable>
        <processVariable name="LimitsVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a561ec4c-6a12-4a2e-b679-7a36eedc5bee</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>*************-44fc-bb3e-2484335df6d6</guid>
            <versionId>6f85416e-bb64-4918-af6e-ba57a0667bc9</versionId>
        </processVariable>
        <processVariable name="limitMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.36c3e17e-7e3f-49b8-8785-7377d9497339</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>82258ebe-ca72-4793-a2ac-875cfc5b386a</guid>
            <versionId>d6dc2dc5-888d-4aeb-a5ac-91e26981be72</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f9fecd90-aca2-4e68-8ce7-b4ce4a4f1b88</processVariableId>
            <description isNull="true" />
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>237ebc56-2c09-4707-98f5-890a2350c267</guid>
            <versionId>9a93dfff-207d-4c5d-b607-5e7d5e5213c8</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a26010b6-678b-4d4b-b4c4-9b68ac0cf410</processItemId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.51e9932a-67e9-4712-bd64-0a542319f612</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-7fd6</guid>
            <versionId>2045893c-72cc-440a-806f-34005da960b3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292</processItemId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <name>Update History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c5c42936-f40f-4292-a17a-8cfc87e5209a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-7fd8</guid>
            <versionId>ac2a74fe-753e-4cae-9d01-978699bcfcc3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c5c42936-f40f-4292-a17a-8cfc87e5209a</subProcessId>
                <attachedProcessRef>/1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</attachedProcessRef>
                <guid>b7937b24-36d0-4f8d-b290-c0d1f663970d</guid>
                <versionId>39fbda46-8877-4c0d-beed-f528d44f45f6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5efc34d0-d24d-4113-bd4b-0a61293170ea</processItemId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.108a59aa-30fd-45a1-82d7-5176784d9f43</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a22ae4893:-7fd7</guid>
            <versionId>dba3c3e7-7805-4317-b76f-6b9652175893</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.108a59aa-30fd-45a1-82d7-5176784d9f43</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>957abdd5-93e3-4957-b4df-4705194b11b5</guid>
                <versionId>6881fddc-9671-4f6c-b4c8-635ee94a712c</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.5c940be4-a0bd-477f-8cf2-8299c08d655d</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <guid>e01f4bcb-2437-4425-a9f4-b82ca44bd5e6</guid>
            <versionId>6ff0c406-b82e-4111-b83a-4f5cb244a921</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.2a7847bd-50af-4705-87d2-ffa53bf6fe9a</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <guid>8c0516d0-334d-4fa0-ac93-70bcc5999c81</guid>
            <versionId>98d7b8cf-d0a0-49bd-881c-6b92d97e45cf</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.88090fb3-c551-4717-82c1-9a1a230829b9</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <guid>64541733-af66-451a-9d01-30292c315314</guid>
            <versionId>e05efc51-0c60-47c4-bb0c-2cd17fed679f</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.a26010b6-678b-4d4b-b4c4-9b68ac0cf410</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.4874070b-2182-40a2-9dcf-8bb4b65a8e94" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Review Pending Queue by Credit Admin Execution Maker" id="1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="7ed06b16-0a8b-4809-b5aa-c9b09700b7ee">
                            
                            
                            <ns16:startEvent name="Start" id="59ad6fd1-5327-4c9e-8cd4-b44780d46b82">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.cdeb3082-9890-412e-80ad-e05b78f031e5</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="45db3106-c746-43dd-94ce-4e01afedf792">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="898" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f9781665-6304-4040-b52e-ceecbcbc734b</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="59ad6fd1-5327-4c9e-8cd4-b44780d46b82" targetRef="2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a" name="To Review Pending Queue by Credit Admin Execution Maker" id="2027.cdeb3082-9890-412e-80ad-e05b78f031e5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.00097f86-e94f-4e48-9ad9-2c65080b3666" name="Set Status" id="2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="546" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.00097f86-e94f-4e48-9ad9-2c65080b3666</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.stepLog.action = tw.local.selectedAction;&#xD;
tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingCADExecutionMakerUpdate;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8" targetRef="2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292" name="OK To End" id="2027.00097f86-e94f-4e48-9ad9-2c65080b3666">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Review Pending Queue by Credit Admin Execution Maker" id="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="287" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.9502e10b-e2f5-4872-a344-8d50c7126269</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>f087d562-c52f-41b5-82ee-854e046a58a7</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6ac08bf8-bfa8-4ba2-889e-231be1c1640d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7749a19c-73ce-4dca-82f7-56cc11083200</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>86058feb-7d20-47d4-89e8-7485279e19c6</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7cca0806-dd0b-436b-822a-1b476930056c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f5749336-2c98-42fe-804a-41f30e0b62b8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>866b331a-87e7-405c-821c-32ca84feb7f8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>522f4861-587d-4e92-83b2-7b53733bfd11</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8ed05abc-5746-4d55-8db2-03c8cc8ecd4c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>244b6baa-3b7c-47a7-86bc-87f3f6143c57</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c7cbf1ae-0ab9-4276-8d1c-fce0d7b07ec0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ead9dde3-1c02-4816-8520-d5c788197902</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>21109e91-dd0c-4f08-8b51-680e1802227c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7f289938-0cc2-4cd7-8805-5537cffdd256</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasApprovals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f6cc85a2-9b35-4744-87c5-c2d4014dd33b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>16c4ac20-c95a-41bd-8601-1bb3aefe4ac3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5a6e881f-505b-4330-8f28-65aa724903fa</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>456cbe7c-eb53-4ef7-8eb9-87f4f4878a58</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1523f2aa-4c51-40ce-8555-b3b8497a1300</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>3ea24fef-4c2c-4c7a-83d4-72c24b669829</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>8e536e5b-a0b4-4562-8b0a-eeaaa887f587</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>6ac86cd8-b723-4484-8ccb-86221f49dec9</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>97f16088-9e76-482d-88a1-dca2cbd1a21a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>fbc9aad1-d494-43c9-898f-50cd8921ac28</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>ae1992dd-0592-4045-8c19-ed2f9fc9134c</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>65baf878-d7be-45bf-8231-f9c000d2430a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>5f054ad0-6d18-4b11-86c8-8ba9a81cca47</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>47f4e602-7392-4788-83ec-58af5a351e94</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cb1563e5-6482-4deb-804f-147f527ff14d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>94804aad-6c7e-4355-8623-099db2032bd0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2a43295c-0c7f-4728-8dad-5157baa2c476</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0203cfd9-f3dd-4ec4-8902-29b1294487e7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f18925fd-ffb1-43c1-8934-ea0e4e285b71</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>97b27f96-0f09-4f1a-8dcd-d7ea550a6e6f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>5d2570df-547e-4b87-8257-e7d66f3037b2</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6950bbc8-feb3-41f1-824d-8f8b12cc5d7c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>28596ed1-8c56-416d-8603-3464bf3e194a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d98575e1-4cc8-4c54-829b-6d6f00fcf0dd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>43235844-9d6c-4142-854c-c4d6dc7a6fe8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fb789b9f-c517-487d-8575-d5d6fa6e3bd4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c76ea5d0-9a97-4b4f-8623-afd77f1918a7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1f7b09ac-cd9f-4f5b-8e63-81f9aa592a93</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f2b060ed-bf5a-4bcf-85e7-0ef0b3849472</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>69497bde-363a-4fd9-8631-4df20efd993d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8157dc53-972d-4031-89bb-619f6a589a49</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>no</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>64836706-2ab6-4924-85e3-889727f78611</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7abe27e0-ba25-4728-8921-5173bdaec6fd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>5a95d75f-d61f-465f-8984-036a25edff94</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>568a6495-18d9-4cd7-8d8e-91872b84237d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fd1ef2b6-60b1-4d6c-8c70-052e057c57ba</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>74e7ac3b-b099-4982-8069-80c801b61a22</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>df114436-6972-4066-873d-c32282b93d99</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9cd5aa9e-780e-4f91-82f5-466c3d5ec32f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d1a6fd54-5e46-4dee-85d7-e874e3ae0210</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>62d41e0c-2d22-4088-8f3f-c2617cef7a5d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>34e30f88-0705-4a60-8cc3-b3fe248b31de</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>780fd120-0f14-46a7-8311-9b8869735faa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>35d2d949-5f13-4e75-81d4-9d368558dc57</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9beb0508-54eb-483e-86b0-d473bdb8823f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bc232bee-fa86-47f6-82bc-749304d707a3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3f136395-66e2-4c8f-827c-6d7774d0e7e0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.tmpUsedAdvancePayment</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c38a3afa-bbe5-4f70-855c-133a73129a5b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4287d7e2-33af-4062-8eac-3f7f10f691db</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cd58107e-d3fc-4d43-88ab-69cc08ed4c47</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9ee2b47a-1492-4e36-8d38-620e69d8b5e0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>022400de-7e97-43a4-8570-0e733eb1320a</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Limits_Tracking1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e8a21326-50a9-4f0d-8bf3-28e7699b7fe5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Limits Tracking</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6e5e9f98-bf72-4d71-87cb-828d682e8c81</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a341b6b9-bca1-40ef-8b30-a3e901c8a4ac</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>027ce8ac-1f5c-4dbb-84ba-fe9f499e6070</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>SectionVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facilityVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>eeb76662-2dd8-4bae-8a86-e2735f862711</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facilityPercentageToBookVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facilityPercentageToBookVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7d309b7d-32c6-414b-8004-d05f85b6bbae</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facilitiesCodesList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facilityCodes[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>913ea293-4830-45d6-8b33-ba8452e0ad03</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>facility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.facility</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ff58d99c-44e5-4ce0-81e4-b2b43c49b532</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>LimitsVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.LimitsVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9f93cf22-7b05-4891-86a1-d444aff76e24</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>limitMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.limitMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>18a69afc-d740-42a6-8fce-0c20241b3c59</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.9e7e0016-3899-48ff-9db4-bea34bea40f0</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcContract</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>26e7d331-1335-4992-8cd0-27323559ff15</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>attach1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4bb539a4-2f2c-48a3-80fc-c2489a9671ea</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5570311e-a289-46da-8e10-e293c89613a8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b86f4d9d-5c8b-4858-8cde-4750b2abdd45</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>32ed307a-db8a-4588-8c87-f42b64d1ae87</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>92895645-cab8-4eb2-840b-54f8323e25b9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>227aa18a-d3ec-486a-8ba0-9a3443865cf7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c24ab0d4-17be-472c-8222-29e2ce13039e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c269a1dc-0ea8-4281-8133-827b6c24fd27</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a95e171c-2140-4ecb-8587-ed1d872ae85c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>1ff19c20-8938-4f88-8789-dc220d250607</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4b3fb583-3145-48ee-8b8a-02e3c02567f5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>eaf1e242-99e5-4766-8657-a56a4b01de29</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c28b129f-b2c5-4676-813f-4bc28f3e3759</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>02719d48-698b-473b-85eb-579c95bee4b3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9258f5fe-ce39-4e15-8b7d-130e4b18d8bc</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.CADcomments[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="299" y="37" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" targetRef="2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb" name="To Stay on page" id="2027.2f0bbf6d-1c49-47ac-9ef7-3eb11a59aac5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="1c890b22-fd9e-47fb-9075-1a128b92f9e5">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.7d3db196-3c41-41a3-ad2a-68a39ca92deb" targetRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" name="To Review Pending Queue by Credit Admin Execution Maker" id="2027.e1460911-2e0c-4cf2-af0d-fc9e9ed4786c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f9781665-6304-4040-b52e-ceecbcbc734b" name="Update History" id="2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="716" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.00097f86-e94f-4e48-9ad9-2c65080b3666</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f9781665-6304-4040-b52e-ceecbcbc734b</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.648598d0-2039-40d4-b60b-3753a273a378</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Credit Admin Execution Maker"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.65675974-9215-43be-8dce-3b75511a591d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8fcdef92-a110-407f-aff8-5693f497f953</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.CADcomments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fce152d9-1c42-43bc-8bff-44f6b45aba67</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292" targetRef="45db3106-c746-43dd-94ce-4e01afedf792" name="To End" id="2027.f9781665-6304-4040-b52e-ceecbcbc734b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9" name="Set Step Name" id="2025.149ddfee-98be-42cc-80d0-73d84660d3ef">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="145" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.e64f7456-e137-4747-a620-a40cf84518fe</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.149ddfee-98be-42cc-80d0-73d84660d3ef" targetRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" name="To Review Pending Queue by Credit Admin Execution Maker" id="2027.baae58a7-cd50-41c3-9d37-1e16ba10dbe9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" targetRef="2025.893bca87-4fbf-41f7-a793-8cd2a20570c1" name="To Set Status" id="2027.9502e10b-e2f5-4872-a344-8d50c7126269">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="5b3f4092-ca4b-471e-a7c1-ea31862e983a">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.e64f7456-e137-4747-a620-a40cf84518fe" name="Initialization Script" id="2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="145" y="68" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.cdeb3082-9890-412e-80ad-e05b78f031e5</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e64f7456-e137-4747-a620-a40cf84518fe</ns16:outgoing>
                                
                                
                                <ns16:script>//Dummy&#xD;
//tw.local.idcRequest.appInfo.subStatus = tw.epv.Action.obtainApprovals;&#xD;
&#xD;
tw.local.facilityVis = "NONE";&#xD;
tw.local.LimitsVis = "READONLY";&#xD;
tw.local.errorVIS = "NONE";&#xD;
tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
tw.local.action = [];&#xD;
//tw.local.action[0] = tw.epv.Action.se&#xD;
tw.local.action[0] = tw.epv.Action.sendBackToCADExecutionMakerPool;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b9a41d5e-5057-4b05-9ed6-5aeb1b57635a" targetRef="2025.149ddfee-98be-42cc-80d0-73d84660d3ef" name="To Set Step Name" id="2027.e64f7456-e137-4747-a620-a40cf84518fe">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.89b3a89d-67ef-4332-b3dd-cc4b9c8fc5d5" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.89b8fd58-b0bf-49ab-b78a-45bc3e7c74a2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasApprovals" id="2056.a65a9147-00cd-4063-bb1f-fc5af97bb180">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasReturnReason" id="2056.6116ddee-44fa-418d-ad0c-7872e50dd137">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7" name="Validation" id="2025.893bca87-4fbf-41f7-a793-8cd2a20570c1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="287" y="295" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9502e10b-e2f5-4872-a344-8d50c7126269</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7</ns16:outgoing>
                                
                                
                                <ns16:script>var message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//var invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
//		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
////validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
//validateTab(2);&#xD;
////----------------------------------basic details------------------------------------------------------------------------------&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
//validateTab(1);&#xD;
//----------------------------------------app info------------------------------------------------------------------------------------&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471" gatewayDirection="Unspecified" name="Have Errors" id="2025.d5ee71e3-3aef-40ed-971c-da38288b770e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="408" y="314" width="32" height="32" />
                                    
                                    
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d5ee71e3-3aef-40ed-971c-da38288b770e" targetRef="2025.836f51f9-85a1-47bd-ad7e-f4cbda71c6e8" name="Copy of Copy of no" id="2027.d8cb3b7a-a803-415a-ac7a-06bda90bd471">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d5ee71e3-3aef-40ed-971c-da38288b770e" targetRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" name="Copy of Copy of yes" id="2027.61a54ac7-9d68-4dc7-98aa-96539842a0fe">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.893bca87-4fbf-41f7-a793-8cd2a20570c1" targetRef="2025.d5ee71e3-3aef-40ed-971c-da38288b770e" name="To Have Errors" id="2027.a072fc3a-c461-40b1-8fbe-bb3d36ab0cf7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.95994aed-76e1-4f5b-9d80-abb58863e9b7" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.6269e493-b7cd-4c41-bfe0-8caf48467691" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.50641fea-1d3b-48c4-99e2-0e8ab78cb0ba" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpUsedAdvancePayment" id="2056.10361c85-a71c-4a4b-a391-2040b891b812" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.********-cad6-4c7c-a236-bd2083490c6f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1" name="Handling Error" id="2025.40297c5a-93fa-4ef2-b438-a0365998ec8e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="708" y="37" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.5e1aedc1-d5ed-4318-bbd1-0ffe4ac31292" parallelMultiple="false" name="Error" id="2025.61e12a4c-d8cb-4401-ae4f-11cf0318e094">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="751" y="153" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.4fdaa373-f098-4481-8161-d69988ff39c8" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.61e12a4c-d8cb-4401-ae4f-11cf0318e094" targetRef="2025.40297c5a-93fa-4ef2-b438-a0365998ec8e" name="To Handling Error" id="2027.4909d66b-1c43-4ce0-8091-1bbfe94dba7a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.40297c5a-93fa-4ef2-b438-a0365998ec8e" targetRef="2025.e5e6869a-1e17-403c-92f0-fca3c3d49a1f" name="To Review Pending Queue by Credit Admin Execution Maker" id="2027.2ae7eaf1-b15e-44ca-a28c-cd87ed4540c1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c13cc58e-1638-448c-85e6-b9019d5abfb8" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.eeb08958-0058-4f96-8e48-8e2f965a9c2b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.571cdb64-0b07-4976-9860-23fb1658a821" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.4f7abd54-56b9-4d73-af8c-13c549216d62" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityVis" id="2056.e743f3e9-2d24-44d6-abca-c191c349f731">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"NONE"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="facilityPercentageToBookVis" id="2056.ba6e8c52-8eea-4f04-89d0-1f493a1a88da">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"READONLY"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b7087c1b-7f18-4032-b88b-ffe584eafd08" isCollection="false" name="facility" id="2056.0b4db21e-2a2f-4489-972a-abe4264fe931" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="LimitsVis" id="2056.a561ec4c-6a12-4a2e-b679-7a36eedc5bee" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="limitMessage" id="2056.36c3e17e-7e3f-49b8-8785-7377d9497339" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.f9fecd90-aca2-4e68-8ce7-b4ce4a4f1b88" />
                            
                            
                            <ns3:htmlHeaderTag id="b805f7b8-d49e-4258-8286-37d78420ae95">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="8a9fffca-5a05-4de6-8bc0-d5d6c1a3c340" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="f7d140cf-ed3a-408c-8ba9-1802596f715f" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="8a1ac5df-ab8e-4ec1-8a2c-6dd75babd274" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.b852c5bc-5a36-4a0a-80db-01de82e01b52">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject.requestDate = new Date();
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.c2951d68-3b7f-4f8f-bda5-97f3c7738987" />
                        
                        
                        <ns16:dataInput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.81f6f136-9d8b-427a-af9a-b4bd1f8033fa" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.fb144590-b7a2-4e56-b886-7a45923c2de6" />
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.7337ff82-8393-455a-8f61-436070f73e0a" />
                        
                        
                        <ns16:dataInput name="facilityCodes" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.6dd64106-556d-4bb1-9488-10694af93fc0" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.32624665-7ad7-4374-a7af-0d9148296f16" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.2447ba5e-b42a-4ac1-90b0-f779e556c6cb" />
                        
                        
                        <ns16:dataOutput name="CADcomments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.40117b77-6f7c-4d0f-b9bc-a6c669896cc4" />
                        
                        
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.3a4d2e0c-8f6d-4df3-9840-7761abedc431" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f43d2137-6a13-4fb1-bd0c-4cd5c18d2f90</processLinkId>
            <processId>1.bc5434f8-0ff6-4fd6-9439-6c25b23d1300</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a26010b6-678b-4d4b-b4c4-9b68ac0cf410</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.5efc34d0-d24d-4113-bd4b-0a61293170ea</toProcessItemId>
            <guid>1ea4763f-4f11-49e3-9dc3-457bd2e7f957</guid>
            <versionId>eed634e2-3be2-4cb2-8eed-79e964bfd7ad</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.a26010b6-678b-4d4b-b4c4-9b68ac0cf410</fromProcessItemId>
            <toProcessItemId>2025.5efc34d0-d24d-4113-bd4b-0a61293170ea</toProcessItemId>
        </link>
    </process>
</teamworks>

