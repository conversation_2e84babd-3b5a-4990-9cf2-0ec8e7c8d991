<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488" name="Check Bill">
        <lastModified>1692504796787</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:fef170a08f25d496:5466e087:189df5f8551:74da</guid>
        <versionId>585872b8-a86e-40e7-8ebc-b9330273605d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3c" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.8652528b-8510-4609-8fad-2ef95448055d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"02e7811f-a6b8-4a54-8e84-dd1538f5e561"},{"incoming":["8adb9b50-a60c-4167-88c7-4f68be4efb01","e8602f68-f7b2-4eaf-89d4-640c93f99afb"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:74dc"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"87ad2d23-51c0-49dd-8433-4cb907c9b6d4"},{"targetRef":"d7e848e9-feab-43ec-8fac-9bcdea0629b6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"2027.8652528b-8510-4609-8fad-2ef95448055d","sourceRef":"02e7811f-a6b8-4a54-8e84-dd1538f5e561"},{"startQuantity":1,"outgoing":["78621177-b9e8-468d-80a7-1c42fd26d748"],"incoming":["ec6a3114-6c9a-4457-8cad-9b184f37445e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":207,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c176a079-113c-4f15-8489-7b29872fb5a5","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_BILLS WHERE BILL_OF_LADING_REF = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');\";\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.parameters[0]=new tw.object.SQLParameter();\r\ntw.local.parameters[1]=new tw.object.SQLParameter();\r\ntw.local.parameters[0].value=tw.local.billNumber;\r\ntw.local.parameters[0].type = \"VARCHAR\";\r\ntw.local.parameters[1].value=tw.local.requestID;\r\ntw.local.parameters[1].type = \"INTEGER\";"]}},{"targetRef":"bf7390f1-d2ae-4490-86ba-37f6f67ac564","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"78621177-b9e8-468d-80a7-1c42fd26d748","sourceRef":"c176a079-113c-4f15-8489-7b29872fb5a5"},{"startQuantity":1,"outgoing":["1c8668f8-b942-42a7-8c34-6bafdad2bc86"],"incoming":["78621177-b9e8-468d-80a7-1c42fd26d748"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":347,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.parameters"]}}]},{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"bf7390f1-d2ae-4490-86ba-37f6f67ac564","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.bills"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"6be570b2-8293-4140-8de8-04a99f1fc340","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Output Mapping","declaredType":"sequenceFlow","id":"1c8668f8-b942-42a7-8c34-6bafdad2bc86","sourceRef":"bf7390f1-d2ae-4490-86ba-37f6f67ac564"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.821cb4fd-2072-471d-8878-10d40a2761c4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.76898369-69c5-442a-892f-c02c7f99e03e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"bills","isCollection":true,"declaredType":"dataObject","id":"2056.d2e0978f-fd42-4d57-8b35-f1ef6a670410"},{"startQuantity":1,"outgoing":["ec6a3114-6c9a-4457-8cad-9b184f37445e"],"incoming":["2027.8652528b-8510-4609-8fad-2ef95448055d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":82,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"d7e848e9-feab-43ec-8fac-9bcdea0629b6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.Seperated = new tw.object.listOf.String();\r\ntw.local.Seperated = tw.local.data.split(\"-\");\r\ntw.local.requestID = tw.local.Seperated[0];\r\ntw.local.billNumber = tw.local.Seperated[1];\r\n"]}},{"parallelMultiple":false,"outgoing":["8881c850-862c-460f-8a07-7409b4294a70"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"cfbf9a65-3b99-45f4-8419-781bb5aaa647"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"cdd46dd0-810e-414d-892a-a90dc99598c5","otherAttributes":{"eventImplId":"e7639ecf-c87b-4fcd-81af-719de2140d97"}}],"attachedToRef":"d7e848e9-feab-43ec-8fac-9bcdea0629b6","extensionElements":{"nodeVisualInfo":[{"width":24,"x":117,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Copy of Error","declaredType":"boundaryEvent","id":"cdaed79c-a35a-4319-80b3-e6fc80816e66","outputSet":{}},{"targetRef":"c176a079-113c-4f15-8489-7b29872fb5a5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"ec6a3114-6c9a-4457-8cad-9b184f37445e","sourceRef":"d7e848e9-feab-43ec-8fac-9bcdea0629b6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.64531898-1adc-4ec4-80a8-e69cafaaf30b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.e77cdb5e-dcc7-4673-8324-8431f30bec19"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"billNumber","isCollection":false,"declaredType":"dataObject","id":"2056.08ad0ffe-6fd1-47fd-8f02-8470d8d92dd0"},{"startQuantity":1,"outgoing":["8adb9b50-a60c-4167-88c7-4f68be4efb01"],"incoming":["1c8668f8-b942-42a7-8c34-6bafdad2bc86"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":509,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Output Mapping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6be570b2-8293-4140-8de8-04a99f1fc340","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.bills[0].rows.listLength&gt;0) {\r\n\ttw.local.results = 1;\r\n}\r\nelse{\r\n\ttw.local.results = 0;\r\n}"]}},{"targetRef":"87ad2d23-51c0-49dd-8433-4cb907c9b6d4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"8adb9b50-a60c-4167-88c7-4f68be4efb01","sourceRef":"6be570b2-8293-4140-8de8-04a99f1fc340"},{"parallelMultiple":false,"outgoing":["75ea5b97-c622-480f-8e05-1dfb71e211f3"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"541c7360-98bf-4004-84f4-2c509d3e93c2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9ada05c8-4ef4-4b8f-8ff3-9ff761acdc01","otherAttributes":{"eventImplId":"6af20c49-b69e-4b7b-8e62-0a044ad97444"}}],"attachedToRef":"c176a079-113c-4f15-8489-7b29872fb5a5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":242,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"17813b06-58a1-420b-8afd-f0cbcedf8179","outputSet":{}},{"parallelMultiple":false,"outgoing":["f7c73f90-70fc-45ed-8abd-d29bd0022ad4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3ad5ee7e-598d-4faa-8f89-26e08106baff"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"57f69f13-e31e-4095-8782-87e6d00264e9","otherAttributes":{"eventImplId":"27eca960-9dfb-4ed4-8471-5e7c6c4c68eb"}}],"attachedToRef":"bf7390f1-d2ae-4490-86ba-37f6f67ac564","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"50607b70-c022-49f8-83d1-6e47b658098b","outputSet":{}},{"parallelMultiple":false,"outgoing":["4059b2b5-931b-4606-8431-6b8e4853f59e"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"c88c4678-1287-4230-8b6a-ce4203923dd4"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fd82642b-5b39-40ab-84b1-9ba4697c368d","otherAttributes":{"eventImplId":"2b96066c-cc78-491d-86a8-efc2ded3a828"}}],"attachedToRef":"6be570b2-8293-4140-8de8-04a99f1fc340","extensionElements":{"nodeVisualInfo":[{"width":24,"x":544,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"cb5e5e26-ea23-47ff-8697-702fc221943d","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.e0487ee9-88a2-487d-83ec-9756a177cfd4"},{"startQuantity":1,"outgoing":["e8602f68-f7b2-4eaf-89d4-640c93f99afb"],"incoming":["8881c850-862c-460f-8a07-7409b4294a70","75ea5b97-c622-480f-8e05-1dfb71e211f3","f7c73f90-70fc-45ed-8abd-d29bd0022ad4","4059b2b5-931b-4606-8431-6b8e4853f59e"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":286,"y":170,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b2ebf717-f910-4c54-82bc-9192546a185f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"b2ebf717-f910-4c54-82bc-9192546a185f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"8881c850-862c-460f-8a07-7409b4294a70","sourceRef":"cdaed79c-a35a-4319-80b3-e6fc80816e66"},{"targetRef":"b2ebf717-f910-4c54-82bc-9192546a185f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"75ea5b97-c622-480f-8e05-1dfb71e211f3","sourceRef":"17813b06-58a1-420b-8afd-f0cbcedf8179"},{"targetRef":"b2ebf717-f910-4c54-82bc-9192546a185f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"f7c73f90-70fc-45ed-8abd-d29bd0022ad4","sourceRef":"50607b70-c022-49f8-83d1-6e47b658098b"},{"targetRef":"b2ebf717-f910-4c54-82bc-9192546a185f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"4059b2b5-931b-4606-8431-6b8e4853f59e","sourceRef":"cb5e5e26-ea23-47ff-8697-702fc221943d"},{"targetRef":"87ad2d23-51c0-49dd-8433-4cb907c9b6d4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e8602f68-f7b2-4eaf-89d4-640c93f99afb","sourceRef":"b2ebf717-f910-4c54-82bc-9192546a185f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.6b0c50be-5bcb-4c3c-8777-a38f7c5a15a1"}],"laneSet":[{"id":"66aa03c5-3fd2-4be9-8f19-78ac0ef91c7c","lane":[{"flowNodeRef":["02e7811f-a6b8-4a54-8e84-dd1538f5e561","87ad2d23-51c0-49dd-8433-4cb907c9b6d4","c176a079-113c-4f15-8489-7b29872fb5a5","bf7390f1-d2ae-4490-86ba-37f6f67ac564","d7e848e9-feab-43ec-8fac-9bcdea0629b6","cdaed79c-a35a-4319-80b3-e6fc80816e66","6be570b2-8293-4140-8de8-04a99f1fc340","17813b06-58a1-420b-8afd-f0cbcedf8179","50607b70-c022-49f8-83d1-6e47b658098b","cb5e5e26-ea23-47ff-8697-702fc221943d","b2ebf717-f910-4c54-82bc-9192546a185f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"0495640a-63e1-46d3-8375-cec88e0c61dd","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Check Bill","declaredType":"process","id":"1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.47017165-35f1-4db2-888e-6a34754f2050"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.0b75c02d-797c-420e-899f-eaf78d6c73fb"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"40-123\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.89765247-1a03-4410-8e29-3b9aabd169df"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.89765247-1a03-4410-8e29-3b9aabd169df</processParameterId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"40-123"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1cd39c31-0323-4a2b-83c8-563e2542af1d</guid>
            <versionId>be6881bd-2d7b-4e53-b207-7628979ab836</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.47017165-35f1-4db2-888e-6a34754f2050</processParameterId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>21dc9691-f995-49fe-9f8b-f731c129db6f</guid>
            <versionId>c3f5e017-5437-499a-ad73-40aad555ae4f</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0b75c02d-797c-420e-899f-eaf78d6c73fb</processParameterId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c282a410-4233-4628-9b34-d0317e97c04a</guid>
            <versionId>635f5bce-1f2b-40f6-9ac5-12c22178026a</versionId>
        </processParameter>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.821cb4fd-2072-471d-8878-10d40a2761c4</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>40c8d7cb-8313-4c8c-82d5-3a2616b2627e</guid>
            <versionId>788b1601-9ba4-4900-a570-59b5d912e962</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.76898369-69c5-442a-892f-c02c7f99e03e</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8447d03e-0c76-4178-a497-423c9a4d07a0</guid>
            <versionId>0a1e8dc1-9d70-446c-bd3e-a0f653ea649d</versionId>
        </processVariable>
        <processVariable name="bills">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d2e0978f-fd42-4d57-8b35-f1ef6a670410</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>67d24345-9b76-40a3-8719-03264529174b</guid>
            <versionId>7d0149e5-4289-4c5e-a9ea-e02c5b9fe06a</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.64531898-1adc-4ec4-80a8-e69cafaaf30b</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4c517128-8264-4e53-a6c9-18e4e2bb3b34</guid>
            <versionId>2ebb6549-45fd-49bc-a476-3a45f1f1db18</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e77cdb5e-dcc7-4673-8324-8431f30bec19</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8d44c700-c3fb-4900-ae9b-61c959e1c060</guid>
            <versionId>00a0fea9-2363-4a20-8cb2-40b19b888517</versionId>
        </processVariable>
        <processVariable name="billNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.08ad0ffe-6fd1-47fd-8f02-8470d8d92dd0</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>712b522a-335a-46a6-9d02-a53a49ca0b6b</guid>
            <versionId>dd1ada5d-157a-4803-8567-c5d7621a38f6</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e0487ee9-88a2-487d-83ec-9756a177cfd4</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88238bfc-2331-4837-aab0-a5912de76c44</guid>
            <versionId>92ea3697-45fd-4983-8ea3-59eb2289fb00</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6b0c50be-5bcb-4c3c-8777-a38f7c5a15a1</processVariableId>
            <description isNull="true" />
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e2a10324-33a4-4e1d-866e-aec4c941d6cd</guid>
            <versionId>bd61f9ca-e3c8-44dc-8163-35460143bf6a</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bf7390f1-d2ae-4490-86ba-37f6f67ac564</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:7501</guid>
            <versionId>07c88b69-f78b-4d7d-8cf2-e60dbc98a21c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="347" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3b</errorHandlerItem>
                <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>28c695e2-61c6-4bd9-b969-97445616f7fa</guid>
                <versionId>101a6f5f-20fa-4049-8f4d-e9644e875a64</versionId>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.31650cc7-2ba0-4979-a04f-e002962ce0bf</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4692f4d1-0bcd-471e-a10a-d29afe62a144</guid>
                    <versionId>0e7a7648-ee08-42c5-b5b9-cb5cae06a3cf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2a45328e-2804-428d-8243-eb7dd849ce86</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>748efaf8-d540-4c7c-a715-4f8861ab5200</guid>
                    <versionId>117d093e-42e7-4f50-b8dd-eca95edc9d34</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.81038d60-b7c7-4a41-8044-8b96a0918e92</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>0ef01ec4-0662-4b26-9bdf-0281f2211cea</guid>
                    <versionId>14e909f0-6cdf-460b-8a06-a4a27f87a616</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e86b8ea2-7491-45c4-b6b4-1a4f672d4435</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>569a8bd1-4df0-42c6-9ec3-89f6d436d92c</guid>
                    <versionId>48e18f47-98f6-488a-b369-d3ba2b703775</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.85c9f960-766b-44f6-8d07-6473d78c070a</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d686a43a-8ccd-495d-8465-d21e9e00bd3d</guid>
                    <versionId>9054c53d-230d-4396-868e-4b480d05815a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9ac0d744-ac26-4eb4-9e9c-558eb67a9ee5</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.8b777e0b-1dd0-4793-bf38-f999fb067f83</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.bills</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>bbc92fb5-3baa-4324-aa31-b3ac1fecc7ab</guid>
                    <versionId>b4456280-4f19-40bd-bcf8-b8d7dffab1d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6be570b2-8293-4140-8de8-04a99f1fc340</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>Output Mapping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.19faf6ef-382f-4a9e-bc0a-c1c709726ecf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:757d</guid>
            <versionId>62d2e51a-e864-475b-9123-0dde89c38efb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="509" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3b</errorHandlerItem>
                <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topRight" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.19faf6ef-382f-4a9e-bc0a-c1c709726ecf</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.bills[0].rows.listLength&gt;0) {&#xD;
	tw.local.results = 1;&#xD;
}&#xD;
else{&#xD;
	tw.local.results = 0;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>fd63fcca-823f-4225-bb57-b17a0091bac8</guid>
                <versionId>2c82e510-f6a3-4eb4-ac34-93b7f34ead9d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7e897d91-3a24-4a18-a60d-e968b0a42cde</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:752d</guid>
            <versionId>cceda5d8-27be-4831-9e63-dee0715456fc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.fc57949b-3f2f-4631-ad11-a812d1fbd1e6</processItemPrePostId>
                <processItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>c253a208-a117-4d6f-a493-bb56e1906d39</guid>
                <versionId>037622d5-5e8c-4651-b4e5-4f5b18da08ab</versionId>
            </processPrePosts>
            <layoutData x="82" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Copy of Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3b</errorHandlerItem>
                <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7e897d91-3a24-4a18-a60d-e968b0a42cde</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.billNumber = tw.local.Seperated[1];&#xD;
</script>
                <isRule>false</isRule>
                <guid>a1085ae8-2341-4202-91a5-3161295de362</guid>
                <versionId>d194a0ce-1bc1-4514-9a02-6e5d467e818d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.05d929c0-2e49-4736-a71b-fc2206b67813</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3b</guid>
            <versionId>da1bdf8f-52cc-4c76-a267-181e5948bfed</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="286" y="170">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.05d929c0-2e49-4736-a71b-fc2206b67813</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>f86a44a7-c1d6-411c-822c-3c89f1369bee</guid>
                <versionId>b0c5e931-c48e-45e5-adc4-b731d888c549</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.87ad2d23-51c0-49dd-8433-4cb907c9b6d4</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.dff0674e-c8a4-470a-b4c9-dfb04b793584</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:74dc</guid>
            <versionId>e979c40e-bc36-441b-a43f-233fe88974ff</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.dff0674e-c8a4-470a-b4c9-dfb04b793584</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8d998583-68b6-4119-8f21-448772af56d0</guid>
                <versionId>26a11688-fda2-483a-b9cf-ab3b26a87246</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c176a079-113c-4f15-8489-7b29872fb5a5</processItemId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <name>Init SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.dd619e12-236b-43bf-9c1e-8a3bd44b7383</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:74e0</guid>
            <versionId>e9ae4394-0086-4b87-8e59-8b2f0be8a99d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="207" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d3b</errorHandlerItem>
                <errorHandlerItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.dd619e12-236b-43bf-9c1e-8a3bd44b7383</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_BILLS WHERE BILL_OF_LADING_REF = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value=tw.local.billNumber;&#xD;
tw.local.parameters[0].type = "VARCHAR";&#xD;
tw.local.parameters[1].value=tw.local.requestID;&#xD;
tw.local.parameters[1].type = "INTEGER";</script>
                <isRule>false</isRule>
                <guid>b1424c0d-**************-c4d11f708f0f</guid>
                <versionId>4933a66a-b2a8-40b0-95da-58e39ce898b8</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Bill" id="1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.89765247-1a03-4410-8e29-3b9aabd169df">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"40-123"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.47017165-35f1-4db2-888e-6a34754f2050" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.0b75c02d-797c-420e-899f-eaf78d6c73fb" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="66aa03c5-3fd2-4be9-8f19-78ac0ef91c7c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="0495640a-63e1-46d3-8375-cec88e0c61dd" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>02e7811f-a6b8-4a54-8e84-dd1538f5e561</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>87ad2d23-51c0-49dd-8433-4cb907c9b6d4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c176a079-113c-4f15-8489-7b29872fb5a5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bf7390f1-d2ae-4490-86ba-37f6f67ac564</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d7e848e9-feab-43ec-8fac-9bcdea0629b6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cdaed79c-a35a-4319-80b3-e6fc80816e66</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6be570b2-8293-4140-8de8-04a99f1fc340</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>17813b06-58a1-420b-8afd-f0cbcedf8179</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>50607b70-c022-49f8-83d1-6e47b658098b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cb5e5e26-ea23-47ff-8697-702fc221943d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b2ebf717-f910-4c54-82bc-9192546a185f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="02e7811f-a6b8-4a54-8e84-dd1538f5e561">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.8652528b-8510-4609-8fad-2ef95448055d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="87ad2d23-51c0-49dd-8433-4cb907c9b6d4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:74dc</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8adb9b50-a60c-4167-88c7-4f68be4efb01</ns16:incoming>
                        
                        
                        <ns16:incoming>e8602f68-f7b2-4eaf-89d4-640c93f99afb</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="02e7811f-a6b8-4a54-8e84-dd1538f5e561" targetRef="d7e848e9-feab-43ec-8fac-9bcdea0629b6" name="To Init SQL Query" id="2027.8652528b-8510-4609-8fad-2ef95448055d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL Query" id="c176a079-113c-4f15-8489-7b29872fb5a5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="207" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ec6a3114-6c9a-4457-8cad-9b184f37445e</ns16:incoming>
                        
                        
                        <ns16:outgoing>78621177-b9e8-468d-80a7-1c42fd26d748</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "SELECT IDC_REQUEST_ID FROM bpm.IDC_REQUEST_BILLS WHERE BILL_OF_LADING_REF = ? AND IDC_REQUEST_ID NOT LIKE ? AND IDC_REQUEST_ID NOT IN (SELECT ID FROM bpm.IDC_REQUEST_DETAILS WHERE REQUEST_STATUS = 'Terminated' OR REQUEST_STATUS = 'Canceled');";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[1]=new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value=tw.local.billNumber;&#xD;
tw.local.parameters[0].type = "VARCHAR";&#xD;
tw.local.parameters[1].value=tw.local.requestID;&#xD;
tw.local.parameters[1].type = "INTEGER";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c176a079-113c-4f15-8489-7b29872fb5a5" targetRef="bf7390f1-d2ae-4490-86ba-37f6f67ac564" name="To SQL Execute Statement" id="78621177-b9e8-468d-80a7-1c42fd26d748">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="bf7390f1-d2ae-4490-86ba-37f6f67ac564">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>78621177-b9e8-468d-80a7-1c42fd26d748</ns16:incoming>
                        
                        
                        <ns16:outgoing>1c8668f8-b942-42a7-8c34-6bafdad2bc86</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.bills</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="bf7390f1-d2ae-4490-86ba-37f6f67ac564" targetRef="6be570b2-8293-4140-8de8-04a99f1fc340" name="To Output Mapping" id="1c8668f8-b942-42a7-8c34-6bafdad2bc86">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.821cb4fd-2072-471d-8878-10d40a2761c4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.76898369-69c5-442a-892f-c02c7f99e03e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="bills" id="2056.d2e0978f-fd42-4d57-8b35-f1ef6a670410">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false" />
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="d7e848e9-feab-43ec-8fac-9bcdea0629b6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="82" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.8652528b-8510-4609-8fad-2ef95448055d</ns16:incoming>
                        
                        
                        <ns16:outgoing>ec6a3114-6c9a-4457-8cad-9b184f37445e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.billNumber = tw.local.Seperated[1];&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="d7e848e9-feab-43ec-8fac-9bcdea0629b6" parallelMultiple="false" name="Copy of Error" id="cdaed79c-a35a-4319-80b3-e6fc80816e66">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="117" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8881c850-862c-460f-8a07-7409b4294a70</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="cfbf9a65-3b99-45f4-8419-781bb5aaa647" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="cdd46dd0-810e-414d-892a-a90dc99598c5" eventImplId="e7639ecf-c87b-4fcd-81af-719de2140d97">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="d7e848e9-feab-43ec-8fac-9bcdea0629b6" targetRef="c176a079-113c-4f15-8489-7b29872fb5a5" name="To Init SQL Query" id="ec6a3114-6c9a-4457-8cad-9b184f37445e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.64531898-1adc-4ec4-80a8-e69cafaaf30b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestID" id="2056.e77cdb5e-dcc7-4673-8324-8431f30bec19" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="billNumber" id="2056.08ad0ffe-6fd1-47fd-8f02-8470d8d92dd0" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Output Mapping" id="6be570b2-8293-4140-8de8-04a99f1fc340">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="509" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1c8668f8-b942-42a7-8c34-6bafdad2bc86</ns16:incoming>
                        
                        
                        <ns16:outgoing>8adb9b50-a60c-4167-88c7-4f68be4efb01</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.bills[0].rows.listLength&gt;0) {&#xD;
	tw.local.results = 1;&#xD;
}&#xD;
else{&#xD;
	tw.local.results = 0;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6be570b2-8293-4140-8de8-04a99f1fc340" targetRef="87ad2d23-51c0-49dd-8433-4cb907c9b6d4" name="To SQL Execute Multiple Statements (SQLResult)" id="8adb9b50-a60c-4167-88c7-4f68be4efb01">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c176a079-113c-4f15-8489-7b29872fb5a5" parallelMultiple="false" name="Error" id="17813b06-58a1-420b-8afd-f0cbcedf8179">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="242" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>75ea5b97-c622-480f-8e05-1dfb71e211f3</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="541c7360-98bf-4004-84f4-2c509d3e93c2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9ada05c8-4ef4-4b8f-8ff3-9ff761acdc01" eventImplId="6af20c49-b69e-4b7b-8e62-0a044ad97444">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="bf7390f1-d2ae-4490-86ba-37f6f67ac564" parallelMultiple="false" name="Error1" id="50607b70-c022-49f8-83d1-6e47b658098b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f7c73f90-70fc-45ed-8abd-d29bd0022ad4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3ad5ee7e-598d-4faa-8f89-26e08106baff" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="57f69f13-e31e-4095-8782-87e6d00264e9" eventImplId="27eca960-9dfb-4ed4-8471-5e7c6c4c68eb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6be570b2-8293-4140-8de8-04a99f1fc340" parallelMultiple="false" name="Error2" id="cb5e5e26-ea23-47ff-8697-702fc221943d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="544" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4059b2b5-931b-4606-8431-6b8e4853f59e</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c88c4678-1287-4230-8b6a-ce4203923dd4" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fd82642b-5b39-40ab-84b1-9ba4697c368d" eventImplId="2b96066c-cc78-491d-86a8-efc2ded3a828">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.e0487ee9-88a2-487d-83ec-9756a177cfd4" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="b2ebf717-f910-4c54-82bc-9192546a185f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="286" y="170" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8881c850-862c-460f-8a07-7409b4294a70</ns16:incoming>
                        
                        
                        <ns16:incoming>75ea5b97-c622-480f-8e05-1dfb71e211f3</ns16:incoming>
                        
                        
                        <ns16:incoming>f7c73f90-70fc-45ed-8abd-d29bd0022ad4</ns16:incoming>
                        
                        
                        <ns16:incoming>4059b2b5-931b-4606-8431-6b8e4853f59e</ns16:incoming>
                        
                        
                        <ns16:outgoing>e8602f68-f7b2-4eaf-89d4-640c93f99afb</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="cdaed79c-a35a-4319-80b3-e6fc80816e66" targetRef="b2ebf717-f910-4c54-82bc-9192546a185f" name="To Catch Errors" id="8881c850-862c-460f-8a07-7409b4294a70">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="17813b06-58a1-420b-8afd-f0cbcedf8179" targetRef="b2ebf717-f910-4c54-82bc-9192546a185f" name="To Catch Errors" id="75ea5b97-c622-480f-8e05-1dfb71e211f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="50607b70-c022-49f8-83d1-6e47b658098b" targetRef="b2ebf717-f910-4c54-82bc-9192546a185f" name="To Catch Errors" id="f7c73f90-70fc-45ed-8abd-d29bd0022ad4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="cb5e5e26-ea23-47ff-8697-702fc221943d" targetRef="b2ebf717-f910-4c54-82bc-9192546a185f" name="To Catch Errors" id="4059b2b5-931b-4606-8431-6b8e4853f59e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b2ebf717-f910-4c54-82bc-9192546a185f" targetRef="87ad2d23-51c0-49dd-8433-4cb907c9b6d4" name="To End" id="e8602f68-f7b2-4eaf-89d4-640c93f99afb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.6b0c50be-5bcb-4c3c-8777-a38f7c5a15a1" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Init SQL Query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ec6a3114-6c9a-4457-8cad-9b184f37445e</processLinkId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c176a079-113c-4f15-8489-7b29872fb5a5</toProcessItemId>
            <guid>8c081000-3753-4180-8fce-de0f840792f7</guid>
            <versionId>25cd9f03-07d7-4e3e-b277-a5a6d0ddc52b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d7e848e9-feab-43ec-8fac-9bcdea0629b6</fromProcessItemId>
            <toProcessItemId>2025.c176a079-113c-4f15-8489-7b29872fb5a5</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8adb9b50-a60c-4167-88c7-4f68be4efb01</processLinkId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6be570b2-8293-4140-8de8-04a99f1fc340</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.87ad2d23-51c0-49dd-8433-4cb907c9b6d4</toProcessItemId>
            <guid>1e4ae8ea-32e7-4abd-aba2-c55ac8198cc5</guid>
            <versionId>314b7ab2-aa9f-4a95-8f86-018a3e2643b2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6be570b2-8293-4140-8de8-04a99f1fc340</fromProcessItemId>
            <toProcessItemId>2025.87ad2d23-51c0-49dd-8433-4cb907c9b6d4</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.78621177-b9e8-468d-80a7-1c42fd26d748</processLinkId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c176a079-113c-4f15-8489-7b29872fb5a5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bf7390f1-d2ae-4490-86ba-37f6f67ac564</toProcessItemId>
            <guid>203ef31e-8ec7-44c0-8748-c1dd1f778f95</guid>
            <versionId>707e523a-ce02-4992-96c4-22cb08a47e6a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c176a079-113c-4f15-8489-7b29872fb5a5</fromProcessItemId>
            <toProcessItemId>2025.bf7390f1-d2ae-4490-86ba-37f6f67ac564</toProcessItemId>
        </link>
        <link name="To Output Mapping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1c8668f8-b942-42a7-8c34-6bafdad2bc86</processLinkId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.bf7390f1-d2ae-4490-86ba-37f6f67ac564</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.6be570b2-8293-4140-8de8-04a99f1fc340</toProcessItemId>
            <guid>1c1f938d-960b-4800-a771-4875d396ec10</guid>
            <versionId>b29c1d73-14cf-492b-a2c5-7031b89a3417</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.bf7390f1-d2ae-4490-86ba-37f6f67ac564</fromProcessItemId>
            <toProcessItemId>2025.6be570b2-8293-4140-8de8-04a99f1fc340</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e8602f68-f7b2-4eaf-89d4-640c93f99afb</processLinkId>
            <processId>1.0edd3d3e-45ce-4d92-9d7a-fb84c86a1488</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.87ad2d23-51c0-49dd-8433-4cb907c9b6d4</toProcessItemId>
            <guid>30da1f21-0ba4-4f6d-add1-70f47617193f</guid>
            <versionId>ce5c49c3-1950-4efb-bf91-f66596898930</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.b2ebf717-f910-4c54-82bc-9192546a185f</fromProcessItemId>
            <toProcessItemId>2025.87ad2d23-51c0-49dd-8433-4cb907c9b6d4</toProcessItemId>
        </link>
    </process>
</teamworks>

