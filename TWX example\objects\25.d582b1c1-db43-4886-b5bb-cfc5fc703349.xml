<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.d582b1c1-db43-4886-b5bb-cfc5fc703349" name="IDC Reversal">
        <lastModified>1688661261536</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <bpdId>25.d582b1c1-db43-4886-b5bb-cfc5fc703349</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="bc6c9d75-a278-4533-901f-73ce5514ad07" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="IDC Reversal" id="25.d582b1c1-db43-4886-b5bb-cfc5fc703349" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot;IDC Withdrawal:&amp;quot; + tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="true" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1684076750388161688664857789"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="c04b97d7-3288-48f8-b3c9-9783f97d29c0" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.14b71d5c-752c-49c4-afba-cff5d026e9cd" epvProcessLinkId="b4a0db41-a37c-499a-807c-c66f18670b36" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="0afe161e-787f-43ff-840a-1f1723add6ed" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="23ea615a-807c-4f94-b559-a03150f850d4" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="c919e54f-8708-4b12-a044-145b89c18d64"&gt;&lt;ns15:lane name="Execution Hub Maker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="6c5b18c1-a97d-4c17-9b7f-b2df082ae6d3" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;c89befac-6937-410d-b2e5-9c90ac07b46e&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8fdf57ad-7e7b-4e9d-9455-a4e2125f257b&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Checker" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="0985dba9-683c-40a0-882f-82de34165182" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;ee4ff23b-d4a1-4503-8a63-c683cffb7fe6&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;d6df9dc1-02db-4cd3-a858-cca650366d6d&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="ad44fe7c-c85c-40e7-942a-ebd9f96bb2bd" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="c89befac-6937-410d-b2e5-9c90ac07b46e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;ce87703e-5665-4e49-b4de-f09454477680&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;ce87703e-5665-4e49-b4de-f09454477680&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="Approved / Canceled" id="d6df9dc1-02db-4cd3-a858-cca650366d6d"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="911" y="68" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;e79149ba-8309-4e7b-8483-14be4e32292b&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="c89befac-6937-410d-b2e5-9c90ac07b46e" targetRef="8fdf57ad-7e7b-4e9d-9455-a4e2125f257b" name="To Create IDC Reversal Request " id="ce87703e-5665-4e49-b4de-f09454477680"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.7eee0980-1508-423c-95a0-c08797e92721" default="8fe5969f-1d93-4bee-9756-5dbad522be55" name="Create IDC Reversal Request " id="8fdf57ad-7e7b-4e9d-9455-a4e2125f257b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="291" y="57" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create IDC Reversal Request – إنشاء طلب اعادة قيد تحصيل مستندى استيراد &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;ce87703e-5665-4e49-b4de-f09454477680&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;ac2955b2-cbbe-4117-821f-fcf7cf57f224&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;8fe5969f-1d93-4bee-9756-5dbad522be55&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.08987844-87e6-4b47-bfda-027fbffde9b4&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474"&gt;tw.local.idcReversalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.20977cc3-c153-4a4d-ab6d-c1f2c594370b&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474"&gt;tw.local.idcReversalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.98cf1a97-9e24-44c6-9e06-44065464f45b" default="cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089" name="Review IDC Reversal Request " id="ee4ff23b-d4a1-4503-8a63-c683cffb7fe6"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="418" y="44" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review IDC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى استيراد &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;8fe5969f-1d93-4bee-9756-5dbad522be55&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9ce07be7-7b8a-4d2a-90e5-6c29e200aaf3&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474"&gt;tw.local.idcReversalRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.55d25bea-ef33-46d8-a18d-25074e74d44f&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474"&gt;tw.local.idcReversalRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:exclusiveGateway default="e79149ba-8309-4e7b-8483-14be4e32292b" name="Is Approved" id="ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="715" y="63" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;ac2955b2-cbbe-4117-821f-fcf7cf57f224&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;e79149ba-8309-4e7b-8483-14be4e32292b&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="8fdf57ad-7e7b-4e9d-9455-a4e2125f257b" targetRef="ee4ff23b-d4a1-4503-8a63-c683cffb7fe6" name="To Review IDC Reversal Request " id="8fe5969f-1d93-4bee-9756-5dbad522be55"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="ee4ff23b-d4a1-4503-8a63-c683cffb7fe6" targetRef="ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c" name="To Is Approved" id="cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c" targetRef="8fdf57ad-7e7b-4e9d-9455-a4e2125f257b" name="To Create IDC Reversal Request " id="ac2955b2-cbbe-4117-821f-fcf7cf57f224"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:customBendPoint x="655" y="344" /&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcReversalRequest.appInfo.subStatus	  ==	  tw.epv.IDCReversalSubStatus.returnedToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c" targetRef="d6df9dc1-02db-4cd3-a858-cca650366d6d" name="To Approved / Canceled" id="e79149ba-8309-4e7b-8483-14be4e32292b"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.idcReversalRequest.subStatus	  ==	  "Null"&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.36df9eba-480f-4caa-9609-6a99e6f2c474" isCollection="false" name="idcReversalRequest" id="8f583d3b-0062-4754-9bc6-530ba474352b" /&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef" /&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef" /&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="IDC WithdrawalInterface" id="2ab4cbcf-fba9-4154-9e1a-dbef4b67b67e" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6603</guid>
        <versionId>789eb2c2-20f4-443c-ab44-4eeb9b50209f</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:4042929508233c47:-1c6e1647:188f04d39d0:6272">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>IDC Reversal</name>
            <documentation></documentation>
            <name>IDC Reversal</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>eslam</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1688664857793</creationDate>
            <modificationDate>1688666092043</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"IDC Withdrawal:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="c919e54f-8708-4b12-a044-145b89c18d64" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ff" />
            <ownerTeamInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6600" />
            <simulationScenario id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6088">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1688664861535</startTime>
            </simulationScenario>
            <flow id="e79149ba-8309-4e7b-8483-14be4e32292b" connectionType="SequenceFlow">
                <name>To Approved / Canceled</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e8">
                        <expression>tw.local.idcReversalRequest.subStatus	  ==	  "Null"</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="8fe5969f-1d93-4bee-9756-5dbad522be55" connectionType="SequenceFlow">
                <name>To Review IDC Reversal Request </name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6087" />
                </connection>
            </flow>
            <flow id="ac2955b2-cbbe-4117-821f-fcf7cf57f224" connectionType="SequenceFlow">
                <name>To Create IDC Reversal Request </name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ea">
                        <expression>tw.local.idcReversalRequest.appInfo.subStatus	  ==	  tw.epv.IDCReversalSubStatus.returnedToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="ce87703e-5665-4e49-b4de-f09454477680" connectionType="SequenceFlow">
                <name>To Create IDC Reversal Request </name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6086" />
                </connection>
            </flow>
            <flow id="cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089" connectionType="SequenceFlow">
                <name>To Is Approved</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6085" />
                </connection>
            </flow>
            <pool id="c919e54f-8708-4b12-a044-145b89c18d64">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at1684076750388161688664857789</restrictedName>
                <dimension>
                    <size w="3000" h="400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="6c5b18c1-a97d-4c17-9b7f-b2df082ae6d3">
                    <name>Execution Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="8fdf57ad-7e7b-4e9d-9455-a4e2125f257b" componentType="Activity">
                        <name>Create IDC Reversal Request </name>
                        <position>
                            <location x="291" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.7eee0980-1508-423c-95a0-c08797e92721</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create IDC Reversal Request – إنشاء طلب اعادة قيد تحصيل مستندى استيراد </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65fc">
                                    <name>idcReversalRequest</name>
                                    <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
                                    <input>true</input>
                                    <value>tw.local.idcReversalRequest</value>
                                    <parameterId>2055.08987844-87e6-4b47-bfda-027fbffde9b4</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65fb">
                                    <name>idcReversalRequest</name>
                                    <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
                                    <input>true</input>
                                    <value>tw.local.idcReversalRequest</value>
                                    <parameterId>2055.20977cc3-c153-4a4d-ab6d-c1f2c594370b</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65fa">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f9">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f2">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="ce87703e-5665-4e49-b4de-f09454477680" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65eb">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="ac2955b2-cbbe-4117-821f-fcf7cf57f224" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f1">
                            <positionId>rightCenter</positionId>
                            <flow ref="8fe5969f-1d93-4bee-9756-5dbad522be55" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="c89befac-6937-410d-b2e5-9c90ac07b46e" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f3">
                            <positionId>rightCenter</positionId>
                            <flow ref="ce87703e-5665-4e49-b4de-f09454477680" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="0985dba9-683c-40a0-882f-82de34165182">
                    <name>Execution Hub Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="ee4ff23b-d4a1-4503-8a63-c683cffb7fe6" componentType="Activity">
                        <name>Review IDC Reversal Request </name>
                        <position>
                            <location x="418" y="44" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.98cf1a97-9e24-44c6-9e06-44065464f45b</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى استيراد </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f7">
                                    <name>idcReversalRequest</name>
                                    <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
                                    <input>true</input>
                                    <value>tw.local.idcReversalRequest</value>
                                    <parameterId>2055.9ce07be7-7b8a-4d2a-90e5-6c29e200aaf3</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f6">
                                    <name>idcReversalRequest</name>
                                    <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
                                    <input>true</input>
                                    <value>tw.local.idcReversalRequest</value>
                                    <parameterId>2055.55d25bea-ef33-46d8-a18d-25074e74d44f</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f5">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f4">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65f0">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="8fe5969f-1d93-4bee-9756-5dbad522be55" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ef">
                            <positionId>rightCenter</positionId>
                            <flow ref="cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="d6df9dc1-02db-4cd3-a858-cca650366d6d" componentType="Event">
                        <name>Approved / Canceled</name>
                        <documentation></documentation>
                        <position>
                            <location x="911" y="68" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e9">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="e79149ba-8309-4e7b-8483-14be4e32292b" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="ec4a2c94-bf39-45ec-a56f-6028f7cf1f3c" componentType="Gateway">
                        <name>Is Approved</name>
                        <documentation></documentation>
                        <position>
                            <location x="715" y="63" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ee">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="cfe1a05a-c0e3-49c5-b14b-dc9ee39a6089" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ed">
                            <positionId>bottomCenter</positionId>
                            <flow ref="ac2955b2-cbbe-4117-821f-fcf7cf57f224" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65ec">
                            <positionId>rightCenter</positionId>
                            <flow ref="e79149ba-8309-4e7b-8483-14be4e32292b" />
                        </outputPort>
                    </flowObject>
                </lane>
                <privateVariable id="8f583d3b-0062-4754-9bc6-530ba474352b">
                    <name>idcReversalRequest</name>
                    <description></description>
                    <classId>/12.36df9eba-480f-4caa-9609-6a99e6f2c474</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65fe">
                    <epvId>/21.14b71d5c-752c-49c4-afba-cff5d026e9cd</epvId>
                </epv>
            </pool>
            <extension id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-65e7" type="CASE">
                <caseFolder id="c04b97d7-3288-48f8-b3c9-9783f97d29c0">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

