<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b16e9737-5d24-4229-83f7-2e9dc0637416" name="Get Product Codes">
        <lastModified>1692507007057</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.96e465ab-a70e-49aa-b700-f71f118f71cb</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>a0df24a9-0d8d-40a9-b542-a252c17b9b10</guid>
        <versionId>94cb3c17-ad1a-444c-9443-0a79e03f9a2e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec7" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f15d3e18-7440-4eb8-b111-0d6c120690a8"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":77,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"e5d19efb-17cc-4154-a6bd-edf3a1bf26ff"},{"incoming":["05dbd6f9-6ddf-4696-9e37-5eba8ec19d92","8473a2fa-1866-4013-8848-4308fe1755fc"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":654,"y":33,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6140"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"64312887-8ce6-4182-ab7c-70bbb35797f9"},{"targetRef":"96e465ab-a70e-49aa-b700-f71f118f71cb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get product codes ","declaredType":"sequenceFlow","id":"2027.f15d3e18-7440-4eb8-b111-0d6c120690a8","sourceRef":"e5d19efb-17cc-4154-a6bd-edf3a1bf26ff"},{"startQuantity":1,"outgoing":["c6fb27fd-a9de-4a2f-9766-25a24568adbd"],"incoming":["2027.f15d3e18-7440-4eb8-b111-0d6c120690a8"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":54,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get product codes ","dataInputAssociation":[{"targetRef":"2055.d98c668e-db67-401b-8e84-cc98608569ea","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.prefix"]}}]},{"targetRef":"2055.a1145555-7f2d-469e-89ad-87ef52093009","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.userID"]}}]},{"targetRef":"2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceID"]}}]},{"targetRef":"2055.409793fb-5b0c-4dc8-8d73-474de2f1d809","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.processName"]}}]},{"targetRef":"2055.97f65893-b0d8-4cd1-8557-a3e44250b84f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.snapshot"]}}]},{"targetRef":"2055.1019293e-e09f-4c51-84c4-e3e8aa04849c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestAppID"]}}]},{"targetRef":"2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BICO\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"96e465ab-a70e-49aa-b700-f71f118f71cb","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.033d5d03-1e28-4166-8adf-83166aef740a","declaredType":"TFormalExpression","content":["tw.local.mwProductDetails"]}}],"sourceRef":["2055.7cbc47bc-3460-4dab-84af-9838e8e29b10"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.b78f8c49-b985-497c-8134-903ee8801680"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.16a42fb6-68f4-443b-836a-521a420d5cb3"]}],"calledElement":"1.757041b2-81d0-44c8-9d96-c2bbdf22a853"},{"targetRef":"ae7f70aa-919a-4381-8263-377b33c37a40","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"c6fb27fd-a9de-4a2f-9766-25a24568adbd","sourceRef":"96e465ab-a70e-49aa-b700-f71f118f71cb"},{"startQuantity":1,"outgoing":["05dbd6f9-6ddf-4696-9e37-5eba8ec19d92"],"incoming":["666e1faf-b2d6-49ce-8898-bbf851285f96"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":484,"y":10,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Product Codes","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"245f9385-224e-4431-b3e6-ae1f6e6189ef","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.listOf.DBLookup();\r\n\tfor (var i=0; i&lt;tw.local.mwProductDetails.listLength; i++) {\r\n\t\ttw.local.results[i] = {};\r\n\t\ttw.local.results[i].code = tw.local.mwProductDetails[i].productCode;\r\n\t\ttw.local.results[i].englishdescription = tw.local.mwProductDetails[i].englishName;\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}"]}},{"targetRef":"64312887-8ce6-4182-ab7c-70bbb35797f9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Product Codes","declaredType":"sequenceFlow","id":"05dbd6f9-6ddf-4696-9e37-5eba8ec19d92","sourceRef":"245f9385-224e-4431-b3e6-ae1f6e6189ef"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEINTT.MW_ProductDetails();\nautoObject[0] = new tw.object.toolkit.NBEINTT.MW_ProductDetails();\nautoObject[0].productCode = \"\";\nautoObject[0].englishName = \"\";\nautoObject[0].arabicName = \"\";\nautoObject[0].productGroup = \"\";\nautoObject[0].productGroupDescription = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.033d5d03-1e28-4166-8adf-83166aef740a","name":"mwProductDetails","isCollection":true,"declaredType":"dataObject","id":"2056.944e1564-ced1-4d42-857e-a7f85740403e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.4a924895-a490-4626-b3de-d1daa7313c0b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.670e5c76-7acc-4ab4-9e9d-68c764efa42a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.ce5f21f6-781f-49de-b591-7c34677410c0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.d2db910f-1258-47a3-b7b1-c9bbea53fa5c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.ac5369da-4884-44c9-ac6d-73617da22d6b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.04092bdc-b905-45be-8ed4-371861cc4e2d"},{"parallelMultiple":false,"outgoing":["f8e29354-979a-4886-824a-09fafe70ddf7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"57dae90e-9ac5-41b0-819a-b8f937aebc39"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a0ec151f-8732-40ac-8c13-6af7212c39f4","otherAttributes":{"eventImplId":"f6c15d17-dd2b-4015-8d0d-716d369a2aed"}}],"attachedToRef":"96e465ab-a70e-49aa-b700-f71f118f71cb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":212,"y":112,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"f2aed8b2-e427-4840-85d5-d7a61505ea69","outputSet":{}},{"parallelMultiple":false,"outgoing":["a6e58995-0094-4cb0-814d-ddcebe0605ff"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8f8e986c-b9b1-4174-8e5c-db5090d9af6e"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"acf7fb9d-a675-446f-848e-7408e785bb1a","otherAttributes":{"eventImplId":"d1fe9285-c607-4fe0-8c6e-7c17119eb797"}}],"attachedToRef":"245f9385-224e-4431-b3e6-ae1f6e6189ef","extensionElements":{"nodeVisualInfo":[{"width":24,"x":519,"y":68,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"a4b18752-3d99-4711-85ef-61598c6a3108","outputSet":{}},{"targetRef":"0170477e-3388-4630-856d-e46f47a3b0e2","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f8e29354-979a-4886-824a-09fafe70ddf7","sourceRef":"f2aed8b2-e427-4840-85d5-d7a61505ea69"},{"targetRef":"0170477e-3388-4630-856d-e46f47a3b0e2","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a6e58995-0094-4cb0-814d-ddcebe0605ff","sourceRef":"a4b18752-3d99-4711-85ef-61598c6a3108"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.ff9edf5d-1df2-4e4b-8902-f276a2086ccd"},{"outgoing":["666e1faf-b2d6-49ce-8898-bbf851285f96","1bbe33b5-ad70-4057-8c82-7d347890a5f2"],"incoming":["c6fb27fd-a9de-4a2f-9766-25a24568adbd"],"default":"666e1faf-b2d6-49ce-8898-bbf851285f96","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":349,"y":94,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"ae7f70aa-919a-4381-8263-377b33c37a40"},{"targetRef":"245f9385-224e-4431-b3e6-ae1f6e6189ef","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map Product Codes","declaredType":"sequenceFlow","id":"666e1faf-b2d6-49ce-8898-bbf851285f96","sourceRef":"ae7f70aa-919a-4381-8263-377b33c37a40"},{"targetRef":"0170477e-3388-4630-856d-e46f47a3b0e2","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"1bbe33b5-ad70-4057-8c82-7d347890a5f2","sourceRef":"ae7f70aa-919a-4381-8263-377b33c37a40"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.35b0542a-90af-4505-8b47-c6fcf7d07378"},{"startQuantity":1,"outgoing":["8473a2fa-1866-4013-8848-4308fe1755fc"],"incoming":["1bbe33b5-ad70-4057-8c82-7d347890a5f2","a6e58995-0094-4cb0-814d-ddcebe0605ff","f8e29354-979a-4886-824a-09fafe70ddf7"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":361,"y":198,"declaredType":"TNodeVisualInfo","height":69}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0170477e-3388-4630-856d-e46f47a3b0e2","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"64312887-8ce6-4182-ab7c-70bbb35797f9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8473a2fa-1866-4013-8848-4308fe1755fc","sourceRef":"0170477e-3388-4630-856d-e46f47a3b0e2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.9a77c978-4e95-4775-8da8-3872cf940cd9"}],"laneSet":[{"id":"81b875fa-e50a-47e1-94ef-001e98a4201f","lane":[{"flowNodeRef":["e5d19efb-17cc-4154-a6bd-edf3a1bf26ff","64312887-8ce6-4182-ab7c-70bbb35797f9","96e465ab-a70e-49aa-b700-f71f118f71cb","245f9385-224e-4431-b3e6-ae1f6e6189ef","f2aed8b2-e427-4840-85d5-d7a61505ea69","a4b18752-3d99-4711-85ef-61598c6a3108","ae7f70aa-919a-4381-8263-377b33c37a40","0170477e-3388-4630-856d-e46f47a3b0e2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":299}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"a9edfe5a-e2b1-4c53-b8d4-b430bf0ffc4e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Product Codes","declaredType":"process","id":"1.b16e9737-5d24-4229-83f7-2e9dc0637416","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.47c71a97-**************-a930ea9643bc"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.f821694c-b790-4833-827c-1466bfb443e5"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.47c71a97-**************-a930ea9643bc","2055.f821694c-b790-4833-827c-1466bfb443e5"]}],"dataInput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.411c1bda-80b1-4831-86f1-913523af5296"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.411c1bda-80b1-4831-86f1-913523af5296</processParameterId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>683bfe1a-b2fb-4e85-b1d8-c62a0978f883</guid>
            <versionId>865dfeaf-1870-4578-b02d-cb1ca15dab2b</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.47c71a97-**************-a930ea9643bc</processParameterId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2ef7ee9f-bd1d-443f-b6c3-af765b52e38c</guid>
            <versionId>77061859-c454-49d0-8442-22e955144770</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f821694c-b790-4833-827c-1466bfb443e5</processParameterId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b274570b-3c49-4380-b5ce-9922441e206c</guid>
            <versionId>7370f857-3d4e-46e8-86da-9cfb92ffa5ec</versionId>
        </processParameter>
        <processVariable name="mwProductDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.944e1564-ced1-4d42-857e-a7f85740403e</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.033d5d03-1e28-4166-8adf-83166aef740a</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEINTT.MW_ProductDetails();
autoObject[0] = new tw.object.toolkit.NBEINTT.MW_ProductDetails();
autoObject[0].productCode = "";
autoObject[0].englishName = "";
autoObject[0].arabicName = "";
autoObject[0].productGroup = "";
autoObject[0].productGroupDescription = "";
autoObject</defaultValue>
            <guid>570375c7-e806-487a-895b-3203e81ffaa4</guid>
            <versionId>4b9f230f-3c1a-4a3b-9541-85e46c47e623</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4a924895-a490-4626-b3de-d1daa7313c0b</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d77f6fd2-f428-4b6d-86e8-97b81d346cd4</guid>
            <versionId>92bf5a89-ae97-4692-b569-eee9bf5c296c</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.670e5c76-7acc-4ab4-9e9d-68c764efa42a</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bddb75fd-712b-46e1-ace8-922c64642453</guid>
            <versionId>57eed845-c0e3-4351-a915-65c74c9f20e1</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce5f21f6-781f-49de-b591-7c34677410c0</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ae31486d-6565-467e-9fec-3a8a7b50c809</guid>
            <versionId>d563a592-5831-4d60-9dd9-6dec6cf3b34f</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d2db910f-1258-47a3-b7b1-c9bbea53fa5c</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fa79d79f-ec2d-4d41-8867-d790c06da95a</guid>
            <versionId>9455b4b4-4e7f-47f5-97e1-100df38ff56b</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ac5369da-4884-44c9-ac6d-73617da22d6b</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e9fab174-dfcd-44a3-9816-b247f88024b1</guid>
            <versionId>0b12523f-a11d-473e-a3d4-c37370679fcb</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.04092bdc-b905-45be-8ed4-371861cc4e2d</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b1090e29-067a-4efa-9e00-77100fa5696a</guid>
            <versionId>17ad30ff-a774-476d-bfce-2760e8f939cc</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ff9edf5d-1df2-4e4b-8902-f276a2086ccd</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bf4c89ea-8836-4113-ad11-805524eb4115</guid>
            <versionId>25e7e02f-245a-4731-8af9-f6e7068473f0</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.35b0542a-90af-4505-8b47-c6fcf7d07378</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>22fd25b2-7715-42ff-9180-dde6bd9ca5fb</guid>
            <versionId>a347ccc2-fda8-498c-b14c-345735bbb3b6</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a77c978-4e95-4775-8da8-3872cf940cd9</processVariableId>
            <description isNull="true" />
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc6064b7-7f05-4763-90c0-d407de00dc1b</guid>
            <versionId>c7c75331-1396-47ba-ae92-2cbb37dc25e1</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</processItemId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8e9a6154-e29f-4842-bc76-95ed982fe20d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec1</guid>
            <versionId>3631c50e-0ff3-41c0-a0fd-8e36f4ccbc5e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="361" y="198">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8e9a6154-e29f-4842-bc76-95ed982fe20d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>936bdbd3-82c5-4f90-b806-3edf4d77e953</guid>
                <versionId>4f214f09-dc2f-47a8-9e1a-8196c2fc5127</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.96e465ab-a70e-49aa-b700-f71f118f71cb</processItemId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <name>Get product codes </name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6142</guid>
            <versionId>85b2ac3b-e322-461a-b30e-a82acf49a63d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="177" y="54">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec1</errorHandlerItem>
                <errorHandlerItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.757041b2-81d0-44c8-9d96-c2bbdf22a853</attachedProcessRef>
                <guid>4001e9bf-6dbf-4a4c-9111-df196ba5c5b3</guid>
                <versionId>2f5c0a50-2d92-435a-92a4-2c24bf34c284</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d1d74826-816f-43d8-82de-28f739d13123</parameterMappingId>
                    <processParameterId>2055.1019293e-e09f-4c51-84c4-e3e8aa04849c</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestAppID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f916f356-b27a-49cf-8d0d-79b16de829fd</guid>
                    <versionId>09f790fc-2ca0-47df-bc4c-c9027a31db7c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e193eb0a-1865-43c9-b7d6-13aa6d4f626e</parameterMappingId>
                    <processParameterId>2055.409793fb-5b0c-4dc8-8d73-474de2f1d809</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.processName</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c587d98b-41dd-4caa-89f6-eba9f1bd0959</guid>
                    <versionId>0bff4687-f643-4423-bb59-c376990232b2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b70ffa1c-be20-43c1-968b-a54c584d2e5d</parameterMappingId>
                    <processParameterId>2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a236091b-3646-48b8-862d-1a521fe4f350</guid>
                    <versionId>148e7f46-8cd1-45c6-a1a1-591874989b2f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cbb1a049-ff34-4c2d-b381-54c83dded714</parameterMappingId>
                    <processParameterId>2055.7cbc47bc-3460-4dab-84af-9838e8e29b10</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mwProductDetails</value>
                    <classRef>/12.033d5d03-1e28-4166-8adf-83166aef740a</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>af7be532-a2c7-4a68-8b3a-da77f0829710</guid>
                    <versionId>1e80aef6-5836-4560-bf78-e998c369bd0b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9bc64da6-1ec7-4b43-824f-80f75c5b5d4a</parameterMappingId>
                    <processParameterId>2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>32626424-2f03-4483-bcfe-3c952ca5b5f6</guid>
                    <versionId>2a8fd97c-6593-4f32-8521-daa9ee6f590d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.35924869-c07e-495e-82c5-3f6e324986aa</parameterMappingId>
                    <processParameterId>2055.97f65893-b0d8-4cd1-8557-a3e44250b84f</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.snapshot</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1f0b74bd-54c7-422d-a9d7-27f52a4d8078</guid>
                    <versionId>41157295-51ba-4da1-90a1-ead2c63dc95e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.64d17837-04a0-49f3-8f89-61c4e639dbe5</parameterMappingId>
                    <processParameterId>2055.b78f8c49-b985-497c-8134-903ee8801680</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4c26daa0-67a2-4adc-bd9f-72ae7b385376</guid>
                    <versionId>4cfcb4a3-0bf5-4063-9c0f-b088ec6c9406</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c24257eb-eb6f-4365-b238-a4d44e99c8e6</parameterMappingId>
                    <processParameterId>2055.16a42fb6-68f4-443b-836a-521a420d5cb3</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f5ad6679-3ac1-479b-85ba-424dc332c27b</guid>
                    <versionId>7504e609-8131-49ce-ab7f-e47c6f5811cc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7f88ed3e-3731-4ca9-904d-4be91e5c73f9</parameterMappingId>
                    <processParameterId>2055.d98c668e-db67-401b-8e84-cc98608569ea</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e07eb19f-b61c-45fe-aef3-f294f8bc7c07</guid>
                    <versionId>9862d028-1daf-45ff-932a-08f8408bb069</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productGroup">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6bcf8392-a2ef-4722-a66c-eb4a4544eb6d</parameterMappingId>
                    <processParameterId>2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"BICO"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2048e0fb-4dea-4e9b-9deb-f29a43acc05e</guid>
                    <versionId>ac1f4abe-58b0-4875-b00e-356f42ecc764</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.15f57cba-edcd-49fb-ac01-1427c49f53ea</parameterMappingId>
                    <processParameterId>2055.a1145555-7f2d-469e-89ad-87ef52093009</processParameterId>
                    <parameterMappingParentId>3012.f05dcfa6-ede2-47a4-9e86-154b40e4797d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.userID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3b3fae02-ec8f-4fce-934a-2bb3f5b5f70c</guid>
                    <versionId>c8da2ca7-b95c-42d6-8bf6-c46e6c98b10d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.64312887-8ce6-4182-ab7c-70bbb35797f9</processItemId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c9072efd-e692-4029-bda4-b6d57ba43a7f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6140</guid>
            <versionId>979df448-f907-41b8-bc9e-91e1deb03d6d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="654" y="33">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c9072efd-e692-4029-bda4-b6d57ba43a7f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>72c089f0-01c3-4a53-9b68-63b3b27731e1</guid>
                <versionId>5d6ed9ea-d326-4d0c-864c-ef6d4d322bc7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</processItemId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.80947ec9-18cc-4d22-8288-27ca4628b6a5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-ee0</guid>
            <versionId>ba0a595a-3495-42fe-93a9-f1d319453d4c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="349" y="94">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.80947ec9-18cc-4d22-8288-27ca4628b6a5</switchId>
                <guid>a4056c72-432f-457a-91ef-c42a02a2904d</guid>
                <versionId>dde05608-a726-4011-a195-7eebd317f2f7</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.bff06f86-c6f7-4784-9f34-f5c03367256c</switchConditionId>
                    <switchId>3013.80947ec9-18cc-4d22-8288-27ca4628b6a5</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec6</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>91cbcb63-119d-4f8f-931b-b1100b4fc63c</guid>
                    <versionId>54ae4aaf-32e8-4db6-8811-25155fcaa033</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.245f9385-224e-4431-b3e6-ae1f6e6189ef</processItemId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <name>Map Product Codes</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.801239a6-0834-4413-b045-4d14776256d2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6141</guid>
            <versionId>dab2a213-c74f-4287-be20-a47de05db5e3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="484" y="10">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec1</errorHandlerItem>
                <errorHandlerItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.801239a6-0834-4413-b045-4d14776256d2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.listOf.DBLookup();&#xD;
	for (var i=0; i&lt;tw.local.mwProductDetails.listLength; i++) {&#xD;
		tw.local.results[i] = {};&#xD;
		tw.local.results[i].code = tw.local.mwProductDetails[i].productCode;&#xD;
		tw.local.results[i].englishdescription = tw.local.mwProductDetails[i].englishName;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>b2276f2b-c14b-4b38-9dda-91e9bd1fee5d</guid>
                <versionId>a8fdb36f-90a9-4158-bb20-cf97d22e6d81</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.96e465ab-a70e-49aa-b700-f71f118f71cb</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Product Codes" id="1.b16e9737-5d24-4229-83f7-2e9dc0637416" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.411c1bda-80b1-4831-86f1-913523af5296" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.47c71a97-**************-a930ea9643bc" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.f821694c-b790-4833-827c-1466bfb443e5" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.47c71a97-**************-a930ea9643bc</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.f821694c-b790-4833-827c-1466bfb443e5</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="81b875fa-e50a-47e1-94ef-001e98a4201f">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="a9edfe5a-e2b1-4c53-b8d4-b430bf0ffc4e" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="299" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>e5d19efb-17cc-4154-a6bd-edf3a1bf26ff</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>64312887-8ce6-4182-ab7c-70bbb35797f9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>96e465ab-a70e-49aa-b700-f71f118f71cb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>245f9385-224e-4431-b3e6-ae1f6e6189ef</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f2aed8b2-e427-4840-85d5-d7a61505ea69</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a4b18752-3d99-4711-85ef-61598c6a3108</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ae7f70aa-919a-4381-8263-377b33c37a40</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0170477e-3388-4630-856d-e46f47a3b0e2</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="e5d19efb-17cc-4154-a6bd-edf3a1bf26ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="77" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.f15d3e18-7440-4eb8-b111-0d6c120690a8</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="64312887-8ce6-4182-ab7c-70bbb35797f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="654" y="33" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6140</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>05dbd6f9-6ddf-4696-9e37-5eba8ec19d92</ns16:incoming>
                        
                        
                        <ns16:incoming>8473a2fa-1866-4013-8848-4308fe1755fc</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e5d19efb-17cc-4154-a6bd-edf3a1bf26ff" targetRef="96e465ab-a70e-49aa-b700-f71f118f71cb" name="To Get product codes " id="2027.f15d3e18-7440-4eb8-b111-0d6c120690a8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.757041b2-81d0-44c8-9d96-c2bbdf22a853" name="Get product codes " id="96e465ab-a70e-49aa-b700-f71f118f71cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="54" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.f15d3e18-7440-4eb8-b111-0d6c120690a8</ns16:incoming>
                        
                        
                        <ns16:outgoing>c6fb27fd-a9de-4a2f-9766-25a24568adbd</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d98c668e-db67-401b-8e84-cc98608569ea</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a1145555-7f2d-469e-89ad-87ef52093009</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.userID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.409793fb-5b0c-4dc8-8d73-474de2f1d809</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.processName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.97f65893-b0d8-4cd1-8557-a3e44250b84f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.snapshot</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1019293e-e09f-4c51-84c4-e3e8aa04849c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestAppID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"BICO"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.7cbc47bc-3460-4dab-84af-9838e8e29b10</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.033d5d03-1e28-4166-8adf-83166aef740a">tw.local.mwProductDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.b78f8c49-b985-497c-8134-903ee8801680</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.16a42fb6-68f4-443b-836a-521a420d5cb3</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="96e465ab-a70e-49aa-b700-f71f118f71cb" targetRef="ae7f70aa-919a-4381-8263-377b33c37a40" name="To is Successful" id="c6fb27fd-a9de-4a2f-9766-25a24568adbd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Product Codes" id="245f9385-224e-4431-b3e6-ae1f6e6189ef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="484" y="10" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>666e1faf-b2d6-49ce-8898-bbf851285f96</ns16:incoming>
                        
                        
                        <ns16:outgoing>05dbd6f9-6ddf-4696-9e37-5eba8ec19d92</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.listOf.DBLookup();&#xD;
	for (var i=0; i&lt;tw.local.mwProductDetails.listLength; i++) {&#xD;
		tw.local.results[i] = {};&#xD;
		tw.local.results[i].code = tw.local.mwProductDetails[i].productCode;&#xD;
		tw.local.results[i].englishdescription = tw.local.mwProductDetails[i].englishName;&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="245f9385-224e-4431-b3e6-ae1f6e6189ef" targetRef="64312887-8ce6-4182-ab7c-70bbb35797f9" name="To Product Codes" id="05dbd6f9-6ddf-4696-9e37-5eba8ec19d92">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.033d5d03-1e28-4166-8adf-83166aef740a" isCollection="true" name="mwProductDetails" id="2056.944e1564-ced1-4d42-857e-a7f85740403e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEINTT.MW_ProductDetails();
autoObject[0] = new tw.object.toolkit.NBEINTT.MW_ProductDetails();
autoObject[0].productCode = "";
autoObject[0].englishName = "";
autoObject[0].arabicName = "";
autoObject[0].productGroup = "";
autoObject[0].productGroupDescription = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.4a924895-a490-4626-b3de-d1daa7313c0b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.670e5c76-7acc-4ab4-9e9d-68c764efa42a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.ce5f21f6-781f-49de-b591-7c34677410c0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.d2db910f-1258-47a3-b7b1-c9bbea53fa5c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.ac5369da-4884-44c9-ac6d-73617da22d6b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.04092bdc-b905-45be-8ed4-371861cc4e2d" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="96e465ab-a70e-49aa-b700-f71f118f71cb" parallelMultiple="false" name="Error" id="f2aed8b2-e427-4840-85d5-d7a61505ea69">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="212" y="112" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f8e29354-979a-4886-824a-09fafe70ddf7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="57dae90e-9ac5-41b0-819a-b8f937aebc39" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a0ec151f-8732-40ac-8c13-6af7212c39f4" eventImplId="f6c15d17-dd2b-4015-8d0d-716d369a2aed">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="245f9385-224e-4431-b3e6-ae1f6e6189ef" parallelMultiple="false" name="Error1" id="a4b18752-3d99-4711-85ef-61598c6a3108">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="68" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a6e58995-0094-4cb0-814d-ddcebe0605ff</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8f8e986c-b9b1-4174-8e5c-db5090d9af6e" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="acf7fb9d-a675-446f-848e-7408e785bb1a" eventImplId="d1fe9285-c607-4fe0-8c6e-7c17119eb797">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f2aed8b2-e427-4840-85d5-d7a61505ea69" targetRef="0170477e-3388-4630-856d-e46f47a3b0e2" name="To End Event" id="f8e29354-979a-4886-824a-09fafe70ddf7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a4b18752-3d99-4711-85ef-61598c6a3108" targetRef="0170477e-3388-4630-856d-e46f47a3b0e2" name="To End Event" id="a6e58995-0094-4cb0-814d-ddcebe0605ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.ff9edf5d-1df2-4e4b-8902-f276a2086ccd" />
                    
                    
                    <ns16:exclusiveGateway default="666e1faf-b2d6-49ce-8898-bbf851285f96" name="is Successful" id="ae7f70aa-919a-4381-8263-377b33c37a40">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="349" y="94" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c6fb27fd-a9de-4a2f-9766-25a24568adbd</ns16:incoming>
                        
                        
                        <ns16:outgoing>666e1faf-b2d6-49ce-8898-bbf851285f96</ns16:outgoing>
                        
                        
                        <ns16:outgoing>1bbe33b5-ad70-4057-8c82-7d347890a5f2</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="ae7f70aa-919a-4381-8263-377b33c37a40" targetRef="245f9385-224e-4431-b3e6-ae1f6e6189ef" name="To Map Product Codes" id="666e1faf-b2d6-49ce-8898-bbf851285f96">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ae7f70aa-919a-4381-8263-377b33c37a40" targetRef="0170477e-3388-4630-856d-e46f47a3b0e2" name="To End Event" id="1bbe33b5-ad70-4057-8c82-7d347890a5f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.35b0542a-90af-4505-8b47-c6fcf7d07378" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="0170477e-3388-4630-856d-e46f47a3b0e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="361" y="198" width="95" height="69" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1bbe33b5-ad70-4057-8c82-7d347890a5f2</ns16:incoming>
                        
                        
                        <ns16:incoming>a6e58995-0094-4cb0-814d-ddcebe0605ff</ns16:incoming>
                        
                        
                        <ns16:incoming>f8e29354-979a-4886-824a-09fafe70ddf7</ns16:incoming>
                        
                        
                        <ns16:outgoing>8473a2fa-1866-4013-8848-4308fe1755fc</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0170477e-3388-4630-856d-e46f47a3b0e2" targetRef="64312887-8ce6-4182-ab7c-70bbb35797f9" name="To End" id="8473a2fa-1866-4013-8848-4308fe1755fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.9a77c978-4e95-4775-8da8-3872cf940cd9" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1bbe33b5-ad70-4057-8c82-7d347890a5f2</processLinkId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ec6</endStateId>
            <toProcessItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</toProcessItemId>
            <guid>545e66d3-ade2-4088-84fa-9b2411ded04c</guid>
            <versionId>423107c8-9fb0-470b-8ebb-709ebaa23dd9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topLeft" portType="2" />
            <fromProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</fromProcessItemId>
            <toProcessItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8473a2fa-1866-4013-8848-4308fe1755fc</processLinkId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.64312887-8ce6-4182-ab7c-70bbb35797f9</toProcessItemId>
            <guid>74d42a2b-6e3a-4600-8ad1-16272715af42</guid>
            <versionId>5710746e-77ff-4a13-9145-b6ed4c93710b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.0170477e-3388-4630-856d-e46f47a3b0e2</fromProcessItemId>
            <toProcessItemId>2025.64312887-8ce6-4182-ab7c-70bbb35797f9</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c6fb27fd-a9de-4a2f-9766-25a24568adbd</processLinkId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.96e465ab-a70e-49aa-b700-f71f118f71cb</fromProcessItemId>
            <endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199</endStateId>
            <toProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</toProcessItemId>
            <guid>36102d22-c9b3-414d-b9c0-29a0d26fc261</guid>
            <versionId>c9ce8351-2ab8-460e-8578-0bc801408ae8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.96e465ab-a70e-49aa-b700-f71f118f71cb</fromProcessItemId>
            <toProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</toProcessItemId>
        </link>
        <link name="To Map Product Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.666e1faf-b2d6-49ce-8898-bbf851285f96</processLinkId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.245f9385-224e-4431-b3e6-ae1f6e6189ef</toProcessItemId>
            <guid>ed2c949d-8e1e-4faf-a19a-1bcd82203153</guid>
            <versionId>cb770ec9-1fd9-4600-b9b1-c966526dffe9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ae7f70aa-919a-4381-8263-377b33c37a40</fromProcessItemId>
            <toProcessItemId>2025.245f9385-224e-4431-b3e6-ae1f6e6189ef</toProcessItemId>
        </link>
        <link name="To Product Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.05dbd6f9-6ddf-4696-9e37-5eba8ec19d92</processLinkId>
            <processId>1.b16e9737-5d24-4229-83f7-2e9dc0637416</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.245f9385-224e-4431-b3e6-ae1f6e6189ef</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.64312887-8ce6-4182-ab7c-70bbb35797f9</toProcessItemId>
            <guid>83706c82-3aec-4459-a5be-1e8f33bb9bc2</guid>
            <versionId>ce06ca39-bc15-431d-a43d-ad155a7b039f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.245f9385-224e-4431-b3e6-ae1f6e6189ef</fromProcessItemId>
            <toProcessItemId>2025.64312887-8ce6-4182-ab7c-70bbb35797f9</toProcessItemId>
        </link>
    </process>
</teamworks>

