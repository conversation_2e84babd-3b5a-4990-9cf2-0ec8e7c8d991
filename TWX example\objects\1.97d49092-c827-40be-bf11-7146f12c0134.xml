<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.97d49092-c827-40be-bf11-7146f12c0134" name="Get IDC initiator">
        <lastModified>1692525666204</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>120e2416-54f7-42ea-9bda-fd9a77300da7</guid>
        <versionId>f530bd28-2c3c-41f6-b43c-502d7363fd16</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aa4c986259b1691d:-23a6d209:18a11a6235c:785" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"d059de6d-d2ed-4235-b05f-4d56d2845ee6"},{"incoming":["4ee20b54-f0d7-4d65-8111-f3fe12dee546","4993296d-f069-4a98-8fd6-296cab47baa9","e2b4188f-93d4-401a-81b0-0a61d53abc0e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":610,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b07"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ecd5bb3b-564d-4aba-8cb5-207b1051d1c6"},{"targetRef":"60302aaa-84d7-4bc3-a5a6-683a0945209f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get initiator group","declaredType":"sequenceFlow","id":"2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0","sourceRef":"d059de6d-d2ed-4235-b05f-4d56d2845ee6"},{"startQuantity":1,"outgoing":["b57fb058-a9a1-4ba6-856e-ff9b8ecda993"],"incoming":["2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":130,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Get initiator group","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"60302aaa-84d7-4bc3-a5a6-683a0945209f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.userTeams = new tw.object.listOf.String();\r\n\/\/tw.system.org.findUserByName(tw.system.user_loginName).roles\r\n\r\nfor(var i=0;i&lt;tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)\r\n{\r\n\ttw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;\r\n}\r\nlog.info(\"all roles: \"+tw.local.userTeams);\r\n\r\nfor(i=0;i&lt;tw.local.userTeams.listLength;i++)\r\n{\r\n\tif(tw.local.userTeams[i] != \"\" &amp;&amp; tw.local.userTeams[i] != null)\r\n\t{\r\n\t\r\n\t\r\n\tvar newString = tw.local.userTeams[i].substring(0,2);\r\n\tif(newString == \"BR\")\r\n\t{\r\n\t\ttw.local.branchCode = tw.local.userTeams[i];\r\n\t\ttw.local.branchSeq = tw.local.userTeams[i].substring(2,5);\r\n\t\tlog.info(\"Branch Code#: \"+tw.local.branchCode);\r\n\t\tlog.info(\"Branch seq#: \"+tw.local.branchSeq);\r\n\t\tbreak;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tvar splittedArray = tw.local.userTeams[i].split('_',4);\r\n\t\tif(splittedArray[0] == \"BPM\" &amp;&amp; splittedArray[1] == \"IDC\" &amp;&amp; splittedArray[2] == \"HUB\")\r\n\t\t{\r\n\t\t\ttw.local.hubCode = splittedArray[3];\r\n\t\t\tlog.info(\"split array #: \"+splittedArray);\r\n\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\t}\r\n}\r\n\r\n"]}},{"targetRef":"e6bc156f-a11c-4f64-85a0-0b640f94e2b6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Branch?","declaredType":"sequenceFlow","id":"b57fb058-a9a1-4ba6-856e-ff9b8ecda993","sourceRef":"60302aaa-84d7-4bc3-a5a6-683a0945209f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userTeams","isCollection":true,"declaredType":"dataObject","id":"2056.c5b52be1-0155-4ea8-8898-6a1525e34d41"},{"outgoing":["ea128420-7d78-4331-8657-ed8261f03c95","9bda7b96-7cbc-484e-8776-2a60d9e42ee4"],"incoming":["b57fb058-a9a1-4ba6-856e-ff9b8ecda993"],"default":"9bda7b96-7cbc-484e-8776-2a60d9e42ee4","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":328,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Branch?","declaredType":"exclusiveGateway","id":"e6bc156f-a11c-4f64-85a0-0b640f94e2b6"},{"targetRef":"6233bee3-e987-4fe8-8ae9-9b02f5a5522d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.branchCode != \"\" &amp;&amp; tw.local.branchCode != null"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"ea128420-7d78-4331-8657-ed8261f03c95","sourceRef":"e6bc156f-a11c-4f64-85a0-0b640f94e2b6"},{"startQuantity":1,"outgoing":["4993296d-f069-4a98-8fd6-296cab47baa9"],"incoming":["ea128420-7d78-4331-8657-ed8261f03c95"],"extensionElements":{"postAssignmentScript":["tw.local.branchName = tw.local.BranchNameValue.name;"],"nodeVisualInfo":[{"width":95,"x":442,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Branch Name","dataInputAssociation":[{"targetRef":"2055.264287fd-743d-4580-9bb3-4f10fd434c0e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM.NBE_SME_LG_BRANCHES\""]}}]},{"targetRef":"2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.branchSeq"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6233bee3-e987-4fe8-8ae9-9b02f5a5522d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.BranchNameValue"]}}],"sourceRef":["2055.64f4ba7a-4d36-4697-830d-c1951a7875bd"]}],"calledElement":"1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a"},{"targetRef":"ecd5bb3b-564d-4aba-8cb5-207b1051d1c6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4993296d-f069-4a98-8fd6-296cab47baa9","sourceRef":"6233bee3-e987-4fe8-8ae9-9b02f5a5522d"},{"startQuantity":1,"outgoing":["4ee20b54-f0d7-4d65-8111-f3fe12dee546"],"incoming":["9bda7b96-7cbc-484e-8776-2a60d9e42ee4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":442,"y":184,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get List of Hubs","dataInputAssociation":[{"targetRef":"2055.21f4753b-2351-494a-849c-bc4c055e9605","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.hubCode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"839fe4d2-d1fe-4562-8a19-1230e37290f1","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.hubName"]}}],"sourceRef":["2055.c7d0cfc7-082a-485e-8888-d60e6af40354"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.90c24b32-faa1-4055-8e19-b7c8ce78b175"]}],"calledElement":"1.027e3afb-1a94-4896-883d-daa4cdfee232"},{"targetRef":"839fe4d2-d1fe-4562-8a19-1230e37290f1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"9bda7b96-7cbc-484e-8776-2a60d9e42ee4","sourceRef":"e6bc156f-a11c-4f64-85a0-0b640f94e2b6"},{"targetRef":"ecd5bb3b-564d-4aba-8cb5-207b1051d1c6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Hub by code","declaredType":"sequenceFlow","id":"4ee20b54-f0d7-4d65-8111-f3fe12dee546","sourceRef":"839fe4d2-d1fe-4562-8a19-1230e37290f1"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"BranchNameValue","isCollection":false,"declaredType":"dataObject","id":"2056.2307fc36-e8c2-45ef-89fd-20c496d587af"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"listofHubs","isCollection":true,"declaredType":"dataObject","id":"2056.fefcc0bb-cbda-4922-8d8a-d3910768fa62"},{"parallelMultiple":false,"outgoing":["76890b3b-c2f9-41fe-811b-b50abe0fd69b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"d4862e54-523e-423a-8853-9f1d2e7186e8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"cabf2ee8-5830-4de8-89e1-ed564fa13c5c","otherAttributes":{"eventImplId":"1a8da8d9-1085-408b-8c50-bb8c27c7cdf6"}}],"attachedToRef":"60302aaa-84d7-4bc3-a5a6-683a0945209f","extensionElements":{"nodeVisualInfo":[{"width":24,"x":165,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"cf1a1a49-5495-4978-8562-73379b93fa31","outputSet":{}},{"parallelMultiple":false,"outgoing":["45c3ad95-677b-4f39-85e2-382e353d0ecf"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ab0568c3-10c1-42ac-8353-7951657b40cd"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"242ad61d-d70c-446c-80a0-3547eca2eed6","otherAttributes":{"eventImplId":"bc783000-417b-4c23-8312-c4bc59d4dd77"}}],"attachedToRef":"6233bee3-e987-4fe8-8ae9-9b02f5a5522d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":477,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"cc5f326d-c722-4aa5-8737-4858ed885e1b","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.a51ac0ac-17e6-4e2b-8d9c-f7be56338458"},{"startQuantity":1,"outgoing":["e2b4188f-93d4-401a-81b0-0a61d53abc0e"],"incoming":["76890b3b-c2f9-41fe-811b-b50abe0fd69b","45c3ad95-677b-4f39-85e2-382e353d0ecf"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":245,"y":281,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4a328211-142b-49ac-8f91-6e8330480077","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"4a328211-142b-49ac-8f91-6e8330480077","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"76890b3b-c2f9-41fe-811b-b50abe0fd69b","sourceRef":"cf1a1a49-5495-4978-8562-73379b93fa31"},{"targetRef":"4a328211-142b-49ac-8f91-6e8330480077","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"45c3ad95-677b-4f39-85e2-382e353d0ecf","sourceRef":"cc5f326d-c722-4aa5-8737-4858ed885e1b"},{"targetRef":"ecd5bb3b-564d-4aba-8cb5-207b1051d1c6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e2b4188f-93d4-401a-81b0-0a61d53abc0e","sourceRef":"4a328211-142b-49ac-8f91-6e8330480077"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.e8082524-2548-42a4-815b-fed87b0e1ffe"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.f1942296-3dfc-48a8-8c1c-be807f3a8f9d"}],"laneSet":[{"id":"154d8904-c1b9-4af9-bf40-67facff27ed6","lane":[{"flowNodeRef":["d059de6d-d2ed-4235-b05f-4d56d2845ee6","ecd5bb3b-564d-4aba-8cb5-207b1051d1c6","60302aaa-84d7-4bc3-a5a6-683a0945209f","e6bc156f-a11c-4f64-85a0-0b640f94e2b6","6233bee3-e987-4fe8-8ae9-9b02f5a5522d","839fe4d2-d1fe-4562-8a19-1230e37290f1","cf1a1a49-5495-4978-8562-73379b93fa31","cc5f326d-c722-4aa5-8737-4858ed885e1b","4a328211-142b-49ac-8f91-6e8330480077"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"2e1e5a35-c459-42e4-bd00-8c9c34bfff9c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get IDC initiator","declaredType":"process","id":"1.97d49092-c827-40be-bf11-7146f12c0134","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubCode","isCollection":false,"id":"2055.b72ee3d3-b09c-4c35-8a2e-671fde2d2442"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.6cab012e-a54a-449d-b07a-4332318c0e9c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchName","isCollection":false,"id":"2055.d3acd9c4-151d-429d-8926-71ff1a2cce6c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubName","isCollection":false,"id":"2055.fde9fca1-a5bf-475b-8836-1fa0a60c3917"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchSeq","isCollection":false,"id":"2055.*************-40d5-8927-9a96aa91fd83"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.d37ebe05-41d3-47ac-9237-53de467d6a4a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.c827eb34-876e-4ad9-8e02-7ca240406fb2"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}]},"inputSet":[{"dataInputRefs":["2055.021b06f9-34d3-4351-8a7b-8dd9987234ed"]}],"outputSet":[{"dataOutputRefs":["2055.b72ee3d3-b09c-4c35-8a2e-671fde2d2442","2055.6cab012e-a54a-449d-b07a-4332318c0e9c","2055.d3acd9c4-151d-429d-8926-71ff1a2cce6c","2055.fde9fca1-a5bf-475b-8836-1fa0a60c3917","2055.*************-40d5-8927-9a96aa91fd83"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"idcbranchmkr\"\r\n\"lcbrmkr01\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"loggedInUser","isCollection":false,"id":"2055.021b06f9-34d3-4351-8a7b-8dd9987234ed"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="loggedInUser">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.021b06f9-34d3-4351-8a7b-8dd9987234ed</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"idcbranchmkr"&#xD;
"lcbrmkr01"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cc461a67-c146-4d64-bd17-e9f1862f1438</guid>
            <versionId>879ad4e8-bacc-472a-b9d9-10e0c7ea0161</versionId>
        </processParameter>
        <processParameter name="hubCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b72ee3d3-b09c-4c35-8a2e-671fde2d2442</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a2a69511-cfac-4aed-9e84-e8f58aff86b8</guid>
            <versionId>66a66724-5f78-45d3-975d-3f1ea6a4bd63</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6cab012e-a54a-449d-b07a-4332318c0e9c</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>247380c7-07de-4a8e-aa0f-14389a99e101</guid>
            <versionId>56c5156c-6f0f-4e14-aef1-d567f9d06ed1</versionId>
        </processParameter>
        <processParameter name="branchName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d3acd9c4-151d-429d-8926-71ff1a2cce6c</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3f1e0e1e-73dd-4c72-aa72-581b53c4579e</guid>
            <versionId>04f92ac0-a5f9-48fa-8230-ac522e64083c</versionId>
        </processParameter>
        <processParameter name="hubName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fde9fca1-a5bf-475b-8836-1fa0a60c3917</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>edc1ebab-8cd7-497e-a9b2-5fd88059d452</guid>
            <versionId>70d7a992-ca6e-4b92-9530-f2b93b03089b</versionId>
        </processParameter>
        <processParameter name="branchSeq">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.*************-40d5-8927-9a96aa91fd83</processParameterId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>14427740-fdf5-4f34-8fe8-944851ae3322</guid>
            <versionId>c463d739-778a-4f1c-acf6-b24928ff52e5</versionId>
        </processParameter>
        <processVariable name="userTeams">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c5b52be1-0155-4ea8-8898-6a1525e34d41</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f1d15784-6097-4af7-a593-eabe8168785c</guid>
            <versionId>c51c1343-2e36-4b64-9ac7-f995719c538d</versionId>
        </processVariable>
        <processVariable name="BranchNameValue">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2307fc36-e8c2-45ef-89fd-20c496d587af</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f92184b2-11a2-425c-a80b-20a9e114576d</guid>
            <versionId>e408d05e-755c-48eb-8dd4-498b60fb2740</versionId>
        </processVariable>
        <processVariable name="listofHubs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fefcc0bb-cbda-4922-8d8a-d3910768fa62</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>928758bf-fad2-424d-94ff-ba20e993b6e2</guid>
            <versionId>ed5d52e6-b7b2-4863-87a9-4eafc303ca96</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a51ac0ac-17e6-4e2b-8d9c-f7be56338458</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dfaaa89d-2517-4c3f-b2df-0f03eb79cdf9</guid>
            <versionId>a1bb1055-a86e-4139-8ba6-8e3203380882</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e8082524-2548-42a4-815b-fed87b0e1ffe</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c09a4d11-85bf-4956-9e30-345047b0287b</guid>
            <versionId>3c271362-4442-45fb-a5b0-8ec514b5b207</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f1942296-3dfc-48a8-8c1c-be807f3a8f9d</processVariableId>
            <description isNull="true" />
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>31586102-e87a-4af5-85ae-9673235c9a0e</guid>
            <versionId>804d784d-8090-49d2-90a0-edf55b6f65f9</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.839fe4d2-d1fe-4562-8a19-1230e37290f1</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>Get List of Hubs</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f02bb729-c869-4da7-bc63-3f0e1d2f9245</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:4c4f</guid>
            <versionId>3b115e0f-9b08-42cb-b326-545e7b16f0af</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="442" y="184">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f02bb729-c869-4da7-bc63-3f0e1d2f9245</subProcessId>
                <attachedProcessRef>/1.027e3afb-1a94-4896-883d-daa4cdfee232</attachedProcessRef>
                <guid>385a2eca-1e3d-47d3-b02c-6f05e99e253c</guid>
                <versionId>0a844e94-26c6-400d-adb8-4bbb6ec5e4f3</versionId>
                <parameterMapping name="hubName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.162945a5-ca1c-4106-8ea6-b09b34c83989</parameterMappingId>
                    <processParameterId>2055.c7d0cfc7-082a-485e-8888-d60e6af40354</processParameterId>
                    <parameterMappingParentId>3012.f02bb729-c869-4da7-bc63-3f0e1d2f9245</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.hubName</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>269d74af-c1a4-4e73-bc3c-3b241e4b3a2b</guid>
                    <versionId>2fc36373-9c2f-41f5-8e4e-1967a6a55e9c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.94557624-31a0-4261-a4eb-ff99763e36b8</parameterMappingId>
                    <processParameterId>2055.90c24b32-faa1-4055-8e19-b7c8ce78b175</processParameterId>
                    <parameterMappingParentId>3012.f02bb729-c869-4da7-bc63-3f0e1d2f9245</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>30d8fd50-b136-4add-813d-84c367ac008d</guid>
                    <versionId>9c08c084-5297-4688-b08e-98e7cfb62383</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b2ec238e-1b32-4c78-8c03-8928986532aa</parameterMappingId>
                    <processParameterId>2055.21f4753b-2351-494a-849c-bc4c055e9605</processParameterId>
                    <parameterMappingParentId>3012.f02bb729-c869-4da7-bc63-3f0e1d2f9245</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.hubCode</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ac1c21d0-46bf-4d16-aae2-ba1d3fabcd7b</guid>
                    <versionId>b58c7860-4dbc-441f-9073-2c04e3a7e1a9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>Get Branch Name</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.75833db7-7d9f-4d82-a7ed-0210a98686bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</errorHandlerItemId>
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:4c4e</guid>
            <versionId>496d6b4a-2bab-420f-9469-d30966ceaa3d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.205becbb-1e87-48c4-bb3e-d0f35a6fda3d</processItemPrePostId>
                <processItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</processItemId>
                <location>2</location>
                <script>tw.local.branchName = tw.local.BranchNameValue.name;</script>
                <guid>c4a17fcf-0c41-4239-bdec-423ba6a4d1b9</guid>
                <versionId>4ae70e04-3dfc-4104-b43c-edb394b57d19</versionId>
            </processPrePosts>
            <layoutData x="442" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e8c</errorHandlerItem>
                <errorHandlerItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.75833db7-7d9f-4d82-a7ed-0210a98686bb</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a</attachedProcessRef>
                <guid>9b69aacf-d4c2-49fd-ba58-c6bd554928a8</guid>
                <versionId>9b705932-a122-43be-ab82-4b16508f22f5</versionId>
                <parameterMapping name="returnedName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6ff75db1-4309-4a28-8119-5dc57c3a4418</parameterMappingId>
                    <processParameterId>2055.64f4ba7a-4d36-4697-830d-c1951a7875bd</processParameterId>
                    <parameterMappingParentId>3012.75833db7-7d9f-4d82-a7ed-0210a98686bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.BranchNameValue</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5332086e-28c3-43fa-9926-9fa2bc0dacc2</guid>
                    <versionId>4f6d824b-4cb6-4a42-a51e-56831c244f59</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="tableName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d078a06a-fdf2-471f-a41c-87e0489f58cf</parameterMappingId>
                    <processParameterId>2055.264287fd-743d-4580-9bb3-4f10fd434c0e</processParameterId>
                    <parameterMappingParentId>3012.75833db7-7d9f-4d82-a7ed-0210a98686bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"BPM.NBE_SME_LG_BRANCHES"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e63cbc13-289f-414a-987e-0ea081ddad0b</guid>
                    <versionId>5fb7c7b9-e643-4f38-b8d3-d1c80336ee08</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="code">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.156bc3ff-36be-4d05-8046-************</parameterMappingId>
                    <processParameterId>2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0</processParameterId>
                    <parameterMappingParentId>3012.75833db7-7d9f-4d82-a7ed-0210a98686bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.branchSeq</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5585abf9-6cf4-4cc7-a5e2-299be987d898</guid>
                    <versionId>f06adefd-137f-464b-83eb-9869cd10877e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.980c784f-3d90-4a35-8902-1a6e0d71ba1e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b07</guid>
            <versionId>53b494d6-88e0-4181-ae0c-fddea2910975</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="610" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.980c784f-3d90-4a35-8902-1a6e0d71ba1e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>67ed118b-3836-41f6-927c-27df0e093c85</guid>
                <versionId>b27c6254-bdd1-48ee-8662-858cda328959</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>is Branch?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.edc5ba15-fe4e-4015-83c0-6847ff622ecd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:4c50</guid>
            <versionId>7200338c-1756-4952-a31d-056888790889</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="328" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.edc5ba15-fe4e-4015-83c0-6847ff622ecd</switchId>
                <guid>64e8f5e2-d407-42be-9f7c-af61b2da5319</guid>
                <versionId>3b518f54-3ca8-4cb2-a9da-eee37e20ef4a</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.58a0fa20-a92d-4663-abfd-64ee1431a9a2</switchConditionId>
                    <switchId>3013.edc5ba15-fe4e-4015-83c0-6847ff622ecd</switchId>
                    <seq>1</seq>
                    <endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:784</endStateId>
                    <condition>tw.local.branchCode != "" &amp;&amp; tw.local.branchCode != null</condition>
                    <guid>e46e3432-7ad1-424f-8e5a-3d792cb7e148</guid>
                    <versionId>d9c7b7d5-a62e-49db-b5a5-21f7a89eae9a</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>Get initiator group</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.34e8332c-da0d-4795-9c1d-472e62a80b29</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b06</guid>
            <versionId>e1093281-054b-4a8b-ac77-317e268ad023</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.18579414-d615-4161-9b11-340fd47f628b</processItemPrePostId>
                <processItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>6da0b5b3-9d3b-4954-9f31-9d33723b408f</guid>
                <versionId>30f7cc27-6443-4003-85f4-49f36871c9f1</versionId>
            </processPrePosts>
            <layoutData x="130" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e8c</errorHandlerItem>
                <errorHandlerItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.34e8332c-da0d-4795-9c1d-472e62a80b29</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.userTeams = new tw.object.listOf.String();&#xD;
//tw.system.org.findUserByName(tw.system.user_loginName).roles&#xD;
&#xD;
for(var i=0;i&lt;tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)&#xD;
{&#xD;
	tw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;&#xD;
}&#xD;
log.info("all roles: "+tw.local.userTeams);&#xD;
&#xD;
for(i=0;i&lt;tw.local.userTeams.listLength;i++)&#xD;
{&#xD;
	if(tw.local.userTeams[i] != "" &amp;&amp; tw.local.userTeams[i] != null)&#xD;
	{&#xD;
	&#xD;
	&#xD;
	var newString = tw.local.userTeams[i].substring(0,2);&#xD;
	if(newString == "BR")&#xD;
	{&#xD;
		tw.local.branchCode = tw.local.userTeams[i];&#xD;
		tw.local.branchSeq = tw.local.userTeams[i].substring(2,5);&#xD;
		log.info("Branch Code#: "+tw.local.branchCode);&#xD;
		log.info("Branch seq#: "+tw.local.branchSeq);&#xD;
		break;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		var splittedArray = tw.local.userTeams[i].split('_',4);&#xD;
		if(splittedArray[0] == "BPM" &amp;&amp; splittedArray[1] == "IDC" &amp;&amp; splittedArray[2] == "HUB")&#xD;
		{&#xD;
			tw.local.hubCode = splittedArray[3];&#xD;
			log.info("split array #: "+splittedArray);&#xD;
			break;&#xD;
		}&#xD;
	}&#xD;
	}&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>e2fa2e0d-57cd-480d-a165-8a6a8a2f3b89</guid>
                <versionId>ad36fdec-7191-4ad3-9b05-9034c30c6e6a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</processItemId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a8a25d86-5215-4411-b5fc-58ecfa96b603</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e8c</guid>
            <versionId>eaf8f9a8-6a05-456e-966a-b6f87d172980</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="245" y="281">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a8a25d86-5215-4411-b5fc-58ecfa96b603</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>4ed7129b-9374-45dd-8e16-e4357269c373</guid>
                <versionId>c4cbc0fb-4cc3-4a8d-ad5d-4b7680d2e60b</versionId>
            </TWComponent>
        </item>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.00603a68-8291-43b4-bdce-e51b371abb4a</resourceProcessLinkId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <guid>7c323201-a3ee-4eaf-9291-5d3ac1e24b7b</guid>
            <versionId>6bbbeb45-5dba-4685-ba94-790c15df44bf</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get IDC initiator" id="1.97d49092-c827-40be-bf11-7146f12c0134" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.d37ebe05-41d3-47ac-9237-53de467d6a4a</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.c827eb34-876e-4ad9-8e02-7ca240406fb2</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="loggedInUser" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.021b06f9-34d3-4351-8a7b-8dd9987234ed">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"idcbranchmkr"&#xD;
"lcbrmkr01"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="hubCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b72ee3d3-b09c-4c35-8a2e-671fde2d2442" />
                        
                        
                        <ns16:dataOutput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6cab012e-a54a-449d-b07a-4332318c0e9c" />
                        
                        
                        <ns16:dataOutput name="branchName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d3acd9c4-151d-429d-8926-71ff1a2cce6c" />
                        
                        
                        <ns16:dataOutput name="hubName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.fde9fca1-a5bf-475b-8836-1fa0a60c3917" />
                        
                        
                        <ns16:dataOutput name="branchSeq" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.*************-40d5-8927-9a96aa91fd83" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.021b06f9-34d3-4351-8a7b-8dd9987234ed</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.b72ee3d3-b09c-4c35-8a2e-671fde2d2442</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.6cab012e-a54a-449d-b07a-4332318c0e9c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.d3acd9c4-151d-429d-8926-71ff1a2cce6c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.fde9fca1-a5bf-475b-8836-1fa0a60c3917</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.*************-40d5-8927-9a96aa91fd83</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="154d8904-c1b9-4af9-bf40-67facff27ed6">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2e1e5a35-c459-42e4-bd00-8c9c34bfff9c" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>d059de6d-d2ed-4235-b05f-4d56d2845ee6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>60302aaa-84d7-4bc3-a5a6-683a0945209f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e6bc156f-a11c-4f64-85a0-0b640f94e2b6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6233bee3-e987-4fe8-8ae9-9b02f5a5522d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>839fe4d2-d1fe-4562-8a19-1230e37290f1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cf1a1a49-5495-4978-8562-73379b93fa31</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cc5f326d-c722-4aa5-8737-4858ed885e1b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4a328211-142b-49ac-8f91-6e8330480077</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="d059de6d-d2ed-4235-b05f-4d56d2845ee6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ecd5bb3b-564d-4aba-8cb5-207b1051d1c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="610" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5b07</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4ee20b54-f0d7-4d65-8111-f3fe12dee546</ns16:incoming>
                        
                        
                        <ns16:incoming>4993296d-f069-4a98-8fd6-296cab47baa9</ns16:incoming>
                        
                        
                        <ns16:incoming>e2b4188f-93d4-401a-81b0-0a61d53abc0e</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="d059de6d-d2ed-4235-b05f-4d56d2845ee6" targetRef="60302aaa-84d7-4bc3-a5a6-683a0945209f" name="To Get initiator group" id="2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get initiator group" id="60302aaa-84d7-4bc3-a5a6-683a0945209f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="130" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.0f6d452d-ac22-4772-bcf3-3f8bced248b0</ns16:incoming>
                        
                        
                        <ns16:outgoing>b57fb058-a9a1-4ba6-856e-ff9b8ecda993</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.userTeams = new tw.object.listOf.String();&#xD;
//tw.system.org.findUserByName(tw.system.user_loginName).roles&#xD;
&#xD;
for(var i=0;i&lt;tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)&#xD;
{&#xD;
	tw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;&#xD;
}&#xD;
log.info("all roles: "+tw.local.userTeams);&#xD;
&#xD;
for(i=0;i&lt;tw.local.userTeams.listLength;i++)&#xD;
{&#xD;
	if(tw.local.userTeams[i] != "" &amp;&amp; tw.local.userTeams[i] != null)&#xD;
	{&#xD;
	&#xD;
	&#xD;
	var newString = tw.local.userTeams[i].substring(0,2);&#xD;
	if(newString == "BR")&#xD;
	{&#xD;
		tw.local.branchCode = tw.local.userTeams[i];&#xD;
		tw.local.branchSeq = tw.local.userTeams[i].substring(2,5);&#xD;
		log.info("Branch Code#: "+tw.local.branchCode);&#xD;
		log.info("Branch seq#: "+tw.local.branchSeq);&#xD;
		break;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		var splittedArray = tw.local.userTeams[i].split('_',4);&#xD;
		if(splittedArray[0] == "BPM" &amp;&amp; splittedArray[1] == "IDC" &amp;&amp; splittedArray[2] == "HUB")&#xD;
		{&#xD;
			tw.local.hubCode = splittedArray[3];&#xD;
			log.info("split array #: "+splittedArray);&#xD;
			break;&#xD;
		}&#xD;
	}&#xD;
	}&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="60302aaa-84d7-4bc3-a5a6-683a0945209f" targetRef="e6bc156f-a11c-4f64-85a0-0b640f94e2b6" name="To is Branch?" id="b57fb058-a9a1-4ba6-856e-ff9b8ecda993">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="userTeams" id="2056.c5b52be1-0155-4ea8-8898-6a1525e34d41" />
                    
                    
                    <ns16:exclusiveGateway default="9bda7b96-7cbc-484e-8776-2a60d9e42ee4" name="is Branch?" id="e6bc156f-a11c-4f64-85a0-0b640f94e2b6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="328" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b57fb058-a9a1-4ba6-856e-ff9b8ecda993</ns16:incoming>
                        
                        
                        <ns16:outgoing>ea128420-7d78-4331-8657-ed8261f03c95</ns16:outgoing>
                        
                        
                        <ns16:outgoing>9bda7b96-7cbc-484e-8776-2a60d9e42ee4</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="e6bc156f-a11c-4f64-85a0-0b640f94e2b6" targetRef="6233bee3-e987-4fe8-8ae9-9b02f5a5522d" name="Yes" id="ea128420-7d78-4331-8657-ed8261f03c95">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.branchCode != "" &amp;&amp; tw.local.branchCode != null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Branch Name" id="6233bee3-e987-4fe8-8ae9-9b02f5a5522d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="442" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>tw.local.branchName = tw.local.BranchNameValue.name;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ea128420-7d78-4331-8657-ed8261f03c95</ns16:incoming>
                        
                        
                        <ns16:outgoing>4993296d-f069-4a98-8fd6-296cab47baa9</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.264287fd-743d-4580-9bb3-4f10fd434c0e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"BPM.NBE_SME_LG_BRANCHES"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.branchSeq</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.64f4ba7a-4d36-4697-830d-c1951a7875bd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.BranchNameValue</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6233bee3-e987-4fe8-8ae9-9b02f5a5522d" targetRef="ecd5bb3b-564d-4aba-8cb5-207b1051d1c6" name="To End" id="4993296d-f069-4a98-8fd6-296cab47baa9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.027e3afb-1a94-4896-883d-daa4cdfee232" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get List of Hubs" id="839fe4d2-d1fe-4562-8a19-1230e37290f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="442" y="184" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9bda7b96-7cbc-484e-8776-2a60d9e42ee4</ns16:incoming>
                        
                        
                        <ns16:outgoing>4ee20b54-f0d7-4d65-8111-f3fe12dee546</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.21f4753b-2351-494a-849c-bc4c055e9605</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.hubCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c7d0cfc7-082a-485e-8888-d60e6af40354</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.hubName</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.90c24b32-faa1-4055-8e19-b7c8ce78b175</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e6bc156f-a11c-4f64-85a0-0b640f94e2b6" targetRef="839fe4d2-d1fe-4562-8a19-1230e37290f1" name="No" id="9bda7b96-7cbc-484e-8776-2a60d9e42ee4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="839fe4d2-d1fe-4562-8a19-1230e37290f1" targetRef="ecd5bb3b-564d-4aba-8cb5-207b1051d1c6" name="To Get Hub by code" id="4ee20b54-f0d7-4d65-8111-f3fe12dee546">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" name="BranchNameValue" id="2056.2307fc36-e8c2-45ef-89fd-20c496d587af" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" name="listofHubs" id="2056.fefcc0bb-cbda-4922-8d8a-d3910768fa62" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="60302aaa-84d7-4bc3-a5a6-683a0945209f" parallelMultiple="false" name="Error" id="cf1a1a49-5495-4978-8562-73379b93fa31">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="165" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>76890b3b-c2f9-41fe-811b-b50abe0fd69b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="d4862e54-523e-423a-8853-9f1d2e7186e8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="cabf2ee8-5830-4de8-89e1-ed564fa13c5c" eventImplId="1a8da8d9-1085-408b-8c50-bb8c27c7cdf6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6233bee3-e987-4fe8-8ae9-9b02f5a5522d" parallelMultiple="false" name="Error1" id="cc5f326d-c722-4aa5-8737-4858ed885e1b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="477" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>45c3ad95-677b-4f39-85e2-382e353d0ecf</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ab0568c3-10c1-42ac-8353-7951657b40cd" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="242ad61d-d70c-446c-80a0-3547eca2eed6" eventImplId="bc783000-417b-4c23-8312-c4bc59d4dd77">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.a51ac0ac-17e6-4e2b-8d9c-f7be56338458" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="4a328211-142b-49ac-8f91-6e8330480077">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="245" y="281" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>76890b3b-c2f9-41fe-811b-b50abe0fd69b</ns16:incoming>
                        
                        
                        <ns16:incoming>45c3ad95-677b-4f39-85e2-382e353d0ecf</ns16:incoming>
                        
                        
                        <ns16:outgoing>e2b4188f-93d4-401a-81b0-0a61d53abc0e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="cf1a1a49-5495-4978-8562-73379b93fa31" targetRef="4a328211-142b-49ac-8f91-6e8330480077" name="To Catch Errors" id="76890b3b-c2f9-41fe-811b-b50abe0fd69b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="cc5f326d-c722-4aa5-8737-4858ed885e1b" targetRef="4a328211-142b-49ac-8f91-6e8330480077" name="To Catch Errors" id="45c3ad95-677b-4f39-85e2-382e353d0ecf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4a328211-142b-49ac-8f91-6e8330480077" targetRef="ecd5bb3b-564d-4aba-8cb5-207b1051d1c6" name="To End" id="e2b4188f-93d4-401a-81b0-0a61d53abc0e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.e8082524-2548-42a4-815b-fed87b0e1ffe" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.f1942296-3dfc-48a8-8c1c-be807f3a8f9d" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Get Hub by code">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4ee20b54-f0d7-4d65-8111-f3fe12dee546</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.839fe4d2-d1fe-4562-8a19-1230e37290f1</fromProcessItemId>
            <endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:7333</endStateId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
            <guid>bbb24636-b3c9-473b-8885-e0fc0920069f</guid>
            <versionId>0eececa1-36a1-40a7-9f4b-eea7f239a15e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.839fe4d2-d1fe-4562-8a19-1230e37290f1</fromProcessItemId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e2b4188f-93d4-401a-81b0-0a61d53abc0e</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
            <guid>360590e8-e9b5-4b00-a58f-a0db01fb4a3c</guid>
            <versionId>6cd97e4b-7f8c-41eb-9e00-3da09cd2b067</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.4a328211-142b-49ac-8f91-6e8330480077</fromProcessItemId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9bda7b96-7cbc-484e-8776-2a60d9e42ee4</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.839fe4d2-d1fe-4562-8a19-1230e37290f1</toProcessItemId>
            <guid>a2a6a335-264f-4a4e-9f92-4b0e5d47f6f6</guid>
            <versionId>992f2ba9-c99e-4c60-834b-d8c9159cce5b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</fromProcessItemId>
            <toProcessItemId>2025.839fe4d2-d1fe-4562-8a19-1230e37290f1</toProcessItemId>
        </link>
        <link name="To is Branch?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b57fb058-a9a1-4ba6-856e-ff9b8ecda993</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</toProcessItemId>
            <guid>8690c32a-6773-42ef-9070-1e0baed96ac5</guid>
            <versionId>9ba24dca-3b90-466d-bc1e-4b33095984c3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.60302aaa-84d7-4bc3-a5a6-683a0945209f</fromProcessItemId>
            <toProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4993296d-f069-4a98-8fd6-296cab47baa9</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a</endStateId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
            <guid>7efbd93a-89a8-4692-b337-530a8f986c06</guid>
            <versionId>c7ec9758-c345-4913-a35c-e8f54bb35817</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</fromProcessItemId>
            <toProcessItemId>2025.ecd5bb3b-564d-4aba-8cb5-207b1051d1c6</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ea128420-7d78-4331-8657-ed8261f03c95</processLinkId>
            <processId>1.97d49092-c827-40be-bf11-7146f12c0134</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:784</endStateId>
            <toProcessItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</toProcessItemId>
            <guid>21daf5e7-db2b-4183-a9d0-218619de8e73</guid>
            <versionId>e3b03813-4710-4c06-b808-7e72be527576</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e6bc156f-a11c-4f64-85a0-0b640f94e2b6</fromProcessItemId>
            <toProcessItemId>2025.6233bee3-e987-4fe8-8ae9-9b02f5a5522d</toProcessItemId>
        </link>
    </process>
</teamworks>

