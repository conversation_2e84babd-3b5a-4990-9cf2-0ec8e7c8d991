<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.37eab8a6-3b8b-4e52-8824-851827e9889b" name="Check Assign Large Corprate">
        <lastModified>1692530837247</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>3ae11984-ea1b-4b38-898d-c261c83bc41d</guid>
        <versionId>8037e590-cde5-4c5f-bb6f-9d59bac51cdf</versionId>
        <dependencySummary>&lt;dependencySummary id="b734f3f9-b13a-4665-ab9f-58612d64656f" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a2a70141-40f9-485d-84cf-53aff58969a6</processParameterId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0bad3a52-7c7a-44ab-a00d-9ba542352965</guid>
            <versionId>6f9c41ee-c83e-497e-84f7-684aa91c81b4</versionId>
        </processParameter>
        <processParameter name="teamSuffix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5f070a30-da63-42e9-a0e0-************</processParameterId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6cb5fd93-fa0a-4dcb-931c-fc26a8c95bec</guid>
            <versionId>d4ae2969-20ff-4284-92f3-986e55537fc3</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d</processParameterId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>30c7ec2b-247a-45e1-9fa9-16e7aacbbf34</guid>
            <versionId>822e09e3-68d6-4e0f-8255-3b6b03726503</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3b9bf44d-a309-479b-90ad-b216f09e23bb</processParameterId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>51b18183-13da-483f-a717-af7906e77a4a</guid>
            <versionId>bb163e92-75d6-4a09-b3f3-d8b1fd8763eb</versionId>
        </processParameter>
        <processVariable name="users_List">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.be7a17b9-8c65-44dc-95f4-9ebad0857a66</processVariableId>
            <description isNull="true" />
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "";
autoObject[0].value = "";
autoObject</defaultValue>
            <guid>ea444b27-a667-4a9b-a2a2-c9e3103c956e</guid>
            <versionId>7b416522-a49c-48dd-bac7-40e54c092639</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.72fa83ed-4ad4-43f9-9f61-ffe030f67c8c</processVariableId>
            <description isNull="true" />
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>22d4d0dd-dbf1-4198-9362-28525a1f1fab</guid>
            <versionId>b1dce9e3-bbd2-4f96-8509-ec504fd020be</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4edb50d7-4a1a-4558-94c1-31d774da605f</processItemId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.a385015c-8cca-474f-bd77-4c294486b2ab</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2a</guid>
            <versionId>0dee3948-411e-45f1-86c8-92ac1ec283a6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="532" y="204">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.a385015c-8cca-474f-bd77-4c294486b2ab</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>df2834fb-87f4-4af5-8b47-f7c1dd6f600b</guid>
                <versionId>d844a0e2-b100-4296-88af-38e1eb876648</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f832caa4-59ff-4223-8201-5975bc798b25</parameterMappingId>
                    <processParameterId>2055.3b9bf44d-a309-479b-90ad-b216f09e23bb</processParameterId>
                    <parameterMappingParentId>3007.a385015c-8cca-474f-bd77-4c294486b2ab</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e7b625cb-e41d-4eaf-aaae-456eed91e06c</guid>
                    <versionId>cbd11410-dad7-4108-a509-578f12c079cb</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e02ce4e3-c4c8-4be9-ae48-9f6f11645358</processItemId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.224de8c7-3860-400e-8c70-67f91f7c8b6e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2d</guid>
            <versionId>20bb5263-1c5f-4a4e-8533-0864aa224a16</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="429" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.224de8c7-3860-400e-8c70-67f91f7c8b6e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.users_List != null &amp;&amp; tw.local.users_List.listLength &gt; 0)&#xD;
{&#xD;
	tw.local.filteredTeam = new tw.object.Team();&#xD;
	tw.local.filteredTeam.name = tw.local.teamSuffix;&#xD;
	tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
	for (var i=0; i&lt;tw.local.users_List.listLength; i++) &#xD;
	{&#xD;
		tw.local.filteredTeam.members.insertIntoList(i, tw.local.users_List[i].value);&#xD;
	}&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.filteredTeam = new tw.object.Team();&#xD;
	tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
}</script>
                <isRule>false</isRule>
                <guid>1cf6729f-3e02-40c3-8253-6a21bcd86fe8</guid>
                <versionId>19a91767-634d-4b4e-874a-bb410062edad</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</processItemId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <name>Retrive Large Corprate From Team DB</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0fb15030-d6dd-4d9b-9238-789a5aa131db</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.eb09051c-4a5b-4b64-abcd-aae79cec4591</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2b</guid>
            <versionId>b3a824ab-477b-456a-972b-b2d449b51a4d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.0130ec24-609e-4b4b-b174-a8b84b9e4e97</processItemPrePostId>
                <processItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Retrive Large Corprate From Team DB : END");</script>
                <guid>37a123c4-2a75-4e3c-8586-01071a042e35</guid>
                <versionId>93bee63c-058f-444d-89d3-9ccdd3102e52</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.33b5937d-d2bf-4645-a94e-b6d6446164ab</processItemPrePostId>
                <processItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</processItemId>
                <location>1</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Check Assign Large Corprate : START");&#xD;
log.info(" ServiceName : Retrive Large Corprate From Team DB : START");</script>
                <guid>f06d090d-a428-4399-9899-d66e75f82e79</guid>
                <versionId>c9d9b7b1-9787-4595-86f1-6568271ef6fa</versionId>
            </processPrePosts>
            <layoutData x="195" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2c</errorHandlerItem>
                <errorHandlerItemId>2025.eb09051c-4a5b-4b64-abcd-aae79cec4591</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0fb15030-d6dd-4d9b-9238-789a5aa131db</subProcessId>
                <attachedProcessRef>/1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258</attachedProcessRef>
                <guid>bbc3d520-e0a1-4d68-a5ee-35b653fc2252</guid>
                <versionId>6338b43d-9894-48fd-afb9-84c78aedfc14</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2113a4ca-0d4c-4304-86be-23f10d12d030</parameterMappingId>
                    <processParameterId>2055.847e0f41-e473-44cc-b21c-f92a67dd5962</processParameterId>
                    <parameterMappingParentId>3012.0fb15030-d6dd-4d9b-9238-789a5aa131db</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.users_List</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>ec7a737b-6335-4ce9-ad0a-6720034fce56</guid>
                    <versionId>34e79d86-4f58-4606-af07-e301fb429a86</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="CIF">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c938b4f7-cc9c-4336-8fbc-6d52c27862de</parameterMappingId>
                    <processParameterId>2055.e087656c-e882-4af5-8843-8b459ca220c9</processParameterId>
                    <parameterMappingParentId>3012.0fb15030-d6dd-4d9b-9238-789a5aa131db</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.CIF</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ebf7dc63-d7ed-4021-8f68-a996f7e8d752</guid>
                    <versionId>a9460df3-1882-45a4-95c5-80287a23153f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dep_role">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6e0474c3-bd5f-4a4e-bcc5-e5751fb83f26</parameterMappingId>
                    <processParameterId>2055.9c75881f-44f2-4b85-9c0f-8f6802d36afd</processParameterId>
                    <parameterMappingParentId>3012.0fb15030-d6dd-4d9b-9238-789a5aa131db</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.teamSuffix</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5882b614-50f7-4f4b-a554-6c4633a58194</guid>
                    <versionId>b95817dc-45f6-4fc5-87e4-87c4aaa0c5ad</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d089509d-c78e-49d2-aec8-20912a44c862</processItemId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.8c6531ec-652a-4831-9e0b-5af425fe8b32</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2e</guid>
            <versionId>bd7c2411-cb0a-4b81-ab6b-c98a46e1d7c0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.cc62745a-1041-43cd-a5c7-47778ae2ca65</processItemPrePostId>
                <processItemId>2025.d089509d-c78e-49d2-aec8-20912a44c862</processItemId>
                <location>2</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Check Assign Large Corprate : END");</script>
                <guid>ace7ce83-b01f-4b82-9808-a64c97ba87e4</guid>
                <versionId>11337c55-f112-431f-a3c6-1a0c76d3ee7e</versionId>
            </processPrePosts>
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.8c6531ec-652a-4831-9e0b-5af425fe8b32</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>8a747054-ea50-4822-9846-99e7efe3e448</guid>
                <versionId>28842aee-bb17-43b9-920f-2ac683568ad5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.eb09051c-4a5b-4b64-abcd-aae79cec4591</processItemId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <name>log Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.be7d29c0-efa9-4ba6-954d-466fafae64d5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2c</guid>
            <versionId>e11d79f5-ea9d-4c6d-8aab-9ece457b4837</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="346" y="175">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.be7d29c0-efa9-4ba6-954d-466fafae64d5</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Check Assign Large Corprate -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Check Assign Large Corprate -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</script>
                <isRule>false</isRule>
                <guid>f40288b7-1cee-4a93-9476-07d7d3ce0145</guid>
                <versionId>976f43b2-dcc2-419f-9883-e2c7c5183789</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Assign Large Corprate" id="1.37eab8a6-3b8b-4e52-8824-851827e9889b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a2a70141-40f9-485d-84cf-53aff58969a6" />
                        
                        
                        <ns16:dataInput name="teamSuffix" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5f070a30-da63-42e9-a0e0-************" />
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.a2a70141-40f9-485d-84cf-53aff58969a6</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5f070a30-da63-42e9-a0e0-************</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="996d71be-dd5d-4ba5-8e90-437e36c742e6">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2eb79024-038c-4a0a-b284-3888073b1cff" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>273e71cf-e4e3-499d-b78f-bca667fead56</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d089509d-c78e-49d2-aec8-20912a44c862</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e5b25c15-be99-40bf-9436-fcc2fc654c91</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e02ce4e3-c4c8-4be9-ae48-9f6f11645358</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>34693c57-403d-4bd8-b180-1ad1eac7d185</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>eb09051c-4a5b-4b64-abcd-aae79cec4591</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4edb50d7-4a1a-4558-94c1-31d774da605f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="273e71cf-e4e3-499d-b78f-bca667fead56">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.af6a27f9-6264-414a-a424-a38f59fd935f</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="d089509d-c78e-49d2-aec8-20912a44c862">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2e</ns3:endStateId>
                            
                            
                            <ns3:postAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Check Assign Large Corprate : END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ad5eff09-9539-4fb7-bfe9-dbcbf72eed7e</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="273e71cf-e4e3-499d-b78f-bca667fead56" targetRef="e5b25c15-be99-40bf-9436-fcc2fc654c91" name="To Retrive Large Corprate From Team DB" id="2027.af6a27f9-6264-414a-a424-a38f59fd935f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.a3bac1a8-8d2c-46d2-8eb7-dcc05001e258" name="Retrive Large Corprate From Team DB" id="e5b25c15-be99-40bf-9436-fcc2fc654c91">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="195" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Check Assign Large Corprate : START");&#xD;
log.info(" ServiceName : Retrive Large Corprate From Team DB : START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Retrive Large Corprate From Team DB : END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.af6a27f9-6264-414a-a424-a38f59fd935f</ns16:incoming>
                        
                        
                        <ns16:outgoing>15581899-d1f7-4831-b34d-12188a461ea0</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e087656c-e882-4af5-8843-8b459ca220c9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9c75881f-44f2-4b85-9c0f-8f6802d36afd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.teamSuffix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.847e0f41-e473-44cc-b21c-f92a67dd5962</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.users_List</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e5b25c15-be99-40bf-9436-fcc2fc654c91" targetRef="e02ce4e3-c4c8-4be9-ae48-9f6f11645358" name="To Script Task" id="15581899-d1f7-4831-b34d-12188a461ea0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f71</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="users_List" id="2056.be7a17b9-8c65-44dc-95f4-9ebad0857a66">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "";
autoObject[0].value = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="e02ce4e3-c4c8-4be9-ae48-9f6f11645358">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="429" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>15581899-d1f7-4831-b34d-12188a461ea0</ns16:incoming>
                        
                        
                        <ns16:outgoing>ad5eff09-9539-4fb7-bfe9-dbcbf72eed7e</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.users_List != null &amp;&amp; tw.local.users_List.listLength &gt; 0)&#xD;
{&#xD;
	tw.local.filteredTeam = new tw.object.Team();&#xD;
	tw.local.filteredTeam.name = tw.local.teamSuffix;&#xD;
	tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
	for (var i=0; i&lt;tw.local.users_List.listLength; i++) &#xD;
	{&#xD;
		tw.local.filteredTeam.members.insertIntoList(i, tw.local.users_List[i].value);&#xD;
	}&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.filteredTeam = new tw.object.Team();&#xD;
	tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e02ce4e3-c4c8-4be9-ae48-9f6f11645358" targetRef="d089509d-c78e-49d2-aec8-20912a44c862" name="To End" id="ad5eff09-9539-4fb7-bfe9-dbcbf72eed7e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e5b25c15-be99-40bf-9436-fcc2fc654c91" parallelMultiple="false" name="Error" id="34693c57-403d-4bd8-b180-1ad1eac7d185">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="230" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8520d1b1-1be0-4306-835c-71571586970c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a23515d3-3e37-4c29-b6c5-c1f682e3ceb2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="48609008-823e-4015-a955-92d2c76c14cc" eventImplId="c6a154fc-a5ee-44f6-800f-d8260ed567b6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="log Error" id="eb09051c-4a5b-4b64-abcd-aae79cec4591">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="346" y="175" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8520d1b1-1be0-4306-835c-71571586970c</ns16:incoming>
                        
                        
                        <ns16:outgoing>e2ea692e-c0e4-411b-aa9c-94ca07b548d2</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Check Assign Large Corprate -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Check Assign Large Corprate -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="34693c57-403d-4bd8-b180-1ad1eac7d185" targetRef="eb09051c-4a5b-4b64-abcd-aae79cec4591" name="To log Error" id="8520d1b1-1be0-4306-835c-71571586970c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.72fa83ed-4ad4-43f9-9f61-ffe030f67c8c" />
                    
                    
                    <ns16:endEvent name="End Event" id="4edb50d7-4a1a-4558-94c1-31d774da605f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="532" y="204" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e2ea692e-c0e4-411b-aa9c-94ca07b548d2</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="ab4f333d-db36-466f-8ce1-f1870a8e2493" eventImplId="8cac0c7c-56b9-4a77-8e02-1388b398f7f6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="eb09051c-4a5b-4b64-abcd-aae79cec4591" targetRef="4edb50d7-4a1a-4558-94c1-31d774da605f" name="To End Event" id="e2ea692e-c0e4-411b-aa9c-94ca07b548d2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.804b8483-1f1e-49f4-9a3f-8dfc889a1da2</processLinkId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.eb09051c-4a5b-4b64-abcd-aae79cec4591</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4edb50d7-4a1a-4558-94c1-31d774da605f</toProcessItemId>
            <guid>4d385a9a-903a-4850-b1c5-29f7162bfec3</guid>
            <versionId>0422bd33-0d8f-4552-86d1-7060479130b5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.eb09051c-4a5b-4b64-abcd-aae79cec4591</fromProcessItemId>
            <toProcessItemId>2025.4edb50d7-4a1a-4558-94c1-31d774da605f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.47f2510c-36f4-4837-971b-4f41f4467eeb</processLinkId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e02ce4e3-c4c8-4be9-ae48-9f6f11645358</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d089509d-c78e-49d2-aec8-20912a44c862</toProcessItemId>
            <guid>a9b7d177-369d-4377-91c9-970bf04c7cdb</guid>
            <versionId>85bf35d7-cc1e-4750-ab06-7a0760251d5e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e02ce4e3-c4c8-4be9-ae48-9f6f11645358</fromProcessItemId>
            <toProcessItemId>2025.d089509d-c78e-49d2-aec8-20912a44c862</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9ddca6e9-ee0f-4b6e-be42-c6d6d2e6710c</processLinkId>
            <processId>1.37eab8a6-3b8b-4e52-8824-851827e9889b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-1f71</endStateId>
            <toProcessItemId>2025.e02ce4e3-c4c8-4be9-ae48-9f6f11645358</toProcessItemId>
            <guid>4e66876c-a0e4-4415-9cf5-c035fae9354c</guid>
            <versionId>9bf03984-b00e-4628-948f-c44d9d22ec5c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e5b25c15-be99-40bf-9436-fcc2fc654c91</fromProcessItemId>
            <toProcessItemId>2025.e02ce4e3-c4c8-4be9-ae48-9f6f11645358</toProcessItemId>
        </link>
    </process>
</teamworks>

