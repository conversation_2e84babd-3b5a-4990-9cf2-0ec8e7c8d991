<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.4de86ec1-53b9-4472-8ae0-17c04487a8d3" name="4de86ec1-53b9-4472-8ae0-17c04487a8d3">
        <lastModified>1688661262703</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <bpdId>25.4de86ec1-53b9-4472-8ae0-17c04487a8d3</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>2</type>
        <rootBpdId>25.1faa27cf-6102-48f4-be75-358e93428a99</rootBpdId>
        <parentBpdId>25.1faa27cf-6102-48f4-be75-358e93428a99</parentBpdId>
        <parentFlowObjectId>bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-606b</parentFlowObjectId>
        <xmlData isNull="true" />
        <bpmn2Data isNull="true" />
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63f6</guid>
        <versionId>9c0572c1-cb23-4399-9a0f-5e0a8c559bdc</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:4042929508233c47:-1c6e1647:188f04d39d0:6260">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>4de86ec1-53b9-4472-8ae0-17c04487a8d3</name>
            <documentation></documentation>
            <name>4de86ec1-53b9-4472-8ae0-17c04487a8d3</name>
            <dimension>
                <size w="0" h="0" />
            </dimension>
            <author>eslam</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1688664858044</creationDate>
            <modificationDate>1688666092043</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"Hub - Liquidation Treasury Approval:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>1</dueDateTime>
            <dueDateTimeResolution>2</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>false</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="e9b15f5f-01f0-4614-b7af-ad2e100ab91f" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6068" />
            <ownerTeamInstanceUI id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-606a" />
            <simulationScenario id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6069">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1688664862700</startTime>
            </simulationScenario>
            <flow id="afe90717-db6d-4fda-8ebe-3279f7a0da1a" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6067" />
                </connection>
            </flow>
            <flow id="950ad8ab-ee24-4aab-ab9d-047e40929295" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Treasury Approval End</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6066" />
                </connection>
            </flow>
            <flow id="6d703fab-4a03-45a2-843c-ec7107746fca" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6065" />
                </connection>
            </flow>
            <flow id="ec3d073f-638d-476f-8acb-394cb8b4136f" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Checker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63af">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingTreasuryMakerReview</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="a4215c57-b3f9-41db-85fb-7267e180582f" connectionType="SequenceFlow">
                <name>Copy of To Review Request by Treasury Maker</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6064" />
                </connection>
            </flow>
            <flow id="64d7a001-4bc6-42c2-908f-e5467386cd7d" connectionType="SequenceFlow">
                <name>To Copy of Treasury Escalation Mail Service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6063" />
                </connection>
            </flow>
            <flow id="d9864ed4-0cc6-4e42-b43d-d9382b0ba805" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Maker</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6062" />
                </connection>
            </flow>
            <flow id="d1ee0ff1-83f0-4cfd-9abb-8b6e73554873" connectionType="SequenceFlow">
                <name>Copy of To checker Approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6061" />
                </connection>
            </flow>
            <flow id="60785941-cbe2-4a2f-9b50-017be426d555" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Treasury Approval End</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6060" />
                </connection>
            </flow>
            <flow id="b299eacb-e6a1-4e64-8219-b2194cdbc5ad" connectionType="SequenceFlow">
                <name>Copy 2 of Copy 4 of To End Escalation</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-605f" />
                </connection>
            </flow>
            <flow id="57a479af-156f-4d3b-b7f9-2410fadc7197" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Review Request by Treasury Checker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ba">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingTreasuryCheckerApproval</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="23f8aa5a-24da-466d-97b5-92b070399287" connectionType="SequenceFlow">
                <name>To Hub - Liquidation Waiting CBE Approval</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b8">
                        <expression>tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.pendingCBE</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="4440c2eb-1887-44d5-bc4c-bd63501b6da6" connectionType="SequenceFlow">
                <name>Copy of To Maker Approved ?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-605e" />
                </connection>
            </flow>
            <pool id="e9b15f5f-01f0-4614-b7af-ad2e100ab91f">
                <name>Pool</name>
                <documentation></documentation>
                <dimension>
                    <size w="3000" h="844" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="3df433c4-8b28-41ee-88e5-7a90a8ce427f">
                    <name>Treasury Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>/24.4a9192e1-7977-401f-aa25-b8e2a9357b1d</attachedParticipant>
                    <flowObject id="b7b737c1-d5d9-48a9-bd06-2e1a27813820" componentType="Activity">
                        <name>Hub - Liquidation Review Request by Treasury Maker</name>
                        <position>
                            <location x="196" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d5475add-79dd-4468-9671-f08aa8c7c181</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>2</dueDateTime>
                                <dueDateTimeResolution>2</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review IDC by Treasury Maker –  مراجعة طلب تحصيل مستندى استيراد</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63f2">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.8b091ffe-9246-4a69-bde2-94a65cfe8f09</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63f1">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.f99743ef-4742-46f8-b9d7-80a95385ca25</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63f0">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.635673ed-7d0d-4457-8c49-9ab426653044</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ef">
                                    <name>intiator</name>
                                    <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.intiator</value>
                                    <parameterId>2055.c0f0cdee-abca-419e-85ab-c8d79a5209f3</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ee">
                                    <name>ECMproperties</name>
                                    <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
                                    <input>true</input>
                                    <value>tw.local.ECMproperties</value>
                                    <parameterId>2055.73b6ef38-8a52-4e41-aec3-9813ec6c931b</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ed">
                                    <name>folderId</name>
                                    <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
                                    <input>true</input>
                                    <value>tw.local.folderId</value>
                                    <parameterId>2055.a80db957-70a0-4d66-8021-4079b2a01892</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ec">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.b9c72c1d-3df4-40b0-8b75-60489434ecdc</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63eb">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.b6e4bcff-d80c-4147-8eed-a3240ddcd486</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ea">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.41a92624-2a12-41f4-bc3c-e22a6fee34e0</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e9">
                                    <serviceType>1</serviceType>
                                    <teamRef>/24.4a9192e1-7977-401f-aa25-b8e2a9357b1d</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e8">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c2">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="a4215c57-b3f9-41db-85fb-7267e180582f" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b6">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="d9864ed4-0cc6-4e42-b43d-d9382b0ba805" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b0">
                            <positionId>bottomRight</positionId>
                            <input>true</input>
                            <flow ref="ec3d073f-638d-476f-8acb-394cb8b4136f" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c1">
                            <positionId>rightCenter</positionId>
                            <flow ref="4440c2eb-1887-44d5-bc4c-bd63501b6da6" />
                        </outputPort>
                        <attachedEvent id="a3a52a1f-6dc1-4c4e-87e0-ecfaff4c7011" componentType="Event">
                            <name>Boundary Event15</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="2a8958a2-3be7-4277-a916-67bbe1aeb597">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="df69e746-d6ca-4169-859c-8f58741f2c0e">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63a8">
                                <positionId>bottomCenter</positionId>
                                <flow ref="64d7a001-4bc6-42c2-908f-e5467386cd7d" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63cc">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="3f307957-6706-464c-8f30-f56abcd3aaf9" componentType="Event">
                        <name>Hub - Liquidation Treasury Approval Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c3">
                            <positionId>rightCenter</positionId>
                            <flow ref="a4215c57-b3f9-41db-85fb-7267e180582f" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="8bb888c6-7111-4b56-8e42-da3c6caf1e61" componentType="Event">
                        <name>Hub - Liquidation Treasury Approval End</name>
                        <documentation></documentation>
                        <position>
                            <location x="650" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63bc">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="950ad8ab-ee24-4aab-ab9d-047e40929295" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b1">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="60785941-cbe2-4a2f-9b50-017be426d555" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="cc753a5b-1cac-452e-aa2c-e3b5e026dc90" componentType="Gateway">
                        <name>Hub - Liquidation Maker Approved?</name>
                        <documentation></documentation>
                        <position>
                            <location x="383" y="75" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c0">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="4440c2eb-1887-44d5-bc4c-bd63501b6da6" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63be">
                            <positionId>bottomCenter</positionId>
                            <flow ref="57a479af-156f-4d3b-b7f9-2410fadc7197" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63bd">
                            <positionId>bottomCenter</positionId>
                            <flow ref="23f8aa5a-24da-466d-97b5-92b070399287" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63bf">
                            <positionId>rightCenter</positionId>
                            <flow ref="950ad8ab-ee24-4aab-ab9d-047e40929295" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="174a8e20-32d5-478c-b703-7255065a92d3">
                    <name>Treasury Checker</name>
                    <height>244</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>/24.bd1bee62-02d9-42de-a716-2b37ec625521</attachedParticipant>
                    <flowObject id="99253010-f588-45f4-b4ff-1aadb0b08fb3" componentType="Activity">
                        <name>Hub - Liquidation Review Request by Treasury Checker</name>
                        <position>
                            <location x="322" y="41" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.f964f62b-c99c-41db-81e6-d7c13f5ef504</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>2</dueDateTime>
                                <dueDateTimeResolution>2</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Approve IDC by Treasury Checker –  الموافقة على طلب تحصيل مستندى استيراد</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e6">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.4898ef7e-ce59-4378-8525-ee675fd0de0a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e5">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.74770b74-e03c-43e0-962c-3fd3870f8b96</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e4">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.63cdeaec-b471-4f8c-a069-333caf05f017</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e3">
                                    <name>intiator</name>
                                    <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.intiator</value>
                                    <parameterId>2055.e9b37ea8-8ac8-4223-bb6f-65e8305d4ba5</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e2">
                                    <name>ECMproperties</name>
                                    <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
                                    <input>true</input>
                                    <value>tw.local.ECMproperties</value>
                                    <parameterId>2055.b8f27a6f-4b9e-477c-8fcc-1ee957600c65</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e1">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.cc6ddc86-b660-4b3a-94d1-5972a1156abf</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63e0">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.45f66d73-32b1-4ca0-b9c9-2b6fc4fb919f</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63df">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.1ddef6c7-f014-4a4a-8c78-0843adb3820b</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63de">
                                    <serviceType>1</serviceType>
                                    <teamRef>/24.bd1bee62-02d9-42de-a716-2b37ec625521</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63dd">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63bb">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="57a479af-156f-4d3b-b7f9-2410fadc7197" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b5">
                            <positionId>bottomCenter</positionId>
                            <flow ref="d1ee0ff1-83f0-4cfd-9abb-8b6e73554873" />
                        </outputPort>
                        <attachedEvent id="f7c87c34-cf59-4602-a122-40deaedd693e" componentType="Event">
                            <name>Boundary Event16</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomLeft</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="06bfd920-4fb4-422a-a7df-f7a49ef73e52">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="bb12a7ee-798c-48c2-8864-c63504fa634a">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63aa">
                                <positionId>bottomCenter</positionId>
                                <flow ref="afe90717-db6d-4fda-8ebe-3279f7a0da1a" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c9">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="74101bbd-40f6-4bb2-8937-01171474850e" componentType="Gateway">
                        <name>Hub - Liquidation Checker Approved?</name>
                        <documentation></documentation>
                        <position>
                            <location x="335" y="163" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b4">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="d1ee0ff1-83f0-4cfd-9abb-8b6e73554873" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b2">
                            <positionId>leftCenter</positionId>
                            <flow ref="ec3d073f-638d-476f-8acb-394cb8b4136f" />
                        </outputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b3">
                            <positionId>rightCenter</positionId>
                            <flow ref="60785941-cbe2-4a2f-9b50-017be426d555" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="c1097fbb-9c42-4989-b090-25c6be552186">
                    <name>CPE Queue</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="3c0e20da-cdb7-481f-870c-79eb9fe5b310" componentType="Activity">
                        <name>Hub - Liquidation Waiting CBE Approval</name>
                        <position>
                            <location x="521" y="100" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.f2c8f018-67e0-416a-acb3-8af30664cba5</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>2</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>IDC Waiting CBE by Treasury Maker – الحصول على موافقة البنك المركزى لطلب تحصيل مستندى استيراد</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63db">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.03a5f1e9-21d6-4ac6-9894-82edc980e087</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63da">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.7a6533b6-bbb9-4f62-aa08-d41a34aa3927</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d9">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.3b198fb8-2cbb-43a3-8d9a-6f2233fce87a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d8">
                                    <name>ECMproperties</name>
                                    <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
                                    <input>true</input>
                                    <value>tw.local.ECMproperties</value>
                                    <parameterId>2055.d9261eb9-6067-4b77-b05b-249696e3ac60</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d7">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.19153306-7e7e-4ce0-8d73-b24ade667180</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d6">
                                    <name>attachment</name>
                                    <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.attachment</value>
                                    <parameterId>2055.690326c2-3e2a-48f2-8091-935656717e2b</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d5">
                                    <name>treasuryComments</name>
                                    <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.treasuryComments</value>
                                    <parameterId>2055.dbd6de09-c2b9-41d6-99dd-5de23cc5a7c1</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d4">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d3">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b9">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="23f8aa5a-24da-466d-97b5-92b070399287" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63b7">
                            <positionId>leftCenter</positionId>
                            <flow ref="d9864ed4-0cc6-4e42-b43d-d9382b0ba805" />
                        </outputPort>
                        <attachedEvent id="3c899eed-05b4-4890-8461-e05f13b486d4" componentType="Event">
                            <name>Boundary Event17</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="293e10f9-0b92-42d9-a5eb-134c15f0a584">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="af76eb70-5618-4a43-86a7-a67f2e8dbf7c">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>true</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ac">
                                <positionId>bottomCenter</positionId>
                                <flow ref="6d703fab-4a03-45a2-843c-ec7107746fca" />
                            </outputPort>
                            <assignment id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63c6">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskid</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                </lane>
                <lane id="4429bf92-f35c-4136-b394-ee266e4cf848">
                    <name>System</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="0653b2d6-ce2c-4997-b714-64adb72e0bda" componentType="Activity">
                        <name>Copy of Treasury Escalation Mail Service</name>
                        <position>
                            <location x="613" y="99" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.dc54f3f7-6c6c-4ef0-92ef-d60cbcf0c82b</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d1">
                                    <name>mailDebugMode</name>
                                    <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.mailDebugMode</value>
                                    <parameterId>2055.fb6c9e80-2768-4eb3-b290-28ef566eafed</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63d0">
                                    <name>taskid</name>
                                    <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.taskid</value>
                                    <parameterId>2055.a3998db2-ad91-4b7f-87a2-2a80091e47c9</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63cf">
                                    <name>idcRequest</name>
                                    <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
                                    <input>true</input>
                                    <value>tw.local.idcRequest</value>
                                    <parameterId>2055.9e7c1182-49d5-4102-8b97-5c2c2a5eade8</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ce">
                                    <serviceType>1</serviceType>
                                    <teamRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63cd">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ab">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="6d703fab-4a03-45a2-843c-ec7107746fca" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63a9">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="afe90717-db6d-4fda-8ebe-3279f7a0da1a" />
                        </inputPort>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63a7">
                            <positionId>leftBottom</positionId>
                            <input>true</input>
                            <flow ref="64d7a001-4bc6-42c2-908f-e5467386cd7d" />
                        </inputPort>
                        <outputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ae">
                            <positionId>rightCenter</positionId>
                            <flow ref="b299eacb-e6a1-4e64-8219-b2194cdbc5ad" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="f6d33e5b-378b-4c2f-9477-473385d1657c" componentType="Event">
                        <name>Copy 2 of Treasury End Escalation</name>
                        <documentation></documentation>
                        <position>
                            <location x="831" y="117" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63ad">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b299eacb-e6a1-4e64-8219-b2194cdbc5ad" />
                        </inputPort>
                    </flowObject>
                </lane>
                <epv id="bpdid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-63f5">
                    <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
                </epv>
            </pool>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

