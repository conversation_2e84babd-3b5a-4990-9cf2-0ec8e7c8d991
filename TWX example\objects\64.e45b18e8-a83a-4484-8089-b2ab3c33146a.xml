<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.e45b18e8-a83a-4484-8089-b2ab3c33146a" name="Commissions And Charges">
        <lastModified>1702303111503</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;bb949d49-6f39-4d64-8470-d94135287e5c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8475a38a-f6d6-448f-8c09-a95ddcf599ec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Applied Commissions And Charges&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cfd82b9e-a7f2-4169-80d6-03f195f5c545&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e1153dc8-a665-41a5-8161-2a4c50c6872f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eab71df7-e16a-4e11-8d26-83edcb1629ae&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ff3ee77-4696-4bc0-8fe7-2b0d8ed170ef&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//${GetChargeDetails1}.execute();
//view.getCommissions();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;848bdcd6-021f-4f24-8aec-323888ce2c20&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5bec115d-11e0-4d6f-814a-bc9678358cd1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6de9b429-2690-4a66-8d9d-6cad9f86cb49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Commission / Charge Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22230b40-c280-46fe-88b8-a57047a88bbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9861590e-a5ba-46f4-8b75-4d8d14cff294&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b34c2e7e-602a-46ee-800d-0a462aa30b75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;245effc3-46e2-4768-82ee-db804f325556&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;dc0bf108-b2af-4b6f-8edd-b6a010e03339&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;66e965ff-9efd-404d-8e57-9e0ce10c5903&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d25c159a-43b7-4ce1-861e-1b4f02c160ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be3cf915-16f8-4754-8aa4-7f6421f2425b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e49e6ea-4365-4a59-89da-1569852436ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;47a42b30-2aed-47a2-8e09-0ac6cd7229d3&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;314c835b-47f9-4cb4-8b39-7e3bb3e77f3e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f112c253-76d6-4a68-809e-d8ce3d333956&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;224dcbff-dd9a-4785-8b9d-f2c8a519b9c6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;48a294ec-7cf6-4f22-8394-7eafbc73b346&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c03a6de0-e775-4142-8849-329e846c6216&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;84412b90-e902-4b36-81a8-d5b91e43048d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Component&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b143e23d-d3b6-48f9-8db4-3f1ea6761530&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Component&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;896ced85-ee4b-4b82-82c5-97cbae284585&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6a7a594-c20a-4515-817f-a94bd97f8334&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8cb27df6-074e-471f-8116-dd5546ddf6c1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.component&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fcfada7f-ec4f-422e-8223-b565142f86b4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;defaultAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3c8edf15-756b-48bb-8096-3a920346e6ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.defaultAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b7cfc368-966e-4c45-8d07-65e07190fe41&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01758a7b-8363-45e7-8c2d-0dfcee4e7422&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da39da8f-0516-4c2e-83f7-c3595fce0e52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;269e6243-5882-46a7-8cb3-14c589d7ff9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f6653ad-226f-4bcf-8e05-fc4607158f56&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.defaultAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b113379b-b5ec-41fa-800d-cb69ae268357&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Waiver1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82426338-07dc-4a57-8bb8-622a4e4a3edc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Waiver&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aa4f483f-6a65-4b1c-818d-dca3d725da59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93cdb7aa-9cda-43b0-873f-427096b61e7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8024b5f5-20f4-42ec-828c-b1c4b4ead3ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd7bdda9-b8a8-4535-8164-20c963daddf4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c1e8b706-8e61-4458-8521-40957308e9f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be69053a-ae51-41c8-8597-b14b7301d8d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9050984c-20ca-41c4-800b-3361d4b5f9e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.waiver&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4165ae34-ea77-4c0d-8a0d-eb2205ae39ab&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;254a7635-fc43-45b9-8d09-cb68f4032aca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d38f608-e798-4f1d-8b1a-e4c3a7f938b6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e9763c4-1a6f-43c6-8662-f5e53b758f2e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;7eb3c3f4-b9a4-408b-8621-d371d20dd1a7&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f7a7433b-4a4f-40a3-8ee0-ec06b606ee85&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;defaultCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a67a255d-bce5-4f59-8455-bcbdfec8240c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.defaultCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;20c14134-e25a-4078-8cc3-34dcc99efb0e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2764d8e-8b36-45c1-864f-0d7e92b9591f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb1e2bc5-397b-4050-893a-86765f4e875e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb8c7572-e6f4-4268-87ea-18f535366568&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1c9f7805-d7c3-4cc7-827b-b412f7eb7dc4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c00def3-059b-437c-8d6a-7789cb9a9686&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34c03c21-9f6e-4953-89ea-d264fe0f91f1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"id","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.defaultCurrency.code&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6f26f95e-1755-45a1-8bd8-cb07fb402a2f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ChangeAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a8e2db9a-2952-4dc7-b335-af20420b4561&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d83f2bb1-74ee-48af-87ee-7cd60ee5017b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ChangeAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc3056ea-89d8-4276-88ca-0dfa6a513c56&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e10e959-9af6-4bfd-8765-6e87f87d956b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71123bb3-4a5b-4290-8160-a49416ba8a5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;thousandsSep&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;afd9ffcb-0f26-42ec-8a40-6bb58eaf71f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.debitedAmountSum(me);
if(me.getData() &amp;lt; 0){
	me.setValid(false , "Must be &amp;gt;= 0");
	return false;
}
else{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.chargeAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6dd8540e-3b86-4311-816a-e64e31704ef7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAccountDetails1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a3553bc6-8808-4434-8bc7-3221dcae36de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Account Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06a62fc3-fd2f-464e-8ff8-51c82e213199&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d2e361c6-a2f7-4ebc-809e-d3614f9d345b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2fef7d26-33a1-4823-8df3-a7211154f5b4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;83465b65-2376-4407-84a1-78893bff63f8&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0d7c63fc-885f-45c9-8ca6-8733b298e84c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3efe3521-90db-4439-8ecb-8d21a6644c10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93646e17-5966-4cd8-8574-797e4a9d3acd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea7f49ab-5bd7-4df6-8b28-7ff5cca2d3b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;37c53df2-482e-4c56-87c4-b310191a3f4d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fee2b3b7-0247-4263-804c-bb59e0e46365&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68c3dfb0-d2ed-48b5-8875-9f04a6231899&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;19e4e30f-3cc1-4506-8f8a-f7f50e8b87db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9d084f1-7207-43aa-8a26-f48354a6c779&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;819d8bfe-a798-4fe5-8b2b-0cbaf6552a55&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"50%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;007196f0-0ca1-421f-8455-************&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;bff93d62-f11e-4af4-8528-26375f80cff7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountClass&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32d95270-c088-475b-8370-7aa20ed2f2da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountClass&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bf9ac224-2bf4-4f35-80b7-b62907b46b59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;25be1945-1f84-447b-827a-ad50a3bcec5c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f6b211e-8f8e-440d-8251-a8c508849c4a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-3871-442f-8237-a47a683fde64&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97218a27-c9de-483b-8253-67b957cfa238&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;78efb1ca-ec6c-4df9-805c-1ac95392349f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Customer Account","value":"Customer Account"},{"name":"GL Account","value":"GL Account"}]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;affbfc98-4402-42b9-80c5-b0562569cb43&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeService(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a33571e-9ab2-48b4-897c-35b3876fa4c9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;console.log("======CHARGES=====");
console.dir(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.accountClass&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fc592783-2022-481f-8429-15d0c63baba4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;662c8928-bbeb-4637-8f59-42b6d6f294f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;430506d5-bdd6-4250-8710-579361be57d6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7a8b0858-d917-4584-8f16-0959158c1387&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;4a15017f-f829-47ad-83c4-df63f18bbe14&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9f2a3c77-d115-40fd-88cd-cd8da350cbf5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountNumberGL&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f2d5831-a363-4bf8-98cf-593b076e065e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd0c112d-bf70-4942-8b5d-ef3db1a766da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.GLAccountNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;02506bd1-5a8d-4dd2-8044-2771c5e358fe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ac5cdf13-8635-43df-8f3f-0f6a39504240&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eb06b9e4-bd7e-452a-8b58-a4fa99c53e1f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cfa460fa-a6a4-47e1-8e0c-60ae124f9315&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetGLButton(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ece97e61-fd59-4307-8179-e66113b8179f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 9){
	me.setValid(false , "must be 9 characters");
//	me.setData("");
	return false;
}else{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.GLAccountNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4e28fe48-c5e3-45e1-848c-7efeae60733e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;VerifyGLAccountBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;*************-48d1-8a07-651987edb137&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Verify GL Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1f4abfa5-05c7-4b3a-801c-6815a43cdd63&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8315826e-a7cd-4b01-83a4-004e10c98b69&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd43dfdc-71e9-4538-826b-6f5dad2a81fd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc794266-8ce1-4d3b-821a-4d650442c215&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8921dc1e-a6fe-4057-856f-f8f5edc3ca3c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeGL(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4632cc9-7605-447d-8df0-0d7fd621ef5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4956ab25-9643-4412-8d0e-6f04b62d9afa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e9a363c-de4e-4195-8e3c-eefd30a90ba1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expression&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15116837-9026-416b-81c4-2d298e5d521f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fb31572-6c60-4e2c-8139-1a250557a6f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.isGLVerifiedC&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6a97e680-c505-4999-8d24-4c25470c587e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountCurrency1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9627a0d8-2aea-49f7-8741-2cf76b6d1d0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6adac95-0acf-470e-8ad6-f44d42488b04&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b54f1e7b-c6d2-4463-8c58-d57f368f8878&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37bc19e9-5a3a-44c5-8e30-a627118cb86b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bdc96cf4-44e6-4772-88a4-d9c33a38f6cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb4d3bd3-8f77-44fc-8906-533abefd4bd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f643b523-c18d-4112-80dc-69ce6d0b30f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95f0f127-7e01-4451-8205-08331d961d23&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3016ddbe-091f-4fa8-8002-dbf20c21358a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExchangeRate(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d0fc096c-b967-47c9-82dc-6af6c82bccc7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.accountCurrency.code&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ef65613d-d22a-42df-8a22-d7ae35422a1a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95e4dc95-c73c-4ecf-8b6a-19930c4844a0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BalanceSign&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;172e6900-7793-4ad9-88aa-911073d335d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d37eb690-0661-414d-88c9-e21bf919a04f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01135b94-4f13-4dc1-85e7-2817eda525ce&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.balanceSign&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0f8c28f7-a846-43be-8046-584fbb149126&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92d3c353-a76f-4b1d-8be7-8bbc226c54dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5690f13e-e9c1-4b3a-8152-297cafa6f88e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3aa4d328-9c2c-4a12-8a0d-bdd91a813599&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f205037-8d22-4c6c-8786-188ba969f4c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"50%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;a1a6ee33-7407-4ca4-8515-41014a6eccd6&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;********-f0f4-452f-818a-894ab453d12a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Owner1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b6e4f7a6-a52e-474f-8c1b-933f0c54ba4a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.ownerAccount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f883baf1-6582-4f22-87ac-127582a49881&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7bcc12bd-59a1-4f41-82c1-7b21342a8fbe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7158991d-cd2b-4f0a-8ec0-dd659da534bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-e33c-484d-8c3f-244e9d08d1c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Drawee","value":"Drawee"},{"name":"Case In Need","value":"Case In Need"},{"name":"Accountee","value":"Accountee"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a91e4eac-4795-49ec-802d-db7d34918646&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setPartyCIF(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-1f7f-48a5-82de-c7bf79e18528&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b940721-433a-4242-849f-01384a4bdf9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.ownerAccounts&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e174cac9-8a8b-49a7-8318-7eb18889b262&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fab23882-7e5f-4418-8428-64e12804fb44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c2ed443d-8f2a-46cb-8cd9-d00259e7bd34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e3a5c1ed-7ab0-4581-8b88-a4651314c184&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cc5dc30-ed1f-442b-8ec6-619ea29a34e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0da3e52b-eeac-4092-88b5-d21712cc0c29&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a83638a4-800b-4e75-8dbc-d8cb7b6b4b6d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd8730cf-5864-4c13-83d0-430cf7005779&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.commAccountList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b593ca32-efbc-4845-86bd-ad3916a1447f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.accountVis(me);

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68924d71-d4b7-4555-8ff7-cf27f9b8a205&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setAccountInfo(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d667a0b1-e8cd-4881-8039-1ffd1e29c86d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.753ea3e8-d234-409e-9fb2-015400e4c9fb&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0070dc5b-e3de-4d4b-818e-b64417ea926b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;13ae8557-c8b8-4d5f-829d-18309c794127&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ee55029-ce65-4765-8332-73e708f34d8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.commAccountList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc7a847d-3e88-48df-8790-386aa622385e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.accountNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cfe72d83-7366-4e2e-82eb-3fdcce1d67df&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AccountBranchCode1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6023508b-cd79-4b38-bdb2-64767b1b31c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c28d6cbc-20af-4a58-8e2c-6478b545fc97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountBranchCode&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f9e9d125-24af-46d0-883c-b50ae364ab3f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93aa5451-b614-478d-8685-c9267b7eecf7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;370cca36-6258-4693-80dd-110b21205127&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b0fa2eaa-dfc4-4fa9-83c3-23992befcaaf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateDigits(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3261bad1-3d8f-48f9-83a3-d6d8d7d7cf13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 3)
{
	me.setValid(false , "max lenght 3 Digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.accountBranchCode&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0f6bf2f4-2d73-4bf9-8d68-539ec405be08&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Decimal1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;016e31ff-6191-4d48-bbc6-853a7706e388&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83f73755-ea65-455b-84af-3199417447eb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AccountBalance&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f67ac75-117f-47be-888f-b52d399f1840&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da9b5bd7-2be3-4991-89c9-325158db7f55&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f2ea4058-3bd6-4c64-8513-3d787e81f260&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.accountBalance&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2ec2df3a-973b-44d1-8b3b-f42587b51d77&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetCustomerAccount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b121513b-29f3-4a3e-8b43-188708d9cff4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Customer Account Info&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;009bc3a6-e914-45c2-878d-3edc3405a6b5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;42aabbe2-8a5f-45f9-8395-e5b642d017b6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5026b002-dec6-4f98-8b4c-098bfa1e4cdf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;autoRun&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;44a595eb-c043-42fd-87e6-f4e68b272b79&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.753ea3e8-d234-409e-9fb2-015400e4c9fb&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d1d94c3-53f4-4d8b-8578-63b7f9cee27e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;53fbb27e-bf21-4a2a-8df2-6022b3abf1e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setCommAccounts(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;66066c50-1e9f-4596-8443-b639ce699f69&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c94c68d2-9e11-4065-86de-4f834b36d087&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5cc748b2-6699-498c-8322-fe921ab3781e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.CommAccountList[]&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1bb683c9-c97f-4410-8804-3dd59121b4c0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CheckexistingGLAccount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;339be535-b650-4829-82d2-35ee4c429d11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Check existing GL Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa5e9589-7dee-4b97-81d9-5eecd7ebc4b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4358f8ca-a92b-427b-87c8-1c9ba87d634d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;130cf439-e4ab-40bf-814c-79876e580707&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.417ae6ff-837a-426f-b651-d8e3e8810d90&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4c5befe6-9607-4968-815a-f396c2b0ca8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb327451-7bdd-4284-82a4-65c4d155ed90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validGlAccount(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3fe8d5e0-e16f-4a6e-887f-2f8d95d438d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;console.dir(error);
view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAccount.isGLFoundC&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;720c216a-6e60-4523-8155-107a9f73e9a0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_Panel3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;27ba0133-5fde-47ce-8f94-c4effb07df56&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Amount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;14c363c0-692f-4d0d-8c3c-29a3372c5fe4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f05d60d6-249d-4fc7-89f9-74fad3b7a8bd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;abef2c6e-f95d-445b-8567-8cafcd6abe62&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;86f86e34-c334-4a2c-83b2-0e4c6d2afd9b&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f6ca4ece-e47f-476f-8071-7b859e155f54&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb7fdbea-ea17-459e-8b6b-a75407e1ae2d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dbf3d84e-7bf2-4f4f-898f-7f53b8e7f195&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75d3ff89-a841-40ca-8aae-5a0e5f447a5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;cce5a5a3-1e36-4ff8-8f17-b51b0bb30695&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d399d500-ebc9-4fc4-87c8-1b3fe381535f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f165636a-b7af-434a-8538-0a279603c6e9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f34ff98-a602-4c7e-8add-27ddef21c21f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;48b8347a-0f81-4550-83d3-00e92e83ce25&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8c1f75ea-6c2f-46eb-8a13-f6b4859a24a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"50%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ee5c23cf-2ac3-4292-8651-fa85b2eb903c&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;df806345-ad6d-4085-873b-8fcbf1f51597&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;StandardExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6e2cc642-1f85-4669-96cb-8daa95f4b86a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;04e518c4-dc26-4452-831c-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.StandardExchangeRate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae0cec97-aa7e-4845-8bb4-fb5f4a18cc97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51447f61-d3ac-4591-8f0b-8c9b53ac609e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06955fce-d0cb-4d92-84e7-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAmount.standardExchangeRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6ebc0865-5cb2-4e31-85c1-234f7d627d68&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;NegotiatedExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0cae0d17-48c1-4361-9e6f-6408d0721d20&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;*************-4695-87c4-c0c03d9f3c73&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.NegotiatedExchangeRate&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;16c2d8f9-b12d-4b34-8712-9581ff52f452&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9c7c9408-96c5-41eb-8fef-afbdb5570921&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;21c50642-26b2-471b-80c8-a5cc003b1607&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hideThousandsSeparator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b1f23df-3aa8-4bbe-85c1-4768aecefb03&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.debitedAmountSum(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36eda037-f49c-4544-838e-4223a3cbdd09&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAmount.negotiatedExchangeRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2f5a82f5-9094-4069-820b-c5e6594ead07&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e97a04b-ed04-49f5-8299-d8027d452665&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 7&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b82fe0cf-664b-4546-8d73-4ca65d83956d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43080527-eaa3-45e8-820e-c4d7ec18a189&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;573ad3af-9055-47bf-8aeb-571b278dbbfa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"50%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;462d3dca-a994-46b5-83cb-8db8e84198e4&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d7d41cbe-1929-4bb2-8147-33ff793182a2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DebitedAmountinAccountCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;baa6f76e-92b7-4500-bd6b-2e48a753afda&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6869d640-3f97-4e5d-89d6-7c5c2079accc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DebitedAmountinAccountCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b9aa864-eddc-4d4c-80cd-07ad842ec672&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b89e33e-58d9-472e-8ca6-b7df7f30b7b6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b33e38d-31ba-4b33-8ed6-500889c727ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.CommissionsAndChargesDetails.currentItem.debitedAmount.debitedAmtinAccountCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b72921a3-9911-4568-823d-9938c16ffdd4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d06fa146-c908-41b0-86da-9c1fb672f4d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Exchange Rate&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c203db9-7027-4f74-8442-d59054be71c1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef13bcff-**************-1fda3a13b870&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c85f92b9-bd4a-44f8-8149-16686d420122&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.55393dcd-3352-41df-9690-75cb207d48b8&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9f7a65c9-374f-416c-8dcb-09ebf47f993d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;80f77ddc-9bbf-45bd-8d40-e36ced4073b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59659e32-c16d-44ea-8874-42c0e8e203a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExServiceResults(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6a0c40f5-960c-4429-8536-0bd2eb65a0d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.exRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>5d07b7f4-152e-4650-950b-6c633283e074</guid>
        <versionId>a0033234-5701-409e-ae02-871323b4035c</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="CommissionsAndChargesDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.65230fe4-4eec-44c8-a5ab-263fd5987213</coachViewBindingTypeId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>true</isList>
            <classId>/12.89a53d06-50b8-41df-a5cc-5e4f61147b6d</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>c91e0f8f-dfce-4464-96f2-f61f9c99d966</guid>
            <versionId>fcf294ca-60fc-41c4-aa9c-3b243cf9975e</versionId>
        </bindingType>
        <configOption name="CommAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.25dcf2d1-7917-4799-9cff-8c443cb1573e</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>f0aeee2e-847b-4969-bb68-3da9d2d0c45b</guid>
            <versionId>201ed871-d744-4abe-a253-58c52e290920</versionId>
        </configOption>
        <configOption name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b9c2f55f-ed92-4439-ab7d-83a9520e09ff</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>20f7a06e-193e-4cc6-a47a-4a5d73840066</guid>
            <versionId>40bfaa13-0875-48b6-9ff4-7af74c05761a</versionId>
        </configOption>
        <configOption name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8fb835ed-76a1-4b64-8ccc-2ffc45b5c0a5</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>05fcb809-0e73-4c78-a212-3610c7256065</guid>
            <versionId>78956e94-db69-4dcc-aab2-aa7ec00d1e91</versionId>
        </configOption>
        <configOption name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.874f8937-7b87-4c36-85ea-1d51e4001b0c</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>52d04350-0085-4b1e-bb59-5467f998441c</guid>
            <versionId>7e613835-de9e-4226-ab9a-26335bbe2b07</versionId>
        </configOption>
        <configOption name="draweeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.4960f236-ac77-454d-a1bf-77af9e54106f</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>d42a6316-00fc-429f-9225-858051fcc7b8</guid>
            <versionId>93a22e33-ece8-45c2-ba84-e51d34ca3d91</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.1bb83c79-3a1c-4536-a466-2bb7991eb3c7</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>81adb043-8e4e-471e-82c0-7b53bde9a791</guid>
            <versionId>09a7b80e-a0e7-4bdd-b3e0-d7786083a855</versionId>
        </configOption>
        <configOption name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.700403af-b260-4606-9af3-328407f990e4</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>05c0626d-f3e1-465d-8bb3-5bb91e061809</guid>
            <versionId>54aed6a9-2800-44a0-bbda-6624b1ccd670</versionId>
        </configOption>
        <configOption name="accountIndexC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8dd9da4d-8b5b-4113-b58c-0b5d67823d6c</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>25741c87-7d1b-4b67-8994-fb4e8e217279</guid>
            <versionId>29db02ad-a58b-4535-8773-47520f1306e6</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.86795e0a-eb11-40d6-bb89-b3a282e65dad</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>0e1abf67-b047-4f29-9ff5-2e2c85379bb5</guid>
            <versionId>f18dec68-d469-4902-8ae5-a417aebf219a</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.4b3f127f-24e9-471f-bfa3-34b4075cb72b</coachViewConfigOptionId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>6072e10a-06f8-4824-85d5-955d78f50f7a</guid>
            <versionId>fc4c6eb7-c35d-4a7e-9d4c-786de622ed7b</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.f89958e3-afcb-4002-b828-6184769a0275</coachViewInlineScriptId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.debitedAmountSum = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	var changeAm = this.ui.get("ChangeAmount["+index+"]").getData();&#xD;
	var nRate = this.ui.get("NegotiatedExchangeRate["+index+"]").getData();&#xD;
	var sum = changeAm * nRate;&#xD;
	this.ui.get("DebitedAmountinAccountCurrency["+index+"]").setData(sum);&#xD;
}&#xD;
&#xD;
this.executeService = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if (value.getData() == "Customer Account"){&#xD;
       	this.ui.get("AccountBranchCode1["+index+"]").setEnabled(false);&#xD;
       	this.ui.get("AccountCurrency1["+index+"]").setEnabled(false);&#xD;
		this.ui.get("AccountNumberGL["+index+"]").setEnabled(false);&#xD;
        	this.ui.get("VerifyGLAccountBtn["+index+"]").setEnabled(false);&#xD;
       	this.ui.get("Owner1["+index+"]").setEnabled(true);&#xD;
    		if (this.ui.get("Owner1["+index+"]").getData() != "") {&#xD;
   	  		this.ui.get("AccountNumber["+index+"]").setEnabled(true);&#xD;
    	  		this.ui.get("AccountNumberGL["+index+"]").setData("");&#xD;
    		}&#xD;
	}else if (value.getData() == "GL Account"){&#xD;
		this.ui.get("AccountBranchCode1["+index+"]").setEnabled(true);&#xD;
       	this.ui.get("AccountCurrency1["+index+"]").setEnabled(true);&#xD;
		this.ui.get("AccountNumber["+index+"]").setEnabled(false);&#xD;
		this.ui.get("Owner1["+index+"]").setEnabled(false);&#xD;
   	  	this.ui.get("VerifyGLAccountBtn["+index+"]").setEnabled(true);&#xD;
		this.ui.get("AccountNumberGL["+index+"]").setEnabled(true);&#xD;
		this.ui.get("AccountNumber["+index+"]").setData("");&#xD;
		this.ui.get("Decimal1["+index+"]").setData("");&#xD;
		this.ui.get("Output_Text1["+index+"]").setData("");&#xD;
		this.ui.get("AccountBranchCode1["+index+"]").setData("");&#xD;
		this.ui.get("AccountCurrency1["+index+"]").setData("");&#xD;
    }&#xD;
}&#xD;
&#xD;
this.executeGL = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
//	value.setEnabled(false);&#xD;
	this.context.options.accountIndexC.set("value", value.ui.getIndex());&#xD;
	var data = this.context.binding.get("value").get(index).get("debitedAccount").get("GLAccountNumber");&#xD;
	this.ui.get("CheckexistingGLAccount1["+index+"]").execute(data);&#xD;
}&#xD;
&#xD;
this.setAccountInfo = function (value){&#xD;
	for (var i=0; i&lt;this.context.options.CommAccountList.get("value").length(); i++) {&#xD;
		if (this.context.options.CommAccountList.get("value").get(i).get("accountNO") == value.getData()) {&#xD;
		&#xD;
			// this.context.options.commClassCode.set("value", this.context.options.CommAccountList.get("value").get(i).get("accountClassCode"));&#xD;
			// var commClassCode =  this.context.options.commClassCode.get("value");//SA04&#xD;
			commClassCode = this.context.options.CommAccountList.get("value").get(i).get("accountClassCode");&#xD;
			var code = commClassCode.substring(0,1);&#xD;
			&#xD;
			if (code == "O" || code == "D"){		&#xD;
				this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("isOverDraft", true);&#xD;
			}else{&#xD;
				this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("isOverDraft", false);&#xD;
			}&#xD;
			&#xD;
			this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("accountBranchCode", this.context.options.CommAccountList.get("value").get(i).get("branchCode"));&#xD;
			this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("accountCurrency",{});&#xD;
			this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").get("accountCurrency").set("code", this.context.options.CommAccountList.get("value").get(i).get("currencyCode"));&#xD;
			this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("accountBalance", this.context.options.CommAccountList.get("value").get(i).get("balance"));&#xD;
			this.context.binding.get("value").get(value.ui.getIndex()).get("debitedAccount").set("balanceSign", this.context.options.CommAccountList.get("value").get(i).get("balanceType"));&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.validGlAccount = function (value){&#xD;
	record = value.ui.getIndex();&#xD;
	if (!this.context.binding.get("value").get(record).get("debitedAccount").get("isGLFoundC")) {&#xD;
		this.ui.get("AccountNumberGL["+record+"]").setValid(false, "Account Not Found!");&#xD;
	}else{&#xD;
		this.ui.get("AccountNumberGL["+record+"]").setValid(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setPartyCIF = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.ui.get("AccountNumber["+index+"]").setEnabled(true);&#xD;
	if (value.getData() == "Accountee") {&#xD;
		this.context.binding.get("value").get(index).get("debitedAccount").set("commCIF", this.context.options.accounteeCIF.get("value"));&#xD;
	}else if (value.getData() == "Case In Need") {&#xD;
		this.context.binding.get("value").get(index).get("debitedAccount").set("commCIF", this.context.options.caseCIF.get("value"));&#xD;
	}else{&#xD;
		this.context.binding.get("value").get(index).get("debitedAccount").set("commCIF", this.context.options.draweeCIF.get("value"));&#xD;
	}&#xD;
	this.ui.get("GetCustomerAccount1["+index+"]").execute(this.context.binding.get("value").get(index).get("debitedAccount").get("commCIF"));&#xD;
}&#xD;
&#xD;
this.setExchangeRate = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if(value.getData() != "" &amp;&amp; value.getData() != null &amp;&amp; value.getData() != undefined){&#xD;
		if (this.ui.get("defaultCurrency["+index+"]").getData() == value.getData()) {&#xD;
			this.ui.get("StandardExchangeRate["+index+"]").setData(1.0);&#xD;
			this.ui.get("NegotiatedExchangeRate["+index+"]").setData(this.ui.get("StandardExchangeRate["+index+"]").getData());&#xD;
		}else{&#xD;
			var defaultCurrency = this.ui.get("defaultCurrency["+index+"]").getData();&#xD;
			var accountCurrency = value.getData();&#xD;
			concatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:"TRANSFER" , sType:"S"};&#xD;
			var inputCurr = JSON.stringify(concatedCurrency);&#xD;
			this.ui.get("GetExchangeRate["+index+"]").execute(inputCurr);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.setExServiceResults = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.ui.get("StandardExchangeRate["+index+"]").setData(this.context.options.exRate.get("value"));&#xD;
	this.ui.get("NegotiatedExchangeRate["+index+"]").setData(this.ui.get("StandardExchangeRate["+index+"]").getData());&#xD;
	this.ui.get("NegotiatedExchangeRate["+index+"]").setEnabled(true);&#xD;
}&#xD;
&#xD;
this.setCommAccounts = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.context.binding.get("value").get(index).set("commAccountList" , []);&#xD;
	for (var i=0; i&lt;this.context.options.CommAccountList.get("value").length(); i++) {&#xD;
		this.context.binding.get("value").get(index).get("commAccountList").add({name:this.context.options.CommAccountList.get("value").get(i).get("accountNO") , value:this.context.options.CommAccountList.get("value").get(i).get("accountNO")});&#xD;
	}&#xD;
}&#xD;
&#xD;
this.resetGLButton = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.ui.get("VerifyGLAccountBtn["+index+"]").setData(false);&#xD;
}&#xD;
&#xD;
this.accountVis = function(value){&#xD;
	value.setEnabled(false);&#xD;
	var index = value.ui.getIndex();&#xD;
	if(this.context.options.isChecker.get("value") == true || this.ui.get("Owner1["+index+"]").getData() == ""){&#xD;
		value.setEnabled(false);&#xD;
	}else&#xD;
		value.setEnabled(true);&#xD;
}&#xD;
&#xD;
this.validateDigits = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if(isNaN(Number(value.getData()))){&#xD;
		value.setData("");&#xD;
		value.setValid(false ,"must be digits");&#xD;
		return false;&#xD;
	}else&#xD;
	{&#xD;
		value.setValid(true);&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
this.validateOverDraft = function (value) {&#xD;
	var index = value.ui.getIndex();&#xD;
	if (this.context.binding.get("value").get(index).get("debitedAccount").get("isOverDraft") == true) {&#xD;
		this.ui.get("DebitedAmountinAccountCurrency["+index+"]").setValid(false,"WARNING: Must be &lt; Account Balance");&#xD;
	}else{&#xD;
		this.ui.get("DebitedAmountinAccountCurrency["+index+"]").setValid(true);&#xD;
	}&#xD;
}&#xD;
//--------------------------------------------------------------------------------Drop_2----------------------------------------------------------------------------------------&#xD;
//------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>36d18735-5a23-44e6-bdbc-a205c207af2e</guid>
            <versionId>cbc8fe9a-0255-4232-88cd-550c89c918e1</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.fe027bd0-e768-4491-b57b-033bb2865bd4</coachViewLocalResId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>7c970179-92c1-46bd-81bf-773fa9d42a25</guid>
            <versionId>bf0dbc20-7faf-434e-a0a5-f5c5b6c641cf</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.3b163c8b-1358-469e-9415-1b452c5d4095</coachViewLocalResId>
            <coachViewId>64.e45b18e8-a83a-4484-8089-b2ab3c33146a</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>672c62e5-c0bf-425e-9af8-129e2dc3a173</guid>
            <versionId>20f42ced-b49c-45f9-9993-ced3fdda50a2</versionId>
        </localization>
    </coachView>
</teamworks>

