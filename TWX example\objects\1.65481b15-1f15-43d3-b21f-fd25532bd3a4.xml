<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.65481b15-1f15-43d3-b21f-fd25532bd3a4" name="Validate Collateral Amount">
        <lastModified>1692789024295</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:3619</guid>
        <versionId>ce761e63-874e-4d57-8f2d-e48808e443de</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:1b86" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":5,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"46e66179-fa99-4c5e-8eac-c5f762c13a54"},{"incoming":["6fac77c1-07c9-4d05-8006-************","1fa54440-cc95-4516-8b8e-ea314c444c96"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:83e1efe624431d49:-7084ad56:189daab98ba:361b"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"bca98841-731b-415a-8dab-c2c41178ef9f"},{"targetRef":"43371b51-d899-4cde-8d25-544d0bf83edc","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Need Service?","declaredType":"sequenceFlow","id":"2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6","sourceRef":"46e66179-fa99-4c5e-8eac-c5f762c13a54"},{"startQuantity":1,"outgoing":["c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6"],"incoming":["2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":333,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Exchange Rate","dataInputAssociation":[{"targetRef":"2055.4f02925b-11da-44b4-9ffd-ff1068446fe0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.exConcatedString"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6d140118-ace3-49c1-847f-1d0bbe490aad","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.a0f02886-5e9f-473a-8ace-7a356e991464"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rate"]}}],"sourceRef":["2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce"]}],"calledElement":"1.55393dcd-3352-41df-9690-75cb207d48b8"},{"targetRef":"9f71c0f6-bce5-48b4-85cd-b48ceaf35667","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6","sourceRef":"6d140118-ace3-49c1-847f-1d0bbe490aad"},{"startQuantity":1,"outgoing":["2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52"],"incoming":["540613c5-3eac-4ac2-8d50-d86fcc54155f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":184,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"02f3765e-12d9-42fa-8e39-9a1c2c863a40","scriptFormat":"text\/x-javascript","script":{"content":["var fromC = tw.local.documentCurrency;\r\nvar toC = tw.local.collateralCurrency;\r\nvar concatedCurrency = {ccFrom : fromC , ccTo : toC , type:\"TRANSFER\" , sType:\"S\"};\r\nvar inputCurr = JSON.stringify(concatedCurrency);\r\n\r\ntw.local.exConcatedString = inputCurr;"]}},{"targetRef":"6d140118-ace3-49c1-847f-1d0bbe490aad","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Exchange Rate","declaredType":"sequenceFlow","id":"2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52","sourceRef":"02f3765e-12d9-42fa-8e39-9a1c2c863a40"},{"startQuantity":1,"outgoing":["6fac77c1-07c9-4d05-8006-************"],"incoming":["c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":511,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"9f71c0f6-bce5-48b4-85cd-b48ceaf35667","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = false;\r\nif (tw.local.rate &gt; 0.0) {\r\n\tvar sum = tw.local.collateralAmount * tw.local.rate;\r\n\tif (sum &gt; tw.local.amtPayableByNBE) {\r\n\t\ttw.local.results = true;\r\n\t}else{\r\n\t\ttw.local.results = false;\r\n\t}\r\n}"]}},{"targetRef":"bca98841-731b-415a-8dab-c2c41178ef9f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6fac77c1-07c9-4d05-8006-************","sourceRef":"9f71c0f6-bce5-48b4-85cd-b48ceaf35667"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"exConcatedString","isCollection":false,"declaredType":"dataObject","id":"2056.b23b9c95-07fd-4116-87a1-75049adfc755"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"rate","isCollection":false,"declaredType":"dataObject","id":"2056.126d5fc0-4f7a-4a6a-8e81-65079b3a8015"},{"parallelMultiple":false,"outgoing":["3298ba04-0389-426c-82db-610707b3f96d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"760e1a49-9fb3-4dec-8538-3099ede6c3ae"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0f1442b7-4cc1-4441-80e7-11c6132853fe","otherAttributes":{"eventImplId":"43a5e1e0-4b97-4e36-8d6c-1b0a5af14938"}}],"attachedToRef":"02f3765e-12d9-42fa-8e39-9a1c2c863a40","extensionElements":{"nodeVisualInfo":[{"width":24,"x":219,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"26a765e2-ef5d-4326-8d4a-7b091fc2a37b","outputSet":{}},{"parallelMultiple":false,"outgoing":["1025d5e8-745f-4134-8b2b-65fe51f7277d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8bedad0b-afea-4fac-8c2f-7fb0f433cf4f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"16f18a12-4aeb-4a7d-864b-d1d4fd4dda03","otherAttributes":{"eventImplId":"80b70f97-09cb-46e8-8209-d19601719a95"}}],"attachedToRef":"9f71c0f6-bce5-48b4-85cd-b48ceaf35667","extensionElements":{"nodeVisualInfo":[{"width":24,"x":546,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"6e93825b-2703-4691-8c1f-c16bf20f1852","outputSet":{}},{"incoming":["3298ba04-0389-426c-82db-610707b3f96d","1025d5e8-745f-4134-8b2b-65fe51f7277d"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e1fd9004-9be9-410f-86b6-f75922ae34df","otherAttributes":{"eventImplId":"840620e0-eaff-4c98-8300-a9bb9dc8a988"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":415,"y":188,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Validate Collateral Amount -&gt; Log Error ]- start\");\r\n\/\/log.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Validate Collateral Amount -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\nif (tw.local.error == null || tw.local.error == undefined) {\r\n\ttw.local.error = new tw.object.AjaxError();\r\n}\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"6c18cc85-d8b7-4052-823b-fe90073f8946"},{"targetRef":"6c18cc85-d8b7-4052-823b-fe90073f8946","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"3298ba04-0389-426c-82db-610707b3f96d","sourceRef":"26a765e2-ef5d-4326-8d4a-7b091fc2a37b"},{"targetRef":"6c18cc85-d8b7-4052-823b-fe90073f8946","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"1025d5e8-745f-4134-8b2b-65fe51f7277d","sourceRef":"6e93825b-2703-4691-8c1f-c16bf20f1852"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c50298b7-9bf5-4244-8df7-6fa69210fd08"},{"outgoing":["540613c5-3eac-4ac2-8d50-d86fcc54155f","c46de1a0-b808-4bf2-87d9-6e375841eb0f"],"incoming":["2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6"],"default":"540613c5-3eac-4ac2-8d50-d86fcc54155f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":50,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Need Service?","declaredType":"exclusiveGateway","id":"43371b51-d899-4cde-8d25-544d0bf83edc"},{"targetRef":"02f3765e-12d9-42fa-8e39-9a1c2c863a40","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map Input","declaredType":"sequenceFlow","id":"540613c5-3eac-4ac2-8d50-d86fcc54155f","sourceRef":"43371b51-d899-4cde-8d25-544d0bf83edc"},{"startQuantity":1,"outgoing":["1fa54440-cc95-4516-8b8e-ea314c444c96"],"incoming":["c46de1a0-b808-4bf2-87d9-6e375841eb0f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":49,"y":178,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f9be60d1-31cb-4120-8036-306da12c1c66","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = false;"]}},{"targetRef":"f9be60d1-31cb-4120-8036-306da12c1c66","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.collateralCurrency\t  ==\t  tw.local.documentCurrency"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"c46de1a0-b808-4bf2-87d9-6e375841eb0f","sourceRef":"43371b51-d899-4cde-8d25-544d0bf83edc"},{"targetRef":"bca98841-731b-415a-8dab-c2c41178ef9f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"1fa54440-cc95-4516-8b8e-ea314c444c96","sourceRef":"f9be60d1-31cb-4120-8036-306da12c1c66"}],"laneSet":[{"id":"47531938-94d9-4486-84c1-a68e9b5b6536","lane":[{"flowNodeRef":["46e66179-fa99-4c5e-8eac-c5f762c13a54","bca98841-731b-415a-8dab-c2c41178ef9f","6d140118-ace3-49c1-847f-1d0bbe490aad","02f3765e-12d9-42fa-8e39-9a1c2c863a40","9f71c0f6-bce5-48b4-85cd-b48ceaf35667","26a765e2-ef5d-4326-8d4a-7b091fc2a37b","6e93825b-2703-4691-8c1f-c16bf20f1852","6c18cc85-d8b7-4052-823b-fe90073f8946","43371b51-d899-4cde-8d25-544d0bf83edc","f9be60d1-31cb-4120-8036-306da12c1c66"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"e213e9db-babd-4b87-896c-7463937fbc81","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Validate Collateral Amount","declaredType":"process","id":"1.65481b15-1f15-43d3-b21f-fd25532bd3a4","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.2e5c2d36-1cb6-4404-865e-ca49af04774b"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.42c73093-59d8-44af-84b3-c09fc9e1c189"}],"inputSet":[{"dataInputRefs":["2055.0688d493-359b-4a0b-81bd-37e587fbd6e7","2055.5b452876-5cda-491a-8847-d4dfeef773b4","2055.c30a281a-a493-4d2b-80dd-4cee029be76d","2055.bedba264-3c78-4feb-8c7d-15bdb4b543a9"]}],"outputSet":[{"dataOutputRefs":["2055.2e5c2d36-1cb6-4404-865e-ca49af04774b","2055.42c73093-59d8-44af-84b3-c09fc9e1c189"]}],"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"documentCurrency","isCollection":false,"id":"2055.0688d493-359b-4a0b-81bd-37e587fbd6e7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"collateralCurrency","isCollection":false,"id":"2055.5b452876-5cda-491a-8847-d4dfeef773b4"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"collateralAmount","isCollection":false,"id":"2055.c30a281a-a493-4d2b-80dd-4cee029be76d"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"amtPayableByNBE","isCollection":false,"id":"2055.bedba264-3c78-4feb-8c7d-15bdb4b543a9"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="documentCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0688d493-359b-4a0b-81bd-37e587fbd6e7</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c5e47a16-bd20-4693-a304-a0e70b2f731a</guid>
            <versionId>8b046e1c-bffc-4bb7-b013-c757067c63cd</versionId>
        </processParameter>
        <processParameter name="collateralCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5b452876-5cda-491a-8847-d4dfeef773b4</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3da8843d-452c-4acd-9baf-0db413b334d8</guid>
            <versionId>ac9a6d83-abf6-49d9-abb5-e44a14b58c0c</versionId>
        </processParameter>
        <processParameter name="collateralAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c30a281a-a493-4d2b-80dd-4cee029be76d</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4b137821-d4fe-4959-9645-a128aa8b7b75</guid>
            <versionId>89a2e055-0ce8-48a4-9037-e562cba975f0</versionId>
        </processParameter>
        <processParameter name="amtPayableByNBE">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bedba264-3c78-4feb-8c7d-15bdb4b543a9</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>719d3c40-3e10-46df-a95b-aea9dd1b62ae</guid>
            <versionId>e1cde101-2159-4c96-a8d9-34e6f088b4eb</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2e5c2d36-1cb6-4404-865e-ca49af04774b</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0d63c59f-0365-4675-92ef-06d587e6ddf2</guid>
            <versionId>853c54f0-7349-413d-b972-b0467bd7f7a3</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.42c73093-59d8-44af-84b3-c09fc9e1c189</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5663ddbc-2519-4bdd-af7f-4e0542e6cf5e</guid>
            <versionId>4f1f786a-4612-48a9-b216-0c35f27a6125</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.70ac7686-5217-477b-a8de-731a133e2756</processParameterId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>09d0cbad-c634-4b85-9b38-4217335110d5</guid>
            <versionId>e09b65a7-d27c-4282-b39f-************</versionId>
        </processParameter>
        <processVariable name="exConcatedString">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b23b9c95-07fd-4116-87a1-75049adfc755</processVariableId>
            <description isNull="true" />
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e120d7fa-ad42-446d-b7cf-8d2aca558bc7</guid>
            <versionId>0cc9c21a-5b2e-4cce-b73d-1ff510ff6d08</versionId>
        </processVariable>
        <processVariable name="rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.126d5fc0-4f7a-4a6a-8e81-65079b3a8015</processVariableId>
            <description isNull="true" />
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6dbbeff0-e737-4d42-a03d-f2d5e3600251</guid>
            <versionId>f3b0b29e-493a-4e1f-ae65-d4b93142de8c</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c50298b7-9bf5-4244-8df7-6fa69210fd08</processVariableId>
            <description isNull="true" />
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cec99c52-20c9-4244-a61f-e2b4e9d54aee</guid>
            <versionId>5403841c-3e77-4e5b-8160-8e39c66aff60</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9f71c0f6-bce5-48b4-85cd-b48ceaf35667</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.cac732e5-7de8-43bf-8d0f-fff68807d145</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:43c1</guid>
            <versionId>18b4c537-ed4f-40df-8b75-c21dd9b62ddf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="511" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189daab98ba:49dd</errorHandlerItem>
                <errorHandlerItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.cac732e5-7de8-43bf-8d0f-fff68807d145</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = false;&#xD;
if (tw.local.rate &gt; 0.0) {&#xD;
	var sum = tw.local.collateralAmount * tw.local.rate;&#xD;
	if (sum &gt; tw.local.amtPayableByNBE) {&#xD;
		tw.local.results = true;&#xD;
	}else{&#xD;
		tw.local.results = false;&#xD;
	}&#xD;
}</script>
                <isRule>false</isRule>
                <guid>34089f3d-b12e-43ee-9015-31a7942e16c8</guid>
                <versionId>18c3feb7-96d6-46c1-b49f-ab6d421cc972</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.02f3765e-12d9-42fa-8e39-9a1c2c863a40</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>Map Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.79b5534b-c33b-4331-8bb6-24155688dc13</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</errorHandlerItemId>
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:43c0</guid>
            <versionId>61274717-bd91-4155-8d6d-92a57c0be83c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="184" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189daab98ba:49dd</errorHandlerItem>
                <errorHandlerItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.79b5534b-c33b-4331-8bb6-24155688dc13</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var fromC = tw.local.documentCurrency;&#xD;
var toC = tw.local.collateralCurrency;&#xD;
var concatedCurrency = {ccFrom : fromC , ccTo : toC , type:"TRANSFER" , sType:"S"};&#xD;
var inputCurr = JSON.stringify(concatedCurrency);&#xD;
&#xD;
tw.local.exConcatedString = inputCurr;</script>
                <isRule>false</isRule>
                <guid>20561f63-b6e2-40c9-97a3-132a18846dec</guid>
                <versionId>26ae1bba-0d3a-4304-aec1-814a88ec0a93</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f9be60d1-31cb-4120-8036-306da12c1c66</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.46aff5b0-2e17-4e78-b9fc-b19b95ca12af</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:4b40</guid>
            <versionId>6ce9cbea-aa39-484e-a817-246f83ca0e2e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="49" y="178">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.46aff5b0-2e17-4e78-b9fc-b19b95ca12af</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = false;</script>
                <isRule>false</isRule>
                <guid>b9b0a3f3-8ac4-423c-8f8c-0cb9c19ef186</guid>
                <versionId>4059b6f9-70d7-4fd9-99f0-228dbbaeac2a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6d140118-ace3-49c1-847f-1d0bbe490aad</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>Get Exchange Rate</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6e6735b6-9a1b-4d96-b826-e377708db3bc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:362d</guid>
            <versionId>95bffd2b-ff03-44cf-b1e3-c23ff291b9fb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="333" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6e6735b6-9a1b-4d96-b826-e377708db3bc</subProcessId>
                <attachedProcessRef>/1.55393dcd-3352-41df-9690-75cb207d48b8</attachedProcessRef>
                <guid>5c568cf7-ad71-4311-9cda-915f3e4a8ec6</guid>
                <versionId>5d1665a3-d09f-4dcc-9268-056bef2d1fac</versionId>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b58428f9-84c1-4b04-9e39-2e02b665d2d8</parameterMappingId>
                    <processParameterId>2055.a0f02886-5e9f-473a-8ace-7a356e991464</processParameterId>
                    <parameterMappingParentId>3012.6e6735b6-9a1b-4d96-b826-e377708db3bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3a6080af-aa5a-4785-b677-615ba3115a19</guid>
                    <versionId>389e30b1-a48a-4c41-9f26-4c3506781d1a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a081a684-aff0-4d48-996c-b73e6de8e2d2</parameterMappingId>
                    <processParameterId>2055.4f02925b-11da-44b4-9ffd-ff1068446fe0</processParameterId>
                    <parameterMappingParentId>3012.6e6735b6-9a1b-4d96-b826-e377708db3bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.exConcatedString</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>34e15984-f8eb-478f-af9a-530a54158cb0</guid>
                    <versionId>5c516a5d-6114-4f9f-b576-9cad462ccf25</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cfd946f9-230d-44db-9155-08d078e79f02</parameterMappingId>
                    <processParameterId>2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce</processParameterId>
                    <parameterMappingParentId>3012.6e6735b6-9a1b-4d96-b826-e377708db3bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rate</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>52bc117f-b294-401f-893d-7f8dcda9307b</guid>
                    <versionId>9f9a4925-fb70-46f5-8bf5-d5f24429abe6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.3739bc21-536a-4be9-ac40-768add542859</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:49dd</guid>
            <versionId>cbb8e66e-a11a-48e8-b64a-00fa27cfcf3d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4cdcf78e-6dcb-42da-807c-7a1d739ba4e4</processItemPrePostId>
                <processItemId>2025.6c18cc85-d8b7-4052-823b-fe90073f8946</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Validate Collateral Amount -&gt; Log Error ]- start");&#xD;
//log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Validate Collateral Amount -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
if (tw.local.error == null || tw.local.error == undefined) {&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>ed655342-b932-4c40-b53a-7a8d9471cbb1</guid>
                <versionId>d7f4ace4-c551-4dcd-80f1-8e5b744fbefe</versionId>
            </processPrePosts>
            <layoutData x="415" y="188">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.3739bc21-536a-4be9-ac40-768add542859</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>b9cf7b45-aebb-45ae-b03b-b2ea1a0f0b1e</guid>
                <versionId>d61ddf47-e133-4785-afe1-692086f30715</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fc9c0552-cbc6-4776-ac99-ff4892764480</parameterMappingId>
                    <processParameterId>2055.70ac7686-5217-477b-a8de-731a133e2756</processParameterId>
                    <parameterMappingParentId>3007.3739bc21-536a-4be9-ac40-768add542859</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a55b23f8-59da-47d6-8e7a-ea1870405f2c</guid>
                    <versionId>cddae322-fda6-4a9d-ba22-f0af77408d33</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bca98841-731b-415a-8dab-c2c41178ef9f</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.7f62d68e-8135-41c6-9274-38d8ae89becc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:361b</guid>
            <versionId>e0e82360-036f-4cfe-aa6a-e3a0873c8ac2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.7f62d68e-8135-41c6-9274-38d8ae89becc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>e6c1c8da-ab33-47f8-b977-2acefe476abc</guid>
                <versionId>08ee4a82-e23e-431c-a52d-57a10e8ed57c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</processItemId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <name>Need Service?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.919cf6c8-a98e-4e93-addf-631cad46636e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:4ad8</guid>
            <versionId>fc2af748-57cc-4904-bba7-d88ed6e3ba20</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="50" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.919cf6c8-a98e-4e93-addf-631cad46636e</switchId>
                <guid>2a821625-f80a-416b-9864-7533f45894fa</guid>
                <versionId>51acbf97-7433-4c62-b750-453d6a7a0ab1</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.23d7f75b-8774-4f17-a179-9eb8b901a0c9</switchConditionId>
                    <switchId>3013.919cf6c8-a98e-4e93-addf-631cad46636e</switchId>
                    <seq>1</seq>
                    <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:1b85</endStateId>
                    <condition>tw.local.collateralCurrency	  ==	  tw.local.documentCurrency</condition>
                    <guid>e3906122-6a42-4e26-b48e-08f9369bc6f0</guid>
                    <versionId>b400a912-4aaa-4408-a10b-aed0a08210a7</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="5" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Validate Collateral Amount" id="1.65481b15-1f15-43d3-b21f-fd25532bd3a4" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="documentCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0688d493-359b-4a0b-81bd-37e587fbd6e7" />
                        
                        
                        <ns16:dataInput name="collateralCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5b452876-5cda-491a-8847-d4dfeef773b4" />
                        
                        
                        <ns16:dataInput name="collateralAmount" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.c30a281a-a493-4d2b-80dd-4cee029be76d" />
                        
                        
                        <ns16:dataInput name="amtPayableByNBE" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.bedba264-3c78-4feb-8c7d-15bdb4b543a9" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.2e5c2d36-1cb6-4404-865e-ca49af04774b" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.42c73093-59d8-44af-84b3-c09fc9e1c189" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.0688d493-359b-4a0b-81bd-37e587fbd6e7</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5b452876-5cda-491a-8847-d4dfeef773b4</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.c30a281a-a493-4d2b-80dd-4cee029be76d</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.bedba264-3c78-4feb-8c7d-15bdb4b543a9</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.2e5c2d36-1cb6-4404-865e-ca49af04774b</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.42c73093-59d8-44af-84b3-c09fc9e1c189</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="47531938-94d9-4486-84c1-a68e9b5b6536">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="e213e9db-babd-4b87-896c-7463937fbc81" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>46e66179-fa99-4c5e-8eac-c5f762c13a54</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bca98841-731b-415a-8dab-c2c41178ef9f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6d140118-ace3-49c1-847f-1d0bbe490aad</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>02f3765e-12d9-42fa-8e39-9a1c2c863a40</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9f71c0f6-bce5-48b4-85cd-b48ceaf35667</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>26a765e2-ef5d-4326-8d4a-7b091fc2a37b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6e93825b-2703-4691-8c1f-c16bf20f1852</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6c18cc85-d8b7-4052-823b-fe90073f8946</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>43371b51-d899-4cde-8d25-544d0bf83edc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f9be60d1-31cb-4120-8036-306da12c1c66</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="46e66179-fa99-4c5e-8eac-c5f762c13a54">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="5" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="bca98841-731b-415a-8dab-c2c41178ef9f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:361b</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6fac77c1-07c9-4d05-8006-************</ns16:incoming>
                        
                        
                        <ns16:incoming>1fa54440-cc95-4516-8b8e-ea314c444c96</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="46e66179-fa99-4c5e-8eac-c5f762c13a54" targetRef="43371b51-d899-4cde-8d25-544d0bf83edc" name="To Need Service?" id="2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.55393dcd-3352-41df-9690-75cb207d48b8" name="Get Exchange Rate" id="6d140118-ace3-49c1-847f-1d0bbe490aad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="333" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52</ns16:incoming>
                        
                        
                        <ns16:outgoing>c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4f02925b-11da-44b4-9ffd-ff1068446fe0</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.exConcatedString</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a0f02886-5e9f-473a-8ace-7a356e991464</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c23b3f23-166d-481d-b3b1-f5ae145d92ce</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rate</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6d140118-ace3-49c1-847f-1d0bbe490aad" targetRef="9f71c0f6-bce5-48b4-85cd-b48ceaf35667" name="To Map output" id="c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Input" id="02f3765e-12d9-42fa-8e39-9a1c2c863a40">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="184" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>540613c5-3eac-4ac2-8d50-d86fcc54155f</ns16:incoming>
                        
                        
                        <ns16:outgoing>2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52</ns16:outgoing>
                        
                        
                        <ns16:script>var fromC = tw.local.documentCurrency;&#xD;
var toC = tw.local.collateralCurrency;&#xD;
var concatedCurrency = {ccFrom : fromC , ccTo : toC , type:"TRANSFER" , sType:"S"};&#xD;
var inputCurr = JSON.stringify(concatedCurrency);&#xD;
&#xD;
tw.local.exConcatedString = inputCurr;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="02f3765e-12d9-42fa-8e39-9a1c2c863a40" targetRef="6d140118-ace3-49c1-847f-1d0bbe490aad" name="To Get Exchange Rate" id="2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="9f71c0f6-bce5-48b4-85cd-b48ceaf35667">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="511" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6</ns16:incoming>
                        
                        
                        <ns16:outgoing>6fac77c1-07c9-4d05-8006-************</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = false;&#xD;
if (tw.local.rate &gt; 0.0) {&#xD;
	var sum = tw.local.collateralAmount * tw.local.rate;&#xD;
	if (sum &gt; tw.local.amtPayableByNBE) {&#xD;
		tw.local.results = true;&#xD;
	}else{&#xD;
		tw.local.results = false;&#xD;
	}&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="9f71c0f6-bce5-48b4-85cd-b48ceaf35667" targetRef="bca98841-731b-415a-8dab-c2c41178ef9f" name="To End" id="6fac77c1-07c9-4d05-8006-************">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="exConcatedString" id="2056.b23b9c95-07fd-4116-87a1-75049adfc755" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="rate" id="2056.126d5fc0-4f7a-4a6a-8e81-65079b3a8015" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="02f3765e-12d9-42fa-8e39-9a1c2c863a40" parallelMultiple="false" name="Error" id="26a765e2-ef5d-4326-8d4a-7b091fc2a37b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="219" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>3298ba04-0389-426c-82db-610707b3f96d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="760e1a49-9fb3-4dec-8538-3099ede6c3ae" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0f1442b7-4cc1-4441-80e7-11c6132853fe" eventImplId="43a5e1e0-4b97-4e36-8d6c-1b0a5af14938">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9f71c0f6-bce5-48b4-85cd-b48ceaf35667" parallelMultiple="false" name="Error2" id="6e93825b-2703-4691-8c1f-c16bf20f1852">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="546" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1025d5e8-745f-4134-8b2b-65fe51f7277d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8bedad0b-afea-4fac-8c2f-7fb0f433cf4f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="16f18a12-4aeb-4a7d-864b-d1d4fd4dda03" eventImplId="80b70f97-09cb-46e8-8209-d19601719a95">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="6c18cc85-d8b7-4052-823b-fe90073f8946">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="415" y="188" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Validate Collateral Amount -&gt; Log Error ]- start");&#xD;
//log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Validate Collateral Amount -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
if (tw.local.error == null || tw.local.error == undefined) {&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3298ba04-0389-426c-82db-610707b3f96d</ns16:incoming>
                        
                        
                        <ns16:incoming>1025d5e8-745f-4134-8b2b-65fe51f7277d</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="e1fd9004-9be9-410f-86b6-f75922ae34df" eventImplId="840620e0-eaff-4c98-8300-a9bb9dc8a988">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="26a765e2-ef5d-4326-8d4a-7b091fc2a37b" targetRef="6c18cc85-d8b7-4052-823b-fe90073f8946" name="To End Event" id="3298ba04-0389-426c-82db-610707b3f96d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6e93825b-2703-4691-8c1f-c16bf20f1852" targetRef="6c18cc85-d8b7-4052-823b-fe90073f8946" name="To End Event" id="1025d5e8-745f-4134-8b2b-65fe51f7277d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c50298b7-9bf5-4244-8df7-6fa69210fd08" />
                    
                    
                    <ns16:exclusiveGateway default="540613c5-3eac-4ac2-8d50-d86fcc54155f" name="Need Service?" id="43371b51-d899-4cde-8d25-544d0bf83edc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="50" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.a2ed0c3c-5df4-47dc-8bc6-a8c99574f2a6</ns16:incoming>
                        
                        
                        <ns16:outgoing>540613c5-3eac-4ac2-8d50-d86fcc54155f</ns16:outgoing>
                        
                        
                        <ns16:outgoing>c46de1a0-b808-4bf2-87d9-6e375841eb0f</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="43371b51-d899-4cde-8d25-544d0bf83edc" targetRef="02f3765e-12d9-42fa-8e39-9a1c2c863a40" name="To Map Input" id="540613c5-3eac-4ac2-8d50-d86fcc54155f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="f9be60d1-31cb-4120-8036-306da12c1c66">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="49" y="178" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c46de1a0-b808-4bf2-87d9-6e375841eb0f</ns16:incoming>
                        
                        
                        <ns16:outgoing>1fa54440-cc95-4516-8b8e-ea314c444c96</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = false;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="43371b51-d899-4cde-8d25-544d0bf83edc" targetRef="f9be60d1-31cb-4120-8036-306da12c1c66" name="To Script Task" id="c46de1a0-b808-4bf2-87d9-6e375841eb0f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.collateralCurrency	  ==	  tw.local.documentCurrency</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f9be60d1-31cb-4120-8036-306da12c1c66" targetRef="bca98841-731b-415a-8dab-c2c41178ef9f" name="To End" id="1fa54440-cc95-4516-8b8e-ea314c444c96">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6fac77c1-07c9-4d05-8006-************</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9f71c0f6-bce5-48b4-85cd-b48ceaf35667</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bca98841-731b-415a-8dab-c2c41178ef9f</toProcessItemId>
            <guid>5fa3e379-b146-4ca9-8fb9-864cb7fa14a4</guid>
            <versionId>24958f2e-f818-47e4-a832-160c7bffb06b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9f71c0f6-bce5-48b4-85cd-b48ceaf35667</fromProcessItemId>
            <toProcessItemId>2025.bca98841-731b-415a-8dab-c2c41178ef9f</toProcessItemId>
        </link>
        <link name="To Map Input">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.540613c5-3eac-4ac2-8d50-d86fcc54155f</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.02f3765e-12d9-42fa-8e39-9a1c2c863a40</toProcessItemId>
            <guid>b6cb0e2c-17b3-41f7-b5f0-39345e84610a</guid>
            <versionId>3166e8c2-3f39-4bc8-a913-ae108efacbb8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</fromProcessItemId>
            <toProcessItemId>2025.02f3765e-12d9-42fa-8e39-9a1c2c863a40</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c46de1a0-b808-4bf2-87d9-6e375841eb0f</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</fromProcessItemId>
            <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:1b85</endStateId>
            <toProcessItemId>2025.f9be60d1-31cb-4120-8036-306da12c1c66</toProcessItemId>
            <guid>837a2253-d364-4bc5-a960-371fde9bc791</guid>
            <versionId>46be46c8-ba7f-4a9c-b5b2-70b6ebedb76d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.43371b51-d899-4cde-8d25-544d0bf83edc</fromProcessItemId>
            <toProcessItemId>2025.f9be60d1-31cb-4120-8036-306da12c1c66</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c717f07a-1bc1-4ac5-8e27-8d4506ca5ac6</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6d140118-ace3-49c1-847f-1d0bbe490aad</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6158</endStateId>
            <toProcessItemId>2025.9f71c0f6-bce5-48b4-85cd-b48ceaf35667</toProcessItemId>
            <guid>c678c36b-4639-464b-b5d8-7e3b491bc6f6</guid>
            <versionId>76c07696-764c-4049-a6b8-f0ebc924b7ea</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6d140118-ace3-49c1-847f-1d0bbe490aad</fromProcessItemId>
            <toProcessItemId>2025.9f71c0f6-bce5-48b4-85cd-b48ceaf35667</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1fa54440-cc95-4516-8b8e-ea314c444c96</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f9be60d1-31cb-4120-8036-306da12c1c66</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bca98841-731b-415a-8dab-c2c41178ef9f</toProcessItemId>
            <guid>b39f8f34-9796-40fe-80e5-44d49f76add6</guid>
            <versionId>76f29e9e-188d-4563-8949-4150f2942137</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.f9be60d1-31cb-4120-8036-306da12c1c66</fromProcessItemId>
            <toProcessItemId>2025.bca98841-731b-415a-8dab-c2c41178ef9f</toProcessItemId>
        </link>
        <link name="To Get Exchange Rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2c5bbe3d-0776-460f-85b2-9f3d9b8c0c52</processLinkId>
            <processId>1.65481b15-1f15-43d3-b21f-fd25532bd3a4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.02f3765e-12d9-42fa-8e39-9a1c2c863a40</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6d140118-ace3-49c1-847f-1d0bbe490aad</toProcessItemId>
            <guid>3f71feb5-9ae2-4b14-9d9a-d81c6e47866c</guid>
            <versionId>fb0627bd-9751-4ad1-aba1-512bc19376ec</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.02f3765e-12d9-42fa-8e39-9a1c2c863a40</fromProcessItemId>
            <toProcessItemId>2025.6d140118-ace3-49c1-847f-1d0bbe490aad</toProcessItemId>
        </link>
    </process>
</teamworks>

