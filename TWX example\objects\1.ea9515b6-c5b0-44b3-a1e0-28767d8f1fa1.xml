<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1" name="get party type">
        <lastModified>1688662626902</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8d1c9a01-4a45-4d07-9e0b-97f57c12024f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>1386b57a-3c0b-48a3-83f7-584ee90c617c</guid>
        <versionId>011905f4-bb8b-46d2-bfb6-2eef2cdbeefa</versionId>
        <dependencySummary>&lt;dependencySummary id="c59191dd-2aad-49a6-992f-12c886672db2" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cb694d21-fb18-4602-b8c1-6667016e0568</processParameterId>
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fc31ca07-0e9a-49c3-a7af-3b3343525a91</guid>
            <versionId>2992b85b-631b-44ba-8ea0-5f9670f2ad9b</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.996325d5-e1eb-432a-8682-9f0a1fec48ca</processParameterId>
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fa61e008-30c9-49d5-8a30-e755152172a3</guid>
            <versionId>c350da4e-9680-4448-9bca-4e5878d2f40e</versionId>
        </processParameter>
        <processVariable name="partyList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.435decf3-e379-42b4-ba06-598c5df14c50</processVariableId>
            <description isNull="true" />
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.f2a214f9-b538-4717-8202-9941bce6aa94</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.Party();
autoObject[0] = new tw.object.toolkit.NBEC.Party();
autoObject[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].partyType.name = "Accountee";
autoObject[0].partyType.value = "Accountee";
autoObject[0].CIF = "";
autoObject[0].name = "";
autoObject[0].country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].country.name = "";
autoObject[0].country.value = "";
autoObject[0].reference = "";
autoObject[0].address1 = "";
autoObject[0].address2 = "";
autoObject[0].address3 = "";
autoObject[0].address4 = "";
autoObject[0].media = "";
autoObject[0].address = "";
autoObject[0].phone = "";
autoObject[0].fax = "";
autoObject[0].email = "";
autoObject[0].contactPersonName = "";
autoObject[0].mobile = "";
autoObject[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].branch.name = "";
autoObject[0].branch.value = "";
autoObject</defaultValue>
            <guid>9d42217d-62de-44a0-b594-da3f5266a326</guid>
            <versionId>75b28b5c-0a4c-45d3-bc69-a8fddd3a0590</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8d1c9a01-4a45-4d07-9e0b-97f57c12024f</processItemId>
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6efea23f-cbf9-41b3-af2c-faf1f96e938c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5af8</guid>
            <versionId>b38ce8b4-7a6c-47fd-b0b6-75ebe95a51cf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="234" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6efea23f-cbf9-41b3-af2c-faf1f96e938c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
////tw.local.partyList = new tw.object.listOf.Party();&#xD;
////tw.local.partyList = tw.local.data;&#xD;
//&#xD;
//&#xD;
for (var i=0; i &lt; tw.local.partyList.listLength ; i++) {&#xD;
//	alert(i);&#xD;
	if (tw.local.partyList[i].partyType.name != "Accountee") {&#xD;
		tw.local.results[0] = new tw.object.NameValuePair();&#xD;
		tw.local.results[0].name = "Drawee";&#xD;
		tw.local.results[0].value = "Drawee";&#xD;
		&#xD;
	}else {&#xD;
		tw.local.results[0] = new tw.object.NameValuePair();&#xD;
		tw.local.results[0].name = "Accountee";&#xD;
		tw.local.results[0].value = "Accountee";&#xD;
		&#xD;
		tw.local.results[1] = new tw.object.NameValuePair();&#xD;
		tw.local.results[1].name = "Drawee";&#xD;
		tw.local.results[1].value = "Drawee";&#xD;
		&#xD;
	}&#xD;
}&#xD;
tw.local.results[0] = new tw.object.NameValuePair();&#xD;
tw.local.results[0].name = "Accountee";&#xD;
tw.local.results[0].value = "Accountee";&#xD;
&#xD;
tw.local.results[1] = new tw.object.NameValuePair();&#xD;
tw.local.results[1].name = "Drawee";&#xD;
tw.local.results[1].value = "Drawee";&#xD;
</script>
                <isRule>false</isRule>
                <guid>9dcd059e-c4b1-4a1d-8a1b-09106bf86991</guid>
                <versionId>f024e1b1-6776-4b0f-91fa-4c1d04e44163</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6e6eec57-5007-4b15-962e-2333fce99f61</processItemId>
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c562b710-fe07-4105-8712-e9fe67f38f8a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5af9</guid>
            <versionId>fe3c3138-8c89-4fe6-8653-ae4ddf733e3a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c562b710-fe07-4105-8712-e9fe67f38f8a</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>04e57d0d-f8c8-41c1-ad9d-7465aec54dc0</guid>
                <versionId>c413f214-f7d7-452f-b518-87ff40ed49e1</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8d1c9a01-4a45-4d07-9e0b-97f57c12024f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="get party type" id="1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.cb694d21-fb18-4602-b8c1-6667016e0568">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"Accountee"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.996325d5-e1eb-432a-8682-9f0a1fec48ca" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="73426fff-2a09-48e9-abfa-2d02add2fa95">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="827c1443-98e9-4719-9278-e756a0218660" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1e4f2438-65bc-40e9-9ea6-b0b0cf2c50f1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6e6eec57-5007-4b15-962e-2333fce99f61</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8d1c9a01-4a45-4d07-9e0b-97f57c12024f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1e4f2438-65bc-40e9-9ea6-b0b0cf2c50f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.d9c8eca0-f027-45d9-b915-206023bbba89</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6e6eec57-5007-4b15-962e-2333fce99f61">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5af9</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cb9adb8a-09ae-4135-b4bb-a9e20dd28680</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1e4f2438-65bc-40e9-9ea6-b0b0cf2c50f1" targetRef="8d1c9a01-4a45-4d07-9e0b-97f57c12024f" name="To Script Task" id="2027.d9c8eca0-f027-45d9-b915-206023bbba89">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="8d1c9a01-4a45-4d07-9e0b-97f57c12024f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="234" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.d9c8eca0-f027-45d9-b915-206023bbba89</ns16:incoming>
                        
                        
                        <ns16:outgoing>cb9adb8a-09ae-4135-b4bb-a9e20dd28680</ns16:outgoing>
                        
                        
                        <ns16:script>//tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
////tw.local.partyList = new tw.object.listOf.Party();&#xD;
////tw.local.partyList = tw.local.data;&#xD;
//&#xD;
//&#xD;
for (var i=0; i &lt; tw.local.partyList.listLength ; i++) {&#xD;
//	alert(i);&#xD;
	if (tw.local.partyList[i].partyType.name != "Accountee") {&#xD;
		tw.local.results[0] = new tw.object.NameValuePair();&#xD;
		tw.local.results[0].name = "Drawee";&#xD;
		tw.local.results[0].value = "Drawee";&#xD;
		&#xD;
	}else {&#xD;
		tw.local.results[0] = new tw.object.NameValuePair();&#xD;
		tw.local.results[0].name = "Accountee";&#xD;
		tw.local.results[0].value = "Accountee";&#xD;
		&#xD;
		tw.local.results[1] = new tw.object.NameValuePair();&#xD;
		tw.local.results[1].name = "Drawee";&#xD;
		tw.local.results[1].value = "Drawee";&#xD;
		&#xD;
	}&#xD;
}&#xD;
tw.local.results[0] = new tw.object.NameValuePair();&#xD;
tw.local.results[0].name = "Accountee";&#xD;
tw.local.results[0].value = "Accountee";&#xD;
&#xD;
tw.local.results[1] = new tw.object.NameValuePair();&#xD;
tw.local.results[1].name = "Drawee";&#xD;
tw.local.results[1].value = "Drawee";&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8d1c9a01-4a45-4d07-9e0b-97f57c12024f" targetRef="6e6eec57-5007-4b15-962e-2333fce99f61" name="To End" id="cb9adb8a-09ae-4135-b4bb-a9e20dd28680">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f2a214f9-b538-4717-8202-9941bce6aa94" isCollection="true" name="partyList" id="2056.435decf3-e379-42b4-ba06-598c5df14c50">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.Party();
autoObject[0] = new tw.object.toolkit.NBEC.Party();
autoObject[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].partyType.name = "Accountee";
autoObject[0].partyType.value = "Accountee";
autoObject[0].CIF = "";
autoObject[0].name = "";
autoObject[0].country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].country.name = "";
autoObject[0].country.value = "";
autoObject[0].reference = "";
autoObject[0].address1 = "";
autoObject[0].address2 = "";
autoObject[0].address3 = "";
autoObject[0].address4 = "";
autoObject[0].media = "";
autoObject[0].address = "";
autoObject[0].phone = "";
autoObject[0].fax = "";
autoObject[0].email = "";
autoObject[0].contactPersonName = "";
autoObject[0].mobile = "";
autoObject[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].branch.name = "";
autoObject[0].branch.value = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9c60e2cb-58c5-4b29-bb50-a53d8b018da3</processLinkId>
            <processId>1.ea9515b6-c5b0-44b3-a1e0-28767d8f1fa1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8d1c9a01-4a45-4d07-9e0b-97f57c12024f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6e6eec57-5007-4b15-962e-2333fce99f61</toProcessItemId>
            <guid>ca3c0d86-af5b-436e-b37f-ee0784ecee61</guid>
            <versionId>47f2122b-9d79-457d-b2b5-bd15beb9f698</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8d1c9a01-4a45-4d07-9e0b-97f57c12024f</fromProcessItemId>
            <toProcessItemId>2025.6e6eec57-5007-4b15-962e-2333fce99f61</toProcessItemId>
        </link>
    </process>
</teamworks>

