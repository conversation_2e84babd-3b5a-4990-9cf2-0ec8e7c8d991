<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.c042b0b3-**************-a4a851c1b331" name="Add Advance Payment">
        <lastModified>1692727053925</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.c042b0b3-**************-a4a851c1b331</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c8e</guid>
        <versionId>bdbad7cb-8c38-4cd3-9fa4-2f0110d5e0d2</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1e0d33c2:-3b3e" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["82d8a9cc-f76e-4eee-8964-8899121255b8"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"78eba4e5-8897-4dc9-898d-5cf37b634ef4"},{"incoming":["fe811d26-9eb1-4407-84a2-3589592d48c6","569d11ac-d676-451e-85ee-858ad76f0889"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":680,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c90"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"246a0bad-d438-4116-8cd1-da5927de745e"},{"startQuantity":1,"outgoing":["b0fb1138-ae4d-4e22-8a7f-af09b360bb0e"],"incoming":["46f10717-2115-4c33-85ff-5744fb18ca3f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":225,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6a00358b-a5b1-4ae1-831f-0dd90fedbf12","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar i = 0;\r\nvar j = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti= i+1;\r\n}\r\n\/\/---------------------------------------------INSERT---------------------------------\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_ADVANCED_PAYMENT_USED (IDC_REQUEST_ID,AMOUNT_ALLOCATED_FOR_REQUEST, ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY, REFERRAL_REQUEST_NUMBER) VALUES (?,?,?,?) ;\";\r\n\r\nparamInit (\"INTEGER\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"DECIMAL\",tw.local.tmpAdvancePayment.AmountAllocated);\r\nparamInit (\"DECIMAL\",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);\r\nparamInit (\"VARCHAR\",tw.local.requestID);\r\n\/\/----------------------------------------------UPDATE----------------------------------\r\ni=0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\n\/\/-------------------------------------------Select-----------------------------------\r\ni=0;\r\nj++;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[j].sql = \"SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;\";\r\n\r\nparamInit (\"VARCHAR\",tw.local.tmpAdvancePayment.DBID);\r\n\r\n"]}},{"targetRef":"980af382-d798-4229-8aa6-09ea4fdb9a45","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"b0fb1138-ae4d-4e22-8a7f-af09b360bb0e","sourceRef":"6a00358b-a5b1-4ae1-831f-0dd90fedbf12"},{"startQuantity":1,"outgoing":["c1276268-5a3d-47a9-800e-3fb636344858"],"incoming":["b0fb1138-ae4d-4e22-8a7f-af09b360bb0e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":405,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"980af382-d798-4229-8aa6-09ea4fdb9a45","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.queryResult"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"b438218b-52b8-4a6b-8e6a-97d16382827e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map Output","declaredType":"sequenceFlow","id":"c1276268-5a3d-47a9-800e-3fb636344858","sourceRef":"980af382-d798-4229-8aa6-09ea4fdb9a45"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.08684c96-e52f-4b16-85e1-d6d17564c42b"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"queryResult","isCollection":true,"declaredType":"dataObject","id":"2056.d80f1e58-c002-4233-8b48-4fc6646ffcbe"},{"startQuantity":1,"outgoing":["46f10717-2115-4c33-85ff-5744fb18ca3f"],"incoming":["82d8a9cc-f76e-4eee-8964-8899121255b8"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":100,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"867f2ef7-591c-4de4-8bda-77b980ec459b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.Seperated = new tw.object.listOf.String();\r\ntw.local.Seperated = tw.local.data.split(\"-\");\r\ntw.local.requestID = tw.local.Seperated[0];\r\ntw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();\r\ntw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];\r\ntw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];\r\ntw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];\r\n\r\n"]}},{"targetRef":"6a00358b-a5b1-4ae1-831f-0dd90fedbf12","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init SQL Query","declaredType":"sequenceFlow","id":"46f10717-2115-4c33-85ff-5744fb18ca3f","sourceRef":"867f2ef7-591c-4de4-8bda-77b980ec459b"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.7234bb46-7f70-4d95-870f-8db4caeabac5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.249aa7a8-3c4d-4556-8332-f0179314e178"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.14dcb429-74d5-4915-8dfa-147bb4a22f3b"},{"startQuantity":1,"outgoing":["fe811d26-9eb1-4407-84a2-3589592d48c6"],"incoming":["c1276268-5a3d-47a9-800e-3fb636344858"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":531,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b438218b-52b8-4a6b-8e6a-97d16382827e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = tw.local.queryResult[2].rows[0].data[0];"]}},{"targetRef":"246a0bad-d438-4116-8cd1-da5927de745e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"fe811d26-9eb1-4407-84a2-3589592d48c6","sourceRef":"b438218b-52b8-4a6b-8e6a-97d16382827e"},{"parallelMultiple":false,"outgoing":["08f7a6af-b825-406f-8fe0-8efe4391f068"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"cfbf9a65-3b99-45f4-8419-781bb5aaa647"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a9142ca9-4e35-434c-8c22-ebb6aa09ffb0","otherAttributes":{"eventImplId":"1c147714-95f7-4047-874f-cf9d7a41a551"}}],"attachedToRef":"867f2ef7-591c-4de4-8bda-77b980ec459b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":135,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"0cd26a63-045a-4ae5-8365-f5f70e7539f2","outputSet":{}},{"parallelMultiple":false,"outgoing":["ab859c4f-3628-4bbc-80ae-f73adee325d0"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"61498c11-f2e0-4fea-8e9b-587a65845073"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a1043d37-7c53-405d-873f-3194a07ca396","otherAttributes":{"eventImplId":"f9e43a9d-5806-4803-852d-ffc9a75dd574"}}],"attachedToRef":"6a00358b-a5b1-4ae1-831f-0dd90fedbf12","extensionElements":{"nodeVisualInfo":[{"width":24,"x":260,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"37507baa-ef19-40fe-8f06-3dae6685e10d","outputSet":{}},{"parallelMultiple":false,"outgoing":["0867cbac-4baa-473a-8a6a-c353af825c1c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"7f442526-da04-4611-8217-88cee0102ff0"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a839f879-750e-4346-8d65-ab400c298c08","otherAttributes":{"eventImplId":"8d474bf8-045f-430a-8c6a-f5aa2cb062e9"}}],"attachedToRef":"980af382-d798-4229-8aa6-09ea4fdb9a45","extensionElements":{"nodeVisualInfo":[{"width":24,"x":440,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"b6dceb8c-b16b-4a56-81b9-828cad8806ba","outputSet":{}},{"parallelMultiple":false,"outgoing":["0da6b490-57f0-49eb-8036-a3740fe72770"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f0bec061-21e5-495e-8b06-4f74564ad239"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"efb6b070-55e1-49f0-88b1-36259d04d8c0","otherAttributes":{"eventImplId":"2875623b-50ea-41cb-8d18-d2ebb72dfdd5"}}],"attachedToRef":"b438218b-52b8-4a6b-8e6a-97d16382827e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":566,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869","outputSet":{}},{"targetRef":"17de5544-34c3-41fe-8d9f-8ad5d016e595","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"08f7a6af-b825-406f-8fe0-8efe4391f068","sourceRef":"0cd26a63-045a-4ae5-8365-f5f70e7539f2"},{"targetRef":"17de5544-34c3-41fe-8d9f-8ad5d016e595","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ab859c4f-3628-4bbc-80ae-f73adee325d0","sourceRef":"37507baa-ef19-40fe-8f06-3dae6685e10d"},{"targetRef":"17de5544-34c3-41fe-8d9f-8ad5d016e595","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0867cbac-4baa-473a-8a6a-c353af825c1c","sourceRef":"b6dceb8c-b16b-4a56-81b9-828cad8806ba"},{"targetRef":"17de5544-34c3-41fe-8d9f-8ad5d016e595","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"0da6b490-57f0-49eb-8036-a3740fe72770","sourceRef":"fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.32da6292-3838-4f7c-8f3c-ab0bb800981e"},{"startQuantity":1,"outgoing":["569d11ac-d676-451e-85ee-858ad76f0889"],"incoming":["0da6b490-57f0-49eb-8036-a3740fe72770","0867cbac-4baa-473a-8a6a-c353af825c1c","ab859c4f-3628-4bbc-80ae-f73adee325d0","08f7a6af-b825-406f-8fe0-8efe4391f068"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":432,"y":215,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"17de5544-34c3-41fe-8d9f-8ad5d016e595","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.e2503488-ccc2-46f6-8e41-2ea14fd78bfa"},{"targetRef":"246a0bad-d438-4116-8cd1-da5927de745e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"569d11ac-d676-451e-85ee-858ad76f0889","sourceRef":"17de5544-34c3-41fe-8d9f-8ad5d016e595"},{"targetRef":"867f2ef7-591c-4de4-8bda-77b980ec459b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"82d8a9cc-f76e-4eee-8964-8899121255b8","sourceRef":"78eba4e5-8897-4dc9-898d-5cf37b634ef4"}],"laneSet":[{"id":"fed4f186-f7bc-4cef-89e5-8701162904d0","lane":[{"flowNodeRef":["78eba4e5-8897-4dc9-898d-5cf37b634ef4","246a0bad-d438-4116-8cd1-da5927de745e","6a00358b-a5b1-4ae1-831f-0dd90fedbf12","980af382-d798-4229-8aa6-09ea4fdb9a45","867f2ef7-591c-4de4-8bda-77b980ec459b","b438218b-52b8-4a6b-8e6a-97d16382827e","0cd26a63-045a-4ae5-8365-f5f70e7539f2","37507baa-ef19-40fe-8f06-3dae6685e10d","b6dceb8c-b16b-4a56-81b9-828cad8806ba","fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869","17de5544-34c3-41fe-8d9f-8ad5d016e595"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"e1f22efc-b298-488a-8eee-009b14c27371","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Add Advance Payment","declaredType":"process","id":"1.c042b0b3-**************-a4a851c1b331","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.d06cf1d1-8d6c-4ea3-8438-c15212b5d4d7"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.a4c89a84-db22-4cda-8816-34afbe3b8a9a"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"7-0.4-20-56\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.1ff07db0-eb69-4093-8477-57810cb817a7"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1ff07db0-eb69-4093-8477-57810cb817a7</processParameterId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"7-0.4-20-56"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e792c03d-7c60-49fc-b54c-9e2b608c34b9</guid>
            <versionId>792703e5-1836-4dc3-bc40-926bcb9d8075</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d06cf1d1-8d6c-4ea3-8438-c15212b5d4d7</processParameterId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>94ef3c71-66c2-435f-aece-c301dfd9a3ca</guid>
            <versionId>d4c182d5-8b4a-4ff6-ad19-6f5e5010d1a3</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a4c89a84-db22-4cda-8816-34afbe3b8a9a</processParameterId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ce1fcdeb-b06c-4902-9122-3e5f69dae30b</guid>
            <versionId>fb64c6f0-b1bc-4d3b-9625-0b1b08441a54</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.08684c96-e52f-4b16-85e1-d6d17564c42b</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>592e6301-f4ee-4845-bc69-3b6ca323cf89</guid>
            <versionId>f0737f6f-9ba8-4839-9862-f689b7d44219</versionId>
        </processVariable>
        <processVariable name="queryResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d80f1e58-c002-4233-8b48-4fc6646ffcbe</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>530e868a-ab2a-4c9a-b889-5154c0ba817b</guid>
            <versionId>dc1e2fab-bafd-4f6c-bc05-05085fc737cb</versionId>
        </processVariable>
        <processVariable name="tmpAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7234bb46-7f70-4d95-870f-8db4caeabac5</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0281e78e-8dec-4949-84b9-d70feecf1ade</guid>
            <versionId>b713d913-b6f4-49f3-b6b2-701fa28a2452</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.249aa7a8-3c4d-4556-8332-f0179314e178</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0ed342dd-2024-488c-b5f4-069c44b1fd9a</guid>
            <versionId>07db0aaf-58bb-4787-9ed5-14f804805711</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.14dcb429-74d5-4915-8dfa-147bb4a22f3b</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>557f859e-14f8-4eac-aef8-471b7d3ffe7f</guid>
            <versionId>bfcc04a2-ed9a-4512-9507-5a67a5f75808</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.32da6292-3838-4f7c-8f3c-ab0bb800981e</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0604d401-e328-486e-aa2c-71f6cc68bb24</guid>
            <versionId>d57bd2bd-a70c-4841-8073-c168153a5c74</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e2503488-ccc2-46f6-8e41-2ea14fd78bfa</processVariableId>
            <description isNull="true" />
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ee8837e9-2cc6-4da6-827f-eaa83efd22ec</guid>
            <versionId>15baaa56-2ead-4f9f-b11c-d5efb531a4b6</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>Catch Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4ba6b639-2a9c-4204-a701-f1efadff564a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:2218</guid>
            <versionId>3dcd0adf-700d-49a7-aa3b-d6761a20d35f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="432" y="215">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4ba6b639-2a9c-4204-a701-f1efadff564a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>84a59116-146d-4bc1-8d14-0fab959b8947</guid>
                <versionId>49d502fc-60aa-468b-b9a6-37e6af1e1e79</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b438218b-52b8-4a6b-8e6a-97d16382827e</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>Map Output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.83cc3a49-1968-41e8-a718-8b6d921f6be2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189d14e6292:46e1</guid>
            <versionId>8019b20a-f196-4159-994b-752066db6ad9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="531" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:2218</errorHandlerItem>
                <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.83cc3a49-1968-41e8-a718-8b6d921f6be2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = tw.local.queryResult[2].rows[0].data[0];</script>
                <isRule>false</isRule>
                <guid>20662929-164b-4c1b-b344-c8af73b2cbec</guid>
                <versionId>8ac32f80-bb60-410d-924d-9d1538ac8969</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d03e5fd2-ecbd-46e4-af5d-96de91924808</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c95</guid>
            <versionId>848a9c51-bc1f-41c6-b3e5-2c0d393eb530</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.70c5efd7-646e-48ed-9cd2-704ba3b3af46</processItemPrePostId>
                <processItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>0a46634a-bb05-463e-a8be-be219006c9ec</guid>
                <versionId>36e7545b-0814-4803-bf01-8f2f4c40ae78</versionId>
            </processPrePosts>
            <layoutData x="405" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:2218</errorHandlerItem>
                <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d03e5fd2-ecbd-46e4-af5d-96de91924808</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>543d37a7-da42-4d32-bd0e-745fb1ce47fc</guid>
                <versionId>24c45bf4-c8f1-44c2-9858-3aad2efbbf02</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d2a75e78-b730-4cf0-947c-70e349bb7c8a</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.d03e5fd2-ecbd-46e4-af5d-96de91924808</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d988a94d-2e80-492a-9dc5-891286ba308a</guid>
                    <versionId>47d09939-16bb-4955-b994-8a5c4f4b6976</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3a95bdef-d45f-42cd-a61a-3bd21e5bc434</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.d03e5fd2-ecbd-46e4-af5d-96de91924808</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryResult</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>40d326e5-a81b-4192-bd39-9812b90eb1ce</guid>
                    <versionId>c71d6f25-689b-4982-8c50-f516faf2b78e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2ce1142a-d0d0-4f03-9bab-15f0b074da02</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.d03e5fd2-ecbd-46e4-af5d-96de91924808</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>d732cde9-c33a-4e7e-bec1-ca9959279928</guid>
                    <versionId>faac176f-3700-4c32-834a-af51cda2ccd8</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6a00358b-a5b1-4ae1-831f-0dd90fedbf12</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>Init SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.165125f1-7828-44e3-a4b5-2153d54797c9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c94</guid>
            <versionId>b3df579c-3c70-4bbd-b45a-5e3bae35b31e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="225" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:2218</errorHandlerItem>
                <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.165125f1-7828-44e3-a4b5-2153d54797c9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------INSERT---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_ADVANCED_PAYMENT_USED (IDC_REQUEST_ID,AMOUNT_ALLOCATED_FOR_REQUEST, ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY, REFERRAL_REQUEST_NUMBER) VALUES (?,?,?,?) ;";&#xD;
&#xD;
paramInit ("INTEGER",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AmountAllocated);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);&#xD;
paramInit ("VARCHAR",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
//-------------------------------------------Select-----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>349f1a21-1366-43fa-9da2-1beb726b6ac5</guid>
                <versionId>5e583eb4-7f0f-4b12-81dc-2d4d79de751a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.09b5c490-4f4a-4426-9aae-be80ebf0bfaa</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7d89</guid>
            <versionId>b93af1f2-6c2b-481d-ad52-e67512c1fdce</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.d7dee008-96d4-4b36-b184-17587732f5e9</processItemPrePostId>
                <processItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>cf6203a3-cd30-439b-81b0-b341e59aead2</guid>
                <versionId>01329cfe-5d65-4adf-9db2-99b7bdbfa3f5</versionId>
            </processPrePosts>
            <layoutData x="100" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:2218</errorHandlerItem>
                <errorHandlerItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.09b5c490-4f4a-4426-9aae-be80ebf0bfaa</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];&#xD;
tw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>1e8d6a4e-dc9d-4495-aaa2-aa4fb47ddefc</guid>
                <versionId>ff469051-bc4f-4663-aa60-850be6fdd1d8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.246a0bad-d438-4116-8cd1-da5927de745e</processItemId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ee62d6fe-9bdb-4eaa-923b-f416c6e627cc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c90</guid>
            <versionId>ca3cf09b-5359-402b-b660-a5846f836b6e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="680" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ee62d6fe-9bdb-4eaa-923b-f416c6e627cc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2e4ba985-55ce-4a66-935c-437a241841b8</guid>
                <versionId>fbcadcc6-bb90-435c-a8f6-7365042b0733</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Add Advance Payment" id="1.c042b0b3-**************-a4a851c1b331" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.1ff07db0-eb69-4093-8477-57810cb817a7">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"7-0.4-20-56"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d06cf1d1-8d6c-4ea3-8438-c15212b5d4d7" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.a4c89a84-db22-4cda-8816-34afbe3b8a9a" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="fed4f186-f7bc-4cef-89e5-8701162904d0">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="e1f22efc-b298-488a-8eee-009b14c27371" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>78eba4e5-8897-4dc9-898d-5cf37b634ef4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>246a0bad-d438-4116-8cd1-da5927de745e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6a00358b-a5b1-4ae1-831f-0dd90fedbf12</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>980af382-d798-4229-8aa6-09ea4fdb9a45</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>867f2ef7-591c-4de4-8bda-77b980ec459b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b438218b-52b8-4a6b-8e6a-97d16382827e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0cd26a63-045a-4ae5-8365-f5f70e7539f2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>37507baa-ef19-40fe-8f06-3dae6685e10d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b6dceb8c-b16b-4a56-81b9-828cad8806ba</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>17de5544-34c3-41fe-8d9f-8ad5d016e595</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="78eba4e5-8897-4dc9-898d-5cf37b634ef4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>82d8a9cc-f76e-4eee-8964-8899121255b8</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="246a0bad-d438-4116-8cd1-da5927de745e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="680" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:e5873b94e72cc4db:-66d3a9fb:189cbc88ec2:7c90</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fe811d26-9eb1-4407-84a2-3589592d48c6</ns16:incoming>
                        
                        
                        <ns16:incoming>569d11ac-d676-451e-85ee-858ad76f0889</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL Query" id="6a00358b-a5b1-4ae1-831f-0dd90fedbf12">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="225" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>46f10717-2115-4c33-85ff-5744fb18ca3f</ns16:incoming>
                        
                        
                        <ns16:outgoing>b0fb1138-ae4d-4e22-8a7f-af09b360bb0e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var i = 0;&#xD;
var j = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i= i+1;&#xD;
}&#xD;
//---------------------------------------------INSERT---------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_ADVANCED_PAYMENT_USED (IDC_REQUEST_ID,AMOUNT_ALLOCATED_FOR_REQUEST, ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY, REFERRAL_REQUEST_NUMBER) VALUES (?,?,?,?) ;";&#xD;
&#xD;
paramInit ("INTEGER",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AmountAllocated);&#xD;
paramInit ("DECIMAL",tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency);&#xD;
paramInit ("VARCHAR",tw.local.requestID);&#xD;
//----------------------------------------------UPDATE----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "update BPM.IDC_REQUEST_DETAILS set  ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ( (select min( AMOUNT_PAYABLE_BY_NBE) from BPM.IDC_REQUEST_DETAILS where ID= ?) - COALESCE((select sum(AMOUNT_ALLOCATED_FOR_REQUEST) from BPM.IDC_ADVANCED_PAYMENT_USED where IDC_REQUEST_ID=? ),0)) where ID=?";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
//-------------------------------------------Select-----------------------------------&#xD;
i=0;&#xD;
j++;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[j].sql = "SELECT ADVANCE_PAYMENT_OUTSTANDING_AMOUNT FROM bpm.IDC_REQUEST_DETAILS WHERE ID=?;";&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.tmpAdvancePayment.DBID);&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6a00358b-a5b1-4ae1-831f-0dd90fedbf12" targetRef="980af382-d798-4229-8aa6-09ea4fdb9a45" name="To SQL Execute Multiple Statements (SQLResult)" id="b0fb1138-ae4d-4e22-8a7f-af09b360bb0e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="SQL Execute Multiple Statements (SQLResult)" id="980af382-d798-4229-8aa6-09ea4fdb9a45">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="405" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b0fb1138-ae4d-4e22-8a7f-af09b360bb0e</ns16:incoming>
                        
                        
                        <ns16:outgoing>c1276268-5a3d-47a9-800e-3fb636344858</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.queryResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="980af382-d798-4229-8aa6-09ea4fdb9a45" targetRef="b438218b-52b8-4a6b-8e6a-97d16382827e" name="To Map Output" id="c1276268-5a3d-47a9-800e-3fb636344858">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.08684c96-e52f-4b16-85e1-d6d17564c42b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="queryResult" id="2056.d80f1e58-c002-4233-8b48-4fc6646ffcbe" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Split Input" id="867f2ef7-591c-4de4-8bda-77b980ec459b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="100" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>82d8a9cc-f76e-4eee-8964-8899121255b8</ns16:incoming>
                        
                        
                        <ns16:outgoing>46f10717-2115-4c33-85ff-5744fb18ca3f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.Seperated = new tw.object.listOf.String();&#xD;
tw.local.Seperated = tw.local.data.split("-");&#xD;
tw.local.requestID = tw.local.Seperated[0];&#xD;
tw.local.tmpAdvancePayment = new tw.object.UsedAdvancePayment();&#xD;
tw.local.tmpAdvancePayment.AllocatedAmountinRequestCurrency = tw.local.Seperated[1];&#xD;
tw.local.tmpAdvancePayment.AmountAllocated = tw.local.Seperated[2];&#xD;
tw.local.tmpAdvancePayment.DBID = tw.local.Seperated[3];&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="867f2ef7-591c-4de4-8bda-77b980ec459b" targetRef="6a00358b-a5b1-4ae1-831f-0dd90fedbf12" name="To Init SQL Query" id="46f10717-2115-4c33-85ff-5744fb18ca3f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpAdvancePayment" id="2056.7234bb46-7f70-4d95-870f-8db4caeabac5" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestID" id="2056.249aa7a8-3c4d-4556-8332-f0179314e178" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.14dcb429-74d5-4915-8dfa-147bb4a22f3b" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Output" id="b438218b-52b8-4a6b-8e6a-97d16382827e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="531" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c1276268-5a3d-47a9-800e-3fb636344858</ns16:incoming>
                        
                        
                        <ns16:outgoing>fe811d26-9eb1-4407-84a2-3589592d48c6</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = tw.local.queryResult[2].rows[0].data[0];</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b438218b-52b8-4a6b-8e6a-97d16382827e" targetRef="246a0bad-d438-4116-8cd1-da5927de745e" name="To End" id="fe811d26-9eb1-4407-84a2-3589592d48c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="867f2ef7-591c-4de4-8bda-77b980ec459b" parallelMultiple="false" name="Error" id="0cd26a63-045a-4ae5-8365-f5f70e7539f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="135" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>08f7a6af-b825-406f-8fe0-8efe4391f068</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="cfbf9a65-3b99-45f4-8419-781bb5aaa647" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a9142ca9-4e35-434c-8c22-ebb6aa09ffb0" eventImplId="1c147714-95f7-4047-874f-cf9d7a41a551">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6a00358b-a5b1-4ae1-831f-0dd90fedbf12" parallelMultiple="false" name="Error1" id="37507baa-ef19-40fe-8f06-3dae6685e10d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="260" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ab859c4f-3628-4bbc-80ae-f73adee325d0</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="61498c11-f2e0-4fea-8e9b-587a65845073" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a1043d37-7c53-405d-873f-3194a07ca396" eventImplId="f9e43a9d-5806-4803-852d-ffc9a75dd574">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="980af382-d798-4229-8aa6-09ea4fdb9a45" parallelMultiple="false" name="Error2" id="b6dceb8c-b16b-4a56-81b9-828cad8806ba">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="440" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0867cbac-4baa-473a-8a6a-c353af825c1c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="7f442526-da04-4611-8217-88cee0102ff0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a839f879-750e-4346-8d65-ab400c298c08" eventImplId="8d474bf8-045f-430a-8c6a-f5aa2cb062e9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b438218b-52b8-4a6b-8e6a-97d16382827e" parallelMultiple="false" name="Error3" id="fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="566" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0da6b490-57f0-49eb-8036-a3740fe72770</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f0bec061-21e5-495e-8b06-4f74564ad239" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="efb6b070-55e1-49f0-88b1-36259d04d8c0" eventImplId="2875623b-50ea-41cb-8d18-d2ebb72dfdd5">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="0cd26a63-045a-4ae5-8365-f5f70e7539f2" targetRef="17de5544-34c3-41fe-8d9f-8ad5d016e595" name="To End Event" id="08f7a6af-b825-406f-8fe0-8efe4391f068">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="37507baa-ef19-40fe-8f06-3dae6685e10d" targetRef="17de5544-34c3-41fe-8d9f-8ad5d016e595" name="To End Event" id="ab859c4f-3628-4bbc-80ae-f73adee325d0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b6dceb8c-b16b-4a56-81b9-828cad8806ba" targetRef="17de5544-34c3-41fe-8d9f-8ad5d016e595" name="To End Event" id="0867cbac-4baa-473a-8a6a-c353af825c1c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="fc2ac28c-f4b2-4b7f-8e4f-d56d7e549869" targetRef="17de5544-34c3-41fe-8d9f-8ad5d016e595" name="To End Event" id="0da6b490-57f0-49eb-8036-a3740fe72770">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.32da6292-3838-4f7c-8f3c-ab0bb800981e" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Error" id="17de5544-34c3-41fe-8d9f-8ad5d016e595">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="432" y="215" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0da6b490-57f0-49eb-8036-a3740fe72770</ns16:incoming>
                        
                        
                        <ns16:incoming>0867cbac-4baa-473a-8a6a-c353af825c1c</ns16:incoming>
                        
                        
                        <ns16:incoming>ab859c4f-3628-4bbc-80ae-f73adee325d0</ns16:incoming>
                        
                        
                        <ns16:incoming>08f7a6af-b825-406f-8fe0-8efe4391f068</ns16:incoming>
                        
                        
                        <ns16:outgoing>569d11ac-d676-451e-85ee-858ad76f0889</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.e2503488-ccc2-46f6-8e41-2ea14fd78bfa" />
                    
                    
                    <ns16:sequenceFlow sourceRef="17de5544-34c3-41fe-8d9f-8ad5d016e595" targetRef="246a0bad-d438-4116-8cd1-da5927de745e" name="To End" id="569d11ac-d676-451e-85ee-858ad76f0889">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="78eba4e5-8897-4dc9-898d-5cf37b634ef4" targetRef="867f2ef7-591c-4de4-8bda-77b980ec459b" name="To SQL Execute Multiple Statements (SQLResult)" id="82d8a9cc-f76e-4eee-8964-8899121255b8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fe811d26-9eb1-4407-84a2-3589592d48c6</processLinkId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b438218b-52b8-4a6b-8e6a-97d16382827e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.246a0bad-d438-4116-8cd1-da5927de745e</toProcessItemId>
            <guid>59e3ed14-9a85-4c01-8456-e3490c0dd2f6</guid>
            <versionId>07e0a9d8-1fe9-40c5-bd72-8f79f048eb31</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b438218b-52b8-4a6b-8e6a-97d16382827e</fromProcessItemId>
            <toProcessItemId>2025.246a0bad-d438-4116-8cd1-da5927de745e</toProcessItemId>
        </link>
        <link name="To SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b0fb1138-ae4d-4e22-8a7f-af09b360bb0e</processLinkId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6a00358b-a5b1-4ae1-831f-0dd90fedbf12</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</toProcessItemId>
            <guid>986426e7-b71d-4366-bfa1-7f6b8cf89637</guid>
            <versionId>1374045c-78f7-49d7-a0d0-b740fb1100b3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6a00358b-a5b1-4ae1-831f-0dd90fedbf12</fromProcessItemId>
            <toProcessItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</toProcessItemId>
        </link>
        <link name="To Map Output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c1276268-5a3d-47a9-800e-3fb636344858</processLinkId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.b438218b-52b8-4a6b-8e6a-97d16382827e</toProcessItemId>
            <guid>53ca007f-3877-4d77-84bf-92623f9c8298</guid>
            <versionId>260e6532-1fa8-46ab-872c-d1c9e0552a84</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.980af382-d798-4229-8aa6-09ea4fdb9a45</fromProcessItemId>
            <toProcessItemId>2025.b438218b-52b8-4a6b-8e6a-97d16382827e</toProcessItemId>
        </link>
        <link name="To Init SQL Query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.46f10717-2115-4c33-85ff-5744fb18ca3f</processLinkId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6a00358b-a5b1-4ae1-831f-0dd90fedbf12</toProcessItemId>
            <guid>f8c8dd35-acb6-438b-a75e-1c33fce77cd0</guid>
            <versionId>283462fa-c0c3-40dc-97a2-25a669410584</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.867f2ef7-591c-4de4-8bda-77b980ec459b</fromProcessItemId>
            <toProcessItemId>2025.6a00358b-a5b1-4ae1-831f-0dd90fedbf12</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.569d11ac-d676-451e-85ee-858ad76f0889</processLinkId>
            <processId>1.c042b0b3-**************-a4a851c1b331</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.246a0bad-d438-4116-8cd1-da5927de745e</toProcessItemId>
            <guid>e09c4712-1f0d-4c77-b65d-0ffed21230bb</guid>
            <versionId>a5052705-e311-435d-bb28-246f7b341295</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.17de5544-34c3-41fe-8d9f-8ad5d016e595</fromProcessItemId>
            <toProcessItemId>2025.246a0bad-d438-4116-8cd1-da5927de745e</toProcessItemId>
        </link>
    </process>
</teamworks>

