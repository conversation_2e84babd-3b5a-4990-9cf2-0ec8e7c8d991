<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e3028b61-b01a-4a56-a086-244083a8d445" name="Get Applicant and Accountee Facility Codes">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>f9daaa58-ac41-4f76-b889-1cefbe11efc6</guid>
        <versionId>a9cd3b56-b156-47f9-a16d-22b9ac5b4562</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e05" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.dbf8fae3-a2d9-4697-b702-86812f5581ce"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"604a0f10-7d52-41da-894a-115f0c2bce02"},{"incoming":["cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b","6f40bf0d-360e-4d93-8c19-761a792b3e3e","3d282ae9-beca-4972-8c71-b0dfcb4ab133"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":690,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a49"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"9887c30c-5747-4563-a3ca-f80933efd198"},{"targetRef":"d468dd30-9324-4ae4-80cd-ca21249fc015","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Facility Codes","declaredType":"sequenceFlow","id":"2027.dbf8fae3-a2d9-4697-b702-86812f5581ce","sourceRef":"604a0f10-7d52-41da-894a-115f0c2bce02"},{"startQuantity":1,"outgoing":["299f1ad2-34c5-4ca8-8c06-1de2d102a013"],"incoming":["7067a45a-b182-43d4-bcec-b82c270bdef0","af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0"],"extensionElements":{"postAssignmentScript":["tw.local.index++;\r\n\r\n\r\nif(tw.local.isSuccessful &amp;&amp; tw.local.facilities.length&gt;0){\r\n\tfor(var i =0 ; i&lt;tw.local.facilities.listLength; i++){\r\n\t\tvar code = new tw.object.NameValuePair();\r\n\t\tcode.name = tw.local.facilities[i].facilityCode;\r\n\t\tcode.value = tw.local.facilities[i].facilityCode;\r\n\t\ttw.local.facilityCodes.insertIntoList(tw.local.facilityCodes.listLength, code)\r\n\t\ttw.local.applicantAccounteeFacilities.insertIntoList(tw.local.applicantAccounteeFacilities.listLength, tw.local.facilities[i])\r\n\r\n\t}\r\n}\r\n"],"nodeVisualInfo":[{"width":95,"x":309,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Facility Codes","dataInputAssociation":[{"targetRef":"2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.cifs[tw.local.index]"]}}]},{"targetRef":"2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.partyType[tw.local.index]"]}}]},{"targetRef":"2055.38bc233e-db52-4fae-a389-f979f7efca57","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.productCode"]}}]},{"targetRef":"2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.1b074b68-bcee-4c98-8d30-4c268a160c7d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.8524f680-2f8c-44ac-8819-19d77f9e07a2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f02f4452-3134-40b0-a3fb-e50ebe47a002","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.72812cef-1b03-4818-8d0e-f55b3e396b46"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.status"]}}],"sourceRef":["2055.399f4efd-9154-4595-a15c-435ca48599ce"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","declaredType":"TFormalExpression","content":["tw.local.facilities"]}}],"sourceRef":["2055.40d442e7-0366-4e9d-83ec-deb29b0fc118"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab"]}],"calledElement":"1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"index","isCollection":false,"declaredType":"dataObject","id":"2056.5c44f432-e63d-4208-8555-a9eecaa1098c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.6c74b774-8f98-4eaa-82ee-9f1cf1d7f18b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.d1697e28-8100-42a0-915a-d7f7fcbf14e3"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.1356d40b-7198-47b5-9276-84056c0587c0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"declaredType":"dataObject","id":"2056.6cacd5cc-4807-4a0d-8e79-3d8dbef29206"},{"outgoing":["cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b","7067a45a-b182-43d4-bcec-b82c270bdef0"],"incoming":["59d8167e-eec3-4630-8fe3-9c51cf774f7c"],"default":"cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":552,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"get code?","declaredType":"exclusiveGateway","id":"1fd8df56-6c68-4772-bafe-889a6bd683f4"},{"targetRef":"9887c30c-5747-4563-a3ca-f80933efd198","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b","sourceRef":"1fd8df56-6c68-4772-bafe-889a6bd683f4"},{"targetRef":"f02f4452-3134-40b0-a3fb-e50ebe47a002","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.index\t  &lt;\t  tw.local.cifs.listLength"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"7067a45a-b182-43d4-bcec-b82c270bdef0","sourceRef":"1fd8df56-6c68-4772-bafe-889a6bd683f4"},{"outgoing":["af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0","6f40bf0d-360e-4d93-8c19-761a792b3e3e"],"incoming":["2027.dbf8fae3-a2d9-4697-b702-86812f5581ce"],"default":"af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":["tw.local.facilities = [];\r\ntw.local.facilityCodes = [];\r\ntw.local.applicantAccounteeFacilities = [];"],"nodeVisualInfo":[{"width":32,"x":188,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"cif exists?","declaredType":"exclusiveGateway","id":"d468dd30-9324-4ae4-80cd-ca21249fc015"},{"targetRef":"f02f4452-3134-40b0-a3fb-e50ebe47a002","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Get Facility Codes","declaredType":"sequenceFlow","id":"af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0","sourceRef":"d468dd30-9324-4ae4-80cd-ca21249fc015"},{"targetRef":"9887c30c-5747-4563-a3ca-f80933efd198","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.cifs.listLength\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":464,"y":310}]}]},"name":"To End","declaredType":"sequenceFlow","id":"6f40bf0d-360e-4d93-8c19-761a792b3e3e","sourceRef":"d468dd30-9324-4ae4-80cd-ca21249fc015"},{"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"facilities","isCollection":true,"declaredType":"dataObject","id":"2056.828f3396-7ec1-4fee-be9d-6302137d6aac"},{"targetRef":"f9c9bdea-1ae9-4a88-823f-3762f30fad77","extensionElements":{"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-599f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To get code?","declaredType":"sequenceFlow","id":"299f1ad2-34c5-4ca8-8c06-1de2d102a013","sourceRef":"f02f4452-3134-40b0-a3fb-e50ebe47a002"},{"outgoing":["59d8167e-eec3-4630-8fe3-9c51cf774f7c","8bd6b585-b8b0-4a93-8328-ba803abca7e2"],"incoming":["299f1ad2-34c5-4ca8-8c06-1de2d102a013"],"default":"59d8167e-eec3-4630-8fe3-9c51cf774f7c","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":444,"y":77,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"f9c9bdea-1ae9-4a88-823f-3762f30fad77"},{"targetRef":"1fd8df56-6c68-4772-bafe-889a6bd683f4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To get code?","declaredType":"sequenceFlow","id":"59d8167e-eec3-4630-8fe3-9c51cf774f7c","sourceRef":"f9c9bdea-1ae9-4a88-823f-3762f30fad77"},{"startQuantity":1,"outgoing":["3d282ae9-beca-4972-8c71-b0dfcb4ab133"],"incoming":["8bd6b585-b8b0-4a93-8328-ba803abca7e2"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":434,"y":171,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8e1fc3e0-c12f-4881-8a3e-998a1326b8b0","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"8e1fc3e0-c12f-4881-8a3e-998a1326b8b0","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"8bd6b585-b8b0-4a93-8328-ba803abca7e2","sourceRef":"f9c9bdea-1ae9-4a88-823f-3762f30fad77"},{"targetRef":"9887c30c-5747-4563-a3ca-f80933efd198","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"3d282ae9-beca-4972-8c71-b0dfcb4ab133","sourceRef":"8e1fc3e0-c12f-4881-8a3e-998a1326b8b0"}],"laneSet":[{"id":"f73f3c5b-f109-4e9d-ba54-b75ba9f8a394","lane":[{"flowNodeRef":["604a0f10-7d52-41da-894a-115f0c2bce02","9887c30c-5747-4563-a3ca-f80933efd198","f02f4452-3134-40b0-a3fb-e50ebe47a002","1fd8df56-6c68-4772-bafe-889a6bd683f4","d468dd30-9324-4ae4-80cd-ca21249fc015","f9c9bdea-1ae9-4a88-823f-3762f30fad77","8e1fc3e0-c12f-4881-8a3e-998a1326b8b0"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"0348e94a-1c9f-4684-a936-e536132e6cd5","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Applicant and Accountee Facility Codes","declaredType":"process","id":"1.e3028b61-b01a-4a56-a086-244083a8d445","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"facilityCodes","isCollection":true,"id":"2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe"},{"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"applicantAccounteeFacilities","isCollection":true,"id":"2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.a114913d-d916-4842-823e-b034e78846b9"}],"inputSet":[{"dataInputRefs":["2055.805a4cbb-3faf-424f-8d2e-0300020c7082","2055.506476be-4213-48fc-9956-612812bf0ca8","2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2"]}],"outputSet":[{"dataOutputRefs":["2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe","2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c","2055.a114913d-d916-4842-823e-b034e78846b9"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject[0] = \"********\";\r\n\/\/autoObject[0] = \"********\";\r\nautoObject[1] = \"********\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cifs","isCollection":true,"id":"2055.805a4cbb-3faf-424f-8d2e-0300020c7082"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject[0].name = \"Applicant\";\nautoObject[0].value = \"APP\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"partyType","isCollection":true,"id":"2055.506476be-4213-48fc-9956-612812bf0ca8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"ILSN\"\r\n\/\/'IUDC'\r\n\/\/'ICAP'\r\n\"IAVC\"\r\n\/\/'IMBC'\r\n\/\/'ISPC'\r\n\/\/'ADFR'\r\n\/\/'ADAM'\r\n\/\/'TRNS'\r\n\/\/'ASAT'"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"id":"2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="cifs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.805a4cbb-3faf-424f-8d2e-0300020c7082</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "********";&#xD;
//autoObject[0] = "********";&#xD;
autoObject[1] = "********";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0f2f3a0d-ebb4-45ad-bb0a-8cdf99145213</guid>
            <versionId>e96edea6-45da-4eb5-9b12-96e44f98bad6</versionId>
        </processParameter>
        <processParameter name="partyType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.506476be-4213-48fc-9956-612812bf0ca8</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "Applicant";
autoObject[0].value = "APP";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f58709a0-f3cd-49f9-8f03-fea3b7167719</guid>
            <versionId>7e24a9b9-19d1-4290-875c-d8226573ceeb</versionId>
        </processParameter>
        <processParameter name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"ILSN"&#xD;
//'IUDC'&#xD;
//'ICAP'&#xD;
"IAVC"&#xD;
//'IMBC'&#xD;
//'ISPC'&#xD;
//'ADFR'&#xD;
//'ADAM'&#xD;
//'TRNS'&#xD;
//'ASAT'</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>62aa6302-bbcf-4fc6-a58e-49169f17d580</guid>
            <versionId>624120c6-a268-457c-81f6-c66c646d71f2</versionId>
        </processParameter>
        <processParameter name="facilityCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6a026cb8-700f-4c9d-8fad-2c5eca56c7aa</guid>
            <versionId>7fa60e4e-b2b7-4cb3-be33-40a501ab35f1</versionId>
        </processParameter>
        <processParameter name="applicantAccounteeFacilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fd197f9f-05d4-4cf2-a2f1-cb142d85c245</guid>
            <versionId>64a83464-facd-41f7-b2db-e1f8bce60c7a</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a114913d-d916-4842-823e-b034e78846b9</processParameterId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5356a153-dbab-4b4b-8775-93b4d1c058cf</guid>
            <versionId>9cede183-bf44-4c21-b301-8bed62866921</versionId>
        </processParameter>
        <processVariable name="index">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5c44f432-e63d-4208-8555-a9eecaa1098c</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>0</defaultValue>
            <guid>17cc8ddf-6452-4142-bc27-52f00c0e34e4</guid>
            <versionId>3cf2d09d-d7d7-48dc-a1e2-85cd789c0bf5</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6c74b774-8f98-4eaa-82ee-9f1cf1d7f18b</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>642d3c2f-c0a3-4cd3-8a57-51b5358d4bf8</guid>
            <versionId>4d9d9b2b-c4f1-410b-bf7d-be5f00186e43</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d1697e28-8100-42a0-915a-d7f7fcbf14e3</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e0771a37-a27f-45d2-96ff-1828e7e9aff9</guid>
            <versionId>a04aed01-92a4-497f-ae9a-6e062523ab54</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1356d40b-7198-47b5-9276-84056c0587c0</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>22e6c5ef-6b70-405e-a627-4287870efe3e</guid>
            <versionId>c134ef93-1a72-4679-bdcf-e347aaa60b5a</versionId>
        </processVariable>
        <processVariable name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6cacd5cc-4807-4a0d-8e79-3d8dbef29206</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ab11e6f4-ba9d-49b0-9bdd-0b5ae57b39a3</guid>
            <versionId>7763c9af-73fe-4b4d-b50b-11cb5b716cb7</versionId>
        </processVariable>
        <processVariable name="facilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.828f3396-7ec1-4fee-be9d-6302137d6aac</processVariableId>
            <description isNull="true" />
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>801c2e19-7be5-4f07-926f-40d5fcd35cd4</guid>
            <versionId>505166b0-1f60-4300-9470-02c299781b17</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>cif exists?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.7c45e468-d9f1-4f27-9cf1-5aef8bc65a29</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a46</guid>
            <versionId>11a66c76-e185-4f5b-a7af-dc4821f73365</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.410e641c-ad1a-4a4b-9373-681eda01cf6e</processItemPrePostId>
                <processItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</processItemId>
                <location>2</location>
                <script>tw.local.facilities = [];&#xD;
tw.local.facilityCodes = [];&#xD;
tw.local.applicantAccounteeFacilities = [];</script>
                <guid>82d7371b-0011-4a93-89ba-870d4e9705af</guid>
                <versionId>a72ee272-61d2-42ae-8fed-79fac13bd0e6</versionId>
            </processPrePosts>
            <layoutData x="188" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.7c45e468-d9f1-4f27-9cf1-5aef8bc65a29</switchId>
                <guid>8f3416b6-084d-4221-aade-5e079d0cadb6</guid>
                <versionId>afd8418c-e38a-40ef-8ac8-8e3ea3de406e</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.af693a83-eb40-4389-8f41-6e716ed1a87c</switchConditionId>
                    <switchId>3013.7c45e468-d9f1-4f27-9cf1-5aef8bc65a29</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e03</endStateId>
                    <condition>tw.local.cifs.listLength	  ==	  0</condition>
                    <guid>f2462d9b-3b7b-4b91-adf7-99791da1aca2</guid>
                    <versionId>15252a56-bcc4-4bab-8020-2f8eba34d5a8</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.67730503-41e5-4f09-aaf5-1737447fb466</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3df3</guid>
            <versionId>85e5e53c-7a79-46e2-86ee-4370657a7a92</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="444" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.67730503-41e5-4f09-aaf5-1737447fb466</switchId>
                <guid>0120223c-20c7-4cc7-a9b1-605d76079b3a</guid>
                <versionId>980a9b8e-613c-4a70-a919-39f4db0c6219</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.b35e177d-16fd-48ca-908a-a94db63b5e42</switchConditionId>
                    <switchId>3013.67730503-41e5-4f09-aaf5-1737447fb466</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e04</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>c7427b92-ba0e-453e-a8fd-59b3688dcbc5</guid>
                    <versionId>e62c6107-2e6b-4766-ab3f-b12fea6e21f4</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.777a28c3-8057-4c9a-be9e-261d7e3196db</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3df0</guid>
            <versionId>9ae9a2a3-4aac-4b29-b9e5-1f0dde800846</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="434" y="171">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.777a28c3-8057-4c9a-be9e-261d7e3196db</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>33f4fcc9-6666-4b38-9fad-8b0886c7466e</guid>
                <versionId>6d7173bb-0fa7-4cbe-ad52-56a1376bdb02</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>get code?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.5011242e-f931-4de9-b0a7-64190d7a3585</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a47</guid>
            <versionId>9c6284ad-bd53-4eb2-abe4-0fd906b28d61</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="552" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.5011242e-f931-4de9-b0a7-64190d7a3585</switchId>
                <guid>3931834a-9dd0-4c5f-965a-a29029997748</guid>
                <versionId>cac8ef49-bf02-47d1-b737-006e099fc363</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.b3719946-286c-4eb6-9048-bea41c5953d8</switchConditionId>
                    <switchId>3013.5011242e-f931-4de9-b0a7-64190d7a3585</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e02</endStateId>
                    <condition>tw.local.index	  &lt;	  tw.local.cifs.listLength</condition>
                    <guid>d75dd210-d8a9-4c19-aa68-c53b60eef3ba</guid>
                    <versionId>b011ac2a-adac-4399-98c1-fc0ea043b418</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>Get Facility Codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a48</guid>
            <versionId>a68bfeee-8f2a-42f6-a624-59554a9cf051</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.49238f96-6acb-4af7-a048-3eabc8e549c5</processItemPrePostId>
                <processItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</processItemId>
                <location>2</location>
                <script>tw.local.index++;&#xD;
&#xD;
&#xD;
if(tw.local.isSuccessful &amp;&amp; tw.local.facilities.length&gt;0){&#xD;
	for(var i =0 ; i&lt;tw.local.facilities.listLength; i++){&#xD;
		var code = new tw.object.NameValuePair();&#xD;
		code.name = tw.local.facilities[i].facilityCode;&#xD;
		code.value = tw.local.facilities[i].facilityCode;&#xD;
		tw.local.facilityCodes.insertIntoList(tw.local.facilityCodes.listLength, code)&#xD;
		tw.local.applicantAccounteeFacilities.insertIntoList(tw.local.applicantAccounteeFacilities.listLength, tw.local.facilities[i])&#xD;
&#xD;
	}&#xD;
}&#xD;
</script>
                <guid>bcbd6ef0-3834-435f-9e4b-1d5cb3c29d72</guid>
                <versionId>588a21ed-2ac6-4589-92b7-8817ea316f39</versionId>
            </processPrePosts>
            <layoutData x="309" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</subProcessId>
                <attachedProcessRef>/1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f</attachedProcessRef>
                <guid>b886ede2-3178-4b69-8eac-fc49e6c3e2f2</guid>
                <versionId>0cadf5ca-6726-49af-849d-09b1db37562a</versionId>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1a520d0f-e20c-4538-8706-bd082a658637</parameterMappingId>
                    <processParameterId>2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.cifs[tw.local.index]</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fb1c053e-5964-4ade-96b2-d855a213c431</guid>
                    <versionId>0b49ccdb-fe9e-43fc-ae6f-192e2794353b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6adec1ad-d4a6-400b-aaf1-************</parameterMappingId>
                    <processParameterId>2055.72812cef-1b03-4818-8d0e-f55b3e396b46</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8a2dceb2-ee8d-4172-b6b6-f68a30dd2ee8</guid>
                    <versionId>0d814cbe-6574-4b31-ab82-da565fcb5e7b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d0d63338-1b9f-4cde-9374-2aecbd5a1f80</parameterMappingId>
                    <processParameterId>2055.8524f680-2f8c-44ac-8819-19d77f9e07a2</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>542eace1-66e2-4ffd-b38d-72d76cd31e2d</guid>
                    <versionId>149a39f5-1dfb-4e76-addb-b6894667299a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cfa6486c-68f0-4f4e-97c4-377241ce2cc3</parameterMappingId>
                    <processParameterId>2055.38bc233e-db52-4fae-a389-f979f7efca57</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productCode</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9ef4ae4e-213b-4a36-bbbf-fa5d20b331bc</guid>
                    <versionId>1e48388a-b12f-4416-a6c2-29fd08b86909</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b8292b44-04a4-46d4-99b8-f6f74f2e18fc</parameterMappingId>
                    <processParameterId>2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5c6db8fe-d112-4d79-bc05-03b3597e0083</guid>
                    <versionId>23c2a55e-f5f4-4b79-aa6f-0eb13d80b585</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a7f62af7-9fe1-4081-a831-4f796b24d3ff</parameterMappingId>
                    <processParameterId>2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>9704eb88-4550-48fa-a46e-d2e60116817a</guid>
                    <versionId>2b864e4e-8106-47b8-912c-777c841a7673</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9c9d3eaf-8cbe-4515-8a5e-e550b844ddd9</parameterMappingId>
                    <processParameterId>2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1637d6e4-559f-4a6a-8bf0-b51927fb4b2d</guid>
                    <versionId>441f7f44-9cfa-4f05-9686-53bd0dfc506a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2958d6e2-0594-4937-b74d-8b4623b9ab79</parameterMappingId>
                    <processParameterId>2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>10a57425-f4de-469f-8ead-52dc41bf1760</guid>
                    <versionId>5c72e76b-d465-44f3-a2fb-c0337f9134a5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c7fcf214-e7a2-46ee-8cca-44803877b6f0</parameterMappingId>
                    <processParameterId>2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9e4123f7-3eae-4759-89b1-65e4b772f68c</guid>
                    <versionId>662cdabf-4ac7-4278-8ee9-09a3e44d0d04</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4b4b4249-547f-42dc-b73b-1a873bc372ca</parameterMappingId>
                    <processParameterId>2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5c5259b6-2d67-41a9-a069-95db6458cf9b</guid>
                    <versionId>86be39d1-37a0-4b81-9ff3-2f0b64961f21</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="partyType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c9eb3a39-fb41-4bc2-89fb-3027459db043</parameterMappingId>
                    <processParameterId>2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.partyType[tw.local.index]</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>795d3c73-3231-4c24-9132-0fcbf2431551</guid>
                    <versionId>983f9b81-57d5-4e89-93d9-3c4d2f9654d0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.672464e9-ad80-45f6-a1c4-1b31ba3534f8</parameterMappingId>
                    <processParameterId>2055.1b074b68-bcee-4c98-8d30-4c268a160c7d</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>567875bf-77d8-4270-bd0d-344280dd96da</guid>
                    <versionId>ec465e73-c90b-4e9d-a145-5cf2ee190c57</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerFacilities">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e7d456eb-d01d-4634-ad41-329a35a42eb5</parameterMappingId>
                    <processParameterId>2055.40d442e7-0366-4e9d-83ec-deb29b0fc118</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.facilities</value>
                    <classRef>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>c9f6c685-913b-4f19-9e58-cad1fbfacb50</guid>
                    <versionId>f0a2bb25-923c-4527-9259-a4f031536969</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.af821df5-a1ec-4d62-9c37-fa666f8c613d</parameterMappingId>
                    <processParameterId>2055.399f4efd-9154-4595-a15c-435ca48599ce</processParameterId>
                    <parameterMappingParentId>3012.8eccbe5a-6fc4-4a8c-9f93-be9627a6f444</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0ea6679e-14d9-496a-ae7d-bcf00050c374</guid>
                    <versionId>ff98883e-f196-4f25-9144-77dc84e56e1e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</processItemId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.88ae3ebb-6c09-4202-923b-86988b034d38</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a49</guid>
            <versionId>d4c87516-9139-438a-954f-faffec59d163</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="690" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.88ae3ebb-6c09-4202-923b-86988b034d38</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a868b898-07b9-41a6-994f-bf56aef5e746</guid>
                <versionId>4a6e309c-df7f-4729-a928-ba80727d7595</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Applicant and Accountee Facility Codes" id="1.e3028b61-b01a-4a56-a086-244083a8d445" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="cifs" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" id="2055.805a4cbb-3faf-424f-8d2e-0300020c7082">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "********";&#xD;
//autoObject[0] = "********";&#xD;
autoObject[1] = "********";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="partyType" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.506476be-4213-48fc-9956-612812bf0ca8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].name = "Applicant";
autoObject[0].value = "APP";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="productCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"ILSN"&#xD;
//'IUDC'&#xD;
//'ICAP'&#xD;
"IAVC"&#xD;
//'IMBC'&#xD;
//'ISPC'&#xD;
//'ADFR'&#xD;
//'ADAM'&#xD;
//'TRNS'&#xD;
//'ASAT'</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="facilityCodes" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" id="2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe" />
                        
                        
                        <ns16:dataOutput name="applicantAccounteeFacilities" itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" id="2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.a114913d-d916-4842-823e-b034e78846b9" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.805a4cbb-3faf-424f-8d2e-0300020c7082</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.506476be-4213-48fc-9956-612812bf0ca8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.6a732d7a-8c1d-42ac-817b-74d1af5ed3f2</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.31267b61-cbbb-4069-a8ba-bd0e66828cbe</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.de2ed1a7-b416-4db6-b2d1-e71e9e4d064c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.a114913d-d916-4842-823e-b034e78846b9</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="f73f3c5b-f109-4e9d-ba54-b75ba9f8a394">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="0348e94a-1c9f-4684-a936-e536132e6cd5" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>604a0f10-7d52-41da-894a-115f0c2bce02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9887c30c-5747-4563-a3ca-f80933efd198</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f02f4452-3134-40b0-a3fb-e50ebe47a002</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1fd8df56-6c68-4772-bafe-889a6bd683f4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d468dd30-9324-4ae4-80cd-ca21249fc015</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f9c9bdea-1ae9-4a88-823f-3762f30fad77</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="604a0f10-7d52-41da-894a-115f0c2bce02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.dbf8fae3-a2d9-4697-b702-86812f5581ce</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="9887c30c-5747-4563-a3ca-f80933efd198">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="690" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:e6d52ec61513ed03:232a663a:189f41ec14b:5a49</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b</ns16:incoming>
                        
                        
                        <ns16:incoming>6f40bf0d-360e-4d93-8c19-761a792b3e3e</ns16:incoming>
                        
                        
                        <ns16:incoming>3d282ae9-beca-4972-8c71-b0dfcb4ab133</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="604a0f10-7d52-41da-894a-115f0c2bce02" targetRef="d468dd30-9324-4ae4-80cd-ca21249fc015" name="To Get Facility Codes" id="2027.dbf8fae3-a2d9-4697-b702-86812f5581ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.272ad8b7-8ea1-4c8e-af76-b7534aa5129f" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Facility Codes" id="f02f4452-3134-40b0-a3fb-e50ebe47a002">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="309" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>tw.local.index++;&#xD;
&#xD;
&#xD;
if(tw.local.isSuccessful &amp;&amp; tw.local.facilities.length&gt;0){&#xD;
	for(var i =0 ; i&lt;tw.local.facilities.listLength; i++){&#xD;
		var code = new tw.object.NameValuePair();&#xD;
		code.name = tw.local.facilities[i].facilityCode;&#xD;
		code.value = tw.local.facilities[i].facilityCode;&#xD;
		tw.local.facilityCodes.insertIntoList(tw.local.facilityCodes.listLength, code)&#xD;
		tw.local.applicantAccounteeFacilities.insertIntoList(tw.local.applicantAccounteeFacilities.listLength, tw.local.facilities[i])&#xD;
&#xD;
	}&#xD;
}&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7067a45a-b182-43d4-bcec-b82c270bdef0</ns16:incoming>
                        
                        
                        <ns16:incoming>af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0</ns16:incoming>
                        
                        
                        <ns16:outgoing>299f1ad2-34c5-4ca8-8c06-1de2d102a013</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0db0b9d8-4638-4f46-ab8e-28ebff9a6dd8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.cifs[tw.local.index]</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8ae57b3b-a1c6-4c9a-ace6-5037a8f9f4d2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.partyType[tw.local.index]</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.38bc233e-db52-4fae-a389-f979f7efca57</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f1b3cb0a-6ab8-496e-b5b5-8ea6784b7fe3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5a1d8e1d-0acc-479b-9813-9c93cfb3cd33</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.67b8924c-57fe-4b24-a14d-06bf4a2a8adc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1b074b68-bcee-4c98-8d30-4c268a160c7d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8524f680-2f8c-44ac-8819-19d77f9e07a2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4e26ab5c-0d08-4c08-9271-9fc405fbe3e3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.72812cef-1b03-4818-8d0e-f55b3e396b46</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.399f4efd-9154-4595-a15c-435ca48599ce</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.01cc1f38-1881-4fbf-8f4b-58d82916cfe1</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.40d442e7-0366-4e9d-83ec-deb29b0fc118</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c">tw.local.facilities</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4e2e27c9-24ad-42fd-a9ca-cde8cedcb7ab</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="index" id="2056.5c44f432-e63d-4208-8555-a9eecaa1098c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">0</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.6c74b774-8f98-4eaa-82ee-9f1cf1d7f18b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.d1697e28-8100-42a0-915a-d7f7fcbf14e3" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.1356d40b-7198-47b5-9276-84056c0587c0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="status" id="2056.6cacd5cc-4807-4a0d-8e79-3d8dbef29206" />
                    
                    
                    <ns16:exclusiveGateway default="cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b" name="get code?" id="1fd8df56-6c68-4772-bafe-889a6bd683f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="552" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>59d8167e-eec3-4630-8fe3-9c51cf774f7c</ns16:incoming>
                        
                        
                        <ns16:outgoing>cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b</ns16:outgoing>
                        
                        
                        <ns16:outgoing>7067a45a-b182-43d4-bcec-b82c270bdef0</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="1fd8df56-6c68-4772-bafe-889a6bd683f4" targetRef="9887c30c-5747-4563-a3ca-f80933efd198" name="No" id="cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1fd8df56-6c68-4772-bafe-889a6bd683f4" targetRef="f02f4452-3134-40b0-a3fb-e50ebe47a002" name="Yes" id="7067a45a-b182-43d4-bcec-b82c270bdef0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.index	  &lt;	  tw.local.cifs.listLength</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0" name="cif exists?" id="d468dd30-9324-4ae4-80cd-ca21249fc015">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="188" y="76" width="32" height="32" />
                            
                            
                            <ns3:postAssignmentScript>tw.local.facilities = [];&#xD;
tw.local.facilityCodes = [];&#xD;
tw.local.applicantAccounteeFacilities = [];</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.dbf8fae3-a2d9-4697-b702-86812f5581ce</ns16:incoming>
                        
                        
                        <ns16:outgoing>af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0</ns16:outgoing>
                        
                        
                        <ns16:outgoing>6f40bf0d-360e-4d93-8c19-761a792b3e3e</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="d468dd30-9324-4ae4-80cd-ca21249fc015" targetRef="f02f4452-3134-40b0-a3fb-e50ebe47a002" name="To Get Facility Codes" id="af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d468dd30-9324-4ae4-80cd-ca21249fc015" targetRef="9887c30c-5747-4563-a3ca-f80933efd198" name="To End" id="6f40bf0d-360e-4d93-8c19-761a792b3e3e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="464" y="310" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.cifs.listLength	  ==	  0</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" name="facilities" id="2056.828f3396-7ec1-4fee-be9d-6302137d6aac" />
                    
                    
                    <ns16:sequenceFlow sourceRef="f02f4452-3134-40b0-a3fb-e50ebe47a002" targetRef="f9c9bdea-1ae9-4a88-823f-3762f30fad77" name="To get code?" id="299f1ad2-34c5-4ca8-8c06-1de2d102a013">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-599f</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="59d8167e-eec3-4630-8fe3-9c51cf774f7c" gatewayDirection="Unspecified" name="is Successful" id="f9c9bdea-1ae9-4a88-823f-3762f30fad77">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="444" y="77" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>299f1ad2-34c5-4ca8-8c06-1de2d102a013</ns16:incoming>
                        
                        
                        <ns16:outgoing>59d8167e-eec3-4630-8fe3-9c51cf774f7c</ns16:outgoing>
                        
                        
                        <ns16:outgoing>8bd6b585-b8b0-4a93-8328-ba803abca7e2</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f9c9bdea-1ae9-4a88-823f-3762f30fad77" targetRef="1fd8df56-6c68-4772-bafe-889a6bd683f4" name="To get code?" id="59d8167e-eec3-4630-8fe3-9c51cf774f7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="8e1fc3e0-c12f-4881-8a3e-998a1326b8b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="434" y="171" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8bd6b585-b8b0-4a93-8328-ba803abca7e2</ns16:incoming>
                        
                        
                        <ns16:outgoing>3d282ae9-beca-4972-8c71-b0dfcb4ab133</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="f9c9bdea-1ae9-4a88-823f-3762f30fad77" targetRef="8e1fc3e0-c12f-4881-8a3e-998a1326b8b0" name="To Catch Errors" id="8bd6b585-b8b0-4a93-8328-ba803abca7e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8e1fc3e0-c12f-4881-8a3e-998a1326b8b0" targetRef="9887c30c-5747-4563-a3ca-f80933efd198" name="To End" id="3d282ae9-beca-4972-8c71-b0dfcb4ab133">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Catch Errors">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8bd6b585-b8b0-4a93-8328-ba803abca7e2</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e04</endStateId>
            <toProcessItemId>2025.8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</toProcessItemId>
            <guid>5befcca5-a36b-44c1-8681-4e99957cb141</guid>
            <versionId>0056137b-cd78-4991-a938-c407234c1fa4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</fromProcessItemId>
            <toProcessItemId>2025.8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7067a45a-b182-43d4-bcec-b82c270bdef0</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e02</endStateId>
            <toProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</toProcessItemId>
            <guid>0a7efee2-a327-4145-902a-4fd58f1b82c7</guid>
            <versionId>0de6fb8c-e6b5-44d8-9c62-484d00baad63</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</fromProcessItemId>
            <toProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</toProcessItemId>
        </link>
        <link name="To Get Facility Codes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.af36fe78-fcc2-4c87-9d6f-2ecc6874b5b0</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</toProcessItemId>
            <guid>9adbdf2c-53b1-421a-9cb5-964d8a7244d7</guid>
            <versionId>5206d1c0-f724-4910-aef9-3b25a0dcd487</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</fromProcessItemId>
            <toProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3d282ae9-beca-4972-8c71-b0dfcb4ab133</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
            <guid>7e818880-18fe-4f56-b783-9683ce3e3292</guid>
            <versionId>5cdb9bd9-0af5-4a80-a0e0-434e533144fb</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.8e1fc3e0-c12f-4881-8a3e-998a1326b8b0</fromProcessItemId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cf5164f1-b921-4baf-a8f7-2c2ad13f1f1b</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
            <guid>4f3e013d-ccc9-4c9a-938c-51da57d2bdd5</guid>
            <versionId>d65ea1f5-8f73-4b3f-9aab-3c36c7bb8839</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</fromProcessItemId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
        </link>
        <link name="To get code?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.59d8167e-eec3-4630-8fe3-9c51cf774f7c</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</toProcessItemId>
            <guid>11089d1c-**************-c473a44c0345</guid>
            <versionId>e1c64f95-102a-4cab-86de-292db0041cbd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</fromProcessItemId>
            <toProcessItemId>2025.1fd8df56-6c68-4772-bafe-889a6bd683f4</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6f40bf0d-360e-4d93-8c19-761a792b3e3e</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e03</endStateId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
            <guid>0e25ee91-7a85-4057-bec1-78404b8bf9cd</guid>
            <versionId>e68ae5a9-41af-407e-bee2-0ac700163ccd</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="464" y="310" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.d468dd30-9324-4ae4-80cd-ca21249fc015</fromProcessItemId>
            <toProcessItemId>2025.9887c30c-5747-4563-a3ca-f80933efd198</toProcessItemId>
        </link>
        <link name="To get code?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.299f1ad2-34c5-4ca8-8c06-1de2d102a013</processLinkId>
            <processId>1.e3028b61-b01a-4a56-a086-244083a8d445</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</fromProcessItemId>
            <endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-599f</endStateId>
            <toProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</toProcessItemId>
            <guid>5c717dce-4651-4079-8f21-377b8db17a2e</guid>
            <versionId>f32aac7f-c0c4-42e8-875f-5bff8bd2708a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f02f4452-3134-40b0-a3fb-e50ebe47a002</fromProcessItemId>
            <toProcessItemId>2025.f9c9bdea-1ae9-4a88-823f-3762f30fad77</toProcessItemId>
        </link>
    </process>
</teamworks>

